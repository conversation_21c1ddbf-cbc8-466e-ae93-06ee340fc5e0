{"name": "deferred-leveldown", "description": "For handling delayed-open on LevelDOWN compatible libraries", "version": "1.2.2", "contributors": ["<PERSON>g <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/chesles/)", "<PERSON> <<EMAIL>> (https://github.com/raynos)", "<PERSON> <<EMAIL>> (https://github.com/dominictarr)", "<PERSON> <<EMAIL>> (https://github.com/maxogden)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/ralphtheninja)", "<PERSON> <<EMAIL>> (https://github.com/kesla)", "<PERSON> <<EMAIL>> (https://github.com/juliangruber)", "<PERSON> <<EMAIL>> (https://github.com/hij1nx)", "<PERSON> <<EMAIL>> (https://github.com/No9)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON> <<EMAIL>> (https://github.com/pgte)", "<PERSON> <<EMAIL>> (https://github.com/substack)"], "repository": {"type": "git", "url": "https://github.com/Level/deferred-leveldown.git"}, "homepage": "https://github.com/Level/deferred-leveldown", "keywords": ["leveldb", "level", "levelup", "leveldown"], "main": "deferred-leveldown.js", "dependencies": {"abstract-leveldown": "~2.6.0"}, "devDependencies": {"tape": "^4.6.0"}, "scripts": {"test": "node test.js"}, "license": "MIT"}