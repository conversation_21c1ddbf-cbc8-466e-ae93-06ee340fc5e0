{"name": "@types/secp256k1", "version": "4.0.6", "description": "TypeScript definitions for secp256k1", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/secp256k1", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "githubUsername": "anler", "url": "https://github.com/anler"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/secp256k1"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "5364542d96c35869169266cf0c299440004d7c624bc8cc18e1d30f01a26b0c08", "typeScriptVersion": "4.5"}