# LitElement 3.0

A simple base class for creating fast, lightweight web components.

[![Build Status](https://github.com/lit/lit/workflows/Tests/badge.svg)](https://github.com/lit/lit/actions?query=workflow%3ATests)
[![Published on npm](https://img.shields.io/npm/v/lit-element.svg?logo=npm)](https://www.npmjs.com/package/lit-element)
[![Join our Discord](https://img.shields.io/badge/discord-join%20chat-5865F2.svg?logo=discord&logoColor=fff)](https://lit.dev/discord/)
[![Mentioned in Awesome Lit](https://awesome.re/mentioned-badge.svg)](https://github.com/web-padawan/awesome-lit)

LitElement is the base class that powers the [Lit](https://lit.dev) library for building fast web components. Most users should import `LitElement` from the [`lit`](https://www.npmjs.com/package/lit) package rather than installing and importing from the `lit-element` package directly.

## About this release

This is a stable release of `lit-element` 3.0.0 (part of the Lit 2.0 release). If upgrading from previous versions of `lit-element`, please see the [Upgrade Guide](https://lit.dev/docs/releases/upgrade/) for a step-by-step guide on upgrading.

## Documentation

Full documentation is available at [lit.dev](https://lit.dev).

## Contributing

Please see [CONTRIBUTING.md](../../CONTRIBUTING.md).
