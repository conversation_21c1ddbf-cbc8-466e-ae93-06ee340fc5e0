export declare function fallbackAesEncrypt(iv: Uint8<PERSON><PERSON><PERSON>, key: Uint8<PERSON>rray, data: Uint8Array): Uint8Array;
export declare function fallbackAesDecrypt(iv: Uint8Array, key: Uint8Array, data: Uint8Array): Uint8Array;
export declare function fallbackHmacSha256Sign(key: Uint8Array, data: Uint8Array): Uint8Array;
export declare function fallbackHmacSha512Sign(key: Uint8Array, data: Uint8Array): Uint8Array;
export declare function fallbackSha256(msg: Uint8Array): Uint8Array;
export declare function fallbackSha512(msg: Uint8Array): Uint8Array;
export declare function fallbackRipemd160(msg: Uint8Array): Uint8Array;
//# sourceMappingURL=fallback.d.ts.map