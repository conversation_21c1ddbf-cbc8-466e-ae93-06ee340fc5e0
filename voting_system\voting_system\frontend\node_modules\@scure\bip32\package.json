{"name": "@scure/bip32", "version": "1.4.0", "description": "Secure, audited & minimal implementation of BIP32 hierarchical deterministic (HD) wallets over secp256k1", "files": ["index.ts", "lib/index.js", "lib/index.d.ts", "lib/index.js.map", "lib/esm/package.json", "lib/esm/index.js", "lib/esm/index.js.map"], "main": "lib/index.js", "module": "lib/esm/index.js", "types": "lib/index.d.ts", "exports": {".": {"types": "./lib/index.d.ts", "import": "./lib/esm/index.js", "default": "./lib/index.js"}}, "dependencies": {"@noble/curves": "~1.4.0", "@noble/hashes": "~1.4.0", "@scure/base": "~1.1.6"}, "devDependencies": {"@paulmillr/jsbt": "0.1.0", "micro-should": "0.4.0", "prettier": "3.1.1", "typescript": "5.3.2"}, "sideEffects": false, "author": "<PERSON> (https://paulmillr.com)", "homepage": "https://paulmillr.com/noble/#scure", "repository": {"type": "git", "url": "git+https://github.com/paulmillr/scure-bip32.git"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "url": "https://paulmillr.com"}], "license": "MIT", "scripts": {"build": "tsc && tsc -p tsconfig.esm.json", "build:release": "cd build && npm ci && npm run build:release", "lint": "prettier --check 'index.ts' 'test/*.test.ts'", "format": "prettier --write 'index.ts' 'test/*.test.ts'", "test": "cd test && tsc && node hdkey.test.js"}, "keywords": ["bip32", "hierarchical", "deterministic", "hd key", "bip0032", "bip-32", "bip39", "micro", "scure", "mnemonic", "phrase", "code"], "funding": "https://paulmillr.com/funding/"}