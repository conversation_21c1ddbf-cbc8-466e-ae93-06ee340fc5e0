"""
Generated by `compile_contracts.py` script.
Compiled with Solidity v0.8.19.
"""

# source: web3/_utils/contract_sources/OffchainResolver.sol:OffchainResolver
OFFCHAIN_RESOLVER_BYTECODE = "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"  # noqa: E501
OFFCHAIN_RESOLVER_RUNTIME = "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"  # noqa: E501
OFFCHAIN_RESOLVER_ABI = [
    {
        "inputs": [
            {"internalType": "string[]", "name": "_urls", "type": "string[]"},
            {"internalType": "address[]", "name": "_signers", "type": "address[]"},
        ],
        "stateMutability": "nonpayable",
        "type": "constructor",
    },
    {
        "inputs": [
            {"internalType": "address", "name": "sender", "type": "address"},
            {"internalType": "string[]", "name": "urls", "type": "string[]"},
            {"internalType": "bytes", "name": "callData", "type": "bytes"},
            {"internalType": "bytes4", "name": "callbackFunction", "type": "bytes4"},
            {"internalType": "bytes", "name": "extraData", "type": "bytes"},
        ],
        "name": "OffchainLookup",
        "type": "error",
    },
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": False,
                "internalType": "address[]",
                "name": "signers",
                "type": "address[]",
            }
        ],
        "name": "NewSigners",
        "type": "event",
    },
    {
        "inputs": [
            {"internalType": "address", "name": "target", "type": "address"},
            {"internalType": "uint64", "name": "expires", "type": "uint64"},
            {"internalType": "bytes", "name": "request", "type": "bytes"},
            {"internalType": "bytes", "name": "result", "type": "bytes"},
        ],
        "name": "makeSignatureHash",
        "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}],
        "stateMutability": "pure",
        "type": "function",
    },
    {
        "inputs": [
            {"internalType": "bytes", "name": "name", "type": "bytes"},
            {"internalType": "bytes", "name": "data", "type": "bytes"},
        ],
        "name": "resolve",
        "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [
            {"internalType": "bytes", "name": "response", "type": "bytes"},
            {"internalType": "bytes", "name": "extraData", "type": "bytes"},
        ],
        "name": "resolveWithProof",
        "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "address", "name": "", "type": "address"}],
        "name": "signers",
        "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "bytes4", "name": "interfaceID", "type": "bytes4"}],
        "name": "supportsInterface",
        "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
        "stateMutability": "pure",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "name": "urls",
        "outputs": [{"internalType": "string", "name": "", "type": "string"}],
        "stateMutability": "view",
        "type": "function",
    },
]
OFFCHAIN_RESOLVER_DATA = {
    "bytecode": OFFCHAIN_RESOLVER_BYTECODE,
    "bytecode_runtime": OFFCHAIN_RESOLVER_RUNTIME,
    "abi": OFFCHAIN_RESOLVER_ABI,
}
