{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;EAeE;;;;;;AAGF,kEAAqE;AAQrE,2CAAyE;AACzE,6CAAqD;AAMrD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAkCG;AACH,MAAqB,iBAEnB,SAAQ,2BAAuF;IAUhG;;;;;OAKG;IACH,yFAAyF;IACzF,kDAAkD;IAClD,YACC,UAAkB,EAClB,aAAiD,EACjD,gBAA4C;QAE5C,KAAK,CAAC,UAAU,EAAE,aAAa,EAAE,gBAAgB,CAAC,CAAC;IACpD,CAAC;IAnBD,kDAAkD;IACxC,qBAAqB,CAAC,WAAmB;QAClD,OAAO,OAAO,WAAW,KAAK,QAAQ,CAAC,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IACpF,CAAC;IAkBM,SAAS;QACf,IAAI,IAAI,CAAC,iBAAiB,IAAI,CAAC,IAAA,sBAAS,EAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE;YACjE,QAAQ,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE;gBAC1C,KAAK,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;oBACvC,OAAO,YAAY,CAAC;iBACpB;gBACD,KAAK,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;oBACjC,OAAO,WAAW,CAAC;iBACnB;gBACD,OAAO,CAAC,CAAC;oBACR,OAAO,cAAc,CAAC;iBACtB;aACD;SACD;QACD,OAAO,cAAc,CAAC;IACvB,CAAC;IAES,qBAAqB;QAC9B,IAAI,CAAC,iBAAiB,GAAG,IAAI,uBAAS,CACrC,IAAI,CAAC,WAAW,EAChB,SAAS,EACT,IAAI,CAAC,cAAc,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,MAAM,KAAK,CAAC;YACnE,CAAC,CAAC,SAAS;YACX,CAAC,CAAC,IAAI,CAAC,cAAc,CACtB,CAAC;IACH,CAAC;IAES,sBAAsB,CAAC,IAAa,EAAE,IAAa;;QAC5D,MAAA,IAAI,CAAC,iBAAiB,0CAAE,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC3C,CAAC;IAES,aAAa,CACtB,OAAoC;;QAEpC,IAAI,IAAI,CAAC,SAAS,EAAE,KAAK,cAAc,EAAE;YACxC,MAAM,IAAI,oCAAsB,EAAE,CAAC;SACnC;QACD,MAAA,IAAI,CAAC,iBAAiB,0CAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;IACvD,CAAC;IAES,eAAe,CAAC,KAA6B;QACtD,OAAO,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,KAAK,CAAC,IAAc,CAAC,CAAC;IACrE,CAAC;IAES,mBAAmB;;QAC5B,MAAA,IAAI,CAAC,iBAAiB,0CAAE,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QACtE,MAAA,IAAI,CAAC,iBAAiB,0CAAE,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC5E,MAAA,IAAI,CAAC,iBAAiB,0CAAE,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;QAChF,MAAA,IAAI,CAAC,iBAAiB,0CAAE,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;IACzE,CAAC;IAES,sBAAsB;;QAC/B,MAAA,IAAI,CAAC,iBAAiB,0CAAE,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC/E,MAAA,IAAI,CAAC,iBAAiB,0CAAE,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QACzE,MAAA,IAAI,CAAC,iBAAiB,0CAAE,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QAC3E,kIAAkI;IACnI,CAAC;IAES,aAAa,CAAC,KAAiB;;QACxC,IACC,IAAI,CAAC,iBAAiB,CAAC,aAAa;YACpC,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,EACtD;YACD,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,OAAO;SACP;QACD,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QACzB,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC9B,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;QAC7C,iEAAiE;QACjE,MAAA,IAAI,CAAC,iBAAiB,0CAAE,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;IAC5E,CAAC;CACD;AApGD,oCAoGC;AAEQ,8CAAiB"}