{"version": 3, "file": "error.js", "sourceRoot": "", "sources": ["../../src/error.ts"], "names": [], "mappings": ";;;AACA,2CAKqB;AAGrB,SAAgB,iBAAiB,CAAC,IAAY;IAC5C,OAAO,IAAI,IAAI,mCAAuB,CAAC,CAAC,CAAC,IAAI,IAAI,IAAI,mCAAuB,CAAC,CAAC,CAAC,CAAC;AAClF,CAAC;AAFD,8CAEC;AAED,SAAgB,mBAAmB,CAAC,IAAY;IAC9C,OAAO,gCAAoB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AAC7C,CAAC;AAFD,kDAEC;AAED,SAAgB,gBAAgB,CAAC,IAAY;IAC3C,OAAO,OAAO,IAAI,KAAK,QAAQ,CAAC;AAClC,CAAC;AAFD,4CAEC;AAED,SAAgB,QAAQ,CAAC,IAAY;IACnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,8BAAkB,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QACnD,OAAO,8BAAkB,CAAC,yBAAa,CAAC,CAAC;KAC1C;IACD,OAAO,8BAAkB,CAAC,IAAI,CAAC,CAAC;AAClC,CAAC;AALD,4BAKC;AAED,SAAgB,cAAc,CAAC,IAAY;IACzC,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,8BAAkB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;IAC3E,IAAI,CAAC,KAAK,EAAE;QACV,OAAO,8BAAkB,CAAC,yBAAa,CAAC,CAAC;KAC1C;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAND,wCAMC;AAED,SAAgB,oBAAoB,CAAC,QAAsB;IACzD,IAAI,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,WAAW,EAAE;QAC9C,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,iCAAiC,EAAE,CAAC;KACnE;IACD,IAAI,OAAO,QAAQ,CAAC,KAAK,CAAC,OAAO,KAAK,WAAW,EAAE;QACjD,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,oCAAoC,EAAE,CAAC;KACtE;IACD,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;QAC1C,OAAO;YACL,KAAK,EAAE,KAAK;YACZ,KAAK,EAAE,yCAAyC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE;SACtE,CAAC;KACH;IACD,IAAI,mBAAmB,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,MAAM,KAAK,GAAG,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAClD,IACE,KAAK,CAAC,OAAO,KAAK,8BAAkB,CAAC,yBAAa,CAAC,CAAC,OAAO;YAC3D,QAAQ,CAAC,KAAK,CAAC,OAAO,KAAK,KAAK,CAAC,OAAO,EACxC;YACA,OAAO;gBACL,KAAK,EAAE,KAAK;gBACZ,KAAK,EAAE,4CAA4C,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE;aACzE,CAAC;SACH;KACF;IACD,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;AACzB,CAAC;AA1BD,oDA0BC;AAED,SAAgB,oBAAoB,CAAC,CAAQ,EAAE,GAAW,EAAE,IAAY;IACtE,OAAO,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,sBAAsB,CAAC;QAC9F,CAAC,CAAC,IAAI,KAAK,CAAC,eAAe,IAAI,eAAe,GAAG,EAAE,CAAC;QACpD,CAAC,CAAC,CAAC,CAAC;AACR,CAAC;AAJD,oDAIC"}