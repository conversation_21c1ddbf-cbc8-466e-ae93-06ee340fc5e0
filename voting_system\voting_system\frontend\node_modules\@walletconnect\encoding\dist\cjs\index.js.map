{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";;;;AAAA,0EAA0C;AAC1C,wFAAsD;AAItD,MAAM,OAAO,GAAG,KAAK,CAAC;AACtB,MAAM,QAAQ,GAAG,MAAM,CAAC;AACxB,MAAM,OAAO,GAAG,QAAQ,CAAC;AAEzB,MAAM,WAAW,GAAG,QAAQ,CAAC;AAC7B,MAAM,UAAU,GAAG,OAAO,CAAC;AAC3B,MAAM,gBAAgB,GAAG,aAAa,CAAC;AACvC,MAAM,iBAAiB,GAAG,cAAc,CAAC;AAEzC,MAAM,WAAW,GAAG,GAAG,CAAC;AAIxB,SAAgB,aAAa,CAAC,GAAW;IACvC,OAAO,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;AAC7B,CAAC;AAFD,sCAEC;AAED,SAAgB,WAAW,CAAC,GAAW,EAAE,QAAQ,GAAG,KAAK;IACvD,MAAM,GAAG,GAAG,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IAClC,OAAO,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;AAC5C,CAAC;AAHD,kCAGC;AAED,SAAgB,YAAY,CAAC,GAAW;IACtC,OAAO,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAChC,CAAC;AAFD,oCAEC;AAED,SAAgB,cAAc,CAAC,GAAW;IACxC,OAAO,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;AACvC,CAAC;AAFD,wCAEC;AAED,SAAgB,cAAc,CAAC,GAAW;IACxC,OAAO,aAAa,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC;AAC3C,CAAC;AAFD,wCAEC;AAID,SAAgB,aAAa,CAAC,GAAe;IAC3C,OAAO,8BAAkB,CAAC,GAAG,CAAC,CAAC;AACjC,CAAC;AAFD,sCAEC;AAED,SAAgB,UAAU,CAAC,GAAe,EAAE,QAAQ,GAAG,KAAK;IAC1D,OAAO,WAAW,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC,CAAC;AACnD,CAAC;AAFD,gCAEC;AAED,SAAgB,WAAW,CAAC,GAAe;IACzC,OAAO,YAAY,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC;AAC1C,CAAC;AAFD,kCAEC;AAED,SAAgB,aAAa,CAAC,GAAe;IAC3C,OAAO,cAAc,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC;AAC5C,CAAC;AAFD,sCAEC;AAED,SAAgB,aAAa,CAAC,GAAe;IAC3C,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC;SACnB,GAAG,CAAC,cAAc,CAAC;SACnB,IAAI,CAAC,EAAE,CAAC,CAAC;AACd,CAAC;AAJD,sCAIC;AAID,SAAgB,WAAW,CAAC,GAAW;IACrC,OAAO,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC;AACpD,CAAC;AAFD,kCAEC;AAED,SAAgB,UAAU,CAAC,GAAW;IACpC,OAAO,aAAa,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;AACzC,CAAC;AAFD,gCAEC;AAED,SAAgB,SAAS,CAAC,GAAW;IACnC,OAAO,YAAY,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;AACxC,CAAC;AAFD,8BAEC;AAED,SAAgB,WAAW,CAAC,GAAW;IACrC,OAAO,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;AACxC,CAAC;AAFD,kCAEC;AAED,SAAgB,WAAW,CAAC,GAAW;IACrC,OAAO,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;AACxC,CAAC;AAFD,kCAEC;AAID,SAAgB,YAAY,CAAC,IAAY;IACvC,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AACrC,CAAC;AAFD,oCAEC;AAED,SAAgB,WAAW,CAAC,IAAY;IACtC,OAAO,aAAa,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;AAC3C,CAAC;AAFD,kCAEC;AAED,SAAgB,SAAS,CAAC,IAAY,EAAE,QAAQ,GAAG,KAAK;IACtD,OAAO,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAC,CAAC;AACnD,CAAC;AAFD,8BAEC;AAED,SAAgB,YAAY,CAAC,IAAY;IACvC,MAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;IAC/B,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,4CAA4C,CAAC,CAAC;IACrE,OAAO,GAAG,CAAC;AACb,CAAC;AAJD,oCAIC;AAED,SAAgB,YAAY,CAAC,IAAY;IACvC,OAAO,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;AAC1C,CAAC;AAFD,oCAEC;AAID,SAAgB,cAAc,CAAC,GAAW;IACxC,OAAO,cAAc,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC;AAC7C,CAAC;AAFD,wCAEC;AAED,SAAgB,aAAa,CAAC,GAAW;IACvC,OAAO,aAAa,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC;AAC5C,CAAC;AAFD,sCAEC;AAED,SAAgB,WAAW,CAAC,GAAW,EAAE,QAAkB;IACzD,OAAO,WAAW,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC,CAAC;AACpD,CAAC;AAFD,kCAEC;AAED,SAAgB,YAAY,CAAC,GAAW;IACtC,OAAO,GAAG,GAAG,EAAE,CAAC;AAClB,CAAC;AAFD,oCAEC;AAED,SAAgB,cAAc,CAAC,GAAW;IACxC,MAAM,GAAG,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IACpC,OAAO,aAAa,CAAC,GAAG,CAAC,CAAC;AAC5B,CAAC;AAHD,wCAGC;AAID,SAAgB,cAAc,CAAC,GAAW;IACxC,OAAO,aAAa,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC;AAC3C,CAAC;AAFD,wCAEC;AAED,SAAgB,aAAa,CAAC,GAAW;IACvC,OAAO,IAAI,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAClE,CAAC;AAFD,sCAEC;AAED,SAAgB,WAAW,CAAC,GAAoB,EAAE,QAAkB;IAClE,OAAO,UAAU,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC,CAAC;AAClD,CAAC;AAFD,kCAEC;AAED,SAAgB,YAAY,CAAC,GAAW;IACtC,OAAO,WAAW,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC;AACzC,CAAC;AAFD,oCAEC;AAED,SAAgB,cAAc,CAAC,GAAW;IACxC,OAAO,aAAa,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC;AAC3C,CAAC;AAFD,wCAEC;AAID,SAAgB,cAAc,CAAC,GAAQ;IACrC,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,CAAC,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;QAC/D,OAAO,KAAK,CAAC;KACd;IACD,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;QACxB,OAAO,KAAK,CAAC;KACd;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AARD,wCAQC;AAED,SAAgB,WAAW,CAAC,GAAQ,EAAE,MAAe;IACnD,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,kBAAkB,CAAC,EAAE;QAC7D,OAAO,KAAK,CAAC;KACd;IACD,IAAI,MAAM,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,MAAM,EAAE;QAC3C,OAAO,KAAK,CAAC;KACd;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AARD,kCAQC;AAED,SAAgB,QAAQ,CAAC,GAAQ;IAC/B,OAAO,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AAC9B,CAAC;AAFD,4BAEC;AAED,SAAgB,YAAY,CAAC,GAAQ;IACnC,OAAO,uBAAa,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AACrD,CAAC;AAFD,oCAEC;AAED,SAAgB,aAAa,CAAC,GAAQ;IACpC,OAAO,CACL,CAAC,YAAY,CAAC,GAAG,CAAC;QAClB,CAAC,QAAQ,CAAC,GAAG,CAAC;QACd,OAAO,GAAG,CAAC,UAAU,KAAK,WAAW,CACtC,CAAC;AACJ,CAAC;AAND,sCAMC;AAED,SAAgB,OAAO,CAAC,GAAQ;IAC9B,IAAI,QAAQ,CAAC,GAAG,CAAC,EAAE;QACjB,OAAO,WAAW,CAAC;KACpB;SAAM,IAAI,YAAY,CAAC,GAAG,CAAC,EAAE;QAC5B,OAAO,gBAAgB,CAAC;KACzB;SAAM,IAAI,aAAa,CAAC,GAAG,CAAC,EAAE;QAC7B,OAAO,iBAAiB,CAAC;KAC1B;SAAM,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QAC7B,OAAO,UAAU,CAAC;KACnB;SAAM;QACL,OAAO,OAAO,GAAG,CAAC;KACnB;AACH,CAAC;AAZD,0BAYC;AAED,SAAgB,WAAW,CAAC,GAAW;IACrC,IAAI,cAAc,CAAC,GAAG,CAAC,EAAE;QACvB,OAAO,OAAO,CAAC;KAChB;IACD,IAAI,WAAW,CAAC,GAAG,CAAC,EAAE;QACpB,OAAO,OAAO,CAAC;KAChB;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC;AARD,kCAQC;AAID,SAAgB,aAAa,CAAC,GAAG,IAAc;IAC7C,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACnC,OAAO,MAAM,CAAC;AAChB,CAAC;AAHD,sCAGC;AAED,SAAgB,YAAY,CAAC,GAAG,IAAkB;IAChD,IAAI,MAAM,GAAa,EAAE,CAAC;IAC1B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/D,OAAO,IAAI,UAAU,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;AACrC,CAAC;AAJD,oCAIC;AAED,SAAgB,QAAQ,CAAC,IAAY,EAAE,MAAc;IACnD,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IAClC,IAAI,IAAI,GAAG,CAAC,EAAE;QACZ,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;KACzB;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAND,4BAMC;AAED,SAAgB,SAAS,CAAC,IAAY,EAAE,MAAc;IACpD,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAC/B,CAAC;AAFD,8BAEC;AAED,SAAgB,cAAc,CAAC,MAAc,EAAE,QAAQ,GAAG,CAAC;IACzD,MAAM,SAAS,GAAG,MAAM,GAAG,QAAQ,CAAC;IACpC,OAAO,SAAS;QACd,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,SAAS,CAAC,GAAG,QAAQ,CAAC,GAAG,QAAQ,GAAG,QAAQ;QACzD,CAAC,CAAC,MAAM,CAAC;AACb,CAAC;AALD,wCAKC;AAED,SAAgB,UAAU,CAAC,GAAW,EAAE,QAAQ,GAAG,CAAC;IAClD,MAAM,KAAK,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,KAAK,QAAQ,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;IAC3E,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;AACjC,CAAC;AAHD,gCAGC;AAED,SAAgB,SAAS,CAAC,GAAW;IACnC,OAAO,UAAU,CAAC,GAAG,CAAC;SACnB,GAAG,CAAC,aAAa,CAAC;SAClB,IAAI,CAAC,EAAE,CAAC,CAAC;AACd,CAAC;AAJD,8BAIC;AAED,SAAgB,OAAO,CAAC,GAAW;IACjC,OAAO,WAAW,CAAC,SAAS,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAClD,CAAC;AAFD,0BAEC;AAED,SAAgB,aAAa,CAC3B,GAAW,EACX,QAAQ,GAAG,CAAC,EACZ,OAAO,GAAG,WAAW;IAErB,OAAO,OAAO,CAAC,GAAG,EAAE,cAAc,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,EAAE,OAAO,CAAC,CAAC;AACrE,CAAC;AAND,sCAMC;AAED,SAAgB,OAAO,CACrB,GAAW,EACX,MAAc,EACd,OAAO,GAAG,WAAW;IAErB,OAAO,SAAS,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AAC/C,CAAC;AAND,0BAMC;AAED,SAAgB,QAAQ,CACtB,GAAW,EACX,MAAc,EACd,OAAO,GAAG,WAAW;IAErB,OAAO,SAAS,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;AAChD,CAAC;AAND,4BAMC;AAED,SAAgB,eAAe,CAAC,GAAW;IACzC,OAAO,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AAChC,CAAC;AAFD,0CAEC;AAED,SAAgB,YAAY,CAAC,GAAW;IACtC,OAAO,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;AACjD,CAAC;AAFD,oCAEC;AAED,SAAgB,WAAW,CAAC,GAAW;IACrC,GAAG,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC;IAC3B,GAAG,GAAG,aAAa,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IAC5B,IAAI,GAAG,EAAE;QACP,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;KACzB;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AAPD,kCAOC;AAED,SAAgB,qBAAqB,CAAC,GAAW;IAC/C,MAAM,QAAQ,GAAG,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IACtC,GAAG,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC;IAC3B,GAAG,GAAG,GAAG,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;IAC3D,OAAO,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;AAC5C,CAAC;AALD,sDAKC;AAID,SAAS,WAAW,CAAC,KAAU;IAC7B,OAAO,OAAO,KAAK,KAAK,WAAW,CAAC;AACtC,CAAC;AAED,SAAS,SAAS,CAAC,KAAU;IAC3B,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAC7B,CAAC;AAED,SAAS,MAAM,CAAC,SAAkB,EAAE,YAAoB;IACtD,IAAI,CAAC,SAAS,EAAE;QACd,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;KAC/B;AACH,CAAC;AAED,SAAS,aAAa,CAAC,GAAW;IAChC,OAAO,GAAG;SACP,KAAK,CAAC,EAAE,CAAC;SACT,OAAO,EAAE;SACT,IAAI,CAAC,EAAE,CAAC,CAAC;AACd,CAAC;AAED,SAAS,SAAS,CAChB,GAAW,EACX,MAAc,EACd,IAAa,EACb,OAAO,GAAG,WAAW;IAErB,MAAM,IAAI,GAAG,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;IACjC,IAAI,MAAM,GAAG,GAAG,CAAC;IACjB,IAAI,IAAI,GAAG,CAAC,EAAE;QACZ,MAAM,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACjC,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC;KACvC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC"}