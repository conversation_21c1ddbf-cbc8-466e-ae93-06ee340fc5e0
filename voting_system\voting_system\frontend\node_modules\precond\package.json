{"name": "precond", "description": "Precondition checking utilities.", "version": "0.2.3", "author": "<PERSON><PERSON> <<EMAIL>>", "keywords": ["precondition", "assert", "invariant", "contract", "condition"], "repository": {"type": "git", "url": "https://github.com/MathieuTurcotte/node-precond.git"}, "devDependencies": {"nodeunit": "0.9", "jshint": "2.5"}, "scripts": {"pretest": "node_modules/.bin/jshint lib/ examples/ index.js", "test": "node_modules/.bin/nodeunit tests/"}, "engines": {"node": ">= 0.6"}, "files": ["index.js", "lib"]}