type Cleanup = () => void;
type WatchGet = <T extends object>(proxyObject: T) => T;
type WatchCallback = (get: WatchGet) => Cleanup | void | undefined;
type WatchOptions = {
    sync?: boolean;
};
/**
 * watch
 *
 * Creates a reactive effect that automatically tracks proxy objects and
 * reevaluates everytime one of the tracked proxy objects updates. It returns
 * a cleanup function to stop the reactive effect from reevaluating.
 *
 * Callback is invoked immediately to detect the tracked objects.
 *
 * Callback passed to `watch` receives a `get` function that "tracks" the
 * passed proxy object.
 *
 * Watch callbacks may return an optional cleanup function, which is evaluated
 * whenever the callback reevaluates or when the cleanup function returned by
 * `watch` is evaluated.
 *
 * `watch` calls inside `watch` are also automatically tracked and cleaned up
 * whenever the parent `watch` reevaluates.
 *
 * @param callback
 * @returns A cleanup function that stops the callback from reevaluating and
 * also performs cleanups registered into `watch`.
 */
export declare function watch(callback: WatchCallback, options?: WatchOptions): Cleanup;
export {};
declare type Awaited<T> = T extends Promise<infer V> ? V : T;