!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e||self).proxyCompare={})}(this,function(e){function t(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function r(e,r){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(n)return(n=n.call(e)).next.bind(n);if(Array.isArray(e)||(n=function(e,r){if(e){if("string"==typeof e)return t(e,r);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?t(e,r):void 0}}(e))||r&&e&&"number"==typeof e.length){n&&(e=n);var o=0;return function(){return o>=e.length?{done:!0}:{done:!1,value:e[o++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var n=Symbol(),o=Symbol(),a="a",i="w",u=function(e,t){return new Proxy(e,t)},f=Object.getPrototypeOf,c=new WeakMap,l=function(e){return e&&(c.has(e)?c.get(e):f(e)===Object.prototype||f(e)===Array.prototype)},s=function(e){return"object"==typeof e&&null!==e},p=function(e){if(Array.isArray(e))return Array.from(e);var t=Object.getOwnPropertyDescriptors(e);return Object.values(t).forEach(function(e){e.configurable=!0}),Object.create(f(e),t)},y=function(e){return e[o]||e},v=function(e,t,r,f){if(!l(e))return e;var c=f&&f.get(e);if(!c){var s=y(e);c=function(e){return Object.values(Object.getOwnPropertyDescriptors(e)).some(function(e){return!e.configurable&&!e.writable})}(s)?[s,p(s)]:[s],null==f||f.set(e,c)}var g,h,d,b,w,m,O,j=c[0],P=c[1],S=r&&r.get(j);return S&&S[1].f===!!P||((g=j,h=!!P,(d={}).f=h,b=d,w=!1,m=function(e,t){if(!w){var r=b[a].get(g);if(r||b[a].set(g,r={}),e===i)r[i]=!0;else{var n=r[e];n||(n=new Set,r[e]=n),n.add(t)}}},O={get:function(e,t){return t===o?g:(m("k",t),v(Reflect.get(e,t),b[a],b.c,b.t))},has:function(e,t){return t===n?(w=!0,b[a].delete(g),!0):(m("h",t),Reflect.has(e,t))},getOwnPropertyDescriptor:function(e,t){return m("o",t),Reflect.getOwnPropertyDescriptor(e,t)},ownKeys:function(e){return m(i),Reflect.ownKeys(e)}},h&&(O.set=O.deleteProperty=function(){return!1}),S=[O,b])[1].p=u(P||j,S[0]),r&&r.set(j,S)),S[1][a]=t,S[1].c=r,S[1].t=f,S[1].p};e.affectedToPathList=function(e,t,r){var n=[],o=new WeakSet;return function e(a,u){if(!o.has(a)){s(a)&&o.add(a);var f=s(a)&&t.get(y(a));if(f){var c,l;if(null==(c=f.h)||c.forEach(function(e){var t=":has("+String(e)+")";n.push(u?[].concat(u,[t]):[t])}),!0===f[i]){var p=":ownKeys";n.push(u?[].concat(u,[p]):[p])}else{var v;null==(v=f.o)||v.forEach(function(e){var t=":hasOwn("+String(e)+")";n.push(u?[].concat(u,[t]):[t])})}null==(l=f.k)||l.forEach(function(t){r&&!("value"in(Object.getOwnPropertyDescriptor(a,t)||{}))||e(a[t],u?[].concat(u,[t]):[t])})}else u&&n.push(u)}}(e),n},e.createProxy=v,e.getUntracked=function(e){return l(e)&&e[o]||null},e.isChanged=function e(t,n,o,a){if(Object.is(t,n))return!1;if(!s(t)||!s(n))return!0;var u=o.get(y(t));if(!u)return!0;if(a){var f,c=a.get(t);if(c&&c.n===n)return c.g;a.set(t,((f={}).n=n,f.g=!1,f))}var l=null;try{for(var p,v=r(u.h||[]);!(p=v()).done;){var g=p.value;if(l=Reflect.has(t,g)!==Reflect.has(n,g))return l}if(!0===u[i]){if(l=function(e,t){var r=Reflect.ownKeys(e),n=Reflect.ownKeys(t);return r.length!==n.length||r.some(function(e,t){return e!==n[t]})}(t,n),l)return l}else for(var h,d=r(u.o||[]);!(h=d()).done;){var b=h.value;if(l=!!Reflect.getOwnPropertyDescriptor(t,b)!=!!Reflect.getOwnPropertyDescriptor(n,b))return l}for(var w,m=r(u.k||[]);!(w=m()).done;){var O=w.value;if(l=e(t[O],n[O],o,a))return l}return null===l&&(l=!0),l}finally{var j;a&&a.set(t,((j={}).n=n,j.g=l,j))}},e.markToTrack=function(e,t){void 0===t&&(t=!0),c.set(e,t)},e.replaceNewProxy=function(e){u=e},e.trackMemo=function(e){return!!l(e)&&n in e}});
//# sourceMappingURL=index.umd.js.map
