import React, { useState, useEffect } from 'react';

const Profile = ({ voter, isAuthenticated, onNavigate }) => {
    const [userData, setUserData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');

    useEffect(() => {
        // Get user data from localStorage
        const storedUserData = localStorage.getItem('userData');
        if (storedUserData) {
            try {
                const parsedUserData = JSON.parse(storedUserData);
                setUserData(parsedUserData);
            } catch (err) {
                console.error('Error parsing user data:', err);
                setError('Could not load user profile data');
            }
        }
        setLoading(false);
    }, []);

    if (!isAuthenticated) {
        return (
            <div className="container mt-4">
                <div className="alert alert-warning">
                    <h4 className="alert-heading">Authentication Required</h4>
                    <p>You need to log in to view your profile.</p>
                    <hr />
                    <div className="d-flex gap-2">
                        <button
                            className="btn btn-primary"
                            onClick={() => onNavigate('login')}
                        >
                            Login
                        </button>
                        <button
                            className="btn btn-outline-secondary"
                            onClick={() => onNavigate('signup')}
                        >
                            Create Account
                        </button>
                    </div>
                </div>
            </div>
        );
    }

    if (loading) {
        return (
            <div className="container mt-4">
                <div className="card">
                    <div className="card-body text-center">
                        <div className="spinner-border text-primary" role="status">
                            <span className="visually-hidden">Loading...</span>
                        </div>
                        <p className="mt-2">Loading profile information...</p>
                    </div>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="container mt-4">
                <div className="alert alert-danger">
                    <h4 className="alert-heading">Error</h4>
                    <p>{error}</p>
                </div>
            </div>
        );
    }

    return (
        <div className="container mt-4">
            <div className="row">
                <div className="col-md-8 mx-auto">
                    <div className="card">
                        <div className="card-header bg-primary text-white">
                            <h3 className="mb-0">User Profile</h3>
                        </div>
                        <div className="card-body">
                            {userData && (
                                <div className="row">
                                    <div className="col-md-4 text-center mb-4 mb-md-0">
                                        <div className="profile-image-container bg-light rounded-circle mx-auto d-flex align-items-center justify-content-center" style={{ width: '150px', height: '150px' }}>
                                            <i className="bi bi-person-circle" style={{ fontSize: '5rem' }}></i>
                                        </div>
                                        <h4 className="mt-3">{userData.firstName} {userData.lastName}</h4>
                                        <p className="text-muted">@{userData.username}</p>
                                    </div>
                                    <div className="col-md-8">
                                        <h4 className="border-bottom pb-2">Account Information</h4>
                                        <div className="row mb-3">
                                            <div className="col-sm-4 fw-bold">Email:</div>
                                            <div className="col-sm-8">{userData.email}</div>
                                        </div>
                                        <div className="row mb-3">
                                            <div className="col-sm-4 fw-bold">Account Type:</div>
                                            <div className="col-sm-8">
                                                {userData.isStaff ? 'Administrator' : 'Standard User'}
                                                {userData.isCandidate && ' (Candidate)'}
                                            </div>
                                        </div>

                                        <h4 className="border-bottom pb-2 mt-4">Voter Status</h4>
                                        {voter ? (
                                            <>
                                                <div className="row mb-3">
                                                    <div className="col-sm-4 fw-bold">ID Number:</div>
                                                    <div className="col-sm-8">{voter.id_number}</div>
                                                </div>
                                                <div className="row mb-3">
                                                    <div className="col-sm-4 fw-bold">Constituency:</div>
                                                    <div className="col-sm-8">{voter.constituency?.name || 'Not specified'}</div>
                                                </div>
                                                <div className="row mb-3">
                                                    <div className="col-sm-4 fw-bold">Polling Station:</div>
                                                    <div className="col-sm-8">{voter.polling_station?.name || 'Not specified'}</div>
                                                </div>
                                                <div className="row mb-3">
                                                    <div className="col-sm-4 fw-bold">Verification Status:</div>
                                                    <div className="col-sm-8">
                                                        {voter.is_verified ? (
                                                            <span className="badge bg-success">Verified</span>
                                                        ) : (
                                                            <span className="badge bg-warning text-dark">Pending Verification</span>
                                                        )}
                                                    </div>
                                                </div>
                                            </>
                                        ) : (
                                            <div className="alert alert-warning">
                                                <h5 className="alert-heading">Not Registered as a Voter</h5>
                                                <p>
                                                    You have an account but you are not registered as a voter. 
                                                    You need to register as a voter to participate in elections.
                                                </p>
                                                <hr />
                                                <button
                                                    className="btn btn-success"
                                                    onClick={() => onNavigate('register')}
                                                >
                                                    Register as Voter
                                                </button>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Profile;
