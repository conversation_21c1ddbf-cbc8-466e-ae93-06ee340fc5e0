// Generated by CoffeeScript 1.12.7
(function() {
  var GenericApp, execute_request, fake_response, fs, http, querystring, url, utils;

  url = require('url');

  querystring = require('querystring');

  fs = require('fs');

  http = require('http');

  utils = require('./utils');

  execute_request = function(app, funs, req, res, data) {
    var fun, results, x;
    try {
      results = [];
      while (funs.length > 0) {
        fun = funs.shift();
        req.last_fun = fun;
        results.push(data = app[fun](req, res, data, req.next_filter));
      }
      return results;
    } catch (error1) {
      x = error1;
      if (typeof x === 'object' && 'status' in x) {
        if (x.status === 0) {
          return;
        } else if ('handle_' + x.status in app) {
          app['handle_' + x.status](req, res, x);
        } else {
          app['handle_error'](req, res, x);
        }
      } else {
        app['handle_error'](req, res, x);
      }
      return app['log_request'](req, res, true);
    }
  };

  fake_response = function(req, res) {
    var headers;
    headers = {
      'Connection': 'close'
    };
    res.writeHead = function(status, user_headers) {
      var k, r, x;
      if (user_headers == null) {
        user_headers = {};
      }
      r = [];
      r.push('HTTP/' + req.httpVersion + ' ' + status + ' ' + http.STATUS_CODES[status]);
      utils.objectExtend(headers, user_headers);
      for (k in headers) {
        r.push(k + ': ' + headers[k]);
      }
      r = r.concat(['', '']);
      try {
        return res.write(r.join('\r\n'));
      } catch (error1) {
        x = error1;
      }
    };
    return res.setHeader = function(k, v) {
      return headers[k] = v;
    };
  };

  exports.generateHandler = function(app, dispatcher) {
    return function(req, res, head) {
      var allowed_methods, found, funs, i, j, l, len, m, method, path, ref, row;
      if (typeof res.writeHead === "undefined") {
        fake_response(req, res);
      }
      utils.objectExtend(req, url.parse(req.url, true));
      req.start_date = new Date();
      found = false;
      allowed_methods = [];
      for (j = 0, len = dispatcher.length; j < len; j++) {
        row = dispatcher[j];
        method = row[0], path = row[1], funs = row[2];
        if (path.constructor !== Array) {
          path = [path];
        }
        m = req.pathname.match(path[0]);
        if (!m) {
          continue;
        }
        if (!req.method.match(new RegExp(method))) {
          allowed_methods.push(method);
          continue;
        }
        for (i = l = 1, ref = path.length; 1 <= ref ? l < ref : l > ref; i = 1 <= ref ? ++l : --l) {
          req[path[i]] = m[i];
        }
        funs = funs.slice(0);
        funs.push('log_request');
        req.next_filter = function(data) {
          return execute_request(app, funs, req, res, data);
        };
        req.next_filter(head);
        found = true;
        break;
      }
      if (!found) {
        if (allowed_methods.length !== 0) {
          app['handle_405'](req, res, allowed_methods);
        } else {
          app['handle_404'](req, res);
        }
        app['log_request'](req, res, true);
      }
    };
  };

  exports.GenericApp = GenericApp = (function() {
    function GenericApp() {}

    GenericApp.prototype.handle_404 = function(req, res, x) {
      if (res.finished) {
        return x;
      }
      res.writeHead(404, {});
      res.end();
      return true;
    };

    GenericApp.prototype.handle_405 = function(req, res, methods) {
      res.writeHead(405, {
        'Allow': methods.join(', ')
      });
      res.end();
      return true;
    };

    GenericApp.prototype.handle_error = function(req, res, x) {
      if (res.finished) {
        return x;
      }
      if (typeof x === 'object' && 'status' in x) {
        res.writeHead(x.status, {});
        res.end(x.message || "");
      } else {
        try {
          res.writeHead(500, {});
          res.end("500 - Internal Server Error");
        } catch (error1) {
          x = error1;
        }
        this.log('error', 'Exception on "' + req.method + ' ' + req.href + '" in filter "' + req.last_fun + '":\n' + (x.stack || x));
      }
      return true;
    };

    GenericApp.prototype.log_request = function(req, res, data) {
      var td;
      td = (new Date()) - req.start_date;
      this.log('info', req.method + ' ' + req.url + ' ' + td + 'ms ' + (res.finished ? res.statusCode : '(unfinished)'));
      return data;
    };

    GenericApp.prototype.log = function(severity, line) {
      return console.log(line);
    };

    GenericApp.prototype.expose_html = function(req, res, content) {
      if (res.finished) {
        return content;
      }
      if (!res.getHeader('Content-Type')) {
        res.setHeader('Content-Type', 'text/html; charset=UTF-8');
      }
      return this.expose(req, res, content);
    };

    GenericApp.prototype.expose_json = function(req, res, content) {
      if (res.finished) {
        return content;
      }
      if (!res.getHeader('Content-Type')) {
        res.setHeader('Content-Type', 'application/json');
      }
      return this.expose(req, res, JSON.stringify(content));
    };

    GenericApp.prototype.expose = function(req, res, content) {
      if (res.finished) {
        return content;
      }
      if (content && !res.getHeader('Content-Type')) {
        res.setHeader('Content-Type', 'text/plain');
      }
      if (content) {
        res.setHeader('Content-Length', content.length);
      }
      res.writeHead(res.statusCode);
      res.end(content, 'utf8');
      return true;
    };

    GenericApp.prototype.serve_file = function(req, res, filename, next_filter) {
      var a;
      a = function(error, content) {
        if (error) {
          res.writeHead(500);
          res.end("can't read file");
        } else {
          res.setHeader('Content-length', content.length);
          res.writeHead(res.statusCode, res.headers);
          res.end(content, 'utf8');
        }
        return next_filter(true);
      };
      fs.readFile(filename, a);
      throw {
        status: 0
      };
    };

    GenericApp.prototype.cache_for = function(req, res, content) {
      var exp;
      res.cache_for = res.cache_for || 365 * 24 * 60 * 60;
      res.setHeader('Cache-Control', 'public, max-age=' + res.cache_for);
      exp = new Date();
      exp.setTime(exp.getTime() + res.cache_for * 1000);
      res.setHeader('Expires', exp.toGMTString());
      return content;
    };

    GenericApp.prototype.h_no_cache = function(req, res, content) {
      res.setHeader('Cache-Control', 'no-store, no-cache, no-transform, must-revalidate, max-age=0');
      return content;
    };

    GenericApp.prototype.expect_form = function(req, res, _data, next_filter) {
      var data;
      data = new Buffer(0);
      req.on('data', (function(_this) {
        return function(d) {
          return data = utils.buffer_concat(data, new Buffer(d, 'binary'));
        };
      })(this));
      req.on('end', (function(_this) {
        return function() {
          var q;
          data = data.toString('utf-8');
          switch ((req.headers['content-type'] || '').split(';')[0]) {
            case 'application/x-www-form-urlencoded':
              q = querystring.parse(data);
              break;
            case 'text/plain':
            case '':
              q = data;
              break;
            default:
              _this.log('error', "Unsupported content-type " + req.headers['content-type']);
              q = void 0;
          }
          return next_filter(q);
        };
      })(this));
      throw {
        status: 0
      };
    };

    GenericApp.prototype.expect_xhr = function(req, res, _data, next_filter) {
      var data;
      data = new Buffer(0);
      req.on('data', (function(_this) {
        return function(d) {
          return data = utils.buffer_concat(data, new Buffer(d, 'binary'));
        };
      })(this));
      req.on('end', (function(_this) {
        return function() {
          var q;
          data = data.toString('utf-8');
          switch ((req.headers['content-type'] || '').split(';')[0]) {
            case 'text/plain':
            case 'T':
            case 'application/json':
            case 'application/xml':
            case '':
            case 'text/xml':
              q = data;
              break;
            default:
              _this.log('error', 'Unsupported content-type ' + req.headers['content-type']);
              q = void 0;
          }
          return next_filter(q);
        };
      })(this));
      throw {
        status: 0
      };
    };

    return GenericApp;

  })();

}).call(this);
