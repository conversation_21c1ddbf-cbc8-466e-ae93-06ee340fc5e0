"""
Generated by `compile_contracts.py` script.
Compiled with Solidity v0.8.19.
"""

# source: web3/_utils/contract_sources/EmitterContract.sol:EmitterContract
EMITTER_CONTRACT_BYTECODE = "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"  # noqa: E501
EMITTER_CONTRACT_RUNTIME = "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"  # noqa: E501
EMITTER_CONTRACT_ABI = [
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": True,
                "internalType": "address",
                "name": "arg0",
                "type": "address",
            },
            {
                "indexed": False,
                "internalType": "address",
                "name": "arg1",
                "type": "address",
            },
        ],
        "name": "LogAddressIndexed",
        "type": "event",
    },
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": False,
                "internalType": "address",
                "name": "arg0",
                "type": "address",
            },
            {
                "indexed": False,
                "internalType": "address",
                "name": "arg1",
                "type": "address",
            },
        ],
        "name": "LogAddressNotIndexed",
        "type": "event",
    },
    {"anonymous": True, "inputs": [], "name": "LogAnonymous", "type": "event"},
    {
        "anonymous": False,
        "inputs": [
            {"indexed": False, "internalType": "bytes", "name": "v", "type": "bytes"}
        ],
        "name": "LogBytes",
        "type": "event",
    },
    {
        "anonymous": True,
        "inputs": [
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "arg0",
                "type": "uint256",
            },
            {
                "indexed": True,
                "internalType": "uint256",
                "name": "arg1",
                "type": "uint256",
            },
        ],
        "name": "LogDoubleAnonymous",
        "type": "event",
    },
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "arg0",
                "type": "uint256",
            },
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "arg1",
                "type": "uint256",
            },
        ],
        "name": "LogDoubleArg",
        "type": "event",
    },
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "arg0",
                "type": "uint256",
            },
            {
                "indexed": True,
                "internalType": "uint256",
                "name": "arg1",
                "type": "uint256",
            },
        ],
        "name": "LogDoubleWithIndex",
        "type": "event",
    },
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": True,
                "internalType": "string",
                "name": "arg0",
                "type": "string",
            },
            {
                "indexed": False,
                "internalType": "string",
                "name": "arg1",
                "type": "string",
            },
        ],
        "name": "LogDynamicArgs",
        "type": "event",
    },
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": True,
                "internalType": "bytes2[]",
                "name": "arg0",
                "type": "bytes2[]",
            },
            {
                "indexed": False,
                "internalType": "bytes2[]",
                "name": "arg1",
                "type": "bytes2[]",
            },
        ],
        "name": "LogListArgs",
        "type": "event",
    },
    {"anonymous": False, "inputs": [], "name": "LogNoArguments", "type": "event"},
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "arg0",
                "type": "uint256",
            },
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "arg1",
                "type": "uint256",
            },
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "arg2",
                "type": "uint256",
            },
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "arg3",
                "type": "uint256",
            },
        ],
        "name": "LogQuadrupleArg",
        "type": "event",
    },
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "arg0",
                "type": "uint256",
            },
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "arg1",
                "type": "uint256",
            },
            {
                "indexed": True,
                "internalType": "uint256",
                "name": "arg2",
                "type": "uint256",
            },
            {
                "indexed": True,
                "internalType": "uint256",
                "name": "arg3",
                "type": "uint256",
            },
        ],
        "name": "LogQuadrupleWithIndex",
        "type": "event",
    },
    {
        "anonymous": True,
        "inputs": [
            {
                "indexed": True,
                "internalType": "uint256",
                "name": "arg0",
                "type": "uint256",
            }
        ],
        "name": "LogSingleAnonymous",
        "type": "event",
    },
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "arg0",
                "type": "uint256",
            }
        ],
        "name": "LogSingleArg",
        "type": "event",
    },
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": True,
                "internalType": "uint256",
                "name": "arg0",
                "type": "uint256",
            }
        ],
        "name": "LogSingleWithIndex",
        "type": "event",
    },
    {
        "anonymous": False,
        "inputs": [
            {"indexed": False, "internalType": "string", "name": "v", "type": "string"}
        ],
        "name": "LogString",
        "type": "event",
    },
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "arg0",
                "type": "uint256",
            },
            {
                "components": [
                    {"internalType": "uint256", "name": "a", "type": "uint256"},
                    {"internalType": "uint256", "name": "b", "type": "uint256"},
                    {
                        "components": [
                            {"internalType": "uint256", "name": "c", "type": "uint256"}
                        ],
                        "internalType": "struct EmitterContract.NestedTestTuple",
                        "name": "nested",
                        "type": "tuple",
                    },
                ],
                "indexed": False,
                "internalType": "struct EmitterContract.TestTuple",
                "name": "arg1",
                "type": "tuple",
            },
        ],
        "name": "LogStructArgs",
        "type": "event",
    },
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "arg0",
                "type": "uint256",
            },
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "arg1",
                "type": "uint256",
            },
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "arg2",
                "type": "uint256",
            },
        ],
        "name": "LogTripleArg",
        "type": "event",
    },
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "arg0",
                "type": "uint256",
            },
            {
                "indexed": True,
                "internalType": "uint256",
                "name": "arg1",
                "type": "uint256",
            },
            {
                "indexed": True,
                "internalType": "uint256",
                "name": "arg2",
                "type": "uint256",
            },
        ],
        "name": "LogTripleWithIndex",
        "type": "event",
    },
    {
        "inputs": [
            {"internalType": "address", "name": "arg0", "type": "address"},
            {"internalType": "address", "name": "arg1", "type": "address"},
        ],
        "name": "logAddressIndexedArgs",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [
            {"internalType": "address", "name": "arg0", "type": "address"},
            {"internalType": "address", "name": "arg1", "type": "address"},
        ],
        "name": "logAddressNotIndexedArgs",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "bytes", "name": "v", "type": "bytes"}],
        "name": "logBytes",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [
            {
                "internalType": "enum EmitterContract.WhichEvent",
                "name": "which",
                "type": "uint8",
            },
            {"internalType": "uint256", "name": "arg0", "type": "uint256"},
            {"internalType": "uint256", "name": "arg1", "type": "uint256"},
        ],
        "name": "logDouble",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [
            {"internalType": "string", "name": "arg0", "type": "string"},
            {"internalType": "string", "name": "arg1", "type": "string"},
        ],
        "name": "logDynamicArgs",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [
            {"internalType": "bytes2[]", "name": "arg0", "type": "bytes2[]"},
            {"internalType": "bytes2[]", "name": "arg1", "type": "bytes2[]"},
        ],
        "name": "logListArgs",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [
            {
                "internalType": "enum EmitterContract.WhichEvent",
                "name": "which",
                "type": "uint8",
            }
        ],
        "name": "logNoArgs",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [
            {
                "internalType": "enum EmitterContract.WhichEvent",
                "name": "which",
                "type": "uint8",
            },
            {"internalType": "uint256", "name": "arg0", "type": "uint256"},
            {"internalType": "uint256", "name": "arg1", "type": "uint256"},
            {"internalType": "uint256", "name": "arg2", "type": "uint256"},
            {"internalType": "uint256", "name": "arg3", "type": "uint256"},
        ],
        "name": "logQuadruple",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [
            {
                "internalType": "enum EmitterContract.WhichEvent",
                "name": "which",
                "type": "uint8",
            },
            {"internalType": "uint256", "name": "arg0", "type": "uint256"},
        ],
        "name": "logSingle",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "string", "name": "v", "type": "string"}],
        "name": "logString",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [
            {"internalType": "uint256", "name": "arg0", "type": "uint256"},
            {
                "components": [
                    {"internalType": "uint256", "name": "a", "type": "uint256"},
                    {"internalType": "uint256", "name": "b", "type": "uint256"},
                    {
                        "components": [
                            {"internalType": "uint256", "name": "c", "type": "uint256"}
                        ],
                        "internalType": "struct EmitterContract.NestedTestTuple",
                        "name": "nested",
                        "type": "tuple",
                    },
                ],
                "internalType": "struct EmitterContract.TestTuple",
                "name": "arg1",
                "type": "tuple",
            },
        ],
        "name": "logStruct",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [
            {
                "internalType": "enum EmitterContract.WhichEvent",
                "name": "which",
                "type": "uint8",
            },
            {"internalType": "uint256", "name": "arg0", "type": "uint256"},
            {"internalType": "uint256", "name": "arg1", "type": "uint256"},
            {"internalType": "uint256", "name": "arg2", "type": "uint256"},
        ],
        "name": "logTriple",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
]
EMITTER_CONTRACT_DATA = {
    "bytecode": EMITTER_CONTRACT_BYTECODE,
    "bytecode_runtime": EMITTER_CONTRACT_RUNTIME,
    "abi": EMITTER_CONTRACT_ABI,
}
