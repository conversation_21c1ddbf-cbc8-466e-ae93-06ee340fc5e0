Metadata-Version: 2.1
Name: py-solc-x
Version: 2.0.3
Summary: Python wrapper and version management tool for the solc Solidity compiler.
Home-page: https://github.com/ApeWorX/py-solc-x
Author: ApeWorX Ltd.
Author-email: <EMAIL>
License: MIT
Keywords: ethereum solidity solc
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Natural Language :: English
Classifier: Operating System :: MacOS
Classifier: Operating System :: POSIX
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Requires-Python: >=3.8,<4
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: requests <3,>=2.19.0
Requires-Dist: packaging <24,>=23.1
Provides-Extra: dev
Requires-Dist: pytest >=6.0 ; extra == 'dev'
Requires-Dist: pytest-xdist ; extra == 'dev'
Requires-Dist: pytest-cov ; extra == 'dev'
Requires-Dist: pytest-mock ; extra == 'dev'
Requires-Dist: hypothesis <7.0,>=6.2.0 ; extra == 'dev'
Requires-Dist: black <24,>=23.11.0 ; extra == 'dev'
Requires-Dist: mypy <2,>=1.6.1 ; extra == 'dev'
Requires-Dist: types-setuptools ; extra == 'dev'
Requires-Dist: types-requests ; extra == 'dev'
Requires-Dist: flake8 <7,>=6.1.0 ; extra == 'dev'
Requires-Dist: isort <6,>=5.10.1 ; extra == 'dev'
Requires-Dist: mdformat >=0.7.17 ; extra == 'dev'
Requires-Dist: mdformat-gfm >=0.3.5 ; extra == 'dev'
Requires-Dist: mdformat-frontmatter >=0.4.1 ; extra == 'dev'
Requires-Dist: myst-parser <2,>=1.0.0 ; extra == 'dev'
Requires-Dist: sphinx-click <5,>=4.4.0 ; extra == 'dev'
Requires-Dist: Sphinx <7,>=6.1.3 ; extra == 'dev'
Requires-Dist: sphinx-rtd-theme <2,>=1.2.0 ; extra == 'dev'
Requires-Dist: sphinxcontrib-napoleon >=0.7 ; extra == 'dev'
Requires-Dist: sphinx-plausible <0.2,>=0.1.2 ; extra == 'dev'
Requires-Dist: setuptools ; extra == 'dev'
Requires-Dist: setuptools-scm ; extra == 'dev'
Requires-Dist: wheel ; extra == 'dev'
Requires-Dist: twine ; extra == 'dev'
Requires-Dist: commitizen ; extra == 'dev'
Requires-Dist: pre-commit ; extra == 'dev'
Requires-Dist: pytest-watch ; extra == 'dev'
Requires-Dist: IPython ; extra == 'dev'
Requires-Dist: ipdb ; extra == 'dev'
Provides-Extra: doc
Requires-Dist: myst-parser <2,>=1.0.0 ; extra == 'doc'
Requires-Dist: sphinx-click <5,>=4.4.0 ; extra == 'doc'
Requires-Dist: Sphinx <7,>=6.1.3 ; extra == 'doc'
Requires-Dist: sphinx-rtd-theme <2,>=1.2.0 ; extra == 'doc'
Requires-Dist: sphinxcontrib-napoleon >=0.7 ; extra == 'doc'
Requires-Dist: sphinx-plausible <0.2,>=0.1.2 ; extra == 'doc'
Provides-Extra: lint
Requires-Dist: black <24,>=23.11.0 ; extra == 'lint'
Requires-Dist: mypy <2,>=1.6.1 ; extra == 'lint'
Requires-Dist: types-setuptools ; extra == 'lint'
Requires-Dist: types-requests ; extra == 'lint'
Requires-Dist: flake8 <7,>=6.1.0 ; extra == 'lint'
Requires-Dist: isort <6,>=5.10.1 ; extra == 'lint'
Requires-Dist: mdformat >=0.7.17 ; extra == 'lint'
Requires-Dist: mdformat-gfm >=0.3.5 ; extra == 'lint'
Requires-Dist: mdformat-frontmatter >=0.4.1 ; extra == 'lint'
Provides-Extra: release
Requires-Dist: setuptools ; extra == 'release'
Requires-Dist: setuptools-scm ; extra == 'release'
Requires-Dist: wheel ; extra == 'release'
Requires-Dist: twine ; extra == 'release'
Provides-Extra: test
Requires-Dist: pytest >=6.0 ; extra == 'test'
Requires-Dist: pytest-xdist ; extra == 'test'
Requires-Dist: pytest-cov ; extra == 'test'
Requires-Dist: pytest-mock ; extra == 'test'
Requires-Dist: hypothesis <7.0,>=6.2.0 ; extra == 'test'

# py-solc-x

[![Pypi Status](https://img.shields.io/pypi/v/py-solc-x.svg)](https://pypi.org/project/py-solc-x/) [![Build Status](https://img.shields.io/github/workflow/status/iamdefinitelyahuman/py-solc-x/py-solc-x%20workflow)](https://github.com/iamdefinitelyahuman/py-solc-x/actions) [![Coverage Status](https://img.shields.io/codecov/c/github/iamdefinitelyahuman/py-solc-x)](https://codecov.io/gh/iamdefinitelyahuman/py-solc-x)

Python wrapper and version management tool for the `solc` Solidity compiler.

Forked from [`py-solc`](https://github.com/ethereum/py-solc).

## Features

- Full support for Solidity `>=0.4.11`
- Install Solidity on Linux, OSX and Windows
- Compile Solidity from source on Linux and OSX

## Dependencies

Py-solc-x allows the use of multiple versions of solc, and can install or compile them as needed. If you wish to compile from source you must first insall the required [solc dependencies](https://solidity.readthedocs.io/en/latest/installing-solidity.html#building-from-source).

## Installation

### via `pip`

```bash
pip install py-solc-x
```

### via `setuptools`

```bash
git clone https://github.com/iamdefinitelyahuman/py-solc-x.git
cd py-solc-x
python3 setup.py install
```

## Documentation

Documentation is hosted at [Read the Docs](https://solcx.readthedocs.io/en/latest/).

## Testing

Py-solc-x is tested on Linux, OSX and Windows with solc versions `>=0.4.11`.

To run the test suite:

```bash
pytest tests/
```

By default, the test suite installs all available `solc` versions for your OS. If you only wish to test against already installed versions, include the `--no-install` flag.

## Contributing

Help is always appreciated! Feel free to open an issue if you find a problem, or a pull request if you've solved an issue.

Please check out our [Contribution Guide](CONTRIBUTING.md) prior to opening a pull request, and join the Brownie [Gitter channel](https://gitter.im/eth-brownie/community) if you have any questions.

## License

This project is licensed under the [MIT license](LICENSE).
