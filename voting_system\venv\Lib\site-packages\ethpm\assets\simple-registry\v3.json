{"compilers": [{"contractTypes": ["Ownable", "PackageRegistry", "PackageRegistryInterface"], "name": "solc", "settings": {"optimize": false}, "version": "0.5.10+commit.5a6ea5b1"}], "contractTypes": {"Ownable": {"abi": [{"constant": true, "inputs": [], "name": "owner", "outputs": [{"name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "isOwner", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "payable": false, "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "previousOwner", "type": "address"}, {"indexed": true, "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}], "deploymentBytecode": {"bytecode": "0x"}, "devdoc": {"details": "Contract module which provides a basic access control mechanism, where there is an account (an owner) that can be granted exclusive access to specific functions. * This module is used through inheritance. It will make available the modifier `onlyOwner`, which can be applied to your functions to restrict their use to the owner.", "methods": {"constructor": {"details": "Initializes the contract setting the deployer as the initial owner."}, "isOwner()": {"details": "Returns true if the caller is the current owner."}, "owner()": {"details": "Returns the address of the current owner."}, "transferOwnership(address)": {"details": "Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner."}}}, "runtimeBytecode": {"bytecode": "0x"}, "sourceId": "Ownable.sol"}, "PackageRegistry": {"abi": [{"constant": true, "inputs": [{"name": "packageId", "type": "bytes32"}], "name": "getPackageName", "outputs": [{"name": "packageName", "type": "string"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "packageName", "type": "string"}, {"name": "version", "type": "string"}], "name": "getReleaseId", "outputs": [{"name": "releaseId", "type": "bytes32"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "packageName", "type": "string"}, {"name": "version", "type": "string"}, {"name": "manifestURI", "type": "string"}], "name": "release", "outputs": [{"name": "", "type": "bytes32"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"name": "offset", "type": "uint256"}, {"name": "limit", "type": "uint256"}], "name": "getAllPackageIds", "outputs": [{"name": "packageIds", "type": "bytes32[]"}, {"name": "pointer", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "releaseId", "type": "bytes32"}], "name": "getReleaseData", "outputs": [{"name": "packageName", "type": "string"}, {"name": "version", "type": "string"}, {"name": "manifestURI", "type": "string"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "", "type": "bytes32"}], "name": "packages", "outputs": [{"name": "exists", "type": "bool"}, {"name": "createdAt", "type": "uint256"}, {"name": "updatedAt", "type": "uint256"}, {"name": "releaseCount", "type": "uint256"}, {"name": "name", "type": "string"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "value", "type": "string"}], "name": "validateStringIdentifier", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "pure", "type": "function"}, {"constant": true, "inputs": [{"name": "packageName", "type": "string"}], "name": "packageExists", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "owner", "outputs": [{"name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "isOwner", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "name", "type": "string"}], "name": "validatePackageName", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "pure", "type": "function"}, {"constant": true, "inputs": [], "name": "numPackageIds", "outputs": [{"name": "totalCount", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "name", "type": "string"}], "name": "generatePackageId", "outputs": [{"name": "", "type": "bytes32"}], "payable": false, "stateMutability": "pure", "type": "function"}, {"constant": true, "inputs": [{"name": "packageName", "type": "string"}, {"name": "version", "type": "string"}], "name": "generateReleaseId", "outputs": [{"name": "", "type": "bytes32"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "releaseCount", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "packageName", "type": "string"}, {"name": "offset", "type": "uint256"}, {"name": "limit", "type": "uint256"}], "name": "getAllReleaseIds", "outputs": [{"name": "releaseIds", "type": "bytes32[]"}, {"name": "pointer", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "packageName", "type": "string"}], "name": "numReleaseIds", "outputs": [{"name": "totalCount", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "packageCount", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "packageName", "type": "string"}, {"name": "version", "type": "string"}], "name": "releaseExists", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"name": "", "type": "bytes32"}], "name": "releases", "outputs": [{"name": "exists", "type": "bool"}, {"name": "createdAt", "type": "uint256"}, {"name": "packageId", "type": "bytes32"}, {"name": "version", "type": "string"}, {"name": "manifestURI", "type": "string"}], "payable": false, "stateMutability": "view", "type": "function"}, {"anonymous": false, "inputs": [{"indexed": false, "name": "packageName", "type": "string"}, {"indexed": false, "name": "version", "type": "string"}, {"indexed": false, "name": "manifestURI", "type": "string"}], "name": "VersionRelease", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "old<PERSON>wner", "type": "address"}, {"indexed": true, "name": "new<PERSON>wner", "type": "address"}], "name": "PackageTransfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "previousOwner", "type": "address"}, {"indexed": true, "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}], "deploymentBytecode": {"bytecode": "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"}, "devdoc": {"author": "<PERSON> <<EMAIL>>", "methods": {"generatePackageId(string)": {"details": "Returns name hash for a given package name.", "params": {"name": "Package name"}}, "getAllPackageIds(uint256,uint256)": {"details": "Returns a slice of the array of all package ids for the named package.", "params": {"limit": "The length of the slice", "offset": "The starting index for the slice."}}, "getAllReleaseIds(string,uint256,uint256)": {"details": "Returns a slice of the array of all release hashes for the named package.", "params": {"limit": "The length of the slice", "offset": "The starting index for the slice.", "packageName": "Package name"}}, "getPackageName(bytes32)": {"details": "Returns the string name of the package associated with a package id", "params": {"packageId": "The package id to look up"}}, "getReleaseData(bytes32)": {"details": "Returns the package data for a release.", "params": {"releaseId": "Release id"}}, "getReleaseId(string,string)": {"details": "Returns the release id for a given name and version pair if present on registry.", "params": {"packageName": "Package name", "version": "Version string(ex: '1.0.0')"}}, "isOwner()": {"details": "Returns true if the caller is the current owner."}, "numPackageIds()": {"details": "Returns the number of packages stored on the registry"}, "numReleaseIds(string)": {"details": "Returns the number of releases for a given package name on the registry", "params": {"packageName": "Package name"}}, "owner()": {"details": "Returns the address of the current owner."}, "packageExists(string)": {"details": "Returns a bool indicating whether the given package exists in this registry.", "params": {"packageName": "Package Name"}}, "release(string,string,string)": {"details": "Creates a new release for the named package.  If this is the first release for the given package then this will also create and store the package.  Returns releaseID if successful.", "params": {"manifestURI": "The URI for the release manifest for this release.", "packageName": "Package name", "version": "Version string (ex: '1.0.0')"}}, "releaseExists(string,string)": {"details": "Returns a bool indicating whether the given release exists in this registry.", "params": {"packageName": "Package Name", "version": "version"}}, "transferOwnership(address)": {"details": "Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner."}, "validatePackageName(string)": {"details": "Returns boolean whether the provided package name is valid.", "params": {"name": "The name of the package."}}, "validateStringIdentifier(string)": {"details": "Returns boolean whether the input string has a length", "params": {"value": "The string to validate."}}}, "title": "Contract for an ERC1319 Registry, adapted from ethpm/escape-truffle"}, "runtimeBytecode": {"bytecode": "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"}, "sourceId": "PackageRegistry.sol"}, "PackageRegistryInterface": {"abi": [{"constant": true, "inputs": [{"name": "packageId", "type": "bytes32"}], "name": "getPackageName", "outputs": [{"name": "packageName", "type": "string"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "packageName", "type": "string"}, {"name": "version", "type": "string"}], "name": "getReleaseId", "outputs": [{"name": "releaseId", "type": "bytes32"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "packageName", "type": "string"}, {"name": "version", "type": "string"}, {"name": "manifestURI", "type": "string"}], "name": "release", "outputs": [{"name": "releaseId", "type": "bytes32"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"name": "offset", "type": "uint256"}, {"name": "limit", "type": "uint256"}], "name": "getAllPackageIds", "outputs": [{"name": "packageIds", "type": "bytes32[]"}, {"name": "pointer", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "releaseId", "type": "bytes32"}], "name": "getReleaseData", "outputs": [{"name": "packageName", "type": "string"}, {"name": "version", "type": "string"}, {"name": "manifestURI", "type": "string"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "numPackageIds", "outputs": [{"name": "totalCount", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "packageName", "type": "string"}, {"name": "version", "type": "string"}], "name": "generateReleaseId", "outputs": [{"name": "releaseId", "type": "bytes32"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "packageName", "type": "string"}, {"name": "offset", "type": "uint256"}, {"name": "limit", "type": "uint256"}], "name": "getAllReleaseIds", "outputs": [{"name": "releaseIds", "type": "bytes32[]"}, {"name": "pointer", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "packageName", "type": "string"}], "name": "numReleaseIds", "outputs": [{"name": "totalCount", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}], "deploymentBytecode": {"bytecode": "0x"}, "devdoc": {"author": "<PERSON> <piperm<PERSON><EMAIL>>, <PERSON> <christopher<PERSON><PERSON><PERSON>@gmail.com>", "methods": {"getAllPackageIds(uint256,uint256)": {"details": "Returns a slice of the array of all package ids for the named package.", "params": {"limit": "The length of the slice", "offset": "The starting index for the slice."}}, "getAllReleaseIds(string,uint256,uint256)": {"details": "Returns a slice of the array of all release hashes for the named package.", "params": {"limit": "The length of the slice", "offset": "The starting index for the slice.", "packageName": "Package name"}}, "getPackageName(bytes32)": {"details": "Returns the string name of the package associated with a package id", "params": {"packageId": "The package id to look up"}}, "getReleaseData(bytes32)": {"details": "Returns the package data for a release.", "params": {"releaseId": "Release id"}}, "getReleaseId(string,string)": {"details": "Returns the release id for a given name and version pair if present on registry.", "params": {"packageName": "Package name", "version": "Version string(ex: '1.0.0')"}}, "numPackageIds()": {"details": "Returns the number of packages stored on the registry"}, "numReleaseIds(string)": {"details": "Returns the number of releases for a given package name on the registry", "params": {"packageName": "Package name"}}, "release(string,string,string)": {"details": "Creates a a new release for the named package.", "params": {"manifestURI": "The URI for the release manifest for this release.", "packageName": "Package name", "version": "Version string (ex: 1.0.0)"}}}, "title": "EIP 1319 Smart Contract Package Registry Interface"}, "runtimeBytecode": {"bytecode": "0x"}, "sourceId": "PackageRegistryInterface.sol"}}, "manifest": "ethpm/3", "name": "simple-registry", "sources": {"./Ownable.sol": {"content": "pragma solidity ^0.5.0;\n\n/**\n * @dev Contract module which provides a basic access control mechanism, where\n * there is an account (an owner) that can be granted exclusive access to\n * specific functions.\n *\n * This module is used through inheritance. It will make available the modifier\n * `onlyOwner`, which can be applied to your functions to restrict their use to\n * the owner.\n */\ncontract Ownable {\n    address private _owner;\n\n    event OwnershipTransferred(address indexed previousOwner, address indexed newOwner);\n\n    /**\n     * @dev Initializes the contract setting the deployer as the initial owner.\n     */\n    constructor () internal {\n        _owner = msg.sender;\n        emit OwnershipTransferred(address(0), _owner);\n    }\n\n    /**\n     * @dev Returns the address of the current owner.\n     */\n    function owner() public view returns (address) {\n        return _owner;\n    }\n\n    /**\n     * @dev Throws if called by any account other than the owner.\n     */\n    modifier onlyOwner() {\n        require(isOwner(), \"Ownable: caller is not the owner\");\n        _;\n    }\n\n    /**\n     * @dev Returns true if the caller is the current owner.\n     */\n    function isOwner() public view returns (bool) {\n        return msg.sender == _owner;\n    }\n\n    /**\n     * @dev Transfers ownership of the contract to a new account (`newOwner`).\n     * Can only be called by the current owner.\n     */\n    function transferOwnership(address newOwner) public onlyOwner {\n        _transferOwnership(newOwner);\n    }\n\n    /**\n     * @dev Transfers ownership of the contract to a new account (`newOwner`).\n     */\n    function _transferOwnership(address newOwner) internal {\n        require(newOwner != address(0), \"Ownable: new owner is the zero address\");\n        emit OwnershipTransferred(_owner, newOwner);\n        _owner = newOwner;\n    }\n}", "installPath": "./Ownable.sol", "type": "solidity"}, "./PackageRegistry.sol": {"content": "pragma solidity >=0.5.10;\n\nimport {PackageRegistryInterface} from \"./PackageRegistryInterface.sol\";\nimport {Ownable} from \"./Ownable.sol\";\n\n/// @title Contract for an ERC1319 Registry, adapted from ethpm/escape-truffle\n/// <AUTHOR> <<EMAIL>>\ncontract PackageRegistry is PackageRegistryInterface, Ownable {\n    struct Package {\n        bool exists;\n        uint createdAt;\n        uint updatedAt;\n        uint releaseCount;\n        string name;\n    }\n\n    struct Release {\n        bool exists;\n        uint createdAt;\n        bytes32 packageId;\n        string version;\n        string manifestURI;\n    }\n\n    mapping (bytes32 => Package) public packages;\n    mapping (bytes32 => Release) public releases;\n\n    // package_id#release_count => release_id\n    mapping (bytes32 => bytes32) packageReleaseIndex;\n    // Total package number (int128) => package_id (bytes32)\n    mapping (uint => bytes32) allPackageIds;\n    // Total release number (int128) => release_id (bytes32)\n    mapping (uint => bytes32) allReleaseIds;\n    // Total number of packages in registry\n    uint public packageCount;\n    // Total number of releases in registry\n    uint public releaseCount;\n\n    // Events\n    event VersionRelease(string packageName, string version, string manifestURI);\n    event PackageTransfer(address indexed oldOwner, address indexed newOwner);\n\n    // Modifiers\n    modifier onlyIfPackageExists(string memory packageName) {\n        require(packageExists(packageName), \"package-does-not-exist\");\n        _;\n    }\n\n    modifier onlyIfReleaseExists(string memory packageName, string memory version) {\n        require (releaseExists(packageName, version), \"release-does-not-exist\");\n        _;\n    }\n\n    //\n    // ===============\n    // |  Write API  |\n    // ===============\n    //\n\n    /// @dev Creates a new release for the named package.  If this is the first release for the given\n    /// package then this will also create and store the package.  Returns releaseID if successful.\n    /// @notice Will create a new release the given package with the given release information.\n    /// @param packageName Package name\n    /// @param version Version string (ex: '1.0.0')\n    /// @param manifestURI The URI for the release manifest for this release.\n    function release(\n        string memory packageName,\n        string memory version,\n        string memory manifestURI\n    )\n        public\n        onlyOwner\n        returns (bytes32)\n    {\n        validatePackageName(packageName);\n        validateStringIdentifier(version);\n        validateStringIdentifier(manifestURI);\n\n        // Compute hashes\n        bytes32 packageId = generatePackageId(packageName);\n        bytes32 releaseId = generateReleaseId(packageName, version);\n        Package storage package = packages[packageId];\n\n        // If the package does not yet exist create it\n        if (package.exists == false) {\n            package.exists = true;\n            package.createdAt = block.timestamp;\n            package.updatedAt = block.timestamp;\n            package.name = packageName;\n            package.releaseCount = 0;\n            allPackageIds[packageCount] = packageId;\n            packageCount++;\n        } else {\n            package.updatedAt = block.timestamp;\n        }\n        cutRelease(packageId, releaseId, packageName, version, manifestURI);\n        return releaseId;\n    }\n\n    function cutRelease(\n        bytes32 packageId,\n        bytes32 releaseId,\n        string memory packageName,\n        string memory version,\n        string memory manifestURI\n    )\n        private\n    {\n        Release storage newRelease = releases[releaseId];\n        require(newRelease.exists == false, \"release-already-exists\");\n\n        // Store new release data\n        newRelease.exists = true;\n        newRelease.createdAt = block.timestamp;\n        newRelease.packageId = packageId;\n        newRelease.version = version;\n        newRelease.manifestURI = manifestURI;\n\n        releases[releaseId] = newRelease;\n        allReleaseIds[releaseCount] = releaseId;\n        releaseCount++;\n\n        // Update package's release count\n        Package storage package = packages[packageId];\n        bytes32 packageReleaseId = generatePackageReleaseId(packageId, package.releaseCount);\n        packageReleaseIndex[packageReleaseId] = releaseId;\n        package.releaseCount++;\n\n        // Log the release.\n        emit VersionRelease(packageName, version, manifestURI);\n    }\n\n    //\n    // ==============\n    // |  Read API  |\n    // ==============\n    //\n\n    /// @dev Returns the string name of the package associated with a package id\n    /// @param packageId The package id to look up\n    function getPackageName(bytes32 packageId)\n        public\n        view\n        returns (string memory packageName)\n    {\n        Package memory targetPackage = packages[packageId];\n        require (targetPackage.exists == true, \"package-does-not-exist\");\n        return targetPackage.name;\n    }\n\n    /// @dev Returns a slice of the array of all package ids for the named package.\n    /// @param offset The starting index for the slice.\n    /// @param limit  The length of the slice\n    function getAllPackageIds(uint offset, uint limit)\n        public\n        view\n        returns (\n            bytes32[] memory packageIds,\n            uint pointer\n        )\n    {\n        bytes32[] memory hashes;                 // Array of package ids to return\n        uint cursor = offset;                    // Index counter to traverse DB array\n        uint remaining;                          // Counter to collect `limit` packages\n\n        // Is request within range?\n        if (cursor < packageCount){\n\n            // Get total remaining records\n            remaining = packageCount - cursor;\n\n            // Number of records to collect is lesser of `remaining` and `limit`\n            if (remaining > limit ){\n                remaining = limit;\n            }\n\n            // Allocate return array\n            hashes = new bytes32[](remaining);\n\n            // Collect records.\n            while(remaining > 0){\n                bytes32 hash = allPackageIds[cursor];\n                hashes[remaining - 1] = hash;\n                remaining--;\n                cursor++;\n            }\n        }\n        return (hashes, cursor);\n    }\n\n    /// @dev Returns a slice of the array of all release hashes for the named package.\n    /// @param packageName Package name\n    /// @param offset The starting index for the slice.\n    /// @param limit  The length of the slice\n    function getAllReleaseIds(string memory packageName, uint offset, uint limit)\n        public\n        view\n        onlyIfPackageExists(packageName)\n        returns (\n            bytes32[] memory releaseIds,\n            uint pointer\n        )\n    {\n        bytes32 packageId = generatePackageId(packageName);\n        Package storage package = packages[packageId];\n        bytes32[] memory hashes;                                    // Release ids to return\n        uint cursor = offset;                                       // Index counter to traverse DB array\n        uint remaining;                                             // Counter to collect `limit` packages\n        uint numPackageReleases = package.releaseCount;\t\t        // Total number of packages in registry\n\n        // Is request within range?\n        if (cursor < numPackageReleases){\n\n            // Get total remaining records\n            remaining = numPackageReleases - cursor;\n\n            // Number of records to collect is lesser of `remaining` and `limit`\n            if (remaining > limit ){\n                remaining = limit;\n            }\n\n            // Allocate return array\n            hashes = new bytes32[](remaining);\n\n            // Collect records.\n            while(remaining > 0){\n                bytes32 packageReleaseId = generatePackageReleaseId(packageId, cursor);\n                bytes32 hash = packageReleaseIndex[packageReleaseId];\n                hashes[remaining - 1] = hash;\n                remaining--;\n                cursor++;\n            }\n        }\n        return (hashes, cursor);\n    }\n\n\n    /// @dev Returns the package data for a release.\n    /// @param releaseId Release id\n    function getReleaseData(bytes32 releaseId)\n        public\n        view\n        returns (\n            string memory packageName, string memory version,\n            string memory manifestURI\n        )\n    {\n        Release memory targetRelease = releases[releaseId];\n        Package memory targetPackage = packages[targetRelease.packageId];\n        return (targetPackage.name, targetRelease.version, targetRelease.manifestURI);\n    }\n\n    /// @dev Returns the release id for a given name and version pair if present on registry.\n    /// @param packageName Package name\n    /// @param version Version string(ex: '1.0.0')\n    function getReleaseId(string memory packageName, string memory version)\n        public\n        view\n        onlyIfPackageExists(packageName)\n        onlyIfReleaseExists(packageName, version)\n        returns (bytes32 releaseId)\n    {\n        return generateReleaseId(packageName, version);\n    }\n\n    /// @dev Returns the number of packages stored on the registry\n    function numPackageIds() public view returns (uint totalCount)\n    {\n        return packageCount;\n    }\n\n    /// @dev Returns the number of releases for a given package name on the registry\n    /// @param packageName Package name\n    function numReleaseIds(string memory packageName)\n        public\n        view\n        onlyIfPackageExists(packageName)\n        returns (uint totalCount)\n    {\n        bytes32 packageId = generatePackageId(packageName);\n        Package storage package = packages[packageId];\n        return package.releaseCount;\n    }\n\n    /// @dev Returns a bool indicating whether the given release exists in this registry.\n    /// @param packageName Package Name\n    /// @param version version\n    function releaseExists(string memory packageName, string memory version)\n        public\n        view\n        onlyIfPackageExists(packageName)\n        returns (bool)\n    {\n        bytes32 releaseId = generateReleaseId(packageName, version);\n        Release storage targetRelease = releases[releaseId];\n        return targetRelease.exists;\n    }\n\n    /// @dev Returns a bool indicating whether the given package exists in this registry.\n    /// @param packageName Package Name\n    function packageExists(string memory packageName) public view returns (bool) {\n        bytes32 packageId = generatePackageId(packageName);\n        return packages[packageId].exists;\n    }\n\n    //\n    //  ====================\n    //  |  Hash Functions  |\n    //  ====================\n    // \n\n    /// @dev Returns name hash for a given package name.\n    /// @param name Package name\n    function generatePackageId(string memory name)\n        public\n        pure\n        returns (bytes32)\n    {\n        return keccak256(abi.encodePacked(name));\n    }\n\n    // @dev Returns release id that *would* be generated for a name and version pair on `release`.\n    // @param packageName Package name\n    // @param version Version string (ex: '1.0.0')\n    function generateReleaseId(\n        string memory packageName,\n        string memory version\n    )\n        public\n        view\n        returns (bytes32)\n    {\n        return keccak256(abi.encodePacked(packageName, version));\n    }\n\n    function generatePackageReleaseId(\n        bytes32 packageId,\n        uint packageReleaseCount\n    )\n        private\n\t\tpure\n        returns (bytes32)\n    {\n        return keccak256(abi.encodePacked(packageId, packageReleaseCount));\n    }\n\n\n    //\n    // ================\n    // |  Validation  |\n    // ================\n    //\n\n    /// @dev Returns boolean whether the provided package name is valid.\n    /// @param name The name of the package.\n    function validatePackageName(string memory name)\n        public\n        pure\n        returns (bool)\n    {\n        require (bytes(name).length > 2 && bytes(name).length < 255, \"invalid-package-name\");\n    }\n\n    /// @dev Returns boolean whether the input string has a length\n    /// @param value The string to validate.\n    function validateStringIdentifier(string memory value)\n        public\n        pure\n        returns (bool)\n    {\n        require (bytes(value).length != 0, \"invalid-string-identifier\");\n    }\n}", "installPath": "./PackageRegistry.sol", "type": "solidity"}, "./PackageRegistryInterface.sol": {"content": "pragma solidity >=0.5.10;\n\n\n/// @title EIP 1319 Smart Contract Package Registry Interface\n/// <AUTHOR> <<EMAIL>>, <PERSON> <christopher<PERSON><PERSON><EMAIL>>\ncontract PackageRegistryInterface {\n\n    //\n    // +-------------+\n    // |  Write API  |\n    // +-------------+\n    //\n\n    /// @dev Creates a a new release for the named package.\n    /// @notice Will create a new release the given package with the given release information.\n    /// @param packageName Package name\n    /// @param version Version string (ex: 1.0.0)\n    /// @param manifestURI The URI for the release manifest for this release.\n    function release(\n        string memory packageName,\n        string memory version,\n        string memory manifestURI\n    )\n        public\n        returns (bytes32 releaseId);\n\n    //\n    // +------------+\n    // |  Read API  |\n    // +------------+\n    //\n\n    /// @dev Returns the string name of the package associated with a package id\n    /// @param packageId The package id to look up\n    function getPackageName(bytes32 packageId)\n        public\n        view\n        returns (string memory packageName);\n\n    /// @dev Returns a slice of the array of all package ids for the named package.\n    /// @param offset The starting index for the slice.\n    /// @param limit  The length of the slice\n    function getAllPackageIds(uint offset, uint limit)\n        public\n        view\n        returns (\n            bytes32[] memory packageIds,\n            uint pointer\n        );\n\n    /// @dev Returns a slice of the array of all release hashes for the named package.\n    /// @param packageName Package name\n    /// @param offset The starting index for the slice.\n    /// @param limit  The length of the slice\n    function getAllReleaseIds(string memory packageName, uint offset, uint limit)\n        public\n        view\n        returns (\n            bytes32[] memory releaseIds,\n            uint pointer\n        );\n\n    /// @dev Returns the package data for a release.\n    /// @param releaseId Release id\n    function getReleaseData(bytes32 releaseId)\n        public\n        view\n        returns (\n            string memory packageName,\n            string memory version,\n            string memory manifestURI\n        );\n\n    // @dev Returns release id that *would* be generated for a name and version pair on `release`.\n    // @param packageName Package name\n    // @param version Version string (ex: '1.0.0')\n    function generateReleaseId(string memory packageName, string memory version)\n        public\n        view\n        returns (bytes32 releaseId);\n\n    /// @dev Returns the release id for a given name and version pair if present on registry.\n    /// @param packageName Package name\n    /// @param version Version string(ex: '1.0.0')\n    function getReleaseId(string memory packageName, string memory version)\n        public\n        view\n        returns (bytes32 releaseId);\n\n    /// @dev Returns the number of packages stored on the registry\n    function numPackageIds() public view returns (uint totalCount);\n\n    /// @dev Returns the number of releases for a given package name on the registry\n    /// @param packageName Package name\n    function numReleaseIds(string memory packageName) public view returns (uint totalCount);\n}", "installPath": "./PackageRegistryInterface.sol", "type": "solidity"}}, "version": "1.0.0"}