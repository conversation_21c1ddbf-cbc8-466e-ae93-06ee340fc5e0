language: c
os: linux
addons:
  apt:
    packages: libgmp-dev
compiler:
  - clang
  - gcc
cache:
  directories:
  - src/java/guava/
env:
  global:
    - FIELD=auto  BIGNUM=auto  SCALAR=auto  ENDOMORPHISM=no  STATICPRECOMPUTATION=yes  ECMULTGENPRECISION=auto  ASM=no  BUILD=check  EXTRAFLAGS=  HOST=  ECDH=no  RECOVERY=no  EXPERIMENTAL=no  JNI=no
    - GUAVA_URL=https://search.maven.org/remotecontent?filepath=com/google/guava/guava/18.0/guava-18.0.jar GUAVA_JAR=src/java/guava/guava-18.0.jar
  matrix:
    - SCALAR=32bit    RECOVERY=yes
    - SCALAR=32bit    FIELD=32bit       ECDH=yes  EXPERIMENTAL=yes
    - SCALAR=64bit
    - FIELD=64bit     RECOVERY=yes
    - FIELD=64bit     ENDOMORPHISM=yes
    - FIELD=64bit     ENDOMORPHISM=yes  ECDH=yes EXPERIMENTAL=yes
    - FIELD=64bit                       ASM=x86_64
    - FIELD=64bit     ENDOMORPHISM=yes  ASM=x86_64
    - FIELD=32bit     ENDOMORPHISM=yes
    - BIGNUM=no
    - BIGNUM=no       ENDOMORPHISM=yes RECOVERY=yes EXPERIMENTAL=yes
    - BIGNUM=no       STATICPRECOMPUTATION=no
    - BUILD=distcheck
    - EXTRAFLAGS=CPPFLAGS=-DDETERMINISTIC
    - EXTRAFLAGS=CFLAGS=-O0
    - BUILD=check-java JNI=yes ECDH=yes EXPERIMENTAL=yes
    - ECMULTGENPRECISION=2
    - ECMULTGENPRECISION=8
matrix:
  fast_finish: true
  include:
    - compiler: clang
      env: HOST=i686-linux-gnu ENDOMORPHISM=yes
      addons:
        apt:
          packages:
            - gcc-multilib
            - libgmp-dev:i386
    - compiler: clang
      env: HOST=i686-linux-gnu
      addons:
        apt:
          packages:
            - gcc-multilib
    - compiler: gcc
      env: HOST=i686-linux-gnu ENDOMORPHISM=yes
      addons:
        apt:
          packages:
            - gcc-multilib
    - compiler: gcc
      env: HOST=i686-linux-gnu
      addons:
        apt:
          packages:
            - gcc-multilib
            - libgmp-dev:i386
    - compiler: gcc
      env:
        - BIGNUM=no  ENDOMORPHISM=yes  ASM=x86_64 EXPERIMENTAL=yes ECDH=yes  RECOVERY=yes
        - VALGRIND=yes EXTRAFLAGS="--disable-openssl-tests CPPFLAGS=-DVALGRIND" BUILD=
      addons:
        apt:
          packages:
            - valgrind
    - compiler: gcc
      env: # The same as above but without endomorphism.
        - BIGNUM=no  ENDOMORPHISM=no  ASM=x86_64 EXPERIMENTAL=yes ECDH=yes  RECOVERY=yes
        - VALGRIND=yes EXTRAFLAGS="--disable-openssl-tests CPPFLAGS=-DVALGRIND" BUILD=
      addons:
        apt:
          packages:
            - valgrind

before_install: mkdir -p `dirname $GUAVA_JAR`
install: if [ ! -f $GUAVA_JAR ]; then wget $GUAVA_URL -O $GUAVA_JAR; fi
before_script: ./autogen.sh

script:
 - if [ -n "$HOST" ]; then export USE_HOST="--host=$HOST"; fi
 - if [ "x$HOST" = "xi686-linux-gnu" ]; then export CC="$CC -m32"; fi
 - ./configure --enable-experimental=$EXPERIMENTAL --enable-endomorphism=$ENDOMORPHISM --with-field=$FIELD --with-bignum=$BIGNUM --with-asm=$ASM --with-scalar=$SCALAR --enable-ecmult-static-precomputation=$STATICPRECOMPUTATION --with-ecmult-gen-precision=$ECMULTGENPRECISION --enable-module-ecdh=$ECDH --enable-module-recovery=$RECOVERY --enable-jni=$JNI $EXTRAFLAGS $USE_HOST
 - if [ -n "$BUILD" ]; then make -j2 $BUILD; fi
 - # travis_wait extends the 10 minutes without output allowed (https://docs.travis-ci.com/user/common-build-problems/#build-times-out-because-no-output-was-received)
 - # the `--error-exitcode` is required to make the test fail if valgrind found errors, otherwise it'll return 0 (http://valgrind.org/docs/manual/manual-core.html)
 - if [ -n "$VALGRIND" ]; then
   make -j2 &&
   travis_wait 30 valgrind --error-exitcode=42 ./tests 16 &&
   travis_wait 30 valgrind --error-exitcode=42 ./exhaustive_tests;
   fi

after_script:
    - cat ./tests.log
    - cat ./exhaustive_tests.log
