{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,mCAAgD;AAChD,yCAA0D;AAU1D;;GAEG;AACH;IAiDE;;;;;OAKG;IACH,gBACE,KAA+B,EAC/B,QAAwB,EACxB,kBAAkC;QAElC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;QACxC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAA;QACrB,IAAI,CAAC,mBAAmB,GAAG,kBAAkB,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,kBAAkB,CAAA;QACrF,IAAI,QAAQ,EAAE;YACZ,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAA;SAC3B;IACH,CAAC;IA7DD;;;;;;;;;OASG;IACI,qBAAc,GAArB,UACE,SAA0B,EAC1B,iBAAiC,EACjC,QAAwB,EACxB,kBAAkC;QAElC,IAAM,mBAAmB,GAAG,MAAM,CAAC,eAAe,CAAC,SAAS,CAAC,CAAA;QAE7D,OAAO,IAAI,MAAM,uBAEV,mBAAmB,GACnB,iBAAiB,GAEtB,QAAQ,EACR,kBAAkB,CACnB,CAAA;IACH,CAAC;IAEc,sBAAe,GAA9B,UAA+B,KAAsB;QACnD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,IAAI,eAAW,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,EAAE;gBAC/B,OAAO,eAAW,CAAC,eAAW,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAA;aAChD;YAED,MAAM,IAAI,KAAK,CAAC,mBAAiB,KAAK,mBAAgB,CAAC,CAAA;SACxD;QAED,IAAI,eAAW,CAAC,KAAK,CAAC,EAAE;YACtB,OAAO,eAAW,CAAC,KAAK,CAAC,CAAA;SAC1B;QAED,MAAM,IAAI,KAAK,CAAC,qBAAmB,KAAK,mBAAgB,CAAC,CAAA;IAC3D,CAAC;IAqBD;;;;;OAKG;IACH,yBAAQ,GAAR,UAAS,KAA+B;QACtC,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC1D,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;SAClD;aAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YACpC,IAAM,QAAQ,GAAG,CAAC,WAAW,EAAE,SAAS,EAAE,WAAW,EAAE,gBAAgB,CAAC,CAAA;YACxE,KAAoB,UAAQ,EAAR,qBAAQ,EAAR,sBAAQ,EAAR,IAAQ,EAAE;gBAAzB,IAAM,KAAK,iBAAA;gBACd,IAAU,KAAM,CAAC,KAAK,CAAC,KAAK,SAAS,EAAE;oBACrC,MAAM,IAAI,KAAK,CAAC,uCAAqC,KAAO,CAAC,CAAA;iBAC9D;aACF;YACD,IAAI,CAAC,YAAY,GAAG,KAAc,CAAA;SACnC;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAA;SACtC;QACD,OAAO,IAAI,CAAC,YAAY,CAAA;IAC1B,CAAC;IAED;;;OAGG;IACH,4BAAW,GAAX,UAAY,QAAuB;QACjC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,EAAE;YACxC,MAAM,IAAI,KAAK,CAAC,cAAY,QAAQ,gDAA6C,CAAC,CAAA;SACnF;QACD,IAAI,OAAO,GAAG,KAAK,CAAA;QACnB,KAAwB,UAAe,EAAf,oBAAA,qBAAe,EAAf,6BAAe,EAAf,IAAe,EAAE;YAApC,IAAM,SAAS,wBAAA;YAClB,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;gBAC7B,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAA;gBACzB,OAAO,GAAG,IAAI,CAAA;aACf;SACF;QACD,IAAI,CAAC,OAAO,EAAE;YACZ,MAAM,IAAI,KAAK,CAAC,wBAAsB,QAAQ,mBAAgB,CAAC,CAAA;SAChE;IACH,CAAC;IAED;;;;OAIG;IACH,gCAAe,GAAf,UAAgB,QAAwB,EAAE,aAAuB;QAC/D,aAAa,GAAG,aAAa,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,CAAA;QAClE,IAAI,CAAC,QAAQ,EAAE;YACb,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACnB,MAAM,IAAI,KAAK,CAAC,iEAAiE,CAAC,CAAA;aACnF;iBAAM;gBACL,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAA;aAC1B;SACF;aAAM,IAAI,aAAa,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,EAAE;YAChE,MAAM,IAAI,KAAK,CAAC,cAAY,QAAQ,gDAA6C,CAAC,CAAA;SACnF;QACD,OAAO,QAAQ,CAAA;IACjB,CAAC;IAED;;;;OAIG;IACH,6BAAY,GAAZ,UAAa,QAAgB;QAC3B,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,EAAE,CAAA;QAC5B,KAAiB,UAAG,EAAH,WAAG,EAAH,iBAAG,EAAH,IAAG,EAAE;YAAjB,IAAM,EAAE,YAAA;YACX,IAAI,EAAE,CAAC,MAAM,CAAC,KAAK,QAAQ;gBAAE,OAAO,EAAE,CAAA;SACvC;QACD,MAAM,IAAI,KAAK,CAAC,cAAY,QAAQ,+BAA0B,IAAI,CAAC,SAAS,EAAI,CAAC,CAAA;IACnF,CAAC;IAED;;;;OAIG;IACH,qCAAoB,GAApB,UAAqB,QAAuB;QAC1C,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE;YACvC,KAA0B,UAAwB,EAAxB,KAAA,IAAI,CAAC,mBAAmB,EAAxB,cAAwB,EAAxB,IAAwB,EAAE;gBAA/C,IAAM,WAAW,SAAA;gBACpB,IAAI,QAAQ,KAAK,WAAW;oBAAE,OAAO,IAAI,CAAA;aAC1C;SACF;aAAM;YACL,OAAO,IAAI,CAAA;SACZ;QACD,OAAO,KAAK,CAAA;IACd,CAAC;IAED;;;;;OAKG;IACH,sBAAK,GAAL,UAAM,KAAa,EAAE,IAAY,EAAE,QAAiB;QAClD,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAA;QAEzC,IAAI,KAAK,CAAA;QACT,KAAwB,UAAe,EAAf,oBAAA,qBAAe,EAAf,6BAAe,EAAf,IAAe,EAAE;YAApC,IAAM,SAAS,wBAAA;YAClB,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;gBACxB,MAAM,IAAI,KAAK,CAAC,WAAS,KAAK,iBAAc,CAAC,CAAA;aAC9C;YACD,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,SAAS,EAAE;gBAC3C,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;aACpC;YACD,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,QAAQ;gBAAE,MAAK;SACrC;QACD,IAAI,KAAK,KAAK,SAAS,EAAE;YACvB,MAAM,IAAI,KAAK,CAAI,KAAK,mBAAc,IAAI,eAAY,CAAC,CAAA;SACxD;QACD,OAAO,KAAK,CAAA;IACd,CAAC;IAED;;;;;OAKG;IACH,6BAAY,GAAZ,UAAa,KAAa,EAAE,IAAY,EAAE,WAAmB;QAC3D,IAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAA;QACnD,IAAM,QAAQ,GAAG,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAA;QACxD,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAA;IAC1C,CAAC;IAED;;;;;;OAMG;IACH,wCAAuB,GAAvB,UACE,QAAuB,EACvB,WAAmB,EACnB,IAAsB;QAEtB,IAAI,GAAG,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAA;QACrC,IAAM,aAAa,GAAG,IAAI,CAAC,aAAa,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAA;QACnF,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAA;QACxD,IAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAA;QAC5C,IAAI,OAAO,KAAK,IAAI,IAAI,WAAW,IAAI,OAAO;YAAE,OAAO,IAAI,CAAA;QAC3D,OAAO,KAAK,CAAA;IACd,CAAC;IAED;;;;;OAKG;IACH,8BAAa,GAAb,UAAc,WAAmB,EAAE,IAAsB;QACvD,OAAO,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE,WAAW,EAAE,IAAI,CAAC,CAAA;IAC9D,CAAC;IAED;;;;;;OAMG;IACH,oCAAmB,GAAnB,UACE,SAAwB,EACxB,SAAiB,EACjB,IAAsB;QAEtB,IAAI,GAAG,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAA;QACrC,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAA;QAC1E,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,CAAA;QAE/D,IAAI,SAAS,CAAA;QACb,IAAI,UAAU,EAAE;YACd,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;SAC7C;aAAM;YACL,SAAS,GAAG,IAAI,CAAC,SAAS,EAAE,CAAA;SAC7B;QAED,IAAI,MAAM,GAAG,CAAC,CAAC,EACb,MAAM,GAAG,CAAC,CAAC,CAAA;QACb,IAAI,KAAK,GAAG,CAAC,CAAA;QACb,KAAiB,UAAS,EAAT,uBAAS,EAAT,uBAAS,EAAT,IAAS,EAAE;YAAvB,IAAM,EAAE,kBAAA;YACX,IAAI,EAAE,CAAC,MAAM,CAAC,KAAK,SAAS;gBAAE,MAAM,GAAG,KAAK,CAAA;YAC5C,IAAI,EAAE,CAAC,MAAM,CAAC,KAAK,SAAS;gBAAE,MAAM,GAAG,KAAK,CAAA;YAC5C,KAAK,IAAI,CAAC,CAAA;SACX;QACD,OAAO,MAAM,IAAI,MAAM,CAAA;IACzB,CAAC;IAED;;;;;OAKG;IACH,4BAAW,GAAX,UAAY,QAAgB,EAAE,IAAsB;QAClD,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;IACvD,CAAC;IAED;;;;;OAKG;IACH,wCAAuB,GAAvB,UAAwB,QAAwB,EAAE,IAAsB;QACtE,IAAI,GAAG,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAA;QACrC,IAAM,aAAa,GAAG,IAAI,CAAC,aAAa,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAA;QACnF,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAA;QACxD,KAAiB,UAAgB,EAAhB,KAAA,IAAI,CAAC,SAAS,EAAE,EAAhB,cAAgB,EAAhB,IAAgB,EAAE;YAA9B,IAAM,EAAE,SAAA;YACX,IAAI,EAAE,CAAC,MAAM,CAAC,KAAK,QAAQ,IAAI,EAAE,CAAC,OAAO,CAAC,KAAK,IAAI;gBAAE,OAAO,IAAI,CAAA;SACjE;QACD,OAAO,KAAK,CAAA;IACd,CAAC;IAED;;;;;OAKG;IACH,gCAAe,GAAf,UAAgB,WAA2B,EAAE,IAAsB;QACjE,IAAI,GAAG,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAA;QACrC,IAAM,eAAe,GAAG,EAAE,CAAA;QAC1B,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,EAAE,CAAA;QAC5B,KAAiB,UAAG,EAAH,WAAG,EAAH,iBAAG,EAAH,IAAG,EAAE;YAAjB,IAAM,EAAE,YAAA;YACX,IAAI,EAAE,CAAC,OAAO,CAAC,KAAK,IAAI;gBAAE,SAAQ;YAClC,IAAI,WAAW,KAAK,SAAS,IAAI,WAAW,KAAK,IAAI,IAAI,WAAW,GAAG,EAAE,CAAC,OAAO,CAAC;gBAAE,MAAK;YACzF,IAAI,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;gBAAE,SAAQ;YAE1E,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;SACzB;QACD,OAAO,eAAe,CAAA;IACxB,CAAC;IAED;;;;;OAKG;IACH,+BAAc,GAAd,UAAe,WAA2B,EAAE,IAAsB;QAChE,IAAI,GAAG,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAA;QACrC,IAAM,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,IAAI,CAAC,CAAA;QAC/D,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;YAC9B,OAAO,eAAe,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAA;SAC3D;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAA;SACxD;IACH,CAAC;IAED;;;;OAIG;IACH,8BAAa,GAAb,UAAc,QAAiB;QAC7B,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAA;QAChD,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAA;IAC7C,CAAC;IAED;;;;;OAKG;IACH,gCAAe,GAAf,UAAgB,WAAmB,EAAE,QAAiB;QACpD,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAA;QAChD,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,WAAW,EAAE;YAChD,OAAO,IAAI,CAAA;SACZ;aAAM;YACL,OAAO,KAAK,CAAA;SACb;IACH,CAAC;IAED;;;;OAIG;IACH,0BAAS,GAAT,UAAU,QAAiB;QACzB,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAA;QACzC,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAA;IACjD,CAAC;IAED;;;;OAIG;IACH,yBAAQ,GAAR,UAAS,QAAiB;QACxB,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAA;QACzC,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAA;IAChD,CAAC;IAED;;;OAGG;IACH,wBAAO,GAAP;QACE,OAAa,IAAI,CAAC,YAAa,CAAC,SAAS,CAAC,CAAA;IAC5C,CAAC;IAED;;;OAGG;IACH,0BAAS,GAAT;QACE,OAAa,IAAI,CAAC,YAAa,CAAC,WAAW,CAAC,CAAA;IAC9C,CAAC;IAED;;;OAGG;IACH,+BAAc,GAAd;QACE,OAAa,IAAI,CAAC,YAAa,CAAC,gBAAgB,CAAC,CAAA;IACnD,CAAC;IAED;;;OAGG;IACH,yBAAQ,GAAR;QACE,OAAO,IAAI,CAAC,SAAS,CAAA;IACvB,CAAC;IAED;;;OAGG;IACH,wBAAO,GAAP;QACE,OAAqB,IAAI,CAAC,YAAa,CAAC,SAAS,CAAC,CAAA;IACpD,CAAC;IAED;;;OAGG;IACH,0BAAS,GAAT;QACE,OAAO,eAAW,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,IAAU,IAAI,CAAC,YAAa,CAAC,MAAM,CAAC,CAAA;IACjF,CAAC;IAED;;;OAGG;IACH,0BAAS,GAAT;QACE,OAAa,IAAI,CAAC,YAAa,CAAC,WAAW,CAAC,CAAA;IAC9C,CAAC;IACH,aAAC;AAAD,CAAC,AAtaD,IAsaC"}