import { IJsonRpcSubscription, IJsonRpcRequest, IJsonRpcResponseSuccess, IJsonRpcResponseError, IInternalEvent } from "@walletconnect/types";
export declare function isEmptyString(value: string): boolean;
export declare function isEmptyArray(array: any[]): boolean;
export declare function isBuffer(val: any): boolean;
export declare function isTypedArray(val: any): boolean;
export declare function isArrayBuffer(val: any): boolean;
export declare function getType(val: any): "string" | "number" | "bigint" | "boolean" | "symbol" | "undefined" | "object" | "function" | "buffer" | "array" | "typed-array" | "array-buffer";
export declare function getEncoding(val: any): "hex" | "utf8" | "binary";
export declare function isHexString(value: any, length?: number): boolean;
export declare function isJsonRpcSubscription(object: any): object is IJsonRpcSubscription;
export declare function isJsonRpcRequest(object: any): object is IJsonRpcRequest;
export declare function isJsonRpcResponseSuccess(object: any): object is IJsonRpcResponseSuccess;
export declare function isJsonRpcResponseError(object: any): object is IJsonRpcResponseError;
export declare function isInternalEvent(object: any): object is IInternalEvent;
export declare function isReservedEvent(event: string): boolean;
export declare function isSilentPayload(request: IJsonRpcRequest): boolean;
//# sourceMappingURL=validators.d.ts.map