{"version": 3, "file": "index.modern.mjs", "sources": ["../src/index.ts"], "sourcesContent": ["// symbols\nconst TRACK_MEMO_SYMBOL = Symbol();\nconst GET_ORIGINAL_SYMBOL = Symbol();\n\n// properties\nconst AFFECTED_PROPERTY = 'a';\nconst IS_TARGET_COPIED_PROPERTY = 'f';\nconst PROXY_PROPERTY = 'p';\nconst PROXY_CACHE_PROPERTY = 'c';\nconst TARGET_CACHE_PROPERTY = 't';\nconst NEXT_OBJECT_PROPERTY = 'n';\nconst CHANGED_PROPERTY = 'g';\nconst HAS_KEY_PROPERTY = 'h';\nconst ALL_OWN_KEYS_PROPERTY = 'w';\nconst HAS_OWN_KEY_PROPERTY = 'o';\nconst KEYS_PROPERTY = 'k';\n\n// function to create a new bare proxy\nlet newProxy = <T extends object>(\n  target: T,\n  handler: ProxyHandler<T>,\n) => new Proxy(target, handler);\n\n// get object prototype\nconst getProto = Object.getPrototypeOf;\n\nconst objectsToTrack = new WeakMap<object, boolean>();\n\n// check if obj is a plain object or an array\nconst isObjectToTrack = <T>(obj: T): obj is T extends object ? T : never => (\n  obj && (objectsToTrack.has(obj as unknown as object)\n    ? objectsToTrack.get(obj as unknown as object) as boolean\n    : (getProto(obj) === Object.prototype || getProto(obj) === Array.prototype)\n  )\n);\n\n// check if it is object\nconst isObject = (x: unknown): x is object => (\n  typeof x === 'object' && x !== null\n);\n\n// Properties that are both non-configurable and non-writable will break\n// the proxy get trap when we try to return a recursive/child compare proxy\n// from them. We can avoid this by making a copy of the target object with\n// all descriptors marked as configurable, see `copyTargetObject`.\n// See: https://github.com/dai-shi/proxy-compare/pull/8\nconst needsToCopyTargetObject = (obj: object) => (\n  Object.values(Object.getOwnPropertyDescriptors(obj)).some(\n    (descriptor) => !descriptor.configurable && !descriptor.writable,\n  )\n);\n\n// Make a copy with all descriptors marked as configurable.\nconst copyTargetObject = <T extends object>(obj: T): T => {\n  if (Array.isArray(obj)) {\n    // Arrays need a special way to copy\n    return Array.from(obj) as T;\n  }\n  // For non-array objects, we create a new object keeping the prototype\n  // with changing all configurable options (otherwise, proxies will complain)\n  const descriptors = Object.getOwnPropertyDescriptors(obj);\n  Object.values(descriptors).forEach((desc) => { desc.configurable = true; });\n  return Object.create(getProto(obj), descriptors);\n};\n\ntype HasKeySet = Set<string | symbol>\ntype HasOwnKeySet = Set<string | symbol>\ntype KeysSet = Set<string | symbol>\ntype Used = {\n  [HAS_KEY_PROPERTY]?: HasKeySet;\n  [ALL_OWN_KEYS_PROPERTY]?: true;\n  [HAS_OWN_KEY_PROPERTY]?: HasOwnKeySet;\n  [KEYS_PROPERTY]?: KeysSet;\n};\ntype Affected = WeakMap<object, Used>;\ntype ProxyHandlerState<T extends object> = {\n  readonly [IS_TARGET_COPIED_PROPERTY]: boolean;\n  [PROXY_PROPERTY]?: T;\n  [PROXY_CACHE_PROPERTY]?: ProxyCache<object> | undefined;\n  [TARGET_CACHE_PROPERTY]?: TargetCache<object> | undefined;\n  [AFFECTED_PROPERTY]?: Affected;\n}\ntype ProxyCache<T extends object> = WeakMap<\n  object,\n  readonly [ProxyHandler<T>, ProxyHandlerState<T>]\n>;\ntype TargetCache<T extends object> = WeakMap<\n  object,\n  readonly [target: T, copiedTarget?: T]\n>;\n\nconst createProxyHandler = <T extends object>(origObj: T, isTargetCopied: boolean) => {\n  const state: ProxyHandlerState<T> = {\n    [IS_TARGET_COPIED_PROPERTY]: isTargetCopied,\n  };\n  let trackObject = false; // for trackMemo\n  const recordUsage = (\n    type:\n      | typeof HAS_KEY_PROPERTY\n      | typeof ALL_OWN_KEYS_PROPERTY\n      | typeof HAS_OWN_KEY_PROPERTY\n      | typeof KEYS_PROPERTY,\n    key?: string | symbol,\n  ) => {\n    if (!trackObject) {\n      let used = (state[AFFECTED_PROPERTY] as Affected).get(origObj);\n      if (!used) {\n        used = {};\n        (state[AFFECTED_PROPERTY] as Affected).set(origObj, used);\n      }\n      if (type === ALL_OWN_KEYS_PROPERTY) {\n        used[ALL_OWN_KEYS_PROPERTY] = true;\n      } else {\n        let set = used[type];\n        if (!set) {\n          set = new Set();\n          used[type] = set;\n        }\n        set.add(key as string | symbol);\n      }\n    }\n  };\n  const recordObjectAsUsed = () => {\n    trackObject = true;\n    (state[AFFECTED_PROPERTY] as Affected).delete(origObj);\n  };\n  const handler: ProxyHandler<T> = {\n    get(target, key) {\n      if (key === GET_ORIGINAL_SYMBOL) {\n        return origObj;\n      }\n      recordUsage(KEYS_PROPERTY, key);\n      return createProxy(\n        Reflect.get(target, key),\n        (state[AFFECTED_PROPERTY] as Affected),\n        state[PROXY_CACHE_PROPERTY],\n        state[TARGET_CACHE_PROPERTY],\n      );\n    },\n    has(target, key) {\n      if (key === TRACK_MEMO_SYMBOL) {\n        recordObjectAsUsed();\n        return true;\n      }\n      recordUsage(HAS_KEY_PROPERTY, key);\n      return Reflect.has(target, key);\n    },\n    getOwnPropertyDescriptor(target, key) {\n      recordUsage(HAS_OWN_KEY_PROPERTY, key);\n      return Reflect.getOwnPropertyDescriptor(target, key);\n    },\n    ownKeys(target) {\n      recordUsage(ALL_OWN_KEYS_PROPERTY);\n      return Reflect.ownKeys(target);\n    },\n  };\n  if (isTargetCopied) {\n    handler.set = handler.deleteProperty = () => false;\n  }\n  return [handler, state] as const;\n};\n\nconst getOriginalObject = <T extends object>(obj: T) => (\n  // unwrap proxy\n  (obj as { [GET_ORIGINAL_SYMBOL]?: typeof obj })[GET_ORIGINAL_SYMBOL]\n  // otherwise\n  || obj\n);\n\n/**\n * Create a proxy.\n *\n * This function will create a proxy at top level and proxy nested objects as you access them,\n * in order to keep track of which properties were accessed via get/has proxy handlers:\n *\n * NOTE: Printing of WeakMap is hard to inspect and not very readable\n * for this purpose you can use the `affectedToPathList` helper.\n *\n * @param {object} obj - Object that will be wrapped on the proxy.\n * @param {WeakMap<object, unknown>} affected -\n * WeakMap that will hold the tracking of which properties in the proxied object were accessed.\n * @param {WeakMap<object, unknown>} [proxyCache] -\n * WeakMap that will help keep referential identity for proxies.\n * @returns {Proxy<object>} - Object wrapped in a proxy.\n *\n * @example\n * import { createProxy } from 'proxy-compare';\n *\n * const original = { a: \"1\", c: \"2\", d: { e: \"3\" } };\n * const affected = new WeakMap();\n * const proxy = createProxy(original, affected);\n *\n * proxy.a // Will mark as used and track its value.\n * // This will update the affected WeakMap with original as key\n * // and a Set with \"a\"\n *\n * proxy.d // Will mark \"d\" as accessed to track and proxy itself ({ e: \"3\" }).\n * // This will update the affected WeakMap with original as key\n * // and a Set with \"d\"\n */\nexport const createProxy = <T>(\n  obj: T,\n  affected: WeakMap<object, unknown>,\n  proxyCache?: WeakMap<object, unknown>,\n  targetCache?: WeakMap<object, unknown>,\n): T => {\n  if (!isObjectToTrack(obj)) return obj;\n  let targetAndCopied = (\n    targetCache && (targetCache as TargetCache<typeof obj>).get(obj)\n  );\n  if (!targetAndCopied) {\n    const target = getOriginalObject(obj);\n    if (needsToCopyTargetObject(target)) {\n      targetAndCopied = [target, copyTargetObject(target)];\n    } else {\n      targetAndCopied = [target];\n    }\n    targetCache?.set(obj, targetAndCopied);\n  }\n  const [target, copiedTarget] = targetAndCopied;\n  let handlerAndState = (\n    proxyCache && (proxyCache as ProxyCache<typeof target>).get(target)\n  );\n  if (\n    !handlerAndState\n    || handlerAndState[1][IS_TARGET_COPIED_PROPERTY] !== !!copiedTarget\n  ) {\n    handlerAndState = createProxyHandler<typeof target>(target, !!copiedTarget);\n    handlerAndState[1][PROXY_PROPERTY] = newProxy(\n      copiedTarget || target,\n      handlerAndState[0],\n    );\n    if (proxyCache) {\n      proxyCache.set(target, handlerAndState);\n    }\n  }\n  handlerAndState[1][AFFECTED_PROPERTY] = affected as Affected;\n  handlerAndState[1][PROXY_CACHE_PROPERTY] = proxyCache as ProxyCache<object> | undefined;\n  handlerAndState[1][TARGET_CACHE_PROPERTY] = targetCache as TargetCache<object> | undefined;\n  return handlerAndState[1][PROXY_PROPERTY] as typeof target;\n};\n\nconst isAllOwnKeysChanged = (prevObj: object, nextObj: object) => {\n  const prevKeys = Reflect.ownKeys(prevObj);\n  const nextKeys = Reflect.ownKeys(nextObj);\n  return prevKeys.length !== nextKeys.length\n    || prevKeys.some((k, i) => k !== nextKeys[i]);\n};\n\ntype ChangedCache = WeakMap<object, {\n  [NEXT_OBJECT_PROPERTY]: object;\n  [CHANGED_PROPERTY]: boolean;\n}>;\n\n/**\n * Compare changes on objects.\n *\n * This will compare the affected properties on tracked objects inside the proxy\n * to check if there were any changes made to it,\n * by default if no property was accessed on the proxy it will attempt to do a\n * reference equality check for the objects provided (Object.is(a, b)). If you access a property\n * on the proxy, then isChanged will only compare the affected properties.\n *\n * @param {object} prevObj - The previous object to compare.\n * @param {object} nextObj - Object to compare with the previous one.\n * @param {WeakMap<object, unknown>} affected -\n * WeakMap that holds the tracking of which properties in the proxied object were accessed.\n * @param {WeakMap<object, unknown>} [cache] -\n * WeakMap that holds a cache of the comparisons for better performance with repetitive comparisons,\n * and to avoid infinite loop with circular structures.\n * @returns {boolean} - Boolean indicating if the affected property on the object has changed.\n *\n * @example\n * import { createProxy, isChanged } from 'proxy-compare';\n *\n * const obj = { a: \"1\", c: \"2\", d: { e: \"3\" } };\n * const affected = new WeakMap();\n *\n * const proxy = createProxy(obj, affected);\n *\n * proxy.a\n *\n * isChanged(obj, { a: \"1\" }, affected) // false\n *\n * proxy.a = \"2\"\n *\n * isChanged(obj, { a: \"1\" }, affected) // true\n */\n\nexport const isChanged = (\n  prevObj: unknown,\n  nextObj: unknown,\n  affected: WeakMap<object, unknown>,\n  cache?: WeakMap<object, unknown>,\n): boolean => {\n  if (Object.is(prevObj, nextObj)) {\n    return false;\n  }\n  if (!isObject(prevObj) || !isObject(nextObj)) return true;\n  const used = (affected as Affected).get(getOriginalObject(prevObj));\n  if (!used) return true;\n  if (cache) {\n    const hit = (cache as ChangedCache).get(prevObj);\n    if (hit && hit[NEXT_OBJECT_PROPERTY] === nextObj) {\n      return hit[CHANGED_PROPERTY];\n    }\n    // for object with cycles\n    (cache as ChangedCache).set(prevObj, {\n      [NEXT_OBJECT_PROPERTY]: nextObj,\n      [CHANGED_PROPERTY]: false,\n    });\n  }\n  let changed: boolean | null = null;\n  try {\n    for (const key of used[HAS_KEY_PROPERTY] || []) {\n      changed = Reflect.has(prevObj, key) !== Reflect.has(nextObj, key);\n      if (changed) return changed;\n    }\n    if (used[ALL_OWN_KEYS_PROPERTY] === true) {\n      changed = isAllOwnKeysChanged(prevObj, nextObj);\n      if (changed) return changed;\n    } else {\n      for (const key of used[HAS_OWN_KEY_PROPERTY] || []) {\n        const hasPrev = !!Reflect.getOwnPropertyDescriptor(prevObj, key);\n        const hasNext = !!Reflect.getOwnPropertyDescriptor(nextObj, key);\n        changed = hasPrev !== hasNext;\n        if (changed) return changed;\n      }\n    }\n    for (const key of used[KEYS_PROPERTY] || []) {\n      changed = isChanged(\n        (prevObj as any)[key],\n        (nextObj as any)[key],\n        affected,\n        cache,\n      );\n      if (changed) return changed;\n    }\n    if (changed === null) changed = true;\n    return changed;\n  } finally {\n    if (cache) {\n      cache.set(prevObj, {\n        [NEXT_OBJECT_PROPERTY]: nextObj,\n        [CHANGED_PROPERTY]: changed,\n      });\n    }\n  }\n};\n\n// explicitly track object with memo\nexport const trackMemo = (obj: unknown) => {\n  if (isObjectToTrack(obj)) {\n    return TRACK_MEMO_SYMBOL in obj;\n  }\n  return false;\n};\n\n/**\n * Unwrap proxy to get the original object.\n *\n * Used to retrieve the original object used to create the proxy instance with `createProxy`.\n *\n * @param {Proxy<object>} obj -  The proxy wrapper of the originial object.\n * @returns {object | null} - Return either the unwrapped object if exists.\n *\n * @example\n * import { createProxy, getUntracked } from 'proxy-compare';\n *\n * const original = { a: \"1\", c: \"2\", d: { e: \"3\" } };\n * const affected = new WeakMap();\n *\n * const proxy = createProxy(original, affected);\n * const originalFromProxy = getUntracked(proxy)\n *\n * Object.is(original, originalFromProxy) // true\n * isChanged(original, originalFromProxy, affected) // false\n */\nexport const getUntracked = <T>(obj: T): T | null => {\n  if (isObjectToTrack(obj)) {\n    return (obj as { [GET_ORIGINAL_SYMBOL]?: T })[GET_ORIGINAL_SYMBOL] || null;\n  }\n  return null;\n};\n\n/**\n * Mark object to be tracked.\n *\n * This function marks an object that will be passed into `createProxy`\n * as marked to track or not. By default only Array and Object are marked to track,\n * so this is useful for example to mark a class instance to track or to mark a object\n * to be untracked when creating your proxy.\n *\n * @param obj - Object to mark as tracked or not.\n * @param mark - Boolean indicating whether you want to track this object or not.\n * @returns - No return.\n *\n * @example\n * import { createProxy, markToTrack, isChanged } from 'proxy-compare';\n *\n * const nested = { e: \"3\" }\n *\n * markToTrack(nested, false)\n *\n * const original = { a: \"1\", c: \"2\", d: nested };\n * const affected = new WeakMap();\n *\n * const proxy = createProxy(original, affected);\n *\n * proxy.d.e\n *\n * isChanged(original, { d: { e: \"3\" } }, affected) // true\n */\nexport const markToTrack = (obj: object, mark = true) => {\n  objectsToTrack.set(obj, mark);\n};\n\n/**\n * Convert `affected` to path list\n *\n * `affected` is a weak map which is not printable.\n * This function is can convert it to printable path list.\n * It's for debugging purpose.\n *\n * @param obj - An object that is used with `createProxy`.\n * @param affected - A weak map that is used with `createProxy`.\n * @param onlyWithValues - An optional boolean to exclude object getters.\n * @returns - An array of paths.\n */\nexport const affectedToPathList = (\n  obj: unknown,\n  affected: WeakMap<object, unknown>,\n  onlyWithValues?: boolean,\n) => {\n  const list: (string | symbol)[][] = [];\n  const seen = new WeakSet();\n  const walk = (x: unknown, path?: (string | symbol)[]) => {\n    if (seen.has(x as object)) {\n      // for object with cycles\n      return;\n    }\n    if (isObject(x)) {\n      seen.add(x);\n    }\n    const used = isObject(x) && (affected as Affected).get(getOriginalObject(x));\n    if (used) {\n      used[HAS_KEY_PROPERTY]?.forEach((key) => {\n        const segment = `:has(${String(key)})`;\n        list.push(path ? [...path, segment] : [segment]);\n      });\n      if (used[ALL_OWN_KEYS_PROPERTY] === true) {\n        const segment = ':ownKeys';\n        list.push(path ? [...path, segment] : [segment]);\n      } else {\n        used[HAS_OWN_KEY_PROPERTY]?.forEach((key) => {\n          const segment = `:hasOwn(${String(key)})`;\n          list.push(path ? [...path, segment] : [segment]);\n        });\n      }\n      used[KEYS_PROPERTY]?.forEach((key) => {\n        if (!onlyWithValues || 'value' in (Object.getOwnPropertyDescriptor(x, key) || {})) {\n          walk((x as any)[key], path ? [...path, key] : [key]);\n        }\n      });\n    } else if (path) {\n      list.push(path);\n    }\n  };\n  walk(obj);\n  return list;\n};\n\n/**\n * replace newProxy function.\n *\n * This can be used if you want to use proxy-polyfill.\n * Note that proxy-polyfill can't polyfill everything.\n * Use it at your own risk.\n */\nexport const replaceNewProxy = (fn: typeof newProxy) => {\n  newProxy = fn;\n};\n"], "names": ["TRACK_MEMO_SYMBOL", "Symbol", "GET_ORIGINAL_SYMBOL", "AFFECTED_PROPERTY", "ALL_OWN_KEYS_PROPERTY", "newProxy", "target", "handler", "Proxy", "getProto", "Object", "getPrototypeOf", "objectsToTrack", "WeakMap", "isObjectToTrack", "obj", "has", "get", "prototype", "Array", "isObject", "x", "copyTargetObject", "isArray", "from", "descriptors", "getOwnPropertyDescriptors", "values", "for<PERSON>ach", "desc", "configurable", "create", "getOriginalObject", "createProxy", "affected", "proxyCache", "targetCache", "targetAndCopied", "some", "descriptor", "writable", "needsToCopyTargetObject", "set", "<PERSON><PERSON>arget", "handlerAndState", "createProxyHandler", "origObj", "isTargetCopied", "state", "f", "trackObject", "recordUsage", "type", "key", "used", "Set", "add", "Reflect", "delete", "getOwnPropertyDescriptor", "ownKeys", "deleteProperty", "isChanged", "prevObj", "nextObj", "cache", "is", "hit", "n", "g", "changed", "isAllOwnKeysChanged", "prevKeys", "nextKeys", "length", "k", "i", "trackMemo", "getUntracked", "markToTrack", "mark", "affectedToPathList", "only<PERSON><PERSON><PERSON><PERSON><PERSON>", "list", "seen", "WeakSet", "walk", "path", "_used$HAS_KEY_PROPERT", "_used$KEYS_PROPERTY", "segment", "String", "push", "_used$HAS_OWN_KEY_PRO", "replaceNewProxy", "fn"], "mappings": "AACA,MAAMA,EAAoBC,SACpBC,EAAsBD,SAGtBE,EAAoB,IAQpBC,EAAwB,IAK9B,IAAIC,EAAWA,CACbC,EACAC,IACG,IAAIC,MAAMF,EAAQC,GAGvB,MAAME,EAAWC,OAAOC,eAElBC,EAAiB,IAAIC,QAGrBC,EAAsBC,GAC1BA,IAAQH,EAAeI,IAAID,GACvBH,EAAeK,IAAIF,GAClBN,EAASM,KAASL,OAAOQ,WAAaT,EAASM,KAASI,MAAMD,WAK/DE,EAAYC,GACH,iBAANA,GAAwB,OAANA,EAerBC,EAAsCP,IAC1C,GAAII,MAAMI,QAAQR,GAEhB,OAAOI,MAAMK,KAAKT,GAIpB,MAAMU,EAAcf,OAAOgB,0BAA0BX,GAErD,OADAL,OAAOiB,OAAOF,GAAaG,QAASC,IAAWA,EAAKC,cAAe,CAAA,GAC5DpB,OAAOqB,OAAOtB,EAASM,GAAMU,EAAW,EAoG3CO,EAAuCjB,GAE1CA,EAA+Cb,IAE7Ca,EAkCQkB,EAAcA,CACzBlB,EACAmB,EACAC,EACAC,KAEA,IAAKtB,EAAgBC,GAAM,OAAOA,EAClC,IAAIsB,EACFD,GAAgBA,EAAwCnB,IAAIF,GAE9D,IAAKsB,EAAiB,CACpB,MAAM/B,EAAS0B,EAAkBjB,GAE/BsB,EAvK2BtB,IAC/BL,OAAOiB,OAAOjB,OAAOgB,0BAA0BX,IAAMuB,KAClDC,IAAgBA,EAAWT,eAAiBS,EAAWC,UAoKpDC,CAAwBnC,GACR,CAACA,EAAQgB,EAAiBhB,IAE1B,CAACA,SAErB8B,GAAAA,EAAaM,IAAI3B,EAAKsB,EACvB,CACD,MAAO/B,EAAQqC,GAAgBN,EAC/B,IAAIO,EACFT,GAAeA,EAAyClB,IAAIX,GAkB9D,OAfGsC,GACEA,EAAgB,GAA4B,MAAQD,IAEvDC,EAxIuBC,EAAmBC,EAAYC,KACxD,MAAMC,EAA8B,CAClCC,EAA6BF,GAE/B,IAAIG,GAAc,EAClB,MAAMC,EAAcA,CAClBC,EAKAC,KAEA,IAAKH,EAAa,CAChB,IAAII,EAAQN,EAAM7C,GAAgCc,IAAI6B,GAKtD,GAJKQ,IACHA,EAAO,GACNN,EAAM7C,GAAgCuC,IAAII,EAASQ,IAElDF,IAAShD,EACXkD,EAAKlD,IAAyB,MACzB,CACL,IAAIsC,EAAMY,EAAKF,GACVV,IACHA,EAAM,IAAIa,IACVD,EAAKF,GAAQV,GAEfA,EAAIc,IAAIH,EACT,CACF,GAMG9C,EAA2B,CAC/BU,IAAGA,CAACX,EAAQ+C,IACNA,IAAQnD,EACH4C,GAETK,EApHgB,IAoHWE,GACpBpB,EACLwB,QAAQxC,IAAIX,EAAQ+C,GACnBL,EAAM7C,GACP6C,EAA0B,EAC1BA,EAA2B,IAG/BhC,IAAGA,CAACV,EAAQ+C,IACNA,IAAQrD,GAjBdkD,GAAc,EACbF,EAAM7C,GAAgCuD,OAAOZ,IAkBnC,IAETK,EApImB,IAoIWE,GACvBI,QAAQzC,IAAIV,EAAQ+C,IAE7BM,yBAAwBA,CAACrD,EAAQ+C,KAC/BF,EAtIuB,IAsIWE,GAC3BI,QAAQE,yBAAyBrD,EAAQ+C,IAElDO,QAAQtD,IACN6C,EAAY/C,GACLqD,QAAQG,QAAQtD,KAM3B,OAHIyC,IACFxC,EAAQmC,IAAMnC,EAAQsD,eAAiB,KAAM,GAExC,CAACtD,EAASyC,EAAK,EAoEFH,CAAkCvC,IAAUqC,GAC9DC,EAAgB,GAAiB,EAAIvC,EACnCsC,GAAgBrC,EAChBsC,EAAgB,IAEdT,GACFA,EAAWO,IAAIpC,EAAQsC,IAG3BA,EAAgB,GAAGzC,GAAqB+B,EACxCU,EAAgB,GAAuB,EAAIT,EAC3CS,EAAgB,GAAwB,EAAIR,EACrCQ,EAAgB,GAAiB,GAkD7BkB,EAAYA,CACvBC,EACAC,EACA9B,EACA+B,KAEA,GAAIvD,OAAOwD,GAAGH,EAASC,GACrB,OAAO,EAET,IAAK5C,EAAS2C,KAAa3C,EAAS4C,GAAU,OAAO,EACrD,MAAMV,EAAQpB,EAAsBjB,IAAIe,EAAkB+B,IAC1D,IAAKT,EAAM,OAAO,EAClB,GAAIW,EAAO,CACT,MAAME,EAAOF,EAAuBhD,IAAI8C,GACxC,GAAII,GAAOA,EAAwB,IAAMH,EACvC,OAAOG,EAAoB,EAG5BF,EAAuBvB,IAAIqB,EAAS,CACnCK,EAAwBJ,EACxBK,GAAoB,GAEvB,CACD,IAAIC,EAA0B,KAC9B,IACE,IAAK,MAAMjB,KAAOC,EAAqB,GAAK,GAE1C,GADAgB,EAAUb,QAAQzC,IAAI+C,EAASV,KAASI,QAAQzC,IAAIgD,EAASX,GACzDiB,EAAS,OAAOA,EAEtB,IAAoC,IAAhChB,EAAKlD,IAEP,GADAkE,EA7EsBC,EAACR,EAAiBC,KAC5C,MAAMQ,EAAWf,QAAQG,QAAQG,GAC3BU,EAAWhB,QAAQG,QAAQI,GACjC,OAAOQ,EAASE,SAAWD,EAASC,QAC/BF,EAASlC,KAAK,CAACqC,EAAGC,IAAMD,IAAMF,EAASG,GAAE,EAyEhCL,CAAoBR,EAASC,GACnCM,EAAS,OAAOA,OAEpB,IAAK,MAAMjB,KAAOC,EAAyB,GAAK,GAI9C,GADAgB,IAFkBb,QAAQE,yBAAyBI,EAASV,MAC1CI,QAAQE,yBAAyBK,EAASX,GAExDiB,EAAS,OAAOA,EAGxB,IAAK,MAAMjB,KAAOC,EAAkB,GAAK,GAOvC,GANAgB,EAAUR,EACPC,EAAgBV,GAChBW,EAAgBX,GACjBnB,EACA+B,GAEEK,EAAS,OAAOA,EAGtB,OADgB,OAAZA,IAAkBA,GAAU,GACzBA,CACR,CAAA,QACKL,GACFA,EAAMvB,IAAIqB,EAAS,CACjBK,EAAwBJ,EACxBK,EAAoBC,GAGzB,GAIUO,EAAa9D,KACpBD,EAAgBC,IACXf,KAAqBe,EAyBnB+D,EAAmB/D,GAC1BD,EAAgBC,IACVA,EAAsCb,IAGlD,KA8Ba6E,EAAcA,CAAChE,EAAaiE,GAAO,KAC9CpE,EAAe8B,IAAI3B,EAAKiE,EAAI,EAejBC,EAAqBA,CAChClE,EACAmB,EACAgD,KAEA,MAAMC,EAA8B,GAC9BC,EAAO,IAAIC,QACXC,EAAOA,CAACjE,EAAYkE,KACxB,GAAIH,EAAKpE,IAAIK,GAEX,OAEED,EAASC,IACX+D,EAAK5B,IAAInC,GAEX,MAAMiC,EAAOlC,EAASC,IAAOa,EAAsBjB,IAAIe,EAAkBX,IACzE,GAAIiC,EAAM,CAAAkC,IAAAA,EAAAC,EAKR,GAJsB,OAAtBD,EAAAlC,EAAqB,IAArBkC,EAAwB5D,QAASyB,IAC/B,MAAMqC,EAAkB,QAAAC,OAAOtC,MAC/B8B,EAAKS,KAAKL,EAAO,IAAIA,EAAMG,GAAW,CAACA,GAAQ,IAEb,IAAhCpC,EAAKlD,GAAiC,CACxC,MAAMsF,EAAU,WAChBP,EAAKS,KAAKL,EAAO,IAAIA,EAAMG,GAAW,CAACA,GACxC,KAAM,CAAAG,IAAAA,EACqB,OAA1BA,EAAAvC,EAAyB,IAAzBuC,EAA4BjE,QAASyB,IACnC,MAAMqC,EAAqB,WAAAC,OAAOtC,MAClC8B,EAAKS,KAAKL,EAAO,IAAIA,EAAMG,GAAW,CAACA,KAE1C,QACDD,EAAAnC,EAAkB,IAAlBmC,EAAqB7D,QAASyB,IACvB6B,KAAkB,UAAYxE,OAAOiD,yBAAyBtC,EAAGgC,IAAQ,MAC5EiC,EAAMjE,EAAUgC,GAAMkC,EAAO,IAAIA,EAAMlC,GAAO,CAACA,GAChD,EAEJ,MAAUkC,GACTJ,EAAKS,KAAKL,EACX,EAGH,OADAD,EAAKvE,GACEoE,GAUIW,EAAmBC,IAC9B1F,EAAW0F,CACb"}