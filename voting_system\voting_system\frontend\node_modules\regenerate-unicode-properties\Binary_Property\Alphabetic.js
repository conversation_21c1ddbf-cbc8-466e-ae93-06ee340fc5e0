const set = require('regenerate')(0xAA, 0xB5, 0xBA, 0x2EC, 0x2EE, 0x345, 0x37F, 0x386, 0x38C, 0x559, 0x5BF, 0x5C7, 0x6FF, 0x7FA, 0x897, 0x9B2, 0x9CE, 0x9D7, 0x9FC, 0xA51, 0xA5E, 0xAD0, 0xB71, 0xB9C, 0xBD0, 0xBD7, 0xC5D, 0xD4E, 0xDBD, 0xDD6, 0xE4D, 0xE84, 0xEA5, 0xEC6, 0xECD, 0xF00, 0x1038, 0x10C7, 0x10CD, 0x1258, 0x12C0, 0x17D7, 0x17DC, 0x1AA7, 0x1CFA, 0x1F59, 0x1F5B, 0x1F5D, 0x1FBE, 0x2071, 0x207F, 0x2102, 0x2107, 0x2115, 0x2124, 0x2126, 0x2128, 0x214E, 0x2D27, 0x2D2D, 0x2D6F, 0x2E2F, 0xA7D3, 0xA8C5, 0xA8FB, 0xA9CF, 0xAAC0, 0xAAC2, 0xFB3E, 0x10808, 0x1083C, 0x10D69, 0x10EFC, 0x10F27, 0x110C2, 0x11176, 0x111DA, 0x111DC, 0x11237, 0x11288, 0x11350, 0x11357, 0x1138B, 0x1138E, 0x113C2, 0x113C5, 0x113D1, 0x113D3, 0x114C7, 0x11640, 0x11644, 0x116B8, 0x11909, 0x119E1, 0x11A9D, 0x11C40, 0x11D3A, 0x11D43, 0x11D98, 0x11FB0, 0x16FE3, 0x1B132, 0x1B155, 0x1BC9E, 0x1D4A2, 0x1D4BB, 0x1D546, 0x1E08F, 0x1E14E, 0x1E5F0, 0x1E947, 0x1E94B, 0x1EE24, 0x1EE27, 0x1EE39, 0x1EE3B, 0x1EE42, 0x1EE47, 0x1EE49, 0x1EE4B, 0x1EE54, 0x1EE57, 0x1EE59, 0x1EE5B, 0x1EE5D, 0x1EE5F, 0x1EE64, 0x1EE7E);
set.addRange(0x41, 0x5A).addRange(0x61, 0x7A).addRange(0xC0, 0xD6).addRange(0xD8, 0xF6).addRange(0xF8, 0x2C1).addRange(0x2C6, 0x2D1).addRange(0x2E0, 0x2E4).addRange(0x363, 0x374).addRange(0x376, 0x377).addRange(0x37A, 0x37D).addRange(0x388, 0x38A).addRange(0x38E, 0x3A1).addRange(0x3A3, 0x3F5).addRange(0x3F7, 0x481).addRange(0x48A, 0x52F).addRange(0x531, 0x556).addRange(0x560, 0x588).addRange(0x5B0, 0x5BD).addRange(0x5C1, 0x5C2).addRange(0x5C4, 0x5C5).addRange(0x5D0, 0x5EA).addRange(0x5EF, 0x5F2).addRange(0x610, 0x61A).addRange(0x620, 0x657).addRange(0x659, 0x65F).addRange(0x66E, 0x6D3).addRange(0x6D5, 0x6DC).addRange(0x6E1, 0x6E8).addRange(0x6ED, 0x6EF).addRange(0x6FA, 0x6FC).addRange(0x710, 0x73F).addRange(0x74D, 0x7B1).addRange(0x7CA, 0x7EA).addRange(0x7F4, 0x7F5).addRange(0x800, 0x817).addRange(0x81A, 0x82C).addRange(0x840, 0x858).addRange(0x860, 0x86A).addRange(0x870, 0x887).addRange(0x889, 0x88E).addRange(0x8A0, 0x8C9).addRange(0x8D4, 0x8DF).addRange(0x8E3, 0x8E9).addRange(0x8F0, 0x93B).addRange(0x93D, 0x94C).addRange(0x94E, 0x950).addRange(0x955, 0x963).addRange(0x971, 0x983).addRange(0x985, 0x98C).addRange(0x98F, 0x990).addRange(0x993, 0x9A8);
set.addRange(0x9AA, 0x9B0).addRange(0x9B6, 0x9B9).addRange(0x9BD, 0x9C4).addRange(0x9C7, 0x9C8).addRange(0x9CB, 0x9CC).addRange(0x9DC, 0x9DD).addRange(0x9DF, 0x9E3).addRange(0x9F0, 0x9F1).addRange(0xA01, 0xA03).addRange(0xA05, 0xA0A).addRange(0xA0F, 0xA10).addRange(0xA13, 0xA28).addRange(0xA2A, 0xA30).addRange(0xA32, 0xA33).addRange(0xA35, 0xA36).addRange(0xA38, 0xA39).addRange(0xA3E, 0xA42).addRange(0xA47, 0xA48).addRange(0xA4B, 0xA4C).addRange(0xA59, 0xA5C).addRange(0xA70, 0xA75).addRange(0xA81, 0xA83).addRange(0xA85, 0xA8D).addRange(0xA8F, 0xA91).addRange(0xA93, 0xAA8).addRange(0xAAA, 0xAB0).addRange(0xAB2, 0xAB3).addRange(0xAB5, 0xAB9).addRange(0xABD, 0xAC5).addRange(0xAC7, 0xAC9).addRange(0xACB, 0xACC).addRange(0xAE0, 0xAE3).addRange(0xAF9, 0xAFC).addRange(0xB01, 0xB03).addRange(0xB05, 0xB0C).addRange(0xB0F, 0xB10).addRange(0xB13, 0xB28).addRange(0xB2A, 0xB30).addRange(0xB32, 0xB33).addRange(0xB35, 0xB39).addRange(0xB3D, 0xB44).addRange(0xB47, 0xB48).addRange(0xB4B, 0xB4C).addRange(0xB56, 0xB57).addRange(0xB5C, 0xB5D).addRange(0xB5F, 0xB63).addRange(0xB82, 0xB83).addRange(0xB85, 0xB8A).addRange(0xB8E, 0xB90).addRange(0xB92, 0xB95).addRange(0xB99, 0xB9A);
set.addRange(0xB9E, 0xB9F).addRange(0xBA3, 0xBA4).addRange(0xBA8, 0xBAA).addRange(0xBAE, 0xBB9).addRange(0xBBE, 0xBC2).addRange(0xBC6, 0xBC8).addRange(0xBCA, 0xBCC).addRange(0xC00, 0xC0C).addRange(0xC0E, 0xC10).addRange(0xC12, 0xC28).addRange(0xC2A, 0xC39).addRange(0xC3D, 0xC44).addRange(0xC46, 0xC48).addRange(0xC4A, 0xC4C).addRange(0xC55, 0xC56).addRange(0xC58, 0xC5A).addRange(0xC60, 0xC63).addRange(0xC80, 0xC83).addRange(0xC85, 0xC8C).addRange(0xC8E, 0xC90).addRange(0xC92, 0xCA8).addRange(0xCAA, 0xCB3).addRange(0xCB5, 0xCB9).addRange(0xCBD, 0xCC4).addRange(0xCC6, 0xCC8).addRange(0xCCA, 0xCCC).addRange(0xCD5, 0xCD6).addRange(0xCDD, 0xCDE).addRange(0xCE0, 0xCE3).addRange(0xCF1, 0xCF3).addRange(0xD00, 0xD0C).addRange(0xD0E, 0xD10).addRange(0xD12, 0xD3A).addRange(0xD3D, 0xD44).addRange(0xD46, 0xD48).addRange(0xD4A, 0xD4C).addRange(0xD54, 0xD57).addRange(0xD5F, 0xD63).addRange(0xD7A, 0xD7F).addRange(0xD81, 0xD83).addRange(0xD85, 0xD96).addRange(0xD9A, 0xDB1).addRange(0xDB3, 0xDBB).addRange(0xDC0, 0xDC6).addRange(0xDCF, 0xDD4).addRange(0xDD8, 0xDDF).addRange(0xDF2, 0xDF3).addRange(0xE01, 0xE3A).addRange(0xE40, 0xE46).addRange(0xE81, 0xE82).addRange(0xE86, 0xE8A);
set.addRange(0xE8C, 0xEA3).addRange(0xEA7, 0xEB9).addRange(0xEBB, 0xEBD).addRange(0xEC0, 0xEC4).addRange(0xEDC, 0xEDF).addRange(0xF40, 0xF47).addRange(0xF49, 0xF6C).addRange(0xF71, 0xF83).addRange(0xF88, 0xF97).addRange(0xF99, 0xFBC).addRange(0x1000, 0x1036).addRange(0x103B, 0x103F).addRange(0x1050, 0x108F).addRange(0x109A, 0x109D).addRange(0x10A0, 0x10C5).addRange(0x10D0, 0x10FA).addRange(0x10FC, 0x1248).addRange(0x124A, 0x124D).addRange(0x1250, 0x1256).addRange(0x125A, 0x125D).addRange(0x1260, 0x1288).addRange(0x128A, 0x128D).addRange(0x1290, 0x12B0).addRange(0x12B2, 0x12B5).addRange(0x12B8, 0x12BE).addRange(0x12C2, 0x12C5).addRange(0x12C8, 0x12D6).addRange(0x12D8, 0x1310).addRange(0x1312, 0x1315).addRange(0x1318, 0x135A).addRange(0x1380, 0x138F).addRange(0x13A0, 0x13F5).addRange(0x13F8, 0x13FD).addRange(0x1401, 0x166C).addRange(0x166F, 0x167F).addRange(0x1681, 0x169A).addRange(0x16A0, 0x16EA).addRange(0x16EE, 0x16F8).addRange(0x1700, 0x1713).addRange(0x171F, 0x1733).addRange(0x1740, 0x1753).addRange(0x1760, 0x176C).addRange(0x176E, 0x1770).addRange(0x1772, 0x1773).addRange(0x1780, 0x17B3).addRange(0x17B6, 0x17C8).addRange(0x1820, 0x1878).addRange(0x1880, 0x18AA).addRange(0x18B0, 0x18F5).addRange(0x1900, 0x191E).addRange(0x1920, 0x192B);
set.addRange(0x1930, 0x1938).addRange(0x1950, 0x196D).addRange(0x1970, 0x1974).addRange(0x1980, 0x19AB).addRange(0x19B0, 0x19C9).addRange(0x1A00, 0x1A1B).addRange(0x1A20, 0x1A5E).addRange(0x1A61, 0x1A74).addRange(0x1ABF, 0x1AC0).addRange(0x1ACC, 0x1ACE).addRange(0x1B00, 0x1B33).addRange(0x1B35, 0x1B43).addRange(0x1B45, 0x1B4C).addRange(0x1B80, 0x1BA9).addRange(0x1BAC, 0x1BAF).addRange(0x1BBA, 0x1BE5).addRange(0x1BE7, 0x1BF1).addRange(0x1C00, 0x1C36).addRange(0x1C4D, 0x1C4F).addRange(0x1C5A, 0x1C7D).addRange(0x1C80, 0x1C8A).addRange(0x1C90, 0x1CBA).addRange(0x1CBD, 0x1CBF).addRange(0x1CE9, 0x1CEC).addRange(0x1CEE, 0x1CF3).addRange(0x1CF5, 0x1CF6).addRange(0x1D00, 0x1DBF).addRange(0x1DD3, 0x1DF4).addRange(0x1E00, 0x1F15).addRange(0x1F18, 0x1F1D).addRange(0x1F20, 0x1F45).addRange(0x1F48, 0x1F4D).addRange(0x1F50, 0x1F57).addRange(0x1F5F, 0x1F7D).addRange(0x1F80, 0x1FB4).addRange(0x1FB6, 0x1FBC).addRange(0x1FC2, 0x1FC4).addRange(0x1FC6, 0x1FCC).addRange(0x1FD0, 0x1FD3).addRange(0x1FD6, 0x1FDB).addRange(0x1FE0, 0x1FEC).addRange(0x1FF2, 0x1FF4).addRange(0x1FF6, 0x1FFC).addRange(0x2090, 0x209C).addRange(0x210A, 0x2113).addRange(0x2119, 0x211D).addRange(0x212A, 0x212D).addRange(0x212F, 0x2139).addRange(0x213C, 0x213F).addRange(0x2145, 0x2149).addRange(0x2160, 0x2188);
set.addRange(0x24B6, 0x24E9).addRange(0x2C00, 0x2CE4).addRange(0x2CEB, 0x2CEE).addRange(0x2CF2, 0x2CF3).addRange(0x2D00, 0x2D25).addRange(0x2D30, 0x2D67).addRange(0x2D80, 0x2D96).addRange(0x2DA0, 0x2DA6).addRange(0x2DA8, 0x2DAE).addRange(0x2DB0, 0x2DB6).addRange(0x2DB8, 0x2DBE).addRange(0x2DC0, 0x2DC6).addRange(0x2DC8, 0x2DCE).addRange(0x2DD0, 0x2DD6).addRange(0x2DD8, 0x2DDE).addRange(0x2DE0, 0x2DFF).addRange(0x3005, 0x3007).addRange(0x3021, 0x3029).addRange(0x3031, 0x3035).addRange(0x3038, 0x303C).addRange(0x3041, 0x3096).addRange(0x309D, 0x309F).addRange(0x30A1, 0x30FA).addRange(0x30FC, 0x30FF).addRange(0x3105, 0x312F).addRange(0x3131, 0x318E).addRange(0x31A0, 0x31BF).addRange(0x31F0, 0x31FF).addRange(0x3400, 0x4DBF).addRange(0x4E00, 0xA48C).addRange(0xA4D0, 0xA4FD).addRange(0xA500, 0xA60C).addRange(0xA610, 0xA61F).addRange(0xA62A, 0xA62B).addRange(0xA640, 0xA66E).addRange(0xA674, 0xA67B).addRange(0xA67F, 0xA6EF).addRange(0xA717, 0xA71F).addRange(0xA722, 0xA788).addRange(0xA78B, 0xA7CD).addRange(0xA7D0, 0xA7D1).addRange(0xA7D5, 0xA7DC).addRange(0xA7F2, 0xA805).addRange(0xA807, 0xA827).addRange(0xA840, 0xA873).addRange(0xA880, 0xA8C3).addRange(0xA8F2, 0xA8F7).addRange(0xA8FD, 0xA8FF).addRange(0xA90A, 0xA92A).addRange(0xA930, 0xA952).addRange(0xA960, 0xA97C);
set.addRange(0xA980, 0xA9B2).addRange(0xA9B4, 0xA9BF).addRange(0xA9E0, 0xA9EF).addRange(0xA9FA, 0xA9FE).addRange(0xAA00, 0xAA36).addRange(0xAA40, 0xAA4D).addRange(0xAA60, 0xAA76).addRange(0xAA7A, 0xAABE).addRange(0xAADB, 0xAADD).addRange(0xAAE0, 0xAAEF).addRange(0xAAF2, 0xAAF5).addRange(0xAB01, 0xAB06).addRange(0xAB09, 0xAB0E).addRange(0xAB11, 0xAB16).addRange(0xAB20, 0xAB26).addRange(0xAB28, 0xAB2E).addRange(0xAB30, 0xAB5A).addRange(0xAB5C, 0xAB69).addRange(0xAB70, 0xABEA).addRange(0xAC00, 0xD7A3).addRange(0xD7B0, 0xD7C6).addRange(0xD7CB, 0xD7FB).addRange(0xF900, 0xFA6D).addRange(0xFA70, 0xFAD9).addRange(0xFB00, 0xFB06).addRange(0xFB13, 0xFB17).addRange(0xFB1D, 0xFB28).addRange(0xFB2A, 0xFB36).addRange(0xFB38, 0xFB3C).addRange(0xFB40, 0xFB41).addRange(0xFB43, 0xFB44).addRange(0xFB46, 0xFBB1).addRange(0xFBD3, 0xFD3D).addRange(0xFD50, 0xFD8F).addRange(0xFD92, 0xFDC7).addRange(0xFDF0, 0xFDFB).addRange(0xFE70, 0xFE74).addRange(0xFE76, 0xFEFC).addRange(0xFF21, 0xFF3A).addRange(0xFF41, 0xFF5A).addRange(0xFF66, 0xFFBE).addRange(0xFFC2, 0xFFC7).addRange(0xFFCA, 0xFFCF).addRange(0xFFD2, 0xFFD7).addRange(0xFFDA, 0xFFDC).addRange(0x10000, 0x1000B).addRange(0x1000D, 0x10026).addRange(0x10028, 0x1003A).addRange(0x1003C, 0x1003D).addRange(0x1003F, 0x1004D).addRange(0x10050, 0x1005D);
set.addRange(0x10080, 0x100FA).addRange(0x10140, 0x10174).addRange(0x10280, 0x1029C).addRange(0x102A0, 0x102D0).addRange(0x10300, 0x1031F).addRange(0x1032D, 0x1034A).addRange(0x10350, 0x1037A).addRange(0x10380, 0x1039D).addRange(0x103A0, 0x103C3).addRange(0x103C8, 0x103CF).addRange(0x103D1, 0x103D5).addRange(0x10400, 0x1049D).addRange(0x104B0, 0x104D3).addRange(0x104D8, 0x104FB).addRange(0x10500, 0x10527).addRange(0x10530, 0x10563).addRange(0x10570, 0x1057A).addRange(0x1057C, 0x1058A).addRange(0x1058C, 0x10592).addRange(0x10594, 0x10595).addRange(0x10597, 0x105A1).addRange(0x105A3, 0x105B1).addRange(0x105B3, 0x105B9).addRange(0x105BB, 0x105BC).addRange(0x105C0, 0x105F3).addRange(0x10600, 0x10736).addRange(0x10740, 0x10755).addRange(0x10760, 0x10767).addRange(0x10780, 0x10785).addRange(0x10787, 0x107B0).addRange(0x107B2, 0x107BA).addRange(0x10800, 0x10805).addRange(0x1080A, 0x10835).addRange(0x10837, 0x10838).addRange(0x1083F, 0x10855).addRange(0x10860, 0x10876).addRange(0x10880, 0x1089E).addRange(0x108E0, 0x108F2).addRange(0x108F4, 0x108F5).addRange(0x10900, 0x10915).addRange(0x10920, 0x10939).addRange(0x10980, 0x109B7).addRange(0x109BE, 0x109BF).addRange(0x10A00, 0x10A03).addRange(0x10A05, 0x10A06).addRange(0x10A0C, 0x10A13).addRange(0x10A15, 0x10A17).addRange(0x10A19, 0x10A35).addRange(0x10A60, 0x10A7C).addRange(0x10A80, 0x10A9C).addRange(0x10AC0, 0x10AC7);
set.addRange(0x10AC9, 0x10AE4).addRange(0x10B00, 0x10B35).addRange(0x10B40, 0x10B55).addRange(0x10B60, 0x10B72).addRange(0x10B80, 0x10B91).addRange(0x10C00, 0x10C48).addRange(0x10C80, 0x10CB2).addRange(0x10CC0, 0x10CF2).addRange(0x10D00, 0x10D27).addRange(0x10D4A, 0x10D65).addRange(0x10D6F, 0x10D85).addRange(0x10E80, 0x10EA9).addRange(0x10EAB, 0x10EAC).addRange(0x10EB0, 0x10EB1).addRange(0x10EC2, 0x10EC4).addRange(0x10F00, 0x10F1C).addRange(0x10F30, 0x10F45).addRange(0x10F70, 0x10F81).addRange(0x10FB0, 0x10FC4).addRange(0x10FE0, 0x10FF6).addRange(0x11000, 0x11045).addRange(0x11071, 0x11075).addRange(0x11080, 0x110B8).addRange(0x110D0, 0x110E8).addRange(0x11100, 0x11132).addRange(0x11144, 0x11147).addRange(0x11150, 0x11172).addRange(0x11180, 0x111BF).addRange(0x111C1, 0x111C4).addRange(0x111CE, 0x111CF).addRange(0x11200, 0x11211).addRange(0x11213, 0x11234).addRange(0x1123E, 0x11241).addRange(0x11280, 0x11286).addRange(0x1128A, 0x1128D).addRange(0x1128F, 0x1129D).addRange(0x1129F, 0x112A8).addRange(0x112B0, 0x112E8).addRange(0x11300, 0x11303).addRange(0x11305, 0x1130C).addRange(0x1130F, 0x11310).addRange(0x11313, 0x11328).addRange(0x1132A, 0x11330).addRange(0x11332, 0x11333).addRange(0x11335, 0x11339).addRange(0x1133D, 0x11344).addRange(0x11347, 0x11348).addRange(0x1134B, 0x1134C).addRange(0x1135D, 0x11363).addRange(0x11380, 0x11389).addRange(0x11390, 0x113B5);
set.addRange(0x113B7, 0x113C0).addRange(0x113C7, 0x113CA).addRange(0x113CC, 0x113CD).addRange(0x11400, 0x11441).addRange(0x11443, 0x11445).addRange(0x11447, 0x1144A).addRange(0x1145F, 0x11461).addRange(0x11480, 0x114C1).addRange(0x114C4, 0x114C5).addRange(0x11580, 0x115B5).addRange(0x115B8, 0x115BE).addRange(0x115D8, 0x115DD).addRange(0x11600, 0x1163E).addRange(0x11680, 0x116B5).addRange(0x11700, 0x1171A).addRange(0x1171D, 0x1172A).addRange(0x11740, 0x11746).addRange(0x11800, 0x11838).addRange(0x118A0, 0x118DF).addRange(0x118FF, 0x11906).addRange(0x1190C, 0x11913).addRange(0x11915, 0x11916).addRange(0x11918, 0x11935).addRange(0x11937, 0x11938).addRange(0x1193B, 0x1193C).addRange(0x1193F, 0x11942).addRange(0x119A0, 0x119A7).addRange(0x119AA, 0x119D7).addRange(0x119DA, 0x119DF).addRange(0x119E3, 0x119E4).addRange(0x11A00, 0x11A32).addRange(0x11A35, 0x11A3E).addRange(0x11A50, 0x11A97).addRange(0x11AB0, 0x11AF8).addRange(0x11BC0, 0x11BE0).addRange(0x11C00, 0x11C08).addRange(0x11C0A, 0x11C36).addRange(0x11C38, 0x11C3E).addRange(0x11C72, 0x11C8F).addRange(0x11C92, 0x11CA7).addRange(0x11CA9, 0x11CB6).addRange(0x11D00, 0x11D06).addRange(0x11D08, 0x11D09).addRange(0x11D0B, 0x11D36).addRange(0x11D3C, 0x11D3D).addRange(0x11D3F, 0x11D41).addRange(0x11D46, 0x11D47).addRange(0x11D60, 0x11D65).addRange(0x11D67, 0x11D68).addRange(0x11D6A, 0x11D8E).addRange(0x11D90, 0x11D91);
set.addRange(0x11D93, 0x11D96).addRange(0x11EE0, 0x11EF6).addRange(0x11F00, 0x11F10).addRange(0x11F12, 0x11F3A).addRange(0x11F3E, 0x11F40).addRange(0x12000, 0x12399).addRange(0x12400, 0x1246E).addRange(0x12480, 0x12543).addRange(0x12F90, 0x12FF0).addRange(0x13000, 0x1342F).addRange(0x13441, 0x13446).addRange(0x13460, 0x143FA).addRange(0x14400, 0x14646).addRange(0x16100, 0x1612E).addRange(0x16800, 0x16A38).addRange(0x16A40, 0x16A5E).addRange(0x16A70, 0x16ABE).addRange(0x16AD0, 0x16AED).addRange(0x16B00, 0x16B2F).addRange(0x16B40, 0x16B43).addRange(0x16B63, 0x16B77).addRange(0x16B7D, 0x16B8F).addRange(0x16D40, 0x16D6C).addRange(0x16E40, 0x16E7F).addRange(0x16F00, 0x16F4A).addRange(0x16F4F, 0x16F87).addRange(0x16F8F, 0x16F9F).addRange(0x16FE0, 0x16FE1).addRange(0x16FF0, 0x16FF1).addRange(0x17000, 0x187F7).addRange(0x18800, 0x18CD5).addRange(0x18CFF, 0x18D08).addRange(0x1AFF0, 0x1AFF3).addRange(0x1AFF5, 0x1AFFB).addRange(0x1AFFD, 0x1AFFE).addRange(0x1B000, 0x1B122).addRange(0x1B150, 0x1B152).addRange(0x1B164, 0x1B167).addRange(0x1B170, 0x1B2FB).addRange(0x1BC00, 0x1BC6A).addRange(0x1BC70, 0x1BC7C).addRange(0x1BC80, 0x1BC88).addRange(0x1BC90, 0x1BC99).addRange(0x1D400, 0x1D454).addRange(0x1D456, 0x1D49C).addRange(0x1D49E, 0x1D49F).addRange(0x1D4A5, 0x1D4A6).addRange(0x1D4A9, 0x1D4AC).addRange(0x1D4AE, 0x1D4B9).addRange(0x1D4BD, 0x1D4C3).addRange(0x1D4C5, 0x1D505);
set.addRange(0x1D507, 0x1D50A).addRange(0x1D50D, 0x1D514).addRange(0x1D516, 0x1D51C).addRange(0x1D51E, 0x1D539).addRange(0x1D53B, 0x1D53E).addRange(0x1D540, 0x1D544).addRange(0x1D54A, 0x1D550).addRange(0x1D552, 0x1D6A5).addRange(0x1D6A8, 0x1D6C0).addRange(0x1D6C2, 0x1D6DA).addRange(0x1D6DC, 0x1D6FA).addRange(0x1D6FC, 0x1D714).addRange(0x1D716, 0x1D734).addRange(0x1D736, 0x1D74E).addRange(0x1D750, 0x1D76E).addRange(0x1D770, 0x1D788).addRange(0x1D78A, 0x1D7A8).addRange(0x1D7AA, 0x1D7C2).addRange(0x1D7C4, 0x1D7CB).addRange(0x1DF00, 0x1DF1E).addRange(0x1DF25, 0x1DF2A).addRange(0x1E000, 0x1E006).addRange(0x1E008, 0x1E018).addRange(0x1E01B, 0x1E021).addRange(0x1E023, 0x1E024).addRange(0x1E026, 0x1E02A).addRange(0x1E030, 0x1E06D).addRange(0x1E100, 0x1E12C).addRange(0x1E137, 0x1E13D).addRange(0x1E290, 0x1E2AD).addRange(0x1E2C0, 0x1E2EB).addRange(0x1E4D0, 0x1E4EB).addRange(0x1E5D0, 0x1E5ED).addRange(0x1E7E0, 0x1E7E6).addRange(0x1E7E8, 0x1E7EB).addRange(0x1E7ED, 0x1E7EE).addRange(0x1E7F0, 0x1E7FE).addRange(0x1E800, 0x1E8C4).addRange(0x1E900, 0x1E943).addRange(0x1EE00, 0x1EE03).addRange(0x1EE05, 0x1EE1F).addRange(0x1EE21, 0x1EE22).addRange(0x1EE29, 0x1EE32).addRange(0x1EE34, 0x1EE37).addRange(0x1EE4D, 0x1EE4F).addRange(0x1EE51, 0x1EE52).addRange(0x1EE61, 0x1EE62).addRange(0x1EE67, 0x1EE6A).addRange(0x1EE6C, 0x1EE72).addRange(0x1EE74, 0x1EE77).addRange(0x1EE79, 0x1EE7C);
set.addRange(0x1EE80, 0x1EE89).addRange(0x1EE8B, 0x1EE9B).addRange(0x1EEA1, 0x1EEA3).addRange(0x1EEA5, 0x1EEA9).addRange(0x1EEAB, 0x1EEBB).addRange(0x1F130, 0x1F149).addRange(0x1F150, 0x1F169).addRange(0x1F170, 0x1F189).addRange(0x20000, 0x2A6DF).addRange(0x2A700, 0x2B739).addRange(0x2B740, 0x2B81D).addRange(0x2B820, 0x2CEA1).addRange(0x2CEB0, 0x2EBE0).addRange(0x2EBF0, 0x2EE5D).addRange(0x2F800, 0x2FA1D).addRange(0x30000, 0x3134A).addRange(0x31350, 0x323AF);
exports.characters = set;
