{"version": 3, "file": "until.js", "sourceRoot": "", "sources": ["../../src/directives/until.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAO,QAAQ,EAAC,MAAM,gBAAgB,CAAC;AAC9C,OAAO,EAAC,WAAW,EAAC,MAAM,yBAAyB,CAAC;AACpD,OAAO,EAAC,SAAS,EAAE,cAAc,EAAC,MAAM,uBAAuB,CAAC;AAChE,OAAO,EAAC,MAAM,EAAE,aAAa,EAAC,MAAM,4BAA4B,CAAC;AAEjE,MAAM,SAAS,GAAG,CAAC,CAAU,EAAE,EAAE;IAC/B,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,OAAQ,CAAsB,CAAC,IAAI,KAAK,UAAU,CAAC;AAC/E,CAAC,CAAC;AACF,mCAAmC;AACnC,MAAM,SAAS,GAAG,UAAU,CAAC;AAE7B,MAAM,OAAO,cAAe,SAAQ,cAAc;IAAlD;;QACU,wBAAmB,GAAW,SAAS,CAAC;QACxC,aAAQ,GAAc,EAAE,CAAC;QACzB,eAAU,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,CAAC;QACrC,aAAQ,GAAG,IAAI,MAAM,EAAE,CAAC;IAsFlC,CAAC;IApFC,MAAM,CAAC,GAAG,IAAoB;;QAC5B,OAAO,MAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,mCAAI,QAAQ,CAAC;IACrD,CAAC;IAEQ,MAAM,CAAC,KAAW,EAAE,IAAoB;QAC/C,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;QACrC,IAAI,cAAc,GAAG,cAAc,CAAC,MAAM,CAAC;QAC3C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QAErB,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC;QACjC,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC;QAE7B,0EAA0E;QAC1E,6CAA6C;QAC7C,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,IAAI,CAAC,YAAY,EAAE,CAAC;SACrB;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACpC,2DAA2D;YAC3D,IAAI,CAAC,GAAG,IAAI,CAAC,mBAAmB,EAAE;gBAChC,MAAM;aACP;YAED,MAAM,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YAEtB,wCAAwC;YACxC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;gBACrB,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;gBAC7B,sEAAsE;gBACtE,iDAAiD;gBACjD,OAAO,KAAK,CAAC;aACd;YAED,uDAAuD;YACvD,IAAI,CAAC,GAAG,cAAc,IAAI,KAAK,KAAK,cAAc,CAAC,CAAC,CAAC,EAAE;gBACrD,SAAS;aACV;YAED,wEAAwE;YACxE,2CAA2C;YAC3C,IAAI,CAAC,mBAAmB,GAAG,SAAS,CAAC;YACrC,cAAc,GAAG,CAAC,CAAC;YAEnB,sEAAsE;YACtE,wEAAwE;YACxE,0EAA0E;YAC1E,4BAA4B;YAC5B,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,MAAe,EAAE,EAAE;gBACpD,8DAA8D;gBAC9D,iEAAiE;gBACjE,gEAAgE;gBAChE,OAAO,MAAM,CAAC,GAAG,EAAE,EAAE;oBACnB,MAAM,MAAM,CAAC,GAAG,EAAE,CAAC;iBACpB;gBACD,sEAAsE;gBACtE,qEAAqE;gBACrE,2BAA2B;gBAC3B,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC;gBAC/B,IAAI,KAAK,KAAK,SAAS,EAAE;oBACvB,MAAM,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;oBAC5C,uEAAuE;oBACvE,mEAAmE;oBACnE,qDAAqD;oBACrD,IAAI,KAAK,GAAG,CAAC,CAAC,IAAI,KAAK,GAAG,KAAK,CAAC,mBAAmB,EAAE;wBACnD,KAAK,CAAC,mBAAmB,GAAG,KAAK,CAAC;wBAClC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;qBACxB;iBACF;YACH,CAAC,CAAC,CAAC;SACJ;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEQ,YAAY;QACnB,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;QAC7B,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;IACxB,CAAC;IAEQ,WAAW;QAClB,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAChC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;IACzB,CAAC;CACF;AAED;;;;;;;;;;;;;;;;;;;;GAoBG;AACH,MAAM,CAAC,MAAM,KAAK,GAAG,SAAS,CAAC,cAAc,CAAC,CAAC;AAE/C;;;GAGG;AACH,gCAAgC", "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {Part, noChange} from '../lit-html.js';\nimport {isPrimitive} from '../directive-helpers.js';\nimport {directive, AsyncDirective} from '../async-directive.js';\nimport {Pauser, PseudoWeakRef} from './private-async-helpers.js';\n\nconst isPromise = (x: unknown) => {\n  return !isPrimitive(x) && typeof (x as {then?: unknown}).then === 'function';\n};\n// Effectively infinity, but a SMI.\nconst _infinity = 0x3fffffff;\n\nexport class UntilDirective extends AsyncDirective {\n  private __lastRenderedIndex: number = _infinity;\n  private __values: unknown[] = [];\n  private __weakThis = new PseudoWeakRef(this);\n  private __pauser = new Pauser();\n\n  render(...args: Array<unknown>) {\n    return args.find((x) => !isPromise(x)) ?? noChange;\n  }\n\n  override update(_part: Part, args: Array<unknown>) {\n    const previousValues = this.__values;\n    let previousLength = previousValues.length;\n    this.__values = args;\n\n    const weakThis = this.__weakThis;\n    const pauser = this.__pauser;\n\n    // If our initial render occurs while disconnected, ensure that the pauser\n    // and weakThis are in the disconnected state\n    if (!this.isConnected) {\n      this.disconnected();\n    }\n\n    for (let i = 0; i < args.length; i++) {\n      // If we've rendered a higher-priority value already, stop.\n      if (i > this.__lastRenderedIndex) {\n        break;\n      }\n\n      const value = args[i];\n\n      // Render non-Promise values immediately\n      if (!isPromise(value)) {\n        this.__lastRenderedIndex = i;\n        // Since a lower-priority value will never overwrite a higher-priority\n        // synchronous value, we can stop processing now.\n        return value;\n      }\n\n      // If this is a Promise we've already handled, skip it.\n      if (i < previousLength && value === previousValues[i]) {\n        continue;\n      }\n\n      // We have a Promise that we haven't seen before, so priorities may have\n      // changed. Forget what we rendered before.\n      this.__lastRenderedIndex = _infinity;\n      previousLength = 0;\n\n      // Note, the callback avoids closing over `this` so that the directive\n      // can be gc'ed before the promise resolves; instead `this` is retrieved\n      // from `weakThis`, which can break the hard reference in the closure when\n      // the directive disconnects\n      Promise.resolve(value).then(async (result: unknown) => {\n        // If we're disconnected, wait until we're (maybe) reconnected\n        // The while loop here handles the case that the connection state\n        // thrashes, causing the pauser to resume and then get re-paused\n        while (pauser.get()) {\n          await pauser.get();\n        }\n        // If the callback gets here and there is no `this`, it means that the\n        // directive has been disconnected and garbage collected and we don't\n        // need to do anything else\n        const _this = weakThis.deref();\n        if (_this !== undefined) {\n          const index = _this.__values.indexOf(value);\n          // If state.values doesn't contain the value, we've re-rendered without\n          // the value, so don't render it. Then, only render if the value is\n          // higher-priority than what's already been rendered.\n          if (index > -1 && index < _this.__lastRenderedIndex) {\n            _this.__lastRenderedIndex = index;\n            _this.setValue(result);\n          }\n        }\n      });\n    }\n\n    return noChange;\n  }\n\n  override disconnected() {\n    this.__weakThis.disconnect();\n    this.__pauser.pause();\n  }\n\n  override reconnected() {\n    this.__weakThis.reconnect(this);\n    this.__pauser.resume();\n  }\n}\n\n/**\n * Renders one of a series of values, including Promises, to a Part.\n *\n * Values are rendered in priority order, with the first argument having the\n * highest priority and the last argument having the lowest priority. If a\n * value is a Promise, low-priority values will be rendered until it resolves.\n *\n * The priority of values can be used to create placeholder content for async\n * data. For example, a Promise with pending content can be the first,\n * highest-priority, argument, and a non_promise loading indicator template can\n * be used as the second, lower-priority, argument. The loading indicator will\n * render immediately, and the primary content will render when the Promise\n * resolves.\n *\n * Example:\n *\n * ```js\n * const content = fetch('./content.txt').then(r => r.text());\n * html`${until(content, html`<span>Loading...</span>`)}`\n * ```\n */\nexport const until = directive(UntilDirective);\n\n/**\n * The type of the class that powers this directive. Necessary for naming the\n * directive's return type.\n */\n// export type {UntilDirective};\n"]}