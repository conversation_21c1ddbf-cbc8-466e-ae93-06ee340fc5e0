{"/Users/<USER>/Documents/DEV/EthereumJS/ethereumjs-tx/fake.js": {"path": "/Users/<USER>/Documents/DEV/EthereumJS/ethereumjs-tx/fake.js", "s": {"1": 1, "2": 1, "3": 1, "4": 2, "5": 2, "6": 2, "7": 2, "8": 2, "9": 2, "10": 4, "11": 2, "12": 2, "13": 4}, "b": {"1": [2, 0], "2": [2, 2], "3": [2, 2], "4": [4, 2]}, "f": {"1": 2, "2": 2, "3": 4}, "fnMap": {"1": {"name": "(anonymous_1)", "line": 35, "loc": {"start": {"line": 35, "column": 14}, "end": {"line": 35, "column": 21}}}, "2": {"name": "(anonymous_2)", "line": 47, "loc": {"start": {"line": 47, "column": 11}, "end": {"line": 47, "column": 26}}}, "3": {"name": "(anonymous_3)", "line": 62, "loc": {"start": {"line": 62, "column": 7}, "end": {"line": 62, "column": 26}}}}, "statementMap": {"1": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 41}}, "2": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 42}}, "3": {"start": {"line": 34, "column": 0}, "end": {"line": 71, "column": 1}}, "4": {"start": {"line": 36, "column": 4}, "end": {"line": 36, "column": 15}}, "5": {"start": {"line": 38, "column": 4}, "end": {"line": 38, "column": 19}}, "6": {"start": {"line": 43, "column": 4}, "end": {"line": 50, "column": 6}}, "7": {"start": {"line": 48, "column": 8}, "end": {"line": 48, "column": 42}}, "8": {"start": {"line": 52, "column": 4}, "end": {"line": 54, "column": 5}}, "9": {"start": {"line": 53, "column": 6}, "end": {"line": 53, "column": 27}}, "10": {"start": {"line": 63, "column": 4}, "end": {"line": 67, "column": 5}}, "11": {"start": {"line": 65, "column": 6}, "end": {"line": 65, "column": 72}}, "12": {"start": {"line": 66, "column": 6}, "end": {"line": 66, "column": 24}}, "13": {"start": {"line": 69, "column": 4}, "end": {"line": 69, "column": 39}}}, "branchMap": {"1": {"line": 52, "type": "if", "locations": [{"start": {"line": 52, "column": 4}, "end": {"line": 52, "column": 4}}, {"start": {"line": 52, "column": 4}, "end": {"line": 52, "column": 4}}]}, "2": {"line": 52, "type": "binary-expr", "locations": [{"start": {"line": 52, "column": 8}, "end": {"line": 52, "column": 12}}, {"start": {"line": 52, "column": 16}, "end": {"line": 52, "column": 25}}]}, "3": {"line": 63, "type": "if", "locations": [{"start": {"line": 63, "column": 4}, "end": {"line": 63, "column": 4}}, {"start": {"line": 63, "column": 4}, "end": {"line": 63, "column": 4}}]}, "4": {"line": 63, "type": "binary-expr", "locations": [{"start": {"line": 63, "column": 8}, "end": {"line": 63, "column": 24}}, {"start": {"line": 63, "column": 28}, "end": {"line": 63, "column": 38}}]}}}, "/Users/<USER>/Documents/DEV/EthereumJS/ethereumjs-tx/index.js": {"path": "/Users/<USER>/Documents/DEV/EthereumJS/ethereumjs-tx/index.js", "s": {"1": 1, "2": 1, "3": 1, "4": 1, "5": 283, "6": 283, "7": 283, "8": 155, "9": 155, "10": 147, "11": 147, "12": 127, "13": 147, "14": 147, "15": 16, "16": 105, "17": 2, "18": 105, "19": 105, "20": 6, "21": 99, "22": 25, "23": 25, "24": 25, "25": 25, "26": 25, "27": 25, "28": 74, "29": 105, "30": 3, "31": 70, "32": 6, "33": 64, "34": 64, "35": 64, "36": 67, "37": 11, "38": 0, "39": 67, "40": 80, "41": 80, "42": 0, "43": 80, "44": 80, "45": 80, "46": 12, "47": 80, "48": 9, "49": 71, "50": 6, "51": 6, "52": 6, "53": 1, "54": 6, "55": 68, "56": 68, "57": 68, "58": 20975138, "59": 68, "60": 66, "61": 66, "62": 1, "63": 66, "64": 1, "65": 59, "66": 59, "67": 6, "68": 59, "69": 6, "70": 59, "71": 53, "72": 6, "73": 1}, "b": {"1": [283, 4], "2": [127, 20], "3": [147, 127, 125], "4": [2, 103], "5": [6, 99], "6": [25, 74], "7": [6, 64], "8": [11, 56], "9": [67, 56], "10": [0, 11], "11": [0, 80], "12": [80, 30], "13": [12, 68], "14": [1, 5], "15": [2684, 20972454], "16": [1, 65], "17": [66, 16], "18": [6, 53], "19": [6, 53], "20": [53, 6], "21": [59, 6]}, "f": {"1": 283, "2": 16, "3": 105, "4": 3, "5": 70, "6": 67, "7": 80, "8": 6, "9": 68, "10": 66, "11": 1, "12": 59}, "fnMap": {"1": {"name": "(anonymous_1)", "line": 47, "loc": {"start": {"line": 47, "column": 14}, "end": {"line": 47, "column": 21}}}, "2": {"name": "(anonymous_2)", "line": 134, "loc": {"start": {"line": 134, "column": 20}, "end": {"line": 134, "column": 23}}}, "3": {"name": "(anonymous_3)", "line": 143, "loc": {"start": {"line": 143, "column": 7}, "end": {"line": 143, "column": 26}}}, "4": {"name": "(anonymous_4)", "line": 175, "loc": {"start": {"line": 175, "column": 13}, "end": {"line": 175, "column": 16}}}, "5": {"name": "(anonymous_5)", "line": 183, "loc": {"start": {"line": 183, "column": 19}, "end": {"line": 183, "column": 22}}}, "6": {"name": "(anonymous_6)", "line": 196, "loc": {"start": {"line": 196, "column": 21}, "end": {"line": 196, "column": 24}}}, "7": {"name": "(anonymous_7)", "line": 207, "loc": {"start": {"line": 207, "column": 18}, "end": {"line": 207, "column": 21}}}, "8": {"name": "(anonymous_8)", "line": 231, "loc": {"start": {"line": 231, "column": 7}, "end": {"line": 231, "column": 20}}}, "9": {"name": "(anonymous_9)", "line": 244, "loc": {"start": {"line": 244, "column": 13}, "end": {"line": 244, "column": 16}}}, "10": {"name": "(anonymous_10)", "line": 257, "loc": {"start": {"line": 257, "column": 13}, "end": {"line": 257, "column": 16}}}, "11": {"name": "(anonymous_11)", "line": 269, "loc": {"start": {"line": 269, "column": 17}, "end": {"line": 269, "column": 20}}}, "12": {"name": "(anonymous_12)", "line": 280, "loc": {"start": {"line": 280, "column": 11}, "end": {"line": 280, "column": 25}}}}, "statementMap": {"1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 42}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 51}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 21}}, "4": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 94}}, "5": {"start": {"line": 48, "column": 4}, "end": {"line": 48, "column": 21}}, "6": {"start": {"line": 50, "column": 4}, "end": {"line": 97, "column": 6}}, "7": {"start": {"line": 107, "column": 4}, "end": {"line": 107, "column": 48}}, "8": {"start": {"line": 114, "column": 4}, "end": {"line": 118, "column": 6}}, "9": {"start": {"line": 121, "column": 4}, "end": {"line": 121, "column": 42}}, "10": {"start": {"line": 122, "column": 4}, "end": {"line": 122, "column": 45}}, "11": {"start": {"line": 123, "column": 4}, "end": {"line": 123, "column": 32}}, "12": {"start": {"line": 123, "column": 21}, "end": {"line": 123, "column": 32}}, "13": {"start": {"line": 126, "column": 4}, "end": {"line": 126, "column": 48}}, "14": {"start": {"line": 127, "column": 4}, "end": {"line": 127, "column": 26}}, "15": {"start": {"line": 135, "column": 4}, "end": {"line": 135, "column": 41}}, "16": {"start": {"line": 144, "column": 4}, "end": {"line": 144, "column": 63}}, "17": {"start": {"line": 144, "column": 40}, "end": {"line": 144, "column": 63}}, "18": {"start": {"line": 151, "column": 4}, "end": {"line": 151, "column": 13}}, "19": {"start": {"line": 152, "column": 4}, "end": {"line": 165, "column": 5}}, "20": {"start": {"line": 153, "column": 6}, "end": {"line": 153, "column": 22}}, "21": {"start": {"line": 155, "column": 6}, "end": {"line": 164, "column": 7}}, "22": {"start": {"line": 156, "column": 8}, "end": {"line": 156, "column": 36}}, "23": {"start": {"line": 157, "column": 8}, "end": {"line": 157, "column": 30}}, "24": {"start": {"line": 158, "column": 8}, "end": {"line": 158, "column": 18}}, "25": {"start": {"line": 159, "column": 8}, "end": {"line": 159, "column": 18}}, "26": {"start": {"line": 160, "column": 8}, "end": {"line": 160, "column": 24}}, "27": {"start": {"line": 161, "column": 8}, "end": {"line": 161, "column": 22}}, "28": {"start": {"line": 163, "column": 8}, "end": {"line": 163, "column": 36}}, "29": {"start": {"line": 168, "column": 4}, "end": {"line": 168, "column": 33}}, "30": {"start": {"line": 176, "column": 4}, "end": {"line": 176, "column": 24}}, "31": {"start": {"line": 184, "column": 4}, "end": {"line": 186, "column": 5}}, "32": {"start": {"line": 185, "column": 6}, "end": {"line": 185, "column": 23}}, "33": {"start": {"line": 187, "column": 4}, "end": {"line": 187, "column": 44}}, "34": {"start": {"line": 188, "column": 4}, "end": {"line": 188, "column": 48}}, "35": {"start": {"line": 189, "column": 4}, "end": {"line": 189, "column": 21}}, "36": {"start": {"line": 197, "column": 4}, "end": {"line": 199, "column": 5}}, "37": {"start": {"line": 198, "column": 6}, "end": {"line": 198, "column": 71}}, "38": {"start": {"line": 198, "column": 35}, "end": {"line": 198, "column": 71}}, "39": {"start": {"line": 200, "column": 4}, "end": {"line": 200, "column": 29}}, "40": {"start": {"line": 208, "column": 4}, "end": {"line": 208, "column": 36}}, "41": {"start": {"line": 210, "column": 4}, "end": {"line": 212, "column": 5}}, "42": {"start": {"line": 211, "column": 6}, "end": {"line": 211, "column": 18}}, "43": {"start": {"line": 214, "column": 4}, "end": {"line": 222, "column": 5}}, "44": {"start": {"line": 215, "column": 6}, "end": {"line": 215, "column": 41}}, "45": {"start": {"line": 216, "column": 6}, "end": {"line": 218, "column": 7}}, "46": {"start": {"line": 217, "column": 8}, "end": {"line": 217, "column": 34}}, "47": {"start": {"line": 219, "column": 6}, "end": {"line": 219, "column": 72}}, "48": {"start": {"line": 221, "column": 6}, "end": {"line": 221, "column": 18}}, "49": {"start": {"line": 224, "column": 4}, "end": {"line": 224, "column": 31}}, "50": {"start": {"line": 232, "column": 4}, "end": {"line": 232, "column": 36}}, "51": {"start": {"line": 233, "column": 4}, "end": {"line": 233, "column": 51}}, "52": {"start": {"line": 234, "column": 4}, "end": {"line": 236, "column": 5}}, "53": {"start": {"line": 235, "column": 6}, "end": {"line": 235, "column": 36}}, "54": {"start": {"line": 237, "column": 4}, "end": {"line": 237, "column": 28}}, "55": {"start": {"line": 245, "column": 4}, "end": {"line": 245, "column": 28}}, "56": {"start": {"line": 246, "column": 4}, "end": {"line": 246, "column": 26}}, "57": {"start": {"line": 247, "column": 4}, "end": {"line": 249, "column": 5}}, "58": {"start": {"line": 248, "column": 6}, "end": {"line": 248, "column": 92}}, "59": {"start": {"line": 250, "column": 4}, "end": {"line": 250, "column": 15}}, "60": {"start": {"line": 258, "column": 4}, "end": {"line": 258, "column": 53}}, "61": {"start": {"line": 259, "column": 4}, "end": {"line": 261, "column": 5}}, "62": {"start": {"line": 260, "column": 6}, "end": {"line": 260, "column": 34}}, "63": {"start": {"line": 262, "column": 4}, "end": {"line": 262, "column": 14}}, "64": {"start": {"line": 270, "column": 4}, "end": {"line": 272, "column": 31}}, "65": {"start": {"line": 281, "column": 4}, "end": {"line": 281, "column": 21}}, "66": {"start": {"line": 282, "column": 4}, "end": {"line": 284, "column": 5}}, "67": {"start": {"line": 283, "column": 6}, "end": {"line": 283, "column": 38}}, "68": {"start": {"line": 286, "column": 4}, "end": {"line": 288, "column": 5}}, "69": {"start": {"line": 287, "column": 6}, "end": {"line": 287, "column": 79}}, "70": {"start": {"line": 290, "column": 4}, "end": {"line": 294, "column": 5}}, "71": {"start": {"line": 291, "column": 6}, "end": {"line": 291, "column": 32}}, "72": {"start": {"line": 293, "column": 6}, "end": {"line": 293, "column": 29}}, "73": {"start": {"line": 298, "column": 0}, "end": {"line": 298, "column": 28}}}, "branchMap": {"1": {"line": 48, "type": "binary-expr", "locations": [{"start": {"line": 48, "column": 11}, "end": {"line": 48, "column": 15}}, {"start": {"line": 48, "column": 19}, "end": {"line": 48, "column": 21}}]}, "2": {"line": 123, "type": "if", "locations": [{"start": {"line": 123, "column": 4}, "end": {"line": 123, "column": 4}}, {"start": {"line": 123, "column": 4}, "end": {"line": 123, "column": 4}}]}, "3": {"line": 126, "type": "binary-expr", "locations": [{"start": {"line": 126, "column": 20}, "end": {"line": 126, "column": 27}}, {"start": {"line": 126, "column": 31}, "end": {"line": 126, "column": 43}}, {"start": {"line": 126, "column": 47}, "end": {"line": 126, "column": 48}}]}, "4": {"line": 144, "type": "if", "locations": [{"start": {"line": 144, "column": 4}, "end": {"line": 144, "column": 4}}, {"start": {"line": 144, "column": 4}, "end": {"line": 144, "column": 4}}]}, "5": {"line": 152, "type": "if", "locations": [{"start": {"line": 152, "column": 4}, "end": {"line": 152, "column": 4}}, {"start": {"line": 152, "column": 4}, "end": {"line": 152, "column": 4}}]}, "6": {"line": 155, "type": "if", "locations": [{"start": {"line": 155, "column": 6}, "end": {"line": 155, "column": 6}}, {"start": {"line": 155, "column": 6}, "end": {"line": 155, "column": 6}}]}, "7": {"line": 184, "type": "if", "locations": [{"start": {"line": 184, "column": 4}, "end": {"line": 184, "column": 4}}, {"start": {"line": 184, "column": 4}, "end": {"line": 184, "column": 4}}]}, "8": {"line": 197, "type": "if", "locations": [{"start": {"line": 197, "column": 4}, "end": {"line": 197, "column": 4}}, {"start": {"line": 197, "column": 4}, "end": {"line": 197, "column": 4}}]}, "9": {"line": 197, "type": "binary-expr", "locations": [{"start": {"line": 197, "column": 8}, "end": {"line": 197, "column": 27}}, {"start": {"line": 197, "column": 31}, "end": {"line": 197, "column": 57}}]}, "10": {"line": 198, "type": "if", "locations": [{"start": {"line": 198, "column": 6}, "end": {"line": 198, "column": 6}}, {"start": {"line": 198, "column": 6}, "end": {"line": 198, "column": 6}}]}, "11": {"line": 210, "type": "if", "locations": [{"start": {"line": 210, "column": 4}, "end": {"line": 210, "column": 4}}, {"start": {"line": 210, "column": 4}, "end": {"line": 210, "column": 4}}]}, "12": {"line": 210, "type": "binary-expr", "locations": [{"start": {"line": 210, "column": 8}, "end": {"line": 210, "column": 23}}, {"start": {"line": 210, "column": 27}, "end": {"line": 210, "column": 60}}]}, "13": {"line": 216, "type": "if", "locations": [{"start": {"line": 216, "column": 6}, "end": {"line": 216, "column": 6}}, {"start": {"line": 216, "column": 6}, "end": {"line": 216, "column": 6}}]}, "14": {"line": 234, "type": "if", "locations": [{"start": {"line": 234, "column": 4}, "end": {"line": 234, "column": 4}}, {"start": {"line": 234, "column": 4}, "end": {"line": 234, "column": 4}}]}, "15": {"line": 248, "type": "cond-expr", "locations": [{"start": {"line": 248, "column": 22}, "end": {"line": 248, "column": 54}}, {"start": {"line": 248, "column": 57}, "end": {"line": 248, "column": 92}}]}, "16": {"line": 259, "type": "if", "locations": [{"start": {"line": 259, "column": 4}, "end": {"line": 259, "column": 4}}, {"start": {"line": 259, "column": 4}, "end": {"line": 259, "column": 4}}]}, "17": {"line": 259, "type": "binary-expr", "locations": [{"start": {"line": 259, "column": 8}, "end": {"line": 259, "column": 23}}, {"start": {"line": 259, "column": 27}, "end": {"line": 259, "column": 51}}]}, "18": {"line": 282, "type": "if", "locations": [{"start": {"line": 282, "column": 4}, "end": {"line": 282, "column": 4}}, {"start": {"line": 282, "column": 4}, "end": {"line": 282, "column": 4}}]}, "19": {"line": 286, "type": "if", "locations": [{"start": {"line": 286, "column": 4}, "end": {"line": 286, "column": 4}}, {"start": {"line": 286, "column": 4}, "end": {"line": 286, "column": 4}}]}, "20": {"line": 290, "type": "if", "locations": [{"start": {"line": 290, "column": 4}, "end": {"line": 290, "column": 4}}, {"start": {"line": 290, "column": 4}, "end": {"line": 290, "column": 4}}]}, "21": {"line": 290, "type": "binary-expr", "locations": [{"start": {"line": 290, "column": 8}, "end": {"line": 290, "column": 33}}, {"start": {"line": 290, "column": 37}, "end": {"line": 290, "column": 58}}]}}}}