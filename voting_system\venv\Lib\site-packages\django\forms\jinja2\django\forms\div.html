{{ errors }}
{% if errors and not fields %}
  <div>{% for field in hidden_fields %}{{ field }}{% endfor %}</div>
{% endif %}
{% for field, errors in fields %}
  <div{% set classes = field.css_classes() %}{% if classes %} class="{{ classes }}"{% endif %}>
    {{ field.as_field_group() }}
    {% if loop.last %}
      {% for field in hidden_fields %}{{ field }}{% endfor %}
    {% endif %}
</div>
{% endfor %}
{% if not fields and not errors %}
  {% for field in hidden_fields %}{{ field }}{% endfor %}
{% endif %}
