# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2013-2014
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-01-19 16:49+0100\n"
"PO-Revision-Date: 2017-09-23 18:54+0000\n"
"Last-Translator: <PERSON><PERSON> <jann<PERSON>@leidel.info>\n"
"Language-Team: Swahili (http://www.transifex.com/django/django/language/"
"sw/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: sw\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "GIS"
msgstr "GIS"

msgid "The base GIS field."
msgstr ""

msgid ""
"The base Geometry field -- maps to the OpenGIS Specification Geometry type."
msgstr ""

msgid "Point"
msgstr "Nukta"

msgid "Line string"
msgstr "Mstari"

msgid "Polygon"
msgstr "Poligoni"

msgid "Multi-point"
msgstr "Nukta zaidi ya moja"

msgid "Multi-line string"
msgstr "Mstari zaidi ya mmoja. "

msgid "Multi polygon"
msgstr "Poligoni zaidi ya moja"

msgid "Geometry collection"
msgstr "Mkusanyiko wa jiometri"

msgid "Extent Aggregate Field"
msgstr ""

msgid "Raster Field"
msgstr ""

msgid "No geometry value provided."
msgstr "Hakuna thamani ya jiometri iliyotolewa"

msgid "Invalid geometry value."
msgstr "Thamani batili ya jiometri."

msgid "Invalid geometry type."
msgstr "Aina batili ya jiometri."

msgid ""
"An error occurred when transforming the geometry to the SRID of the geometry "
"form field."
msgstr ""
"Hitilafu imetokea wakati wa kubadilisha jiometri kuwa SRID ya sehemu ya fomu "
"ya jiometri."

msgid "Delete all Features"
msgstr ""

msgid "WKT debugging window:"
msgstr ""

msgid "Debugging window (serialized value)"
msgstr ""

msgid "No feeds are registered."
msgstr "Hakuna mlisho uliosajiliwa."

#, python-format
msgid "Slug %r isn't registered."
msgstr "Slagi %r haijasajiliwa"
