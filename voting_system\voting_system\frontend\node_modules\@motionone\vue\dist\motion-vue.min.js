var Motion=function(t,e){"use strict";function n(t,e){-1===t.indexOf(e)&&t.push(e)}const i=(t,e,n)=>Math.min(Math.max(n,t),e),a={duration:.3,delay:0,endDelay:0,repeat:0,easing:"ease"},s=t=>"number"==typeof t,o=t=>Array.isArray(t)&&!s(t[0]);function r(t,e){return o(t)?t[((t,e,n)=>{const i=e-t;return((n-t)%i+i)%i+t})(0,t.length,e)]:t}const l=(t,e,n)=>-n*t+n*e+t,c=()=>{},u=t=>t,h=(t,e,n)=>e-t==0?1:(n-t)/(e-t);function d(t,e){const n=t[t.length-1];for(let i=1;i<=e;i++){const a=h(0,e,i);t.push(l(n,1,a))}}function p(t){const e=[0];return d(e,t-1),e}const f=t=>Array.isArray(t)&&s(t[0]),m=t=>"object"==typeof t&&Boolean(t.createAnimation),v=t=>"function"==typeof t,g=t=>1e3*t,y=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t;function b(t,e,n,i){if(t===e&&n===i)return u;const a=e=>function(t,e,n,i,a){let s,o,r=0;do{o=e+(n-e)/2,s=y(o,i,a)-t,s>0?n=o:e=o}while(Math.abs(s)>1e-7&&++r<12);return o}(e,0,1,t,n);return t=>0===t||1===t?t:y(a(t),e,i)}const w={ease:b(.25,.1,.25,1),"ease-in":b(.42,0,1,1),"ease-in-out":b(.42,0,.58,1),"ease-out":b(0,0,.58,1)},O=/\((.*?)\)/;function E(t){if(v(t))return t;if(f(t))return b(...t);if(w[t])return w[t];if(t.startsWith("steps")){const e=O.exec(t);if(e){const t=e[1].split(",");return function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"end";return n=>{const a=(n="end"===e?Math.min(n,.999):Math.max(n,.001))*t,s="end"===e?Math.floor(a):Math.ceil(a);return i(0,1,s/t)}}(parseFloat(t[0]),t[1].trim())}}return u}class A{constructor(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[0,1],{easing:n,duration:s=a.duration,delay:c=a.delay,endDelay:f=a.endDelay,repeat:v=a.repeat,offset:g,direction:y="normal"}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(this.startTime=null,this.rate=1,this.t=0,this.cancelTimestamp=null,this.easing=u,this.duration=0,this.totalDuration=0,this.repeat=0,this.playState="idle",this.finished=new Promise(((t,e)=>{this.resolve=t,this.reject=e})),n=n||a.easing,m(n)){const t=n.createAnimation(e);n=t.easing,e=t.keyframes||e,s=t.duration||s}this.repeat=v,this.easing=o(n)?u:E(n),this.updateDuration(s);const b=function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p(t.length),n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:u;const a=t.length,s=a-e.length;return s>0&&d(e,s),s=>{let o=0;for(;o<a-2&&!(s<e[o+1]);o++);let c=i(0,1,h(e[o],e[o+1],s));return c=r(n,o)(c),l(t[o],t[o+1],c)}}(e,g,o(n)?n.map(E):u);this.tick=e=>{var n;let i=0;i=void 0!==this.pauseTime?this.pauseTime:(e-this.startTime)*this.rate,this.t=i,i/=1e3,i=Math.max(i-c,0),"finished"===this.playState&&void 0===this.pauseTime&&(i=this.totalDuration);const a=i/this.duration;let s=Math.floor(a),o=a%1;!o&&a>=1&&(o=1),1===o&&s--;const r=s%2;("reverse"===y||"alternate"===y&&r||"alternate-reverse"===y&&!r)&&(o=1-o);const l=i>=this.totalDuration?1:Math.min(o,1),u=b(this.easing(l));t(u);void 0===this.pauseTime&&("finished"===this.playState||i>=this.totalDuration+f)?(this.playState="finished",null===(n=this.resolve)||void 0===n||n.call(this,u)):"idle"!==this.playState&&(this.frameRequestId=requestAnimationFrame(this.tick))},this.play()}play(){const t=performance.now();this.playState="running",void 0!==this.pauseTime?this.startTime=t-this.pauseTime:this.startTime||(this.startTime=t),this.cancelTimestamp=this.startTime,this.pauseTime=void 0,this.frameRequestId=requestAnimationFrame(this.tick)}pause(){this.playState="paused",this.pauseTime=this.t}finish(){this.playState="finished",this.tick(0)}stop(){var t;this.playState="idle",void 0!==this.frameRequestId&&cancelAnimationFrame(this.frameRequestId),null===(t=this.reject)||void 0===t||t.call(this,!1)}cancel(){this.stop(),this.tick(this.cancelTimestamp)}reverse(){this.rate*=-1}commitStyles(){}updateDuration(t){this.duration=t,this.totalDuration=t*(this.repeat+1)}get currentTime(){return this.t}set currentTime(t){void 0!==this.pauseTime||0===this.rate?this.pauseTime=t:this.startTime=performance.now()-t/this.rate}get playbackRate(){return this.rate}set playbackRate(t){this.rate=t}}class j{setAnimation(t){this.animation=t,null==t||t.finished.then((()=>this.clearAnimation())).catch((()=>{}))}clearAnimation(){this.animation=this.generator=void 0}}const S=new WeakMap;function x(t){return S.has(t)||S.set(t,{transforms:[],values:new Map}),S.get(t)}function T(t,e){return t.has(e)||t.set(e,new j),t.get(e)}const D=["","X","Y","Z"],M={x:"translateX",y:"translateY",z:"translateZ"},k={syntax:"<angle>",initialValue:"0deg",toDefaultUnit:t=>t+"deg"},P={translate:{syntax:"<length-percentage>",initialValue:"0px",toDefaultUnit:t=>t+"px"},rotate:k,scale:{syntax:"<number>",initialValue:1,toDefaultUnit:u},skew:k},V=new Map,B=t=>`--motion-${t}`,L=["x","y","z"];["translate","scale","rotate","skew"].forEach((t=>{D.forEach((e=>{L.push(t+e),V.set(B(t+e),P[t])}))}));const C=(t,e)=>L.indexOf(t)-L.indexOf(e),R=new Set(L),U=t=>R.has(t),$=(t,e)=>{M[e]&&(e=M[e]);const{transforms:i}=x(t);n(i,e),t.style.transform=I(i)},I=t=>t.sort(C).reduce(q,"").trim(),q=(t,e)=>`${t} ${e}(var(${B(e)}))`,F=t=>t.startsWith("--"),W=new Set;function _(t){if(!W.has(t)){W.add(t);try{const{syntax:e,initialValue:n}=V.has(t)?V.get(t):{};CSS.registerProperty({name:t,inherits:!1,syntax:e,initialValue:n})}catch(t){}}}const z=(t,e)=>document.createElement("div").animate(t,e),K={cssRegisterProperty:()=>"undefined"!=typeof CSS&&Object.hasOwnProperty.call(CSS,"registerProperty"),waapi:()=>Object.hasOwnProperty.call(Element.prototype,"animate"),partialKeyframes:()=>{try{z({opacity:[1]})}catch(t){return!1}return!0},finished:()=>Boolean(z({opacity:[0,1]},{duration:.001}).finished),linearEasing:()=>{try{z({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0}},X={},Y={};for(const t in K)Y[t]=()=>(void 0===X[t]&&(X[t]=K[t]()),X[t]);const Z=(t,e)=>v(t)?Y.linearEasing()?`linear(${((t,e)=>{let n="";const i=Math.round(e/.015);for(let e=0;e<i;e++)n+=t(h(0,i-1,e))+", ";return n.substring(0,n.length-2)})(t,e)})`:a.easing:f(t)?N(t):t,N=t=>{let[e,n,i,a]=t;return`cubic-bezier(${e}, ${n}, ${i}, ${a})`};function G(t,e){for(let n=0;n<t.length;n++)null===t[n]&&(t[n]=n?t[n-1]:e());return t}const H=t=>Array.isArray(t)?t:[t];function J(t){return M[t]&&(t=M[t]),U(t)?B(t):t}const Q=(t,e)=>{e=J(e);let n=F(e)?t.style.getPropertyValue(e):getComputedStyle(t)[e];if(!n&&0!==n){const t=V.get(e);t&&(n=t.initialValue)}return n},tt=(t,e,n)=>{e=J(e),F(e)?t.style.setProperty(e,n):t.style[e]=n};function et(t){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(t&&"finished"!==t.playState)try{t.stop?t.stop():(e&&t.commitStyles(),t.cancel())}catch(t){}}function nt(t,e){var n;let i=(null==e?void 0:e.toDefaultUnit)||u;const a=t[t.length-1];if("string"==typeof a){const t=(null===(n=a.match(/(-?[\d.]+)([a-z%]*)/))||void 0===n?void 0:n[2])||"";t&&(i=e=>e+t)}return i}function it(){return window.__MOTION_DEV_TOOLS_RECORD}function at(t,e,n){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},r=arguments.length>4?arguments[4]:void 0;const l=it(),u=!1!==i.record&&l;let h,{duration:d=a.duration,delay:p=a.delay,endDelay:f=a.endDelay,repeat:y=a.repeat,easing:b=a.easing,persist:w=!1,direction:O,offset:E,allowWebkitAcceleration:A=!1}=i;const j=x(t),S=U(e);let D=Y.waapi();S&&$(t,e);const M=J(e),k=T(j.values,M),P=V.get(M);return et(k.animation,!(m(b)&&k.generator)&&!1!==i.record),()=>{const a=()=>{var e,n;return null!==(n=null!==(e=Q(t,M))&&void 0!==e?e:null==P?void 0:P.initialValue)&&void 0!==n?n:0};let j=G(H(n),a);const x=nt(j,P);if(m(b)){const t=b.createAnimation(j,"opacity"!==e,a,M,k);b=t.easing,j=t.keyframes||j,d=t.duration||d}if(F(M)&&(Y.cssRegisterProperty()?_(M):D=!1),S&&!Y.linearEasing()&&(v(b)||o(b)&&b.some(v))&&(D=!1),D){P&&(j=j.map((t=>s(t)?P.toDefaultUnit(t):t))),1!==j.length||Y.partialKeyframes()&&!u||j.unshift(a());const e={delay:g(p),duration:g(d),endDelay:g(f),easing:o(b)?void 0:Z(b,d),direction:O,iterations:y+1,fill:"both"};h=t.animate({[M]:j,offset:E,easing:o(b)?b.map((t=>Z(t,d))):void 0},e),h.finished||(h.finished=new Promise(((t,e)=>{h.onfinish=t,h.oncancel=e})));const n=j[j.length-1];h.finished.then((()=>{w||(tt(t,M,n),h.cancel())})).catch(c),A||(h.playbackRate=1.000001)}else if(r&&S)j=j.map((t=>"string"==typeof t?parseFloat(t):t)),1===j.length&&j.unshift(parseFloat(a())),h=new r((e=>{tt(t,M,x?x(e):e)}),j,Object.assign(Object.assign({},i),{duration:d,easing:b}));else{const e=j[j.length-1];tt(t,M,P&&s(e)?P.toDefaultUnit(e):e)}return u&&l(t,e,j,{duration:d,delay:p,easing:b,repeat:y,offset:E},"motion-one"),k.setAnimation(h),h}}const st=(t,e)=>t[e]?Object.assign(Object.assign({},t),t[e]):Object.assign({},t);function ot(t,e){var n;return"string"==typeof t?e?(null!==(n=e[t])&&void 0!==n||(e[t]=document.querySelectorAll(t)),t=e[t]):t=document.querySelectorAll(t):t instanceof Element&&(t=[t]),Array.from(t||[])}function rt(t,e){var n={};for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&e.indexOf(i)<0&&(n[i]=t[i]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(i=Object.getOwnPropertySymbols(t);a<i.length;a++)e.indexOf(i[a])<0&&Object.prototype.propertyIsEnumerable.call(t,i[a])&&(n[i[a]]=t[i[a]])}return n}const lt={any:0,all:1};function ct(t,e){return typeof t!=typeof e||(Array.isArray(t)&&Array.isArray(e)?!function(t,e){const n=e.length;if(n!==t.length)return!1;for(let i=0;i<n;i++)if(e[i]!==t[i])return!1;return!0}(t,e):t!==e)}function ut(t,e){return function(t){return"object"==typeof t}(t)?t:t&&e?e[t]:void 0}let ht;function dt(){if(!ht)return;const t=ht.sort(mt).map(vt);t.forEach(gt),t.forEach(gt),ht=void 0}function pt(t){ht?n(ht,t):(ht=[t],requestAnimationFrame(dt))}function ft(t){ht&&function(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}(ht,t)}const mt=(t,e)=>t.getDepth()-e.getDepth(),vt=t=>t.animateUpdates(),gt=t=>t.next(),yt=(t,e)=>new CustomEvent(t,{detail:{target:e}});function bt(t,e,n){t.dispatchEvent(new CustomEvent(e,{detail:{originalEvent:n}}))}function wt(t,e,n){t.dispatchEvent(new CustomEvent(e,{detail:{originalEntry:n}}))}const Ot={isActive:t=>Boolean(t.inView),subscribe:(t,e,n)=>{let{enable:i,disable:a}=e,{inViewOptions:s={}}=n;const{once:o}=s,r=rt(s,["once"]);return function(t,e){let{root:n,margin:i,amount:a="any"}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if("undefined"==typeof IntersectionObserver)return()=>{};const s=ot(t),o=new WeakMap,r=new IntersectionObserver((t=>{t.forEach((t=>{const n=o.get(t.target);if(t.isIntersecting!==Boolean(n))if(t.isIntersecting){const n=e(t);v(n)?o.set(t.target,n):r.unobserve(t.target)}else n&&(n(t),o.delete(t.target))}))}),{root:n,rootMargin:i,threshold:"number"==typeof a?a:lt[a]});return s.forEach((t=>r.observe(t))),()=>r.disconnect()}(t,(e=>{if(i(),wt(t,"viewenter",e),!o)return e=>{a(),wt(t,"viewleave",e)}}),r)}},Et=(t,e,n)=>i=>{i.pointerType&&"mouse"!==i.pointerType||(n(),bt(t,e,i))},At={inView:Ot,hover:{isActive:t=>Boolean(t.hover),subscribe:(t,e)=>{let{enable:n,disable:i}=e;const a=Et(t,"hoverstart",n),s=Et(t,"hoverend",i);return t.addEventListener("pointerenter",a),t.addEventListener("pointerleave",s),()=>{t.removeEventListener("pointerenter",a),t.removeEventListener("pointerleave",s)}}},press:{isActive:t=>Boolean(t.press),subscribe:(t,e)=>{let{enable:n,disable:i}=e;const a=e=>{i(),bt(t,"pressend",e),window.removeEventListener("pointerup",a)},s=e=>{n(),bt(t,"pressstart",e),window.addEventListener("pointerup",a)};return t.addEventListener("pointerdown",s),()=>{t.removeEventListener("pointerdown",s),window.removeEventListener("pointerup",a)}}}},jt=["initial","animate",...Object.keys(At),"exit"],St=new WeakMap;function xt(t){const e={},n=[];for(let i in t){const a=t[i];U(i)&&(M[i]&&(i=M[i]),n.push(i),i=B(i));let o=Array.isArray(a)?a[0]:a;const r=V.get(i);r&&(o=s(a)?r.toDefaultUnit(a):a),e[i]=o}return n.length&&(e.transform=I(n)),e}const Tt="motion-state",Dt="motion-presence",Mt=()=>({type:Object}),kt=e.defineComponent({name:"Motion",inheritAttrs:!0,props:{tag:{type:String,default:"div"},initial:{type:[Object,Boolean]},animate:Mt(),inView:Mt(),hover:Mt(),press:Mt(),exit:Mt(),inViewOptions:Mt(),transition:Mt(),style:Mt()},setup(t){const n=e.ref(null),i=e.inject(Tt,void 0),a=e.inject(Dt,void 0),s=function(){let t,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments.length>1?arguments[1]:void 0,i=n?n.getDepth()+1:0;const a={initial:!0,animate:!0},s={},o={};for(const t of jt)o[t]="string"==typeof e[t]?e[t]:null==n?void 0:n.getContext()[t];const r=!1===e.initial?"animate":"initial";let l=rt(ut(e[r]||o[r],e.variants)||{},["transition"]);const u=Object.assign({},l);function*h(){var n,i;const s=l;l={};const o={};for(const t of jt){if(!a[t])continue;const s=ut(e[t]);if(s)for(const t in s)"transition"!==t&&(l[t]=s[t],o[t]=st(null!==(i=null!==(n=s.transition)&&void 0!==n?n:e.transition)&&void 0!==i?i:{},t))}const r=new Set([...Object.keys(l),...Object.keys(s)]),h=[];r.forEach((e=>{var n;void 0===l[e]&&(l[e]=u[e]),ct(s[e],l[e])&&(null!==(n=u[e])&&void 0!==n||(u[e]=Q(t,e)),h.push(at(t,e,l[e],o[e],A)))})),yield;const d=h.map((t=>t())).filter(Boolean);if(!d.length)return;const p=l;t.dispatchEvent(yt("motionstart",p)),Promise.all(d.map((t=>t.finished))).then((()=>{t.dispatchEvent(yt("motioncomplete",p))})).catch(c)}const d=(t,e)=>()=>{a[t]=e,pt(f)},p=()=>{for(const n in At){const i=At[n].isActive(e),a=s[n];i&&!a?s[n]=At[n].subscribe(t,{enable:d(n,!0),disable:d(n,!1)},e):!i&&a&&(a(),delete s[n])}},f={update:n=>{t&&(e=n,p(),pt(f))},setActive:(e,n)=>{t&&(a[e]=n,pt(f))},animateUpdates:h,getDepth:()=>i,getTarget:()=>l,getOptions:()=>e,getContext:()=>o,mount:e=>(t=e,St.set(t,f),p(),()=>{St.delete(t),ft(f);for(const t in s)s[t]()}),isMounted:()=>Boolean(t)};return f}(Object.assign(Object.assign({},t),{initial:!1===(null==a?void 0:a.initial)?a.initial:!0===t.initial?void 0:t.initial}),i);e.provide(Tt,s),e.onMounted((()=>{const e=s.mount(n.value);return s.update(Object.assign(Object.assign({},t),{initial:!0===t.initial?void 0:t.initial})),e}));let o=!1;return e.onUpdated((()=>{if(!o&&n.value){o=!0;const t=xt(s.getTarget());for(const e in t)tt(n.value,e,t[e])}s.update(Object.assign(Object.assign({},t),{initial:!0===t.initial?void 0:t.initial}))})),{state:s,root:n,initialStyles:xt(s.getTarget())}},render(){var t,n;return e.h(this.tag,{ref:"root",style:this.state.isMounted()?this.style:Object.assign(Object.assign({},this.style),this.initialStyles)},null===(n=(t=this.$slots).default)||void 0===n?void 0:n.call(t))}}),Pt=new WeakMap;function Vt(t){const e=Pt.get(t);e&&t.removeEventListener("motioncomplete",e),Pt.delete(t)}const Bt=e.defineComponent({name:"Presence",props:{name:{type:String},initial:{type:Boolean,default:!0},exitBeforeEnter:{type:Boolean,default:!1}},methods:{enter(t){const e=St.get(t);e&&(Vt(t),e.setActive("exit",!1))},exit(t,e){const n=St.get(t);if(!n)return e();n.setActive("exit",!0),Vt(t),Pt.set(t,e),t.addEventListener("motioncomplete",e)}},setup(t){let{initial:n}=t;const i={initial:n};e.provide(Dt,i),e.onBeforeUpdate((()=>{i.initial=void 0}))},render(){return e.h(e.Transition,{name:this.name,onEnter:this.enter,onLeave:this.exit,css:!1,mode:this.exitBeforeEnter?"out-in":void 0},this.$slots.default)}});return t.Motion=kt,t.Presence=Bt,Object.defineProperty(t,"__esModule",{value:!0}),t}({},Vue);