# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2011
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-10-09 17:42+0200\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: <PERSON><PERSON> <jann<PERSON>@leidel.info>\n"
"Language-Team: Urdu (http://www.transifex.com/django/django/language/ur/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ur\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Advanced options"
msgstr "اعلٰی اختیارات"

msgid "Flat Pages"
msgstr ""

msgid "URL"
msgstr "یو آر ایل (URL("

msgid ""
"Example: '/about/contact/'. Make sure to have leading and trailing slashes."
msgstr ""
"مثال:  '/about/contact/'۔ یقین کر لیں کہ سابقہ اور لاحقہ سلیش موجود ھے۔"

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"اس قیمت (ویلیو) کا صرف حروف، نمبروں، نقاط، انڈرسکور، ڈیش، سلیش، یا ٹائلڈ پر "
"مشتمل ھونا ضروری ھے۔"

msgid "URL is missing a leading slash."
msgstr ""

msgid "URL is missing a trailing slash."
msgstr ""

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr ""

msgid "title"
msgstr "عنوان"

msgid "content"
msgstr "مضمون"

msgid "enable comments"
msgstr "تبصرے فعال کریں"

msgid "template name"
msgstr "قالب کا نام"

msgid ""
"Example: 'flatpages/contact_page.html'. If this isn't provided, the system "
"will use 'flatpages/default.html'."
msgstr ""
"مثال: 'flatpages/contact_page.html'۔ اگر یہ مھیا نھیں کیا گیا تو سسٹم "
"'flatpages/default.html' استعمال کرے گا۔"

msgid "registration required"
msgstr "رجسٹریشن ضروری ھے"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr ""
"اگر یہ فعال ھے تو صرف اندر آئے ھوئے (logged-in) صارفین یہ صفحہ دیکھنے کے "
"قابل ھوں گے۔"

msgid "sites"
msgstr ""

msgid "flat page"
msgstr "ھموار صفحہ"

msgid "flat pages"
msgstr "ھموار صفحے"
