{"version": 3, "file": "index.js", "sources": ["../../src/node.ts", "../../src/browser/assets/style.ts", "../../../../../node_modules/babel-plugin-transform-async-to-promises/helpers.mjs", "../../src/browser/assets/logo.ts", "../../src/browser/constants/index.ts", "../../src/browser/components/Header.tsx", "../../src/browser/components/ConnectButton.tsx", "../../src/browser/assets/caret.ts", "../../src/browser/components/WalletButton.tsx", "../../src/browser/components/WalletIcon.tsx", "../../src/browser/components/LinkDisplay.tsx", "../../src/browser/components/Notification.tsx", "../../src/browser/components/QRCodeDisplay.tsx", "../../src/browser/components/Modal.tsx", "../../src/browser/languages/de.ts", "../../src/browser/languages/en.ts", "../../src/browser/languages/es.ts", "../../src/browser/languages/fr.ts", "../../src/browser/languages/ko.ts", "../../src/browser/languages/pt.ts", "../../src/browser/languages/zh.ts", "../../src/browser/languages/fa.ts", "../../src/browser/languages/index.ts", "../../src/browser/index.tsx", "../../src/index.ts"], "sourcesContent": ["import QRCode from \"qrcode\";\n\nexport function open(uri: string) {\n  // eslint-disable-next-line no-console\n  QRCode.toString(uri, { type: \"terminal\" }).then(console.log);\n}\n\nexport function close() {\n  // empty\n}\n", "export const WALLETCONNECT_STYLE_SHEET = `:root {\n  --animation-duration: 300ms;\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n\n@keyframes fadeOut {\n  from {\n    opacity: 1;\n  }\n  to {\n    opacity: 0;\n  }\n}\n\n.animated {\n  animation-duration: var(--animation-duration);\n  animation-fill-mode: both;\n}\n\n.fadeIn {\n  animation-name: fadeIn;\n}\n\n.fadeOut {\n  animation-name: fadeOut;\n}\n\n#walletconnect-wrapper {\n  -webkit-user-select: none;\n  align-items: center;\n  display: flex;\n  height: 100%;\n  justify-content: center;\n  left: 0;\n  pointer-events: none;\n  position: fixed;\n  top: 0;\n  user-select: none;\n  width: 100%;\n  z-index: 99999999999999;\n}\n\n.walletconnect-modal__headerLogo {\n  height: 21px;\n}\n\n.walletconnect-modal__header p {\n  color: #ffffff;\n  font-size: 20px;\n  font-weight: 600;\n  margin: 0;\n  align-items: flex-start;\n  display: flex;\n  flex: 1;\n  margin-left: 5px;\n}\n\n.walletconnect-modal__close__wrapper {\n  position: absolute;\n  top: 0px;\n  right: 0px;\n  z-index: 10000;\n  background: white;\n  border-radius: 26px;\n  padding: 6px;\n  box-sizing: border-box;\n  width: 26px;\n  height: 26px;\n  cursor: pointer;\n}\n\n.walletconnect-modal__close__icon {\n  position: relative;\n  top: 7px;\n  right: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transform: rotate(45deg);\n}\n\n.walletconnect-modal__close__line1 {\n  position: absolute;\n  width: 100%;\n  border: 1px solid rgb(48, 52, 59);\n}\n\n.walletconnect-modal__close__line2 {\n  position: absolute;\n  width: 100%;\n  border: 1px solid rgb(48, 52, 59);\n  transform: rotate(90deg);\n}\n\n.walletconnect-qrcode__base {\n  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\n  background: rgba(37, 41, 46, 0.95);\n  height: 100%;\n  left: 0;\n  pointer-events: auto;\n  position: fixed;\n  top: 0;\n  transition: 0.4s cubic-bezier(0.19, 1, 0.22, 1);\n  width: 100%;\n  will-change: opacity;\n  padding: 40px;\n  box-sizing: border-box;\n}\n\n.walletconnect-qrcode__text {\n  color: rgba(60, 66, 82, 0.6);\n  font-size: 16px;\n  font-weight: 600;\n  letter-spacing: 0;\n  line-height: 1.1875em;\n  margin: 10px 0 20px 0;\n  text-align: center;\n  width: 100%;\n}\n\n@media only screen and (max-width: 768px) {\n  .walletconnect-qrcode__text {\n    font-size: 4vw;\n  }\n}\n\n@media only screen and (max-width: 320px) {\n  .walletconnect-qrcode__text {\n    font-size: 14px;\n  }\n}\n\n.walletconnect-qrcode__image {\n  width: calc(100% - 30px);\n  box-sizing: border-box;\n  cursor: none;\n  margin: 0 auto;\n}\n\n.walletconnect-qrcode__notification {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  font-size: 16px;\n  padding: 16px 20px;\n  border-radius: 16px;\n  text-align: center;\n  transition: all 0.1s ease-in-out;\n  background: white;\n  color: black;\n  margin-bottom: -60px;\n  opacity: 0;\n}\n\n.walletconnect-qrcode__notification.notification__show {\n  opacity: 1;\n}\n\n@media only screen and (max-width: 768px) {\n  .walletconnect-modal__header {\n    height: 130px;\n  }\n  .walletconnect-modal__base {\n    overflow: auto;\n  }\n}\n\n@media only screen and (min-device-width: 415px) and (max-width: 768px) {\n  #content {\n    max-width: 768px;\n    box-sizing: border-box;\n  }\n}\n\n@media only screen and (min-width: 375px) and (max-width: 415px) {\n  #content {\n    max-width: 414px;\n    box-sizing: border-box;\n  }\n}\n\n@media only screen and (min-width: 320px) and (max-width: 375px) {\n  #content {\n    max-width: 375px;\n    box-sizing: border-box;\n  }\n}\n\n@media only screen and (max-width: 320px) {\n  #content {\n    max-width: 320px;\n    box-sizing: border-box;\n  }\n}\n\n.walletconnect-modal__base {\n  -webkit-font-smoothing: antialiased;\n  background: #ffffff;\n  border-radius: 24px;\n  box-shadow: 0 10px 50px 5px rgba(0, 0, 0, 0.4);\n  font-family: ui-rounded, \"SF Pro Rounded\", \"SF Pro Text\", medium-content-sans-serif-font,\n    -apple-system, BlinkMacSystemFont, ui-sans-serif, \"Segoe UI\", Roboto, Oxygen, Ubuntu, Cantarell,\n    \"Open Sans\", \"Helvetica Neue\", sans-serif;\n  margin-top: 41px;\n  padding: 24px 24px 22px;\n  pointer-events: auto;\n  position: relative;\n  text-align: center;\n  transition: 0.4s cubic-bezier(0.19, 1, 0.22, 1);\n  will-change: transform;\n  overflow: visible;\n  transform: translateY(-50%);\n  top: 50%;\n  max-width: 500px;\n  margin: auto;\n}\n\n@media only screen and (max-width: 320px) {\n  .walletconnect-modal__base {\n    padding: 24px 12px;\n  }\n}\n\n.walletconnect-modal__base .hidden {\n  transform: translateY(150%);\n  transition: 0.125s cubic-bezier(0.4, 0, 1, 1);\n}\n\n.walletconnect-modal__header {\n  align-items: center;\n  display: flex;\n  height: 26px;\n  left: 0;\n  justify-content: space-between;\n  position: absolute;\n  top: -42px;\n  width: 100%;\n}\n\n.walletconnect-modal__base .wc-logo {\n  align-items: center;\n  display: flex;\n  height: 26px;\n  margin-top: 15px;\n  padding-bottom: 15px;\n  pointer-events: auto;\n}\n\n.walletconnect-modal__base .wc-logo div {\n  background-color: #3399ff;\n  height: 21px;\n  margin-right: 5px;\n  mask-image: url(\"images/wc-logo.svg\") center no-repeat;\n  width: 32px;\n}\n\n.walletconnect-modal__base .wc-logo p {\n  color: #ffffff;\n  font-size: 20px;\n  font-weight: 600;\n  margin: 0;\n}\n\n.walletconnect-modal__base h2 {\n  color: rgba(60, 66, 82, 0.6);\n  font-size: 16px;\n  font-weight: 600;\n  letter-spacing: 0;\n  line-height: 1.1875em;\n  margin: 0 0 19px 0;\n  text-align: center;\n  width: 100%;\n}\n\n.walletconnect-modal__base__row {\n  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\n  align-items: center;\n  border-radius: 20px;\n  cursor: pointer;\n  display: flex;\n  height: 56px;\n  justify-content: space-between;\n  padding: 0 15px;\n  position: relative;\n  margin: 0px 0px 8px;\n  text-align: left;\n  transition: 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);\n  will-change: transform;\n  text-decoration: none;\n}\n\n.walletconnect-modal__base__row:hover {\n  background: rgba(60, 66, 82, 0.06);\n}\n\n.walletconnect-modal__base__row:active {\n  background: rgba(60, 66, 82, 0.06);\n  transform: scale(0.975);\n  transition: 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94);\n}\n\n.walletconnect-modal__base__row__h3 {\n  color: #25292e;\n  font-size: 20px;\n  font-weight: 700;\n  margin: 0;\n  padding-bottom: 3px;\n}\n\n.walletconnect-modal__base__row__right {\n  align-items: center;\n  display: flex;\n  justify-content: center;\n}\n\n.walletconnect-modal__base__row__right__app-icon {\n  border-radius: 8px;\n  height: 34px;\n  margin: 0 11px 2px 0;\n  width: 34px;\n  background-size: 100%;\n  box-shadow: 0 4px 12px 0 rgba(37, 41, 46, 0.25);\n}\n\n.walletconnect-modal__base__row__right__caret {\n  height: 18px;\n  opacity: 0.3;\n  transition: 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94);\n  width: 8px;\n  will-change: opacity;\n}\n\n.walletconnect-modal__base__row:hover .caret,\n.walletconnect-modal__base__row:active .caret {\n  opacity: 0.6;\n}\n\n.walletconnect-modal__mobile__toggle {\n  width: 80%;\n  display: flex;\n  margin: 0 auto;\n  position: relative;\n  overflow: hidden;\n  border-radius: 8px;\n  margin-bottom: 18px;\n  background: #d4d5d9;\n}\n\n.walletconnect-modal__single_wallet {\n  display: flex;\n  justify-content: center;\n  margin-top: 7px;\n  margin-bottom: 18px;\n}\n\n.walletconnect-modal__single_wallet a {\n  cursor: pointer;\n  color: rgb(64, 153, 255);\n  font-size: 21px;\n  font-weight: 800;\n  text-decoration: none !important;\n  margin: 0 auto;\n}\n\n.walletconnect-modal__mobile__toggle_selector {\n  width: calc(50% - 8px);\n  background: white;\n  position: absolute;\n  border-radius: 5px;\n  height: calc(100% - 8px);\n  top: 4px;\n  transition: all 0.2s ease-in-out;\n  transform: translate3d(4px, 0, 0);\n}\n\n.walletconnect-modal__mobile__toggle.right__selected .walletconnect-modal__mobile__toggle_selector {\n  transform: translate3d(calc(100% + 12px), 0, 0);\n}\n\n.walletconnect-modal__mobile__toggle a {\n  font-size: 12px;\n  width: 50%;\n  text-align: center;\n  padding: 8px;\n  margin: 0;\n  font-weight: 600;\n  z-index: 1;\n}\n\n.walletconnect-modal__footer {\n  display: flex;\n  justify-content: center;\n  margin-top: 20px;\n}\n\n@media only screen and (max-width: 768px) {\n  .walletconnect-modal__footer {\n    margin-top: 5vw;\n  }\n}\n\n.walletconnect-modal__footer a {\n  cursor: pointer;\n  color: #898d97;\n  font-size: 15px;\n  margin: 0 auto;\n}\n\n@media only screen and (max-width: 320px) {\n  .walletconnect-modal__footer a {\n    font-size: 14px;\n  }\n}\n\n.walletconnect-connect__buttons__wrapper {\n  max-height: 44vh;\n}\n\n.walletconnect-connect__buttons__wrapper__android {\n  margin: 50% 0;\n}\n\n.walletconnect-connect__buttons__wrapper__wrap {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  margin: 10px 0;\n}\n\n@media only screen and (min-width: 768px) {\n  .walletconnect-connect__buttons__wrapper__wrap {\n    margin-top: 40px;\n  }\n}\n\n.walletconnect-connect__button {\n  background-color: rgb(64, 153, 255);\n  padding: 12px;\n  border-radius: 8px;\n  text-decoration: none;\n  color: rgb(255, 255, 255);\n  font-weight: 500;\n}\n\n.walletconnect-connect__button__icon_anchor {\n  cursor: pointer;\n  display: flex;\n  justify-content: flex-start;\n  align-items: center;\n  margin: 8px;\n  width: 42px;\n  justify-self: center;\n  flex-direction: column;\n  text-decoration: none !important;\n}\n\n@media only screen and (max-width: 320px) {\n  .walletconnect-connect__button__icon_anchor {\n    margin: 4px;\n  }\n}\n\n.walletconnect-connect__button__icon {\n  border-radius: 10px;\n  height: 42px;\n  margin: 0;\n  width: 42px;\n  background-size: cover !important;\n  box-shadow: 0 4px 12px 0 rgba(37, 41, 46, 0.25);\n}\n\n.walletconnect-connect__button__text {\n  color: #424952;\n  font-size: 2.7vw;\n  text-decoration: none !important;\n  padding: 0;\n  margin-top: 1.8vw;\n  font-weight: 600;\n}\n\n@media only screen and (min-width: 768px) {\n  .walletconnect-connect__button__text {\n    font-size: 16px;\n    margin-top: 12px;\n  }\n}\n\n.walletconnect-search__input {\n  border: none;\n  background: #d4d5d9;\n  border-style: none;\n  padding: 8px 16px;\n  outline: none;\n  font-style: normal;\n  font-stretch: normal;\n  font-size: 16px;\n  font-style: normal;\n  font-stretch: normal;\n  line-height: normal;\n  letter-spacing: normal;\n  text-align: left;\n  border-radius: 8px;\n  width: calc(100% - 16px);\n  margin: 0;\n  margin-bottom: 8px;\n}\n`;", "// A type of promise-like that resolves synchronously and supports only one observer\nexport const _Pact = /*#__PURE__*/(function() {\n\tfunction _Pact() {}\n\t_Pact.prototype.then = function(onFulfilled, onRejected) {\n\t\tconst result = new _Pact();\n\t\tconst state = this.s;\n\t\tif (state) {\n\t\t\tconst callback = state & 1 ? onFulfilled : onRejected;\n\t\t\tif (callback) {\n\t\t\t\ttry {\n\t\t\t\t\t_settle(result, 1, callback(this.v));\n\t\t\t\t} catch (e) {\n\t\t\t\t\t_settle(result, 2, e);\n\t\t\t\t}\n\t\t\t\treturn result;\n\t\t\t} else {\n\t\t\t\treturn this;\n\t\t\t}\n\t\t}\n\t\tthis.o = function(_this) {\n\t\t\ttry {\n\t\t\t\tconst value = _this.v;\n\t\t\t\tif (_this.s & 1) {\n\t\t\t\t\t_settle(result, 1, onFulfilled ? onFulfilled(value) : value);\n\t\t\t\t} else if (onRejected) {\n\t\t\t\t\t_settle(result, 1, onRejected(value));\n\t\t\t\t} else {\n\t\t\t\t\t_settle(result, 2, value);\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\t_settle(result, 2, e);\n\t\t\t}\n\t\t};\n\t\treturn result;\n\t}\n\treturn _Pact;\n})();\n\n// Settles a pact synchronously\nexport function _settle(pact, state, value) {\n\tif (!pact.s) {\n\t\tif (value instanceof _Pact) {\n\t\t\tif (value.s) {\n\t\t\t\tif (state & 1) {\n\t\t\t\t\tstate = value.s;\n\t\t\t\t}\n\t\t\t\tvalue = value.v;\n\t\t\t} else {\n\t\t\t\tvalue.o = _settle.bind(null, pact, state);\n\t\t\t\treturn;\n\t\t\t}\n\t\t}\n\t\tif (value && value.then) {\n\t\t\tvalue.then(_settle.bind(null, pact, state), _settle.bind(null, pact, 2));\n\t\t\treturn;\n\t\t}\n\t\tpact.s = state;\n\t\tpact.v = value;\n\t\tconst observer = pact.o;\n\t\tif (observer) {\n\t\t\tobserver(pact);\n\t\t}\n\t}\n}\n\nexport function _isSettledPact(thenable) {\n\treturn thenable instanceof _Pact && thenable.s & 1;\n}\n\n// Converts argument to a function that always returns a Promise\nexport function _async(f) {\n\treturn function() {\n\t\tfor (var args = [], i = 0; i < arguments.length; i++) {\n\t\t\targs[i] = arguments[i];\n\t\t}\n\t\ttry {\n\t\t\treturn Promise.resolve(f.apply(this, args));\n\t\t} catch(e) {\n\t\t\treturn Promise.reject(e);\n\t\t}\n\t}\n}\n\n// Awaits on a value that may or may not be a Promise (equivalent to the await keyword in ES2015, with continuations passed explicitly)\nexport function _await(value, then, direct) {\n\tif (direct) {\n\t\treturn then ? then(value) : value;\n\t}\n\tif (!value || !value.then) {\n\t\tvalue = Promise.resolve(value);\n\t}\n\treturn then ? value.then(then) : value;\n}\n\n// Awaits on a value that may or may not be a Promise, then ignores it\nexport function _awaitIgnored(value, direct) {\n\tif (!direct) {\n\t\treturn value && value.then ? value.then(_empty) : Promise.resolve();\n\t}\n}\n\n// Proceeds after a value has resolved, or proceeds immediately if the value is not thenable\nexport function _continue(value, then) {\n\treturn value && value.then ? value.then(then) : then(value);\n}\n\n// Proceeds after a value has resolved, or proceeds immediately if the value is not thenable\nexport function _continueIgnored(value) {\n\tif (value && value.then) {\n\t\treturn value.then(_empty);\n\t}\n}\n\n// Asynchronously iterate through an object that has a length property, passing the index as the first argument to the callback (even as the length property changes)\nexport function _forTo(array, body, check) {\n\tvar i = -1, pact, reject;\n\tfunction _cycle(result) {\n\t\ttry {\n\t\t\twhile (++i < array.length && (!check || !check())) {\n\t\t\t\tresult = body(i);\n\t\t\t\tif (result && result.then) {\n\t\t\t\t\tif (_isSettledPact(result)) {\n\t\t\t\t\t\tresult = result.v;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tresult.then(_cycle, reject || (reject = _settle.bind(null, pact = new _Pact(), 2)));\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (pact) {\n\t\t\t\t_settle(pact, 1, result);\n\t\t\t} else {\n\t\t\t\tpact = result;\n\t\t\t}\n\t\t} catch (e) {\n\t\t\t_settle(pact || (pact = new _Pact()), 2, e);\n\t\t}\n\t}\n\t_cycle();\n\treturn pact;\n}\n\n// Asynchronously iterate through an object's properties (including properties inherited from the prototype)\n// Uses a snapshot of the object's properties\nexport function _forIn(target, body, check) {\n\tvar keys = [];\n\tfor (var key in target) {\n\t\tkeys.push(key);\n\t}\n\treturn _forTo(keys, function(i) { return body(keys[i]); }, check);\n}\n\n// Asynchronously iterate through an object's own properties (excluding properties inherited from the prototype)\n// Uses a snapshot of the object's properties\nexport function _forOwn(target, body, check) {\n\tvar keys = [];\n\tfor (var key in target) {\n\t\tif (Object.prototype.hasOwnProperty.call(target, key)) {\n\t\t\tkeys.push(key);\n\t\t}\n\t}\n\treturn _forTo(keys, function(i) { return body(keys[i]); }, check);\n}\n\nexport const _iteratorSymbol = /*#__PURE__*/ typeof Symbol !== \"undefined\" ? (Symbol.iterator || (Symbol.iterator = Symbol(\"Symbol.iterator\"))) : \"@@iterator\";\n\n// Asynchronously iterate through an object's values\n// Uses for...of if the runtime supports it, otherwise iterates until length on a copy\nexport function _forOf(target, body, check) {\n\tif (typeof target[_iteratorSymbol] === \"function\") {\n\t\tvar iterator = target[_iteratorSymbol](), step, pact, reject;\n\t\tfunction _cycle(result) {\n\t\t\ttry {\n\t\t\t\twhile (!(step = iterator.next()).done && (!check || !check())) {\n\t\t\t\t\tresult = body(step.value);\n\t\t\t\t\tif (result && result.then) {\n\t\t\t\t\t\tif (_isSettledPact(result)) {\n\t\t\t\t\t\t\tresult = result.v;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tresult.then(_cycle, reject || (reject = _settle.bind(null, pact = new _Pact(), 2)));\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (pact) {\n\t\t\t\t\t_settle(pact, 1, result);\n\t\t\t\t} else {\n\t\t\t\t\tpact = result;\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\t_settle(pact || (pact = new _Pact()), 2, e);\n\t\t\t}\n\t\t}\n\t\t_cycle();\n\t\tif (iterator.return) {\n\t\t\tvar _fixup = function(value) {\n\t\t\t\ttry {\n\t\t\t\t\tif (!step.done) {\n\t\t\t\t\t\titerator.return();\n\t\t\t\t\t}\n\t\t\t\t} catch(e) {\n\t\t\t\t}\n\t\t\t\treturn value;\n\t\t\t}\n\t\t\tif (pact && pact.then) {\n\t\t\t\treturn pact.then(_fixup, function(e) {\n\t\t\t\t\tthrow _fixup(e);\n\t\t\t\t});\n\t\t\t}\n\t\t\t_fixup();\n\t\t}\n\t\treturn pact;\n\t}\n\t// No support for Symbol.iterator\n\tif (!(\"length\" in target)) {\n\t\tthrow new TypeError(\"Object is not iterable\");\n\t}\n\t// Handle live collections properly\n\tvar values = [];\n\tfor (var i = 0; i < target.length; i++) {\n\t\tvalues.push(target[i]);\n\t}\n\treturn _forTo(values, function(i) { return body(values[i]); }, check);\n}\n\nexport const _asyncIteratorSymbol = /*#__PURE__*/ typeof Symbol !== \"undefined\" ? (Symbol.asyncIterator || (Symbol.asyncIterator = Symbol(\"Symbol.asyncIterator\"))) : \"@@asyncIterator\";\n\n// Asynchronously iterate on a value using it's async iterator if present, or its synchronous iterator if missing\nexport function _forAwaitOf(target, body, check) {\n\tif (typeof target[_asyncIteratorSymbol] === \"function\") {\n\t\tvar pact = new _Pact();\n\t\tvar iterator = target[_asyncIteratorSymbol]();\n\t\titerator.next().then(_resumeAfterNext).then(void 0, _reject);\n\t\treturn pact;\n\t\tfunction _resumeAfterBody(result) {\n\t\t\tif (check && check()) {\n\t\t\t\treturn _settle(pact, 1, iterator.return ? iterator.return().then(function() { return result; }) : result);\n\t\t\t}\n\t\t\titerator.next().then(_resumeAfterNext).then(void 0, _reject);\n\t\t}\n\t\tfunction _resumeAfterNext(step) {\n\t\t\tif (step.done) {\n\t\t\t\t_settle(pact, 1);\n\t\t\t} else {\n\t\t\t\tPromise.resolve(body(step.value)).then(_resumeAfterBody).then(void 0, _reject);\n\t\t\t}\n\t\t}\n\t\tfunction _reject(error) {\n\t\t\t_settle(pact, 2, iterator.return ? iterator.return().then(function() { return error; }) : error);\n\t\t}\n\t}\n\treturn Promise.resolve(_forOf(target, function(value) { return Promise.resolve(value).then(body); }, check));\n}\n\n// Asynchronously implement a generic for loop\nexport function _for(test, update, body) {\n\tvar stage;\n\tfor (;;) {\n\t\tvar shouldContinue = test();\n\t\tif (_isSettledPact(shouldContinue)) {\n\t\t\tshouldContinue = shouldContinue.v;\n\t\t}\n\t\tif (!shouldContinue) {\n\t\t\treturn result;\n\t\t}\n\t\tif (shouldContinue.then) {\n\t\t\tstage = 0;\n\t\t\tbreak;\n\t\t}\n\t\tvar result = body();\n\t\tif (result && result.then) {\n\t\t\tif (_isSettledPact(result)) {\n\t\t\t\tresult = result.s;\n\t\t\t} else {\n\t\t\t\tstage = 1;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t\tif (update) {\n\t\t\tvar updateValue = update();\n\t\t\tif (updateValue && updateValue.then && !_isSettledPact(updateValue)) {\n\t\t\t\tstage = 2;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t}\n\tvar pact = new _Pact();\n\tvar reject = _settle.bind(null, pact, 2);\n\t(stage === 0 ? shouldContinue.then(_resumeAfterTest) : stage === 1 ? result.then(_resumeAfterBody) : updateValue.then(_resumeAfterUpdate)).then(void 0, reject);\n\treturn pact;\n\tfunction _resumeAfterBody(value) {\n\t\tresult = value;\n\t\tdo {\n\t\t\tif (update) {\n\t\t\t\tupdateValue = update();\n\t\t\t\tif (updateValue && updateValue.then && !_isSettledPact(updateValue)) {\n\t\t\t\t\tupdateValue.then(_resumeAfterUpdate).then(void 0, reject);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\t\t\tshouldContinue = test();\n\t\t\tif (!shouldContinue || (_isSettledPact(shouldContinue) && !shouldContinue.v)) {\n\t\t\t\t_settle(pact, 1, result);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (shouldContinue.then) {\n\t\t\t\tshouldContinue.then(_resumeAfterTest).then(void 0, reject);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tresult = body();\n\t\t\tif (_isSettledPact(result)) {\n\t\t\t\tresult = result.v;\n\t\t\t}\n\t\t} while (!result || !result.then);\n\t\tresult.then(_resumeAfterBody).then(void 0, reject);\n\t}\n\tfunction _resumeAfterTest(shouldContinue) {\n\t\tif (shouldContinue) {\n\t\t\tresult = body();\n\t\t\tif (result && result.then) {\n\t\t\t\tresult.then(_resumeAfterBody).then(void 0, reject);\n\t\t\t} else {\n\t\t\t\t_resumeAfterBody(result);\n\t\t\t}\n\t\t} else {\n\t\t\t_settle(pact, 1, result);\n\t\t}\n\t}\n\tfunction _resumeAfterUpdate() {\n\t\tif (shouldContinue = test()) {\n\t\t\tif (shouldContinue.then) {\n\t\t\t\tshouldContinue.then(_resumeAfterTest).then(void 0, reject);\n\t\t\t} else {\n\t\t\t\t_resumeAfterTest(shouldContinue);\n\t\t\t}\n\t\t} else {\n\t\t\t_settle(pact, 1, result);\n\t\t}\n\t}\n}\n\n// Asynchronously implement a do ... while loop\nexport function _do(body, test) {\n\tvar awaitBody;\n\tdo {\n\t\tvar result = body();\n\t\tif (result && result.then) {\n\t\t\tif (_isSettledPact(result)) {\n\t\t\t\tresult = result.v;\n\t\t\t} else {\n\t\t\t\tawaitBody = true;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t\tvar shouldContinue = test();\n\t\tif (_isSettledPact(shouldContinue)) {\n\t\t\tshouldContinue = shouldContinue.v;\n\t\t}\n\t\tif (!shouldContinue) {\n\t\t\treturn result;\n\t\t}\n\t} while (!shouldContinue.then);\n\tconst pact = new _Pact();\n\tconst reject = _settle.bind(null, pact, 2);\n\t(awaitBody ? result.then(_resumeAfterBody) : shouldContinue.then(_resumeAfterTest)).then(void 0, reject);\n\treturn pact;\n\tfunction _resumeAfterBody(value) {\n\t\tresult = value;\n\t\tfor (;;) {\n\t\t\tshouldContinue = test();\n\t\t\tif (_isSettledPact(shouldContinue)) {\n\t\t\t\tshouldContinue = shouldContinue.v;\n\t\t\t}\n\t\t\tif (!shouldContinue) {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\tif (shouldContinue.then) {\n\t\t\t\tshouldContinue.then(_resumeAfterTest).then(void 0, reject);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tresult = body();\n\t\t\tif (result && result.then) {\n\t\t\t\tif (_isSettledPact(result)) {\n\t\t\t\t\tresult = result.v;\n\t\t\t\t} else {\n\t\t\t\t\tresult.then(_resumeAfterBody).then(void 0, reject);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t_settle(pact, 1, result);\n\t}\n\tfunction _resumeAfterTest(shouldContinue) {\n\t\tif (shouldContinue) {\n\t\t\tdo {\n\t\t\t\tresult = body();\n\t\t\t\tif (result && result.then) {\n\t\t\t\t\tif (_isSettledPact(result)) {\n\t\t\t\t\t\tresult = result.v;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tresult.then(_resumeAfterBody).then(void 0, reject);\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tshouldContinue = test();\n\t\t\t\tif (_isSettledPact(shouldContinue)) {\n\t\t\t\t\tshouldContinue = shouldContinue.v;\n\t\t\t\t}\n\t\t\t\tif (!shouldContinue) {\n\t\t\t\t\t_settle(pact, 1, result);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t} while (!shouldContinue.then);\n\t\t\tshouldContinue.then(_resumeAfterTest).then(void 0, reject);\n\t\t} else {\n\t\t\t_settle(pact, 1, result);\n\t\t}\n\t}\n}\n\n// Asynchronously implement a switch statement\nexport function _switch(discriminant, cases) {\n\tvar dispatchIndex = -1;\n\tvar awaitBody;\n\touter: {\n\t\tfor (var i = 0; i < cases.length; i++) {\n\t\t\tvar test = cases[i][0];\n\t\t\tif (test) {\n\t\t\t\tvar testValue = test();\n\t\t\t\tif (testValue && testValue.then) {\n\t\t\t\t\tbreak outer;\n\t\t\t\t}\n\t\t\t\tif (testValue === discriminant) {\n\t\t\t\t\tdispatchIndex = i;\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t// Found the default case, set it as the pending dispatch case\n\t\t\t\tdispatchIndex = i;\n\t\t\t}\n\t\t}\n\t\tif (dispatchIndex !== -1) {\n\t\t\tdo {\n\t\t\t\tvar body = cases[dispatchIndex][1];\n\t\t\t\twhile (!body) {\n\t\t\t\t\tdispatchIndex++;\n\t\t\t\t\tbody = cases[dispatchIndex][1];\n\t\t\t\t}\n\t\t\t\tvar result = body();\n\t\t\t\tif (result && result.then) {\n\t\t\t\t\tawaitBody = true;\n\t\t\t\t\tbreak outer;\n\t\t\t\t}\n\t\t\t\tvar fallthroughCheck = cases[dispatchIndex][2];\n\t\t\t\tdispatchIndex++;\n\t\t\t} while (fallthroughCheck && !fallthroughCheck());\n\t\t\treturn result;\n\t\t}\n\t}\n\tconst pact = new _Pact();\n\tconst reject = _settle.bind(null, pact, 2);\n\t(awaitBody ? result.then(_resumeAfterBody) : testValue.then(_resumeAfterTest)).then(void 0, reject);\n\treturn pact;\n\tfunction _resumeAfterTest(value) {\n\t\tfor (;;) {\n\t\t\tif (value === discriminant) {\n\t\t\t\tdispatchIndex = i;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\tif (++i === cases.length) {\n\t\t\t\tif (dispatchIndex !== -1) {\n\t\t\t\t\tbreak;\n\t\t\t\t} else {\n\t\t\t\t\t_settle(pact, 1, result);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\t\t\ttest = cases[i][0];\n\t\t\tif (test) {\n\t\t\t\tvalue = test();\n\t\t\t\tif (value && value.then) {\n\t\t\t\t\tvalue.then(_resumeAfterTest).then(void 0, reject);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tdispatchIndex = i;\n\t\t\t}\n\t\t}\n\t\tdo {\n\t\t\tvar body = cases[dispatchIndex][1];\n\t\t\twhile (!body) {\n\t\t\t\tdispatchIndex++;\n\t\t\t\tbody = cases[dispatchIndex][1];\n\t\t\t}\n\t\t\tvar result = body();\n\t\t\tif (result && result.then) {\n\t\t\t\tresult.then(_resumeAfterBody).then(void 0, reject);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tvar fallthroughCheck = cases[dispatchIndex][2];\n\t\t\tdispatchIndex++;\n\t\t} while (fallthroughCheck && !fallthroughCheck());\n\t\t_settle(pact, 1, result);\n\t}\n\tfunction _resumeAfterBody(result) {\n\t\tfor (;;) {\n\t\t\tvar fallthroughCheck = cases[dispatchIndex][2];\n\t\t\tif (!fallthroughCheck || fallthroughCheck()) {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\tdispatchIndex++;\n\t\t\tvar body = cases[dispatchIndex][1];\n\t\t\twhile (!body) {\n\t\t\t\tdispatchIndex++;\n\t\t\t\tbody = cases[dispatchIndex][1];\n\t\t\t}\n\t\t\tresult = body();\n\t\t\tif (result && result.then) {\n\t\t\t\tresult.then(_resumeAfterBody).then(void 0, reject);\n\t\t\t\treturn;\n\t\t\t}\n\t\t}\n\t\t_settle(pact, 1, result);\n\t}\n}\n\n// Asynchronously call a function and pass the result to explicitly passed continuations\nexport function _call(body, then, direct) {\n\tif (direct) {\n\t\treturn then ? then(body()) : body();\n\t}\n\ttry {\n\t\tvar result = Promise.resolve(body());\n\t\treturn then ? result.then(then) : result;\n\t} catch (e) {\n\t\treturn Promise.reject(e);\n\t}\n}\n\n// Asynchronously call a function and swallow the result\nexport function _callIgnored(body, direct) {\n\treturn _call(body, _empty, direct);\n}\n\n// Asynchronously call a function and pass the result to explicitly passed continuations\nexport function _invoke(body, then) {\n\tvar result = body();\n\tif (result && result.then) {\n\t\treturn result.then(then);\n\t}\n\treturn then(result);\n}\n\n// Asynchronously call a function and swallow the result\nexport function _invokeIgnored(body) {\n\tvar result = body();\n\tif (result && result.then) {\n\t\treturn result.then(_empty);\n\t}\n}\n\n// Asynchronously call a function and send errors to recovery continuation\nexport function _catch(body, recover) {\n\ttry {\n\t\tvar result = body();\n\t} catch(e) {\n\t\treturn recover(e);\n\t}\n\tif (result && result.then) {\n\t\treturn result.then(void 0, recover);\n\t}\n\treturn result;\n}\n\n// Asynchronously await a promise and pass the result to a finally continuation\nexport function _finallyRethrows(body, finalizer) {\n\ttry {\n\t\tvar result = body();\n\t} catch (e) {\n\t\treturn finalizer(true, e);\n\t}\n\tif (result && result.then) {\n\t\treturn result.then(finalizer.bind(null, false), finalizer.bind(null, true));\n\t}\n\treturn finalizer(false, result);\n}\n\n// Asynchronously await a promise and invoke a finally continuation that always overrides the result\nexport function _finally(body, finalizer) {\n\ttry {\n\t\tvar result = body();\n\t} catch (e) {\n\t\treturn finalizer();\n\t}\n\tif (result && result.then) {\n\t\treturn result.then(finalizer, finalizer);\n\t}\n\treturn finalizer();\n}\n\n// Rethrow or return a value from a finally continuation\nexport function _rethrow(thrown, value) {\n\tif (thrown)\n\t\tthrow value;\n\treturn value;\n}\n\n// Empty function to implement break and other control flow that ignores asynchronous results\nexport function _empty() {\n}\n\n// Sentinel value for early returns in generators \nexport const _earlyReturn = /*#__PURE__*/ {};\n\n// Asynchronously call a function and send errors to recovery continuation, skipping early returns\nexport function _catchInGenerator(body, recover) {\n\treturn _catch(body, function(e) {\n\t\tif (e === _earlyReturn) {\n\t\t\tthrow e;\n\t\t}\n\t\treturn recover(e);\n\t});\n}\n\n// Asynchronous generator class; accepts the entrypoint of the generator, to which it passes itself when the generator should start\nexport const _AsyncGenerator = /*#__PURE__*/(function() {\n\tfunction _AsyncGenerator(entry) {\n\t\tthis._entry = entry;\n\t\tthis._pact = null;\n\t\tthis._resolve = null;\n\t\tthis._return = null;\n\t\tthis._promise = null;\n\t}\n\n\tfunction _wrapReturnedValue(value) {\n\t\treturn { value: value, done: true };\n\t}\n\tfunction _wrapYieldedValue(value) {\n\t\treturn { value: value, done: false };\n\t}\n\n\t_AsyncGenerator.prototype._yield = function(value) {\n\t\t// Yield the value to the pending next call\n\t\tthis._resolve(value && value.then ? value.then(_wrapYieldedValue) : _wrapYieldedValue(value));\n\t\t// Return a pact for an upcoming next/return/throw call\n\t\treturn this._pact = new _Pact();\n\t};\n\t_AsyncGenerator.prototype.next = function(value) {\n\t\t// Advance the generator, starting it if it has yet to be started\n\t\tconst _this = this;\n\t\treturn _this._promise = new Promise(function (resolve) {\n\t\t\tconst _pact = _this._pact;\n\t\t\tif (_pact === null) {\n\t\t\t\tconst _entry = _this._entry;\n\t\t\t\tif (_entry === null) {\n\t\t\t\t\t// Generator is started, but not awaiting a yield expression\n\t\t\t\t\t// Abandon the next call!\n\t\t\t\t\treturn resolve(_this._promise);\n\t\t\t\t}\n\t\t\t\t// Start the generator\n\t\t\t\t_this._entry = null;\n\t\t\t\t_this._resolve = resolve;\n\t\t\t\tfunction returnValue(value) {\n\t\t\t\t\t_this._resolve(value && value.then ? value.then(_wrapReturnedValue) : _wrapReturnedValue(value));\n\t\t\t\t\t_this._pact = null;\n\t\t\t\t\t_this._resolve = null;\n\t\t\t\t}\n\t\t\t\tvar result = _entry(_this);\n\t\t\t\tif (result && result.then) {\n\t\t\t\t\tresult.then(returnValue, function(error) {\n\t\t\t\t\t\tif (error === _earlyReturn) {\n\t\t\t\t\t\t\treturnValue(_this._return);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconst pact = new _Pact();\n\t\t\t\t\t\t\t_this._resolve(pact);\n\t\t\t\t\t\t\t_this._pact = null;\n\t\t\t\t\t\t\t_this._resolve = null;\n\t\t\t\t\t\t\t_resolve(pact, 2, error);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\treturnValue(result);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t// Generator is started and a yield expression is pending, settle it\n\t\t\t\t_this._pact = null;\n\t\t\t\t_this._resolve = resolve;\n\t\t\t\t_settle(_pact, 1, value);\n\t\t\t}\n\t\t});\n\t};\n\t_AsyncGenerator.prototype.return = function(value) {\n\t\t// Early return from the generator if started, otherwise abandons the generator\n\t\tconst _this = this;\n\t\treturn _this._promise = new Promise(function (resolve) {\n\t\t\tconst _pact = _this._pact;\n\t\t\tif (_pact === null) {\n\t\t\t\tif (_this._entry === null) {\n\t\t\t\t\t// Generator is started, but not awaiting a yield expression\n\t\t\t\t\t// Abandon the return call!\n\t\t\t\t\treturn resolve(_this._promise);\n\t\t\t\t}\n\t\t\t\t// Generator is not started, abandon it and return the specified value\n\t\t\t\t_this._entry = null;\n\t\t\t\treturn resolve(value && value.then ? value.then(_wrapReturnedValue) : _wrapReturnedValue(value));\n\t\t\t}\n\t\t\t// Settle the yield expression with a rejected \"early return\" value\n\t\t\t_this._return = value;\n\t\t\t_this._resolve = resolve;\n\t\t\t_this._pact = null;\n\t\t\t_settle(_pact, 2, _earlyReturn);\n\t\t});\n\t};\n\t_AsyncGenerator.prototype.throw = function(error) {\n\t\t// Inject an exception into the pending yield expression\n\t\tconst _this = this;\n\t\treturn _this._promise = new Promise(function (resolve, reject) {\n\t\t\tconst _pact = _this._pact;\n\t\t\tif (_pact === null) {\n\t\t\t\tif (_this._entry === null) {\n\t\t\t\t\t// Generator is started, but not awaiting a yield expression\n\t\t\t\t\t// Abandon the throw call!\n\t\t\t\t\treturn resolve(_this._promise);\n\t\t\t\t}\n\t\t\t\t// Generator is not started, abandon it and return a rejected Promise containing the error\n\t\t\t\t_this._entry = null;\n\t\t\t\treturn reject(error);\n\t\t\t}\n\t\t\t// Settle the yield expression with the value as a rejection\n\t\t\t_this._resolve = resolve;\n\t\t\t_this._pact = null;\n\t\t\t_settle(_pact, 2, error);\n\t\t});\n\t};\n\n\t_AsyncGenerator.prototype[_asyncIteratorSymbol] = function() {\n\t\treturn this;\n\t};\n\t\n\treturn _AsyncGenerator;\n})();\n", "export const WALLETCONNECT_LOGO_SVG_URL = `data:image/svg+xml,%3Csvg height='185' viewBox='0 0 300 185' width='300' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='m61.4385429 36.2562612c48.9112241-47.8881663 128.2119871-47.8881663 177.1232091 0l5.886545 5.7634174c2.445561 2.3944081 2.445561 6.2765112 0 8.6709204l-20.136695 19.715503c-1.222781 1.1972051-3.2053 1.1972051-4.428081 0l-8.100584-7.9311479c-34.121692-33.4079817-89.443886-33.4079817-123.5655788 0l-8.6750562 8.4936051c-1.2227816 1.1972041-3.205301 1.1972041-4.4280806 0l-20.1366949-19.7155031c-2.4455612-2.3944092-2.4455612-6.2765122 0-8.6709204zm218.7677961 40.7737449 17.921697 17.546897c2.445549 2.3943969 2.445563 6.2764769.000031 8.6708899l-80.810171 79.121134c-2.445544 2.394426-6.410582 2.394453-8.85616.000062-.00001-.00001-.000022-.000022-.000032-.000032l-57.354143-56.154572c-.61139-.598602-1.60265-.598602-2.21404 0-.000004.000004-.000007.000008-.000011.000011l-57.3529212 56.154531c-2.4455368 2.394432-6.4105755 2.394472-8.8561612.000087-.0000143-.000014-.0000296-.000028-.0000449-.000044l-80.81241943-79.122185c-2.44556021-2.394408-2.44556021-6.2765115 0-8.6709197l17.92172963-17.5468673c2.4455602-2.3944082 6.4105989-2.3944082 8.8561602 0l57.3549775 56.155357c.6113908.598602 1.602649.598602 2.2140398 0 .0000092-.000009.0000174-.000017.0000265-.000024l57.3521031-56.155333c2.445505-2.3944633 6.410544-2.3945531 8.856161-.0002.000034.0000336.000068.0000673.000101.000101l57.354902 56.155432c.61139.598601 1.60265.598601 2.21404 0l57.353975-56.1543249c2.445561-2.3944092 6.410599-2.3944092 8.85616 0z' fill='%233b99fc'/%3E%3C/svg%3E`;", "export const WALLETCONNECT_HEADER_TEXT = \"WalletConnect\";\n\nexport const ANIMATION_DURATION = 300;\nexport const DEFAULT_BUTTON_COLOR = \"rgb(64, 153, 255)\";\n\nexport const WALLETCONNECT_WRAPPER_ID = \"walletconnect-wrapper\";\nexport const WALLETCONNECT_STYLE_ID = \"walletconnect-style-sheet\";\nexport const WALLETCONNECT_MODAL_ID = \"walletconnect-qrcode-modal\";\nexport const WALLETCONNECT_CLOSE_BUTTON_ID = \"walletconnect-qrcode-close\";\nexport const WALLETCONNECT_CTA_TEXT_ID = \"walletconnect-qrcode-text\";\nexport const WALLETCONNECT_CONNECT_BUTTON_ID = \"walletconnect-connect-button\";\n", "// eslint-disable-next-line @typescript-eslint/no-unused-vars\nimport * as React from \"react\";\n\nimport { WALLETCONNECT_LOGO_SVG_URL } from \"../assets/logo\";\nimport { WALLETCONNECT_HEADER_TEXT, WALLETCONNECT_CLOSE_BUTTON_ID } from \"../constants\";\n\ninterface HeaderProps {\n  onClose: any;\n}\n\nfunction Header(props: HeaderProps) {\n  return (\n    <div className=\"walletconnect-modal__header\">\n      <img src={WALLETCONNECT_LOGO_SVG_URL} className=\"walletconnect-modal__headerLogo\" />\n      <p>{WALLETCONNECT_HEADER_TEXT}</p>\n      <div className=\"walletconnect-modal__close__wrapper\" onClick={props.onClose}>\n        <div id={WALLETCONNECT_CLOSE_BUTTON_ID} className=\"walletconnect-modal__close__icon\">\n          <div className=\"walletconnect-modal__close__line1\"></div>\n          <div className=\"walletconnect-modal__close__line2\"></div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default Header;\n", "import * as React from \"react\";\n\nimport { WALLETCONNECT_CONNECT_BUTTON_ID } from \"../constants\";\n\ninterface ConnectButtonProps {\n  name: string;\n  color: string;\n  href: string;\n  onClick: (event: React.MouseEvent<HTMLAnchorElement>) => void;\n}\n\nfunction ConnectButton(props: ConnectButtonProps) {\n  return (\n    <a\n      className=\"walletconnect-connect__button\"\n      href={props.href}\n      id={`${WALLETCONNECT_CONNECT_BUTTON_ID}-${props.name}`}\n      onClick={props.onClick}\n      rel=\"noopener noreferrer\"\n      style={{ backgroundColor: props.color }}\n      target=\"_blank\"\n    >\n      {props.name}\n    </a>\n  );\n}\n\nexport default ConnectButton;\n", "export const CARET_SVG_URL = `data:image/svg+xml,%3Csvg fill='none' height='18' viewBox='0 0 8 18' width='8' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath clip-rule='evenodd' d='m.586301.213898c-.435947.33907-.5144813.967342-.175411 1.403292l4.87831 6.27212c.28087.36111.28087.86677 0 1.22788l-4.878311 6.27211c-.33907.436-.260536 1.0642.175412 1.4033.435949.3391 1.064219.2605 1.403289-.1754l4.87832-6.2721c.84259-1.08336.84259-2.60034 0-3.68367l-4.87832-6.27212c-.33907-.4359474-.96734-.514482-1.403289-.175412z' fill='%233c4252' fill-rule='evenodd'/%3E%3C/svg%3E`;", "import * as React from \"react\";\nimport { CARET_SVG_URL } from \"../assets/caret\";\n\ninterface WalletButtonProps {\n  color: string;\n  name: string;\n  logo: string;\n  href: string;\n  onClick: (event: React.MouseEvent<HTMLAnchorElement>) => void;\n}\n\nfunction WalletButton(props: WalletButtonProps) {\n  const { color, href, name, logo, onClick } = props;\n  return (\n    <a\n      className=\"walletconnect-modal__base__row\"\n      href={href}\n      onClick={onClick}\n      rel=\"noopener noreferrer\"\n      target=\"_blank\"\n    >\n      <h3 className={\"walletconnect-modal__base__row__h3\"}>{name}</h3>\n      <div className=\"walletconnect-modal__base__row__right\">\n        <div\n          className={`walletconnect-modal__base__row__right__app-icon`}\n          style={{ background: `url('${logo}') ${color}`, backgroundSize: \"100%\" }}\n        ></div>\n        <img src={CARET_SVG_URL} className=\"walletconnect-modal__base__row__right__caret\" />\n      </div>\n    </a>\n  );\n}\n\nexport default WalletButton;\n", "import * as React from \"react\";\n\ninterface WalletIconProps {\n  color: string;\n  logo: string;\n  href: string;\n  name: string;\n  onClick: (event: React.MouseEvent<HTMLAnchorElement>) => void;\n}\n\nfunction WalletIcon(props: WalletIconProps) {\n  const { color, href, name, logo, onClick } = props;\n  const fontSize = window.innerWidth < 768 ? `${name.length > 8 ? 2.5 : 2.7}vw` : \"inherit\";\n  return (\n    <a\n      className=\"walletconnect-connect__button__icon_anchor\"\n      href={href}\n      onClick={onClick}\n      rel=\"noopener noreferrer\"\n      target=\"_blank\"\n    >\n      <div\n        className=\"walletconnect-connect__button__icon\"\n        style={{ background: `url('${logo}') ${color}`, backgroundSize: \"100%\" }}\n      ></div>\n      <div style={{ fontSize }} className={\"walletconnect-connect__button__text\"}>\n        {name}\n      </div>\n    </a>\n  );\n}\n\nexport default WalletIcon;\n", "import * as React from \"react\";\nimport { IMobileRegistryEntry, IQRCodeModalOptions } from \"@walletconnect/types\";\nimport { isAndroid, formatIOSMobile, saveMobileLinkInfo } from \"@walletconnect/browser-utils\";\n\nimport { DEFAULT_BUTTON_COLOR, WALLETCONNECT_CTA_TEXT_ID } from \"../constants\";\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nimport ConnectButton from \"./ConnectButton\";\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nimport WalletButton from \"./WalletButton\";\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nimport WalletIcon from \"./WalletIcon\";\nimport { TextMap } from \"../types\";\n\ninterface LinkDisplayProps {\n  mobile: boolean;\n  text: TextMap;\n  uri: string;\n  qrcodeModalOptions?: IQRCodeModalOptions;\n  links: IMobileRegistryEntry[];\n  errorMessage: string;\n}\n\nconst GRID_MIN_COUNT = 5;\nconst LINKS_PER_PAGE = 12;\n\nfunction LinkDisplay(props: LinkDisplayProps) {\n  const android = isAndroid();\n  const [input, setInput] = React.useState(\"\");\n  const [filter, setFilter] = React.useState(\"\");\n  const [page, setPage] = React.useState(1);\n  const links = filter\n    ? props.links.filter(link => link.name.toLowerCase().includes(filter.toLowerCase()))\n    : props.links;\n  const errorMessage = props.errorMessage;\n  const grid = filter || links.length > GRID_MIN_COUNT;\n  const pages = Math.ceil(links.length / LINKS_PER_PAGE);\n  const range = [(page - 1) * LINKS_PER_PAGE + 1, page * LINKS_PER_PAGE];\n  const pageLinks = links.length\n    ? links.filter((_, index) => index + 1 >= range[0] && index + 1 <= range[1])\n    : [];\n  const hasPaging = !!(!android && pages > 1);\n  let filterTimeout: any = undefined;\n  function handleInput(e) {\n    setInput(e.target.value);\n    clearTimeout(filterTimeout);\n    if (e.target.value) {\n      filterTimeout = setTimeout(() => {\n        setFilter(e.target.value);\n        setPage(1);\n      }, 1000);\n    } else {\n      setInput(\"\");\n      setFilter(\"\");\n      setPage(1);\n    }\n  }\n\n  return (\n    <div>\n      <p id={WALLETCONNECT_CTA_TEXT_ID} className=\"walletconnect-qrcode__text\">\n        {android ? props.text.connect_mobile_wallet : props.text.choose_preferred_wallet}\n      </p>\n      {!android && (\n        <input\n          className={`walletconnect-search__input`}\n          placeholder=\"Search\"\n          value={input}\n          onChange={handleInput}\n        />\n      )}\n      <div\n        className={`walletconnect-connect__buttons__wrapper${\n          android ? \"__android\" : grid && links.length ? \"__wrap\" : \"\"\n        }`}\n      >\n        {!android ? (\n          pageLinks.length ? (\n            pageLinks.map((entry: IMobileRegistryEntry) => {\n              const { color, name, shortName, logo } = entry;\n              const href = formatIOSMobile(props.uri, entry);\n              const handleClickIOS = React.useCallback(() => {\n                saveMobileLinkInfo({\n                  name,\n                  href,\n                });\n              }, [pageLinks]);\n              return !grid ? (\n                <WalletButton\n                  color={color}\n                  href={href}\n                  name={name}\n                  logo={logo}\n                  onClick={handleClickIOS}\n                />\n              ) : (\n                <WalletIcon\n                  color={color}\n                  href={href}\n                  name={shortName || name}\n                  logo={logo}\n                  onClick={handleClickIOS}\n                />\n              );\n            })\n          ) : (\n            <>\n              <p>\n                {errorMessage.length\n                  ? props.errorMessage\n                  : !!props.links.length && !links.length\n                  ? props.text.no_wallets_found\n                  : props.text.loading}\n              </p>\n            </>\n          )\n        ) : (\n          <ConnectButton\n            name={props.text.connect}\n            color={DEFAULT_BUTTON_COLOR}\n            href={props.uri}\n            onClick={React.useCallback(() => {\n              saveMobileLinkInfo({\n                name: \"Unknown\",\n                href: props.uri,\n              });\n            }, [])}\n          />\n        )}\n      </div>\n      {hasPaging && (\n        <div className=\"walletconnect-modal__footer\">\n          {Array(pages)\n            .fill(0)\n            .map((_, index) => {\n              const pageNumber = index + 1;\n              const selected = page === pageNumber;\n              return (\n                <a\n                  style={{ margin: \"auto 10px\", fontWeight: selected ? \"bold\" : \"normal\" }}\n                  onClick={() => setPage(pageNumber)}\n                >\n                  {pageNumber}\n                </a>\n              );\n            })}\n        </div>\n      )}\n    </div>\n  );\n}\n\nexport default LinkDisplay;\n", "// eslint-disable-next-line @typescript-eslint/no-unused-vars\nimport * as React from \"react\";\n\ninterface NotificationProps {\n  message: string;\n}\n\nfunction Notification(props: NotificationProps) {\n  const show = !!props.message.trim();\n  return (\n    <div className={`walletconnect-qrcode__notification${show ? \" notification__show\" : \"\"}`}>\n      {props.message}\n    </div>\n  );\n}\n\nexport default Notification;\n", "// eslint-disable-next-line @typescript-eslint/no-unused-vars\nimport * as React from \"react\";\nimport QRCode from \"qrcode\";\nimport copy from \"copy-to-clipboard\";\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nimport Notification from \"./Notification\";\n\nimport { WALLETCONNECT_CTA_TEXT_ID } from \"../constants\";\nimport { TextMap } from \"../types\";\n\nasync function formatQRCodeImage(data: string) {\n  let result = \"\";\n  const dataString = await QRCode.toString(data, { margin: 0, type: \"svg\" });\n  if (typeof dataString === \"string\") {\n    result = dataString.replace(\"<svg\", `<svg class=\"walletconnect-qrcode__image\"`);\n  }\n  return result;\n}\n\ninterface QRCodeDisplayProps {\n  text: TextMap;\n  uri: string;\n}\n\nfunction QRCodeDisplay(props: QRCodeDisplayProps) {\n  const [notification, setNotification] = React.useState(\"\");\n  const [svg, setSvg] = React.useState(\"\");\n\n  React.useEffect(() => {\n    (async () => {\n      setSvg(await formatQRCodeImage(props.uri));\n    })();\n  }, []);\n\n  const copyToClipboard = () => {\n    const success = copy(props.uri);\n    if (success) {\n      setNotification(props.text.copied_to_clipboard);\n      setInterval(() => setNotification(\"\"), 1200);\n    } else {\n      setNotification(\"Error\");\n      setInterval(() => setNotification(\"\"), 1200);\n    }\n  };\n\n  return (\n    <div>\n      <p id={WALLETCONNECT_CTA_TEXT_ID} className=\"walletconnect-qrcode__text\">\n        {props.text.scan_qrcode_with_wallet}\n      </p>\n      <div dangerouslySetInnerHTML={{ __html: svg }}></div>\n      <div className=\"walletconnect-modal__footer\">\n        <a onClick={copyToClipboard}>{props.text.copy_to_clipboard}</a>\n      </div>\n      <Notification message={notification} />\n    </div>\n  );\n}\n\nexport default QRCodeDisplay;\n", "import * as React from \"react\";\nimport {\n  IMobileRegistryEntry,\n  IQRCodeModalOptions,\n  IAppRegistry,\n  IMobileLinkInfo,\n} from \"@walletconnect/types\";\nimport {\n  isMobile,\n  isAndroid,\n  formatIOSMobile,\n  saveMobileLinkInfo,\n  getMobileLinkRegistry,\n  getWalletRegistryUrl,\n  formatMobileRegistry,\n} from \"@walletconnect/browser-utils\";\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nimport Header from \"./Header\";\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nimport LinkDisplay from \"./LinkDisplay\";\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nimport QRCodeDisplay from \"./QRCodeDisplay\";\n\nimport { WALLETCONNECT_MODAL_ID } from \"../constants\";\nimport { TextMap } from \"../types\";\n\ninterface ModalProps {\n  text: TextMap;\n  uri: string;\n  onClose: any;\n  qrcodeModalOptions?: IQRCodeModalOptions;\n}\n\nfunction Modal(props: ModalProps) {\n  const android = isAndroid();\n  const mobile = isMobile();\n\n  const whitelist = mobile\n    ? props.qrcodeModalOptions && props.qrcodeModalOptions.mobileLinks\n      ? props.qrcodeModalOptions.mobileLinks\n      : undefined\n    : props.qrcodeModalOptions && props.qrcodeModalOptions.desktopLinks\n    ? props.qrcodeModalOptions.desktopLinks\n    : undefined;\n  const [loading, setLoading] = React.useState(false);\n  const [fetched, setFetched] = React.useState(false);\n  const [displayQRCode, setDisplayQRCode] = React.useState(!mobile);\n  const displayProps = {\n    mobile,\n    text: props.text,\n    uri: props.uri,\n    qrcodeModalOptions: props.qrcodeModalOptions,\n  };\n\n  const [singleLinkHref, setSingleLinkHref] = React.useState(\"\");\n  const [hasSingleLink, setHasSingleLink] = React.useState(false);\n  const [links, setLinks] = React.useState<IMobileRegistryEntry[]>([]);\n  const [errorMessage, setErrorMessage] = React.useState(\"\");\n\n  const getLinksIfNeeded = () => {\n    if (fetched || loading || (whitelist && !whitelist.length) || links.length > 0) {\n      return;\n    }\n\n    React.useEffect(() => {\n      const initLinks = async () => {\n        if (android) return;\n        setLoading(true);\n        try {\n          const url =\n            props.qrcodeModalOptions && props.qrcodeModalOptions.registryUrl\n              ? props.qrcodeModalOptions.registryUrl\n              : getWalletRegistryUrl();\n          const registryResponse = await fetch(url)\n          const registry = (await registryResponse.json()).listings as IAppRegistry;\n          const platform = mobile ? \"mobile\" : \"desktop\";\n          const _links = getMobileLinkRegistry(formatMobileRegistry(registry, platform), whitelist);\n          setLoading(false);\n          setFetched(true);\n          setErrorMessage(!_links.length ? props.text.no_supported_wallets : \"\");\n          setLinks(_links);\n          const hasSingleLink = _links.length === 1;\n          if (hasSingleLink) {\n            setSingleLinkHref(formatIOSMobile(props.uri, _links[0]));\n            setDisplayQRCode(true);\n          }\n          setHasSingleLink(hasSingleLink);\n        } catch (e) {\n          setLoading(false);\n          setFetched(true);\n          setErrorMessage(props.text.something_went_wrong);\n          console.error(e); // eslint-disable-line no-console\n        }\n      };\n      initLinks();\n    });\n  };\n\n  getLinksIfNeeded();\n\n  const rightSelected = mobile ? displayQRCode : !displayQRCode;\n  return (\n    <div id={WALLETCONNECT_MODAL_ID} className=\"walletconnect-qrcode__base animated fadeIn\">\n      <div className=\"walletconnect-modal__base\">\n        <Header onClose={props.onClose} />\n        {hasSingleLink && displayQRCode ? (\n          <div className=\"walletconnect-modal__single_wallet\">\n            <a\n              onClick={() => saveMobileLinkInfo({ name: links[0].name, href: singleLinkHref })}\n              href={singleLinkHref}\n              rel=\"noopener noreferrer\"\n              target=\"_blank\"\n            >\n              {props.text.connect_with + \" \" + (hasSingleLink ? links[0].name : \"\") + \" ›\"}\n            </a>\n          </div>\n        ) : android || loading || (!loading && links.length) ? (\n          <div\n            className={`walletconnect-modal__mobile__toggle${\n              rightSelected ? \" right__selected\" : \"\"\n            }`}\n          >\n            <div className=\"walletconnect-modal__mobile__toggle_selector\" />\n            {mobile ? (\n              <>\n                <a onClick={() => (setDisplayQRCode(false), getLinksIfNeeded())}>\n                  {props.text.mobile}\n                </a>\n                <a onClick={() => setDisplayQRCode(true)}>{props.text.qrcode}</a>\n              </>\n            ) : (\n              <>\n                <a onClick={() => setDisplayQRCode(true)}>{props.text.qrcode}</a>\n                <a onClick={() => (setDisplayQRCode(false), getLinksIfNeeded())}>\n                  {props.text.desktop}\n                </a>\n              </>\n            )}\n          </div>\n        ) : null}\n\n        <div>\n          {displayQRCode || (!android && !loading && !links.length) ? (\n            <QRCodeDisplay {...displayProps} />\n          ) : (\n            <LinkDisplay {...displayProps} links={links} errorMessage={errorMessage} />\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default Modal;\n", "import { TextMap } from \"../types\";\n\nconst de: TextMap = {\n  choose_preferred_wallet: \"Wähle bevorzugte Wallet\",\n  connect_mobile_wallet: \"Verbinde mit Mobile Wallet\",\n  scan_qrcode_with_wallet: \"Scanne den QR-code mit einer WalletConnect kompatiblen Wallet\",\n  connect: \"Verbinden\",\n  qrcode: \"QR-Code\",\n  mobile: \"Mobile\",\n  desktop: \"Desktop\",\n  copy_to_clipboard: \"In die Zwischenablage kopieren\",\n  copied_to_clipboard: \"In die Zwischenablage kopiert!\",\n  connect_with: \"Verbinden mit Hilfe von\",\n  loading: \"Laden...\",\n  something_went_wrong: \"Etwas ist schief gelaufen\",\n  no_supported_wallets: \"Es gibt noch keine unterstützten Wallet\",\n  no_wallets_found: \"keine Wallet gefunden\",\n};\n\nexport default de;\n", "import { TextMap } from \"../types\";\n\nconst en: TextMap = {\n  choose_preferred_wallet: \"Choose your preferred wallet\",\n  connect_mobile_wallet: \"Connect to Mobile Wallet\",\n  scan_qrcode_with_wallet: \"Scan QR code with a WalletConnect-compatible wallet\",\n  connect: \"Connect\",\n  qrcode: \"QR Code\",\n  mobile: \"Mobile\",\n  desktop: \"Desktop\",\n  copy_to_clipboard: \"Copy to clipboard\",\n  copied_to_clipboard: \"Copied to clipboard!\",\n  connect_with: \"Connect with\",\n  loading: \"Loading...\",\n  something_went_wrong: \"Something went wrong\",\n  no_supported_wallets: \"There are no supported wallets yet\",\n  no_wallets_found: \"No wallets found\",\n};\n\nexport default en;\n", "import { TextMap } from \"../types\";\n\nconst es: TextMap = {\n  choose_preferred_wallet: \"Elige tu billetera preferida\",\n  connect_mobile_wallet: \"Conectar a billetera móvil\",\n  scan_qrcode_with_wallet: \"Escanea el código QR con una billetera compatible con WalletConnect\",\n  connect: \"Conectar\",\n  qrcode: \"Código QR\",\n  mobile: \"Móvil\",\n  desktop: \"Desktop\",\n  copy_to_clipboard: \"Copiar\",\n  copied_to_clipboard: \"Copiado!\",\n  connect_with: \"Conectar mediante\",\n  loading: \"Cargando...\",\n  something_went_wrong: \"Algo salió mal\",\n  no_supported_wallets: \"Todavía no hay billeteras compatibles\",\n  no_wallets_found: \"No se encontraron billeteras\",\n};\n\nexport default es;\n", "import { TextMap } from \"../types\";\n\nconst fr: TextMap = {\n  choose_preferred_wallet: \"Choisissez votre portefeuille préféré\",\n  connect_mobile_wallet: \"Se connecter au portefeuille mobile\",\n  scan_qrcode_with_wallet: \"Scannez le QR code avec un portefeuille compatible WalletConnect\",\n  connect: \"Se connecter\",\n  qrcode: \"QR Code\",\n  mobile: \"Mobile\",\n  desktop: \"Desktop\",\n  copy_to_clipboard: \"Copier\",\n  copied_to_clipboard: \"Copié!\",\n  connect_with: \"Connectez-vous à l'aide de\",\n  loading: \"Chargement...\",\n  something_went_wrong: \"<PERSON><PERSON><PERSON> chose a mal tourné\",\n  no_supported_wallets: \"Il n'y a pas encore de portefeuilles pris en charge\",\n  no_wallets_found: \"Aucun portefeuille trouvé\",\n};\n\nexport default fr;\n", "import { TextMap } from \"../types\";\n\nconst ko: TextMap = {\n  choose_preferred_wallet: \"원하는 지갑을 선택하세요\",\n  connect_mobile_wallet: \"모바일 지갑과 연결\",\n  scan_qrcode_with_wallet: \"WalletConnect 지원 지갑에서 QR코드를 스캔하세요\",\n  connect: \"연결\",\n  qrcode: \"QR 코드\",\n  mobile: \"모바일\",\n  desktop: \"데스크탑\",\n  copy_to_clipboard: \"클립보드에 복사\",\n  copied_to_clipboard: \"클립보드에 복사되었습니다!\",\n  connect_with: \"와 연결하다\",\n  loading: \"로드 중...\",\n  something_went_wrong: \"문제가 발생했습니다.\",\n  no_supported_wallets: \"아직 지원되는 지갑이 없습니다\",\n  no_wallets_found: \"지갑을 찾을 수 없습니다\",\n};\n\nexport default ko;\n", "import { TextMap } from \"../types\";\n\nconst pt: TextMap = {\n  choose_preferred_wallet: \"Escolha sua carteira preferida\",\n  connect_mobile_wallet: \"Conectar-se à carteira móvel\",\n  scan_qrcode_with_wallet: \"Ler o código QR com uma carteira compatível com WalletConnect\",\n  connect: \"Conectar\",\n  qrcode: \"Código QR\",\n  mobile: \"Móvel\",\n  desktop: \"Desktop\",\n  copy_to_clipboard: \"Copiar\",\n  copied_to_clipboard: \"Copiado!\",\n  connect_with: \"Ligar por meio de\",\n  loading: \"Carregamento...\",\n  something_went_wrong: \"Algo correu mal\",\n  no_supported_wallets: \"Ainda não há carteiras suportadas\",\n  no_wallets_found: \"Nenhuma carteira encontrada\",\n};\n\nexport default pt;\n", "import { TextMap } from \"../types\";\n\nconst zh: TextMap = {\n  choose_preferred_wallet: \"选择你的钱包\",\n  connect_mobile_wallet: \"连接至移动端钱包\",\n  scan_qrcode_with_wallet: \"使用兼容 WalletConnect 的钱包扫描二维码\",\n  connect: \"连接\",\n  qrcode: \"二维码\",\n  mobile: \"移动\",\n  desktop: \"桌面\",\n  copy_to_clipboard: \"复制到剪贴板\",\n  copied_to_clipboard: \"复制到剪贴板成功！\",\n  connect_with: \"通过以下方式连接\",\n  loading: \"正在加载...\",\n  something_went_wrong: \"出了问题\",\n  no_supported_wallets: \"目前还没有支持的钱包\",\n  no_wallets_found: \"没有找到钱包\",\n};\n\nexport default zh;\n", "import { TextMap } from \"../types\";\n\nconst fa: TextMap = {\n  choose_preferred_wallet: \"کیف پول مورد نظر خود را انتخاب کنید\",\n  connect_mobile_wallet: \"به کیف پول موبایل وصل شوید\",\n  scan_qrcode_with_wallet: \"کد QR را با یک کیف پول سازگار با WalletConnect اسکن کنید\",\n  connect: \"اتصال\",\n  qrcode: \"کد QR\",\n  mobile: \"سیار\",\n  desktop: \"دسکتاپ\",\n  copy_to_clipboard: \"کپی به کلیپ بورد\",\n  copied_to_clipboard: \"در کلیپ بورد کپی شد!\",\n  connect_with: \"ارتباط با\",\n  loading: \"...بارگذاری\",\n  something_went_wrong: \"مشکلی پیش آمد\",\n  no_supported_wallets: \"هنوز هیچ کیف پول پشتیبانی شده ای وجود ندارد\",\n  no_wallets_found: \"هیچ کیف پولی پیدا نشد\",\n};\n\nexport default fa;\n", "import { TextMap } from \"../types\";\n\nimport de from \"./de\";\nimport en from \"./en\";\nimport es from \"./es\";\nimport fr from \"./fr\";\nimport ko from \"./ko\";\nimport pt from \"./pt\";\nimport zh from \"./zh\";\nimport fa from \"./fa\";\n\nconst languages: { [lang: string]: TextMap } = { de, en, es, fr, ko, pt, zh, fa };\n\nexport default languages;\n", "// eslint-disable-next-line @typescript-eslint/no-unused-vars\nimport * as React from \"react\";\n// @ts-ignore\nimport * as ReactDOM from \"react-dom\";\nimport { getDocumentOrThrow, getNavigatorOrThrow } from \"@walletconnect/browser-utils\";\n\nimport { WALLETCONNECT_STYLE_SHEET } from \"./assets/style\";\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nimport Modal from \"./components/Modal\";\nimport Languages from \"./languages\";\nimport {\n  ANIMATION_DURATION,\n  WALLETCONNECT_WRAPPER_ID,\n  WALLETCONNECT_MODAL_ID,\n  WALLETCONNECT_STYLE_ID,\n} from \"./constants\";\nimport { IQRCodeModalOptions } from \"@walletconnect/types\";\nimport { TextMap } from \"./types\";\n\nfunction injectStyleSheet() {\n  const doc = getDocumentOrThrow();\n  const prev = doc.getElementById(WALLETCONNECT_STYLE_ID);\n  if (prev) {\n    doc.head.removeChild(prev);\n  }\n  const style = doc.createElement(\"style\");\n  style.setAttribute(\"id\", WALLETCONNECT_STYLE_ID);\n  style.innerText = WALLETCONNECT_STYLE_SHEET;\n  doc.head.appendChild(style);\n}\n\nfunction renderWrapper(): HTMLDivElement {\n  const doc = getDocumentOrThrow();\n  const wrapper = doc.createElement(\"div\");\n  wrapper.setAttribute(\"id\", WALLETCONNECT_WRAPPER_ID);\n  doc.body.appendChild(wrapper);\n  return wrapper;\n}\n\nfunction triggerCloseAnimation(): void {\n  const doc = getDocumentOrThrow();\n  const modal = doc.getElementById(WALLETCONNECT_MODAL_ID);\n  if (modal) {\n    modal.className = modal.className.replace(\"fadeIn\", \"fadeOut\");\n    setTimeout(() => {\n      const wrapper = doc.getElementById(WALLETCONNECT_WRAPPER_ID);\n      if (wrapper) {\n        doc.body.removeChild(wrapper);\n      }\n    }, ANIMATION_DURATION);\n  }\n}\n\nfunction getWrappedCallback(cb: any): any {\n  return () => {\n    triggerCloseAnimation();\n    if (cb) {\n      cb();\n    }\n  };\n}\n\nfunction getText(): TextMap {\n  const lang = getNavigatorOrThrow().language.split(\"-\")[0] || \"en\";\n  return Languages[lang] || Languages[\"en\"];\n}\n\nexport function open(uri: string, cb: any, qrcodeModalOptions?: IQRCodeModalOptions) {\n  injectStyleSheet();\n  const wrapper = renderWrapper();\n  ReactDOM.render(\n    <Modal\n      text={getText()}\n      uri={uri}\n      onClose={getWrappedCallback(cb)}\n      qrcodeModalOptions={qrcodeModalOptions}\n    />,\n    wrapper,\n  );\n}\n\nexport function close() {\n  triggerCloseAnimation();\n}\n", "import { IQRCodeModalOptions } from \"@walletconnect/types\";\n\nimport * as nodeLib from \"./node\";\nimport * as browserLib from \"./browser\";\n\nconst isNode = () =>\n  typeof process !== \"undefined\" &&\n  typeof process.versions !== \"undefined\" &&\n  typeof process.versions.node !== \"undefined\";\n\nfunction open(uri: string, cb: any, qrcodeModalOptions?: IQRCodeModalOptions) {\n  // eslint-disable-next-line no-console\n  console.log(uri);\n  if (isNode()) {\n    nodeLib.open(uri);\n  } else {\n    browserLib.open(uri, cb, qrcodeModalOptions);\n  }\n}\n\nfunction close() {\n  if (isNode()) {\n    nodeLib.close();\n  } else {\n    browserLib.close();\n  }\n}\n\nexport default { open, close };\n"], "names": ["open", "uri", "QRCode", "toString", "type", "then", "console", "log", "const", "WALLETCONNECT_STYLE_SHEET", "_iteratorSymbol", "Symbol", "iterator", "_asyncIteratorSymbol", "asyncIterator", "_catch", "body", "recover", "result", "e", "WALLETCONNECT_LOGO_SVG_URL", "WALLETCONNECT_HEADER_TEXT", "ANIMATION_DURATION", "DEFAULT_BUTTON_COLOR", "WALLETCONNECT_WRAPPER_ID", "WALLETCONNECT_STYLE_ID", "WALLETCONNECT_MODAL_ID", "WALLETCONNECT_CLOSE_BUTTON_ID", "WALLETCONNECT_CTA_TEXT_ID", "WALLETCONNECT_CONNECT_BUTTON_ID", "Header", "props", "React", "className", "src", "onClick", "onClose", "id", "ConnectButton", "href", "name", "rel", "style", "backgroundColor", "color", "target", "CARET_SVG_URL", "WalletButton", "logo", "background", "backgroundSize", "WalletIcon", "fontSize", "window", "innerWidth", "length", "GRID_MIN_COUNT", "LINKS_PER_PAGE", "LinkDisplay", "android", "isAndroid", "input", "setInput", "filter", "setFilter", "page", "setPage", "links", "link", "toLowerCase", "includes", "errorMessage", "grid", "pages", "Math", "ceil", "range", "pageLinks", "_", "index", "hasPaging", "let", "filterTimeout", "undefined", "handleInput", "value", "clearTimeout", "setTimeout", "text", "connect_mobile_wallet", "choose_preferred_wallet", "placeholder", "onChange", "map", "entry", "shortName", "formatIOSMobile", "handleClickIOS", "saveMobileLinkInfo", "no_wallets_found", "loading", "connect", "Array", "fill", "pageNumber", "selected", "margin", "fontWeight", "Notification", "show", "message", "trim", "formatQRCodeImage", "data", "dataString", "replace", "QRCodeDisplay", "notification", "setNotification", "svg", "setSvg", "copyToClipboard", "success", "copy", "copied_to_clipboard", "setInterval", "scan_qrcode_with_wallet", "dangerouslySetInnerHTML", "__html", "copy_to_clipboard", "Modal", "mobile", "isMobile", "whitelist", "qrcodeModalOptions", "mobileLinks", "desktopLinks", "setLoading", "fetched", "setFetched", "displayQRCode", "setDisplayQRCode", "displayProps", "singleLinkHref", "setSingleLinkHref", "hasSingleLink", "setHasSingleLink", "setLinks", "setErrorMessage", "getLinksIfNeeded", "initLinks", "url", "registryUrl", "getWalletRegistryUrl", "fetch", "registryResponse", "json", "registry", "listings", "platform", "_links", "getMobileLinkRegistry", "formatMobileRegistry", "no_supported_wallets", "something_went_wrong", "error", "rightSelected", "connect_with", "qrcode", "desktop", "de", "en", "es", "fr", "ko", "pt", "zh", "fa", "languages", "injectStyleSheet", "doc", "getDocumentOrThrow", "prev", "getElementById", "head", "<PERSON><PERSON><PERSON><PERSON>", "createElement", "setAttribute", "innerText", "append<PERSON><PERSON><PERSON>", "renderWrapper", "wrapper", "triggerCloseAnimation", "modal", "getWrappedCallback", "cb", "getText", "lang", "getNavigatorOrThrow", "language", "split", "Languages", "ReactDOM", "close", "isNode", "process", "versions", "node", "nodeLib", "browserLib"], "mappings": ";;;;;;;SAEgBA,KAAKC;EAEnBC,MAAM,CAACC,QAAP,CAAgBF,GAAhB,EAAqB;IAAEG,IAAI,EAAE;GAA7B,EAA2CC,IAA3C,CAAgDC,OAAO,CAACC,GAAxD;;;ACJKC,IAAMC,yBAAyB,8zUAA/B;;ACAP;AACA,AAmKOD,IAAME,eAAe,gBAAiB,OAAOC,MAAP,KAAkB,WAAlB,GAAiCA,MAAM,CAACC,QAAP,KAAoBD,MAAM,CAACC,QAAP,GAAkBD,MAAM,CAAC,iBAAD,CAA5C,CAAjC,GAAqG,YAA3I;AAGP,AA0DOH,IAAMK,oBAAoB,gBAAiB,OAAOF,MAAP,KAAkB,WAAlB,GAAiCA,MAAM,CAACG,aAAP,KAAyBH,MAAM,CAACG,aAAP,GAAuBH,MAAM,CAAC,sBAAD,CAAtD,CAAjC,GAAoH,iBAA/J;;AAiVP,AAAO,SAASI,MAAT,CAAgBC,IAAhB,EAAsBC,OAAtB,EAA+B;EACrC,IAAI;IACH,IAAIC,MAAM,GAAGF,IAAI,EAAjB;GADD,CAEE,OAAMG,CAAN,EAAS;IACV,OAAOF,OAAO,CAACE,CAAD,CAAd;;;EAED,IAAID,MAAM,IAAIA,MAAM,CAACb,IAArB,EAA2B;IAC1B,OAAOa,MAAM,CAACb,IAAP,CAAY,KAAK,CAAjB,EAAoBY,OAApB,CAAP;;;EAED,OAAOC,MAAP;;;AC3jBMV,IAAMY,0BAA0B,miDAAhC;;ACAAZ,IAAMa,yBAAyB,GAAG,eAAlC;AAEP,AAAOb,IAAMc,kBAAkB,GAAG,GAA3B;AACP,AAAOd,IAAMe,oBAAoB,GAAG,mBAA7B;AAEP,AAAOf,IAAMgB,wBAAwB,GAAG,uBAAjC;AACP,AAAOhB,IAAMiB,sBAAsB,GAAG,2BAA/B;AACP,AAAOjB,IAAMkB,sBAAsB,GAAG,4BAA/B;AACP,AAAOlB,IAAMmB,6BAA6B,GAAG,4BAAtC;AACP,AAAOnB,IAAMoB,yBAAyB,GAAG,2BAAlC;AACP,AAAOpB,IAAMqB,+BAA+B,GAAG,8BAAxC;;ACAP,SAASC,MAAT,CAAgBC,KAAhB;EACE,OACEC,mBAAA,MAAA;IAAKC,SAAS,EAAC;GAAf,EACED,mBAAA,MAAA;IAAKE,GAAG,EAAEd;IAA4Ba,SAAS,EAAC;GAAhD,CADF,EAEED,mBAAA,IAAA,MAAA,EAAIX,yBAAJ,CAFF,EAGEW,mBAAA,MAAA;IAAKC,SAAS,EAAC;IAAsCE,OAAO,EAAEJ,KAAK,CAACK;GAApE,EACEJ,mBAAA,MAAA;IAAKK,EAAE,EAAEV;IAA+BM,SAAS,EAAC;GAAlD,EACED,mBAAA,MAAA;IAAKC,SAAS,EAAC;GAAf,CADF,EAEED,mBAAA,MAAA;IAAKC,SAAS,EAAC;GAAf,CAFF,CADF,CAHF,CADF;;;ACAF,SAASK,aAAT,CAAuBP,KAAvB;EACE,OACEC,mBAAA,IAAA;IACEC,SAAS,EAAC;IACVM,IAAI,EAAER,KAAK,CAACQ;IACZF,EAAE,GAAKR,yCAAmCE,KAAK,CAACS;IAChDL,OAAO,EAAEJ,KAAK,CAACI;IACfM,GAAG,EAAC;IACJC,KAAK,EAAE;MAAEC,eAAe,EAAEZ,KAAK,CAACa;;IAChCC,MAAM,EAAC;GAPT,EASGd,KAAK,CAACS,IATT,CADF;;;ACZKhC,IAAMsC,aAAa,2hBAAnB;;ACWP,SAASC,YAAT,CAAsBhB,KAAtB;EACUa;EAAOL;EAAMC;EAAMQ;EAAMb;EACjC,OACEH,mBAAA,IAAA;IACEC,SAAS,EAAC;IACVM,IAAI,EAAEA;IACNJ,OAAO,EAAEA;IACTM,GAAG,EAAC;IACJI,MAAM,EAAC;GALT,EAOEb,mBAAA,KAAA;IAAIC,SAAS,EAAE;GAAf,EAAsDO,IAAtD,CAPF,EAQER,mBAAA,MAAA;IAAKC,SAAS,EAAC;GAAf,EACED,mBAAA,MAAA;IACEC,SAAS;IACTS,KAAK,EAAE;MAAEO,UAAU,aAAUD,eAAUJ,MAAhC;MAAyCM,cAAc,EAAE;;GAFlE,CADF,EAKElB,mBAAA,MAAA;IAAKE,GAAG,EAAEY;IAAeb,SAAS,EAAC;GAAnC,CALF,CARF,CADF;;;ACHF,SAASkB,UAAT,CAAoBpB,KAApB;EACUa;EAAOL;EAAMC;EAAMQ;EAAMb;EACjC3B,IAAM4C,QAAQ,GAAGC,MAAM,CAACC,UAAP,GAAoB,GAApB,KAA6Bd,IAAI,CAACe,MAAL,GAAc,CAAd,GAAkB,GAAlB,GAAwB,eAAU,SAAhF;EACA,OACEvB,mBAAA,IAAA;IACEC,SAAS,EAAC;IACVM,IAAI,EAAEA;IACNJ,OAAO,EAAEA;IACTM,GAAG,EAAC;IACJI,MAAM,EAAC;GALT,EAOEb,mBAAA,MAAA;IACEC,SAAS,EAAC;IACVS,KAAK,EAAE;MAAEO,UAAU,aAAUD,eAAUJ,MAAhC;MAAyCM,cAAc,EAAE;;GAFlE,CAPF,EAWElB,mBAAA,MAAA;IAAKU,KAAK,EAAE;gBAAEU;;IAAYnB,SAAS,EAAE;GAArC,EACGO,IADH,CAXF,CADF;;;ACUFhC,IAAMgD,cAAc,GAAG,CAAvB;AACAhD,IAAMiD,cAAc,GAAG,EAAvB;;AAEA,SAASC,WAAT,CAAqB3B,KAArB;EACEvB,IAAMmD,OAAO,GAAGC,sBAAS,EAAzB;YAC0B5B,cAAA,CAAe,EAAf;EAAnB6B;EAAOC;cACc9B,cAAA,CAAe,EAAf;EAArB+B;EAAQC;cACShC,cAAA,CAAe,CAAf;EAAjBiC;EAAMC;EACb1D,IAAM2D,KAAK,GAAGJ,MAAM,GAChBhC,KAAK,CAACoC,KAAN,CAAYJ,MAAZ,WAAmBK,eAAQA,IAAI,CAAC5B,IAAL,CAAU6B,WAAV,GAAwBC,QAAxB,CAAiCP,MAAM,CAACM,WAAP,EAAjC,IAA3B,CADgB,GAEhBtC,KAAK,CAACoC,KAFV;EAGA3D,IAAM+D,YAAY,GAAGxC,KAAK,CAACwC,YAA3B;EACA/D,IAAMgE,IAAI,GAAGT,MAAM,IAAII,KAAK,CAACZ,MAAN,GAAeC,cAAtC;EACAhD,IAAMiE,KAAK,GAAGC,IAAI,CAACC,IAAL,CAAUR,KAAK,CAACZ,MAAN,GAAeE,cAAzB,CAAd;EACAjD,IAAMoE,KAAK,GAAG,CAAC,CAACX,IAAI,GAAG,CAAR,IAAaR,cAAb,GAA8B,CAA/B,EAAkCQ,IAAI,GAAGR,cAAzC,CAAd;EACAjD,IAAMqE,SAAS,GAAGV,KAAK,CAACZ,MAAN,GACdY,KAAK,CAACJ,MAAN,WAAce,CAAD,EAAIC,KAAJ,WAAcA,KAAK,GAAG,CAAR,IAAaH,KAAK,CAAC,CAAD,CAAlB,IAAyBG,KAAK,GAAG,CAAR,IAAaH,KAAK,CAAC,CAAD,IAAtE,CADc,GAEd,EAFJ;EAGApE,IAAMwE,SAAS,GAAG,CAAC,EAAE,CAACrB,OAAD,IAAYc,KAAK,GAAG,CAAtB,CAAnB;EACAQ,IAAIC,aAAa,GAAQC,SAAzB;;EACA,SAASC,WAAT,CAAqBjE,CAArB;IACE2C,QAAQ,CAAC3C,CAAC,CAAC0B,MAAF,CAASwC,KAAV,CAAR;IACAC,YAAY,CAACJ,aAAD,CAAZ;;IACA,IAAI/D,CAAC,CAAC0B,MAAF,CAASwC,KAAb,EAAoB;MAClBH,aAAa,GAAGK,UAAU;QACxBvB,SAAS,CAAC7C,CAAC,CAAC0B,MAAF,CAASwC,KAAV,CAAT;QACAnB,OAAO,CAAC,CAAD,CAAP;OAFwB,EAGvB,IAHuB,CAA1B;KADF,MAKO;MACLJ,QAAQ,CAAC,EAAD,CAAR;MACAE,SAAS,CAAC,EAAD,CAAT;MACAE,OAAO,CAAC,CAAD,CAAP;;;;EAIJ,OACElC,mBAAA,MAAA,MAAA,EACEA,mBAAA,IAAA;IAAGK,EAAE,EAAET;IAA2BK,SAAS,EAAC;GAA5C,EACG0B,OAAO,GAAG5B,KAAK,CAACyD,IAAN,CAAWC,qBAAd,GAAsC1D,KAAK,CAACyD,IAAN,CAAWE,uBAD3D,CADF,EAIG,CAAC/B,OAAD,IACC3B,mBAAA,QAAA;IACEC,SAAS;IACT0D,WAAW,EAAC;IACZN,KAAK,EAAExB;IACP+B,QAAQ,EAAER;GAJZ,CALJ,EAYEpD,mBAAA,MAAA;IACEC,SAAS,gDACP0B,OAAO,GAAG,WAAH,GAAiBa,IAAI,IAAIL,KAAK,CAACZ,MAAd,GAAuB,QAAvB,GAAkC;GAF9D,EAKG,CAACI,OAAD,GACCkB,SAAS,CAACtB,MAAV,GACEsB,SAAS,CAACgB,GAAV,WAAeC;IACLlD;IAAOJ;IAAMuD;IAAW/C;IAChCxC,IAAM+B,IAAI,GAAGyD,4BAAe,CAACjE,KAAK,CAAC9B,GAAP,EAAY6F,KAAZ,CAA5B;IACAtF,IAAMyF,cAAc,GAAGjE,iBAAA;MACrBkE,+BAAkB,CAAC;cACjB1D,IADiB;cAEjBD;OAFgB,CAAlB;KADqB,EAKpB,CAACsC,SAAD,CALoB,CAAvB;IAMA,OAAO,CAACL,IAAD,GACLxC,mBAAA,CAACe,YAAD;MACEH,KAAK,EAAEA;MACPL,IAAI,EAAEA;MACNC,IAAI,EAAEA;MACNQ,IAAI,EAAEA;MACNb,OAAO,EAAE8D;KALX,CADK,GASLjE,mBAAA,CAACmB,UAAD;MACEP,KAAK,EAAEA;MACPL,IAAI,EAAEA;MACNC,IAAI,EAAEuD,SAAS,IAAIvD;MACnBQ,IAAI,EAAEA;MACNb,OAAO,EAAE8D;KALX,CATF;GATF,CADF,GA6BEjE,mBAAA,eAAA,MAAA,EACEA,mBAAA,IAAA,MAAA,EACGuC,YAAY,CAAChB,MAAb,GACGxB,KAAK,CAACwC,YADT,GAEG,CAAC,CAACxC,KAAK,CAACoC,KAAN,CAAYZ,MAAd,IAAwB,CAACY,KAAK,CAACZ,MAA/B,GACAxB,KAAK,CAACyD,IAAN,CAAWW,gBADX,GAEApE,KAAK,CAACyD,IAAN,CAAWY,OALjB,CADF,CA9BH,GAyCCpE,mBAAA,CAACM,aAAD;IACEE,IAAI,EAAET,KAAK,CAACyD,IAAN,CAAWa;IACjBzD,KAAK,EAAErB;IACPgB,IAAI,EAAER,KAAK,CAAC9B;IACZkC,OAAO,EAAEH,iBAAA;MACPkE,+BAAkB,CAAC;QACjB1D,IAAI,EAAE,SADW;QAEjBD,IAAI,EAAER,KAAK,CAAC9B;OAFI,CAAlB;KADO,EAKN,EALM;GAJX,CA9CJ,CAZF,EAuEG+E,SAAS,IACRhD,mBAAA,MAAA;IAAKC,SAAS,EAAC;GAAf,EACGqE,KAAK,CAAC7B,KAAD,CAAL,CACE8B,IADF,CACO,CADP,EAEEV,GAFF,WAEOf,CAAD,EAAIC,KAAJ;IACHvE,IAAMgG,UAAU,GAAGzB,KAAK,GAAG,CAA3B;IACAvE,IAAMiG,QAAQ,GAAGxC,IAAI,KAAKuC,UAA1B;IACA,OACExE,mBAAA,IAAA;MACEU,KAAK,EAAE;QAAEgE,MAAM,EAAE,WAAV;QAAuBC,UAAU,EAAEF,QAAQ,GAAG,MAAH,GAAY;;MAC9DtE,OAAO,uBAAQ+B,OAAO,CAACsC,UAAD;KAFxB,EAIGA,UAJH,CADF;GALH,CADH,CAxEJ,CADF;;;ACnDF,SAASI,YAAT,CAAsB7E,KAAtB;EACEvB,IAAMqG,IAAI,GAAG,CAAC,CAAC9E,KAAK,CAAC+E,OAAN,CAAcC,IAAd,EAAf;EACA,OACE/E,mBAAA,MAAA;IAAKC,SAAS,2CAAuC4E,IAAI,GAAG,qBAAH,GAA2B;GAApF,EACG9E,KAAK,CAAC+E,OADT,CADF;;;ICEaE,8BAAkBC;;IAC/BhC,IAAI/D,MAAM,GAAG,EAAb;2BACyBhB,MAAM,CAACC,QAAP,CAAgB8G,IAAhB,EAAsB;MAAEP,MAAM,EAAE,CAAV;MAAatG,IAAI,EAAE;KAAzC,kBAAnB8G;MACN,IAAI,OAAOA,UAAP,KAAsB,QAA1B,EAAoC;QAClChG,MAAM,GAAGgG,UAAU,CAACC,OAAX,CAAmB,MAAnB,8CAAA,CAAT;;;MAEF,OAAOjG,MAAP;;;;;;;AAQF,SAASkG,aAAT,CAAuBrF,KAAvB;YAC0CC,cAAA,CAAe,EAAf;EAAjCqF;EAAcC;cACCtF,cAAA,CAAe,EAAf;EAAfuF;EAAKC;EAEZxF,eAAA;;6BAEiBgF,iBAAiB,CAACjF,KAAK,CAAC9B,GAAP;QAA9BuH,MAAM,oBAAN;;;;;GAFJ,EAIG,EAJH;;EAMAhH,IAAMiH,eAAe;IACnBjH,IAAMkH,OAAO,GAAGC,IAAI,CAAC5F,KAAK,CAAC9B,GAAP,CAApB;;IACA,IAAIyH,OAAJ,EAAa;MACXJ,eAAe,CAACvF,KAAK,CAACyD,IAAN,CAAWoC,mBAAZ,CAAf;MACAC,WAAW,sBAAOP,eAAe,CAAC,EAAD,IAAtB,EAA4B,IAA5B,CAAX;KAFF,MAGO;MACLA,eAAe,CAAC,OAAD,CAAf;MACAO,WAAW,sBAAOP,eAAe,CAAC,EAAD,IAAtB,EAA4B,IAA5B,CAAX;;GAPJ;;EAWA,OACEtF,mBAAA,MAAA,MAAA,EACEA,mBAAA,IAAA;IAAGK,EAAE,EAAET;IAA2BK,SAAS,EAAC;GAA5C,EACGF,KAAK,CAACyD,IAAN,CAAWsC,uBADd,CADF,EAIE9F,mBAAA,MAAA;IAAK+F,uBAAuB,EAAE;MAAEC,MAAM,EAAET;;GAAxC,CAJF,EAKEvF,mBAAA,MAAA;IAAKC,SAAS,EAAC;GAAf,EACED,mBAAA,IAAA;IAAGG,OAAO,EAAEsF;GAAZ,EAA8B1F,KAAK,CAACyD,IAAN,CAAWyC,iBAAzC,CADF,CALF,EAQEjG,mBAAA,CAAC4E,YAAD;IAAcE,OAAO,EAAEO;GAAvB,CARF,CADF;;;ACZF,SAASa,KAAT,CAAenG,KAAf;EACEvB,IAAMmD,OAAO,GAAGC,sBAAS,EAAzB;EACApD,IAAM2H,MAAM,GAAGC,qBAAQ,EAAvB;EAEA5H,IAAM6H,SAAS,GAAGF,MAAM,GACpBpG,KAAK,CAACuG,kBAAN,IAA4BvG,KAAK,CAACuG,kBAAN,CAAyBC,WAArD,GACExG,KAAK,CAACuG,kBAAN,CAAyBC,WAD3B,GAEEpD,SAHkB,GAIpBpD,KAAK,CAACuG,kBAAN,IAA4BvG,KAAK,CAACuG,kBAAN,CAAyBE,YAArD,GACAzG,KAAK,CAACuG,kBAAN,CAAyBE,YADzB,GAEArD,SANJ;YAO8BnD,cAAA,CAAe,KAAf;EAAvBoE;EAASqC;cACczG,cAAA,CAAe,KAAf;EAAvB0G;EAASC;cAC0B3G,cAAA,CAAe,CAACmG,MAAhB;EAAnCS;EAAeC;EACtBrI,IAAMsI,YAAY,GAAG;YACnBX,MADmB;IAEnB3C,IAAI,EAAEzD,KAAK,CAACyD,IAFO;IAGnBvF,GAAG,EAAE8B,KAAK,CAAC9B,GAHQ;IAInBqI,kBAAkB,EAAEvG,KAAK,CAACuG;GAJ5B;cAO4CtG,cAAA,CAAe,EAAf;EAArC+G;EAAgBC;cACmBhH,cAAA,CAAe,KAAf;EAAnCiH;EAAeC;cACIlH,cAAA,CAAuC,EAAvC;EAAnBmC;EAAOgF;cAC0BnH,cAAA,CAAe,EAAf;EAAjCuC;EAAc6E;;EAErB5I,IAAM6I,gBAAgB;IACpB,IAAIX,OAAO,IAAItC,OAAX,IAAuBiC,SAAS,IAAI,CAACA,SAAS,CAAC9E,MAA/C,IAA0DY,KAAK,CAACZ,MAAN,GAAe,CAA7E,EAAgF;MAC9E;;;IAGFvB,eAAA;MACExB,IAAM8I,SAAS;QAAA;UACb,IAAI3F,OAAJ,IAAa;UACb8E,UAAU,CAAC,IAAD,CAAV;;yCACI;YACFjI,IAAM+I,GAAG,GACPxH,KAAK,CAACuG,kBAAN,IAA4BvG,KAAK,CAACuG,kBAAN,CAAyBkB,WAArD,GACIzH,KAAK,CAACuG,kBAAN,CAAyBkB,WAD7B,GAEIC,iCAAoB,EAH1B;YADE,uBAK6BC,KAAK,CAACH,GAAD,CALlC,iBAKII,gBALJ;cAAA,uBAMsBA,gBAAgB,CAACC,IAAjB,EANtB;gBAMFpJ,IAAMqJ,QAAQ,GAAG,sBAAgCC,QAAjD;gBACAtJ,IAAMuJ,QAAQ,GAAG5B,MAAM,GAAG,QAAH,GAAc,SAArC;;gBACA3H,IAAMwJ,MAAM,GAAGC,kCAAqB,CAACC,iCAAoB,CAACL,QAAD,EAAWE,QAAX,CAArB,EAA2C1B,SAA3C,CAApC;;gBACAI,UAAU,CAAC,KAAD,CAAV;gBACAE,UAAU,CAAC,IAAD,CAAV;gBACAS,eAAe,CAAC,CAACY,MAAM,CAACzG,MAAR,GAAiBxB,KAAK,CAACyD,IAAN,CAAW2E,oBAA5B,GAAmD,EAApD,CAAf;gBACAhB,QAAQ,CAACa,MAAD,CAAR;gBACAxJ,IAAMyI,aAAa,GAAGe,MAAM,CAACzG,MAAP,KAAkB,CAAxC;;gBACA,IAAI0F,aAAJ,EAAmB;kBACjBD,iBAAiB,CAAChD,4BAAe,CAACjE,KAAK,CAAC9B,GAAP,EAAY+J,MAAM,CAAC,CAAD,CAAlB,CAAhB,CAAjB;kBACAnB,gBAAgB,CAAC,IAAD,CAAhB;;;gBAEFK,gBAAgB,CAACD,aAAD,CAAhB;;;uBACO9H,GAAG;YACVsH,UAAU,CAAC,KAAD,CAAV;YACAE,UAAU,CAAC,IAAD,CAAV;YACAS,eAAe,CAACrH,KAAK,CAACyD,IAAN,CAAW4E,oBAAZ,CAAf;YACA9J,OAAO,CAAC+J,KAAR,CAAclJ,CAAd;;;;SA1BW;UAAA;;OAAf;;MA6BAmI,SAAS;KA9BX;GALF;;EAuCAD,gBAAgB;EAEhB7I,IAAM8J,aAAa,GAAGnC,MAAM,GAAGS,aAAH,GAAmB,CAACA,aAAhD;EACA,OACE5G,mBAAA,MAAA;IAAKK,EAAE,EAAEX;IAAwBO,SAAS,EAAC;GAA3C,EACED,mBAAA,MAAA;IAAKC,SAAS,EAAC;GAAf,EACED,mBAAA,CAACF,MAAD;IAAQM,OAAO,EAAEL,KAAK,CAACK;GAAvB,CADF,EAEG6G,aAAa,IAAIL,aAAjB,GACC5G,mBAAA,MAAA;IAAKC,SAAS,EAAC;GAAf,EACED,mBAAA,IAAA;IACEG,OAAO,uBAAQ+D,+BAAkB,CAAC;MAAE1D,IAAI,EAAE2B,KAAK,CAAC,CAAD,CAAL,CAAS3B,IAAjB;MAAuBD,IAAI,EAAEwG;KAA9B;IACjCxG,IAAI,EAAEwG;IACNtG,GAAG,EAAC;IACJI,MAAM,EAAC;GAJT,EAMGd,KAAK,CAACyD,IAAN,CAAW+E,YAAX,GAA0B,GAA1B,IAAiCtB,aAAa,GAAG9E,KAAK,CAAC,CAAD,CAAL,CAAS3B,IAAZ,GAAmB,EAAjE,IAAuE,IAN1E,CADF,CADD,GAWGmB,OAAO,IAAIyC,OAAX,IAAuB,CAACA,OAAD,IAAYjC,KAAK,CAACZ,MAAzC,GACFvB,mBAAA,MAAA;IACEC,SAAS,4CACPqI,aAAa,GAAG,kBAAH,GAAwB;GAFzC,EAKEtI,mBAAA,MAAA;IAAKC,SAAS,EAAC;GAAf,CALF,EAMGkG,MAAM,GACLnG,mBAAA,eAAA,MAAA,EACEA,mBAAA,IAAA;IAAGG,OAAO,wBAAS0G,gBAAgB,CAAC,KAAD,CAAhB,EAAyBQ,gBAAgB,EAAhD;GAAZ,EACGtH,KAAK,CAACyD,IAAN,CAAW2C,MADd,CADF,EAIEnG,mBAAA,IAAA;IAAGG,OAAO,uBAAQ0G,gBAAgB,CAAC,IAAD;GAAlC,EAA2C9G,KAAK,CAACyD,IAAN,CAAWgF,MAAtD,CAJF,CADK,GAQLxI,mBAAA,eAAA,MAAA,EACEA,mBAAA,IAAA;IAAGG,OAAO,uBAAQ0G,gBAAgB,CAAC,IAAD;GAAlC,EAA2C9G,KAAK,CAACyD,IAAN,CAAWgF,MAAtD,CADF,EAEExI,mBAAA,IAAA;IAAGG,OAAO,wBAAS0G,gBAAgB,CAAC,KAAD,CAAhB,EAAyBQ,gBAAgB,EAAhD;GAAZ,EACGtH,KAAK,CAACyD,IAAN,CAAWiF,OADd,CAFF,CAdJ,CADE,GAuBA,IApCN,EAsCEzI,mBAAA,MAAA,MAAA,EACG4G,aAAa,IAAK,CAACjF,OAAD,IAAY,CAACyC,OAAb,IAAwB,CAACjC,KAAK,CAACZ,MAAjD,GACCvB,mBAAA,CAACoF,aAAD,oBAAmB0B,aAAnB,CADD,GAGC9G,mBAAA,CAAC0B,WAAD,oBAAiBoF;KAAc3E,KAAK,EAAEA;IAAOI,YAAY,EAAEA,cAA3D,CAJJ,CAtCF,CADF,CADF;;;ACpGF/D,IAAMkK,EAAE,GAAY;EAClBhF,uBAAuB,EAAE,yBADP;EAElBD,qBAAqB,EAAE,4BAFL;EAGlBqC,uBAAuB,EAAE,+DAHP;EAIlBzB,OAAO,EAAE,WAJS;EAKlBmE,MAAM,EAAE,SALU;EAMlBrC,MAAM,EAAE,QANU;EAOlBsC,OAAO,EAAE,SAPS;EAQlBxC,iBAAiB,EAAE,gCARD;EASlBL,mBAAmB,EAAE,gCATH;EAUlB2C,YAAY,EAAE,yBAVI;EAWlBnE,OAAO,EAAE,UAXS;EAYlBgE,oBAAoB,EAAE,2BAZJ;EAalBD,oBAAoB,EAAE,yCAbJ;EAclBhE,gBAAgB,EAAE;CAdpB;;ACAA3F,IAAMmK,EAAE,GAAY;EAClBjF,uBAAuB,EAAE,8BADP;EAElBD,qBAAqB,EAAE,0BAFL;EAGlBqC,uBAAuB,EAAE,qDAHP;EAIlBzB,OAAO,EAAE,SAJS;EAKlBmE,MAAM,EAAE,SALU;EAMlBrC,MAAM,EAAE,QANU;EAOlBsC,OAAO,EAAE,SAPS;EAQlBxC,iBAAiB,EAAE,mBARD;EASlBL,mBAAmB,EAAE,sBATH;EAUlB2C,YAAY,EAAE,cAVI;EAWlBnE,OAAO,EAAE,YAXS;EAYlBgE,oBAAoB,EAAE,sBAZJ;EAalBD,oBAAoB,EAAE,oCAbJ;EAclBhE,gBAAgB,EAAE;CAdpB;;ACAA3F,IAAMoK,EAAE,GAAY;EAClBlF,uBAAuB,EAAE,8BADP;EAElBD,qBAAqB,EAAE,4BAFL;EAGlBqC,uBAAuB,EAAE,qEAHP;EAIlBzB,OAAO,EAAE,UAJS;EAKlBmE,MAAM,EAAE,WALU;EAMlBrC,MAAM,EAAE,OANU;EAOlBsC,OAAO,EAAE,SAPS;EAQlBxC,iBAAiB,EAAE,QARD;EASlBL,mBAAmB,EAAE,UATH;EAUlB2C,YAAY,EAAE,mBAVI;EAWlBnE,OAAO,EAAE,aAXS;EAYlBgE,oBAAoB,EAAE,gBAZJ;EAalBD,oBAAoB,EAAE,uCAbJ;EAclBhE,gBAAgB,EAAE;CAdpB;;ACAA3F,IAAMqK,EAAE,GAAY;EAClBnF,uBAAuB,EAAE,uCADP;EAElBD,qBAAqB,EAAE,qCAFL;EAGlBqC,uBAAuB,EAAE,kEAHP;EAIlBzB,OAAO,EAAE,cAJS;EAKlBmE,MAAM,EAAE,SALU;EAMlBrC,MAAM,EAAE,QANU;EAOlBsC,OAAO,EAAE,SAPS;EAQlBxC,iBAAiB,EAAE,QARD;EASlBL,mBAAmB,EAAE,QATH;EAUlB2C,YAAY,EAAE,4BAVI;EAWlBnE,OAAO,EAAE,eAXS;EAYlBgE,oBAAoB,EAAE,4BAZJ;EAalBD,oBAAoB,EAAE,qDAbJ;EAclBhE,gBAAgB,EAAE;CAdpB;;ACAA3F,IAAMsK,EAAE,GAAY;EAClBpF,uBAAuB,EAAE,eADP;EAElBD,qBAAqB,EAAE,YAFL;EAGlBqC,uBAAuB,EAAE,mCAHP;EAIlBzB,OAAO,EAAE,IAJS;EAKlBmE,MAAM,EAAE,OALU;EAMlBrC,MAAM,EAAE,KANU;EAOlBsC,OAAO,EAAE,MAPS;EAQlBxC,iBAAiB,EAAE,UARD;EASlBL,mBAAmB,EAAE,gBATH;EAUlB2C,YAAY,EAAE,QAVI;EAWlBnE,OAAO,EAAE,SAXS;EAYlBgE,oBAAoB,EAAE,aAZJ;EAalBD,oBAAoB,EAAE,kBAbJ;EAclBhE,gBAAgB,EAAE;CAdpB;;ACAA3F,IAAMuK,EAAE,GAAY;EAClBrF,uBAAuB,EAAE,gCADP;EAElBD,qBAAqB,EAAE,8BAFL;EAGlBqC,uBAAuB,EAAE,+DAHP;EAIlBzB,OAAO,EAAE,UAJS;EAKlBmE,MAAM,EAAE,WALU;EAMlBrC,MAAM,EAAE,OANU;EAOlBsC,OAAO,EAAE,SAPS;EAQlBxC,iBAAiB,EAAE,QARD;EASlBL,mBAAmB,EAAE,UATH;EAUlB2C,YAAY,EAAE,mBAVI;EAWlBnE,OAAO,EAAE,iBAXS;EAYlBgE,oBAAoB,EAAE,iBAZJ;EAalBD,oBAAoB,EAAE,mCAbJ;EAclBhE,gBAAgB,EAAE;CAdpB;;ACAA3F,IAAMwK,EAAE,GAAY;EAClBtF,uBAAuB,EAAE,QADP;EAElBD,qBAAqB,EAAE,UAFL;EAGlBqC,uBAAuB,EAAE,6BAHP;EAIlBzB,OAAO,EAAE,IAJS;EAKlBmE,MAAM,EAAE,KALU;EAMlBrC,MAAM,EAAE,IANU;EAOlBsC,OAAO,EAAE,IAPS;EAQlBxC,iBAAiB,EAAE,QARD;EASlBL,mBAAmB,EAAE,WATH;EAUlB2C,YAAY,EAAE,UAVI;EAWlBnE,OAAO,EAAE,SAXS;EAYlBgE,oBAAoB,EAAE,MAZJ;EAalBD,oBAAoB,EAAE,YAbJ;EAclBhE,gBAAgB,EAAE;CAdpB;;ACAA3F,IAAMyK,EAAE,GAAY;EAClBvF,uBAAuB,EAAE,qCADP;EAElBD,qBAAqB,EAAE,4BAFL;EAGlBqC,uBAAuB,EAAE,0DAHP;EAIlBzB,OAAO,EAAE,OAJS;EAKlBmE,MAAM,EAAE,OALU;EAMlBrC,MAAM,EAAE,MANU;EAOlBsC,OAAO,EAAE,QAPS;EAQlBxC,iBAAiB,EAAE,kBARD;EASlBL,mBAAmB,EAAE,sBATH;EAUlB2C,YAAY,EAAE,WAVI;EAWlBnE,OAAO,EAAE,aAXS;EAYlBgE,oBAAoB,EAAE,eAZJ;EAalBD,oBAAoB,EAAE,6CAbJ;EAclBhE,gBAAgB,EAAE;CAdpB;;ACSA3F,IAAM0K,SAAS,GAAgC;MAAER,EAAF;MAAMC,EAAN;MAAUC,EAAV;MAAcC,EAAd;MAAkBC,EAAlB;MAAsBC,EAAtB;MAA0BC,EAA1B;MAA8BC;CAA7E;;ACQA,SAASE,gBAAT;EACE3K,IAAM4K,GAAG,GAAGC,+BAAkB,EAA9B;EACA7K,IAAM8K,IAAI,GAAGF,GAAG,CAACG,cAAJ,CAAmB9J,sBAAnB,CAAb;;EACA,IAAI6J,IAAJ,EAAU;IACRF,GAAG,CAACI,IAAJ,CAASC,WAAT,CAAqBH,IAArB;;;EAEF9K,IAAMkC,KAAK,GAAG0I,GAAG,CAACM,aAAJ,CAAkB,OAAlB,CAAd;EACAhJ,KAAK,CAACiJ,YAAN,CAAmB,IAAnB,EAAyBlK,sBAAzB;EACAiB,KAAK,CAACkJ,SAAN,GAAkBnL,yBAAlB;EACA2K,GAAG,CAACI,IAAJ,CAASK,WAAT,CAAqBnJ,KAArB;;;AAGF,SAASoJ,aAAT;EACEtL,IAAM4K,GAAG,GAAGC,+BAAkB,EAA9B;EACA7K,IAAMuL,OAAO,GAAGX,GAAG,CAACM,aAAJ,CAAkB,KAAlB,CAAhB;EACAK,OAAO,CAACJ,YAAR,CAAqB,IAArB,EAA2BnK,wBAA3B;EACA4J,GAAG,CAACpK,IAAJ,CAAS6K,WAAT,CAAqBE,OAArB;EACA,OAAOA,OAAP;;;AAGF,SAASC,qBAAT;EACExL,IAAM4K,GAAG,GAAGC,+BAAkB,EAA9B;EACA7K,IAAMyL,KAAK,GAAGb,GAAG,CAACG,cAAJ,CAAmB7J,sBAAnB,CAAd;;EACA,IAAIuK,KAAJ,EAAW;IACTA,KAAK,CAAChK,SAAN,GAAkBgK,KAAK,CAAChK,SAAN,CAAgBkF,OAAhB,CAAwB,QAAxB,EAAkC,SAAlC,CAAlB;IACA5B,UAAU;MACR/E,IAAMuL,OAAO,GAAGX,GAAG,CAACG,cAAJ,CAAmB/J,wBAAnB,CAAhB;;MACA,IAAIuK,OAAJ,EAAa;QACXX,GAAG,CAACpK,IAAJ,CAASyK,WAAT,CAAqBM,OAArB;;KAHM,EAKPzK,kBALO,CAAV;;;;AASJ,SAAS4K,kBAAT,CAA4BC,EAA5B;EACE;IACEH,qBAAqB;;IACrB,IAAIG,EAAJ,EAAQ;MACNA,EAAE;;GAHN;;;AAQF,SAASC,OAAT;EACE5L,IAAM6L,IAAI,GAAGC,gCAAmB,GAAGC,QAAtB,CAA+BC,KAA/B,CAAqC,GAArC,EAA0C,CAA1C,KAAgD,IAA7D;EACA,OAAOC,SAAS,CAACJ,IAAD,CAAT,IAAmBI,SAAS,CAAC,IAAD,CAAnC;;;AAGF,SAAgBzM,OAAKC,KAAakM,IAAS7D;EACzC6C,gBAAgB;EAChB3K,IAAMuL,OAAO,GAAGD,aAAa,EAA7B;EACAY,YAAA,CACE1K,mBAAA,CAACkG,KAAD;IACE1C,IAAI,EAAE4G,OAAO;IACbnM,GAAG,EAAEA;IACLmC,OAAO,EAAE8J,kBAAkB,CAACC,EAAD;IAC3B7D,kBAAkB,EAAEA;GAJtB,CADF,EAOEyD,OAPF;;AAWF,SAAgBY;EACdX,qBAAqB;;;AC7EvBxL,IAAMoM,MAAM,wBACV,OAAOC,OAAP,KAAmB,WAAnB,IACA,OAAOA,OAAO,CAACC,QAAf,KAA4B,WAD5B,IAEA,OAAOD,OAAO,CAACC,QAAR,CAAiBC,IAAxB,KAAiC,cAHnC;;AAKA,SAAS/M,MAAT,CAAcC,GAAd,EAA2BkM,EAA3B,EAAoC7D,kBAApC;EAEEhI,OAAO,CAACC,GAAR,CAAYN,GAAZ;;EACA,IAAI2M,MAAM,EAAV,EAAc;IACZI,IAAA,CAAa/M,GAAb;GADF,MAEO;IACLgN,MAAA,CAAgBhN,GAAhB,EAAqBkM,EAArB,EAAyB7D,kBAAzB;;;;AAIJ,SAASqE,OAAT;EACE,IAAIC,MAAM,EAAV,EAAc,CAAd,MAEO;IACLK,OAAA;;;;AAIJ,YAAe;QAAEjN,MAAF;SAAQ2M;CAAvB;;;;"}