import React, { useState, useEffect } from 'react';
import Web3 from 'web3';
import Header from './Header';
import ElectionList from './ElectionList';
import ElectionDetail from './ElectionDetail';
import CandidateList from './CandidateList';
import VoterRegistration from './VoterRegistration';
import VotingBooth from './VotingBooth';
import AuthModal from './AuthModal';
import Login from './Login';
import Statistics from './Statistics';
import AdvancedAnalytics from './AdvancedAnalytics';
import Profile from './Profile';
import WorkerLogin from './WorkerLogin';
import WorkerDashboard from './WorkerDashboard';
import WorkerVoterRegistration from './WorkerVoterRegistration';
import './styles.css';

const App = () => {
    const [web3, setWeb3] = useState(null);
    const [account, setAccount] = useState('');
    const [contract, setContract] = useState(null);
    const [elections, setElections] = useState([]); // Initialize with empty array
    const [selectedElection, setSelectedElection] = useState(null);
    const [candidates, setCandidates] = useState([]);
    const [selectedCandidate, setSelectedCandidate] = useState(null);
    const [voter, setVoter] = useState(null);
    const [view, setView] = useState('home'); // 'home', 'elections', 'election-detail', 'candidates', 'voting', 'register', 'profile', 'results', 'login', 'statistics', 'analytics', 'worker-login', 'worker-dashboard', 'worker-register-voter'
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState('');
    const [showAuthModal, setShowAuthModal] = useState(false);
    const [connectionType, setConnectionType] = useState(''); // 'metamask', 'walletconnect', 'sms', 'guest'
    const [isAuthenticated, setIsAuthenticated] = useState(false);
    const [authToken, setAuthToken] = useState(localStorage.getItem('authToken') || '');

    // Worker state
    const [isWorkerAuthenticated, setIsWorkerAuthenticated] = useState(false);
    const [workerData, setWorkerData] = useState(null);
    const [workerToken, setWorkerToken] = useState(localStorage.getItem('workerToken') || '');

    // Fetch voter info when account changes
    useEffect(() => {
        if (account) {
            fetchVoterInfo();
        }
    }, [account]);

    // Initialize Web3
    useEffect(() => {
        const initWeb3 = async () => {
            try {
                console.log("Initializing Web3...");

                // Check for MetaMask or other Ethereum providers
                if (window.ethereum) {
                    console.log("Modern Ethereum provider detected (window.ethereum)");

                    // Check if it's MetaMask specifically
                    const isMetaMask = window.ethereum.isMetaMask;
                    console.log(`MetaMask detected: ${isMetaMask ? 'Yes' : 'No'}`);

                    try {
                        const web3Instance = new Web3(window.ethereum);
                        setWeb3(web3Instance);

                        // Detect MetaMask status
                        let metaMaskStatus = "Unknown";
                        try {
                            // Check if MetaMask is unlocked
                            if (window.ethereum._metamask && typeof window.ethereum._metamask.isUnlocked === 'function') {
                                const isUnlocked = await window.ethereum._metamask.isUnlocked();
                                metaMaskStatus = isUnlocked ? "Unlocked" : "Locked";
                                console.log(`MetaMask status: ${metaMaskStatus}`);
                            }
                        } catch (statusError) {
                            console.warn("Could not determine MetaMask lock status:", statusError);
                        }

                        // We won't automatically request accounts on page load
                        // This is better UX - let the user click the connect button instead
                        console.log("Web3 initialized in read-only mode. User can connect wallet manually.");

                        // Check if already connected (for returning users)
                        try {
                            console.log("Checking for existing connections...");
                            const accounts = await window.ethereum.request({
                                method: 'eth_accounts' // This doesn't trigger the MetaMask popup
                            });

                            if (accounts && accounts.length > 0) {
                                console.log("Found existing connection:", accounts[0]);
                                setAccount(accounts[0]);

                                // Load contract with the connected account
                                await loadContract(web3Instance);

                                // Show a notification that we're connected
                                const shortAccount = `${accounts[0].substring(0, 6)}...${accounts[0].substring(accounts[0].length - 4)}`;
                                console.log(`Already connected to MetaMask account: ${shortAccount}`);
                            } else {
                                console.log("No existing connection found. User needs to connect manually.");

                                // If MetaMask is detected but not connected, show a more specific message
                                if (isMetaMask) {
                                    console.log("MetaMask is installed but not connected to this site.");
                                }

                                // Just load elections without an account
                                fetchElections();
                                setIsLoading(false);
                            }
                        } catch (accountError) {
                            console.warn("Error checking for existing accounts:", accountError);
                            // Just load elections without an account
                            fetchElections();
                            setIsLoading(false);
                        }

                        // Set up event listeners for account changes
                        window.ethereum.on('accountsChanged', (accounts) => {
                            console.log('MetaMask account changed:', accounts[0]);
                            if (accounts.length > 0) {
                                setAccount(accounts[0]);

                                // Reload contract when account changes
                                loadContract(web3Instance);
                            } else {
                                // User disconnected all accounts
                                setAccount('');
                                console.log('User disconnected from MetaMask');
                            }
                        });

                        // Set up event listeners for chain changes
                        window.ethereum.on('chainChanged', (chainId) => {
                            console.log('MetaMask network changed to chain ID:', chainId);
                            // Reload the page when the chain changes
                            window.location.reload();
                        });

                        // Set up event listener for connection
                        window.ethereum.on('connect', (connectInfo) => {
                            console.log('MetaMask connected:', connectInfo);
                        });

                        // Set up event listener for disconnection
                        window.ethereum.on('disconnect', (error) => {
                            console.log('MetaMask disconnected:', error);
                            setAccount('');
                        });

                    } catch (error) {
                        console.warn('Error initializing Web3 with window.ethereum:', error);
                        // Proceed in read-only mode
                        fetchElections();
                        setIsLoading(false);
                    }
                }
                // Legacy dapp browsers
                else if (window.web3) {
                    console.log("Legacy Web3 provider detected (window.web3)");

                    try {
                        const web3Instance = new Web3(window.web3.currentProvider);
                        setWeb3(web3Instance);

                        // Check for existing accounts
                        const accounts = await web3Instance.eth.getAccounts();
                        if (accounts && accounts.length > 0) {
                            console.log("Found existing connection (legacy):", accounts[0]);
                            setAccount(accounts[0]);

                            // Load contract with the connected account
                            await loadContract(web3Instance);
                        } else {
                            console.log("No existing connection found (legacy). User needs to connect manually.");
                            // Just load elections without an account
                            fetchElections();
                            setIsLoading(false);
                        }
                    } catch (error) {
                        console.warn('Error with legacy Web3:', error);
                        // Proceed in read-only mode
                        fetchElections();
                        setIsLoading(false);
                    }
                }
                // No Web3 provider - use demo mode
                else {
                    console.log("No Web3 provider detected. Using demo mode.");

                    try {
                        // Try to connect to local node (for development)
                        const provider = new Web3.providers.HttpProvider('http://127.0.0.1:8545');
                        const web3Instance = new Web3(provider);

                        // Check if connected
                        const isListening = await web3Instance.eth.net.isListening();
                        if (isListening) {
                            console.log("Connected to local Ethereum node");
                            setWeb3(web3Instance);

                            // Get accounts from local node
                            const accounts = await web3Instance.eth.getAccounts();
                            if (accounts && accounts.length > 0) {
                                console.log("Using local account:", accounts[0]);
                                setAccount(accounts[0]);
                            } else {
                                console.log("No local accounts found. Using demo account.");
                                setAccount('******************************************');
                            }

                            // Load contract
                            await loadContract(web3Instance);
                        } else {
                            throw new Error("Local node not responding");
                        }
                    } catch (error) {
                        console.log('Could not connect to local node:', error.message);
                        console.log('Proceeding in full demo mode');

                        // Use a mock account for demo purposes
                        setAccount('******************************************');
                        fetchElections();
                        setIsLoading(false);
                    }
                }
            } catch (error) {
                console.error('Unexpected error initializing Web3:', error);
                // Don't show error, just load in demo mode with a mock account
                setAccount('******************************************');
                fetchElections();
                setIsLoading(false);
            }
        };

        initWeb3();

        // Cleanup function to remove event listeners
        return () => {
            if (window.ethereum) {
                window.ethereum.removeAllListeners('accountsChanged');
                window.ethereum.removeAllListeners('chainChanged');
            }
        };
    }, []);

    // Load contract
    const loadContract = async (web3Instance) => {
        try {
            console.log("Loading smart contract...");

            // Make sure web3Instance is valid
            if (!web3Instance || !web3Instance.eth) {
                console.warn('Invalid Web3 instance provided to loadContract');
                fetchElections();
                setIsLoading(false);
                return;
            }

            // Check if we're on the right network
            try {
                const networkId = await web3Instance.eth.net.getId();
                console.log(`Connected to network ID: ${networkId}`);

                // For development, we typically use network ID 1337 (local) or 5777 (Ganache)
                // For production, we'd check for specific networks like Ethereum Mainnet (1) or testnets
                if (![1, 3, 4, 5, 42, 1337, 5777].includes(networkId)) {
                    console.warn(`Connected to network ID ${networkId}, which may not be supported`);
                }
            } catch (networkError) {
                console.warn('Error checking network ID:', networkError);
            }

            // Fetch contract data from backend
            console.log("Fetching contract info from API...");
            const response = await fetch('/api/contract-info/');

            if (!response.ok) {
                console.warn(`Contract info API returned status ${response.status}: ${response.statusText}`);
                fetchElections();
                setIsLoading(false);
                return;
            }

            const data = await response.json();
            console.log("Contract info received:", data.address ? `Address: ${data.address.substring(0, 10)}...` : "No address");

            if (data.address && data.abi) {
                try {
                    console.log("Creating contract instance...");
                    const contractInstance = new web3Instance.eth.Contract(
                        data.abi,
                        data.address
                    );

                    // Verify the contract exists on the blockchain
                    try {
                        const code = await web3Instance.eth.getCode(data.address);
                        if (code === '0x' || code === '0x0') {
                            console.warn(`No contract found at address ${data.address}`);
                            setError(`No smart contract found at the provided address. The application will run in demo mode.`);
                        } else {
                            console.log("Contract verified on blockchain");
                            setContract(contractInstance);
                        }
                    } catch (verifyError) {
                        console.warn('Error verifying contract on blockchain:', verifyError);
                        setContract(contractInstance); // Still set the contract and try to use it
                    }
                } catch (error) {
                    console.warn('Error creating contract instance:', error);
                    setError(`Error connecting to the voting smart contract: ${error.message}. The application will run in demo mode.`);
                }

                // Load elections regardless of contract status
                fetchElections();
                // Check if user is registered as a voter
                fetchVoterInfo();
            } else {
                console.warn('Contract information incomplete:', data);
                fetchElections();
                setIsLoading(false);
            }
        } catch (error) {
            console.warn('Error loading contract:', error);
            setError(`Error loading the voting smart contract: ${error.message}. The application will run in demo mode.`);
            fetchElections();
            setIsLoading(false);
        }
    };

    // Fetch elections from backend
    const fetchElections = async () => {
        try {
            const response = await fetch('/api/elections/');

            if (!response.ok) {
                console.warn('Elections API not available, loading demo data');
                // Load demo data
                const demoElections = [
                    {
                        id: 1,
                        title: 'Presidential Election 2023',
                        description: 'General election for the President of Kenya',
                        election_type: { id: 1, name: 'Presidential' },
                        start_date: '2023-08-01T00:00:00Z',
                        end_date: '2023-08-08T23:59:59Z',
                        active: true,
                        contract_election_id: '1'
                    },
                    {
                        id: 2,
                        title: 'Parliamentary Elections 2023',
                        description: 'Elections for Members of Parliament',
                        election_type: { id: 2, name: 'Parliamentary' },
                        start_date: '2023-08-01T00:00:00Z',
                        end_date: '2023-08-08T23:59:59Z',
                        active: true,
                        contract_election_id: '2'
                    },
                    {
                        id: 3,
                        title: 'Gubernatorial Elections 2023',
                        description: 'Elections for County Governors',
                        election_type: { id: 4, name: 'Governor' },
                        start_date: '2023-08-01T00:00:00Z',
                        end_date: '2023-08-08T23:59:59Z',
                        active: true,
                        contract_election_id: '3'
                    },
                    {
                        id: 4,
                        title: 'Presidential Election 2018',
                        description: 'Past general election for the President of Kenya',
                        election_type: { id: 1, name: 'Presidential' },
                        start_date: '2018-08-01T00:00:00Z',
                        end_date: '2018-08-08T23:59:59Z',
                        active: false,
                        contract_election_id: '4'
                    }
                ];
                setElections(demoElections);
                setIsLoading(false);
                return;
            }

            const data = await response.json();
            // Check if the data is paginated (DRF default format)
            if (data && data.results && Array.isArray(data.results)) {
                console.log('Elections API returned paginated data with', data.results.length, 'elections');
                setElections(data.results);
            } else if (Array.isArray(data)) {
                console.log('Elections API returned array with', data.length, 'elections');
                setElections(data);
            } else {
                console.warn('Elections API returned unexpected format:', data);
                // Use empty array if the API returns unexpected format
                setElections([]);
            }
            setIsLoading(false);
        } catch (error) {
            console.warn('Error fetching elections, loading demo data:', error);
            // Load demo data
            const demoElections = [
                {
                    id: 1,
                    title: 'Presidential Election 2023',
                    description: 'General election for the President of Kenya',
                    election_type: { id: 1, name: 'Presidential' },
                    start_date: '2023-08-01T00:00:00Z',
                    end_date: '2023-08-08T23:59:59Z',
                    active: true,
                    contract_election_id: '1'
                },
                {
                    id: 2,
                    title: 'Parliamentary Elections 2023',
                    description: 'Elections for Members of Parliament',
                    election_type: { id: 2, name: 'Parliamentary' },
                    start_date: '2023-08-01T00:00:00Z',
                    end_date: '2023-08-08T23:59:59Z',
                    active: true,
                    contract_election_id: '2'
                },
                {
                    id: 3,
                    title: 'Gubernatorial Elections 2023',
                    description: 'Elections for County Governors',
                    election_type: { id: 4, name: 'Governor' },
                    start_date: '2023-08-01T00:00:00Z',
                    end_date: '2023-08-08T23:59:59Z',
                    active: true,
                    contract_election_id: '3'
                },
                {
                    id: 4,
                    title: 'Presidential Election 2018',
                    description: 'Past general election for the President of Kenya',
                    election_type: { id: 1, name: 'Presidential' },
                    start_date: '2018-08-01T00:00:00Z',
                    end_date: '2018-08-08T23:59:59Z',
                    active: false,
                    contract_election_id: '4'
                }
            ];
            setElections(demoElections);
            setIsLoading(false);
        }
    };

    // Fetch voter information
    const fetchVoterInfo = async () => {
        // Only try to fetch voter info if we have an account
        if (!account) {
            console.info('No account connected, skipping voter info fetch');
            setVoter(null);
            return;
        }

        try {
            const response = await fetch('/api/voters/me/');
            if (response.ok) {
                const data = await response.json();
                setVoter(data);
            } else if (response.status === 404) {
                // 404 means user is not registered as a voter, which is fine
                console.info('User not registered as a voter');
                setVoter(null);
            } else if (response.status === 401 || response.status === 403) {
                // Unauthorized or Forbidden - user needs to authenticate
                console.info('Authentication required for voter info');
                setVoter(null);
            } else {
                console.warn('Error fetching voter info, using demo mode:', response.statusText);
                // In demo mode, we'll simulate that the user is not registered
                setVoter(null);
            }
        } catch (error) {
            console.warn('Error fetching voter info, using demo mode:', error);
            // In demo mode, we'll simulate that the user is not registered
            setVoter(null);
        }
    };

    // Fetch candidates for a specific election
    const fetchCandidates = async (electionId) => {
        try {
            const response = await fetch(`/api/elections/${electionId}/candidates/`);

            if (!response.ok) {
                console.warn('Candidates API not available, loading demo data');
                // Load demo data based on election ID
                const demoCandidates = [
                    {
                        id: 1,
                        user: { first_name: 'William', last_name: 'Ruto' },
                        party: { id: 1, name: 'United Democratic Alliance', abbreviation: 'UDA' },
                        election: { id: electionId },
                        constituency: null,
                        county: null,
                        bio: 'Experienced leader with a vision for Kenya\'s future.',
                        photo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/2/2f/William_Ruto_in_2022.jpg/220px-William_Ruto_in_2022.jpg',
                        is_independent: false,
                        is_approved: true,
                        votes_count: 7176141,
                        contract_candidate_id: '1'
                    },
                    {
                        id: 2,
                        user: { first_name: 'Raila', last_name: 'Odinga' },
                        party: { id: 2, name: 'Orange Democratic Movement', abbreviation: 'ODM' },
                        election: { id: electionId },
                        constituency: null,
                        county: null,
                        bio: 'Long-time political leader fighting for democratic reforms.',
                        photo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/f3/Raila_Odinga_in_2022.jpg/220px-Raila_Odinga_in_2022.jpg',
                        is_independent: false,
                        is_approved: true,
                        votes_count: 6942930,
                        contract_candidate_id: '2'
                    },
                    {
                        id: 3,
                        user: { first_name: 'George', last_name: 'Wajackoyah' },
                        party: { id: 3, name: 'Roots Party', abbreviation: 'RP' },
                        election: { id: electionId },
                        constituency: null,
                        county: null,
                        bio: 'Advocate for alternative economic policies and reforms.',
                        photo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/e/e7/George_Wajackoyah_in_2022.jpg/220px-George_Wajackoyah_in_2022.jpg',
                        is_independent: false,
                        is_approved: true,
                        votes_count: 61969,
                        contract_candidate_id: '3'
                    },
                    {
                        id: 4,
                        user: { first_name: 'David', last_name: 'Mwaure' },
                        party: { id: 4, name: 'Agano Party', abbreviation: 'AP' },
                        election: { id: electionId },
                        constituency: null,
                        county: null,
                        bio: 'Advocate for ethical leadership and governance.',
                        photo: null,
                        is_independent: false,
                        is_approved: true,
                        votes_count: 31987,
                        contract_candidate_id: '4'
                    }
                ];
                setCandidates(demoCandidates);
                return;
            }

            const data = await response.json();
            setCandidates(data);
        } catch (error) {
            console.warn('Error fetching candidates, loading demo data:', error);
            // Load demo data based on election ID
            const demoCandidates = [
                {
                    id: 1,
                    user: { first_name: 'William', last_name: 'Ruto' },
                    party: { id: 1, name: 'United Democratic Alliance', abbreviation: 'UDA' },
                    election: { id: electionId },
                    constituency: null,
                    county: null,
                    bio: 'Experienced leader with a vision for Kenya\'s future.',
                    photo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/2/2f/William_Ruto_in_2022.jpg/220px-William_Ruto_in_2022.jpg',
                    is_independent: false,
                    is_approved: true,
                    votes_count: 7176141,
                    contract_candidate_id: '1'
                },
                {
                    id: 2,
                    user: { first_name: 'Raila', last_name: 'Odinga' },
                    party: { id: 2, name: 'Orange Democratic Movement', abbreviation: 'ODM' },
                    election: { id: electionId },
                    constituency: null,
                    county: null,
                    bio: 'Long-time political leader fighting for democratic reforms.',
                    photo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/f3/Raila_Odinga_in_2022.jpg/220px-Raila_Odinga_in_2022.jpg',
                    is_independent: false,
                    is_approved: true,
                    votes_count: 6942930,
                    contract_candidate_id: '2'
                },
                {
                    id: 3,
                    user: { first_name: 'George', last_name: 'Wajackoyah' },
                    party: { id: 3, name: 'Roots Party', abbreviation: 'RP' },
                    election: { id: electionId },
                    constituency: null,
                    county: null,
                    bio: 'Advocate for alternative economic policies and reforms.',
                    photo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/e/e7/George_Wajackoyah_in_2022.jpg/220px-George_Wajackoyah_in_2022.jpg',
                    is_independent: false,
                    is_approved: true,
                    votes_count: 61969,
                    contract_candidate_id: '3'
                },
                {
                    id: 4,
                    user: { first_name: 'David', last_name: 'Mwaure' },
                    party: { id: 4, name: 'Agano Party', abbreviation: 'AP' },
                    election: { id: electionId },
                    constituency: null,
                    county: null,
                    bio: 'Advocate for ethical leadership and governance.',
                    photo: null,
                    is_independent: false,
                    is_approved: true,
                    votes_count: 31987,
                    contract_candidate_id: '4'
                }
            ];
            setCandidates(demoCandidates);
        }
    };

    // Handle election selection
    const handleElectionSelect = (election) => {
        setSelectedElection(election);
        setView('election-detail');
    };

    // Handle candidate selection
    const handleCandidateSelect = (candidate) => {
        setSelectedCandidate(candidate);
        setView('voting');
    };

    // Handle viewing candidates for an election
    const handleViewCandidates = async (electionId) => {
        await fetchCandidates(electionId);
        setView('candidates');
    };

    // Handle vote submission
    const handleVote = async (candidateId, electionId) => {
        try {
            if (!contract || !account) {
                setError('Web3 or account not initialized');
                return;
            }

            // Call the vote function on the smart contract
            await contract.methods.vote(electionId, candidateId).send({ from: account });

            // Update the vote in the backend
            await fetch(`/api/elections/${electionId}/vote/${candidateId}/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken'),
                },
                body: JSON.stringify({ account }),
            });

            // Refresh elections
            fetchElections();

            // Show results view
            setView('results');
        } catch (error) {
            console.error('Error voting:', error);
            setError('Error submitting vote. Please try again.');
        }
    };

    // Handle voter registration
    const handleVoterRegistration = async (registrationData) => {
        try {
            if (!account) {
                setError('Web3 not initialized');
                return;
            }

            // Register voter in the backend
            const response = await fetch('/api/voters/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken'),
                },
                body: JSON.stringify({
                    ...registrationData,
                    blockchain_address: account
                }),
            });

            if (response.ok) {
                const data = await response.json();
                setVoter(data);
                setView('elections');
            } else {
                const errorData = await response.json();
                setError(errorData.detail || 'Registration failed. Please try again.');
            }
        } catch (error) {
            console.error('Error registering voter:', error);
            setError('Error registering voter. Please try again.');
        }
    };

    // Handle navigation
    const handleNavigate = (newView, data = null) => {
        console.log(`Navigating to: ${newView}`, data);

        // Reset selected items when navigating away from detail views
        if (newView === 'home' || newView === 'elections') {
            setSelectedElection(null);
            setSelectedCandidate(null);
        }

        // If navigating to elections, fetch the latest data
        if (newView === 'elections') {
            fetchElections();
        }

        // If navigating to profile, fetch voter info
        if (newView === 'profile') {
            fetchVoterInfo();
        }

        // Handle data based on the view
        if (newView === 'election-detail' && data) {
            setSelectedElection(data);
        } else if (newView === 'candidates' && data) {
            setSelectedElection(data);
        } else if (newView === 'voting' && data) {
            setSelectedCandidate(data);
        }

        // Update the view
        setView(newView);
    };

    // Handle login success
    const handleLoginSuccess = (token, userData) => {
        setAuthToken(token);
        setIsAuthenticated(true);

        // Set Authorization header for future requests
        if (token) {
            // You could set up axios defaults here if using axios
            // axios.defaults.headers.common['Authorization'] = `Token ${token}`;

            // Or store for fetch API usage
            localStorage.setItem('authToken', token);

            // If we have voter data, update the voter state
            if (userData && userData.voter) {
                setVoter(userData.voter);
            } else {
                // Fetch voter info in case it's available
                fetchVoterInfo();
            }
        }

        // Navigate to home or dashboard
        handleNavigate('home');
    };

    // Handle worker login success
    const handleWorkerLoginSuccess = (workerAuthData) => {
        setWorkerToken(workerAuthData.token);
        setIsWorkerAuthenticated(true);
        setWorkerData({
            user_id: workerAuthData.user_id,
            username: workerAuthData.username,
            worker_id: workerAuthData.worker_id,
            employee_id: workerAuthData.employee_id,
            station_assigned: workerAuthData.station_assigned
        });

        // Navigate to worker dashboard
        handleNavigate('worker-dashboard');
    };

    // Handle worker logout
    const handleWorkerLogout = () => {
        setWorkerToken('');
        setIsWorkerAuthenticated(false);
        setWorkerData(null);

        // Remove stored data
        localStorage.removeItem('workerToken');
        localStorage.removeItem('workerData');

        // Navigate to home
        handleNavigate('home');
    };

    // Handle successful voter registration by worker
    const handleWorkerVoterRegistrationSuccess = (registrationData) => {
        // Show success message
        alert(`Voter registered successfully!\nUsername: ${registrationData.username}\nTemporary Password: ${registrationData.temporary_password}\n\nPlease inform the voter of their login credentials.`);

        // Navigate back to worker dashboard
        handleNavigate('worker-dashboard');
    };

    // Note: handleLogout is defined at the bottom of the file

    // Handle connecting to wallet
    const handleConnectWallet = async () => {
        // Show the authentication modal
        setShowAuthModal(true);
    };

    // Handle successful connection from any method
    const handleAuthSuccess = async (web3Instance, userAccount, type) => {
        try {
            console.log(`Connected with ${type}. Account: ${userAccount}`);

            // Clear any previous errors
            setError('');

            // Set the web3 instance and account
            setWeb3(web3Instance);
            setAccount(userAccount);
            setConnectionType(type);

            // Close the modal
            setShowAuthModal(false);

            // Load contract if not in guest mode
            if (type !== 'guest') {
                await loadContract(web3Instance);
            } else {
                // In guest mode, just load elections
                fetchElections();
                setIsLoading(false);
            }

            // Show success message
            const shortAccount = `${userAccount.substring(0, 6)}...${userAccount.substring(userAccount.length - 4)}`;
            const connectionMethod = {
                'metamask': 'MetaMask',
                'walletconnect': 'WalletConnect',
                'sms': 'Phone Verification',
                'guest': 'Guest Mode'
            }[type] || 'Wallet';

            alert(`Successfully connected with ${connectionMethod}: ${shortAccount}`);
        } catch (error) {
            console.error('Error after wallet connection:', error);
            setError(`Error after wallet connection: ${error.message || 'Unknown error'}`);
        }
    };

    // Handle authentication error
    const handleAuthError = (errorMessage) => {
        setError(errorMessage);
    };

    // Handle viewing election results
    const handleViewResults = (electionId) => {
        // Store the election ID for reference
        console.log(`Viewing results for election ${electionId}`);
        setView('results');
    };

    // Helper function to get CSRF token
    const getCookie = (name) => {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    };

    // Render the appropriate view
    const renderView = () => {
        if (isLoading) {
            return (
                <div className="loading-container">
                    <div className="spinner-border loading-spinner text-primary" role="status">
                        <span className="visually-hidden">Loading...</span>
                    </div>
                    <p className="mt-3">Loading election data...</p>
                </div>
            );
        }

        if (error) {
            // Special handling for MetaMask error
            if (error.includes('MetaMask')) {
                return (
                    <div className="container mt-4">
                        <div className="row justify-content-center">
                            <div className="col-md-8">
                                <div className="card">
                                    <div className="card-header bg-warning">
                                        <h4 className="mb-0">Blockchain Connection Required</h4>
                                    </div>
                                    <div className="card-body">
                                        <div className="row">
                                            <div className="col-md-3 text-center mb-3 mb-md-0">
                                                <img
                                                    src="https://upload.wikimedia.org/wikipedia/commons/thumb/3/36/MetaMask_Fox.svg/120px-MetaMask_Fox.svg.png"
                                                    alt="MetaMask Logo"
                                                    className="img-fluid"
                                                    style={{ maxWidth: '100px' }}
                                                />
                                            </div>
                                            <div className="col-md-9">
                                                <p>For the full blockchain voting experience, you'll need to install the MetaMask browser extension.</p>
                                                <p>MetaMask allows you to securely connect to the Ethereum blockchain and cast verifiable votes.</p>
                                                <div className="d-grid gap-2">
                                                    <a
                                                        href="https://metamask.io/download/"
                                                        target="_blank"
                                                        rel="noopener noreferrer"
                                                        className="btn btn-primary"
                                                    >
                                                        Install MetaMask
                                                    </a>
                                                    <button
                                                        className="btn btn-outline-secondary"
                                                        onClick={() => setError('')}
                                                    >
                                                        Continue in Demo Mode
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                );
            }

            // For other errors
            return (
                <div className="alert alert-danger mt-3" role="alert">
                    <h4 className="alert-heading">Error</h4>
                    <p>{error}</p>
                    <hr />
                    <p className="mb-0">
                        <button
                            className="btn btn-outline-danger btn-sm"
                            onClick={() => setError('')}
                        >
                            Continue in Demo Mode
                        </button>
                    </p>
                </div>
            );
        }

        // If user is not authenticated, prompt them to log in first
        if (!isAuthenticated && (view === 'voting' || view === 'candidates')) {
            return (
                <div className="alert alert-warning">
                    <h4 className="alert-heading">Login Required</h4>
                    <p>You need to log in before you can participate in elections. If you don't have an account, please visit a registration center to register as a voter.</p>
                    <hr />
                    <button
                        className="btn btn-primary me-2"
                        onClick={() => handleNavigate('login')}
                    >
                        Login
                    </button>
                    <button
                        className="btn btn-outline-info"
                        onClick={() => handleNavigate('worker-login')}
                    >
                        Worker Login
                    </button>
                </div>
            );
        }

        // If user is authenticated but not registered as a voter and trying to vote, show message
        if (isAuthenticated && !voter && (view === 'voting' || view === 'candidates')) {
            return (
                <div className="alert alert-warning">
                    <h4 className="alert-heading">Voter Registration Required</h4>
                    <p>You are logged in, but you need to be registered as a voter by an authorized registration worker before you can participate in elections. Please visit a registration center.</p>
                    <hr />
                    <button
                        className="btn btn-primary"
                        onClick={() => handleNavigate('home')}
                    >
                        Back to Home
                    </button>
                </div>
            );
        }

        switch (view) {
            case 'home':
                return (
                    <div className="fade-in">
                        <div className="jumbotron bg-light p-5 rounded">
                            <h1 className="display-4">Welcome to Kenya's Blockchain Voting System</h1>
                            <p className="lead">
                                This platform ensures secure, transparent, and tamper-proof elections through Ethereum smart contracts.
                            </p>
                            <hr className="my-4" />

                            {isAuthenticated && voter ? (
                                // For registered voters
                                <div className="alert alert-success">
                                    <h4 className="alert-heading">You're Ready to Vote!</h4>
                                    <p>
                                        You are logged in and registered as a voter. You can now participate in active elections.
                                    </p>
                                    <hr />
                                    <div className="d-flex flex-wrap gap-2">
                                        <button
                                            className="btn btn-primary"
                                            onClick={() => handleNavigate('elections')}
                                        >
                                            View Elections and Vote
                                        </button>
                                        <button
                                            className="btn btn-outline-secondary"
                                            onClick={() => handleNavigate('profile')}
                                        >
                                            View My Voter Profile
                                        </button>
                                    </div>
                                </div>
                            ) : isAuthenticated ? (
                                // For logged in users who are not registered as voters
                                <div className="alert alert-warning">
                                    <h4 className="alert-heading">Voter Registration Required</h4>
                                    <p>
                                        You are logged in, but you need to be registered as a voter by an authorized registration worker before you can participate in elections. Please visit a registration center.
                                    </p>
                                    <hr />
                                    <button
                                        className="btn btn-info"
                                        onClick={() => handleNavigate('elections')}
                                    >
                                        View Elections (Read Only)
                                    </button>
                                </div>
                            ) : (
                                // For users who are not logged in
                                <div>
                                    <p>
                                        To get started, you can browse active elections, register as a voter, or log in if you're already registered.
                                    </p>
                                    <div className="d-flex flex-wrap gap-2 mt-4">
                                        <button
                                            className="btn btn-primary btn-lg"
                                            onClick={() => handleNavigate('elections')}
                                        >
                                            View Elections
                                        </button>
                                        <button
                                            className="btn btn-primary btn-lg"
                                            onClick={() => handleNavigate('login')}
                                        >
                                            Voter Login
                                        </button>
                                        <button
                                            className="btn btn-warning btn-lg"
                                            onClick={() => handleNavigate('worker-login')}
                                        >
                                            Worker Login
                                        </button>
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Quick guide section */}
                        <div className="card mt-4">
                            <div className="card-header bg-primary text-white">
                                <h3 className="mb-0">How to Vote</h3>
                            </div>
                            <div className="card-body">
                                <div className="row">
                                    <div className="col-md-4">
                                        <div className="card mb-3">
                                            <div className="card-body text-center">
                                                <h5 className="card-title">Step 1</h5>
                                                <p className="card-text">Log in with your account or register if you're a new voter.</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="col-md-4">
                                        <div className="card mb-3">
                                            <div className="card-body text-center">
                                                <h5 className="card-title">Step 2</h5>
                                                <p className="card-text">Browse active elections and select the one you want to vote in.</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="col-md-4">
                                        <div className="card mb-3">
                                            <div className="card-body text-center">
                                                <h5 className="card-title">Step 3</h5>
                                                <p className="card-text">Select your preferred candidate and confirm your vote.</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                );
            case 'election-detail':
                return (
                    <ElectionDetail
                        election={selectedElection}
                        onBack={() => handleNavigate('elections')}
                        onViewCandidates={handleViewCandidates}
                        onViewResults={handleViewResults}
                    />
                );
            case 'candidates':
                return (
                    <CandidateList
                        candidates={candidates}
                        election={selectedElection}
                        onSelect={handleCandidateSelect}
                        onBack={() => handleNavigate('election-detail')}
                    />
                );
            case 'voting':
                return (
                    <VotingBooth
                        candidate={selectedCandidate}
                        election={selectedElection}
                        onVote={handleVote}
                        onCancel={() => handleNavigate('candidates')}
                        account={account}
                    />
                );
            case 'results':
                return (
                    <ElectionDetail
                        election={selectedElection}
                        showResults={true}
                        onBack={() => handleNavigate('elections')}
                    />
                );
            case 'register':
                return (
                    <VoterRegistration
                        onSubmit={handleVoterRegistration}
                        account={account}
                    />
                );
            case 'profile':
                return (
                    <Profile
                        voter={voter}
                        isAuthenticated={isAuthenticated}
                        onNavigate={handleNavigate}
                    />
                );
            case 'login':
                return (
                    <Login onLoginSuccess={handleLoginSuccess} />
                );

            case 'worker-login':
                return (
                    <WorkerLogin
                        onLoginSuccess={handleWorkerLoginSuccess}
                        onNavigate={handleNavigate}
                    />
                );

            case 'worker-dashboard':
                return (
                    <WorkerDashboard
                        workerData={workerData}
                        onLogout={handleWorkerLogout}
                        onNavigate={handleNavigate}
                    />
                );

            case 'worker-register-voter':
                return (
                    <WorkerVoterRegistration
                        onBack={() => handleNavigate('worker-dashboard')}
                        onSuccess={handleWorkerVoterRegistrationSuccess}
                    />
                );

            case 'statistics':
                return (
                    <Statistics />
                );

            case 'analytics':
                return (
                    <AdvancedAnalytics />
                );

            case 'elections':
            default:
                return (
                    <div className="fade-in">
                        <ElectionList
                            elections={elections}
                            onSelect={handleElectionSelect}
                        />
                    </div>
                );
        }
    };

    // Handle logout
    const handleLogout = () => {
        // Clear authentication state
        setAuthToken('');
        setIsAuthenticated(false);

        // Remove stored data
        localStorage.removeItem('authToken');
        localStorage.removeItem('userData');

        // Navigate to home
        handleNavigate('home');
    };

    return (
        <div className="container">
            <Header
                account={account}
                onConnectWallet={handleConnectWallet}
                onNavigate={handleNavigate}
                currentView={view}
                connectionType={connectionType}
                isAuthenticated={isAuthenticated}
                onLogout={handleLogout}
            />

            {/* Error display */}
            {error && (
                <div className="alert alert-danger alert-dismissible fade show my-3" role="alert">
                    <div className="d-flex align-items-center">
                        <i className="bi bi-exclamation-triangle-fill fs-4 me-2"></i>
                        <div>
                            <strong>Connection Error</strong>
                            <p className="mb-0">{error}</p>
                        </div>
                    </div>
                    <button
                        type="button"
                        className="btn-close"
                        onClick={() => setError('')}
                        aria-label="Close"
                    ></button>
                </div>
            )}

            {renderView()}

            {/* Authentication Modal */}
            <AuthModal
                isOpen={showAuthModal}
                onClose={() => setShowAuthModal(false)}
                onConnect={handleAuthSuccess}
                onError={handleAuthError}
            />
        </div>
    );
};

export default App;
