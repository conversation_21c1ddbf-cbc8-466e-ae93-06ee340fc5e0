# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <PERSON> <<EMAIL>>, 2012
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2014
# <PERSON><PERSON> <<EMAIL>>, 2011,2015
# <PERSON><PERSON><PERSON> <v<PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2016
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2022-05-25 07:05+0000\n"
"Last-Translator: arneatec <<EMAIL>>, 2022\n"
"Language-Team: Bulgarian (http://www.transifex.com/django/django/language/"
"bg/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: bg\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Administrative Documentation"
msgstr "Административна документация"

msgid "Home"
msgstr "Начало"

msgid "Documentation"
msgstr "Документация"

msgid "Bookmarklets"
msgstr "Bookmarklets"

msgid "Documentation bookmarklets"
msgstr "Bookmarklet-и за документация"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"За да инсталирате bookmarklet-и, задърпайте линка в лентата с отметки "
"(bookmarks toolbar), или щракнете с десния бутон и добавете линка в "
"отметките. Сега можете да изберете bookmarklet-а от която и да е страница на "
"сайта."

msgid "Documentation for this page"
msgstr "Документация за тази страница"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Препраща Ви от която и да е страница към документацията за изгледа, който я "
"е генерирал."

msgid "Tags"
msgstr "Тагове"

msgid "List of all the template tags and their functions."
msgstr "Списък на всички шаблонни тагове и техните функции."

msgid "Filters"
msgstr "Филтри"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Филтрите са действия, които могат да се използват върху променливи в даден "
"шаблон, за да променят изхода."

msgid "Models"
msgstr "Модели"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Моделите са описания на всички обекти в системата и свързаните с тях полета. "
"Всеки модел си има списък на полетата, които могат да бъдат достъпени както "
"шаблонни променливи"

msgid "Views"
msgstr "Изгледи"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Всяка страница на публичния сайт се генерира от изглед. Изгледът определя "
"кой шаблон се използва за генериране на страницата и кои обекти са на "
"разположение за този шаблон."

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""
"Инструменти за вашия браузър за бърз достъп до администраторската "
"функционалност."

msgid "Please install docutils"
msgstr "Моля инсталирайте docutils"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a "
"href=\"%(link)s\">docutils</a> library."
msgstr ""
"Системата за администраторска документация изисква библиотеката за Python <a "
"href=\"%(link)s\">docutils</a>."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Моля, помолете вашите администратори да инсталират <a "
"href=\"%(link)s\">docutils</a> ."

#, python-format
msgid "Model: %(name)s"
msgstr "Модел: %(name)s"

msgid "Fields"
msgstr "Полета"

msgid "Field"
msgstr "Поле"

msgid "Type"
msgstr "Тип"

msgid "Description"
msgstr "Описание"

msgid "Methods with arguments"
msgstr "Методи с аргументи"

msgid "Method"
msgstr "Метод"

msgid "Arguments"
msgstr "Аргументи"

msgid "Back to Model documentation"
msgstr " Върни се в документацията за модели"

msgid "Model documentation"
msgstr "Документация за модели"

msgid "Model groups"
msgstr "Групи на модела"

msgid "Templates"
msgstr "Шаблони"

#, python-format
msgid "Template: %(name)s"
msgstr "Шаблон: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Шаблон: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "Път за търсене на шаблон <q>%(name)s</q>:"

msgid "(does not exist)"
msgstr "(не съществува)"

msgid "Back to Documentation"
msgstr "Назад към Документацията"

msgid "Template filters"
msgstr "Шаблонни филтри"

msgid "Template filter documentation"
msgstr "Документация за шаблонни филтри"

msgid "Built-in filters"
msgstr "Вградени филтри"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"За да използвате тези филтри, сложете <code>%(code)s</code> във вашия "
"шаблон, преди да използвате филтъра."

msgid "Template tags"
msgstr "Шаблонни тагове"

msgid "Template tag documentation"
msgstr "Документация за Шаблонни тагове"

msgid "Built-in tags"
msgstr "Вградени тагове"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"За да използвате тези тагове, сложете <code>%(code)s</code> във вашия "
"шаблон, преди да използвате тага."

#, python-format
msgid "View: %(name)s"
msgstr "Изглед: %(name)s"

msgid "Context:"
msgstr "Контекст:"

msgid "Templates:"
msgstr "Шаблони:"

msgid "Back to View documentation"
msgstr "Обратно към документацията за Изглед"

msgid "View documentation"
msgstr "Документация за Изглед"

msgid "Jump to namespace"
msgstr "Прескочи към именни пространства"

msgid "Empty namespace"
msgstr "Празни именни пространства"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "Изгледи по именни пространства %(name)s"

msgid "Views by empty namespace"
msgstr "Изгледи по празни именни пространства"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"    Изглед функция: <code>%(full_name)s</code>. Име: <code>%(url_name)s</"
"code>.\n"

msgid "tag:"
msgstr "таг:"

msgid "filter:"
msgstr "филтър:"

msgid "view:"
msgstr "изглед:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "Приложението %(app_label)r не е намерено"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "Моделът %(model_name)r не е намерен в приложение %(app_label)r"

msgid "model:"
msgstr "модел:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "свързания '%(app_label)s.%(data_type)s' обект"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "свързаните '%(app_label)s.%(object_name)s' обекти"

#, python-format
msgid "all %s"
msgstr "всички %s"

#, python-format
msgid "number of %s"
msgstr "брой %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s не прилича на обект от тип urlpattern"
