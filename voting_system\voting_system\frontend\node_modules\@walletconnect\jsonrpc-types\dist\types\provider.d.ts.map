{"version": 3, "file": "provider.d.ts", "sourceRoot": "", "sources": ["../../src/provider.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,cAAc,EAAE,gBAAgB,EAAE,MAAM,WAAW,CAAC;AAC7E,OAAO,EAAE,OAAO,EAAE,MAAM,QAAQ,CAAC;AAEjC,8BAAsB,kBAAmB,SAAQ,OAAO;IACtD,SAAgB,SAAS,EAAE,OAAO,CAAC;IACnC,SAAgB,UAAU,EAAE,OAAO,CAAC;gBAExB,IAAI,CAAC,EAAE,GAAG;aAIN,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC;aAC/B,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;aACtB,IAAI,CAAC,OAAO,EAAE,cAAc,EAAE,OAAO,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC;CAC5E;AAED,8BAAsB,oBAAqB,SAAQ,OAAO;;aAMxC,OAAO,CAAC,MAAM,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC;aAEpC,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;aAE3B,OAAO,CAAC,MAAM,GAAG,GAAG,EAAE,MAAM,GAAG,GAAG,EAChD,OAAO,EAAE,gBAAgB,CAAC,MAAM,CAAC,EACjC,OAAO,CAAC,EAAE,GAAG,GACZ,OAAO,CAAC,MAAM,CAAC;IAIlB,SAAS,CAAC,QAAQ,CAAC,aAAa,CAAC,MAAM,GAAG,GAAG,EAAE,MAAM,GAAG,GAAG,EACzD,OAAO,EAAE,cAAc,CAAC,MAAM,CAAC,EAC/B,OAAO,CAAC,EAAE,GAAG,GACZ,OAAO,CAAC,MAAM,CAAC;CACnB;AAED,8BAAsB,gBAAiB,SAAQ,oBAAoB;IACjE,SAAgB,UAAU,EAAE,kBAAkB,CAAC;gBAGnC,UAAU,EAAE,MAAM,GAAG,kBAAkB;aAInC,OAAO,CAAC,UAAU,CAAC,EAAE,MAAM,GAAG,kBAAkB,GAAG,OAAO,CAAC,IAAI,CAAC;IAIhF,SAAS,CAAC,QAAQ,CAAC,aAAa,CAAC,UAAU,CAAC,EAAE,MAAM,GAAG,kBAAkB,GAAG,kBAAkB;IAE9F,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,OAAO,EAAE,cAAc,GAAG,IAAI;IAE3D,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,MAAM,GAAG,kBAAkB,GAAG,OAAO,CAAC,IAAI,CAAC;IAEhF,SAAS,CAAC,QAAQ,CAAC,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;CAC1C"}