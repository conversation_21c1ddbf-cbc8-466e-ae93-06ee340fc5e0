{"version": 3, "file": "when.js", "sourceRoot": "", "sources": ["../../src/directives/when.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAkCH,MAAM,UAAU,IAAI,CAClB,SAAkB,EAClB,QAAuB,EACvB,SAAyB;IAEzB,OAAO,SAAS,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,SAAS,aAAT,SAAS,uBAAT,SAAS,EAAI,CAAC;AAChD,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/**\n * When `condition` is true, returns the result of calling `trueCase()`, else\n * returns the result of calling `falseCase()` if `falseCase` is defined.\n *\n * This is a convenience wrapper around a ternary expression that makes it a\n * little nicer to write an inline conditional without an else.\n *\n * @example\n *\n * ```ts\n * render() {\n *   return html`\n *     ${when(this.user, () => html`User: ${this.user.username}`, () => html`Sign In...`)}\n *   `;\n * }\n * ```\n */\nexport function when<T, F>(\n  condition: true,\n  trueCase: () => T,\n  falseCase?: () => F\n): T;\nexport function when<T, F = undefined>(\n  condition: false,\n  trueCase: () => T,\n  falseCase?: () => F\n): F;\nexport function when<T, F = undefined>(\n  condition: unknown,\n  trueCase: () => T,\n  falseCase?: () => F\n): T | F;\nexport function when(\n  condition: unknown,\n  trueCase: () => unknown,\n  falseCase?: () => unknown\n): unknown {\n  return condition ? trueCase() : falseCase?.();\n}\n"]}