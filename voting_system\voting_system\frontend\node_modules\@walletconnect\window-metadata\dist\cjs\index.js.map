{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";;;AAAA,kEAGuC;AASvC,SAAgB,iBAAiB;IAC/B,IAAI,GAAa,CAAC;IAClB,IAAI,GAAa,CAAC;IAElB,IAAI;QACF,GAAG,GAAG,mCAAkB,EAAE,CAAC;QAC3B,GAAG,GAAG,mCAAkB,EAAE,CAAC;KAC5B;IAAC,OAAO,CAAC,EAAE;QACV,OAAO,IAAI,CAAC;KACb;IAED,SAAS,QAAQ;QACf,MAAM,KAAK,GAAsC,GAAG,CAAC,oBAAoB,CACvE,MAAM,CACP,CAAC;QACF,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrC,MAAM,IAAI,GAAoB,KAAK,CAAC,CAAC,CAAC,CAAC;YAEvC,MAAM,GAAG,GAAkB,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YACpD,IAAI,GAAG,EAAE;gBACP,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE;oBAC1C,MAAM,IAAI,GAAkB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;oBAEtD,IAAI,IAAI,EAAE;wBACR,IACE,IAAI,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;4BAC3C,IAAI,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;4BAC1C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EACxB;4BACA,IAAI,YAAY,GAAW,GAAG,CAAC,QAAQ,GAAG,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC;4BAE1D,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;gCAC3B,YAAY,IAAI,IAAI,CAAC;6BACtB;iCAAM;gCACL,MAAM,IAAI,GAAa,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gCAC/C,IAAI,CAAC,GAAG,EAAE,CAAC;gCACX,MAAM,SAAS,GAAW,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gCACzC,YAAY,IAAI,SAAS,GAAG,GAAG,GAAG,IAAI,CAAC;6BACxC;4BAED,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;yBAC1B;6BAAM,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;4BACnC,MAAM,WAAW,GAAW,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC;4BAEhD,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;yBACzB;6BAAM;4BACL,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;yBAClB;qBACF;iBACF;aACF;SACF;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED,SAAS,sBAAsB,CAAC,GAAG,IAAc;QAC/C,MAAM,QAAQ,GAAsC,GAAG,CAAC,oBAAoB,CAC1E,MAAM,CACP,CAAC;QAEF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACxC,MAAM,GAAG,GAAoB,QAAQ,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,UAAU,GAAyB,CAAC,UAAU,EAAE,UAAU,EAAE,MAAM,CAAC;iBACtE,GAAG,CAAC,CAAC,MAAc,EAAE,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;iBACjD,MAAM,CAAC,CAAC,IAAmB,EAAE,EAAE;gBAC9B,IAAI,IAAI,EAAE;oBACR,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;iBAC5B;gBACD,OAAO,KAAK,CAAC;YACf,CAAC,CAAC,CAAC;YAEL,IAAI,UAAU,CAAC,MAAM,IAAI,UAAU,EAAE;gBACnC,MAAM,OAAO,GAAkB,GAAG,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;gBAC3D,IAAI,OAAO,EAAE;oBACX,OAAO,OAAO,CAAC;iBAChB;aACF;SACF;QAED,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,SAAS,OAAO;QACd,IAAI,IAAI,GAAW,sBAAsB,CACvC,MAAM,EACN,cAAc,EACd,UAAU,EACV,eAAe,CAChB,CAAC;QAEF,IAAI,CAAC,IAAI,EAAE;YACT,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC;SAClB;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,SAAS,cAAc;QACrB,MAAM,WAAW,GAAW,sBAAsB,CAChD,aAAa,EACb,gBAAgB,EAChB,qBAAqB,EACrB,UAAU,CACX,CAAC;QAEF,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,MAAM,IAAI,GAAW,OAAO,EAAE,CAAC;IAC/B,MAAM,WAAW,GAAW,cAAc,EAAE,CAAC;IAC7C,MAAM,GAAG,GAAW,GAAG,CAAC,MAAM,CAAC;IAC/B,MAAM,KAAK,GAAa,QAAQ,EAAE,CAAC;IAEnC,MAAM,IAAI,GAAqB;QAC7B,WAAW;QACX,GAAG;QACH,KAAK;QACL,IAAI;KACL,CAAC;IAEF,OAAO,IAAI,CAAC;AACd,CAAC;AA5HD,8CA4HC"}