"""
Generated by `compile_contracts.py` script.
Compiled with Solidity v0.8.19.
"""

# source: web3/_utils/contract_sources/SimpleResolver.sol:SimpleResolver
SIMPLE_RESOLVER_BYTECODE = "0x608060405234801561001057600080fd5b5061028c806100206000396000f3fe608060405234801561001057600080fd5b50600436106100365760003560e01c806301ffc9a71461003b5780633b3b57de1461006b575b600080fd5b61005560048036038101906100509190610134565b61009b565b604051610062919061017c565b60405180910390f35b610085600480360381019061008091906101cd565b6100cd565b604051610092919061023b565b60405180910390f35b6000633b3b57de60e01b827bffffffffffffffffffffffffffffffffffffffffffffffffffffffff1916149050919050565b6000309050919050565b600080fd5b60007fffffffff0000000000000000000000000000000000000000000000000000000082169050919050565b610111816100dc565b811461011c57600080fd5b50565b60008135905061012e81610108565b92915050565b60006020828403121561014a576101496100d7565b5b60006101588482850161011f565b91505092915050565b60008115159050919050565b61017681610161565b82525050565b6000602082019050610191600083018461016d565b92915050565b6000819050919050565b6101aa81610197565b81146101b557600080fd5b50565b6000813590506101c7816101a1565b92915050565b6000602082840312156101e3576101e26100d7565b5b60006101f1848285016101b8565b91505092915050565b600073ffffffffffffffffffffffffffffffffffffffff82169050919050565b6000610225826101fa565b9050919050565b6102358161021a565b82525050565b6000602082019050610250600083018461022c565b9291505056fea26469706673582212202bcb0857f947d53d19a5a1745f623dd3f4bb98c6910ef93f11443eb5271d457364736f6c63430008130033"  # noqa: E501
SIMPLE_RESOLVER_RUNTIME = "0x608060405234801561001057600080fd5b50600436106100365760003560e01c806301ffc9a71461003b5780633b3b57de1461006b575b600080fd5b61005560048036038101906100509190610134565b61009b565b604051610062919061017c565b60405180910390f35b610085600480360381019061008091906101cd565b6100cd565b604051610092919061023b565b60405180910390f35b6000633b3b57de60e01b827bffffffffffffffffffffffffffffffffffffffffffffffffffffffff1916149050919050565b6000309050919050565b600080fd5b60007fffffffff0000000000000000000000000000000000000000000000000000000082169050919050565b610111816100dc565b811461011c57600080fd5b50565b60008135905061012e81610108565b92915050565b60006020828403121561014a576101496100d7565b5b60006101588482850161011f565b91505092915050565b60008115159050919050565b61017681610161565b82525050565b6000602082019050610191600083018461016d565b92915050565b6000819050919050565b6101aa81610197565b81146101b557600080fd5b50565b6000813590506101c7816101a1565b92915050565b6000602082840312156101e3576101e26100d7565b5b60006101f1848285016101b8565b91505092915050565b600073ffffffffffffffffffffffffffffffffffffffff82169050919050565b6000610225826101fa565b9050919050565b6102358161021a565b82525050565b6000602082019050610250600083018461022c565b9291505056fea26469706673582212202bcb0857f947d53d19a5a1745f623dd3f4bb98c6910ef93f11443eb5271d457364736f6c63430008130033"  # noqa: E501
SIMPLE_RESOLVER_ABI = [
    {
        "inputs": [{"internalType": "bytes32", "name": "nodeID", "type": "bytes32"}],
        "name": "addr",
        "outputs": [{"internalType": "address", "name": "", "type": "address"}],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "bytes4", "name": "interfaceID", "type": "bytes4"}],
        "name": "supportsInterface",
        "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
        "stateMutability": "nonpayable",
        "type": "function",
    },
]
SIMPLE_RESOLVER_DATA = {
    "bytecode": SIMPLE_RESOLVER_BYTECODE,
    "bytecode_runtime": SIMPLE_RESOLVER_RUNTIME,
    "abi": SIMPLE_RESOLVER_ABI,
}
