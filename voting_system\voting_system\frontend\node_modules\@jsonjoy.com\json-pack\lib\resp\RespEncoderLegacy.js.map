{"version": 3, "file": "RespEncoderLegacy.js", "sourceRoot": "", "sources": ["../../src/resp/RespEncoderLegacy.ts"], "names": [], "mappings": ";;;AACA,6CAA0E;AAC1E,4DAAuD;AACvD,+CAA0C;AAG1C,MAAM,MAAM,GAAG,QAAQ,CAAC;AACxB,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC;AAK3C,MAAa,iBAAmF,SAAQ,yBAAc;IAC7G,QAAQ,CAAC,KAAc;QAC5B,QAAQ,OAAO,KAAK,EAAE,CAAC;YACrB,KAAK,QAAQ;gBACX,OAAO,IAAI,CAAC,WAAW,CAAC,KAAe,CAAC,CAAC;YAC3C,KAAK,QAAQ;gBACX,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAC9B,KAAK,SAAS;gBACZ,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;YACvD,KAAK,QAAQ,CAAC,CAAC,CAAC;gBACd,IAAI,CAAC,KAAK;oBAAE,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC;gBACpC,IAAI,KAAK,YAAY,KAAK;oBAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBACxD,IAAI,KAAK,YAAY,UAAU;oBAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBAC7D,IAAI,KAAK,YAAY,KAAK;oBAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;gBAChE,IAAI,KAAK,YAAY,GAAG;oBAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBACtD,IAAI,KAAK,YAAY,qCAAiB,EAAE,CAAC;oBACvC,IAAI,KAAK,YAAY,qBAAQ;wBAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;oBAC/D,IAAI,KAAK,YAAY,+BAAkB;wBAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;oBACzE,IAAI,KAAK,YAAY,2BAAc;wBAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACvE,CAAC;gBACD,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAgC,CAAC,CAAC;YACzD,CAAC;YACD,KAAK,WAAW;gBACd,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;YAC3B,KAAK,QAAQ;gBACX,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC;YAC9C;gBACE,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QACpC,CAAC;IACH,CAAC;IAEM,WAAW,CAAC,GAAW;QAC5B,IAAI,aAAa,CAAC,GAAG,CAAC;YAAE,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;;YAC1C,IAAI,CAAC,mBAAmB,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;IAC1C,CAAC;IAEM,QAAQ,CAAC,GAAW;QACzB,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;QAC1B,IAAI,MAAM,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;YAAE,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;;YAC1D,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;IAC9B,CAAC;IAEM,SAAS;QACd,IAAI,CAAC,YAAY,EAAE,CAAC;IACtB,CAAC;IAEM,QAAQ,CAAC,GAAW;QACzB,IAAI,GAAG,CAAC,MAAM,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;YAAE,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;;YAC9D,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;IAC9B,CAAC;IAEM,QAAQ,CAAC,GAAiB;QAC/B,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;IAC1B,CAAC;IAEM,QAAQ,CAAC,GAAc;QAC5B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;QAC1B,MAAM,CAAC,EAAE,IAAU,CAAC;QACpB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QACzB,MAAM,CAAC,GAAG,MAAS,CAAC;QACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAChC,MAAM,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;YACnB,IAAI,GAAG,KAAK,IAAI;gBAAE,IAAI,CAAC,YAAY,EAAE,CAAC;;gBACjC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAC1B,CAAC;IACH,CAAC;IAEM,QAAQ,CAAC,GAA4B;QAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,CAAC,EAAE,IAAU,CAAC;QACpB,IAAI,CAAC,WAAW,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC;QAC9B,MAAM,CAAC,GAAG,MAAS,CAAC;QACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAChC,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACpB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YACnB,MAAM,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;YACrB,IAAI,GAAG,KAAK,IAAI;gBAAE,IAAI,CAAC,YAAY,EAAE,CAAC;;gBACjC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAC1B,CAAC;IACH,CAAC;CACF;AAnFD,8CAmFC"}