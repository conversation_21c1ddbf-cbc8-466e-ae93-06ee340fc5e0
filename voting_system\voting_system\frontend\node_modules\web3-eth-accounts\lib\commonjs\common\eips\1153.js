"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = {
    name: 'EIP-1153',
    number: 1153,
    comment: 'Transient Storage',
    url: 'https://eips.ethereum.org/EIPS/eip-1153',
    status: 'Review',
    minimumHardfork: 'chainstart',
    requiredEIPs: [],
    gasConfig: {},
    gasPrices: {
        tstore: {
            v: 100,
            d: 'Base fee of the TSTORE opcode',
        },
        tload: {
            v: 100,
            d: 'Base fee of the TLOAD opcode',
        },
    },
    vm: {},
    pow: {},
};
//# sourceMappingURL=1153.js.map