# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2023
# <PERSON> <<EMAIL>>, 2015-2020
# <AUTHOR> <EMAIL>, 2015
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-17 02:13-0600\n"
"PO-Revision-Date: 2023-04-19 09:22+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Danish (http://www.transifex.com/django/django/language/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "PostgreSQL extensions"
msgstr "PostgreSQL-udvidelser"

#, python-format
msgid "Item %(nth)s in the array did not validate:"
msgstr "Element %(nth)s i array'et blev ikke valideret:"

msgid "Nested arrays must have the same length."
msgstr "Indlejrede arrays skal have den samme længde."

msgid "Map of strings to strings/nulls"
msgstr "Afbildning fra strenge til strenge/nulls"

#, python-format
msgid "The value of “%(key)s” is not a string or null."
msgstr "Værdien af “%(key)s” er ikke en streng eller null."

msgid "Could not load JSON data."
msgstr "Kunne ikke indlæse JSON-data."

msgid "Input must be a JSON dictionary."
msgstr "Input skal være et JSON-dictionary."

msgid "Enter two valid values."
msgstr "Indtast to gyldige værdier."

msgid "The start of the range must not exceed the end of the range."
msgstr "Starten af intervallet kan ikke overstige slutningen af intervallet."

msgid "Enter two whole numbers."
msgstr "Indtast to heltal."

msgid "Enter two numbers."
msgstr "Indtast to tal."

msgid "Enter two valid date/times."
msgstr "Indtast to gyldige dato/tider."

msgid "Enter two valid dates."
msgstr "Indtast to gyldige datoer."

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no more than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no more than "
"%(limit_value)d."
msgstr[0] ""
"Listen indeholder %(show_value)d element, en bør ikke indeholde mere end "
"%(limit_value)d."
msgstr[1] ""
"Listen indeholder %(show_value)d elementer, den bør ikke indeholde mere end "
"%(limit_value)d."

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no fewer than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no fewer than "
"%(limit_value)d."
msgstr[0] ""
"Listen indeholder %(show_value)d element, den bør ikke indeholde mindre end "
"%(limit_value)d."
msgstr[1] ""
"Listen indeholder %(show_value)d elementer, den bør ikke indeholde mindre "
"end %(limit_value)d."

#, python-format
msgid "Some keys were missing: %(keys)s"
msgstr "Nøgler mangler: %(keys)s"

#, python-format
msgid "Some unknown keys were provided: %(keys)s"
msgstr "Ukendte nøgler angivet: %(keys)s"

#, python-format
msgid ""
"Ensure that the upper bound of the range is not greater than %(limit_value)s."
msgstr "Intervallets øvre grænse må ikke være større end %(limit_value)s."

#, python-format
msgid ""
"Ensure that the lower bound of the range is not less than %(limit_value)s."
msgstr "Intervallets nedre grænse må ikke være mindre end %(limit_value)s."
