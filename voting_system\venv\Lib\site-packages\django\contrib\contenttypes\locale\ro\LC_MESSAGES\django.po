# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON>, 2011
# <PERSON> <<EMAIL>>, 2014
# <PERSON><PERSON><PERSON> <razvan.s<PERSON><PERSON><PERSON>@gmail.com>, 2016
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <razvan.ste<PERSON>@gmail.com>\n"
"Language-Team: Romanian (http://www.transifex.com/django/django/language/"
"ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?"
"2:1));\n"

msgid "Content Types"
msgstr "Tipuri de conținut"

msgid "python model class name"
msgstr "nume clasă model Python"

msgid "content type"
msgstr "tip conținut"

msgid "content types"
msgstr "tipuri conținut"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "Tipul de conținut %(ct_id)s nu are nici un model asociat"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn't exist"
msgstr "Content type %(ct_id)s object %(obj_id)s doesn't exist"

#, python-format
msgid "%(ct_name)s objects don't have a get_absolute_url() method"
msgstr "Obiectele %(ct_name)s nu au o metodă get_absolute_url()"
