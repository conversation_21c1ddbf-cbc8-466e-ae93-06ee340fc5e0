{"version": 3, "file": "join.js", "sources": ["../../../src/directives/join.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/**\n * Returns an iterable containing the values in `items` interleaved with the\n * `joiner` value.\n *\n * @example\n *\n * ```ts\n * render() {\n *   return html`\n *     ${join(items, html`<span class=\"separator\">|</span>`)}\n *   `;\n * }\n */\nexport function join<I, J>(\n  items: Iterable<I> | undefined,\n  joiner: (index: number) => J\n): Iterable<I | J>;\nexport function join<I, J>(\n  items: Iterable<I> | undefined,\n  joiner: J\n): Iterable<I | J>;\nexport function* join<I, J>(items: Iterable<I> | undefined, joiner: J) {\n  const isFunction = typeof joiner === 'function';\n  if (items !== undefined) {\n    let i = -1;\n    for (const value of items) {\n      if (i > -1) {\n        yield isFunction ? joiner(i) : joiner;\n      }\n      i++;\n      yield value;\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;AAIG;UAuBc,IAAI,CAAO,KAA8B,EAAE,MAAS,EAAA;AACnE,IAAA,MAAM,UAAU,GAAG,OAAO,MAAM,KAAK,UAAU,CAAC;IAChD,IAAI,KAAK,KAAK,SAAS,EAAE;AACvB,QAAA,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AACX,QAAA,KAAK,MAAM,KAAK,IAAI,KAAK,EAAE;AACzB,YAAA,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;AACV,gBAAA,MAAM,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;AACvC,aAAA;AACD,YAAA,CAAC,EAAE,CAAC;AACJ,YAAA,MAAM,KAAK,CAAC;AACb,SAAA;AACF,KAAA;AACH;;;;"}