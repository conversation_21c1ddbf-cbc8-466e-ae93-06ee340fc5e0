import{options as n,Fragment as e,Component as t}from"preact";import"preact/devtools";var o={};function r(){o={}}function a(n){return n.type===e?"Fragment":"function"==typeof n.type?n.type.displayName||n.type.name:"string"==typeof n.type?n.type:"#text"}var i=[],s=[];function c(){return i.length>0?i[i.length-1]:null}var l=!1;function u(n){return"function"==typeof n.type&&n.type!=e}function p(n){for(var e=[n],t=n;null!=t.__o;)e.push(t.__o),t=t.__o;return e.reduce(function(n,e){n+="  in "+a(e);var t=e.__source;return t?n+=" (at "+t.fileName+":"+t.lineNumber+")":l||(l=!0,console.warn("Add @babel/plugin-transform-react-jsx-source to get a more detailed component stack. Note that you should not add it to production builds of your App for bundle size reasons.")),n+"\n"},"")}var f="function"==typeof WeakMap,d=t.prototype.setState;t.prototype.setState=function(n,e){return null==this.__v?null==this.state&&console.warn('Calling "this.setState" inside the constructor of a component is a no-op and might be a bug in your application. Instead, set "this.state = {}" directly.\n\n'+p(c())):null==this.__P&&console.warn('Can\'t call "this.setState" on an unmounted component. This is a no-op, but it indicates a memory leak in your application. To fix, cancel all subscriptions and asynchronous tasks in the componentWillUnmount method.\n\n'+p(this.__v)),d.call(this,n,e)};var h=t.prototype.forceUpdate;function y(n){var e=n.props,t=a(n),o="";for(var r in e)if(e.hasOwnProperty(r)&&"children"!==r){var i=e[r];"function"==typeof i&&(i="function "+(i.displayName||i.name)+"() {}"),i=Object(i)!==i||i.toString?i+"":Object.prototype.toString.call(i),o+=" "+r+"="+JSON.stringify(i)}var s=e.children;return"<"+t+o+(s&&s.length?">..</"+t+">":" />")}t.prototype.forceUpdate=function(n){return null==this.__v?console.warn('Calling "this.forceUpdate" inside the constructor of a component is a no-op and might be a bug in your application.\n\n'+p(c())):null==this.__P&&console.warn('Can\'t call "this.forceUpdate" on an unmounted component. This is a no-op, but it indicates a memory leak in your application. To fix, cancel all subscriptions and asynchronous tasks in the componentWillUnmount method.\n\n'+p(this.__v)),h.call(this,n)},function(){!function(){var e=n.__b,t=n.diffed,o=n.__,r=n.vnode,a=n.__r;n.diffed=function(n){u(n)&&s.pop(),i.pop(),t&&t(n)},n.__b=function(n){u(n)&&i.push(n),e&&e(n)},n.__=function(n,e){s=[],o&&o(n,e)},n.vnode=function(n){n.__o=s.length>0?s[s.length-1]:null,r&&r(n)},n.__r=function(n){u(n)&&s.push(n),a&&a(n)}}();var e=n.__b,t=n.diffed,r=n.vnode,c=n.__e,l=n.__,d=n.__h,h=f?{useEffect:new WeakMap,useLayoutEffect:new WeakMap,lazyPropTypes:new WeakMap}:null;n.__e=function(n,e,t){if(e&&e.__c&&"function"==typeof n.then){var o=n;n=new Error("Missing Suspense. The throwing component was: "+a(e));for(var r=e;r;r=r.__)if(r.__c&&r.__c.__c){n=o;break}if(n instanceof Error)throw n}c(n,e,t)},n.__=function(n,e){if(!e)throw new Error("Undefined parent passed to render(), this is the second argument.\nCheck if the element is available in the DOM/has the correct id.");var t;switch(e.nodeType){case 1:case 11:case 9:t=!0;break;default:t=!1}if(!t){var o=a(n);throw new Error("Expected a valid HTML node as a second argument to render.\tReceived "+e+" instead: render(<"+o+" />, "+e+");")}l&&l(n,e)},n.__b=function(n){var t,r,i,s=n.type,c=function n(e){return e?"function"==typeof e.type?n(e.__):e:{}}(n.__);if(void 0===s)throw new Error("Undefined component passed to createElement()\n\nYou likely forgot to export your component or might have mixed up default and named imports"+y(n)+"\n\n"+p(n));if(null!=s&&"object"==typeof s){if(void 0!==s.__k&&void 0!==s.__e)throw new Error("Invalid type passed to createElement(): "+s+"\n\nDid you accidentally pass a JSX literal as JSX twice?\n\n  let My"+a(n)+" = "+y(s)+";\n  let vnode = <My"+a(n)+" />;\n\nThis usually happens when you export a JSX literal and not the component.\n\n"+p(n));throw new Error("Invalid type passed to createElement(): "+(Array.isArray(s)?"array":s))}if("thead"!==s&&"tfoot"!==s&&"tbody"!==s||"table"===c.type?"tr"===s&&"thead"!==c.type&&"tfoot"!==c.type&&"tbody"!==c.type&&"table"!==c.type?console.error("Improper nesting of table. Your <tr> should have a <thead/tbody/tfoot/table> parent."+y(n)+"\n\n"+p(n)):"td"===s&&"tr"!==c.type?console.error("Improper nesting of table. Your <td> should have a <tr> parent."+y(n)+"\n\n"+p(n)):"th"===s&&"tr"!==c.type&&console.error("Improper nesting of table. Your <th> should have a <tr>."+y(n)+"\n\n"+p(n)):console.error("Improper nesting of table. Your <thead/tbody/tfoot> should have a <table> parent."+y(n)+"\n\n"+p(n)),void 0!==n.ref&&"function"!=typeof n.ref&&"object"!=typeof n.ref&&!("$$typeof"in n))throw new Error('Component\'s "ref" property should be a function, or an object created by createRef(), but got ['+typeof n.ref+"] instead\n"+y(n)+"\n\n"+p(n));if("string"==typeof n.type)for(var l in n.props)if("o"===l[0]&&"n"===l[1]&&"function"!=typeof n.props[l]&&null!=n.props[l])throw new Error("Component's \""+l+'" property should be a function, but got ['+typeof n.props[l]+"] instead\n"+y(n)+"\n\n"+p(n));if("function"==typeof n.type&&n.type.propTypes){if("Lazy"===n.type.displayName&&h&&!h.lazyPropTypes.has(n.type)){var u="PropTypes are not supported on lazy(). Use propTypes on the wrapped component itself. ";try{var f=n.type();h.lazyPropTypes.set(n.type,!0),console.warn(u+"Component wrapped in lazy() is "+a(f))}catch(n){console.warn(u+"We will log the wrapped component's name once it is loaded.")}}t=n.type.propTypes,r=n.props,i=a(n),Object.keys(t).forEach(function(n){var e;try{e=t[n](r,n,i,"prop",null,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED")}catch(n){e=n}!e||e.message in o||(o[e.message]=!0,console.error("Failed prop type: "+e.message))})}e&&e(n)},n.__h=function(n,e,t){if(!n)throw new Error("Hook can only be invoked from render methods.");d&&d(n,e,t)};var m=function(n,e){return{get:function(){console.warn("getting vnode."+n+" is deprecated, "+e)},set:function(){console.warn("setting vnode."+n+" is not allowed, "+e)}}},v={nodeName:m("nodeName","use vnode.type"),attributes:m("attributes","use vnode.props"),children:m("children","use vnode.props.children")},b=Object.create({},v);n.vnode=function(n){var e=n.props;if(null!==n.type&&null!=e&&("__source"in e||"__self"in e)){var t=n.props={};for(var o in e){var a=e[o];"__source"===o?n.__source=a:"__self"===o?n.__self=a:t[o]=a}}Object.setPrototypeOf(n,b),r&&r(n)},n.diffed=function(n){n.__k&&n.__k.forEach(function(e){if(e&&void 0===e.type){delete e.__,delete e.__b;var t=Object.keys(e).join(",");throw new Error("Objects are not valid as a child. Encountered an object with the keys {"+t+"}.\n\n"+p(n))}});var e=n.__c;if(e&&e.__H){var o=e.__H;Array.isArray(o.__)&&o.__.forEach(function(e){if(e.__h&&(!e.__H||!Array.isArray(e.__H))){var t=a(n);console.warn("In "+t+" you are calling useMemo/useCallback without passing arguments.\nThis is a noop since it will not be able to memoize, it will execute it every render.\n\n"+p(n))}})}if(t&&t(n),null!=n.__k)for(var r=[],i=0;i<n.__k.length;i++){var s=n.__k[i];if(s&&null!=s.key){var c=s.key;if(-1!==r.indexOf(c)){console.error('Following component has two or more children with the same key attribute: "'+c+'". This may cause glitches and misbehavior in rendering process. Component: \n\n'+y(n)+"\n\n"+p(n));break}r.push(c)}}}}();export{r as resetPropWarnings};
//# sourceMappingURL=debug.module.js.map
