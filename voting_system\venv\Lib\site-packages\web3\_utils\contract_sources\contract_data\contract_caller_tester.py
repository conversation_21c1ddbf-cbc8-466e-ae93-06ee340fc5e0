"""
Generated by `compile_contracts.py` script.
Compiled with Solidity v0.8.19.
"""

# source: web3/_utils/contract_sources/ContractCallerTester.sol:ContractCallerTester
CONTRACT_CALLER_TESTER_BYTECODE = "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"  # noqa: E501
CONTRACT_CALLER_TESTER_RUNTIME = "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"  # noqa: E501
CONTRACT_CALLER_TESTER_ABI = [
    {
        "inputs": [
            {"internalType": "int256", "name": "a", "type": "int256"},
            {"internalType": "int256", "name": "b", "type": "int256"},
        ],
        "name": "add",
        "outputs": [{"internalType": "int256", "name": "", "type": "int256"}],
        "stateMutability": "payable",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "count",
        "outputs": [{"internalType": "int256", "name": "", "type": "int256"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "counter",
        "outputs": [{"internalType": "int256", "name": "", "type": "int256"}],
        "stateMutability": "payable",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "increment",
        "outputs": [{"internalType": "int256", "name": "", "type": "int256"}],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "returnMeta",
        "outputs": [
            {"internalType": "address", "name": "", "type": "address"},
            {"internalType": "bytes", "name": "", "type": "bytes"},
            {"internalType": "uint256", "name": "", "type": "uint256"},
            {"internalType": "uint256", "name": "", "type": "uint256"},
            {"internalType": "uint256", "name": "", "type": "uint256"},
        ],
        "stateMutability": "payable",
        "type": "function",
    },
]
CONTRACT_CALLER_TESTER_DATA = {
    "bytecode": CONTRACT_CALLER_TESTER_BYTECODE,
    "bytecode_runtime": CONTRACT_CALLER_TESTER_RUNTIME,
    "abi": CONTRACT_CALLER_TESTER_ABI,
}
