{"name": "ethereumjs-tx", "version": "1.3.7", "description": "An simple module for creating, manipulating and signing ethereum transactions", "main": "es5/index.js", "scripts": {"coverage": "istanbul cover ./test/index.js", "coveralls": "npm run coverage && coveralls <coverage/lcov.info", "lint": "standard", "test": "npm run test:node && npm run test:browser", "test:browser": "karma start karma.conf.js", "test:node": "tape ./test/index.js", "build:es5": "babel index.js --source-root ./ -d ./es5", "build:docs": "documentation --github  -f md ./index.js > ./docs/index.md && contributor"}, "keywords": ["ethereum", "transactions"], "author": "mjbecze <<EMAIL>>", "license": "MPL-2.0", "dependencies": {"ethereum-common": "^0.0.18", "ethereumjs-util": "^5.0.0"}, "devDependencies": {"async": "^2.0.0", "babel-cli": "^6.22.2", "babel-preset-env": "^1.6.1", "babel-preset-stage-0": "^6.16.0", "babelify": "^7.3.0", "browserify": "^13.0.0", "contributor": "^0.1.25", "coveralls": "^2.11.4", "documentation": "^3.0.4", "ethereumjs-testing": "0.0.1", "istanbul": "^0.4.1", "karma": "^1.1.1", "karma-browserify": "^5.1.0", "karma-detect-browsers": "^2.0.2", "karma-env-preprocessor": "^0.1.1", "karma-firefox-launcher": "^1.0.0", "karma-tap": "^2.0.1", "minimist": "^1.2.0", "standard": "^7.1.2", "tape": "^4.0.3"}, "repository": {"type": "git", "url": "https://github.com/ethereumjs/ethereumjs-tx.git"}, "bugs": {"url": "https://github.com/ethereumjs/ethereumjs-tx/issues"}, "homepage": "https://github.com/ethereumjs/ethereumjs-tx", "standard": {"ignore": ["es5/**", "dist/**", "package-init.js", "package.js"]}, "contributors": [{"name": "<-c-g-> ", "email": "christopher<PERSON><PERSON><PERSON>@gmail.com", "url": "https://github.com/cgewecke", "contributions": 1, "additions": 3, "deletions": 1, "hireable": true}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/fanatid", "contributions": 1, "additions": 3, "deletions": 2, "hireable": true}, {"name": "<PERSON>", "email": null, "url": "https://github.com/Nexus7", "contributions": 1, "additions": 1, "deletions": 1, "hireable": null}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tcoulter", "contributions": 1, "additions": 1, "deletions": 1, "hireable": null}, {"name": "kuma<PERSON>", "email": "<EMAIL>", "url": "https://github.com/kumavis", "contributions": 1, "additions": 6, "deletions": 6, "hireable": true}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tinybike", "contributions": 2, "additions": 1, "deletions": 2, "hireable": null}, {"name": "<PERSON>", "email": null, "url": "https://github.com/SilentCicero", "contributions": 2, "additions": 29, "deletions": 0, "hireable": true}, {"name": null, "email": null, "url": "https://github.com/ckeenan", "contributions": 2, "additions": 4, "deletions": 3, "hireable": null}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/axic", "contributions": 22, "additions": 27562, "deletions": 42613, "hireable": true}, {"name": null, "email": null, "url": "https://github.com/wanderer", "contributions": 171, "additions": 112706, "deletions": 96355, "hireable": null}, {"name": null, "email": null, "url": "https://github.com/kvhnuke", "contributions": 7, "additions": 268, "deletions": 53, "hireable": null}]}