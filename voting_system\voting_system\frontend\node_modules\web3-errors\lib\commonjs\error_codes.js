"use strict";
/*
This file is part of web3.js.

web3.js is free software: you can redistribute it and/or modify
it under the terms of the GNU Lesser General Public License as published by
the Free Software Foundation, either version 3 of the License, or
(at your option) any later version.

web3.js is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU Lesser General Public License for more details.

You should have received a copy of the GNU Lesser General Public License
along with web3.js.  If not, see <http://www.gnu.org/licenses/>.
*/
Object.defineProperty(exports, "__esModule", { value: true });
exports.ERR_TX_POLLING_TIMEOUT = exports.ERR_TX_DATA_AND_INPUT = exports.ERR_TX_UNSUPPORTED_TYPE = exports.ERR_TX_UNSUPPORTED_EIP_1559 = exports.ERR_TX_UNABLE_TO_POPULATE_NONCE = exports.ERR_TX_INVALID_NONCE_OR_CHAIN_ID = exports.ERR_TX_INVALID_OBJECT = exports.ERR_TX_INVALID_LEGACY_FEE_MARKET = exports.ERR_TX_INVALID_FEE_MARKET_GAS_PRICE = exports.ERR_TX_INVALID_FEE_MARKET_GAS = exports.ERR_TX_INVALID_LEGACY_GAS = exports.ERR_TX_MISSING_GAS = exports.ERR_TX_MISSING_CHAIN_INFO = exports.ERR_TX_INVALID_CHAIN_INFO = exports.ERR_TX_CHAIN_ID_MISMATCH = exports.ERR_TX_MISSING_CUSTOM_CHAIN_ID = exports.ERR_TX_MISSING_CUSTOM_CHAIN = exports.ERR_TX_INVALID_CALL = exports.ERR_TX_INVALID_SENDER = exports.ERR_RAW_TX_UNDEFINED = exports.ERR_TX_OUT_OF_GAS = exports.ERR_TX_REVERT_WITHOUT_REASON = exports.ERR_TX_CONTRACT_NOT_STORED = exports.ERR_TX_NO_CONTRACT_ADDRESS = exports.ERR_TX_REVERT_TRANSACTION = exports.ERR_TX_REVERT_INSTRUCTION = exports.ERR_TX = exports.ERR_CONTRACT_TX_DATA_AND_INPUT = exports.ERR_CONTRACT_EXECUTION_REVERTED = exports.ERR_CONTRACT_INSTANTIATION = exports.ERR_CONTRACT_MISSING_FROM_ADDRESS = exports.ERR_CONTRACT_MISSING_ADDRESS = exports.ERR_CONTRACT_MISSING_DEPLOY_DATA = exports.ERR_CONTRACT_RESERVED_EVENT = exports.ERR_CONTRACT_EVENT_NOT_EXISTS = exports.ERR_CONTRACT_REQUIRED_CALLBACK = exports.ERR_CONTRACT_ABI_MISSING = exports.ERR_CONTRACT_RESOLVER_MISSING = exports.ERR_CONTRACT = exports.ERR_MULTIPLE_ERRORS = exports.ERR_INVALID_METHOD_PARAMS = exports.ERR_EXISTING_PLUGIN_NAMESPACE = exports.ERR_ABI_ENCODING = exports.ERR_OPERATION_ABORT = exports.ERR_OPERATION_TIMEOUT = exports.ERR_METHOD_NOT_IMPLEMENTED = exports.ERR_FORMATTERS = exports.ERR_PARAM = exports.ERR_INVALID_RESPONSE = exports.ERR_RESPONSE = void 0;
exports.ERR_INVALID_BYTES = exports.ERR_INVALID_STRING = exports.ERR_ENS_NETWORK_NOT_SYNCED = exports.ERR_ENS_UNSUPPORTED_NETWORK = exports.ERR_ENS_CHECK_INTERFACE_SUPPORT = exports.JSONRPC_ERR_CHAIN_DISCONNECTED = exports.JSONRPC_ERR_DISCONNECTED = exports.JSONRPC_ERR_UNSUPPORTED_METHOD = exports.JSONRPC_ERR_UNAUTHORIZED = exports.JSONRPC_ERR_REJECTED_REQUEST = exports.GENESIS_BLOCK_NUMBER = exports.ERR_INVALID_SIGNATURE = exports.ERR_SIGNATURE_FAILED = exports.ERR_PBKDF2_ITERATIONS = exports.ERR_INVALID_KEYSTORE = exports.ERR_IV_LENGTH = exports.ERR_INVALID_PASSWORD = exports.ERR_KEY_VERSION_UNSUPPORTED = exports.ERR_KEY_DERIVATION_FAIL = exports.ERR_UNSUPPORTED_KDF = exports.ERR_INVALID_PRIVATE_KEY = exports.ERR_PRIVATE_KEY_LENGTH = exports.ERR_WS_PROVIDER = exports.ERR_SUBSCRIPTION = exports.ERR_INVALID_CLIENT = exports.ERR_INVALID_PROVIDER = exports.ERR_PROVIDER = exports.ERR_REQ_ALREADY_SENT = exports.ERR_CONN_PENDING_REQUESTS = exports.ERR_CONN_MAX_ATTEMPTS = exports.ERR_CONN_CLOSE = exports.ERR_CONN_NOT_OPEN = exports.ERR_CONN_TIMEOUT = exports.ERR_CONN_INVALID = exports.ERR_CONN = exports.ERR_TX_GAS_MISMATCH_INNER_ERROR = exports.ERR_TX_MISSING_GAS_INNER_ERROR = exports.ERR_TX_INVALID_PROPERTIES_FOR_TYPE = exports.ERR_TX_REVERT_TRANSACTION_CUSTOM_ERROR = exports.ERR_TX_INVALID_RECEIVER = exports.ERR_TX_HARDFORK_MISMATCH = exports.ERR_TX_CHAIN_MISMATCH = exports.ERR_TX_GAS_MISMATCH = exports.ERR_TX_SIGNING = exports.ERR_TX_BLOCK_TIMEOUT = exports.ERR_TX_SEND_TIMEOUT = exports.ERR_TX_NOT_FOUND = exports.ERR_TX_LOCAL_WALLET_NOT_AVAILABLE = exports.ERR_TX_RECEIPT_MISSING_BLOCK_NUMBER = exports.ERR_TX_RECEIPT_MISSING_OR_BLOCKHASH_NULL = void 0;
exports.ERR_RPC_NOT_SUPPORTED = exports.ERR_RPC_LIMIT_EXCEEDED = exports.ERR_RPC_UNSUPPORTED_METHOD = exports.ERR_RPC_TRANSACTION_REJECTED = exports.ERR_RPC_UNAVAILABLE_RESOURCE = exports.ERR_RPC_MISSING_RESOURCE = exports.ERR_RPC_INVALID_INPUT = exports.ERR_RPC_INTERNAL_ERROR = exports.ERR_RPC_INVALID_PARAMS = exports.ERR_RPC_INVALID_METHOD = exports.ERR_RPC_INVALID_REQUEST = exports.ERR_RPC_INVALID_JSON = exports.ERR_SCHEMA_FORMAT = exports.ERR_CORE_CHAIN_MISMATCH = exports.ERR_CORE_HARDFORK_MISMATCH = exports.ERR_VALIDATION = exports.ERR_INVALID_INTEGER = exports.ERR_INVALID_NIBBLE_WIDTH = exports.ERR_INVALID_TYPE_ABI = exports.ERR_INVALID_BLOCK = exports.ERR_INVALID_LARGE_VALUE = exports.ERR_INVALID_SIZE = exports.ERR_INVALID_UNSIGNED_INTEGER = exports.ERR_INVALID_BOOLEAN = exports.ERR_INVALID_TYPE = exports.ERR_INVALID_HEX = exports.ERR_INVALID_ADDRESS = exports.ERR_INVALID_UNIT = exports.ERR_INVALID_NUMBER = void 0;
// Response error
exports.ERR_RESPONSE = 100;
exports.ERR_INVALID_RESPONSE = 101;
// Generic errors
exports.ERR_PARAM = 200;
exports.ERR_FORMATTERS = 201;
exports.ERR_METHOD_NOT_IMPLEMENTED = 202;
exports.ERR_OPERATION_TIMEOUT = 203;
exports.ERR_OPERATION_ABORT = 204;
exports.ERR_ABI_ENCODING = 205;
exports.ERR_EXISTING_PLUGIN_NAMESPACE = 206;
exports.ERR_INVALID_METHOD_PARAMS = 207;
exports.ERR_MULTIPLE_ERRORS = 208;
// Contract error codes
exports.ERR_CONTRACT = 300;
exports.ERR_CONTRACT_RESOLVER_MISSING = 301;
exports.ERR_CONTRACT_ABI_MISSING = 302;
exports.ERR_CONTRACT_REQUIRED_CALLBACK = 303;
exports.ERR_CONTRACT_EVENT_NOT_EXISTS = 304;
exports.ERR_CONTRACT_RESERVED_EVENT = 305;
exports.ERR_CONTRACT_MISSING_DEPLOY_DATA = 306;
exports.ERR_CONTRACT_MISSING_ADDRESS = 307;
exports.ERR_CONTRACT_MISSING_FROM_ADDRESS = 308;
exports.ERR_CONTRACT_INSTANTIATION = 309;
exports.ERR_CONTRACT_EXECUTION_REVERTED = 310;
exports.ERR_CONTRACT_TX_DATA_AND_INPUT = 311;
// Transaction error codes
exports.ERR_TX = 400;
exports.ERR_TX_REVERT_INSTRUCTION = 401;
exports.ERR_TX_REVERT_TRANSACTION = 402;
exports.ERR_TX_NO_CONTRACT_ADDRESS = 403;
exports.ERR_TX_CONTRACT_NOT_STORED = 404;
exports.ERR_TX_REVERT_WITHOUT_REASON = 405;
exports.ERR_TX_OUT_OF_GAS = 406;
exports.ERR_RAW_TX_UNDEFINED = 407;
exports.ERR_TX_INVALID_SENDER = 408;
exports.ERR_TX_INVALID_CALL = 409;
exports.ERR_TX_MISSING_CUSTOM_CHAIN = 410;
exports.ERR_TX_MISSING_CUSTOM_CHAIN_ID = 411;
exports.ERR_TX_CHAIN_ID_MISMATCH = 412;
exports.ERR_TX_INVALID_CHAIN_INFO = 413;
exports.ERR_TX_MISSING_CHAIN_INFO = 414;
exports.ERR_TX_MISSING_GAS = 415;
exports.ERR_TX_INVALID_LEGACY_GAS = 416;
exports.ERR_TX_INVALID_FEE_MARKET_GAS = 417;
exports.ERR_TX_INVALID_FEE_MARKET_GAS_PRICE = 418;
exports.ERR_TX_INVALID_LEGACY_FEE_MARKET = 419;
exports.ERR_TX_INVALID_OBJECT = 420;
exports.ERR_TX_INVALID_NONCE_OR_CHAIN_ID = 421;
exports.ERR_TX_UNABLE_TO_POPULATE_NONCE = 422;
exports.ERR_TX_UNSUPPORTED_EIP_1559 = 423;
exports.ERR_TX_UNSUPPORTED_TYPE = 424;
exports.ERR_TX_DATA_AND_INPUT = 425;
exports.ERR_TX_POLLING_TIMEOUT = 426;
exports.ERR_TX_RECEIPT_MISSING_OR_BLOCKHASH_NULL = 427;
exports.ERR_TX_RECEIPT_MISSING_BLOCK_NUMBER = 428;
exports.ERR_TX_LOCAL_WALLET_NOT_AVAILABLE = 429;
exports.ERR_TX_NOT_FOUND = 430;
exports.ERR_TX_SEND_TIMEOUT = 431;
exports.ERR_TX_BLOCK_TIMEOUT = 432;
exports.ERR_TX_SIGNING = 433;
exports.ERR_TX_GAS_MISMATCH = 434;
exports.ERR_TX_CHAIN_MISMATCH = 435;
exports.ERR_TX_HARDFORK_MISMATCH = 436;
exports.ERR_TX_INVALID_RECEIVER = 437;
exports.ERR_TX_REVERT_TRANSACTION_CUSTOM_ERROR = 438;
exports.ERR_TX_INVALID_PROPERTIES_FOR_TYPE = 439;
exports.ERR_TX_MISSING_GAS_INNER_ERROR = 440;
exports.ERR_TX_GAS_MISMATCH_INNER_ERROR = 441;
// Connection error codes
exports.ERR_CONN = 500;
exports.ERR_CONN_INVALID = 501;
exports.ERR_CONN_TIMEOUT = 502;
exports.ERR_CONN_NOT_OPEN = 503;
exports.ERR_CONN_CLOSE = 504;
exports.ERR_CONN_MAX_ATTEMPTS = 505;
exports.ERR_CONN_PENDING_REQUESTS = 506;
exports.ERR_REQ_ALREADY_SENT = 507;
// Provider error codes
exports.ERR_PROVIDER = 600;
exports.ERR_INVALID_PROVIDER = 601;
exports.ERR_INVALID_CLIENT = 602;
exports.ERR_SUBSCRIPTION = 603;
exports.ERR_WS_PROVIDER = 604;
// Account error codes
exports.ERR_PRIVATE_KEY_LENGTH = 701;
exports.ERR_INVALID_PRIVATE_KEY = 702;
exports.ERR_UNSUPPORTED_KDF = 703;
exports.ERR_KEY_DERIVATION_FAIL = 704;
exports.ERR_KEY_VERSION_UNSUPPORTED = 705;
exports.ERR_INVALID_PASSWORD = 706;
exports.ERR_IV_LENGTH = 707;
exports.ERR_INVALID_KEYSTORE = 708;
exports.ERR_PBKDF2_ITERATIONS = 709;
// Signature error codes
exports.ERR_SIGNATURE_FAILED = 801;
exports.ERR_INVALID_SIGNATURE = 802;
exports.GENESIS_BLOCK_NUMBER = '0x0';
// RPC error codes (EIP-1193)
// https://github.com/ethereum/EIPs/blob/master/EIPS/eip-1193.md#provider-errors
exports.JSONRPC_ERR_REJECTED_REQUEST = 4001;
exports.JSONRPC_ERR_UNAUTHORIZED = 4100;
exports.JSONRPC_ERR_UNSUPPORTED_METHOD = 4200;
exports.JSONRPC_ERR_DISCONNECTED = 4900;
exports.JSONRPC_ERR_CHAIN_DISCONNECTED = 4901;
// ENS error codes
exports.ERR_ENS_CHECK_INTERFACE_SUPPORT = 901;
exports.ERR_ENS_UNSUPPORTED_NETWORK = 902;
exports.ERR_ENS_NETWORK_NOT_SYNCED = 903;
// Utils error codes
exports.ERR_INVALID_STRING = 1001;
exports.ERR_INVALID_BYTES = 1002;
exports.ERR_INVALID_NUMBER = 1003;
exports.ERR_INVALID_UNIT = 1004;
exports.ERR_INVALID_ADDRESS = 1005;
exports.ERR_INVALID_HEX = 1006;
exports.ERR_INVALID_TYPE = 1007;
exports.ERR_INVALID_BOOLEAN = 1008;
exports.ERR_INVALID_UNSIGNED_INTEGER = 1009;
exports.ERR_INVALID_SIZE = 1010;
exports.ERR_INVALID_LARGE_VALUE = 1011;
exports.ERR_INVALID_BLOCK = 1012;
exports.ERR_INVALID_TYPE_ABI = 1013;
exports.ERR_INVALID_NIBBLE_WIDTH = 1014;
exports.ERR_INVALID_INTEGER = 1015;
// Validation error codes
exports.ERR_VALIDATION = 1100;
// Core error codes
exports.ERR_CORE_HARDFORK_MISMATCH = 1101;
exports.ERR_CORE_CHAIN_MISMATCH = 1102;
// Schema error codes
exports.ERR_SCHEMA_FORMAT = 1200;
// RPC error codes (EIP-1474)
// https://github.com/ethereum/EIPs/blob/master/EIPS/eip-1474.md
exports.ERR_RPC_INVALID_JSON = -32700;
exports.ERR_RPC_INVALID_REQUEST = -32600;
exports.ERR_RPC_INVALID_METHOD = -32601;
exports.ERR_RPC_INVALID_PARAMS = -32602;
exports.ERR_RPC_INTERNAL_ERROR = -32603;
exports.ERR_RPC_INVALID_INPUT = -32000;
exports.ERR_RPC_MISSING_RESOURCE = -32001;
exports.ERR_RPC_UNAVAILABLE_RESOURCE = -32002;
exports.ERR_RPC_TRANSACTION_REJECTED = -32003;
exports.ERR_RPC_UNSUPPORTED_METHOD = -32004;
exports.ERR_RPC_LIMIT_EXCEEDED = -32005;
exports.ERR_RPC_NOT_SUPPORTED = -32006;
//# sourceMappingURL=error_codes.js.map