{"version": 3, "file": "cross-fetch.js", "sources": ["../node_modules/whatwg-fetch/fetch.js"], "sourcesContent": ["(function(self) {\n  'use strict';\n\n  if (self.fetch) {\n    return\n  }\n\n  var support = {\n    searchParams: 'URLSearchParams' in self,\n    iterable: 'Symbol' in self && 'iterator' in Symbol,\n    blob: 'FileReader' in self && 'Blob' in self && (function() {\n      try {\n        new Blob()\n        return true\n      } catch(e) {\n        return false\n      }\n    })(),\n    formData: 'FormData' in self,\n    arrayBuffer: 'ArrayBuffer' in self\n  }\n\n  if (support.arrayBuffer) {\n    var viewClasses = [\n      '[object Int8Array]',\n      '[object Uint8Array]',\n      '[object Uint8ClampedArray]',\n      '[object Int16Array]',\n      '[object Uint16Array]',\n      '[object Int32Array]',\n      '[object Uint32Array]',\n      '[object Float32Array]',\n      '[object Float64Array]'\n    ]\n\n    var isDataView = function(obj) {\n      return obj && DataView.prototype.isPrototypeOf(obj)\n    }\n\n    var isArrayBufferView = ArrayBuffer.isView || function(obj) {\n      return obj && viewClasses.indexOf(Object.prototype.toString.call(obj)) > -1\n    }\n  }\n\n  function normalizeName(name) {\n    if (typeof name !== 'string') {\n      name = String(name)\n    }\n    if (/[^a-z0-9\\-#$%&'*+.\\^_`|~]/i.test(name)) {\n      throw new TypeError('Invalid character in header field name')\n    }\n    return name.toLowerCase()\n  }\n\n  function normalizeValue(value) {\n    if (typeof value !== 'string') {\n      value = String(value)\n    }\n    return value\n  }\n\n  // Build a destructive iterator for the value list\n  function iteratorFor(items) {\n    var iterator = {\n      next: function() {\n        var value = items.shift()\n        return {done: value === undefined, value: value}\n      }\n    }\n\n    if (support.iterable) {\n      iterator[Symbol.iterator] = function() {\n        return iterator\n      }\n    }\n\n    return iterator\n  }\n\n  function Headers(headers) {\n    this.map = {}\n\n    if (headers instanceof Headers) {\n      headers.forEach(function(value, name) {\n        this.append(name, value)\n      }, this)\n    } else if (Array.isArray(headers)) {\n      headers.forEach(function(header) {\n        this.append(header[0], header[1])\n      }, this)\n    } else if (headers) {\n      Object.getOwnPropertyNames(headers).forEach(function(name) {\n        this.append(name, headers[name])\n      }, this)\n    }\n  }\n\n  Headers.prototype.append = function(name, value) {\n    name = normalizeName(name)\n    value = normalizeValue(value)\n    var oldValue = this.map[name]\n    this.map[name] = oldValue ? oldValue+','+value : value\n  }\n\n  Headers.prototype['delete'] = function(name) {\n    delete this.map[normalizeName(name)]\n  }\n\n  Headers.prototype.get = function(name) {\n    name = normalizeName(name)\n    return this.has(name) ? this.map[name] : null\n  }\n\n  Headers.prototype.has = function(name) {\n    return this.map.hasOwnProperty(normalizeName(name))\n  }\n\n  Headers.prototype.set = function(name, value) {\n    this.map[normalizeName(name)] = normalizeValue(value)\n  }\n\n  Headers.prototype.forEach = function(callback, thisArg) {\n    for (var name in this.map) {\n      if (this.map.hasOwnProperty(name)) {\n        callback.call(thisArg, this.map[name], name, this)\n      }\n    }\n  }\n\n  Headers.prototype.keys = function() {\n    var items = []\n    this.forEach(function(value, name) { items.push(name) })\n    return iteratorFor(items)\n  }\n\n  Headers.prototype.values = function() {\n    var items = []\n    this.forEach(function(value) { items.push(value) })\n    return iteratorFor(items)\n  }\n\n  Headers.prototype.entries = function() {\n    var items = []\n    this.forEach(function(value, name) { items.push([name, value]) })\n    return iteratorFor(items)\n  }\n\n  if (support.iterable) {\n    Headers.prototype[Symbol.iterator] = Headers.prototype.entries\n  }\n\n  function consumed(body) {\n    if (body.bodyUsed) {\n      return Promise.reject(new TypeError('Already read'))\n    }\n    body.bodyUsed = true\n  }\n\n  function fileReaderReady(reader) {\n    return new Promise(function(resolve, reject) {\n      reader.onload = function() {\n        resolve(reader.result)\n      }\n      reader.onerror = function() {\n        reject(reader.error)\n      }\n    })\n  }\n\n  function readBlobAsArrayBuffer(blob) {\n    var reader = new FileReader()\n    var promise = fileReaderReady(reader)\n    reader.readAsArrayBuffer(blob)\n    return promise\n  }\n\n  function readBlobAsText(blob) {\n    var reader = new FileReader()\n    var promise = fileReaderReady(reader)\n    reader.readAsText(blob)\n    return promise\n  }\n\n  function readArrayBufferAsText(buf) {\n    var view = new Uint8Array(buf)\n    var chars = new Array(view.length)\n\n    for (var i = 0; i < view.length; i++) {\n      chars[i] = String.fromCharCode(view[i])\n    }\n    return chars.join('')\n  }\n\n  function bufferClone(buf) {\n    if (buf.slice) {\n      return buf.slice(0)\n    } else {\n      var view = new Uint8Array(buf.byteLength)\n      view.set(new Uint8Array(buf))\n      return view.buffer\n    }\n  }\n\n  function Body() {\n    this.bodyUsed = false\n\n    this._initBody = function(body) {\n      this._bodyInit = body\n      if (!body) {\n        this._bodyText = ''\n      } else if (typeof body === 'string') {\n        this._bodyText = body\n      } else if (support.blob && Blob.prototype.isPrototypeOf(body)) {\n        this._bodyBlob = body\n      } else if (support.formData && FormData.prototype.isPrototypeOf(body)) {\n        this._bodyFormData = body\n      } else if (support.searchParams && URLSearchParams.prototype.isPrototypeOf(body)) {\n        this._bodyText = body.toString()\n      } else if (support.arrayBuffer && support.blob && isDataView(body)) {\n        this._bodyArrayBuffer = bufferClone(body.buffer)\n        // IE 10-11 can't handle a DataView body.\n        this._bodyInit = new Blob([this._bodyArrayBuffer])\n      } else if (support.arrayBuffer && (ArrayBuffer.prototype.isPrototypeOf(body) || isArrayBufferView(body))) {\n        this._bodyArrayBuffer = bufferClone(body)\n      } else {\n        throw new Error('unsupported BodyInit type')\n      }\n\n      if (!this.headers.get('content-type')) {\n        if (typeof body === 'string') {\n          this.headers.set('content-type', 'text/plain;charset=UTF-8')\n        } else if (this._bodyBlob && this._bodyBlob.type) {\n          this.headers.set('content-type', this._bodyBlob.type)\n        } else if (support.searchParams && URLSearchParams.prototype.isPrototypeOf(body)) {\n          this.headers.set('content-type', 'application/x-www-form-urlencoded;charset=UTF-8')\n        }\n      }\n    }\n\n    if (support.blob) {\n      this.blob = function() {\n        var rejected = consumed(this)\n        if (rejected) {\n          return rejected\n        }\n\n        if (this._bodyBlob) {\n          return Promise.resolve(this._bodyBlob)\n        } else if (this._bodyArrayBuffer) {\n          return Promise.resolve(new Blob([this._bodyArrayBuffer]))\n        } else if (this._bodyFormData) {\n          throw new Error('could not read FormData body as blob')\n        } else {\n          return Promise.resolve(new Blob([this._bodyText]))\n        }\n      }\n\n      this.arrayBuffer = function() {\n        if (this._bodyArrayBuffer) {\n          return consumed(this) || Promise.resolve(this._bodyArrayBuffer)\n        } else {\n          return this.blob().then(readBlobAsArrayBuffer)\n        }\n      }\n    }\n\n    this.text = function() {\n      var rejected = consumed(this)\n      if (rejected) {\n        return rejected\n      }\n\n      if (this._bodyBlob) {\n        return readBlobAsText(this._bodyBlob)\n      } else if (this._bodyArrayBuffer) {\n        return Promise.resolve(readArrayBufferAsText(this._bodyArrayBuffer))\n      } else if (this._bodyFormData) {\n        throw new Error('could not read FormData body as text')\n      } else {\n        return Promise.resolve(this._bodyText)\n      }\n    }\n\n    if (support.formData) {\n      this.formData = function() {\n        return this.text().then(decode)\n      }\n    }\n\n    this.json = function() {\n      return this.text().then(JSON.parse)\n    }\n\n    return this\n  }\n\n  // HTTP methods whose capitalization should be normalized\n  var methods = ['DELETE', 'GET', 'HEAD', 'OPTIONS', 'POST', 'PUT']\n\n  function normalizeMethod(method) {\n    var upcased = method.toUpperCase()\n    return (methods.indexOf(upcased) > -1) ? upcased : method\n  }\n\n  function Request(input, options) {\n    options = options || {}\n    var body = options.body\n\n    if (input instanceof Request) {\n      if (input.bodyUsed) {\n        throw new TypeError('Already read')\n      }\n      this.url = input.url\n      this.credentials = input.credentials\n      if (!options.headers) {\n        this.headers = new Headers(input.headers)\n      }\n      this.method = input.method\n      this.mode = input.mode\n      if (!body && input._bodyInit != null) {\n        body = input._bodyInit\n        input.bodyUsed = true\n      }\n    } else {\n      this.url = String(input)\n    }\n\n    this.credentials = options.credentials || this.credentials || 'omit'\n    if (options.headers || !this.headers) {\n      this.headers = new Headers(options.headers)\n    }\n    this.method = normalizeMethod(options.method || this.method || 'GET')\n    this.mode = options.mode || this.mode || null\n    this.referrer = null\n\n    if ((this.method === 'GET' || this.method === 'HEAD') && body) {\n      throw new TypeError('Body not allowed for GET or HEAD requests')\n    }\n    this._initBody(body)\n  }\n\n  Request.prototype.clone = function() {\n    return new Request(this, { body: this._bodyInit })\n  }\n\n  function decode(body) {\n    var form = new FormData()\n    body.trim().split('&').forEach(function(bytes) {\n      if (bytes) {\n        var split = bytes.split('=')\n        var name = split.shift().replace(/\\+/g, ' ')\n        var value = split.join('=').replace(/\\+/g, ' ')\n        form.append(decodeURIComponent(name), decodeURIComponent(value))\n      }\n    })\n    return form\n  }\n\n  function parseHeaders(rawHeaders) {\n    var headers = new Headers()\n    // Replace instances of \\r\\n and \\n followed by at least one space or horizontal tab with a space\n    // https://tools.ietf.org/html/rfc7230#section-3.2\n    var preProcessedHeaders = rawHeaders.replace(/\\r?\\n[\\t ]+/g, ' ')\n    preProcessedHeaders.split(/\\r?\\n/).forEach(function(line) {\n      var parts = line.split(':')\n      var key = parts.shift().trim()\n      if (key) {\n        var value = parts.join(':').trim()\n        headers.append(key, value)\n      }\n    })\n    return headers\n  }\n\n  Body.call(Request.prototype)\n\n  function Response(bodyInit, options) {\n    if (!options) {\n      options = {}\n    }\n\n    this.type = 'default'\n    this.status = options.status === undefined ? 200 : options.status\n    this.ok = this.status >= 200 && this.status < 300\n    this.statusText = 'statusText' in options ? options.statusText : 'OK'\n    this.headers = new Headers(options.headers)\n    this.url = options.url || ''\n    this._initBody(bodyInit)\n  }\n\n  Body.call(Response.prototype)\n\n  Response.prototype.clone = function() {\n    return new Response(this._bodyInit, {\n      status: this.status,\n      statusText: this.statusText,\n      headers: new Headers(this.headers),\n      url: this.url\n    })\n  }\n\n  Response.error = function() {\n    var response = new Response(null, {status: 0, statusText: ''})\n    response.type = 'error'\n    return response\n  }\n\n  var redirectStatuses = [301, 302, 303, 307, 308]\n\n  Response.redirect = function(url, status) {\n    if (redirectStatuses.indexOf(status) === -1) {\n      throw new RangeError('Invalid status code')\n    }\n\n    return new Response(null, {status: status, headers: {location: url}})\n  }\n\n  self.Headers = Headers\n  self.Request = Request\n  self.Response = Response\n\n  self.fetch = function(input, init) {\n    return new Promise(function(resolve, reject) {\n      var request = new Request(input, init)\n      var xhr = new XMLHttpRequest()\n\n      xhr.onload = function() {\n        var options = {\n          status: xhr.status,\n          statusText: xhr.statusText,\n          headers: parseHeaders(xhr.getAllResponseHeaders() || '')\n        }\n        options.url = 'responseURL' in xhr ? xhr.responseURL : options.headers.get('X-Request-URL')\n        var body = 'response' in xhr ? xhr.response : xhr.responseText\n        resolve(new Response(body, options))\n      }\n\n      xhr.onerror = function() {\n        reject(new TypeError('Network request failed'))\n      }\n\n      xhr.ontimeout = function() {\n        reject(new TypeError('Network request failed'))\n      }\n\n      xhr.open(request.method, request.url, true)\n\n      if (request.credentials === 'include') {\n        xhr.withCredentials = true\n      } else if (request.credentials === 'omit') {\n        xhr.withCredentials = false\n      }\n\n      if ('responseType' in xhr && support.blob) {\n        xhr.responseType = 'blob'\n      }\n\n      request.headers.forEach(function(value, name) {\n        xhr.setRequestHeader(name, value)\n      })\n\n      xhr.send(typeof request._bodyInit === 'undefined' ? null : request._bodyInit)\n    })\n  }\n  self.fetch.polyfill = true\n})(typeof self !== 'undefined' ? self : this);\n"], "names": ["self", "fetch", "support", "Symbol", "Blob", "e", "viewClasses", "isDataView", "obj", "DataView", "prototype", "isPrototypeOf", "isArrayBuffer<PERSON>iew", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "indexOf", "Object", "toString", "call", "Headers", "append", "name", "value", "normalizeName", "normalizeValue", "oldValue", "this", "map", "get", "has", "hasOwnProperty", "set", "for<PERSON>ach", "callback", "thisArg", "keys", "items", "push", "iteratorFor", "values", "entries", "iterator", "methods", "Request", "clone", "body", "_bodyInit", "Body", "Response", "status", "statusText", "headers", "url", "error", "response", "type", "redirectStatuses", "redirect", "RangeError", "location", "input", "init", "Promise", "resolve", "reject", "request", "xhr", "XMLHttpRequest", "onload", "rawHeaders", "options", "getAllResponseHeaders", "replace", "split", "line", "parts", "key", "shift", "trim", "join", "responseURL", "responseText", "onerror", "TypeError", "ontimeout", "open", "method", "credentials", "withCredentials", "responseType", "setRequestHeader", "send", "polyfill", "String", "test", "toLowerCase", "next", "done", "undefined", "Array", "isArray", "header", "getOwnPropertyNames", "consumed", "bodyUsed", "fileReaderReady", "reader", "result", "readBlobAsArrayBuffer", "blob", "FileReader", "promise", "readAsA<PERSON>y<PERSON><PERSON>er", "bufferClone", "buf", "slice", "view", "Uint8Array", "byteLength", "buffer", "_initBody", "_bodyText", "_bodyBlob", "FormData", "_bodyFormData", "URLSearchParams", "_body<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Error", "rejected", "arrayBuffer", "then", "text", "readAsText", "chars", "length", "i", "fromCharCode", "readArrayBufferAsText", "formData", "decode", "json", "JSON", "parse", "upcased", "mode", "toUpperCase", "referrer", "form", "bytes", "decodeURIComponent", "bodyInit", "ok"], "mappings": "CAAA,SAAUA,GAGR,IAAIA,EAAKC,MAAT,CAIA,IAAIC,EACY,oBAAqBF,EADjCE,EAEQ,WAAYF,GAAQ,aAAcG,OAF1CD,EAGI,eAAgBF,GAAQ,SAAUA,GAAQ,WAC9C,IAEE,OADA,IAAII,MACG,EACP,MAAMC,GACN,OAAO,GALqC,GAH9CH,EAWQ,aAAcF,EAXtBE,EAYW,gBAAiBF,EAGhC,GAAIE,EACF,IAAII,EAAc,CAChB,qBACA,sBACA,6BACA,sBACA,uBACA,sBACA,uBACA,wBACA,yBAGEC,EAAa,SAASC,GACxB,OAAOA,GAAOC,SAASC,UAAUC,cAAcH,IAG7CI,EAAoBC,YAAYC,QAAU,SAASN,GACrD,OAAOA,IAAmE,EAA5DF,EAAYS,QAAQC,OAAON,UAAUO,SAASC,KAAKV,KAyDrEW,EAAQT,UAAUU,OAAS,SAASC,EAAMC,GACxCD,EAAOE,EAAcF,GACrBC,EAAQE,EAAeF,GACvB,IAAIG,EAAWC,KAAKC,IAAIN,GACxBK,KAAKC,IAAIN,GAAQI,EAAWA,EAAS,IAAIH,EAAQA,GAGnDH,EAAQT,UAAkB,OAAI,SAASW,UAC9BK,KAAKC,IAAIJ,EAAcF,KAGhCF,EAAQT,UAAUkB,IAAM,SAASP,GAE/B,OADAA,EAAOE,EAAcF,GACdK,KAAKG,IAAIR,GAAQK,KAAKC,IAAIN,GAAQ,MAG3CF,EAAQT,UAAUmB,IAAM,SAASR,GAC/B,OAAOK,KAAKC,IAAIG,eAAeP,EAAcF,KAG/CF,EAAQT,UAAUqB,IAAM,SAASV,EAAMC,GACrCI,KAAKC,IAAIJ,EAAcF,IAASG,EAAeF,IAGjDH,EAAQT,UAAUsB,QAAU,SAASC,EAAUC,GAC7C,IAAK,IAAIb,KAAQK,KAAKC,IAChBD,KAAKC,IAAIG,eAAeT,IAC1BY,EAASf,KAAKgB,EAASR,KAAKC,IAAIN,GAAOA,EAAMK,OAKnDP,EAAQT,UAAUyB,KAAO,WACvB,IAAIC,EAAQ,GAEZ,OADAV,KAAKM,QAAQ,SAASV,EAAOD,GAAQe,EAAMC,KAAKhB,KACzCiB,EAAYF,IAGrBjB,EAAQT,UAAU6B,OAAS,WACzB,IAAIH,EAAQ,GAEZ,OADAV,KAAKM,QAAQ,SAASV,GAASc,EAAMC,KAAKf,KACnCgB,EAAYF,IAGrBjB,EAAQT,UAAU8B,QAAU,WAC1B,IAAIJ,EAAQ,GAEZ,OADAV,KAAKM,QAAQ,SAASV,EAAOD,GAAQe,EAAMC,KAAK,CAAChB,EAAMC,MAChDgB,EAAYF,IAGjBlC,IACFiB,EAAQT,UAAUP,OAAOsC,UAAYtB,EAAQT,UAAU8B,SAqJzD,IAAIE,EAAU,CAAC,SAAU,MAAO,OAAQ,UAAW,OAAQ,OA4C3DC,EAAQjC,UAAUkC,MAAQ,WACxB,OAAO,IAAID,EAAQjB,KAAM,CAAEmB,KAAMnB,KAAKoB,aAgCxCC,EAAK7B,KAAKyB,EAAQjC,WAgBlBqC,EAAK7B,KAAK8B,EAAStC,WAEnBsC,EAAStC,UAAUkC,MAAQ,WACzB,OAAO,IAAII,EAAStB,KAAKoB,UAAW,CAClCG,OAAQvB,KAAKuB,OACbC,WAAYxB,KAAKwB,WACjBC,QAAS,IAAIhC,EAAQO,KAAKyB,SAC1BC,IAAK1B,KAAK0B,OAIdJ,EAASK,MAAQ,WACf,IAAIC,EAAW,IAAIN,EAAS,KAAM,CAACC,OAAQ,EAAGC,WAAY,KAE1D,OADAI,EAASC,KAAO,QACTD,GAGT,IAAIE,EAAmB,CAAC,IAAK,IAAK,IAAK,IAAK,KAE5CR,EAASS,SAAW,SAASL,EAAKH,GAChC,IAA0C,IAAtCO,EAAiBzC,QAAQkC,GAC3B,MAAM,IAAIS,WAAW,uBAGvB,OAAO,IAAIV,EAAS,KAAM,CAACC,OAAQA,EAAQE,QAAS,CAACQ,SAAUP,MAGjEpD,EAAKmB,QAAUA,EACfnB,EAAK2C,QAAUA,EACf3C,EAAKgD,SAAWA,EAEhBhD,EAAKC,MAAQ,SAAS2D,EAAOC,GAC3B,OAAO,IAAIC,QAAQ,SAASC,EAASC,GACnC,IAAIC,EAAU,IAAItB,EAAQiB,EAAOC,GAC7BK,EAAM,IAAIC,eAEdD,EAAIE,OAAS,WACX,IArEgBC,EAChBlB,EAoEImB,EAAU,CACZrB,OAAQiB,EAAIjB,OACZC,WAAYgB,EAAIhB,WAChBC,SAxEckB,EAwEQH,EAAIK,yBAA2B,GAvEvDpB,EAAU,IAAIhC,EAGQkD,EAAWG,QAAQ,eAAgB,KACzCC,MAAM,SAASzC,QAAQ,SAAS0C,GAClD,IAAIC,EAAQD,EAAKD,MAAM,KACnBG,EAAMD,EAAME,QAAQC,OACxB,GAAIF,EAAK,CACP,IAAItD,EAAQqD,EAAMI,KAAK,KAAKD,OAC5B3B,EAAQ/B,OAAOwD,EAAKtD,MAGjB6B,IA6DHmB,EAAQlB,IAAM,gBAAiBc,EAAMA,EAAIc,YAAcV,EAAQnB,QAAQvB,IAAI,iBAC3E,IAAIiB,EAAO,aAAcqB,EAAMA,EAAIZ,SAAWY,EAAIe,aAClDlB,EAAQ,IAAIf,EAASH,EAAMyB,KAG7BJ,EAAIgB,QAAU,WACZlB,EAAO,IAAImB,UAAU,4BAGvBjB,EAAIkB,UAAY,WACdpB,EAAO,IAAImB,UAAU,4BAGvBjB,EAAImB,KAAKpB,EAAQqB,OAAQrB,EAAQb,KAAK,GAEV,YAAxBa,EAAQsB,YACVrB,EAAIsB,iBAAkB,EACW,SAAxBvB,EAAQsB,cACjBrB,EAAIsB,iBAAkB,GAGpB,iBAAkBtB,GAAOhE,IAC3BgE,EAAIuB,aAAe,QAGrBxB,EAAQd,QAAQnB,QAAQ,SAASV,EAAOD,GACtC6C,EAAIwB,iBAAiBrE,EAAMC,KAG7B4C,EAAIyB,UAAkC,IAAtB1B,EAAQnB,UAA4B,KAAOmB,EAAQnB,cAGvE9C,EAAKC,MAAM2F,UAAW,EApatB,SAASrE,EAAcF,GAIrB,GAHoB,iBAATA,IACTA,EAAOwE,OAAOxE,IAEZ,6BAA6ByE,KAAKzE,GACpC,MAAM,IAAI8D,UAAU,0CAEtB,OAAO9D,EAAK0E,cAGd,SAASvE,EAAeF,GAItB,MAHqB,iBAAVA,IACTA,EAAQuE,OAAOvE,IAEVA,EAIT,SAASgB,EAAYF,GACnB,IAAIK,EAAW,CACbuD,KAAM,WACJ,IAAI1E,EAAQc,EAAMyC,QAClB,MAAO,CAACoB,UAAgBC,IAAV5E,EAAqBA,MAAOA,KAU9C,OANIpB,IACFuC,EAAStC,OAAOsC,UAAY,WAC1B,OAAOA,IAIJA,EAGT,SAAStB,EAAQgC,GACfzB,KAAKC,IAAM,GAEPwB,aAAmBhC,EACrBgC,EAAQnB,QAAQ,SAASV,EAAOD,GAC9BK,KAAKN,OAAOC,EAAMC,IACjBI,MACMyE,MAAMC,QAAQjD,GACvBA,EAAQnB,QAAQ,SAASqE,GACvB3E,KAAKN,OAAOiF,EAAO,GAAIA,EAAO,KAC7B3E,MACMyB,GACTnC,OAAOsF,oBAAoBnD,GAASnB,QAAQ,SAASX,GACnDK,KAAKN,OAAOC,EAAM8B,EAAQ9B,KACzBK,MA0DP,SAAS6E,EAAS1D,GAChB,GAAIA,EAAK2D,SACP,OAAO1C,QAAQE,OAAO,IAAImB,UAAU,iBAEtCtC,EAAK2D,UAAW,EAGlB,SAASC,EAAgBC,GACvB,OAAO,IAAI5C,QAAQ,SAASC,EAASC,GACnC0C,EAAOtC,OAAS,WACdL,EAAQ2C,EAAOC,SAEjBD,EAAOxB,QAAU,WACflB,EAAO0C,EAAOrD,UAKpB,SAASuD,EAAsBC,GAC7B,IAAIH,EAAS,IAAII,WACbC,EAAUN,EAAgBC,GAE9B,OADAA,EAAOM,kBAAkBH,GAClBE,EAoBT,SAASE,EAAYC,GACnB,GAAIA,EAAIC,MACN,OAAOD,EAAIC,MAAM,GAEjB,IAAIC,EAAO,IAAIC,WAAWH,EAAII,YAE9B,OADAF,EAAKrF,IAAI,IAAIsF,WAAWH,IACjBE,EAAKG,OAIhB,SAASxE,IA0FP,OAzFArB,KAAK8E,UAAW,EAEhB9E,KAAK8F,UAAY,SAAS3E,GAExB,GADAnB,KAAKoB,UAAYD,EAGV,GAAoB,iBAATA,EAChBnB,KAAK+F,UAAY5E,OACZ,GAAI3C,GAAgBE,KAAKM,UAAUC,cAAckC,GACtDnB,KAAKgG,UAAY7E,OACZ,GAAI3C,GAAoByH,SAASjH,UAAUC,cAAckC,GAC9DnB,KAAKkG,cAAgB/E,OAChB,GAAI3C,GAAwB2H,gBAAgBnH,UAAUC,cAAckC,GACzEnB,KAAK+F,UAAY5E,EAAK5B,gBACjB,GAAIf,GAAuBA,GAAgBK,EAAWsC,GAC3DnB,KAAKoG,iBAAmBb,EAAYpE,EAAK0E,QAEzC7F,KAAKoB,UAAY,IAAI1C,KAAK,CAACsB,KAAKoG,uBAC3B,CAAA,IAAI5H,IAAwBW,YAAYH,UAAUC,cAAckC,KAASjC,EAAkBiC,GAGhG,MAAM,IAAIkF,MAAM,6BAFhBrG,KAAKoG,iBAAmBb,EAAYpE,QAdpCnB,KAAK+F,UAAY,GAmBd/F,KAAKyB,QAAQvB,IAAI,kBACA,iBAATiB,EACTnB,KAAKyB,QAAQpB,IAAI,eAAgB,4BACxBL,KAAKgG,WAAahG,KAAKgG,UAAUnE,KAC1C7B,KAAKyB,QAAQpB,IAAI,eAAgBL,KAAKgG,UAAUnE,MACvCrD,GAAwB2H,gBAAgBnH,UAAUC,cAAckC,IACzEnB,KAAKyB,QAAQpB,IAAI,eAAgB,qDAKnC7B,IACFwB,KAAKmF,KAAO,WACV,IAAImB,EAAWzB,EAAS7E,MACxB,GAAIsG,EACF,OAAOA,EAGT,GAAItG,KAAKgG,UACP,OAAO5D,QAAQC,QAAQrC,KAAKgG,WACvB,GAAIhG,KAAKoG,iBACd,OAAOhE,QAAQC,QAAQ,IAAI3D,KAAK,CAACsB,KAAKoG,oBACjC,GAAIpG,KAAKkG,cACd,MAAM,IAAIG,MAAM,wCAEhB,OAAOjE,QAAQC,QAAQ,IAAI3D,KAAK,CAACsB,KAAK+F,cAI1C/F,KAAKuG,YAAc,WACjB,OAAIvG,KAAKoG,iBACAvB,EAAS7E,OAASoC,QAAQC,QAAQrC,KAAKoG,kBAEvCpG,KAAKmF,OAAOqB,KAAKtB,KAK9BlF,KAAKyG,KAAO,WACV,IA3FoBtB,EAClBH,EACAK,EAyFEiB,EAAWzB,EAAS7E,MACxB,GAAIsG,EACF,OAAOA,EAGT,GAAItG,KAAKgG,UACP,OAjGkBb,EAiGInF,KAAKgG,UAhG3BhB,EAAS,IAAII,WACbC,EAAUN,EAAgBC,GAC9BA,EAAO0B,WAAWvB,GACXE,EA8FE,GAAIrF,KAAKoG,iBACd,OAAOhE,QAAQC,QA5FrB,SAA+BmD,GAI7B,IAHA,IAAIE,EAAO,IAAIC,WAAWH,GACtBmB,EAAQ,IAAIlC,MAAMiB,EAAKkB,QAElBC,EAAI,EAAGA,EAAInB,EAAKkB,OAAQC,IAC/BF,EAAME,GAAK1C,OAAO2C,aAAapB,EAAKmB,IAEtC,OAAOF,EAAMtD,KAAK,IAqFS0D,CAAsB/G,KAAKoG,mBAC7C,GAAIpG,KAAKkG,cACd,MAAM,IAAIG,MAAM,wCAEhB,OAAOjE,QAAQC,QAAQrC,KAAK+F,YAI5BvH,IACFwB,KAAKgH,SAAW,WACd,OAAOhH,KAAKyG,OAAOD,KAAKS,KAI5BjH,KAAKkH,KAAO,WACV,OAAOlH,KAAKyG,OAAOD,KAAKW,KAAKC,QAGxBpH,KAWT,SAASiB,EAAQiB,EAAOU,GAEtB,IAPuBgB,EACnByD,EAMAlG,GADJyB,EAAUA,GAAW,IACFzB,KAEnB,GAAIe,aAAiBjB,EAAS,CAC5B,GAAIiB,EAAM4C,SACR,MAAM,IAAIrB,UAAU,gBAEtBzD,KAAK0B,IAAMQ,EAAMR,IACjB1B,KAAK6D,YAAc3B,EAAM2B,YACpBjB,EAAQnB,UACXzB,KAAKyB,QAAU,IAAIhC,EAAQyC,EAAMT,UAEnCzB,KAAK4D,OAAS1B,EAAM0B,OACpB5D,KAAKsH,KAAOpF,EAAMoF,KACbnG,GAA2B,MAAnBe,EAAMd,YACjBD,EAAOe,EAAMd,UACbc,EAAM4C,UAAW,QAGnB9E,KAAK0B,IAAMyC,OAAOjC,GAWpB,GARAlC,KAAK6D,YAAcjB,EAAQiB,aAAe7D,KAAK6D,aAAe,QAC1DjB,EAAQnB,SAAYzB,KAAKyB,UAC3BzB,KAAKyB,QAAU,IAAIhC,EAAQmD,EAAQnB,UAErCzB,KAAK4D,QAhCkBA,EAgCOhB,EAAQgB,QAAU5D,KAAK4D,QAAU,MA/B3DyD,EAAUzD,EAAO2D,eACe,EAA5BvG,EAAQ3B,QAAQgI,GAAiBA,EAAUzD,GA+BnD5D,KAAKsH,KAAO1E,EAAQ0E,MAAQtH,KAAKsH,MAAQ,KACzCtH,KAAKwH,SAAW,MAEK,QAAhBxH,KAAK4D,QAAoC,SAAhB5D,KAAK4D,SAAsBzC,EACvD,MAAM,IAAIsC,UAAU,6CAEtBzD,KAAK8F,UAAU3E,GAOjB,SAAS8F,EAAO9F,GACd,IAAIsG,EAAO,IAAIxB,SASf,OARA9E,EAAKiC,OAAOL,MAAM,KAAKzC,QAAQ,SAASoH,GACtC,GAAIA,EAAO,CACT,IAAI3E,EAAQ2E,EAAM3E,MAAM,KACpBpD,EAAOoD,EAAMI,QAAQL,QAAQ,MAAO,KACpClD,EAAQmD,EAAMM,KAAK,KAAKP,QAAQ,MAAO,KAC3C2E,EAAK/H,OAAOiI,mBAAmBhI,GAAOgI,mBAAmB/H,OAGtD6H,EAqBT,SAASnG,EAASsG,EAAUhF,GACrBA,IACHA,EAAU,IAGZ5C,KAAK6B,KAAO,UACZ7B,KAAKuB,YAA4BiD,IAAnB5B,EAAQrB,OAAuB,IAAMqB,EAAQrB,OAC3DvB,KAAK6H,GAAoB,KAAf7H,KAAKuB,QAAiBvB,KAAKuB,OAAS,IAC9CvB,KAAKwB,WAAa,eAAgBoB,EAAUA,EAAQpB,WAAa,KACjExB,KAAKyB,QAAU,IAAIhC,EAAQmD,EAAQnB,SACnCzB,KAAK0B,IAAMkB,EAAQlB,KAAO,GAC1B1B,KAAK8F,UAAU8B,IAnYnB,CAidmB,oBAATtJ,KAAuBA,KAAO0B"}