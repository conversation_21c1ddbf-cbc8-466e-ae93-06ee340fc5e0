# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.3.0](https://github.com/ljharb/json-stable-stringify/compare/v1.2.1...v1.3.0) - 2025-04-21

### Fixed

- [New] add `collapseEmpty` option [`#15`](https://github.com/ljharb/json-stable-stringify/issues/15)

### Commits

- [Dev Deps] update `@arethetypeswrong/cli`, `@ljharb/tsconfig`, `@types/tape` [`1ac9c16`](https://github.com/ljharb/json-stable-stringify/commit/1ac9c164666d4f70b216de0a7bb142ced5de2995)
- [Deps] update `call-bound` [`729fcba`](https://github.com/ljharb/json-stable-stringify/commit/729fcba5d6f6b3b8599cc22e434272defc0a237f)

## [v1.2.1](https://github.com/ljharb/json-stable-stringify/compare/v1.2.0...v1.2.1) - 2024-12-21

### Commits

- [types] input is unknown, can be non object [`7135fba`](https://github.com/ljharb/json-stable-stringify/commit/7135fba3f03c6f97d801047c53fab90c26aacfa8)
- [Dev Deps] update `@arethetypeswrong/cli`, `@types/tape` [`504a644`](https://github.com/ljharb/json-stable-stringify/commit/504a6442880d45ae2548b309a9f5cf0b488176db)

## [v1.2.0](https://github.com/ljharb/json-stable-stringify/compare/v1.1.1...v1.2.0) - 2024-12-17

### Fixed

- [readme] remove dead badges [`#14`](https://github.com/ljharb/json-stable-stringify/issues/14)

### Commits

- [New] add types [`5dbd6c8`](https://github.com/ljharb/json-stable-stringify/commit/5dbd6c802fe013082e597ecf6a8c3428e60d906b)
- [eslint] clean up formatting [`21e95e5`](https://github.com/ljharb/json-stable-stringify/commit/21e95e57ea55c6b7e8c63835391b1791a8ed9323)
- [meta] sort package.json [`a9f44d5`](https://github.com/ljharb/json-stable-stringify/commit/a9f44d5e532e93e7cc48e384472c0a9da189bab9)
- [actions] split out node 10-20, and 20+ [`74551e4`](https://github.com/ljharb/json-stable-stringify/commit/74551e4cc76ae90880b0471365de47d1c3dd1379)
- [Tests] add test coverage for options provided directly on a cmp function [`0a50205`](https://github.com/ljharb/json-stable-stringify/commit/0a502052b9191f53f072599aac61aa829ac9e0ae)
- [Robustness] cache more builtins [`d390c99`](https://github.com/ljharb/json-stable-stringify/commit/d390c99889ec80a20b77a9f73d8d8134f0fc11b8)
- [Dev Deps] update `@ljharb/eslint-config`, `auto-changelog`, `tape` [`03686a0`](https://github.com/ljharb/json-stable-stringify/commit/03686a0af26444bac661e8ca0e70e3d13a4938d0)
- [Tests] key ordering is reversed in node 11+ [`7034a17`](https://github.com/ljharb/json-stable-stringify/commit/7034a176d0dde8df1f899bf0f1c44e73f5792947)
- [Dev Deps] update `npmignore`, `tape` [`ba8d519`](https://github.com/ljharb/json-stable-stringify/commit/ba8d519505f59725c9f4b0451332f6d64801a910)
- [Refactor] use `call-bound` directly [`850b24c`](https://github.com/ljharb/json-stable-stringify/commit/850b24c5b3dc69b59804637c92c10f7bb3277ab8)
- [Tests] replace `aud` with `npm audit` [`22fb720`](https://github.com/ljharb/json-stable-stringify/commit/22fb72061005f9b124a0dc84f8b87c3a977c00bd)
- [Deps] update `call-bind` [`adc30b0`](https://github.com/ljharb/json-stable-stringify/commit/adc30b0746b58d469492e7586b1d32469dce4783)
- [Deps] update `call-bind` [`a280582`](https://github.com/ljharb/json-stable-stringify/commit/a280582e6b8bb6e04642010931b60f9fda2fa0df)
- [Dev Deps] add missing peer dep [`3bb517c`](https://github.com/ljharb/json-stable-stringify/commit/3bb517cc179cd90e841581046791d24cc2bee66a)

## [v1.1.1](https://github.com/ljharb/json-stable-stringify/compare/v1.1.0...v1.1.1) - 2024-01-16

### Fixed

- [Performance] use an array join instead of a string. [`#9`](https://github.com/ljharb/json-stable-stringify/issues/9)

### Commits

- [readme] replaced var with const [`e22d419`](https://github.com/ljharb/json-stable-stringify/commit/e22d419b54d8fd1dc36aa18b685da0032c74ec0f)
- [Dev Deps] update `aud`, `tape` [`dc26af2`](https://github.com/ljharb/json-stable-stringify/commit/dc26af2cac5caa4e9ecb72f384c3c652aa612457)

## [v1.1.0](https://github.com/ljharb/json-stable-stringify/compare/v1.0.2...v1.1.0) - 2023-11-13

### Commits

- [New] `opts.cmp`: add `get` function [`1b11748`](https://github.com/ljharb/json-stable-stringify/commit/1b117480abdfe8d2a29d3ce9fefc46d7181ba2fa)
- [meta] update license text so GitHub can identify it [`fd520e1`](https://github.com/ljharb/json-stable-stringify/commit/fd520e17af7121e2d8e678fe7c296c2b617b0b74)
- [Refactor] use `isarray`, `object-keys` instead of homegrown attempts [`d1d2038`](https://github.com/ljharb/json-stable-stringify/commit/d1d20388f579d6a92fce70fbd00a0af36bd8d097)
- [Refactor] build up a string instead of an array + join [`6c066b8`](https://github.com/ljharb/json-stable-stringify/commit/6c066b82708eb7e7ca0ca7f89737df48aa534a6c)
- [Refactor] avoid an IIFE [`8243ea1`](https://github.com/ljharb/json-stable-stringify/commit/8243ea1a4c780c126b8800334b398e1c5e2ed9f9)
- [Perf] avoid creating an options object when not needed [`02f0778`](https://github.com/ljharb/json-stable-stringify/commit/02f0778989960dfd46781b231fa3d06e9519befa)
- [Refactor] avoid `new Array` [`80d52a1`](https://github.com/ljharb/json-stable-stringify/commit/80d52a197d8e695a6b949c9839136b72606d7bf1)
- [Robustness] use `call-bind` to invoke replacer [`c52438f`](https://github.com/ljharb/json-stable-stringify/commit/c52438fe222554b3f138cebbeac55844b5614451)
- [Robustness] cache `JSON.stringify` at module load [`616dec3`](https://github.com/ljharb/json-stable-stringify/commit/616dec38c80db6f94cdf9c2bcc175f9e7d8bc570)
- [Dev Deps] update `@ljharb/eslint-config`, `aud`, `tape` [`494a3ce`](https://github.com/ljharb/json-stable-stringify/commit/494a3ce7cc1fd2aa56981af68c037c802979378e)
- [Dev Deps] update `@ljharb/eslint-config`, `aud`, `tape` [`861ea7d`](https://github.com/ljharb/json-stable-stringify/commit/861ea7d38700ee3a50721c4299f4e967394129d7)
- [Refactor] avoid recreating default replacer [`4e95ebb`](https://github.com/ljharb/json-stable-stringify/commit/4e95ebb69e17b5e40af9be363c3216b1fcb91517)
- [Tests] remove unused travis.yml [`1226971`](https://github.com/ljharb/json-stable-stringify/commit/12269716dd570af4cb21e87bf9156911e1c6b82b)
- [meta] add missing `engines.node` [`7a80ff6`](https://github.com/ljharb/json-stable-stringify/commit/7a80ff6a9ba24801f58f1d1175b6527accdf9cd0)

## [v1.0.2](https://github.com/ljharb/json-stable-stringify/compare/v1.0.1...v1.0.2) - 2022-11-07

### Commits

- [eslint] fix indentation and whitespace [`c97e78c`](https://github.com/ljharb/json-stable-stringify/commit/c97e78cf3c0695701095dc0036681182585a6392)
- [eslint] more cleanup [`c162117`](https://github.com/ljharb/json-stable-stringify/commit/c162117489c6dc63ece402b4a9b6e566f109fa65)
- [meta] add `auto-changelog` [`83934ff`](https://github.com/ljharb/json-stable-stringify/commit/83934ffbbb3e72b9da09bf6436e1f86e7dce3b74)
- [actions] add reusable workflows [`7b24830`](https://github.com/ljharb/json-stable-stringify/commit/7b248309f6ba87e2e52f99485c1f8b209b5788dc)
- [readme] rename, add badges [`5433588`](https://github.com/ljharb/json-stable-stringify/commit/5433588781ebd98e41c81b5bfed1fb67520cf171)
- [eslint] add eslint [`7be6c27`](https://github.com/ljharb/json-stable-stringify/commit/7be6c2755a7e2ead43017761b248a21511e457a0)
- [meta] create FUNDING.yml; add `funding` in package.json [`6edbece`](https://github.com/ljharb/json-stable-stringify/commit/6edbece874fb656b9957b7bb362cf492f95fe259)
- [meta] use `npmignore` to autogenerate an npmignore file [`b5d7d3a`](https://github.com/ljharb/json-stable-stringify/commit/b5d7d3abbe3d3a653e9ed511ab1b48940c5eb126)
- [Dev Deps] update `tape` [`2200cf1`](https://github.com/ljharb/json-stable-stringify/commit/2200cf1e5822a4dd928541c3122a0922703c951f)
- [actions] update rebase action [`e41ac00`](https://github.com/ljharb/json-stable-stringify/commit/e41ac000fb633d3df7c1e417ffd6213d885b64a1)
- [meta] update URLs [`f17e490`](https://github.com/ljharb/json-stable-stringify/commit/f17e49038cf39a84a8a2677cc6445fad54902766)
- Only apps should have lockfiles [`4f052f4`](https://github.com/ljharb/json-stable-stringify/commit/4f052f4ebf722024bc3827064b2d823f405ff2f6)
- add breaking test (acyclic detected as cyclic) [`7f5f443`](https://github.com/ljharb/json-stable-stringify/commit/7f5f443e90402a520f1413833318b02bbb11ad67)
- [meta] add `safe-publish-latest` [`ddb843f`](https://github.com/ljharb/json-stable-stringify/commit/ddb843f678bfe5145afaf03d811701c5ce4a17a6)
- [Tests] add `aud` in `posttest` [`245c9bf`](https://github.com/ljharb/json-stable-stringify/commit/245c9bfa291d6d33813d44941d7639494fa8579a)
- [Deps] update `jsonify` [`7b79a68`](https://github.com/ljharb/json-stable-stringify/commit/7b79a686f1ccda88b3ab20549840764c9b6f74eb)
- fix conflict [`e43ca2a`](https://github.com/ljharb/json-stable-stringify/commit/e43ca2a1dcfc39bf1514684492767ef6040d1f3e)

## [v1.0.1](https://github.com/ljharb/json-stable-stringify/compare/v1.0.0...v1.0.1) - 2016-02-02

### Commits

- Correctly stringify non-cyclic shared references [`c26c700`](https://github.com/ljharb/json-stable-stringify/commit/c26c700f0b1d078512d2eba0eb16d6e5110a5538)

## [v1.0.0](https://github.com/ljharb/json-stable-stringify/compare/v0.1.3...v1.0.0) - 2014-05-27

### Commits

- Added options.replacer for custom object serialization [`ccf5e63`](https://github.com/ljharb/json-stable-stringify/commit/ccf5e636803a55d062e97aaf4e2c27d5c787aff0)
- document replacer [`894f43b`](https://github.com/ljharb/json-stable-stringify/commit/894f43b633724bf0c6c2741143addfe20e149015)

## [v0.1.3](https://github.com/ljharb/json-stable-stringify/compare/v0.1.2...v0.1.3) - 2014-05-27

### Commits

- Enable toJSON function to return different types [`de0debf`](https://github.com/ljharb/json-stable-stringify/commit/de0debff3a36604010279af1868c6172674f9cc9)

## [v0.1.2](https://github.com/ljharb/json-stable-stringify/compare/v0.1.1...v0.1.2) - 2014-04-02

### Commits

- Should call 'toJSON' if it is defined on the object being stringified. [`c1de9d1`](https://github.com/ljharb/json-stable-stringify/commit/c1de9d193e8d6755d6ea2c2e5ead0544a8122040)
- guard the reference [`a723f70`](https://github.com/ljharb/json-stable-stringify/commit/a723f705dd13fcbab1aa0ffa51849395712aaa13)
- reindent [`7ff314f`](https://github.com/ljharb/json-stable-stringify/commit/7ff314fabf3b40074a4aff906b16e087897c6040)

## [v0.1.1](https://github.com/ljharb/json-stable-stringify/compare/v0.1.0...v0.1.1) - 2013-12-21

### Commits

- fixed merge conflicts [`7e139e8`](https://github.com/ljharb/json-stable-stringify/commit/7e139e8bbeb37b4dfd44991f4d6c98bba446b949)
- fix formatting [`b5df6b9`](https://github.com/ljharb/json-stable-stringify/commit/b5df6b9ec0f5a5826eebb5d93424923041e43405)

## [v0.1.0](https://github.com/ljharb/json-stable-stringify/compare/v0.0.1...v0.1.0) - 2013-12-21

### Commits

- New “space” option to enable pretty printing (same as ES5) [`e6815c9`](https://github.com/ljharb/json-stable-stringify/commit/e6815c9dd8ca4052023d2bbd5c5b78b44f0efef0)
- formatting [`962edf4`](https://github.com/ljharb/json-stable-stringify/commit/962edf4abb96189546b4f78f8719d747fd90fd43)

## [v0.0.1](https://github.com/ljharb/json-stable-stringify/compare/v0.0.0...v0.0.1) - 2013-07-17

### Commits

- don't choke on null [`3f4e9c7`](https://github.com/ljharb/json-stable-stringify/commit/3f4e9c78befc32f7d36af68e408e25cdc84be202)

## v0.0.0 - 2013-07-17

### Commits

- docs, more examples [`81f36c1`](https://github.com/ljharb/json-stable-stringify/commit/81f36c1aa645a75ebefa6d66d9cf41660439ebfe)
- package.json etc [`98c5fd6`](https://github.com/ljharb/json-stable-stringify/commit/98c5fd6f9b12e1679b90777b9f6384203a05e983)
- working implementation with 2 examples [`3e5363a`](https://github.com/ljharb/json-stable-stringify/commit/3e5363ac542fa3bf0bdef51034ca9201648f9839)
- turn examples into tests, everything passes [`cccbd24`](https://github.com/ljharb/json-stable-stringify/commit/cccbd24c1a1a6318e3c004c86ae032db98a9abf8)
- badges [`f8ff127`](https://github.com/ljharb/json-stable-stringify/commit/f8ff127df9f05d0b238bae8f91e483a755e0069e)
- comparison test now passes [`8ab93e2`](https://github.com/ljharb/json-stable-stringify/commit/8ab93e2273ec530990e28233fcb96fde548ab16c)
- failing custom comparison test [`3af627d`](https://github.com/ljharb/json-stable-stringify/commit/3af627d0d367451a98fc9cec6580760ade8f9bae)
- fix object.keys shim [`7c16662`](https://github.com/ljharb/json-stable-stringify/commit/7c16662bc1cc6ecfa64159f9277e067cb1bec505)
- fix for the other tests that don't use a cmp function [`f7b9a47`](https://github.com/ljharb/json-stable-stringify/commit/f7b9a476fd3ce9ec09b2c0588605e6c7c053e9ed)
