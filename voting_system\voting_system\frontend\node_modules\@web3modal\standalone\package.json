{"name": "@web3modal/standalone", "version": "2.4.3", "main": "dist/index.es.js", "type": "module", "types": "dist/_types/index.d.ts", "files": ["dist"], "sideEffects": false, "scripts": {"build:clean": "rm -rf dist", "build:types": "tsc --emitDeclarationOnly", "build:source": "rollup --silent --config rollup.config.js", "build": "npm run build:clean; npm run build:types & npm run build:source", "dev": "rollup --config rollup.config.js --watch", "typecheck": "tsc --noEmit"}, "dependencies": {"@web3modal/core": "2.4.3", "@web3modal/ui": "2.4.3"}, "keywords": ["web3", "crypto", "ethereum", "web3modal", "walletconnect"], "author": "WalletConnect <walletconnect.com>", "license": "Apache-2.0", "homepage": "https://github.com/web3modal/web3modal", "repository": {"type": "git", "url": "git+https://github.com/web3modal/web3modal.git"}, "bugs": {"url": "https://github.com/web3modal/web3modal/issues"}}