import{_$LH as e,noChange as t}from"./lit-html.js";
/**
 * @license
 * Copyright 2019 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const r={boundAttributeSuffix:e.O,marker:e.P,markerMatch:e.A,HTML_RESULT:e.C,getTemplateHtml:e.M,overrideDirectiveResolve:(e,t)=>class extends e{_$AS(e,r){return t(this,r)}},setDirectiveClass(e,t){e._$litDirective$=t},getAttributePartCommittedValue:(e,r,a)=>{let i=t;return e.j=e=>i=e,e._$AI(r,e,a),i},connectedDisconnectable:e=>({...e,_$AU:!0}),resolveDirective:e.D,AttributePart:e.V,PropertyPart:e.U,BooleanAttributePart:e.H,EventPart:e.N,ElementPart:e.F,TemplateInstance:e.L,isIterable:e.R,ChildPart:e.I};export{r as _$LH};
//# sourceMappingURL=private-ssr-support.js.map
