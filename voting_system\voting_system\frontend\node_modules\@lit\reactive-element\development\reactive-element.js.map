{"version": 3, "file": "reactive-element.js", "sourceRoot": "", "sources": ["../src/reactive-element.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;;AAEH;;;;GAIG;AAEH,OAAO,EACL,kBAAkB,EAClB,WAAW,GAGZ,MAAM,cAAc,CAAC;AAMtB,6DAA6D;AAC7D,sEAAsE;AAEtE,cAAc,cAAc,CAAC;AAM7B,MAAM,SAAS,GAAG,KAAK,CAAC;AACxB,MAAM,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC;AAE/C,IAAI,SAAS,EAAE;IACb,MAAA,MAAM,CAAC,cAAc,oCAArB,MAAM,CAAC,cAAc,GAAK,cAAc,EAAC;CAC1C;AAED,MAAM,QAAQ,GAAG,IAAI,CAAC;AAEtB,IAAI,qBAKH,CAAC;AAEF,IAAI,YAAqD,CAAC;AAE1D,MAAM,YAAY,GAAI,MAAwD;KAC3E,YAAY,CAAC;AAEhB,oDAAoD;AACpD,oEAAoE;AACpE,8EAA8E;AAC9E,6BAA6B;AAC7B,MAAM,8BAA8B,GAAG,YAAY;IACjD,CAAC,CAAE,YAAY,CAAC,WAA6B;IAC7C,CAAC,CAAC,EAAE,CAAC;AAEP,MAAM,eAAe,GAAG,QAAQ;IAC9B,CAAC,CAAC,MAAM,CAAC,qCAAqC;IAC9C,CAAC,CAAC,MAAM,CAAC,8BAA8B,CAAC;AAE1C,IAAI,QAAQ,EAAE;IACZ,uEAAuE;IACvE,cAAc;IACd,MAAM,cAAc,GAA4B,OAAC,MAAM,CAAC,iBAAiB,oCAAxB,MAAM,CAAC,iBAAiB,GACvE,IAAI,GAAG,EAAE,EAAC,CAAC;IAEb,0CAA0C;IAC1C,YAAY,GAAG,CAAC,IAAY,EAAE,OAAe,EAAE,EAAE;QAC/C,OAAO,IAAI,4BAA4B,IAAI,wBAAwB,CAAC;QACpE,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;YAChC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACtB,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;SAC7B;IACH,CAAC,CAAC;IAEF,YAAY,CACV,UAAU,EACV,qDAAqD,CACtD,CAAC;IAEF,kCAAkC;IAClC,IAAI,CAAA,MAAA,MAAM,CAAC,QAAQ,0CAAE,KAAK,KAAI,eAAe,KAAK,SAAS,EAAE;QAC3D,YAAY,CACV,0BAA0B,EAC1B,sDAAsD;YACpD,sDAAsD,CACzD,CAAC;KACH;IAED,qBAAqB,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QACjC,IAAI,EAAE,CACJ,WAAsC,EACtC,WAAwB,EACxB,EAAE;YACF,YAAY,CACV,wBAAwB,EACxB,qEAAqE;gBACnE,gBAAgB,IAAI,qCAAqC,CAC5D,CAAC;YACF,IAAI,WAAW,KAAK,SAAS,EAAE;gBAC7B,WAAW,CAAC,KAAK,CAAC,CAAC;aACpB;QACH,CAAC;KACF,CAAC,CAAC;CACJ;AAmCD;;;;GAIG;AACH,MAAM,aAAa,GAAG,QAAQ;IAC5B,CAAC,CAAC,CAAC,KAAsC,EAAE,EAAE;QACzC,MAAM,UAAU,GAAI,MAAwC;aACzD,qBAAqB,CAAC;QACzB,IAAI,CAAC,UAAU,EAAE;YACf,OAAO;SACR;QACD,MAAM,CAAC,aAAa,CAClB,IAAI,WAAW,CAAkC,WAAW,EAAE;YAC5D,MAAM,EAAE,KAAK;SACd,CAAC,CACH,CAAC;IACJ,CAAC;IACH,CAAC,CAAC,SAAS,CAAC;AAEd;;;;;GAKG;AACH,eAAe;AACf,MAAM,yBAAyB,GAAG,CAChC,IAAO,EACP,IAAa,EACV,EAAE,CAAC,IAAI,CAAC;AA+Ib,MAAM,CAAC,MAAM,gBAAgB,GAA8B;IACzD,WAAW,CAAC,KAAc,EAAE,IAAc;QACxC,QAAQ,IAAI,EAAE;YACZ,KAAK,OAAO;gBACV,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,8BAA8B,CAAC,CAAC,CAAC,IAAI,CAAC;gBACtD,MAAM;YACR,KAAK,MAAM,CAAC;YACZ,KAAK,KAAK;gBACR,0DAA0D;gBAC1D,wCAAwC;gBACxC,KAAK,GAAG,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACtD,MAAM;SACT;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,aAAa,CAAC,KAAoB,EAAE,IAAc;QAChD,IAAI,SAAS,GAAY,KAAK,CAAC;QAC/B,QAAQ,IAAI,EAAE;YACZ,KAAK,OAAO;gBACV,SAAS,GAAG,KAAK,KAAK,IAAI,CAAC;gBAC3B,MAAM;YACR,KAAK,MAAM;gBACT,SAAS,GAAG,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAClD,MAAM;YACR,KAAK,MAAM,CAAC;YACZ,KAAK,KAAK;gBACR,mEAAmE;gBACnE,mDAAmD;gBACnD,sDAAsD;gBACtD,IAAI;oBACF,kEAAkE;oBAClE,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,KAAM,CAAY,CAAC;iBAC3C;gBAAC,OAAO,CAAC,EAAE;oBACV,SAAS,GAAG,IAAI,CAAC;iBAClB;gBACD,MAAM;SACT;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;CACF,CAAC;AAMF;;;GAGG;AACH,MAAM,CAAC,MAAM,QAAQ,GAAe,CAAC,KAAc,EAAE,GAAY,EAAW,EAAE;IAC5E,2DAA2D;IAC3D,OAAO,GAAG,KAAK,KAAK,IAAI,CAAC,GAAG,KAAK,GAAG,IAAI,KAAK,KAAK,KAAK,CAAC,CAAC;AAC3D,CAAC,CAAC;AAEF,MAAM,0BAA0B,GAAwB;IACtD,SAAS,EAAE,IAAI;IACf,IAAI,EAAE,MAAM;IACZ,SAAS,EAAE,gBAAgB;IAC3B,OAAO,EAAE,KAAK;IACd,UAAU,EAAE,QAAQ;CACrB,CAAC;AAEF;;;;;GAKG;AACH,MAAM,SAAS,GAAG,WAAW,CAAC;AAS9B;;;;;GAKG;AACH,MAAM,OAAgB,eAAe;AACnC,oEAAoE;AACpE,6CAA6C;AAC7C,EAAE;AACF,4EAA4E;AAC5E,4EAA4E;AAC5E,2EAA2E;AAC3E,sEAAsE;AACtE,iEAAiE;AACjE,SAAQ,WAAW;IAsgBnB;QACE,KAAK,EAAE,CAAC;QA3CF,yBAAoB,GAAoB,IAAI,GAAG,EAAE,CAAC;QAK1D;;;;WAIG;QACH,oBAAe,GAAG,KAAK,CAAC;QAExB;;;;WAIG;QACH,eAAU,GAAG,KAAK,CAAC;QAenB;;WAEG;QACK,yBAAoB,GAAuB,IAAI,CAAC;QAStD,IAAI,CAAC,YAAY,EAAE,CAAC;IACtB,CAAC;IArdD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAgCG;IACH,MAAM,CAAC,cAAc,CAAC,WAAwB;;QAC5C,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,OAAC,IAAI,CAAC,aAAa,oCAAlB,IAAI,CAAC,aAAa,GAAK,EAAE,EAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAChD,CAAC;IAqGD;;;;OAIG;IACH,MAAM,KAAK,kBAAkB;QAC3B,yDAAyD;QACzD,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,MAAM,UAAU,GAAa,EAAE,CAAC;QAChC,2EAA2E;QAC3E,mBAAmB;QACnB,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YACtC,MAAM,IAAI,GAAG,IAAI,CAAC,0BAA0B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACnD,IAAI,IAAI,KAAK,SAAS,EAAE;gBACtB,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;gBAC3C,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACvB;QACH,CAAC,CAAC,CAAC;QACH,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACH,MAAM,CAAC,cAAc,CACnB,IAAiB,EACjB,UAA+B,0BAA0B;;QAEzD,6DAA6D;QAC7D,IAAI,OAAO,CAAC,KAAK,EAAE;YACjB,sCAAsC;YACtC,8DAA8D;YAC7D,OAAe,CAAC,SAAS,GAAG,KAAK,CAAC;SACpC;QACD,oEAAoE;QACpE,2EAA2E;QAC3E,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC1C,sEAAsE;QACtE,2EAA2E;QAC3E,mEAAmE;QACnE,yEAAyE;QACzE,qBAAqB;QACrB,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YAC/D,MAAM,GAAG,GAAG,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YAC9D,MAAM,UAAU,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;YAClE,IAAI,UAAU,KAAK,SAAS,EAAE;gBAC5B,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;gBACxD,IAAI,QAAQ,EAAE;oBACZ,oEAAoE;oBACpE,sEAAsE;oBACtE,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,wBAAwB,CAAC,EAAE;wBAClD,IAAI,CAAC,sBAAsB,GAAG,IAAI,GAAG,CACnC,MAAA,IAAI,CAAC,sBAAsB,mCAAI,EAAE,CAClC,CAAC;qBACH;oBACD,IAAI,CAAC,sBAAuB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;iBACxC;aACF;SACF;IACH,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;OA0BG;IACO,MAAM,CAAC,qBAAqB,CACpC,IAAiB,EACjB,GAAoB,EACpB,OAA4B;QAE5B,OAAO;YACL,8DAA8D;YAC9D,GAAG;gBACD,OAAQ,IAAiC,CAAC,GAAa,CAAC,CAAC;YAC3D,CAAC;YACD,GAAG,CAAwB,KAAc;gBACvC,MAAM,QAAQ,GAAI,IAAuC,CACvD,IAAc,CACf,CAAC;gBACD,IAAuC,CAAC,GAAa,CAAC,GAAG,KAAK,CAAC;gBAC/D,IAAmC,CAAC,aAAa,CAChD,IAAI,EACJ,QAAQ,EACR,OAAO,CACR,CAAC;YACJ,CAAC;YACD,YAAY,EAAE,IAAI;YAClB,UAAU,EAAE,IAAI;SACjB,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,MAAM,CAAC,kBAAkB,CAAC,IAAiB;QACzC,OAAO,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,0BAA0B,CAAC;IACxE,CAAC;IAED;;;;;OAKG;IACO,MAAM,CAAC,QAAQ;QACvB,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE;YAClC,OAAO,KAAK,CAAC;SACd;QACD,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;QACvB,4BAA4B;QAC5B,MAAM,SAAS,GAAG,MAAM,CAAC,cAAc,CAAC,IAAI,CAA2B,CAAC;QACxE,SAAS,CAAC,QAAQ,EAAE,CAAC;QACrB,oEAAoE;QACpE,qEAAqE;QACrE,uCAAuC;QACvC,IAAI,SAAS,CAAC,aAAa,KAAK,SAAS,EAAE;YACzC,IAAI,CAAC,aAAa,GAAG,CAAC,GAAG,SAAS,CAAC,aAAa,CAAC,CAAC;SACnD;QACD,IAAI,CAAC,iBAAiB,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;QAC9D,iDAAiD;QACjD,IAAI,CAAC,wBAAwB,GAAG,IAAI,GAAG,EAAE,CAAC;QAC1C,sBAAsB;QACtB,sEAAsE;QACtE,qEAAqE;QACrE,2CAA2C;QAC3C,IAAI,IAAI,CAAC,cAAc,CAAC,yBAAyB,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,EAAE;YACtE,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;YAC9B,6DAA6D;YAC7D,MAAM,QAAQ,GAAG;gBACf,GAAG,MAAM,CAAC,mBAAmB,CAAC,KAAK,CAAC;gBACpC,GAAG,MAAM,CAAC,qBAAqB,CAAC,KAAK,CAAC;aACvC,CAAC;YACF,iDAAiD;YACjD,KAAK,MAAM,CAAC,IAAI,QAAQ,EAAE;gBACxB,wEAAwE;gBACxE,cAAc;gBACd,8DAA8D;gBAC9D,IAAI,CAAC,cAAc,CAAC,CAAC,EAAG,KAAa,CAAC,CAAC,CAAC,CAAC,CAAC;aAC3C;SACF;QACD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACtD,oBAAoB;QACpB,IAAI,QAAQ,EAAE;YACZ,MAAM,oBAAoB,GAAG,CAAC,IAAY,EAAE,OAAO,GAAG,KAAK,EAAE,EAAE;gBAC7D,IAAI,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;oBACvC,YAAY,CACV,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,EACvC,KAAK,IAAI,8BAA8B,IAAI,CAAC,IAAI,OAAO;wBACrD,YAAY,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,GAAG;wBAC9C,gCAAgC,CACnC,CAAC;iBACH;YACH,CAAC,CAAC;YACF,oBAAoB,CAAC,YAAY,CAAC,CAAC;YACnC,oBAAoB,CAAC,uBAAuB,CAAC,CAAC;YAC9C,oBAAoB,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;SAClD;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAcD;;;;;;;;;;;;;OAaG;IACO,MAAM,CAAC,cAAc,CAC7B,MAAuB;QAEvB,MAAM,aAAa,GAAG,EAAE,CAAC;QACzB,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACzB,0EAA0E;YAC1E,uDAAuD;YACvD,gEAAgE;YAChE,MAAM,GAAG,GAAG,IAAI,GAAG,CAAE,MAAyB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;YACzE,yEAAyE;YACzE,KAAK,MAAM,CAAC,IAAI,GAAG,EAAE;gBACnB,aAAa,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAsB,CAAC,CAAC,CAAC;aACnE;SACF;aAAM,IAAI,MAAM,KAAK,SAAS,EAAE;YAC/B,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC;SAChD;QACD,OAAO,aAAa,CAAC;IACvB,CAAC;IASD;;;OAGG;IACK,MAAM,CAAC,0BAA0B,CACvC,IAAiB,EACjB,OAA4B;QAE5B,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QACpC,OAAO,SAAS,KAAK,KAAK;YACxB,CAAC,CAAC,SAAS;YACX,CAAC,CAAC,OAAO,SAAS,KAAK,QAAQ;gBAC/B,CAAC,CAAC,SAAS;gBACX,CAAC,CAAC,OAAO,IAAI,KAAK,QAAQ;oBAC1B,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE;oBACpB,CAAC,CAAC,SAAS,CAAC;IAChB,CAAC;IAiDD;;;OAGG;IACK,YAAY;;QAClB,IAAI,CAAC,eAAe,GAAG,IAAI,OAAO,CAChC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,cAAc,GAAG,GAAG,CAAC,CACrC,CAAC;QACF,IAAI,CAAC,mBAAmB,GAAG,IAAI,GAAG,EAAE,CAAC;QACrC,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAChC,4DAA4D;QAC5D,mBAAmB;QACnB,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,MAAC,IAAI,CAAC,WAAsC,CAAC,aAAa,0CAAE,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CACxE,CAAC,CAAC,IAAI,CAAC,CACR,CAAC;IACJ,CAAC;IAED;;;;;;;;OAQG;IACH,aAAa,CAAC,UAA8B;;QAC1C,OAAC,IAAI,CAAC,aAAa,oCAAlB,IAAI,CAAC,aAAa,GAAK,EAAE,EAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC7C,iEAAiE;QACjE,oEAAoE;QACpE,mEAAmE;QACnE,yBAAyB;QACzB,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE;YACrD,MAAA,UAAU,CAAC,aAAa,0DAAI,CAAC;SAC9B;IACH,CAAC;IAED;;;OAGG;IACH,gBAAgB,CAAC,UAA8B;;QAC7C,yEAAyE;QACzE,qBAAqB;QACrB,MAAA,IAAI,CAAC,aAAa,0CAAE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9E,CAAC;IAED;;;;;;;;;;;OAWG;IACK,wBAAwB;QAC9B,2EAA2E;QAC3E,mBAAmB;QAClB,IAAI,CAAC,WAAsC,CAAC,iBAAiB,CAAC,OAAO,CACpE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE;YACR,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE;gBAC1B,IAAI,CAAC,oBAAqB,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAe,CAAC,CAAC,CAAC;gBACzD,OAAO,IAAI,CAAC,CAAe,CAAC,CAAC;aAC9B;QACH,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;OAQG;IACO,gBAAgB;;QACxB,MAAM,UAAU,GACd,MAAA,IAAI,CAAC,UAAU,mCACf,IAAI,CAAC,YAAY,CACd,IAAI,CAAC,WAAsC,CAAC,iBAAiB,CAC/D,CAAC;QACJ,WAAW,CACT,UAAU,EACT,IAAI,CAAC,WAAsC,CAAC,aAAa,CAC3D,CAAC;QACF,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;;;OAIG;IACH,iBAAiB;;QACf,yCAAyC;QACzC,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,EAAE;YAE/B,IAGD,CAAC,UAAU,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;SACxC;QACD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAC1B,MAAA,IAAI,CAAC,aAAa,0CAAE,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,WAAC,OAAA,MAAA,CAAC,CAAC,aAAa,iDAAI,CAAA,EAAA,CAAC,CAAC;IAC1D,CAAC;IAED;;;;;OAKG;IACO,cAAc,CAAC,gBAAyB,IAAG,CAAC;IAEtD;;;;;OAKG;IACH,oBAAoB;;QAClB,MAAA,IAAI,CAAC,aAAa,0CAAE,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,WAAC,OAAA,MAAA,CAAC,CAAC,gBAAgB,iDAAI,CAAA,EAAA,CAAC,CAAC;IAC7D,CAAC;IAED;;;;;;;;;;;OAWG;IACH,wBAAwB,CACtB,IAAY,EACZ,IAAmB,EACnB,KAAoB;QAEpB,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC1C,CAAC;IAEO,qBAAqB,CAC3B,IAAiB,EACjB,KAAc,EACd,UAA+B,0BAA0B;;QAEzD,MAAM,IAAI,GACR,IAAI,CAAC,WACN,CAAC,0BAA0B,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC5C,IAAI,IAAI,KAAK,SAAS,IAAI,OAAO,CAAC,OAAO,KAAK,IAAI,EAAE;YAClD,MAAM,SAAS,GACb,CAAA,MAAC,OAAO,CAAC,SAAuC,0CAAE,WAAW;gBAC7D,SAAS;gBACP,CAAC,CAAE,OAAO,CAAC,SAAuC;gBAClD,CAAC,CAAC,gBAAgB,CAAC;YACvB,MAAM,SAAS,GAAG,SAAS,CAAC,WAAY,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;YAC9D,IACE,QAAQ;gBACP,IAAI,CAAC,WAAsC,CAAC,eAAgB,CAAC,OAAO,CACnE,WAAW,CACZ,IAAI,CAAC;gBACN,SAAS,KAAK,SAAS,EACvB;gBACA,YAAY,CACV,2BAA2B,EAC3B,+BAA+B,IAAc,eAAe;oBAC1D,wBAAwB,IAAI,CAAC,SAAS,0BAA0B;oBAChE,+DAA+D;oBAC/D,uCAAuC,CAC1C,CAAC;aACH;YACD,oDAAoD;YACpD,mEAAmE;YACnE,wEAAwE;YACxE,wEAAwE;YACxE,iEAAiE;YACjE,qEAAqE;YACrE,+BAA+B;YAC/B,wBAAwB;YACxB,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;YACjC,IAAI,SAAS,IAAI,IAAI,EAAE;gBACrB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;aAC5B;iBAAM;gBACL,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,SAAmB,CAAC,CAAC;aAC9C;YACD,4BAA4B;YAC5B,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;SAClC;IACH,CAAC;IAED,gBAAgB;IAChB,qBAAqB,CAAC,IAAY,EAAE,KAAoB;;QACtD,MAAM,IAAI,GAAG,IAAI,CAAC,WAAqC,CAAC;QACxD,sEAAsE;QACtE,8DAA8D;QAC9D,MAAM,QAAQ,GAAI,IAAI,CAAC,wBAAyC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC3E,yEAAyE;QACzE,oDAAoD;QACpD,IAAI,QAAQ,KAAK,SAAS,IAAI,IAAI,CAAC,oBAAoB,KAAK,QAAQ,EAAE;YACpE,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;YAClD,MAAM,SAAS,GACb,OAAO,OAAO,CAAC,SAAS,KAAK,UAAU;gBACrC,CAAC,CAAC,EAAC,aAAa,EAAE,OAAO,CAAC,SAAS,EAAC;gBACpC,CAAC,CAAC,CAAA,MAAA,OAAO,CAAC,SAAS,0CAAE,aAAa,MAAK,SAAS;oBAChD,CAAC,CAAC,OAAO,CAAC,SAAS;oBACnB,CAAC,CAAC,gBAAgB,CAAC;YACvB,wBAAwB;YACxB,IAAI,CAAC,oBAAoB,GAAG,QAAQ,CAAC;YACrC,IAAI,CAAC,QAAsB,CAAC,GAAG,SAAS,CAAC,aAAc,CACrD,KAAK,EACL,OAAO,CAAC,IAAI;YACZ,8DAA8D;aACxD,CAAC;YACT,4BAA4B;YAC5B,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;SAClC;IACH,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,aAAa,CACX,IAAkB,EAClB,QAAkB,EAClB,OAA6B;QAE7B,IAAI,mBAAmB,GAAG,IAAI,CAAC;QAC/B,4DAA4D;QAC5D,IAAI,IAAI,KAAK,SAAS,EAAE;YACtB,OAAO;gBACL,OAAO;oBACN,IAAI,CAAC,WAAsC,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YACxE,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,QAAQ,CAAC;YAClD,IAAI,UAAU,CAAC,IAAI,CAAC,IAAkB,CAAC,EAAE,QAAQ,CAAC,EAAE;gBAClD,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;oBACvC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;iBAC9C;gBACD,oCAAoC;gBACpC,iEAAiE;gBACjE,4DAA4D;gBAC5D,2CAA2C;gBAC3C,IAAI,OAAO,CAAC,OAAO,KAAK,IAAI,IAAI,IAAI,CAAC,oBAAoB,KAAK,IAAI,EAAE;oBAClE,IAAI,IAAI,CAAC,sBAAsB,KAAK,SAAS,EAAE;wBAC7C,IAAI,CAAC,sBAAsB,GAAG,IAAI,GAAG,EAAE,CAAC;qBACzC;oBACD,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;iBAChD;aACF;iBAAM;gBACL,sEAAsE;gBACtE,mBAAmB,GAAG,KAAK,CAAC;aAC7B;SACF;QACD,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,mBAAmB,EAAE;YAChD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;SAC/C;QACD,wEAAwE;QACxE,uCAAuC;QACvC,OAAO,QAAQ;YACb,CAAC,CAAE,qBAAqB,CAAC,IAAI,CAAC,SAAS,CAAqB;YAC5D,CAAC,CAAC,SAAS,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe;QAC3B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,IAAI;YACF,2DAA2D;YAC3D,+DAA+D;YAC/D,MAAM,IAAI,CAAC,eAAe,CAAC;SAC5B;QAAC,OAAO,CAAC,EAAE;YACV,qEAAqE;YACrE,mEAAmE;YACnE,6CAA6C;YAC7C,iCAAiC;YACjC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;SACnB;QACD,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QACrC,sEAAsE;QACtE,oEAAoE;QACpE,uEAAuE;QACvE,IAAI,MAAM,IAAI,IAAI,EAAE;YAClB,MAAM,MAAM,CAAC;SACd;QACD,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC;IAC/B,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACO,cAAc;QACtB,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC;IAC9B,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACO,aAAa;;QACrB,8DAA8D;QAC9D,gEAAgE;QAChE,cAAc;QACd,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACzB,OAAO;SACR;QACD,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAG,EAAC,IAAI,EAAE,QAAQ,EAAC,CAAC,CAAC;QAClC,yCAAyC;QACzC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YACpB,uEAAuE;YACvE,IAAI,QAAQ,EAAE;gBACZ,MAAM,kBAAkB,GAAa,EAAE,CAAC;gBACxC,MACE,IAAI,CAAC,WACN,CAAC,sBAAsB,0CAAE,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;;oBACtC,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA,MAAA,IAAI,CAAC,oBAAoB,0CAAE,GAAG,CAAC,CAAC,CAAC,CAAA,EAAE;wBAChE,kBAAkB,CAAC,IAAI,CAAC,CAAW,CAAC,CAAC;qBACtC;gBACH,CAAC,CAAC,CAAC;gBACH,IAAI,kBAAkB,CAAC,MAAM,EAAE;oBAC7B,MAAM,IAAI,KAAK,CACb,uCAAuC,IAAI,CAAC,SAAS,YAAY;wBAC/D,+DAA+D;wBAC/D,WAAW,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;wBAC5C,8DAA8D;wBAC9D,4CAA4C;wBAC5C,4CAA4C;wBAC5C,uBAAuB,CAC1B,CAAC;iBACH;aACF;SACF;QACD,iDAAiD;QACjD,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC7B,2EAA2E;YAC3E,mBAAmB;YACnB,8DAA8D;YAC9D,IAAI,CAAC,oBAAqB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAE,IAAY,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACrE,IAAI,CAAC,oBAAoB,GAAG,SAAS,CAAC;SACvC;QACD,IAAI,YAAY,GAAG,KAAK,CAAC;QACzB,MAAM,iBAAiB,GAAG,IAAI,CAAC,mBAAmB,CAAC;QACnD,IAAI;YACF,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC;YACpD,IAAI,YAAY,EAAE;gBAChB,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;gBACnC,MAAA,IAAI,CAAC,aAAa,0CAAE,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,WAAC,OAAA,MAAA,CAAC,CAAC,UAAU,iDAAI,CAAA,EAAA,CAAC,CAAC;gBACrD,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;aAChC;iBAAM;gBACL,IAAI,CAAC,aAAa,EAAE,CAAC;aACtB;SACF;QAAC,OAAO,CAAC,EAAE;YACV,oEAAoE;YACpE,oBAAoB;YACpB,YAAY,GAAG,KAAK,CAAC;YACrB,mEAAmE;YACnE,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,MAAM,CAAC,CAAC;SACT;QACD,kFAAkF;QAClF,IAAI,YAAY,EAAE;YAChB,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC;SACrC;IACH,CAAC;IAED;;;;;;;;;;;;;;;;;;;;OAoBG;IACO,UAAU,CAAC,kBAAkC,IAAS,CAAC;IAEjE,wDAAwD;IACxD,YAAY;IACZ,WAAW,CAAC,iBAAiC;;QAC3C,MAAA,IAAI,CAAC,aAAa,0CAAE,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,WAAC,OAAA,MAAA,CAAC,CAAC,WAAW,iDAAI,CAAA,EAAA,CAAC,CAAC;QACtD,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YACpB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC;SACtC;QACD,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;QAChC,IACE,QAAQ;YACR,IAAI,CAAC,eAAe;YACnB,IAAI,CAAC,WAAsC,CAAC,eAAgB,CAAC,OAAO,CACnE,kBAAkB,CACnB,IAAI,CAAC,EACN;YACA,YAAY,CACV,kBAAkB,EAClB,WAAW,IAAI,CAAC,SAAS,uBAAuB;gBAC9C,yCAAyC;gBACzC,mEAAmE;gBACnE,mEAAmE;gBACnE,gEAAgE,CACnE,CAAC;SACH;IACH,CAAC;IAEO,aAAa;QACnB,IAAI,CAAC,mBAAmB,GAAG,IAAI,GAAG,EAAE,CAAC;QACrC,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;IAC/B,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACH,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAClC,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACO,iBAAiB;QACzB,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED;;;;;;;OAOG;IACO,YAAY,CAAC,kBAAkC;QACvD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;;;OAQG;IACO,MAAM,CAAC,kBAAkC;QACjD,IAAI,IAAI,CAAC,sBAAsB,KAAK,SAAS,EAAE;YAC7C,qEAAqE;YACrE,yBAAyB;YACzB,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAC3C,IAAI,CAAC,qBAAqB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAe,CAAC,EAAE,CAAC,CAAC,CACxD,CAAC;YACF,IAAI,CAAC,sBAAsB,GAAG,SAAS,CAAC;SACzC;QACD,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC;IAED;;;;;;;;;OASG;IACO,OAAO,CAAC,kBAAkC,IAAG,CAAC;IAExD;;;;;;;;;;;;;;;OAeG;IACO,YAAY,CAAC,kBAAkC,IAAG,CAAC;;KA99B3C,SAAS;AAH3B;;GAEG;AACc,mBAAW,GAAG,IAAI,CAAC;AAEpC;;;;;GAKG;AACI,iCAAiB,GAA2B,IAAI,GAAG,EAAE,CAAC;AA6B7D;;;;;GAKG;AACI,6BAAa,GAA6B,EAAE,CAAC;AA8PpD;;;;;;;;;GASG;AACI,iCAAiB,GAAmB,EAAC,IAAI,EAAE,MAAM,EAAC,CAAC;AA8qB5D,+BAA+B;AAC/B,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAG,EAAC,eAAe,EAAC,CAAC,CAAC;AAErC,uBAAuB;AACvB,IAAI,QAAQ,EAAE;IACZ,uBAAuB;IACvB,eAAe,CAAC,eAAe,GAAG,CAAC,kBAAkB,CAAC,CAAC;IACvD,MAAM,iBAAiB,GAAG,UAAU,IAA4B;QAC9D,IACE,CAAC,IAAI,CAAC,cAAc,CAAC,yBAAyB,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC,EACxE;YACA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAgB,CAAC,KAAK,EAAE,CAAC;SACtD;IACH,CAAC,CAAC;IACF,eAAe,CAAC,aAAa,GAAG,UAE9B,OAAoB;QAEpB,iBAAiB,CAAC,IAAI,CAAC,CAAC;QACxB,IAAI,IAAI,CAAC,eAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YAC9C,IAAI,CAAC,eAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SACrC;IACH,CAAC,CAAC;IACF,eAAe,CAAC,cAAc,GAAG,UAE/B,OAAoB;QAEpB,iBAAiB,CAAC,IAAI,CAAC,CAAC;QACxB,MAAM,CAAC,GAAG,IAAI,CAAC,eAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QACjD,IAAI,CAAC,IAAI,CAAC,EAAE;YACV,IAAI,CAAC,eAAgB,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SACpC;IACH,CAAC,CAAC;CACH;AAED,2EAA2E;AAC3E,yEAAyE;AACzE,OAAC,MAAM,CAAC,uBAAuB,oCAA9B,MAAM,CAAC,uBAAuB,GAAK,EAAE,EAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACtD,IAAI,QAAQ,IAAI,MAAM,CAAC,uBAAuB,CAAC,MAAM,GAAG,CAAC,EAAE;IACzD,YAAa,CACX,mBAAmB,EACnB,6DAA6D;QAC3D,qBAAqB,CACxB,CAAC;CACH", "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/**\n * Use this module if you want to create your own base class extending\n * {@link ReactiveElement}.\n * @packageDocumentation\n */\n\nimport {\n  getCompatibleStyle,\n  adoptStyles,\n  CSSResultGroup,\n  CSSResultOrNative,\n} from './css-tag.js';\nimport type {\n  ReactiveController,\n  ReactiveControllerHost,\n} from './reactive-controller.js';\n\n// In the Node build, this import will be injected by Rollup:\n// import {HTMLElement, customElements} from '@lit-labs/ssr-dom-shim';\n\nexport * from './css-tag.js';\nexport type {\n  ReactiveController,\n  ReactiveControllerHost,\n} from './reactive-controller.js';\n\nconst NODE_MODE = false;\nconst global = NODE_MODE ? globalThis : window;\n\nif (NODE_MODE) {\n  global.customElements ??= customElements;\n}\n\nconst DEV_MODE = true;\n\nlet requestUpdateThenable: (name: string) => {\n  then: (\n    onfulfilled?: (value: boolean) => void,\n    _onrejected?: () => void\n  ) => void;\n};\n\nlet issueWarning: (code: string, warning: string) => void;\n\nconst trustedTypes = (global as unknown as {trustedTypes?: {emptyScript: ''}})\n  .trustedTypes;\n\n// Temporary workaround for https://crbug.com/993268\n// Currently, any attribute starting with \"on\" is considered to be a\n// TrustedScript source. Such boolean attributes must be set to the equivalent\n// trusted emptyScript value.\nconst emptyStringForBooleanAttribute = trustedTypes\n  ? (trustedTypes.emptyScript as unknown as '')\n  : '';\n\nconst polyfillSupport = DEV_MODE\n  ? global.reactiveElementPolyfillSupportDevMode\n  : global.reactiveElementPolyfillSupport;\n\nif (DEV_MODE) {\n  // Ensure warnings are issued only 1x, even if multiple versions of Lit\n  // are loaded.\n  const issuedWarnings: Set<string | undefined> = (global.litIssuedWarnings ??=\n    new Set());\n\n  // Issue a warning, if we haven't already.\n  issueWarning = (code: string, warning: string) => {\n    warning += ` See https://lit.dev/msg/${code} for more information.`;\n    if (!issuedWarnings.has(warning)) {\n      console.warn(warning);\n      issuedWarnings.add(warning);\n    }\n  };\n\n  issueWarning(\n    'dev-mode',\n    `Lit is in dev mode. Not recommended for production!`\n  );\n\n  // Issue polyfill support warning.\n  if (global.ShadyDOM?.inUse && polyfillSupport === undefined) {\n    issueWarning(\n      'polyfill-support-missing',\n      `Shadow DOM is being polyfilled via \\`ShadyDOM\\` but ` +\n        `the \\`polyfill-support\\` module has not been loaded.`\n    );\n  }\n\n  requestUpdateThenable = (name) => ({\n    then: (\n      onfulfilled?: (value: boolean) => void,\n      _onrejected?: () => void\n    ) => {\n      issueWarning(\n        'request-update-promise',\n        `The \\`requestUpdate\\` method should no longer return a Promise but ` +\n          `does so on \\`${name}\\`. Use \\`updateComplete\\` instead.`\n      );\n      if (onfulfilled !== undefined) {\n        onfulfilled(false);\n      }\n    },\n  });\n}\n\n/**\n * Contains types that are part of the unstable debug API.\n *\n * Everything in this API is not stable and may change or be removed in the future,\n * even on patch releases.\n */\n// eslint-disable-next-line @typescript-eslint/no-namespace\nexport namespace ReactiveUnstable {\n  /**\n   * When Lit is running in dev mode and `window.emitLitDebugLogEvents` is true,\n   * we will emit 'lit-debug' events to window, with live details about the update and render\n   * lifecycle. These can be useful for writing debug tooling and visualizations.\n   *\n   * Please be aware that running with window.emitLitDebugLogEvents has performance overhead,\n   * making certain operations that are normally very cheap (like a no-op render) much slower,\n   * because we must copy data and dispatch events.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-namespace\n  export namespace DebugLog {\n    export type Entry = Update;\n    export interface Update {\n      kind: 'update';\n    }\n  }\n}\n\ninterface DebugLoggingWindow {\n  // Even in dev mode, we generally don't want to emit these events, as that's\n  // another level of cost, so only emit them when DEV_MODE is true _and_ when\n  // window.emitLitDebugEvents is true.\n  emitLitDebugLogEvents?: boolean;\n}\n\n/**\n * Useful for visualizing and logging insights into what the Lit template system is doing.\n *\n * Compiled out of prod mode builds.\n */\nconst debugLogEvent = DEV_MODE\n  ? (event: ReactiveUnstable.DebugLog.Entry) => {\n      const shouldEmit = (global as unknown as DebugLoggingWindow)\n        .emitLitDebugLogEvents;\n      if (!shouldEmit) {\n        return;\n      }\n      global.dispatchEvent(\n        new CustomEvent<ReactiveUnstable.DebugLog.Entry>('lit-debug', {\n          detail: event,\n        })\n      );\n    }\n  : undefined;\n\n/*\n * When using Closure Compiler, JSCompiler_renameProperty(property, object) is\n * replaced at compile time by the munged name for object[property]. We cannot\n * alias this function, so we have to use a small shim that has the same\n * behavior when not compiling.\n */\n/*@__INLINE__*/\nconst JSCompiler_renameProperty = <P extends PropertyKey>(\n  prop: P,\n  _obj: unknown\n): P => prop;\n\n/**\n * Converts property values to and from attribute values.\n */\nexport interface ComplexAttributeConverter<Type = unknown, TypeHint = unknown> {\n  /**\n   * Called to convert an attribute value to a property\n   * value.\n   */\n  fromAttribute?(value: string | null, type?: TypeHint): Type;\n\n  /**\n   * Called to convert a property value to an attribute\n   * value.\n   *\n   * It returns unknown instead of string, to be compatible with\n   * https://github.com/WICG/trusted-types (and similar efforts).\n   */\n  toAttribute?(value: Type, type?: TypeHint): unknown;\n}\n\ntype AttributeConverter<Type = unknown, TypeHint = unknown> =\n  | ComplexAttributeConverter<Type>\n  | ((value: string | null, type?: TypeHint) => Type);\n\n/**\n * Defines options for a property accessor.\n */\nexport interface PropertyDeclaration<Type = unknown, TypeHint = unknown> {\n  /**\n   * When set to `true`, indicates the property is internal private state. The\n   * property should not be set by users. When using TypeScript, this property\n   * should be marked as `private` or `protected`, and it is also a common\n   * practice to use a leading `_` in the name. The property is not added to\n   * `observedAttributes`.\n   */\n  readonly state?: boolean;\n\n  /**\n   * Indicates how and whether the property becomes an observed attribute.\n   * If the value is `false`, the property is not added to `observedAttributes`.\n   * If true or absent, the lowercased property name is observed (e.g. `fooBar`\n   * becomes `foobar`). If a string, the string value is observed (e.g\n   * `attribute: 'foo-bar'`).\n   */\n  readonly attribute?: boolean | string;\n\n  /**\n   * Indicates the type of the property. This is used only as a hint for the\n   * `converter` to determine how to convert the attribute\n   * to/from a property.\n   */\n  readonly type?: TypeHint;\n\n  /**\n   * Indicates how to convert the attribute to/from a property. If this value\n   * is a function, it is used to convert the attribute value a the property\n   * value. If it's an object, it can have keys for `fromAttribute` and\n   * `toAttribute`. If no `toAttribute` function is provided and\n   * `reflect` is set to `true`, the property value is set directly to the\n   * attribute. A default `converter` is used if none is provided; it supports\n   * `Boolean`, `String`, `Number`, `Object`, and `Array`. Note,\n   * when a property changes and the converter is used to update the attribute,\n   * the property is never updated again as a result of the attribute changing,\n   * and vice versa.\n   */\n  readonly converter?: AttributeConverter<Type, TypeHint>;\n\n  /**\n   * Indicates if the property should reflect to an attribute.\n   * If `true`, when the property is set, the attribute is set using the\n   * attribute name determined according to the rules for the `attribute`\n   * property option and the value of the property converted using the rules\n   * from the `converter` property option.\n   */\n  readonly reflect?: boolean;\n\n  /**\n   * A function that indicates if a property should be considered changed when\n   * it is set. The function should take the `newValue` and `oldValue` and\n   * return `true` if an update should be requested.\n   */\n  hasChanged?(value: Type, oldValue: Type): boolean;\n\n  /**\n   * Indicates whether an accessor will be created for this property. By\n   * default, an accessor will be generated for this property that requests an\n   * update when set. If this flag is `true`, no accessor will be created, and\n   * it will be the user's responsibility to call\n   * `this.requestUpdate(propertyName, oldValue)` to request an update when\n   * the property changes.\n   */\n  readonly noAccessor?: boolean;\n}\n\n/**\n * Map of properties to PropertyDeclaration options. For each property an\n * accessor is made, and the property is processed according to the\n * PropertyDeclaration options.\n */\nexport interface PropertyDeclarations {\n  readonly [key: string]: PropertyDeclaration;\n}\n\ntype PropertyDeclarationMap = Map<PropertyKey, PropertyDeclaration>;\n\ntype AttributeMap = Map<string, PropertyKey>;\n\n/**\n * A Map of property keys to values.\n *\n * Takes an optional type parameter T, which when specified as a non-any,\n * non-unknown type, will make the Map more strongly-typed, associating the map\n * keys with their corresponding value type on T.\n *\n * Use `PropertyValues<this>` when overriding ReactiveElement.update() and\n * other lifecycle methods in order to get stronger type-checking on keys\n * and values.\n */\n// This type is conditional so that if the parameter T is not specified, or\n// is `any`, the type will include `Map<PropertyKey, unknown>`. Since T is not\n// given in the uses of PropertyValues in this file, all uses here fallback to\n// meaning `Map<PropertyKey, unknown>`, but if a developer uses\n// `PropertyValues<this>` (or any other value for T) they will get a\n// strongly-typed Map type.\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport type PropertyValues<T = any> = T extends object\n  ? PropertyValueMap<T>\n  : Map<PropertyKey, unknown>;\n\n/**\n * Do not use, instead prefer {@linkcode PropertyValues}.\n */\n// This type must be exported such that JavaScript generated by the Google\n// Closure Compiler can import a type reference.\nexport interface PropertyValueMap<T> extends Map<PropertyKey, unknown> {\n  get<K extends keyof T>(k: K): T[K];\n  set<K extends keyof T>(key: K, value: T[K]): this;\n  has<K extends keyof T>(k: K): boolean;\n  delete<K extends keyof T>(k: K): boolean;\n}\n\nexport const defaultConverter: ComplexAttributeConverter = {\n  toAttribute(value: unknown, type?: unknown): unknown {\n    switch (type) {\n      case Boolean:\n        value = value ? emptyStringForBooleanAttribute : null;\n        break;\n      case Object:\n      case Array:\n        // if the value is `null` or `undefined` pass this through\n        // to allow removing/no change behavior.\n        value = value == null ? value : JSON.stringify(value);\n        break;\n    }\n    return value;\n  },\n\n  fromAttribute(value: string | null, type?: unknown) {\n    let fromValue: unknown = value;\n    switch (type) {\n      case Boolean:\n        fromValue = value !== null;\n        break;\n      case Number:\n        fromValue = value === null ? null : Number(value);\n        break;\n      case Object:\n      case Array:\n        // Do *not* generate exception when invalid JSON is set as elements\n        // don't normally complain on being mis-configured.\n        // TODO(sorvell): Do generate exception in *dev mode*.\n        try {\n          // Assert to adhere to Bazel's \"must type assert JSON parse\" rule.\n          fromValue = JSON.parse(value!) as unknown;\n        } catch (e) {\n          fromValue = null;\n        }\n        break;\n    }\n    return fromValue;\n  },\n};\n\nexport interface HasChanged {\n  (value: unknown, old: unknown): boolean;\n}\n\n/**\n * Change function that returns true if `value` is different from `oldValue`.\n * This method is used as the default for a property's `hasChanged` function.\n */\nexport const notEqual: HasChanged = (value: unknown, old: unknown): boolean => {\n  // This ensures (old==NaN, value==NaN) always returns false\n  return old !== value && (old === old || value === value);\n};\n\nconst defaultPropertyDeclaration: PropertyDeclaration = {\n  attribute: true,\n  type: String,\n  converter: defaultConverter,\n  reflect: false,\n  hasChanged: notEqual,\n};\n\n/**\n * The Closure JS Compiler doesn't currently have good support for static\n * property semantics where \"this\" is dynamic (e.g.\n * https://github.com/google/closure-compiler/issues/3177 and others) so we use\n * this hack to bypass any rewriting by the compiler.\n */\nconst finalized = 'finalized';\n\n/**\n * A string representing one of the supported dev mode warning categories.\n */\nexport type WarningKind = 'change-in-update' | 'migration';\n\nexport type Initializer = (element: ReactiveElement) => void;\n\n/**\n * Base element class which manages element properties and attributes. When\n * properties change, the `update` method is asynchronously called. This method\n * should be supplied by subclassers to render updates as desired.\n * @noInheritDoc\n */\nexport abstract class ReactiveElement\n  // In the Node build, this `extends` clause will be substituted with\n  // `(globalThis.HTMLElement ?? HTMLElement)`.\n  //\n  // This way, we will first prefer any global `HTMLElement` polyfill that the\n  // user has assigned, and then fall back to the `HTMLElement` shim which has\n  // been imported (see note at the top of this file about how this import is\n  // generated by Rollup). Note that the `HTMLElement` variable has been\n  // shadowed by this import, so it no longer refers to the global.\n  extends HTMLElement\n  implements ReactiveControllerHost\n{\n  // Note: these are patched in only in DEV_MODE.\n  /**\n   * Read or set all the enabled warning categories for this class.\n   *\n   * This property is only used in development builds.\n   *\n   * @nocollapse\n   * @category dev-mode\n   */\n  static enabledWarnings?: WarningKind[];\n\n  /**\n   * Enable the given warning category for this class.\n   *\n   * This method only exists in development builds, so it should be accessed\n   * with a guard like:\n   *\n   * ```ts\n   * // Enable for all ReactiveElement subclasses\n   * ReactiveElement.enableWarning?.('migration');\n   *\n   * // Enable for only MyElement and subclasses\n   * MyElement.enableWarning?.('migration');\n   * ```\n   *\n   * @nocollapse\n   * @category dev-mode\n   */\n  static enableWarning?: (warningKind: WarningKind) => void;\n\n  /**\n   * Disable the given warning category for this class.\n   *\n   * This method only exists in development builds, so it should be accessed\n   * with a guard like:\n   *\n   * ```ts\n   * // Disable for all ReactiveElement subclasses\n   * ReactiveElement.disableWarning?.('migration');\n   *\n   * // Disable for only MyElement and subclasses\n   * MyElement.disableWarning?.('migration');\n   * ```\n   *\n   * @nocollapse\n   * @category dev-mode\n   */\n  static disableWarning?: (warningKind: WarningKind) => void;\n\n  /**\n   * Adds an initializer function to the class that is called during instance\n   * construction.\n   *\n   * This is useful for code that runs against a `ReactiveElement`\n   * subclass, such as a decorator, that needs to do work for each\n   * instance, such as setting up a `ReactiveController`.\n   *\n   * ```ts\n   * const myDecorator = (target: typeof ReactiveElement, key: string) => {\n   *   target.addInitializer((instance: ReactiveElement) => {\n   *     // This is run during construction of the element\n   *     new MyController(instance);\n   *   });\n   * }\n   * ```\n   *\n   * Decorating a field will then cause each instance to run an initializer\n   * that adds a controller:\n   *\n   * ```ts\n   * class MyElement extends LitElement {\n   *   @myDecorator foo;\n   * }\n   * ```\n   *\n   * Initializers are stored per-constructor. Adding an initializer to a\n   * subclass does not add it to a superclass. Since initializers are run in\n   * constructors, initializers will run in order of the class hierarchy,\n   * starting with superclasses and progressing to the instance's class.\n   *\n   * @nocollapse\n   */\n  static addInitializer(initializer: Initializer) {\n    this.finalize();\n    (this._initializers ??= []).push(initializer);\n  }\n\n  static _initializers?: Initializer[];\n\n  /*\n   * Due to closure compiler ES6 compilation bugs, @nocollapse is required on\n   * all static methods and properties with initializers.  Reference:\n   * - https://github.com/google/closure-compiler/issues/1776\n   */\n\n  /**\n   * Maps attribute names to properties; for example `foobar` attribute to\n   * `fooBar` property. Created lazily on user subclasses when finalizing the\n   * class.\n   * @nocollapse\n   */\n  private static __attributeToPropertyMap: AttributeMap;\n\n  /**\n   * Marks class as having finished creating properties.\n   */\n  protected static [finalized] = true;\n\n  /**\n   * Memoized list of all element properties, including any superclass properties.\n   * Created lazily on user subclasses when finalizing the class.\n   * @nocollapse\n   * @category properties\n   */\n  static elementProperties: PropertyDeclarationMap = new Map();\n\n  /**\n   * User-supplied object that maps property names to `PropertyDeclaration`\n   * objects containing options for configuring reactive properties. When\n   * a reactive property is set the element will update and render.\n   *\n   * By default properties are public fields, and as such, they should be\n   * considered as primarily settable by element users, either via attribute or\n   * the property itself.\n   *\n   * Generally, properties that are changed by the element should be private or\n   * protected fields and should use the `state: true` option. Properties\n   * marked as `state` do not reflect from the corresponding attribute\n   *\n   * However, sometimes element code does need to set a public property. This\n   * should typically only be done in response to user interaction, and an event\n   * should be fired informing the user; for example, a checkbox sets its\n   * `checked` property when clicked and fires a `changed` event. Mutating\n   * public properties should typically not be done for non-primitive (object or\n   * array) properties. In other cases when an element needs to manage state, a\n   * private property set with the `state: true` option should be used. When\n   * needed, state properties can be initialized via public properties to\n   * facilitate complex interactions.\n   * @nocollapse\n   * @category properties\n   */\n  static properties: PropertyDeclarations;\n\n  /**\n   * Memoized list of all element styles.\n   * Created lazily on user subclasses when finalizing the class.\n   * @nocollapse\n   * @category styles\n   */\n  static elementStyles: Array<CSSResultOrNative> = [];\n\n  /**\n   * Array of styles to apply to the element. The styles should be defined\n   * using the {@linkcode css} tag function, via constructible stylesheets, or\n   * imported from native CSS module scripts.\n   *\n   * Note on Content Security Policy:\n   *\n   * Element styles are implemented with `<style>` tags when the browser doesn't\n   * support adopted StyleSheets. To use such `<style>` tags with the style-src\n   * CSP directive, the style-src value must either include 'unsafe-inline' or\n   * `nonce-<base64-value>` with `<base64-value>` replaced be a server-generated\n   * nonce.\n   *\n   * To provide a nonce to use on generated `<style>` elements, set\n   * `window.litNonce` to a server-generated nonce in your page's HTML, before\n   * loading application code:\n   *\n   * ```html\n   * <script>\n   *   // Generated and unique per request:\n   *   window.litNonce = 'a1b2c3d4';\n   * </script>\n   * ```\n   * @nocollapse\n   * @category styles\n   */\n  static styles?: CSSResultGroup;\n\n  /**\n   * The set of properties defined by this class that caused an accessor to be\n   * added during `createProperty`.\n   * @nocollapse\n   */\n  private static __reactivePropertyKeys?: Set<PropertyKey>;\n\n  /**\n   * Returns a list of attributes corresponding to the registered properties.\n   * @nocollapse\n   * @category attributes\n   */\n  static get observedAttributes() {\n    // note: piggy backing on this to ensure we're finalized.\n    this.finalize();\n    const attributes: string[] = [];\n    // Use forEach so this works even if for/of loops are compiled to for loops\n    // expecting arrays\n    this.elementProperties.forEach((v, p) => {\n      const attr = this.__attributeNameForProperty(p, v);\n      if (attr !== undefined) {\n        this.__attributeToPropertyMap.set(attr, p);\n        attributes.push(attr);\n      }\n    });\n    return attributes;\n  }\n\n  /**\n   * Creates a property accessor on the element prototype if one does not exist\n   * and stores a {@linkcode PropertyDeclaration} for the property with the\n   * given options. The property setter calls the property's `hasChanged`\n   * property option or uses a strict identity check to determine whether or not\n   * to request an update.\n   *\n   * This method may be overridden to customize properties; however,\n   * when doing so, it's important to call `super.createProperty` to ensure\n   * the property is setup correctly. This method calls\n   * `getPropertyDescriptor` internally to get a descriptor to install.\n   * To customize what properties do when they are get or set, override\n   * `getPropertyDescriptor`. To customize the options for a property,\n   * implement `createProperty` like this:\n   *\n   * ```ts\n   * static createProperty(name, options) {\n   *   options = Object.assign(options, {myOption: true});\n   *   super.createProperty(name, options);\n   * }\n   * ```\n   *\n   * @nocollapse\n   * @category properties\n   */\n  static createProperty(\n    name: PropertyKey,\n    options: PropertyDeclaration = defaultPropertyDeclaration\n  ) {\n    // if this is a state property, force the attribute to false.\n    if (options.state) {\n      // Cast as any since this is readonly.\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      (options as any).attribute = false;\n    }\n    // Note, since this can be called by the `@property` decorator which\n    // is called before `finalize`, we ensure finalization has been kicked off.\n    this.finalize();\n    this.elementProperties.set(name, options);\n    // Do not generate an accessor if the prototype already has one, since\n    // it would be lost otherwise and that would never be the user's intention;\n    // Instead, we expect users to call `requestUpdate` themselves from\n    // user-defined accessors. Note that if the super has an accessor we will\n    // still overwrite it\n    if (!options.noAccessor && !this.prototype.hasOwnProperty(name)) {\n      const key = typeof name === 'symbol' ? Symbol() : `__${name}`;\n      const descriptor = this.getPropertyDescriptor(name, key, options);\n      if (descriptor !== undefined) {\n        Object.defineProperty(this.prototype, name, descriptor);\n        if (DEV_MODE) {\n          // If this class doesn't have its own set, create one and initialize\n          // with the values in the set from the nearest ancestor class, if any.\n          if (!this.hasOwnProperty('__reactivePropertyKeys')) {\n            this.__reactivePropertyKeys = new Set(\n              this.__reactivePropertyKeys ?? []\n            );\n          }\n          this.__reactivePropertyKeys!.add(name);\n        }\n      }\n    }\n  }\n\n  /**\n   * Returns a property descriptor to be defined on the given named property.\n   * If no descriptor is returned, the property will not become an accessor.\n   * For example,\n   *\n   * ```ts\n   * class MyElement extends LitElement {\n   *   static getPropertyDescriptor(name, key, options) {\n   *     const defaultDescriptor =\n   *         super.getPropertyDescriptor(name, key, options);\n   *     const setter = defaultDescriptor.set;\n   *     return {\n   *       get: defaultDescriptor.get,\n   *       set(value) {\n   *         setter.call(this, value);\n   *         // custom action.\n   *       },\n   *       configurable: true,\n   *       enumerable: true\n   *     }\n   *   }\n   * }\n   * ```\n   *\n   * @nocollapse\n   * @category properties\n   */\n  protected static getPropertyDescriptor(\n    name: PropertyKey,\n    key: string | symbol,\n    options: PropertyDeclaration\n  ): PropertyDescriptor | undefined {\n    return {\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      get(): any {\n        return (this as {[key: string]: unknown})[key as string];\n      },\n      set(this: ReactiveElement, value: unknown) {\n        const oldValue = (this as {} as {[key: string]: unknown})[\n          name as string\n        ];\n        (this as {} as {[key: string]: unknown})[key as string] = value;\n        (this as unknown as ReactiveElement).requestUpdate(\n          name,\n          oldValue,\n          options\n        );\n      },\n      configurable: true,\n      enumerable: true,\n    };\n  }\n\n  /**\n   * Returns the property options associated with the given property.\n   * These options are defined with a `PropertyDeclaration` via the `properties`\n   * object or the `@property` decorator and are registered in\n   * `createProperty(...)`.\n   *\n   * Note, this method should be considered \"final\" and not overridden. To\n   * customize the options for a given property, override\n   * {@linkcode createProperty}.\n   *\n   * @nocollapse\n   * @final\n   * @category properties\n   */\n  static getPropertyOptions(name: PropertyKey) {\n    return this.elementProperties.get(name) || defaultPropertyDeclaration;\n  }\n\n  /**\n   * Creates property accessors for registered properties, sets up element\n   * styling, and ensures any superclasses are also finalized. Returns true if\n   * the element was finalized.\n   * @nocollapse\n   */\n  protected static finalize() {\n    if (this.hasOwnProperty(finalized)) {\n      return false;\n    }\n    this[finalized] = true;\n    // finalize any superclasses\n    const superCtor = Object.getPrototypeOf(this) as typeof ReactiveElement;\n    superCtor.finalize();\n    // Create own set of initializers for this class if any exist on the\n    // superclass and copy them down. Note, for a small perf boost, avoid\n    // creating initializers unless needed.\n    if (superCtor._initializers !== undefined) {\n      this._initializers = [...superCtor._initializers];\n    }\n    this.elementProperties = new Map(superCtor.elementProperties);\n    // initialize Map populated in observedAttributes\n    this.__attributeToPropertyMap = new Map();\n    // make any properties\n    // Note, only process \"own\" properties since this element will inherit\n    // any properties defined on the superClass, and finalization ensures\n    // the entire prototype chain is finalized.\n    if (this.hasOwnProperty(JSCompiler_renameProperty('properties', this))) {\n      const props = this.properties;\n      // support symbols in properties (IE11 does not support this)\n      const propKeys = [\n        ...Object.getOwnPropertyNames(props),\n        ...Object.getOwnPropertySymbols(props),\n      ];\n      // This for/of is ok because propKeys is an array\n      for (const p of propKeys) {\n        // note, use of `any` is due to TypeScript lack of support for symbol in\n        // index types\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        this.createProperty(p, (props as any)[p]);\n      }\n    }\n    this.elementStyles = this.finalizeStyles(this.styles);\n    // DEV mode warnings\n    if (DEV_MODE) {\n      const warnRemovedOrRenamed = (name: string, renamed = false) => {\n        if (this.prototype.hasOwnProperty(name)) {\n          issueWarning(\n            renamed ? 'renamed-api' : 'removed-api',\n            `\\`${name}\\` is implemented on class ${this.name}. It ` +\n              `has been ${renamed ? 'renamed' : 'removed'} ` +\n              `in this version of LitElement.`\n          );\n        }\n      };\n      warnRemovedOrRenamed('initialize');\n      warnRemovedOrRenamed('requestUpdateInternal');\n      warnRemovedOrRenamed('_getUpdateComplete', true);\n    }\n    return true;\n  }\n\n  /**\n   * Options used when calling `attachShadow`. Set this property to customize\n   * the options for the shadowRoot; for example, to create a closed\n   * shadowRoot: `{mode: 'closed'}`.\n   *\n   * Note, these options are used in `createRenderRoot`. If this method\n   * is customized, options should be respected if possible.\n   * @nocollapse\n   * @category rendering\n   */\n  static shadowRootOptions: ShadowRootInit = {mode: 'open'};\n\n  /**\n   * Takes the styles the user supplied via the `static styles` property and\n   * returns the array of styles to apply to the element.\n   * Override this method to integrate into a style management system.\n   *\n   * Styles are deduplicated preserving the _last_ instance in the list. This\n   * is a performance optimization to avoid duplicated styles that can occur\n   * especially when composing via subclassing. The last item is kept to try\n   * to preserve the cascade order with the assumption that it's most important\n   * that last added styles override previous styles.\n   *\n   * @nocollapse\n   * @category styles\n   */\n  protected static finalizeStyles(\n    styles?: CSSResultGroup\n  ): Array<CSSResultOrNative> {\n    const elementStyles = [];\n    if (Array.isArray(styles)) {\n      // Dedupe the flattened array in reverse order to preserve the last items.\n      // Casting to Array<unknown> works around TS error that\n      // appears to come from trying to flatten a type CSSResultArray.\n      const set = new Set((styles as Array<unknown>).flat(Infinity).reverse());\n      // Then preserve original order by adding the set items in reverse order.\n      for (const s of set) {\n        elementStyles.unshift(getCompatibleStyle(s as CSSResultOrNative));\n      }\n    } else if (styles !== undefined) {\n      elementStyles.push(getCompatibleStyle(styles));\n    }\n    return elementStyles;\n  }\n\n  /**\n   * Node or ShadowRoot into which element DOM should be rendered. Defaults\n   * to an open shadowRoot.\n   * @category rendering\n   */\n  readonly renderRoot!: HTMLElement | ShadowRoot;\n\n  /**\n   * Returns the property name for the given attribute `name`.\n   * @nocollapse\n   */\n  private static __attributeNameForProperty(\n    name: PropertyKey,\n    options: PropertyDeclaration\n  ) {\n    const attribute = options.attribute;\n    return attribute === false\n      ? undefined\n      : typeof attribute === 'string'\n      ? attribute\n      : typeof name === 'string'\n      ? name.toLowerCase()\n      : undefined;\n  }\n\n  private __instanceProperties?: PropertyValues = new Map();\n  // Initialize to an unresolved Promise so we can make sure the element has\n  // connected before first update.\n  private __updatePromise!: Promise<boolean>;\n\n  /**\n   * True if there is a pending update as a result of calling `requestUpdate()`.\n   * Should only be read.\n   * @category updates\n   */\n  isUpdatePending = false;\n\n  /**\n   * Is set to `true` after the first update. The element code cannot assume\n   * that `renderRoot` exists before the element `hasUpdated`.\n   * @category updates\n   */\n  hasUpdated = false;\n\n  /**\n   * Map with keys for any properties that have changed since the last\n   * update cycle with previous values.\n   *\n   * @internal\n   */\n  _$changedProperties!: PropertyValues;\n\n  /**\n   * Map with keys of properties that should be reflected when updated.\n   */\n  private __reflectingProperties?: Map<PropertyKey, PropertyDeclaration>;\n\n  /**\n   * Name of currently reflecting property\n   */\n  private __reflectingProperty: PropertyKey | null = null;\n\n  /**\n   * Set of controllers.\n   */\n  private __controllers?: ReactiveController[];\n\n  constructor() {\n    super();\n    this.__initialize();\n  }\n\n  /**\n   * Internal only override point for customizing work done when elements\n   * are constructed.\n   */\n  private __initialize() {\n    this.__updatePromise = new Promise<boolean>(\n      (res) => (this.enableUpdating = res)\n    );\n    this._$changedProperties = new Map();\n    this.__saveInstanceProperties();\n    // ensures first update will be caught by an early access of\n    // `updateComplete`\n    this.requestUpdate();\n    (this.constructor as typeof ReactiveElement)._initializers?.forEach((i) =>\n      i(this)\n    );\n  }\n\n  /**\n   * Registers a `ReactiveController` to participate in the element's reactive\n   * update cycle. The element automatically calls into any registered\n   * controllers during its lifecycle callbacks.\n   *\n   * If the element is connected when `addController()` is called, the\n   * controller's `hostConnected()` callback will be immediately called.\n   * @category controllers\n   */\n  addController(controller: ReactiveController) {\n    (this.__controllers ??= []).push(controller);\n    // If a controller is added after the element has been connected,\n    // call hostConnected. Note, re-using existence of `renderRoot` here\n    // (which is set in connectedCallback) to avoid the need to track a\n    // first connected state.\n    if (this.renderRoot !== undefined && this.isConnected) {\n      controller.hostConnected?.();\n    }\n  }\n\n  /**\n   * Removes a `ReactiveController` from the element.\n   * @category controllers\n   */\n  removeController(controller: ReactiveController) {\n    // Note, if the indexOf is -1, the >>> will flip the sign which makes the\n    // splice do nothing.\n    this.__controllers?.splice(this.__controllers.indexOf(controller) >>> 0, 1);\n  }\n\n  /**\n   * Fixes any properties set on the instance before upgrade time.\n   * Otherwise these would shadow the accessor and break these properties.\n   * The properties are stored in a Map which is played back after the\n   * constructor runs. Note, on very old versions of Safari (<=9) or Chrome\n   * (<=41), properties created for native platform properties like (`id` or\n   * `name`) may not have default values set in the element constructor. On\n   * these browsers native properties appear on instances and therefore their\n   * default value will overwrite any element default (e.g. if the element sets\n   * this.id = 'id' in the constructor, the 'id' will become '' since this is\n   * the native platform default).\n   */\n  private __saveInstanceProperties() {\n    // Use forEach so this works even if for/of loops are compiled to for loops\n    // expecting arrays\n    (this.constructor as typeof ReactiveElement).elementProperties.forEach(\n      (_v, p) => {\n        if (this.hasOwnProperty(p)) {\n          this.__instanceProperties!.set(p, this[p as keyof this]);\n          delete this[p as keyof this];\n        }\n      }\n    );\n  }\n\n  /**\n   * Returns the node into which the element should render and by default\n   * creates and returns an open shadowRoot. Implement to customize where the\n   * element's DOM is rendered. For example, to render into the element's\n   * childNodes, return `this`.\n   *\n   * @return Returns a node into which to render.\n   * @category rendering\n   */\n  protected createRenderRoot(): Element | ShadowRoot {\n    const renderRoot =\n      this.shadowRoot ??\n      this.attachShadow(\n        (this.constructor as typeof ReactiveElement).shadowRootOptions\n      );\n    adoptStyles(\n      renderRoot,\n      (this.constructor as typeof ReactiveElement).elementStyles\n    );\n    return renderRoot;\n  }\n\n  /**\n   * On first connection, creates the element's renderRoot, sets up\n   * element styling, and enables updating.\n   * @category lifecycle\n   */\n  connectedCallback() {\n    // create renderRoot before first update.\n    if (this.renderRoot === undefined) {\n      (\n        this as {\n          renderRoot: Element | DocumentFragment;\n        }\n      ).renderRoot = this.createRenderRoot();\n    }\n    this.enableUpdating(true);\n    this.__controllers?.forEach((c) => c.hostConnected?.());\n  }\n\n  /**\n   * Note, this method should be considered final and not overridden. It is\n   * overridden on the element instance with a function that triggers the first\n   * update.\n   * @category updates\n   */\n  protected enableUpdating(_requestedUpdate: boolean) {}\n\n  /**\n   * Allows for `super.disconnectedCallback()` in extensions while\n   * reserving the possibility of making non-breaking feature additions\n   * when disconnecting at some point in the future.\n   * @category lifecycle\n   */\n  disconnectedCallback() {\n    this.__controllers?.forEach((c) => c.hostDisconnected?.());\n  }\n\n  /**\n   * Synchronizes property values when attributes change.\n   *\n   * Specifically, when an attribute is set, the corresponding property is set.\n   * You should rarely need to implement this callback. If this method is\n   * overridden, `super.attributeChangedCallback(name, _old, value)` must be\n   * called.\n   *\n   * See [using the lifecycle callbacks](https://developer.mozilla.org/en-US/docs/Web/Web_Components/Using_custom_elements#using_the_lifecycle_callbacks)\n   * on MDN for more information about the `attributeChangedCallback`.\n   * @category attributes\n   */\n  attributeChangedCallback(\n    name: string,\n    _old: string | null,\n    value: string | null\n  ) {\n    this._$attributeToProperty(name, value);\n  }\n\n  private __propertyToAttribute(\n    name: PropertyKey,\n    value: unknown,\n    options: PropertyDeclaration = defaultPropertyDeclaration\n  ) {\n    const attr = (\n      this.constructor as typeof ReactiveElement\n    ).__attributeNameForProperty(name, options);\n    if (attr !== undefined && options.reflect === true) {\n      const converter =\n        (options.converter as ComplexAttributeConverter)?.toAttribute !==\n        undefined\n          ? (options.converter as ComplexAttributeConverter)\n          : defaultConverter;\n      const attrValue = converter.toAttribute!(value, options.type);\n      if (\n        DEV_MODE &&\n        (this.constructor as typeof ReactiveElement).enabledWarnings!.indexOf(\n          'migration'\n        ) >= 0 &&\n        attrValue === undefined\n      ) {\n        issueWarning(\n          'undefined-attribute-value',\n          `The attribute value for the ${name as string} property is ` +\n            `undefined on element ${this.localName}. The attribute will be ` +\n            `removed, but in the previous version of \\`ReactiveElement\\`, ` +\n            `the attribute would not have changed.`\n        );\n      }\n      // Track if the property is being reflected to avoid\n      // setting the property again via `attributeChangedCallback`. Note:\n      // 1. this takes advantage of the fact that the callback is synchronous.\n      // 2. will behave incorrectly if multiple attributes are in the reaction\n      // stack at time of calling. However, since we process attributes\n      // in `update` this should not be possible (or an extreme corner case\n      // that we'd like to discover).\n      // mark state reflecting\n      this.__reflectingProperty = name;\n      if (attrValue == null) {\n        this.removeAttribute(attr);\n      } else {\n        this.setAttribute(attr, attrValue as string);\n      }\n      // mark state not reflecting\n      this.__reflectingProperty = null;\n    }\n  }\n\n  /** @internal */\n  _$attributeToProperty(name: string, value: string | null) {\n    const ctor = this.constructor as typeof ReactiveElement;\n    // Note, hint this as an `AttributeMap` so closure clearly understands\n    // the type; it has issues with tracking types through statics\n    const propName = (ctor.__attributeToPropertyMap as AttributeMap).get(name);\n    // Use tracking info to avoid reflecting a property value to an attribute\n    // if it was just set because the attribute changed.\n    if (propName !== undefined && this.__reflectingProperty !== propName) {\n      const options = ctor.getPropertyOptions(propName);\n      const converter =\n        typeof options.converter === 'function'\n          ? {fromAttribute: options.converter}\n          : options.converter?.fromAttribute !== undefined\n          ? options.converter\n          : defaultConverter;\n      // mark state reflecting\n      this.__reflectingProperty = propName;\n      this[propName as keyof this] = converter.fromAttribute!(\n        value,\n        options.type\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      ) as any;\n      // mark state not reflecting\n      this.__reflectingProperty = null;\n    }\n  }\n\n  /**\n   * Requests an update which is processed asynchronously. This should be called\n   * when an element should update based on some state not triggered by setting\n   * a reactive property. In this case, pass no arguments. It should also be\n   * called when manually implementing a property setter. In this case, pass the\n   * property `name` and `oldValue` to ensure that any configured property\n   * options are honored.\n   *\n   * @param name name of requesting property\n   * @param oldValue old value of requesting property\n   * @param options property options to use instead of the previously\n   *     configured options\n   * @category updates\n   */\n  requestUpdate(\n    name?: PropertyKey,\n    oldValue?: unknown,\n    options?: PropertyDeclaration\n  ): void {\n    let shouldRequestUpdate = true;\n    // If we have a property key, perform property update steps.\n    if (name !== undefined) {\n      options =\n        options ||\n        (this.constructor as typeof ReactiveElement).getPropertyOptions(name);\n      const hasChanged = options.hasChanged || notEqual;\n      if (hasChanged(this[name as keyof this], oldValue)) {\n        if (!this._$changedProperties.has(name)) {\n          this._$changedProperties.set(name, oldValue);\n        }\n        // Add to reflecting properties set.\n        // Note, it's important that every change has a chance to add the\n        // property to `_reflectingProperties`. This ensures setting\n        // attribute + property reflects correctly.\n        if (options.reflect === true && this.__reflectingProperty !== name) {\n          if (this.__reflectingProperties === undefined) {\n            this.__reflectingProperties = new Map();\n          }\n          this.__reflectingProperties.set(name, options);\n        }\n      } else {\n        // Abort the request if the property should not be considered changed.\n        shouldRequestUpdate = false;\n      }\n    }\n    if (!this.isUpdatePending && shouldRequestUpdate) {\n      this.__updatePromise = this.__enqueueUpdate();\n    }\n    // Note, since this no longer returns a promise, in dev mode we return a\n    // thenable which warns if it's called.\n    return DEV_MODE\n      ? (requestUpdateThenable(this.localName) as unknown as void)\n      : undefined;\n  }\n\n  /**\n   * Sets up the element to asynchronously update.\n   */\n  private async __enqueueUpdate() {\n    this.isUpdatePending = true;\n    try {\n      // Ensure any previous update has resolved before updating.\n      // This `await` also ensures that property changes are batched.\n      await this.__updatePromise;\n    } catch (e) {\n      // Refire any previous errors async so they do not disrupt the update\n      // cycle. Errors are refired so developers have a chance to observe\n      // them, and this can be done by implementing\n      // `window.onunhandledrejection`.\n      Promise.reject(e);\n    }\n    const result = this.scheduleUpdate();\n    // If `scheduleUpdate` returns a Promise, we await it. This is done to\n    // enable coordinating updates with a scheduler. Note, the result is\n    // checked to avoid delaying an additional microtask unless we need to.\n    if (result != null) {\n      await result;\n    }\n    return !this.isUpdatePending;\n  }\n\n  /**\n   * Schedules an element update. You can override this method to change the\n   * timing of updates by returning a Promise. The update will await the\n   * returned Promise, and you should resolve the Promise to allow the update\n   * to proceed. If this method is overridden, `super.scheduleUpdate()`\n   * must be called.\n   *\n   * For instance, to schedule updates to occur just before the next frame:\n   *\n   * ```ts\n   * override protected async scheduleUpdate(): Promise<unknown> {\n   *   await new Promise((resolve) => requestAnimationFrame(() => resolve()));\n   *   super.scheduleUpdate();\n   * }\n   * ```\n   * @category updates\n   */\n  protected scheduleUpdate(): void | Promise<unknown> {\n    return this.performUpdate();\n  }\n\n  /**\n   * Performs an element update. Note, if an exception is thrown during the\n   * update, `firstUpdated` and `updated` will not be called.\n   *\n   * Call `performUpdate()` to immediately process a pending update. This should\n   * generally not be needed, but it can be done in rare cases when you need to\n   * update synchronously.\n   *\n   * Note: To ensure `performUpdate()` synchronously completes a pending update,\n   * it should not be overridden. In LitElement 2.x it was suggested to override\n   * `performUpdate()` to also customizing update scheduling. Instead, you should now\n   * override `scheduleUpdate()`. For backwards compatibility with LitElement 2.x,\n   * scheduling updates via `performUpdate()` continues to work, but will make\n   * also calling `performUpdate()` to synchronously process updates difficult.\n   *\n   * @category updates\n   */\n  protected performUpdate(): void | Promise<unknown> {\n    // Abort any update if one is not pending when this is called.\n    // This can happen if `performUpdate` is called early to \"flush\"\n    // the update.\n    if (!this.isUpdatePending) {\n      return;\n    }\n    debugLogEvent?.({kind: 'update'});\n    // create renderRoot before first update.\n    if (!this.hasUpdated) {\n      // Produce warning if any class properties are shadowed by class fields\n      if (DEV_MODE) {\n        const shadowedProperties: string[] = [];\n        (\n          this.constructor as typeof ReactiveElement\n        ).__reactivePropertyKeys?.forEach((p) => {\n          if (this.hasOwnProperty(p) && !this.__instanceProperties?.has(p)) {\n            shadowedProperties.push(p as string);\n          }\n        });\n        if (shadowedProperties.length) {\n          throw new Error(\n            `The following properties on element ${this.localName} will not ` +\n              `trigger updates as expected because they are set using class ` +\n              `fields: ${shadowedProperties.join(', ')}. ` +\n              `Native class fields and some compiled output will overwrite ` +\n              `accessors used for detecting changes. See ` +\n              `https://lit.dev/msg/class-field-shadowing ` +\n              `for more information.`\n          );\n        }\n      }\n    }\n    // Mixin instance properties once, if they exist.\n    if (this.__instanceProperties) {\n      // Use forEach so this works even if for/of loops are compiled to for loops\n      // expecting arrays\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      this.__instanceProperties!.forEach((v, p) => ((this as any)[p] = v));\n      this.__instanceProperties = undefined;\n    }\n    let shouldUpdate = false;\n    const changedProperties = this._$changedProperties;\n    try {\n      shouldUpdate = this.shouldUpdate(changedProperties);\n      if (shouldUpdate) {\n        this.willUpdate(changedProperties);\n        this.__controllers?.forEach((c) => c.hostUpdate?.());\n        this.update(changedProperties);\n      } else {\n        this.__markUpdated();\n      }\n    } catch (e) {\n      // Prevent `firstUpdated` and `updated` from running when there's an\n      // update exception.\n      shouldUpdate = false;\n      // Ensure element can accept additional updates after an exception.\n      this.__markUpdated();\n      throw e;\n    }\n    // The update is no longer considered pending and further updates are now allowed.\n    if (shouldUpdate) {\n      this._$didUpdate(changedProperties);\n    }\n  }\n\n  /**\n   * Invoked before `update()` to compute values needed during the update.\n   *\n   * Implement `willUpdate` to compute property values that depend on other\n   * properties and are used in the rest of the update process.\n   *\n   * ```ts\n   * willUpdate(changedProperties) {\n   *   // only need to check changed properties for an expensive computation.\n   *   if (changedProperties.has('firstName') || changedProperties.has('lastName')) {\n   *     this.sha = computeSHA(`${this.firstName} ${this.lastName}`);\n   *   }\n   * }\n   *\n   * render() {\n   *   return html`SHA: ${this.sha}`;\n   * }\n   * ```\n   *\n   * @category updates\n   */\n  protected willUpdate(_changedProperties: PropertyValues): void {}\n\n  // Note, this is an override point for polyfill-support.\n  // @internal\n  _$didUpdate(changedProperties: PropertyValues) {\n    this.__controllers?.forEach((c) => c.hostUpdated?.());\n    if (!this.hasUpdated) {\n      this.hasUpdated = true;\n      this.firstUpdated(changedProperties);\n    }\n    this.updated(changedProperties);\n    if (\n      DEV_MODE &&\n      this.isUpdatePending &&\n      (this.constructor as typeof ReactiveElement).enabledWarnings!.indexOf(\n        'change-in-update'\n      ) >= 0\n    ) {\n      issueWarning(\n        'change-in-update',\n        `Element ${this.localName} scheduled an update ` +\n          `(generally because a property was set) ` +\n          `after an update completed, causing a new update to be scheduled. ` +\n          `This is inefficient and should be avoided unless the next update ` +\n          `can only be scheduled as a side effect of the previous update.`\n      );\n    }\n  }\n\n  private __markUpdated() {\n    this._$changedProperties = new Map();\n    this.isUpdatePending = false;\n  }\n\n  /**\n   * Returns a Promise that resolves when the element has completed updating.\n   * The Promise value is a boolean that is `true` if the element completed the\n   * update without triggering another update. The Promise result is `false` if\n   * a property was set inside `updated()`. If the Promise is rejected, an\n   * exception was thrown during the update.\n   *\n   * To await additional asynchronous work, override the `getUpdateComplete`\n   * method. For example, it is sometimes useful to await a rendered element\n   * before fulfilling this Promise. To do this, first await\n   * `super.getUpdateComplete()`, then any subsequent state.\n   *\n   * @return A promise of a boolean that resolves to true if the update completed\n   *     without triggering another update.\n   * @category updates\n   */\n  get updateComplete(): Promise<boolean> {\n    return this.getUpdateComplete();\n  }\n\n  /**\n   * Override point for the `updateComplete` promise.\n   *\n   * It is not safe to override the `updateComplete` getter directly due to a\n   * limitation in TypeScript which means it is not possible to call a\n   * superclass getter (e.g. `super.updateComplete.then(...)`) when the target\n   * language is ES5 (https://github.com/microsoft/TypeScript/issues/338).\n   * This method should be overridden instead. For example:\n   *\n   * ```ts\n   * class MyElement extends LitElement {\n   *   override async getUpdateComplete() {\n   *     const result = await super.getUpdateComplete();\n   *     await this._myChild.updateComplete;\n   *     return result;\n   *   }\n   * }\n   * ```\n   *\n   * @return A promise of a boolean that resolves to true if the update completed\n   *     without triggering another update.\n   * @category updates\n   */\n  protected getUpdateComplete(): Promise<boolean> {\n    return this.__updatePromise;\n  }\n\n  /**\n   * Controls whether or not `update()` should be called when the element requests\n   * an update. By default, this method always returns `true`, but this can be\n   * customized to control when to update.\n   *\n   * @param _changedProperties Map of changed properties with old values\n   * @category updates\n   */\n  protected shouldUpdate(_changedProperties: PropertyValues): boolean {\n    return true;\n  }\n\n  /**\n   * Updates the element. This method reflects property values to attributes.\n   * It can be overridden to render and keep updated element DOM.\n   * Setting properties inside this method will *not* trigger\n   * another update.\n   *\n   * @param _changedProperties Map of changed properties with old values\n   * @category updates\n   */\n  protected update(_changedProperties: PropertyValues) {\n    if (this.__reflectingProperties !== undefined) {\n      // Use forEach so this works even if for/of loops are compiled to for\n      // loops expecting arrays\n      this.__reflectingProperties.forEach((v, k) =>\n        this.__propertyToAttribute(k, this[k as keyof this], v)\n      );\n      this.__reflectingProperties = undefined;\n    }\n    this.__markUpdated();\n  }\n\n  /**\n   * Invoked whenever the element is updated. Implement to perform\n   * post-updating tasks via DOM APIs, for example, focusing an element.\n   *\n   * Setting properties inside this method will trigger the element to update\n   * again after this update cycle completes.\n   *\n   * @param _changedProperties Map of changed properties with old values\n   * @category updates\n   */\n  protected updated(_changedProperties: PropertyValues) {}\n\n  /**\n   * Invoked when the element is first updated. Implement to perform one time\n   * work on the element after update.\n   *\n   * ```ts\n   * firstUpdated() {\n   *   this.renderRoot.getElementById('my-text-area').focus();\n   * }\n   * ```\n   *\n   * Setting properties inside this method will trigger the element to update\n   * again after this update cycle completes.\n   *\n   * @param _changedProperties Map of changed properties with old values\n   * @category updates\n   */\n  protected firstUpdated(_changedProperties: PropertyValues) {}\n}\n\n// Apply polyfills if available\npolyfillSupport?.({ReactiveElement});\n\n// Dev mode warnings...\nif (DEV_MODE) {\n  // Default warning set.\n  ReactiveElement.enabledWarnings = ['change-in-update'];\n  const ensureOwnWarnings = function (ctor: typeof ReactiveElement) {\n    if (\n      !ctor.hasOwnProperty(JSCompiler_renameProperty('enabledWarnings', ctor))\n    ) {\n      ctor.enabledWarnings = ctor.enabledWarnings!.slice();\n    }\n  };\n  ReactiveElement.enableWarning = function (\n    this: typeof ReactiveElement,\n    warning: WarningKind\n  ) {\n    ensureOwnWarnings(this);\n    if (this.enabledWarnings!.indexOf(warning) < 0) {\n      this.enabledWarnings!.push(warning);\n    }\n  };\n  ReactiveElement.disableWarning = function (\n    this: typeof ReactiveElement,\n    warning: WarningKind\n  ) {\n    ensureOwnWarnings(this);\n    const i = this.enabledWarnings!.indexOf(warning);\n    if (i >= 0) {\n      this.enabledWarnings!.splice(i, 1);\n    }\n  };\n}\n\n// IMPORTANT: do not change the property name or the assignment expression.\n// This line will be used in regexes to search for ReactiveElement usage.\n(global.reactiveElementVersions ??= []).push('1.6.3');\nif (DEV_MODE && global.reactiveElementVersions.length > 1) {\n  issueWarning!(\n    'multiple-versions',\n    `Multiple versions of Lit loaded. Loading multiple versions ` +\n      `is not recommended.`\n  );\n}\n"]}