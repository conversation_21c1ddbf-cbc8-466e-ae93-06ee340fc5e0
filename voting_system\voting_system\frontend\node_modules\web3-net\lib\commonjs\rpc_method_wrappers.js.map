{"version": 3, "file": "rpc_method_wrappers.js", "sourceRoot": "", "sources": ["../../src/rpc_method_wrappers.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;EAeE;;;;;;;;;;;;AAEF,2CAAoC;AAGpC,uDAAiD;AAEjD,SAAsB,KAAK,CAC1B,WAAoC,EACpC,YAA0B;;QAE1B,MAAM,QAAQ,GAAG,MAAM,gCAAa,CAAC,KAAK,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;QAEvE,OAAO,IAAA,mBAAM,EAAC,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,QAA6B,EAAE,YAAY,CAAC,CAAC;IAChF,CAAC;CAAA;AAPD,sBAOC;AAED,SAAsB,YAAY,CACjC,WAAoC,EACpC,YAA0B;;QAE1B,MAAM,QAAQ,GAAG,MAAM,gCAAa,CAAC,YAAY,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;QAE9E,wCAAwC;QACxC,OAAO,IAAA,mBAAM,EAAC,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,QAA6B,EAAE,YAAY,CAAC,CAAC;IAChF,CAAC;CAAA;AARD,oCAQC;AAEM,MAAM,WAAW,GAAG,CAAO,WAAoC,EAAE,EAAE,kDACzE,OAAA,gCAAa,CAAC,WAAW,CAAC,WAAW,CAAC,cAAc,CAAC,CAAA,GAAA,CAAC;AAD1C,QAAA,WAAW,eAC+B"}