try:
    from ._version import version as __version__
except ImportError:
    # broken installation, we don't even try
    # unknown only works because we do poor mans version compare
    __version__ = "unknown"

__all__ = [
    "__version__",
    "PluginManager",
    "PluginValidationError",
    "<PERSON><PERSON><PERSON>r",
    "Hook<PERSON>allError",
    "HookspecO<PERSON>",
    "HookimplOpts",
    "HookImpl",
    "HookRelay",
    "HookspecMarker",
    "HookimplMarker",
    "Result",
    "PluggyWarning",
    "PluggyTeardownRaisedWarning",
]

from ._hooks import HookCaller
from ._hooks import <PERSON><PERSON>mp<PERSON>
from ._hooks import <PERSON>implMarker
from ._hooks import HookimplOpts
from ._hooks import <PERSON><PERSON><PERSON><PERSON>
from ._hooks import <PERSON>specMark<PERSON>
from ._hooks import HookspecOpts
from ._manager import PluginManager
from ._manager import PluginValidationError
from ._result import HookCallError
from ._result import Result
from ._warnings import PluggyTeardownRaisedWarning
from ._warnings import Pluggy<PERSON>arning
