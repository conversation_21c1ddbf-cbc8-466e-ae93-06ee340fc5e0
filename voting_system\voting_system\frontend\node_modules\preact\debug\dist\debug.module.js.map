{"version": 3, "file": "debug.module.js", "sources": ["../src/check-props.js", "../src/component-stack.js", "../src/debug.js", "../src/constants.js"], "sourcesContent": ["const ReactPropTypesSecret = 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED';\n\nlet loggedTypeFailures = {};\n\n/**\n * Reset the history of which prop type warnings have been logged.\n */\nexport function resetPropWarnings() {\n\tloggedTypeFailures = {};\n}\n\n/**\n * Assert that the values match with the type specs.\n * Error messages are memorized and will only be shown once.\n *\n * Adapted from https://github.com/facebook/prop-types/blob/master/checkPropTypes.js\n *\n * @param {object} typeSpecs Map of name to a ReactPropType\n * @param {object} values Runtime values that need to be type-checked\n * @param {string} location e.g. \"prop\", \"context\", \"child context\"\n * @param {string} componentName Name of the component for error messages.\n * @param {?Function} getStack Returns the component stack.\n */\nexport function checkPropTypes(\n\ttypeSpecs,\n\tvalues,\n\tlocation,\n\tcomponentName,\n\tgetStack\n) {\n\tObject.keys(typeSpecs).forEach(typeSpecName => {\n\t\tlet error;\n\t\ttry {\n\t\t\terror = typeSpecs[typeSpecName](\n\t\t\t\tvalues,\n\t\t\t\ttypeSpecName,\n\t\t\t\tcomponentName,\n\t\t\t\tlocation,\n\t\t\t\tnull,\n\t\t\t\tReactPropTypesSecret\n\t\t\t);\n\t\t} catch (e) {\n\t\t\terror = e;\n\t\t}\n\t\tif (error && !(error.message in loggedTypeFailures)) {\n\t\t\tloggedTypeFailures[error.message] = true;\n\t\t\tconsole.error(\n\t\t\t\t`Failed ${location} type: ${error.message}${(getStack && getStack()) ||\n\t\t\t\t\t''}`\n\t\t\t);\n\t\t}\n\t});\n}\n", "import { options, Fragment } from 'preact';\n\n/**\n * Get human readable name of the component/dom node\n * @param {import('./internal').VNode} vnode\n * @param {import('./internal').VNode} vnode\n * @returns {string}\n */\nexport function getDisplayName(vnode) {\n\tif (vnode.type === Fragment) {\n\t\treturn 'Fragment';\n\t} else if (typeof vnode.type == 'function') {\n\t\treturn vnode.type.displayName || vnode.type.name;\n\t} else if (typeof vnode.type == 'string') {\n\t\treturn vnode.type;\n\t}\n\n\treturn '#text';\n}\n\n/**\n * Used to keep track of the currently rendered `vnode` and print it\n * in debug messages.\n */\nlet renderStack = [];\n\n/**\n * Keep track of the current owners. An owner describes a component\n * which was responsible to render a specific `vnode`. This exclude\n * children that are passed via `props.children`, because they belong\n * to the parent owner.\n *\n * ```jsx\n * const Foo = props => <div>{props.children}</div> // div's owner is Foo\n * const Bar = props => {\n *   return (\n *     <Foo><span /></Foo> // Foo's owner is Bar, span's owner is Bar\n *   )\n * }\n * ```\n *\n * Note: A `vnode` may be hoisted to the root scope due to compiler\n * optimiztions. In these cases the `_owner` will be different.\n */\nlet ownerStack = [];\n\n/**\n * Get the currently rendered `vnode`\n * @returns {import('./internal').VNode | null}\n */\nexport function getCurrentVNode() {\n\treturn renderStack.length > 0 ? renderStack[renderStack.length - 1] : null;\n}\n\n/**\n * If the user doesn't have `@babel/plugin-transform-react-jsx-source`\n * somewhere in his tool chain we can't print the filename and source\n * location of a component. In that case we just omit that, but we'll\n * print a helpful message to the console, notifying the user of it.\n */\nlet hasBabelPlugin = false;\n\n/**\n * Check if a `vnode` is a possible owner.\n * @param {import('./internal').VNode} vnode\n */\nfunction isPossibleOwner(vnode) {\n\treturn typeof vnode.type == 'function' && vnode.type != Fragment;\n}\n\n/**\n * Return the component stack that was captured up to this point.\n * @param {import('./internal').VNode} vnode\n * @returns {string}\n */\nexport function getOwnerStack(vnode) {\n\tconst stack = [vnode];\n\tlet next = vnode;\n\twhile (next._owner != null) {\n\t\tstack.push(next._owner);\n\t\tnext = next._owner;\n\t}\n\n\treturn stack.reduce((acc, owner) => {\n\t\tacc += `  in ${getDisplayName(owner)}`;\n\n\t\tconst source = owner.__source;\n\t\tif (source) {\n\t\t\tacc += ` (at ${source.fileName}:${source.lineNumber})`;\n\t\t} else if (!hasBabelPlugin) {\n\t\t\thasBabelPlugin = true;\n\t\t\tconsole.warn(\n\t\t\t\t'Add @babel/plugin-transform-react-jsx-source to get a more detailed component stack. Note that you should not add it to production builds of your App for bundle size reasons.'\n\t\t\t);\n\t\t}\n\n\t\treturn (acc += '\\n');\n\t}, '');\n}\n\n/**\n * Setup code to capture the component trace while rendering. Note that\n * we cannot simply traverse `vnode._parent` upwards, because we have some\n * debug messages for `this.setState` where the `vnode` is `undefined`.\n */\nexport function setupComponentStack() {\n\tlet oldDiff = options._diff;\n\tlet oldDiffed = options.diffed;\n\tlet oldRoot = options._root;\n\tlet oldVNode = options.vnode;\n\tlet oldRender = options._render;\n\n\toptions.diffed = vnode => {\n\t\tif (isPossibleOwner(vnode)) {\n\t\t\townerStack.pop();\n\t\t}\n\t\trenderStack.pop();\n\t\tif (oldDiffed) oldDiffed(vnode);\n\t};\n\n\toptions._diff = vnode => {\n\t\tif (isPossibleOwner(vnode)) {\n\t\t\trenderStack.push(vnode);\n\t\t}\n\t\tif (oldDiff) oldDiff(vnode);\n\t};\n\n\toptions._root = (vnode, parent) => {\n\t\townerStack = [];\n\t\tif (oldRoot) oldRoot(vnode, parent);\n\t};\n\n\toptions.vnode = vnode => {\n\t\tvnode._owner =\n\t\t\townerStack.length > 0 ? ownerStack[ownerStack.length - 1] : null;\n\t\tif (oldVNode) oldVNode(vnode);\n\t};\n\n\toptions._render = vnode => {\n\t\tif (isPossibleOwner(vnode)) {\n\t\t\townerStack.push(vnode);\n\t\t}\n\n\t\tif (oldRender) oldRender(vnode);\n\t};\n}\n", "import { checkPropTypes } from './check-props';\nimport { options, Component } from 'preact';\nimport {\n\tELEMENT_NODE,\n\tDOCUMENT_NODE,\n\tDOCUMENT_FRAGMENT_NODE\n} from './constants';\nimport {\n\tgetOwnerStack,\n\tsetupComponentStack,\n\tgetCurrentVNode,\n\tgetDisplayName\n} from './component-stack';\n\nconst isWeakMapSupported = typeof WeakMap == 'function';\n\nfunction getClosestDomNodeParent(parent) {\n\tif (!parent) return {};\n\tif (typeof parent.type == 'function') {\n\t\treturn getClosestDomNodeParent(parent._parent);\n\t}\n\treturn parent;\n}\n\nexport function initDebug() {\n\tsetupComponentStack();\n\n\t/* eslint-disable no-console */\n\tlet oldBeforeDiff = options._diff;\n\tlet oldDiffed = options.diffed;\n\tlet oldVnode = options.vnode;\n\tlet oldCatchError = options._catchError;\n\tlet oldRoot = options._root;\n\tlet oldHook = options._hook;\n\tconst warnedComponents = !isWeakMapSupported\n\t\t? null\n\t\t: {\n\t\t\t\tuseEffect: new WeakMap(),\n\t\t\t\tuseLayoutEffect: new WeakMap(),\n\t\t\t\tlazyPropTypes: new WeakMap()\n\t\t  };\n\n\toptions._catchError = (error, vnode, oldVNode) => {\n\t\tlet component = vnode && vnode._component;\n\t\tif (component && typeof error.then == 'function') {\n\t\t\tconst promise = error;\n\t\t\terror = new Error(\n\t\t\t\t`Missing Suspense. The throwing component was: ${getDisplayName(vnode)}`\n\t\t\t);\n\n\t\t\tlet parent = vnode;\n\t\t\tfor (; parent; parent = parent._parent) {\n\t\t\t\tif (parent._component && parent._component._childDidSuspend) {\n\t\t\t\t\terror = promise;\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// We haven't recovered and we know at this point that there is no\n\t\t\t// Suspense component higher up in the tree\n\t\t\tif (error instanceof Error) {\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t}\n\n\t\toldCatchError(error, vnode, oldVNode);\n\t};\n\n\toptions._root = (vnode, parentNode) => {\n\t\tif (!parentNode) {\n\t\t\tthrow new Error(\n\t\t\t\t'Undefined parent passed to render(), this is the second argument.\\n' +\n\t\t\t\t\t'Check if the element is available in the DOM/has the correct id.'\n\t\t\t);\n\t\t}\n\t\tlet isValid;\n\t\tswitch (parentNode.nodeType) {\n\t\t\tcase ELEMENT_NODE:\n\t\t\tcase DOCUMENT_FRAGMENT_NODE:\n\t\t\tcase DOCUMENT_NODE:\n\t\t\t\tisValid = true;\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tisValid = false;\n\t\t}\n\n\t\tif (!isValid) {\n\t\t\tlet componentName = getDisplayName(vnode);\n\t\t\tthrow new Error(\n\t\t\t\t`Expected a valid HTML node as a second argument to render.\tReceived ${parentNode} instead: render(<${componentName} />, ${parentNode});`\n\t\t\t);\n\t\t}\n\n\t\tif (oldRoot) oldRoot(vnode, parentNode);\n\t};\n\n\toptions._diff = vnode => {\n\t\tlet { type, _parent: parent } = vnode;\n\t\tlet parentVNode = getClosestDomNodeParent(parent);\n\n\t\tif (type === undefined) {\n\t\t\tthrow new Error(\n\t\t\t\t'Undefined component passed to createElement()\\n\\n' +\n\t\t\t\t\t'You likely forgot to export your component or might have mixed up default and named imports' +\n\t\t\t\t\tserializeVNode(vnode) +\n\t\t\t\t\t`\\n\\n${getOwnerStack(vnode)}`\n\t\t\t);\n\t\t} else if (type != null && typeof type == 'object') {\n\t\t\tif (type._children !== undefined && type._dom !== undefined) {\n\t\t\t\tthrow new Error(\n\t\t\t\t\t`Invalid type passed to createElement(): ${type}\\n\\n` +\n\t\t\t\t\t\t'Did you accidentally pass a JSX literal as JSX twice?\\n\\n' +\n\t\t\t\t\t\t`  let My${getDisplayName(vnode)} = ${serializeVNode(type)};\\n` +\n\t\t\t\t\t\t`  let vnode = <My${getDisplayName(vnode)} />;\\n\\n` +\n\t\t\t\t\t\t'This usually happens when you export a JSX literal and not the component.' +\n\t\t\t\t\t\t`\\n\\n${getOwnerStack(vnode)}`\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tthrow new Error(\n\t\t\t\t'Invalid type passed to createElement(): ' +\n\t\t\t\t\t(Array.isArray(type) ? 'array' : type)\n\t\t\t);\n\t\t}\n\n\t\tif (\n\t\t\t(type === 'thead' || type === 'tfoot' || type === 'tbody') &&\n\t\t\tparentVNode.type !== 'table'\n\t\t) {\n\t\t\tconsole.error(\n\t\t\t\t'Improper nesting of table. Your <thead/tbody/tfoot> should have a <table> parent.' +\n\t\t\t\t\tserializeVNode(vnode) +\n\t\t\t\t\t`\\n\\n${getOwnerStack(vnode)}`\n\t\t\t);\n\t\t} else if (\n\t\t\ttype === 'tr' &&\n\t\t\tparentVNode.type !== 'thead' &&\n\t\t\tparentVNode.type !== 'tfoot' &&\n\t\t\tparentVNode.type !== 'tbody' &&\n\t\t\tparentVNode.type !== 'table'\n\t\t) {\n\t\t\tconsole.error(\n\t\t\t\t'Improper nesting of table. Your <tr> should have a <thead/tbody/tfoot/table> parent.' +\n\t\t\t\t\tserializeVNode(vnode) +\n\t\t\t\t\t`\\n\\n${getOwnerStack(vnode)}`\n\t\t\t);\n\t\t} else if (type === 'td' && parentVNode.type !== 'tr') {\n\t\t\tconsole.error(\n\t\t\t\t'Improper nesting of table. Your <td> should have a <tr> parent.' +\n\t\t\t\t\tserializeVNode(vnode) +\n\t\t\t\t\t`\\n\\n${getOwnerStack(vnode)}`\n\t\t\t);\n\t\t} else if (type === 'th' && parentVNode.type !== 'tr') {\n\t\t\tconsole.error(\n\t\t\t\t'Improper nesting of table. Your <th> should have a <tr>.' +\n\t\t\t\t\tserializeVNode(vnode) +\n\t\t\t\t\t`\\n\\n${getOwnerStack(vnode)}`\n\t\t\t);\n\t\t}\n\n\t\tif (\n\t\t\tvnode.ref !== undefined &&\n\t\t\ttypeof vnode.ref != 'function' &&\n\t\t\ttypeof vnode.ref != 'object' &&\n\t\t\t!('$$typeof' in vnode) // allow string refs when preact-compat is installed\n\t\t) {\n\t\t\tthrow new Error(\n\t\t\t\t`Component's \"ref\" property should be a function, or an object created ` +\n\t\t\t\t\t`by createRef(), but got [${typeof vnode.ref}] instead\\n` +\n\t\t\t\t\tserializeVNode(vnode) +\n\t\t\t\t\t`\\n\\n${getOwnerStack(vnode)}`\n\t\t\t);\n\t\t}\n\n\t\tif (typeof vnode.type == 'string') {\n\t\t\tfor (const key in vnode.props) {\n\t\t\t\tif (\n\t\t\t\t\tkey[0] === 'o' &&\n\t\t\t\t\tkey[1] === 'n' &&\n\t\t\t\t\ttypeof vnode.props[key] != 'function' &&\n\t\t\t\t\tvnode.props[key] != null\n\t\t\t\t) {\n\t\t\t\t\tthrow new Error(\n\t\t\t\t\t\t`Component's \"${key}\" property should be a function, ` +\n\t\t\t\t\t\t\t`but got [${typeof vnode.props[key]}] instead\\n` +\n\t\t\t\t\t\t\tserializeVNode(vnode) +\n\t\t\t\t\t\t\t`\\n\\n${getOwnerStack(vnode)}`\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// Check prop-types if available\n\t\tif (typeof vnode.type == 'function' && vnode.type.propTypes) {\n\t\t\tif (\n\t\t\t\tvnode.type.displayName === 'Lazy' &&\n\t\t\t\twarnedComponents &&\n\t\t\t\t!warnedComponents.lazyPropTypes.has(vnode.type)\n\t\t\t) {\n\t\t\t\tconst m =\n\t\t\t\t\t'PropTypes are not supported on lazy(). Use propTypes on the wrapped component itself. ';\n\t\t\t\ttry {\n\t\t\t\t\tconst lazyVNode = vnode.type();\n\t\t\t\t\twarnedComponents.lazyPropTypes.set(vnode.type, true);\n\t\t\t\t\tconsole.warn(\n\t\t\t\t\t\tm + `Component wrapped in lazy() is ${getDisplayName(lazyVNode)}`\n\t\t\t\t\t);\n\t\t\t\t} catch (promise) {\n\t\t\t\t\tconsole.warn(\n\t\t\t\t\t\tm + \"We will log the wrapped component's name once it is loaded.\"\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t}\n\t\t\tcheckPropTypes(\n\t\t\t\tvnode.type.propTypes,\n\t\t\t\tvnode.props,\n\t\t\t\t'prop',\n\t\t\t\tgetDisplayName(vnode)\n\t\t\t);\n\t\t}\n\n\t\tif (oldBeforeDiff) oldBeforeDiff(vnode);\n\t};\n\n\toptions._hook = (comp, index, type) => {\n\t\tif (!comp) {\n\t\t\tthrow new Error('Hook can only be invoked from render methods.');\n\t\t}\n\n\t\tif (oldHook) oldHook(comp, index, type);\n\t};\n\n\tconst warn = (property, err) => ({\n\t\tget() {\n\t\t\tconsole.warn(`getting vnode.${property} is deprecated, ${err}`);\n\t\t},\n\t\tset() {\n\t\t\tconsole.warn(`setting vnode.${property} is not allowed, ${err}`);\n\t\t}\n\t});\n\n\tconst deprecatedAttributes = {\n\t\tnodeName: warn('nodeName', 'use vnode.type'),\n\t\tattributes: warn('attributes', 'use vnode.props'),\n\t\tchildren: warn('children', 'use vnode.props.children')\n\t};\n\n\tconst deprecatedProto = Object.create({}, deprecatedAttributes);\n\n\toptions.vnode = vnode => {\n\t\tconst props = vnode.props;\n\t\tif (\n\t\t\tvnode.type !== null &&\n\t\t\tprops != null &&\n\t\t\t('__source' in props || '__self' in props)\n\t\t) {\n\t\t\tconst newProps = (vnode.props = {});\n\t\t\tfor (let i in props) {\n\t\t\t\tconst v = props[i];\n\t\t\t\tif (i === '__source') vnode.__source = v;\n\t\t\t\telse if (i === '__self') vnode.__self = v;\n\t\t\t\telse newProps[i] = v;\n\t\t\t}\n\t\t}\n\t\tObject.setPrototypeOf(vnode, deprecatedProto);\n\t\tif (oldVnode) oldVnode(vnode);\n\t};\n\n\toptions.diffed = vnode => {\n\t\t// Check if the user passed plain objects as children. Note that we cannot\n\t\t// move this check into `options.vnode` because components can receive\n\t\t// children in any shape they want (e.g.\n\t\t// `<MyJSONFormatter>{{ foo: 123, bar: \"abc\" }}</MyJSONFormatter>`).\n\t\t// Putting this check in `options.diffed` ensures that\n\t\t// `vnode._children` is set and that we only validate the children\n\t\t// that were actually rendered.\n\t\tif (vnode._children) {\n\t\t\tvnode._children.forEach(child => {\n\t\t\t\tif (child && child.type === undefined) {\n\t\t\t\t\t// Remove internal vnode keys that will always be patched\n\t\t\t\t\tdelete child._parent;\n\t\t\t\t\tdelete child._depth;\n\t\t\t\t\tconst keys = Object.keys(child).join(',');\n\t\t\t\t\tthrow new Error(\n\t\t\t\t\t\t`Objects are not valid as a child. Encountered an object with the keys {${keys}}.` +\n\t\t\t\t\t\t\t`\\n\\n${getOwnerStack(vnode)}`\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\n\t\t/** @type {import('./internal').Component} */\n\t\tconst component = vnode._component;\n\t\tif (component && component.__hooks) {\n\t\t\tlet hooks = component.__hooks;\n\t\t\tif (Array.isArray(hooks._list)) {\n\t\t\t\thooks._list.forEach(hook => {\n\t\t\t\t\tif (hook._factory && (!hook._args || !Array.isArray(hook._args))) {\n\t\t\t\t\t\tlet componentName = getDisplayName(vnode);\n\t\t\t\t\t\tconsole.warn(\n\t\t\t\t\t\t\t`In ${componentName} you are calling useMemo/useCallback without passing arguments.\\n` +\n\t\t\t\t\t\t\t\t`This is a noop since it will not be able to memoize, it will execute it every render.` +\n\t\t\t\t\t\t\t\t`\\n\\n${getOwnerStack(vnode)}`\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\n\t\tif (oldDiffed) oldDiffed(vnode);\n\n\t\tif (vnode._children != null) {\n\t\t\tconst keys = [];\n\t\t\tfor (let i = 0; i < vnode._children.length; i++) {\n\t\t\t\tconst child = vnode._children[i];\n\t\t\t\tif (!child || child.key == null) continue;\n\n\t\t\t\tconst key = child.key;\n\t\t\t\tif (keys.indexOf(key) !== -1) {\n\t\t\t\t\tconsole.error(\n\t\t\t\t\t\t'Following component has two or more children with the ' +\n\t\t\t\t\t\t\t`same key attribute: \"${key}\". This may cause glitches and misbehavior ` +\n\t\t\t\t\t\t\t'in rendering process. Component: \\n\\n' +\n\t\t\t\t\t\t\tserializeVNode(vnode) +\n\t\t\t\t\t\t\t`\\n\\n${getOwnerStack(vnode)}`\n\t\t\t\t\t);\n\n\t\t\t\t\t// Break early to not spam the console\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\n\t\t\t\tkeys.push(key);\n\t\t\t}\n\t\t}\n\t};\n}\n\nconst setState = Component.prototype.setState;\nComponent.prototype.setState = function(update, callback) {\n\tif (this._vnode == null) {\n\t\t// `this._vnode` will be `null` during componentWillMount. But it\n\t\t// is perfectly valid to call `setState` during cWM. So we\n\t\t// need an additional check to verify that we are dealing with a\n\t\t// call inside constructor.\n\t\tif (this.state == null) {\n\t\t\tconsole.warn(\n\t\t\t\t`Calling \"this.setState\" inside the constructor of a component is a ` +\n\t\t\t\t\t`no-op and might be a bug in your application. Instead, set ` +\n\t\t\t\t\t`\"this.state = {}\" directly.\\n\\n${getOwnerStack(getCurrentVNode())}`\n\t\t\t);\n\t\t}\n\t} else if (this._parentDom == null) {\n\t\tconsole.warn(\n\t\t\t`Can't call \"this.setState\" on an unmounted component. This is a no-op, ` +\n\t\t\t\t`but it indicates a memory leak in your application. To fix, cancel all ` +\n\t\t\t\t`subscriptions and asynchronous tasks in the componentWillUnmount method.` +\n\t\t\t\t`\\n\\n${getOwnerStack(this._vnode)}`\n\t\t);\n\t}\n\n\treturn setState.call(this, update, callback);\n};\n\nconst forceUpdate = Component.prototype.forceUpdate;\nComponent.prototype.forceUpdate = function(callback) {\n\tif (this._vnode == null) {\n\t\tconsole.warn(\n\t\t\t`Calling \"this.forceUpdate\" inside the constructor of a component is a ` +\n\t\t\t\t`no-op and might be a bug in your application.\\n\\n${getOwnerStack(\n\t\t\t\t\tgetCurrentVNode()\n\t\t\t\t)}`\n\t\t);\n\t} else if (this._parentDom == null) {\n\t\tconsole.warn(\n\t\t\t`Can't call \"this.forceUpdate\" on an unmounted component. This is a no-op, ` +\n\t\t\t\t`but it indicates a memory leak in your application. To fix, cancel all ` +\n\t\t\t\t`subscriptions and asynchronous tasks in the componentWillUnmount method.` +\n\t\t\t\t`\\n\\n${getOwnerStack(this._vnode)}`\n\t\t);\n\t}\n\treturn forceUpdate.call(this, callback);\n};\n\n/**\n * Serialize a vnode tree to a string\n * @param {import('./internal').VNode} vnode\n * @returns {string}\n */\nexport function serializeVNode(vnode) {\n\tlet { props } = vnode;\n\tlet name = getDisplayName(vnode);\n\n\tlet attrs = '';\n\tfor (let prop in props) {\n\t\tif (props.hasOwnProperty(prop) && prop !== 'children') {\n\t\t\tlet value = props[prop];\n\n\t\t\t// If it is an object but doesn't have toString(), use Object.toString\n\t\t\tif (typeof value == 'function') {\n\t\t\t\tvalue = `function ${value.displayName || value.name}() {}`;\n\t\t\t}\n\n\t\t\tvalue =\n\t\t\t\tObject(value) === value && !value.toString\n\t\t\t\t\t? Object.prototype.toString.call(value)\n\t\t\t\t\t: value + '';\n\n\t\t\tattrs += ` ${prop}=${JSON.stringify(value)}`;\n\t\t}\n\t}\n\n\tlet children = props.children;\n\treturn `<${name}${attrs}${\n\t\tchildren && children.length ? '>..</' + name + '>' : ' />'\n\t}`;\n}\n", "export const ELEMENT_NODE = 1;\nexport const DOCUMENT_NODE = 9;\nexport const DOCUMENT_FRAGMENT_NODE = 11;\n"], "names": ["loggedTypeFailures", "resetPropWarnings", "getDisplayName", "vnode", "type", "Fragment", "displayName", "name", "renderStack", "ownerStack", "getCurrentVNode", "length", "hasBabelPlugin", "isPossibleOwner", "getOwnerStack", "stack", "next", "_owner", "push", "reduce", "acc", "owner", "source", "__source", "fileName", "lineNumber", "console", "warn", "isWeakMapSupported", "WeakMap", "setState", "Component", "prototype", "update", "callback", "this", "_vnode", "state", "_parentDom", "call", "forceUpdate", "serializeVNode", "props", "attrs", "prop", "hasOwnProperty", "value", "Object", "toString", "JSON", "stringify", "children", "oldDiff", "options", "_diff", "oldDiffed", "diffed", "oldRoot", "_root", "oldVNode", "old<PERSON><PERSON>", "_render", "pop", "parent", "setupComponentStack", "oldBeforeDiff", "oldVnode", "oldCatchError", "_catchError", "oldHook", "_hook", "warnedComponents", "useEffect", "useLayoutEffect", "lazyPropTypes", "error", "_component", "then", "promise", "Error", "_parent", "_childDidSuspend", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "nodeType", "componentName", "typeSpecs", "values", "parentVNode", "getClosestDomNodeParent", "undefined", "_children", "_dom", "Array", "isArray", "ref", "key", "propTypes", "has", "m", "lazyVNode", "set", "keys", "for<PERSON>ach", "typeSpecName", "e", "message", "comp", "index", "property", "err", "get", "deprecatedAttributes", "nodeName", "attributes", "deprecatedProto", "create", "newProps", "i", "v", "__self", "setPrototypeOf", "child", "_depth", "join", "component", "__hooks", "hooks", "_list", "hook", "_factory", "_args", "indexOf"], "mappings": "sFAAA,IAEIA,EAAqB,GAKzB,SAAgBC,IACfD,EAAqB,GCAf,SAASE,EAAeC,UAC1BA,EAAMC,OAASC,EACX,WACwB,mBAAdF,EAAMC,KAChBD,EAAMC,KAAKE,aAAeH,EAAMC,KAAKG,KACb,iBAAdJ,EAAMC,KAChBD,EAAMC,KAGP,QAOR,IAAII,EAAc,GAoBdC,EAAa,GAMjB,SAAgBC,WACRF,EAAYG,OAAS,EAAIH,EAAYA,EAAYG,OAAS,GAAK,KASvE,IAAIC,GAAiB,EAMrB,SAASC,EAAgBV,SACI,mBAAdA,EAAMC,MAAsBD,EAAMC,MAAQC,EAQlD,SAASS,EAAcX,WACvBY,EAAQ,CAACZ,GACXa,EAAOb,EACW,MAAfa,EAAKC,KACXF,EAAMG,KAAKF,EAAKC,KAChBD,EAAOA,EAAKC,WAGNF,EAAMI,OAAO,SAACC,EAAKC,GACzBD,WAAelB,EAAemB,OAExBC,EAASD,EAAME,gBACjBD,EACHF,WAAeE,EAAOE,aAAYF,EAAOG,eAC9Bb,IACXA,GAAiB,EACjBc,QAAQC,KACP,mLAIMP,EAAO,MACb,ICnFJ,IAAMQ,EAAuC,mBAAXC,QAmU5BC,EAAWC,EAAUC,UAAUF,SACrCC,EAAUC,UAAUF,SAAW,SAASG,EAAQC,UAC5B,MAAfC,KAAKC,IAKU,MAAdD,KAAKE,OACRX,QAAQC,KACP,gKAEmCb,EAAcJ,MAGtB,MAAnByB,KAAKG,KACfZ,QAAQC,KACP,8NAGQb,EAAcqB,KAAKC,MAItBN,EAASS,KAAKJ,KAAMF,EAAQC,IAGpC,IAAMM,EAAcT,EAAUC,UAAUQ,YAyBjC,SAASC,EAAetC,OACxBuC,EAAUvC,EAAVuC,MACFnC,EAAOL,EAAeC,GAEtBwC,EAAQ,OACP,IAAIC,KAAQF,KACZA,EAAMG,eAAeD,IAAkB,aAATA,EAAqB,KAClDE,EAAQJ,EAAME,GAGE,mBAATE,IACVA,eAAoBA,EAAMxC,aAAewC,EAAMvC,eAGhDuC,EACCC,OAAOD,KAAWA,GAAUA,EAAME,SAE/BF,EAAQ,GADRC,OAAOf,UAAUgB,SAAST,KAAKO,GAGnCH,OAAaC,MAAQK,KAAKC,UAAUJ,OAIlCK,EAAWT,EAAMS,mBACV5C,EAAOoC,GACjBQ,GAAYA,EAASxC,OAAS,QAAUJ,EAAO,IAAM,OAjDvDwB,EAAUC,UAAUQ,YAAc,SAASN,UACvB,MAAfC,KAAKC,IACRV,QAAQC,KACP,0HACqDb,EACnDJ,MAG0B,MAAnByB,KAAKG,KACfZ,QAAQC,KACP,iOAGQb,EAAcqB,KAAKC,MAGtBI,EAAYD,KAAKJ,KAAMD,IApW/B,YDiFA,eACKkB,EAAUC,EAAQC,IAClBC,EAAYF,EAAQG,OACpBC,EAAUJ,EAAQK,GAClBC,EAAWN,EAAQlD,MACnByD,EAAYP,EAAQQ,IAExBR,EAAQG,OAAS,SAAArD,GACZU,EAAgBV,IACnBM,EAAWqD,MAEZtD,EAAYsD,MACRP,GAAWA,EAAUpD,IAG1BkD,EAAQC,IAAQ,SAAAnD,GACXU,EAAgBV,IACnBK,EAAYU,KAAKf,GAEdiD,GAASA,EAAQjD,IAGtBkD,EAAQK,GAAQ,SAACvD,EAAO4D,GACvBtD,EAAa,GACTgD,GAASA,EAAQtD,EAAO4D,IAG7BV,EAAQlD,MAAQ,SAAAA,GACfA,EAAMc,IACLR,EAAWE,OAAS,EAAIF,EAAWA,EAAWE,OAAS,GAAK,KACzDgD,GAAUA,EAASxD,IAGxBkD,EAAQQ,IAAU,SAAA1D,GACbU,EAAgBV,IACnBM,EAAWS,KAAKf,GAGbyD,GAAWA,EAAUzD,ICtH1B6D,OAGIC,EAAgBZ,EAAQC,IACxBC,EAAYF,EAAQG,OACpBU,EAAWb,EAAQlD,MACnBgE,EAAgBd,EAAQe,IACxBX,EAAUJ,EAAQK,GAClBW,EAAUhB,EAAQiB,IAChBC,EAAoB3C,EAEvB,CACA4C,UAAW,IAAI3C,QACf4C,gBAAiB,IAAI5C,QACrB6C,cAAe,IAAI7C,SAJnB,KAOHwB,EAAQe,IAAc,SAACO,EAAOxE,EAAOwD,MACpBxD,GAASA,EAAMyE,KACO,mBAAdD,EAAME,KAAoB,KAC3CC,EAAUH,EAChBA,EAAQ,IAAII,uDACsC7E,EAAeC,YAG7D4D,EAAS5D,EACN4D,EAAQA,EAASA,EAAOiB,MAC1BjB,EAAOa,KAAcb,EAAOa,IAAWK,IAAkB,CAC5DN,EAAQG,WAONH,aAAiBI,YACdJ,EAIRR,EAAcQ,EAAOxE,EAAOwD,IAG7BN,EAAQK,GAAQ,SAACvD,EAAO+E,OAClBA,QACE,IAAIH,MACT,2IAIEI,SACID,EAAWE,eC5EO,OAEU,QADT,ED+EzBD,GAAU,gBAGVA,GAAU,MAGPA,EAAS,KACTE,EAAgBnF,EAAeC,SAC7B,IAAI4E,8EAC8DG,uBAA+BG,UAAqBH,QAIzHzB,GAASA,EAAQtD,EAAO+E,IAG7B7B,EAAQC,IAAQ,SAAAnD,OFxEhBmF,EACAC,EAEAF,EEsEOjF,EAA0BD,EAA1BC,KACFoF,EAlFN,SAASC,EAAwB1B,UAC3BA,EACqB,mBAAfA,EAAO3D,KACVqF,EAAwB1B,EAAOiB,IAEhCjB,EAJa,GAiFD0B,CADctF,EAApB6E,YAGCU,IAATtF,QACG,IAAI2E,MACT,+IAECtC,EAAetC,UACRW,EAAcX,IAEjB,GAAY,MAARC,GAA+B,iBAARA,EAAkB,SAC5BsF,IAAnBtF,EAAKuF,UAAyCD,IAAdtF,EAAKwF,UAClC,IAAIb,MACT,2CAA2C3E,0EAE/BF,EAAeC,SAAYsC,EAAerC,0BACjCF,EAAeC,2FAE5BW,EAAcX,UAIlB,IAAI4E,MACT,4CACEc,MAAMC,QAAQ1F,GAAQ,QAAUA,OAKzB,UAATA,GAA6B,UAATA,GAA6B,UAATA,GACpB,UAArBoF,EAAYpF,KAQH,OAATA,GACqB,UAArBoF,EAAYpF,MACS,UAArBoF,EAAYpF,MACS,UAArBoF,EAAYpF,MACS,UAArBoF,EAAYpF,KAEZsB,QAAQiD,MACP,uFACClC,EAAetC,UACRW,EAAcX,IAEJ,OAATC,GAAsC,OAArBoF,EAAYpF,KACvCsB,QAAQiD,MACP,kEACClC,EAAetC,UACRW,EAAcX,IAEJ,OAATC,GAAsC,OAArBoF,EAAYpF,MACvCsB,QAAQiD,MACP,2DACClC,EAAetC,UACRW,EAAcX,IA3BvBuB,QAAQiD,MACP,oFACClC,EAAetC,UACRW,EAAcX,SA6BTuF,IAAdvF,EAAM4F,KACc,mBAAb5F,EAAM4F,KACO,iBAAb5F,EAAM4F,OACX,aAAc5F,SAEV,IAAI4E,MACT,0GACoC5E,EAAM4F,kBACzCtD,EAAetC,UACRW,EAAcX,OAIC,iBAAdA,EAAMC,SACX,IAAM4F,KAAO7F,EAAMuC,SAEX,MAAXsD,EAAI,IACO,MAAXA,EAAI,IACuB,mBAApB7F,EAAMuC,MAAMsD,IACC,MAApB7F,EAAMuC,MAAMsD,SAEN,IAAIjB,MACT,iBAAgBiB,sDACI7F,EAAMuC,MAAMsD,iBAC/BvD,EAAetC,UACRW,EAAcX,OAOD,mBAAdA,EAAMC,MAAsBD,EAAMC,KAAK6F,UAAW,IAEhC,SAA3B9F,EAAMC,KAAKE,aACXiE,IACCA,EAAiBG,cAAcwB,IAAI/F,EAAMC,MACzC,KACK+F,EACL,iGAEMC,EAAYjG,EAAMC,OACxBmE,EAAiBG,cAAc2B,IAAIlG,EAAMC,MAAM,GAC/CsB,QAAQC,KACPwE,oCAAsCjG,EAAekG,IAErD,MAAOtB,GACRpD,QAAQC,KACPwE,EAAI,gEFzLTb,EE8LGnF,EAAMC,KAAK6F,UF7LdV,EE8LGpF,EAAMuC,MF5LT2C,EE8LGnF,EAAeC,GF3LlB4C,OAAOuD,KAAKhB,GAAWiB,QAAQ,SAAAC,OAC1B7B,MAEHA,EAAQW,EAAUkB,GACjBjB,EACAiB,EACAnB,EEoLA,OFlLA,KAtCyB,gDAyCzB,MAAOoB,GACR9B,EAAQ8B,GAEL9B,GAAWA,EAAM+B,WAAW1G,IAC/BA,EAAmB2E,EAAM+B,UAAW,EACpChF,QAAQiD,2BACqBA,EAAM+B,YE8KhCzC,GAAeA,EAAc9D,IAGlCkD,EAAQiB,IAAQ,SAACqC,EAAMC,EAAOxG,OACxBuG,QACE,IAAI5B,MAAM,iDAGbV,GAASA,EAAQsC,EAAMC,EAAOxG,QAG7BuB,EAAO,SAACkF,EAAUC,SAAS,CAChCC,eACCrF,QAAQC,sBAAsBkF,qBAA2BC,IAE1DT,eACC3E,QAAQC,sBAAsBkF,sBAA4BC,MAItDE,EAAuB,CAC5BC,SAAUtF,EAAK,WAAY,kBAC3BuF,WAAYvF,EAAK,aAAc,mBAC/BwB,SAAUxB,EAAK,WAAY,6BAGtBwF,EAAkBpE,OAAOqE,OAAO,GAAIJ,GAE1C3D,EAAQlD,MAAQ,SAAAA,OACTuC,EAAQvC,EAAMuC,SAEJ,OAAfvC,EAAMC,MACG,MAATsC,IACC,aAAcA,GAAS,WAAYA,GACnC,KACK2E,EAAYlH,EAAMuC,MAAQ,OAC3B,IAAI4E,KAAK5E,EAAO,KACd6E,EAAI7E,EAAM4E,GACN,aAANA,EAAkBnH,EAAMoB,SAAWgG,EACxB,WAAND,EAAgBnH,EAAMqH,OAASD,EACnCF,EAASC,GAAKC,GAGrBxE,OAAO0E,eAAetH,EAAOgH,GACzBjD,GAAUA,EAAS/D,IAGxBkD,EAAQG,OAAS,SAAArD,GAQZA,EAAMwF,KACTxF,EAAMwF,IAAUY,QAAQ,SAAAmB,MACnBA,QAAwBhC,IAAfgC,EAAMtH,KAAoB,QAE/BsH,EAAM1C,UACN0C,EAAMC,QACPrB,EAAOvD,OAAOuD,KAAKoB,GAAOE,KAAK,WAC/B,IAAI7C,MACT,0EAA0EuB,WAClExF,EAAcX,WAOpB0H,EAAY1H,EAAMyE,OACpBiD,GAAaA,EAAUC,IAAS,KAC/BC,EAAQF,EAAUC,IAClBjC,MAAMC,QAAQiC,EAAMC,KACvBD,EAAMC,GAAMzB,QAAQ,SAAA0B,MACfA,EAAKC,OAAcD,EAAKE,MAAUtC,MAAMC,QAAQmC,EAAKE,MAAS,KAC7D9C,EAAgBnF,EAAeC,GACnCuB,QAAQC,KACP,MAAM0D,+JAEEvE,EAAcX,UAOvBoD,GAAWA,EAAUpD,GAEF,MAAnBA,EAAMwF,YACHW,EAAO,GACJgB,EAAI,EAAGA,EAAInH,EAAMwF,IAAUhF,OAAQ2G,IAAK,KAC1CI,EAAQvH,EAAMwF,IAAU2B,MACzBI,GAAsB,MAAbA,EAAM1B,SAEdA,EAAM0B,EAAM1B,QACS,IAAvBM,EAAK8B,QAAQpC,GAAa,CAC7BtE,QAAQiD,MACP,8EACyBqB,qFAExBvD,EAAetC,UACRW,EAAcX,UAOxBmG,EAAKpF,KAAK8E"}