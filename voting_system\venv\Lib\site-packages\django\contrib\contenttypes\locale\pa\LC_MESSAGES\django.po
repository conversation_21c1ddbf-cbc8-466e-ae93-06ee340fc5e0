# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: <PERSON><PERSON> <jann<PERSON>@leidel.info>\n"
"Language-Team: Panjabi (Punjabi) (http://www.transifex.com/django/django/"
"language/pa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: pa\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Content Types"
msgstr ""

msgid "python model class name"
msgstr "ਪਾਈਥਨ ਮਾਡਲ ਕਲਾਸ ਨਾਂ"

msgid "content type"
msgstr "ਸਮੱਗਰੀ ਕਿਸਮ"

msgid "content types"
msgstr "ਸਮੱਗਰੀ ਕਿਸਮ"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr ""

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn't exist"
msgstr ""

#, python-format
msgid "%(ct_name)s objects don't have a get_absolute_url() method"
msgstr ""
