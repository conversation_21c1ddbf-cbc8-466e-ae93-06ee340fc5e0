# Changelog
All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](http://keepachangelog.com/en/1.0.0/) 
(modification: no type change headlines) and this project adheres to 
[Semantic Versioning](http://semver.org/spec/v2.0.0.html).


## [0.2.0] - 2017-10-11
- ``Metro-Byzantium`` compatible
- Block reward reduction
- Added gas costs for curve operation precompiles (``ecAddGas``, ``ecMulGas``,...)
- Added ``modexpGquaddivisor`` (modexp precompile)

[0.2.0]: https://github.com/ethereumjs/common/compare/v0.1.0...v0.2.0

## [0.1.0] - 2017-07-27
- ``Spurious Dragon`` compatible
- ``EXP`` cost increase

[0.1.0]: https://github.com/ethereumjs/common/compare/v0.0.18...v0.1.0

## [0.0.18] - 2016-10-25
- ``EIP 150`` compatible
- Added ``homesteadRepriceForkNumber``
- URL fixes in ``package.json``

[0.0.18]: https://github.com/ethereumjs/common/compare/v0.0.17...v0.0.18


## Older releases:

- [0.0.17](https://github.com/ethereumjs/common/compare/v0.0.16...v0.0.17) - 2016-03-05
- [0.0.16](https://github.com/ethereumjs/common/compare/v0.0.15...v0.0.16) - 2016-01-14
- [0.0.15](https://github.com/ethereumjs/common/compare/v0.0.14...v0.0.15) - 2016-01-12
- [0.0.14](https://github.com/ethereumjs/common/compare/v0.0.13...v0.0.14) - 2016-01-09
- [0.0.13](https://github.com/ethereumjs/common/compare/v0.0.12...v0.0.13) - 2015-12-17
- [0.0.12](https://github.com/ethereumjs/common/compare/v0.0.11...v0.0.12) - 2015-12-12
- 0.0.11 - 2015-12-12



