{"version": 3, "file": "template-content.js", "sourceRoot": "", "sources": ["../../src/directives/template-content.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAC,QAAQ,EAAC,MAAM,gBAAgB,CAAC;AACxC,OAAO,EAAC,SAAS,EAAE,SAAS,EAAY,QAAQ,EAAC,MAAM,iBAAiB,CAAC;AAEzE,MAAM,wBAAyB,SAAQ,SAAS;IAG9C,YAAY,QAAkB;QAC5B,KAAK,CAAC,QAAQ,CAAC,CAAC;QAChB,IAAI,QAAQ,CAAC,IAAI,KAAK,QAAQ,CAAC,KAAK,EAAE;YACpC,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;SACvE;IACH,CAAC;IAED,MAAM,CAAC,QAA6B;QAClC,IAAI,IAAI,CAAC,iBAAiB,KAAK,QAAQ,EAAE;YACvC,OAAO,QAAQ,CAAC;SACjB;QACD,IAAI,CAAC,iBAAiB,GAAG,QAAQ,CAAC;QAClC,OAAO,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IACrD,CAAC;CACF;AAED;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,eAAe,GAAG,SAAS,CAAC,wBAAwB,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {noChange} from '../lit-html.js';\nimport {directive, Directive, PartInfo, PartType} from '../directive.js';\n\nclass TemplateContentDirective extends Directive {\n  private _previousTemplate?: HTMLTemplateElement;\n\n  constructor(partInfo: PartInfo) {\n    super(partInfo);\n    if (partInfo.type !== PartType.CHILD) {\n      throw new Error('templateContent can only be used in child bindings');\n    }\n  }\n\n  render(template: HTMLTemplateElement) {\n    if (this._previousTemplate === template) {\n      return noChange;\n    }\n    this._previousTemplate = template;\n    return document.importNode(template.content, true);\n  }\n}\n\n/**\n * Renders the content of a template element as HTML.\n *\n * Note, the template should be developer controlled and not user controlled.\n * Rendering a user-controlled template with this directive\n * could lead to cross-site-scripting vulnerabilities.\n */\nexport const templateContent = directive(TemplateContentDirective);\n\n/**\n * The type of the class that powers this directive. Necessary for naming the\n * directive's return type.\n */\nexport type {TemplateContentDirective};\n"]}