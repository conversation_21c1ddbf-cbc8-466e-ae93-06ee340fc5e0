"use strict";

function _typeof(obj) { "@babel/helpers - typeof"; if (typeof Symbol === "function" && typeof Symbol.iterator === "symbol") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }; } return _typeof(obj); }

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }

function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }

function _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); if (superClass) _setPrototypeOf(subClass, superClass); }

function _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }

function _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }

function _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === "object" || typeof call === "function")) { return call; } return _assertThisInitialized(self); }

function _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); } return self; }

function _isNativeReflectConstruct() { if (typeof Reflect === "undefined" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === "function") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }

function _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }

// Generated by CoffeeScript 2.5.1
var Bullet, _Declaration;

_Declaration = require('./_Declaration');

module.exports = Bullet = function () {
  var self;

  var Bullet = /*#__PURE__*/function (_Declaration2) {
    _inherits(Bullet, _Declaration2);

    var _super = _createSuper(Bullet);

    function Bullet() {
      _classCallCheck(this, Bullet);

      return _super.apply(this, arguments);
    }

    _createClass(Bullet, [{
      key: "_set",
      value: function _set(val) {
        var alignment, bg, char, color, enabled, m, original;
        val = String(val);
        original = val;
        char = null;
        enabled = false;
        color = 'none';
        bg = 'none';

        if (m = val.match(/\"([^"]+)\"/) || (m = val.match(/\'([^']+)\'/))) {
          char = m[1];
          val = val.replace(m[0], '');
          enabled = true;
        }

        if (m = val.match(/(none|left|right|center)/)) {
          alignment = m[1];
          val = val.replace(m[0], '');
        } else {
          alignment = 'left';
        }

        if (alignment === 'none') {
          enabled = false;
        }

        if (m = val.match(/color\:([\w\-]+)/)) {
          color = m[1];
          val = val.replace(m[0], '');
        }

        if (m = val.match(/bg\:([\w\-]+)/)) {
          bg = m[1];
          val = val.replace(m[0], '');
        }

        if (val.trim() !== '') {
          throw Error("Unrecognizable value `".concat(original, "` for `").concat(this.prop, "`"));
        }

        return this.val = {
          enabled: enabled,
          char: char,
          alignment: alignment,
          background: bg,
          color: color
        };
      }
    }]);

    return Bullet;
  }(_Declaration);

  ;
  self = Bullet;
  return Bullet;
}.call(void 0);