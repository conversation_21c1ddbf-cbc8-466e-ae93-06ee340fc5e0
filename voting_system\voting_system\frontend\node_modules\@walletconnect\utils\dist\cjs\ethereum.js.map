{"version": 3, "file": "ethereum.js", "sourceRoot": "", "sources": ["../../src/ethereum.ts"], "names": [], "mappings": ";;;AAAA,qCAAqC;AACrC,sDAAwE;AAGxE,yCAAuF;AACvF,iCAA4D;AAC5D,6CAAwE;AAExE,SAAgB,iBAAiB,CAAC,OAAe;IAC/C,OAAO,GAAG,IAAA,0BAAe,EAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;IACjD,MAAM,IAAI,GAAG,IAAA,0BAAe,EAAC,IAAA,oBAAU,EAAC,IAAA,8BAAmB,EAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IACvE,IAAI,QAAQ,GAAG,EAAE,CAAC;IAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACvC,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE;YAC7B,QAAQ,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;SACtC;aAAM;YACL,QAAQ,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;SACxB;KACF;IACD,OAAO,IAAA,uBAAY,EAAC,QAAQ,CAAC,CAAC;AAChC,CAAC;AAZD,8CAYC;AAEM,MAAM,cAAc,GAAG,CAAC,OAAgB,EAAE,EAAE;IACjD,IAAI,CAAC,OAAO,EAAE;QACZ,OAAO,KAAK,CAAC;KACd;SAAM,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI,EAAE;QACzD,OAAO,KAAK,CAAC;KACd;SAAM,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;QAChD,OAAO,KAAK,CAAC;KACd;SAAM,IAAI,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;QACrF,OAAO,IAAI,CAAC;KACb;SAAM;QACL,OAAO,OAAO,KAAK,iBAAiB,CAAC,OAAO,CAAC,CAAC;KAC/C;AACH,CAAC,CAAC;AAZW,QAAA,cAAc,kBAYzB;AAEF,SAAgB,iBAAiB,CAAC,MAAgB;IAChD,IAAI,CAAC,IAAA,yBAAY,EAAC,MAAM,CAAC,IAAI,CAAC,IAAA,wBAAW,EAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;QACpD,MAAM,CAAC,CAAC,CAAC,GAAG,IAAA,2BAAgB,EAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;KACzC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AALD,8CAKC;AAED,SAAgB,oBAAoB,CAAC,MAAwB;IAC3D,IAAI,OAAO,MAAM,CAAC,IAAI,KAAK,WAAW,IAAI,MAAM,CAAC,IAAI,KAAK,GAAG;QAAE,OAAO,MAAM,CAAC;IAE7E,IAAI,OAAO,MAAM,CAAC,IAAI,KAAK,WAAW,IAAI,CAAC,IAAA,sBAAc,EAAC,MAAM,CAAC,IAAI,CAAC,EAAE;QACtE,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;KAC1E;IAED,SAAS,cAAc,CAAC,KAAsB;QAC5C,IAAI,MAAM,GAAG,KAAK,CAAC;QACnB,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,IAAA,0BAAa,EAAC,KAAK,CAAC,CAAC,EAAE;YACrF,IAAI,CAAC,IAAA,wBAAW,EAAC,KAAK,CAAC,EAAE;gBACvB,MAAM,GAAG,IAAA,6BAAkB,EAAC,KAAK,CAAC,CAAC;aACpC;iBAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;gBACpC,MAAM,GAAG,IAAA,kBAAW,EAAC,KAAK,CAAC,CAAC;aAC7B;SACF;QACD,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;YAC9B,MAAM,GAAG,IAAA,4BAAqB,EAAC,MAAM,CAAC,CAAC;SACxC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,SAAS,GAAG;QAChB,IAAI,EAAE,IAAA,kBAAW,EAAC,MAAM,CAAC,IAAI,CAAC;QAC9B,EAAE,EAAE,OAAO,MAAM,CAAC,EAAE,KAAK,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAA,kBAAW,EAAC,MAAM,CAAC,EAAE,CAAC;QACzE,QAAQ,EAAE,OAAO,MAAM,CAAC,QAAQ,KAAK,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC;QACvF,GAAG,EACD,OAAO,MAAM,CAAC,GAAG,KAAK,WAAW;YAC/B,CAAC,CAAC,OAAO,MAAM,CAAC,QAAQ,KAAK,WAAW;gBACtC,CAAC,CAAC,EAAE;gBACJ,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC;YACnC,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC;QAChC,KAAK,EAAE,OAAO,MAAM,CAAC,KAAK,KAAK,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC;QAC9E,KAAK,EAAE,OAAO,MAAM,CAAC,KAAK,KAAK,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC;QAC9E,IAAI,EAAE,OAAO,MAAM,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAA,kBAAW,EAAC,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI;KACjF,CAAC;IAEF,MAAM,QAAQ,GAAG,CAAC,UAAU,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACvD,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,GAAW,EAAE,EAAE;QAC7C,IACE,CAAC,OAAO,SAAS,CAAC,GAAG,CAAC,KAAK,WAAW;YACpC,CAAC,OAAO,SAAS,CAAC,GAAG,CAAC,KAAK,QAAQ,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC,CAAC;YACxE,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,EACtB;YACA,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC;SACvB;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,SAAS,CAAC;AACnB,CAAC;AAjDD,oDAiDC"}