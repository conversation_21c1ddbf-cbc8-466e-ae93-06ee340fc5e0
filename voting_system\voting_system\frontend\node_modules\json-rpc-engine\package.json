{"name": "json-rpc-engine", "version": "6.1.0", "description": "A tool for processing JSON-RPC messages.", "license": "ISC", "author": "kuma<PERSON>", "main": "dist/index.js", "engines": {"node": ">=10.0.0"}, "files": ["dist"], "scripts": {"build": "tsc --project .", "lint": "eslint . --ext ts,js,json", "lint:fix": "eslint . --ext ts,js,json --fix", "test": "mocha ./test", "coverage": "nyc --check-coverage yarn test", "prepublishOnly": "yarn && yarn lint && yarn build && yarn coverage"}, "dependencies": {"@metamask/safe-event-emitter": "^2.0.0", "eth-rpc-errors": "^4.0.2"}, "devDependencies": {"@metamask/eslint-config": "^4.1.0", "@types/node": "^14.14.7", "@typescript-eslint/eslint-plugin": "^4.8.0", "@typescript-eslint/parser": "^4.8.0", "eslint": "^7.13.0", "eslint-plugin-import": "^2.20.1", "eslint-plugin-json": "^2.1.0", "eslint-plugin-mocha": "^6.3.0", "eslint-plugin-node": "^11.1.0", "mocha": "^7.1.1", "nyc": "^15.1.0", "sinon": "^9.0.2", "typescript": "^4.0.5"}, "repository": {"type": "git", "url": "git+https://github.com/MetaMask/json-rpc-engine.git"}, "bugs": {"url": "https://github.com/MetaMask/json-rpc-engine/issues"}, "homepage": "https://github.com/MetaMask/json-rpc-engine#readme", "directories": {"test": "test"}, "contributors": ["kumavis <<EMAIL>>", "<PERSON> <<EMAIL>>"]}