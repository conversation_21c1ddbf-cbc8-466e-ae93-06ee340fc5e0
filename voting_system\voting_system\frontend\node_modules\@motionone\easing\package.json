{"name": "@motionone/easing", "version": "10.18.0", "description": "A collection of easing functions.", "license": "MIT", "author": "<PERSON>", "main": "dist/index.cjs.js", "module": "dist/index.es.js", "types": "types/index.d.ts", "sideEffects": false, "scripts": {"build": "rimraf lib dist types && tsc -p . && rollup -c", "test": "jest --coverage --config jest.config.js", "dev": "concurrently -c blue,red -n tsc,rollup --kill-others \"tsc --watch -p . --preserveWatchOutput\" \"rollup --c --watch --no-watch.clearScreen\"", "measure": "bundlesize"}, "dependencies": {"@motionone/utils": "^10.18.0", "tslib": "^2.3.1"}, "bundlesize": [{"path": "./dist/size-index.js", "maxSize": "0.5 kB"}], "gitHead": "f357769434210262a664b8b736b61e1a615e95a7"}