{"name": "ethereumjs-vm", "version": "2.6.0", "description": "An Ethereum VM implementation", "main": "dist/index.js", "files": ["dist"], "scripts": {"coverage": "nyc npm run coverageTests && nyc report --reporter=text-lcov > .nyc_output/lcov.info", "coverageTests": "tape ./tests/api/*.js ./tests/tester.js -s", "coveralls": "npm run coverage && if [ -n \"$COVERALLS_REPO_TOKEN\" ]; then coveralls <.nyc_output/lcov.info; fi", "testVM": "node ./tests/tester -v", "testStateByzantium": "npm run build:dist && node ./tests/tester -s --fork='Byzantium' --dist", "testStateConstantinople": "npm run build:dist && node ./tests/tester -s --fork='Constantinople' --dist", "testStatePetersburg": "npm run build:dist && node ./tests/tester -s --fork='Petersburg' --dist", "testBuildIntegrity": "npm run build:dist && node ./tests/tester -s --dist --test='stackOverflow'", "testBlockchain": "npm run build:dist && node --stack-size=1500 ./tests/tester -b --fork='Constantinople' --dist --excludeDir='GeneralStateTests'", "testBlockchainGeneralStateTests": "npm run build:dist && node --stack-size=1500 ./tests/tester -b --dist --dir='GeneralStateTests'", "testAPI": "tape ./tests/api/*.js", "test": "node ./tests/tester -a", "lint": "standard", "prepublishOnly": "npm run lint && npm run build:dist && npm run testBuildIntegrity", "build:dist": "babel lib/ -d dist/", "build:docs": "documentation build ./lib/index.js ./lib/runBlockchain.js ./lib/runBlock.js ./lib/runTx.js ./lib/runCode.js ./lib/runCall.js --format md --shallow > ./docs/index.md", "formatTest": "node ./scripts/formatTest"}, "repository": {"type": "git", "url": "git+https://github.com/ethereumjs/ethereumjs-vm.git"}, "keywords": ["ethereum", "VM"], "dependencies": {"async": "^2.1.2", "async-eventemitter": "^0.2.2", "ethereumjs-account": "^2.0.3", "ethereumjs-block": "~2.2.0", "ethereumjs-common": "^1.1.0", "ethereumjs-util": "^6.0.0", "fake-merkle-patricia-tree": "^1.0.1", "functional-red-black-tree": "^1.0.1", "merkle-patricia-tree": "^2.3.2", "rustbn.js": "~0.2.0", "safe-buffer": "^5.1.1"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-preset-env": "^1.6.1", "coveralls": "^3.0.0", "documentation": "^8.1.2", "ethereumjs-blockchain": "^3.4.0", "ethereumjs-testing": "git+https://github.com/ethereumjs/ethereumjs-testing.git#v1.2.7", "ethereumjs-tx": "1.3.7", "level": "^4.0.0", "level-mem": "^3.0.1", "minimist": "^1.1.1", "nyc": "^12.0.2", "standard": "^10.0.0", "tap-spec": "^5.0.0", "tape": "4.6.3"}, "author": "mjbecze <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>"], "license": "MPL-2.0", "bugs": {"url": "https://github.com/ethereumjs/ethereumjs-vm/issues"}, "homepage": "https://github.com/ethereumjs/ethereumjs-vm", "nyc": {"exclude": ["tests"]}, "standard": {"ignore": ["dist/**", "examples/runcode-browserify.js"]}}