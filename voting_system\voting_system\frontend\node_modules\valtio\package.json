{"name": "valtio", "private": false, "version": "1.10.5", "description": "💊 Val<PERSON><PERSON> makes proxy-state simple for React and Vanilla", "main": "./index.js", "types": "./index.d.ts", "typesVersions": {"<4.5": {"esm/*": ["ts3.4/*"], "*": ["ts3.4/*"]}}, "exports": {"./package.json": "./package.json", ".": {"types": "./index.d.ts", "import": {"types": "./esm/index.d.mts", "default": "./esm/index.mjs"}, "module": "./esm/index.js", "default": "./index.js"}, "./*": {"types": "./*.d.ts", "import": {"types": "./esm/*.d.mts", "default": "./esm/*.mjs"}, "module": "./esm/*.js", "default": "./*.js"}}, "files": ["**"], "sideEffects": false, "engines": {"node": ">=12.20.0"}, "repository": {"type": "git", "url": "git+https://github.com/pmndrs/valtio.git"}, "keywords": ["react", "state", "manager", "management", "mobx", "proxy", "store"], "author": "<PERSON><PERSON>", "contributors": [], "license": "MIT", "bugs": {"url": "https://github.com/pmndrs/valtio/issues"}, "homepage": "https://github.com/pmndrs/valtio", "dependencies": {"proxy-compare": "2.5.1", "use-sync-external-store": "1.2.0"}, "peerDependencies": {"react": ">=16.8"}, "peerDependenciesMeta": {"react": {"optional": true}}}