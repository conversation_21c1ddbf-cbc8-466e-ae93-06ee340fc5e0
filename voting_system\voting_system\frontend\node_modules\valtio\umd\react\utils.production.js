!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react"),require("valtio/react")):"function"==typeof define&&define.amd?define(["exports","react","valtio/react"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).valtioReactUtils={},e.React,e.valtioReact)}(this,(function(e,t,o){"use strict";e.useProxy=function(e,n){var i=o.useSnapshot(e,n),r=!0;return t.useLayoutEffect((function(){r=!1})),new Proxy(e,{get:function(e,t){return r?i[t]:e[t]}})}}));
