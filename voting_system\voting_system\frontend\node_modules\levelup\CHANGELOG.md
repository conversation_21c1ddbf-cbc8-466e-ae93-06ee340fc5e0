### 1.0.0 @ May 14 2015

 * [[`6ae45d83b6`](https://github.com/level/levelup/commit/6ae45d83b6)] - dispatchError() and readError() are voids (<PERSON><PERSON><PERSON>)
 * [[`c8e22e70bd`](https://github.com/level/levelup/commit/c8e22e70bd)] - util.deprecate static functions (<PERSON>)
 * [[`ef0ba86c99`](https://github.com/level/levelup/commit/ef0ba86c99)] - target multiple iojs versions, remove notifications (<PERSON><PERSON><PERSON>)
 * [[`e19ec96ed7`](https://github.com/level/levelup/commit/e19ec96ed7)] - deprecate .approximateSize() (<PERSON>)
 * [[`9c32ca9fcd`](https://github.com/level/levelup/commit/9c32ca9fcd)] - refactor read streams using level-iterator-stream and level-codec (<PERSON>)
 * [[`8015e088cb`](https://github.com/level/levelup/commit/8015e088cb)] - tap -> tape + faucet (<PERSON><PERSON><PERSON>)
 * [[`af125b580c`](https://github.com/level/levelup/commit/af125b580c)] - fix readStream *AsBuffer options (Julian Gruber)
 * [[`61b44463da`](https://github.com/level/levelup/commit/61b44463da)] - update dependencies (Lars-Magnus Skog)
 * [[`90352e999c`](https://github.com/level/levelup/commit/90352e999c)] - update changelog from 0.18.6 to 0.19.0 (Lars-Magnus Skog)
 * [[`b17e9e775b`](https://github.com/level/levelup/commit/b17e9e775b)] - Fixed valueEncoding bug by passing options without array. (Braydon Fuller)
 * [[`9576842794`](https://github.com/level/levelup/commit/9576842794)] - Added test for valueEncoding "hex" for createReadStream (Braydon Fuller)
 * [[`b9ce2ba2c5`](https://github.com/level/levelup/commit/b9ce2ba2c5)] - s/rvagg\/node-/level\// (Lars-Magnus Skog)
 * [[`7460209eb6`](https://github.com/level/levelup/commit/7460209eb6)] - fix stream-bench.js (Julian Gruber)
 * [[`2a2780c65c`](https://github.com/level/levelup/commit/2a2780c65c)] - refactor ltgt encoding (Julian Gruber)
 * [[`f53e349ec7`](https://github.com/level/levelup/commit/f53e349ec7)] - refactor iterators using new deferred-leveldown (Julian Gruber)
 * [[`e811f7e598`](https://github.com/level/levelup/commit/e811f7e598)] - remove leveled tests (Julian Gruber)
 * [[`b37cf16445`](https://github.com/level/levelup/commit/b37cf16445)] - fix benchmarks by installing leveldown@^0.10.4 (Julian Gruber)
 * [[`187711c96c`](https://github.com/level/levelup/commit/187711c96c)] - use level-codec (Julian Gruber)
 * [[`a1fda6bf2f`](https://github.com/level/levelup/commit/a1fda6bf2f)] - extract error codes into level-errors module (Lars-Magnus Skog)
 * [[`f4e5a44530`](https://github.com/level/levelup/commit/f4e5a44530)] - remove reference to write-stream and iterators (Lars-Magnus Skog)
 * [[`7372fceb4a`](https://github.com/level/levelup/commit/7372fceb4a)] - Changed options for get to same as put (Richard Littauer)
 * [[`7686899b6c`](https://github.com/level/levelup/commit/7686899b6c)] - add node 0.12 and iojs on travis (Lars-Magnus Skog)
 * [[`4aa6e8b7a5`](https://github.com/level/levelup/commit/4aa6e8b7a5)] - remove encoding option (Julian Gruber)
 * [[`b0247a436e`](https://github.com/level/levelup/commit/b0247a436e)] - Added errors to the available namespace when requiring 'levelup'. (Braydon Fuller)
 * [[`8b8da57f7a`](https://github.com/level/levelup/commit/8b8da57f7a)] - clean up old level-ws reference (Lars-Magnus Skog)
 * [[`3d26d39a43`](https://github.com/level/levelup/commit/3d26d39a43)] - update README with info on why WriteStream was removed (Lars-Magnus Skog)
 * [[`ba51315047`](https://github.com/level/levelup/commit/ba51315047)] - add Jarrett Cruger as contributor (Lars-Magnus Skog)
 * [[`ed89907f33`](https://github.com/level/levelup/commit/ed89907f33)] - remove unused dependencies (Lars-Magnus Skog)
 * [[`6067bb4467`](https://github.com/level/levelup/commit/6067bb4467)] - \[doc\] beginning of readme adjustment (Jarrett Cruger)
 * [[`78a06b3c10`](https://github.com/level/levelup/commit/78a06b3c10)] - \[fix\] update travis and package.json scripts (Jarrett Cruger)
 * [[`fcdd49b039`](https://github.com/level/levelup/commit/fcdd49b039)] - \[rm test\] remove fstream based tests (Jarrett Cruger)
 * [[`fb73bdecc0`](https://github.com/level/levelup/commit/fb73bdecc0)] - \[fix\] remove `copy` as it requires write-stream (Jarrett Cruger)
 * [[`616da299f9`](https://github.com/level/levelup/commit/616da299f9)] - \[fix test\] remove references to write-stream in tests (Jarrett Cruger)
 * [[`a712e623a7`](https://github.com/level/levelup/commit/a712e623a7)] - \[fix\] remove references to write-stream (Jarrett Cruger)
 * [[`9e6a6b7ef4`](https://github.com/level/levelup/commit/9e6a6b7ef4)] - update logo and copyright (Lars-Magnus Skog)
 * [[`8b339def43`](https://github.com/level/levelup/commit/8b339def43)] - check notFound on err (Brian Woodward)
 * [[`36658a2c7d`](https://github.com/level/levelup/commit/36658a2c7d)] - support values to be null/undefined (David Björklund)
 * [[`f0bc944005`](https://github.com/level/levelup/commit/f0bc944005)] - explicit devdep versions (Rod Vagg)
 * [[`c951f094eb`](https://github.com/level/levelup/commit/c951f094eb)] - Use newer memdown store. Fixes build. (Eduardo Sorribas)
 * [[`c2c12c9380`](https://github.com/level/levelup/commit/c2c12c9380)] - better document #del method (Ben West)
 * [[`2410aa3aff`](https://github.com/level/levelup/commit/2410aa3aff)] - resolve #261, explain args to callback to #del (Ben West)
 * [[`ef28adbe71`](https://github.com/level/levelup/commit/ef28adbe71)] - explicit devdep versions (Rod Vagg)
 * [[`723391bb93`](https://github.com/level/levelup/commit/723391bb93)] - Use newer memdown store. Fixes build. (Eduardo Sorribas)
 * [[`23b3f7be19`](https://github.com/level/levelup/commit/23b3f7be19)] - better document #del method (Ben West)
 * [[`02bb5c3856`](https://github.com/level/levelup/commit/02bb5c3856)] - resolve #261, explain args to callback to #del (Ben West)
 * [[`e77bbd4c7d`](https://github.com/level/levelup/commit/e77bbd4c7d)] - support values to be null/undefined (David Björklund)
 * [[`b9117a001b`](https://github.com/level/levelup/commit/b9117a001b)] - fixes the leveldb link (Manuel Ernst)
 * [[`3d541dbfc9`](https://github.com/level/levelup/commit/3d541dbfc9)] - remove 0.8 from travis (Rod Vagg)

### 0.19.0 @ Aug 26 2014

 * [[`7f14058440`](https://github.com/level/levelup/commit/7f14058440)] - minor whitespace changes (Rod Vagg)
 * [[`9e6d335df2`](https://github.com/level/levelup/commit/9e6d335df2)] - fix license (Rod Vagg)
 * [[`74caa18c09`](https://github.com/level/levelup/commit/74caa18c09)] - minor style fixes (Rod Vagg)
 * [[`1c7bc43d01`](https://github.com/level/levelup/commit/1c7bc43d01)] - update nodeico badge (Rod Vagg)
 * [[`b6357cc323`](https://github.com/level/levelup/commit/b6357cc323)] - Use highest/lowest instead of largest/smallest (Arnout Engelen)
 * [[`e45ce4e9d5`](https://github.com/level/levelup/commit/e45ce4e9d5)] - Document what 'limit' does in 'reverse' mode (Arnout Engelen)
 * [[`9004e9db69`](https://github.com/level/levelup/commit/9004e9db69)] - Discourage the use of start/end a bit (Arnout Engelen)
 * [[`6178d69cdc`](https://github.com/level/levelup/commit/6178d69cdc)] - merge (Dominic Tarr)
 * [[`e3ab0ebe03`](https://github.com/level/levelup/commit/e3ab0ebe03)] - document with comments (Dominic Tarr)
 * [[`bb88572c15`](https://github.com/level/levelup/commit/bb88572c15)] - unmention bops (Dominic Tarr)
 * [[`45df8e668e`](https://github.com/level/levelup/commit/45df8e668e)] - clarify ltgt (Dominic Tarr)
 * [[`54eba03305`](https://github.com/level/levelup/commit/54eba03305)] - binary encoding in the browser (Calvin Metcalf)

### 0.18.6 @ Jul 26 2014

 * (lots of stuff since 0.18.1 not recorded in changelog, ooops)
 * Extracted encoding / codec to separate modules (@dominictarr)

### 0.18.1 @ Nov 20 2013

 * Make chained-batch obey global LevelUP object options (@mcavage)

### 0.18.0 @ Nov 18 2013

 * Upgrade to LevelDOWN@0.10.0 (and bops@0.1.0 and readable-stream@1.1.9) (@rvagg)

### 0.17.0 @ Oct 01 2013

 * Undo factory pattern, use plain prototypal object and expose full prototype (@rvagg)
 * Move Batch object to batch.js and expose (@rvagg)
 * Use new package, DeferredLevelDOWN to handle all deferred open logic (@rvagg)
 * Code cleanup, update deps (xtend) (@rvagg, @juliangruber)

### 0.16.0 @ Sep 10 2013

 * Added `notFound` boolean property and `status=404` property to NotFoundError (@rvagg)
 * Upgrade to errno@0.1.0 which aliases .type and .name properties (@rvagg)
 * ReadStream gracefully handles multiple destroy() calls (@mcollina)

### 0.15.0 @ Aug 25 2013

 * New ReadStream: upgrade to streams2, remove all state-management cruft, remove fstream support (@substack)
 * Update LevelDOWN dependency to ~0.8.0 with Iterator lt/lte/gt/gte support and NAN as a dependency
 * Added @substack as contributor

### 0.14.0 @ Aug 19 2013

 * Encodings overhaul, allow custom encoders/decoders for `keyEncoding` or `valueEncoding` (@dominictarr)

### 0.13.0 @ Aug 11 2013

 * Update LevelDOWN dependency version ~0.7.0 for Node 0.8->0.11 compatibility

### 0.12.0 @ Jul 25 2013

  * Update LevelDOWN dependency version ~0.6.2

### 0.11.0 @ Jul 17 2013

  * Remove all Function#bind calls for better browser compatibility (@juliangruber)
  * Switch from direct Buffer access to bops for better browser compatibility (@juliangruber)
  * WriteStream#end accepts `data` argument (@pgte)
  * Added @pgte as contributor

### 0.10.0 @ Jun 14 2013

  * Upgrade to LevelDOWN@0.6.0 which upgrades to LevelDB@1.11.0, some important bugfixes: https://groups.google.com/forum/#!topic/leveldb/vS1JvmGlp4E

### 0.9.0 @ 21 May 2013

  * Use LevelDOWN@0.5.0, see https://github.com/level/leveldown/blob/master/CHANGELOG.md for details
  * Race-condition(ish) fixed in ReadStream--createReadStream() does not start immediately and therefore allowed put()s to happen before the stream starts (@dominictarr)
  * ReadStream doesn't emit "ready" event (@dominictarr)
  * Allow separate encodings per operation in db.batch() (@juliangruber)
  * Allow separate encodings per write() in WriteStream (@juliangruber)
  * WriteStream supports "type" option ("put" [default] or "del") on constructor and individual write()s (@mcollina)
  * Remove "leveldown" from dependencies (see http://r.va.gg/2013/05/levelup-v0.9-some-major-changes.html) (@rvagg)
  * Expose LevelDOWN (or LevelDOWN substitute) as `db` property on LevelUP instance (e.g. db.db.approximateSize())
  * Chained batch exposed from LevelDOWN, invoked with argument-less db.batch() (@juliangruber, @rvagg)
  * Significantly improve ReadStream performance by replacing .bind() and .apply() (@mcollina, @kesla)
  * Better Browserify support (@rvagg, @juliangruber, @maxogden, etc.)
  * Deprecate secondary LevelDB-specific operations on LevelUP, prefer direct LevelDOWN access (approximateSize(), repair(), destroy(), getProperty()--new in LevelDOWN@0.5.0) (@rvagg)

### 0.8.0 @ 17 Apr 2013

  * More comprehensive argument checking, will now report back directly or throw if there is a problem rather than on nextTick (@rvagg)
  * Expose `.options` property on LevelUP instances. (@rvagg)
  * Further clarify 'encoding' -> 'valueEncoding' shift. db.options.valueEncoding is now authoritative even if user used 'encoding' on initialisation. (@rvagg)
  * 'level' package now published to npm that bundles LevelUP & LevelDOWN and exposes LevelUP directly; for planned shift to detaching LevelDOWN as a direct-dependency of LevelUP. (@rvagg)

### 0.7.0 @ 8 Apr 2013

  * Windows support in LevelDOWN @0.2.0 (@rvagg)
  * added 'db' option on constructor to replace LevelDOWN (@rvagg)
  * added repair() & destroy() aliases for LevelDOWN implementations (@rvagg)
  * fix early 'close' emit in WriteStream (@rvagg)
  * improved ReadStream reverse=true start key handling (@kesla)
  * ReadStream empty start & end keys ignored rather than segfault (@kesla)
  * 'encoding' option now an alias for 'valueEncoding' only, 'keyEncoding' defaults to 'utf8' and must be changed explicitly (@rvagg)

### 0.6.2 @ 4 Mar 2013

  * use `xtend` package instead of internal util._extend @ralphtheninja
  * internal cleanup of `callback` argument detection @ralphtheninja
  * move deferred-open-operations into an internal `this._db` wrapper rather than make them call public .get()/.put() etc. for a second time @dominictarr

### 0.6.1 @ 1 Mar 2013

  * internal code cleanup & refactoring @ralphtheninja
  * fix multiple iterator.end() calls in ReadStreams throwing errors (destroy() called while read/next is in progress) #82 #83 #84 @rvagg

### 0.6.0 @ Feb 25 2013

  * complete transition to LevelDOWN for the LevelDB binding. No native code left in LevelUP @rvagg
    - LevelDOWN now keeps its own ChangeLog at: https://github.com/level/leveldown/blob/master/CHANGELOG.md
    - LevelDB@1.9.0 and Snappy@1.1.0 are included in LevelDOWN@0.1.2
  * simplify callback signature (remove extra, undocumented properties from some callbacks) @rvagg / @dominictarr

### 0.5.4 @ Feb 16 2013

  * explicit namespaces in C++ @rvagg
  * memory leak, Persistent<Function> callback not Dispose()d for `readStream()` @rvagg
  * allow one next() at a time, improve end() handling @rvagg
  * ensure iterator end & next don't conflict @rvagg
  * remove CloseError @ralphtheninja
  * fix put/batch bug in WriteStream#_process() @ralphtheninja
  * remove `useBatch` in `copy()` @rvagg
  * move encodingOpts levelup.js -> util.js @ralphtheninja

### 0.5.3-1 @ Feb 5 2013

  * non-shrinkwrapped release @rvagg

### 0.5.3 @ Jan 28 2013

  * `location` exposed as read-only property on db object @rvagg
  * swap bufferstream dependency for simple-bufferstream, remove unnecessary compile @rvagg
  * comment out all sqlite3 benchmarks @ralphtheninja
  * put LevelUP() into closure @ralphtheninja

### 0.5.2 @ Jan 24 2013

  * fix: incorrect scope in approximateSize function @sandfox

### 0.5.1 @ Jan 10 2013

  * change `createIfMissing` option default to `true` @rvagg
  * use util._extend instead of local variant @rvagg
  * adjust copyright & contributors @rvagg
  * idempotent open and close, and emit _state as events @dominictarr
  * fix: check that UINT32_OPTION_VALUE is a Uint32 @kesla
  * feature: Support setting size of LRU-cache @kesla
  * use util.inherits() from node core @ralphtheninja

### 0.4.4 @ Jan 1 2013

  * set maxListeners to Infinity to prevent warnings when using deferred open @juliangruber

### 0.4.3 @ Dec 31 2012

  * added @kesla to contributors list @rvagg
  * feature: added approximateSize() @kesla

### 0.4.2 @ Dec 30 2012

  * process.nextTick->setImmediate with polyfill Node 0.9.5 compat @rvagg
  * added @ralphtheninja to contributors list @rvagg

### 0.4.1 @ Dec 20 2013

  * remove `useBatch` option on `writeStream()` @rvagg

### 0.4.0 @ Dec 18 2013

  * remove old, unused util functions @rvagg
  * speed up batch() & allow non-Strings to C++ @rvagg
  * fix batch() benchmarks @rvagg
  * improved compression test @rvagg
  * added SQLite3 to test suite @rvagg
  * remove compile warnings on osx @rvagg
  * return Strings not Buffers from C++ when possible @rvagg
  * optimised encoders & decoders @rvagg
  * added basic get() benchmarks @rvagg
  * revamped benchmark suite @rvagg
  * allow JS Strings through to native layer @rvagg
  * cleaner build for osx @rvagg
  * remove compile warnings for solaris @rvagg
  * LevelDB 1.7 @rvagg
  * added `compress` boolean on open() @rvagg

### 0.3.x and prior

  * stuff
