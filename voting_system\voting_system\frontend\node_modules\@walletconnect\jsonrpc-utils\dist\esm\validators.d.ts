import { JsonRpcError, <PERSON>sonRpcPayload, JsonRpcRequest, JsonRpcResponse, JsonRpcResult, JsonRpcValidation, JsonRpcValidationInvalid } from "./types";
export declare function isJsonRpcPayload(payload: any): payload is JsonRpcPayload;
export declare function isJsonRpcRequest<T = any>(payload: JsonRpcPayload): payload is JsonRpcRequest<T>;
export declare function isJsonRpcResponse<T = any>(payload: JsonRpcPayload): payload is JsonRpcResponse<T>;
export declare function isJsonRpcResult<T = any>(payload: JsonRpcPayload): payload is JsonRpcResult<T>;
export declare function isJsonRpcError(payload: JsonRpcPayload): payload is JsonRpcError;
export declare function isJsonRpcValidationInvalid(validation: JsonRpcValidation): validation is JsonRpcValidationInvalid;
//# sourceMappingURL=validators.d.ts.map