class t{setAnimation(t){this.animation=t,null==t||t.finished.then((()=>this.clearAnimation())).catch((()=>{}))}clearAnimation(){this.animation=this.generator=void 0}}const e=new WeakMap;function n(t){return e.has(t)||e.set(t,{transforms:[],values:new Map}),e.get(t)}const a={duration:.3,delay:0,endDelay:0,repeat:0,easing:"ease"},i=t=>"number"==typeof t,r=t=>Array.isArray(t)&&!i(t[0]),s=()=>{},o=t=>t,l=t=>"object"==typeof t&&Boolean(t.createAnimation),c=t=>"function"==typeof t,u=t=>"string"==typeof t,f=t=>1e3*t,y=["","X","Y","Z"],d={x:"translateX",y:"translateY",z:"translateZ"},p={syntax:"<angle>",initialValue:"0deg",toDefaultUnit:t=>t+"deg"},h={translate:{syntax:"<length-percentage>",initialValue:"0px",toDefaultUnit:t=>t+"px"},rotate:p,scale:{syntax:"<number>",initialValue:1,toDefaultUnit:o},skew:p},g=new Map,m=t=>`--motion-${t}`,v=["x","y","z"];["translate","scale","rotate","skew"].forEach((t=>{y.forEach((e=>{v.push(t+e),g.set(m(t+e),h[t])}))}));const w=(t,e)=>v.indexOf(t)-v.indexOf(e),O=new Set(v),b=t=>O.has(t),x=t=>t.sort(w).reduce(A,"").trim(),A=(t,e)=>`${t} ${e}(var(${m(e)}))`,D=t=>t.startsWith("--"),S=new Set;const E=(t,e)=>document.createElement("div").animate(t,e),P={cssRegisterProperty:()=>"undefined"!=typeof CSS&&Object.hasOwnProperty.call(CSS,"registerProperty"),waapi:()=>Object.hasOwnProperty.call(Element.prototype,"animate"),partialKeyframes:()=>{try{E({opacity:[1]})}catch(t){return!1}return!0},finished:()=>Boolean(E({opacity:[0,1]},{duration:.001}).finished),linearEasing:()=>{try{E({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0}},V={},$={};for(const t in P)$[t]=()=>(void 0===V[t]&&(V[t]=P[t]()),V[t]);const k=(t,e)=>c(t)?$.linearEasing()?`linear(${((t,e)=>{let n="";const a=Math.round(e/.015);for(let e=0;e<a;e++)n+=t((s=e,(r=a-1)-(i=0)==0?1:(s-i)/(r-i)))+", ";var i,r,s;return n.substring(0,n.length-2)})(t,e)})`:a.easing:(t=>Array.isArray(t)&&i(t[0]))(t)?U(t):t,U=([t,e,n,a])=>`cubic-bezier(${t}, ${e}, ${n}, ${a})`;function j(t){return d[t]&&(t=d[t]),b(t)?m(t):t}const C=(t,e)=>{e=j(e);let n=D(e)?t.style.getPropertyValue(e):getComputedStyle(t)[e];if(!n&&0!==n){const t=g.get(e);t&&(n=t.initialValue)}return n},M=(t,e,n)=>{e=j(e),D(e)?t.style.setProperty(e,n):t.style[e]=n};function R(e,y,p,h={},m){const v=window.__MOTION_DEV_TOOLS_RECORD,w=!1!==h.record&&v;let O,{duration:A=a.duration,delay:E=a.delay,endDelay:P=a.endDelay,repeat:V=a.repeat,easing:U=a.easing,persist:R=!1,direction:_,offset:z,allowWebkitAcceleration:W=!1,autoplay:B=!0}=h;const F=n(e),K=b(y);let T=$.waapi();K&&((t,e)=>{d[e]&&(e=d[e]);const{transforms:a}=n(t);var i,r;r=e,-1===(i=a).indexOf(r)&&i.push(r),t.style.transform=x(a)})(e,y);const X=j(y),Y=function(e,n){return e.has(n)||e.set(n,new t),e.get(n)}(F.values,X),Z=g.get(X);return function(t,e=!0){if(t&&"finished"!==t.playState)try{t.stop?t.stop():(e&&t.commitStyles(),t.cancel())}catch(t){}}(Y.animation,!(l(U)&&Y.generator)&&!1!==h.record),()=>{const t=()=>{var t,n;return null!==(n=null!==(t=C(e,X))&&void 0!==t?t:null==Z?void 0:Z.initialValue)&&void 0!==n?n:0};let n=function(t,e){for(let n=0;n<t.length;n++)null===t[n]&&(t[n]=n?t[n-1]:e());return t}((t=>Array.isArray(t)?t:[t])(p),t);const a=function(t,e){var n;let a=(null==e?void 0:e.toDefaultUnit)||o;const i=t[t.length-1];if(u(i)){const t=(null===(n=i.match(/(-?[\d.]+)([a-z%]*)/))||void 0===n?void 0:n[2])||"";t&&(a=e=>e+t)}return a}(n,Z);if(l(U)){const e=U.createAnimation(n,"opacity"!==y,t,X,Y);U=e.easing,n=e.keyframes||n,A=e.duration||A}if(D(X)&&($.cssRegisterProperty()?function(t){if(!S.has(t)){S.add(t);try{const{syntax:e,initialValue:n}=g.has(t)?g.get(t):{};CSS.registerProperty({name:t,inherits:!1,syntax:e,initialValue:n})}catch(t){}}}(X):T=!1),K&&!$.linearEasing()&&(c(U)||r(U)&&U.some(c))&&(T=!1),T){Z&&(n=n.map((t=>i(t)?Z.toDefaultUnit(t):t))),1!==n.length||$.partialKeyframes()&&!w||n.unshift(t());const a={delay:f(E),duration:f(A),endDelay:f(P),easing:r(U)?void 0:k(U,A),direction:_,iterations:V+1,fill:"both"};O=e.animate({[X]:n,offset:z,easing:r(U)?U.map((t=>k(t,A))):void 0},a),O.finished||(O.finished=new Promise(((t,e)=>{O.onfinish=t,O.oncancel=e})));const o=n[n.length-1];O.finished.then((()=>{R||(M(e,X,o),O.cancel())})).catch(s),W||(O.playbackRate=1.000001)}else if(m&&K)n=n.map((t=>"string"==typeof t?parseFloat(t):t)),1===n.length&&n.unshift(parseFloat(t())),O=new m((t=>{M(e,X,a?a(t):t)}),n,Object.assign(Object.assign({},h),{duration:A,easing:U}));else{const t=n[n.length-1];M(e,X,Z&&i(t)?Z.toDefaultUnit(t):t)}return w&&v(e,y,n,{duration:A,delay:E,easing:U,repeat:V,offset:z},"motion-one"),Y.setAnimation(O),O&&!B&&O.pause(),O}}export{R as animateStyle};
