# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2020
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2024-08-07 19:03+0000\n"
"Last-Translator: Resul <<EMAIL>>, 2024\n"
"Language-Team: Turkmen (http://app.transifex.com/django/django/language/"
"tk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: tk\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Advanced options"
msgstr "Has çylşyrymly wariantlar"

msgid "Flat Pages"
msgstr "Ýönekeý Sahypalar"

msgid "URL"
msgstr "URL"

msgid ""
"Example: “/about/contact/”. Make sure to have leading and trailing slashes."
msgstr ""
"Meselem: \"about/contact/\". Dogry we yzygiderli çyzgylaryň bardygyna göz "
"ýetiriň."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""

msgid "Example: “/about/contact”. Make sure to have a leading slash."
msgstr ""

msgid "URL is missing a leading slash."
msgstr ""

msgid "URL is missing a trailing slash."
msgstr ""

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr ""

msgid "title"
msgstr "Sözbaşy"

msgid "content"
msgstr "mazmun"

msgid "enable comments"
msgstr ""

msgid "template name"
msgstr ""

msgid ""
"Example: “flatpages/contact_page.html”. If this isn’t provided, the system "
"will use “flatpages/default.html”."
msgstr ""

msgid "registration required"
msgstr ""

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr ""

msgid "sites"
msgstr ""

msgid "flat page"
msgstr ""

msgid "flat pages"
msgstr ""
