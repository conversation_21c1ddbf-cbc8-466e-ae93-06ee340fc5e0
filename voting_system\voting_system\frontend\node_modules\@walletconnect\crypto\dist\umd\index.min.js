!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define("isoCrypto",[],e):"object"==typeof exports?exports.isoCrypto=e():t.isoCrypto=e()}(this,(function(){return function(t){var e={};function n(r){if(e[r])return e[r].exports;var o=e[r]={i:r,l:!1,exports:{}};return t[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=t,n.c=e,n.d=function(t,e,r){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var o in t)n.d(r,o,function(e){return t[e]}.bind(null,o));return r},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s=10)}([function(t,e,n){"use strict";n.r(e),n.d(e,"__extends",(function(){return o})),n.d(e,"__assign",(function(){return s})),n.d(e,"__rest",(function(){return i})),n.d(e,"__decorate",(function(){return c})),n.d(e,"__param",(function(){return a})),n.d(e,"__metadata",(function(){return u})),n.d(e,"__awaiter",(function(){return h})),n.d(e,"__generator",(function(){return l})),n.d(e,"__createBinding",(function(){return f})),n.d(e,"__exportStar",(function(){return d})),n.d(e,"__values",(function(){return p})),n.d(e,"__read",(function(){return y})),n.d(e,"__spread",(function(){return b})),n.d(e,"__spreadArrays",(function(){return g})),n.d(e,"__await",(function(){return w})),n.d(e,"__asyncGenerator",(function(){return _})),n.d(e,"__asyncDelegator",(function(){return x})),n.d(e,"__asyncValues",(function(){return E})),n.d(e,"__makeTemplateObject",(function(){return v})),n.d(e,"__importStar",(function(){return A})),n.d(e,"__importDefault",(function(){return L})),n.d(e,"__classPrivateFieldGet",(function(){return H})),n.d(e,"__classPrivateFieldSet",(function(){return S}));
/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */
var r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)};function o(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}var s=function(){return(s=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)};function i(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(t);o<r.length;o++)e.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(t,r[o])&&(n[r[o]]=t[r[o]])}return n}function c(t,e,n,r){var o,s=arguments.length,i=s<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)i=Reflect.decorate(t,e,n,r);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(i=(s<3?o(i):s>3?o(e,n,i):o(e,n))||i);return s>3&&i&&Object.defineProperty(e,n,i),i}function a(t,e){return function(n,r){e(n,r,t)}}function u(t,e){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(t,e)}function h(t,e,n,r){return new(n||(n=Promise))((function(o,s){function i(t){try{a(r.next(t))}catch(t){s(t)}}function c(t){try{a(r.throw(t))}catch(t){s(t)}}function a(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(i,c)}a((r=r.apply(t,e||[])).next())}))}function l(t,e){var n,r,o,s,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return s={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function c(s){return function(c){return function(s){if(n)throw new TypeError("Generator is already executing.");for(;i;)try{if(n=1,r&&(o=2&s[0]?r.return:s[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,s[1])).done)return o;switch(r=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return i.label++,{value:s[1],done:!1};case 5:i.label++,r=s[1],s=[0];continue;case 7:s=i.ops.pop(),i.trys.pop();continue;default:if(!(o=i.trys,(o=o.length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){i=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){i.label=s[1];break}if(6===s[0]&&i.label<o[1]){i.label=o[1],o=s;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(s);break}o[2]&&i.ops.pop(),i.trys.pop();continue}s=e.call(t,i)}catch(t){s=[6,t],r=0}finally{n=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}}function f(t,e,n,r){void 0===r&&(r=n),t[r]=e[n]}function d(t,e){for(var n in t)"default"===n||e.hasOwnProperty(n)||(e[n]=t[n])}function p(t){var e="function"==typeof Symbol&&Symbol.iterator,n=e&&t[e],r=0;if(n)return n.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function y(t,e){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var r,o,s=n.call(t),i=[];try{for(;(void 0===e||e-- >0)&&!(r=s.next()).done;)i.push(r.value)}catch(t){o={error:t}}finally{try{r&&!r.done&&(n=s.return)&&n.call(s)}finally{if(o)throw o.error}}return i}function b(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(y(arguments[e]));return t}function g(){for(var t=0,e=0,n=arguments.length;e<n;e++)t+=arguments[e].length;var r=Array(t),o=0;for(e=0;e<n;e++)for(var s=arguments[e],i=0,c=s.length;i<c;i++,o++)r[o]=s[i];return r}function w(t){return this instanceof w?(this.v=t,this):new w(t)}function _(t,e,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r,o=n.apply(t,e||[]),s=[];return r={},i("next"),i("throw"),i("return"),r[Symbol.asyncIterator]=function(){return this},r;function i(t){o[t]&&(r[t]=function(e){return new Promise((function(n,r){s.push([t,e,n,r])>1||c(t,e)}))})}function c(t,e){try{(n=o[t](e)).value instanceof w?Promise.resolve(n.value.v).then(a,u):h(s[0][2],n)}catch(t){h(s[0][3],t)}var n}function a(t){c("next",t)}function u(t){c("throw",t)}function h(t,e){t(e),s.shift(),s.length&&c(s[0][0],s[0][1])}}function x(t){var e,n;return e={},r("next"),r("throw",(function(t){throw t})),r("return"),e[Symbol.iterator]=function(){return this},e;function r(r,o){e[r]=t[r]?function(e){return(n=!n)?{value:w(t[r](e)),done:"return"===r}:o?o(e):e}:o}}function E(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e,n=t[Symbol.asyncIterator];return n?n.call(t):(t=p(t),e={},r("next"),r("throw"),r("return"),e[Symbol.asyncIterator]=function(){return this},e);function r(n){e[n]=t[n]&&function(e){return new Promise((function(r,o){(function(t,e,n,r){Promise.resolve(r).then((function(e){t({value:e,done:n})}),e)})(r,o,(e=t[n](e)).done,e.value)}))}}}function v(t,e){return Object.defineProperty?Object.defineProperty(t,"raw",{value:e}):t.raw=e,t}function A(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var n in t)Object.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e.default=t,e}function L(t){return t&&t.__esModule?t:{default:t}}function H(t,e){if(!e.has(t))throw new TypeError("attempted to get private field on non-instance");return e.get(t)}function S(t,e,n){if(!e.has(t))throw new TypeError("attempted to set private field on non-instance");return e.set(t,n),n}},function(t,e,n){"use strict";
/*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) */Object.defineProperty(e,"__esModule",{value:!0}),e.Hash=e.nextTick=e.byteSwapIfBE=e.byteSwap=e.isLE=e.rotl=e.rotr=e.createView=e.u32=e.u8=void 0,e.isBytes=function(t){return t instanceof Uint8Array||ArrayBuffer.isView(t)&&"Uint8Array"===t.constructor.name},e.byteSwap32=function(t){for(let n=0;n<t.length;n++)t[n]=(0,e.byteSwap)(t[n])},e.bytesToHex=function(t){(0,o.abytes)(t);let e="";for(let n=0;n<t.length;n++)e+=s[t[n]];return e},e.hexToBytes=function(t){if("string"!=typeof t)throw new Error("hex string expected, got "+typeof t);const e=t.length,n=e/2;if(e%2)throw new Error("hex string expected, got unpadded hex of length "+e);const r=new Uint8Array(n);for(let e=0,o=0;e<n;e++,o+=2){const n=f(t.charCodeAt(o)),s=f(t.charCodeAt(o+1));if(void 0===n||void 0===s){const e=t[o]+t[o+1];throw new Error('hex string expected, got non-hex character "'+e+'" at index '+o)}r[e]=16*n+s}return r},e.asyncLoop=async function(t,n,r){let o=Date.now();for(let s=0;s<t;s++){r(s);const t=Date.now()-o;t>=0&&t<n||(await(0,e.nextTick)(),o+=t)}},e.utf8ToBytes=d,e.toBytes=p,e.concatBytes=function(...t){let e=0;for(let n=0;n<t.length;n++){const r=t[n];(0,o.abytes)(r),e+=r.length}const n=new Uint8Array(e);for(let e=0,r=0;e<t.length;e++){const o=t[e];n.set(o,r),r+=o.length}return n},e.checkOpts=function(t,e){if(void 0!==e&&"[object Object]"!=={}.toString.call(e))throw new Error("Options should be object or undefined");return Object.assign(t,e)},e.wrapConstructor=function(t){const e=e=>t().update(p(e)).digest(),n=t();return e.outputLen=n.outputLen,e.blockLen=n.blockLen,e.create=()=>t(),e},e.wrapConstructorWithOpts=function(t){const e=(e,n)=>t(n).update(p(e)).digest(),n=t({});return e.outputLen=n.outputLen,e.blockLen=n.blockLen,e.create=e=>t(e),e},e.wrapXOFConstructorWithOpts=function(t){const e=(e,n)=>t(n).update(p(e)).digest(),n=t({});return e.outputLen=n.outputLen,e.blockLen=n.blockLen,e.create=e=>t(e),e},e.randomBytes=function(t=32){if(r.crypto&&"function"==typeof r.crypto.getRandomValues)return r.crypto.getRandomValues(new Uint8Array(t));if(r.crypto&&"function"==typeof r.crypto.randomBytes)return r.crypto.randomBytes(t);throw new Error("crypto.getRandomValues must be defined")};const r=n(18),o=n(4);e.u8=t=>new Uint8Array(t.buffer,t.byteOffset,t.byteLength);e.u32=t=>new Uint32Array(t.buffer,t.byteOffset,Math.floor(t.byteLength/4));e.createView=t=>new DataView(t.buffer,t.byteOffset,t.byteLength);e.rotr=(t,e)=>t<<32-e|t>>>e;e.rotl=(t,e)=>t<<e|t>>>32-e>>>0,e.isLE=68===new Uint8Array(new Uint32Array([287454020]).buffer)[0];e.byteSwap=t=>t<<24&4278190080|t<<8&16711680|t>>>8&65280|t>>>24&255,e.byteSwapIfBE=e.isLE?t=>t:t=>(0,e.byteSwap)(t);const s=Array.from({length:256},(t,e)=>e.toString(16).padStart(2,"0"));const i=48,c=57,a=65,u=70,h=97,l=102;function f(t){return t>=i&&t<=c?t-i:t>=a&&t<=u?t-(a-10):t>=h&&t<=l?t-(h-10):void 0}function d(t){if("string"!=typeof t)throw new Error("utf8ToBytes expected string, got "+typeof t);return new Uint8Array((new TextEncoder).encode(t))}function p(t){return"string"==typeof t&&(t=d(t)),(0,o.abytes)(t),t}e.nextTick=async()=>{};e.Hash=class{clone(){return this._cloneInto()}}},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.fallbackRipemd160=e.fallbackSha512=e.fallbackSha256=e.fallbackHmacSha512Sign=e.fallbackHmacSha256Sign=e.fallbackAesDecrypt=e.fallbackAesEncrypt=void 0;const r=n(15),o=n(17),s=n(19),i=n(20),c=n(22);e.fallbackAesEncrypt=function(t,e,n){return(0,r.cbc)(e,t).encrypt(n)},e.fallbackAesDecrypt=function(t,e,n){return(0,r.cbc)(e,t).decrypt(n)},e.fallbackHmacSha256Sign=function(t,e){return(0,o.hmac)(s.sha256,t,e)},e.fallbackHmacSha512Sign=function(t,e){return(0,o.hmac)(i.sha512,t,e)},e.fallbackSha256=function(t){return(0,s.sha256)(t)},e.fallbackSha512=function(t){return(0,i.sha512)(t)},e.fallbackRipemd160=function(t){return(0,c.ripemd160)(t)}},function(t,e,n){"use strict";function r(t){if(!Number.isSafeInteger(t)||t<0)throw new Error("positive integer expected, got "+t)}function o(t){return t instanceof Uint8Array||ArrayBuffer.isView(t)&&"Uint8Array"===t.constructor.name}function s(t,...e){if(!o(t))throw new Error("Uint8Array expected");if(e.length>0&&!e.includes(t.length))throw new Error("Uint8Array expected of length "+e+", got length="+t.length)}Object.defineProperty(e,"__esModule",{value:!0}),e.anumber=r,e.abool=function(t){if("boolean"!=typeof t)throw new Error("boolean expected, not "+t)},e.abytes=s,e.ahash=function(t){if("function"!=typeof t||"function"!=typeof t.create)throw new Error("Hash should be wrapped by utils.wrapConstructor");r(t.outputLen),r(t.blockLen)},e.aexists=function(t,e=!0){if(t.destroyed)throw new Error("Hash instance has been destroyed");if(e&&t.finished)throw new Error("Hash#digest() has already been called")},e.aoutput=function(t,e){s(t);const n=e.outputLen;if(t.length<n)throw new Error("digestInto() expects output buffer of length at least "+n)},e.isBytes=o},function(t,e,n){"use strict";function r(t){if(!Number.isSafeInteger(t)||t<0)throw new Error("positive integer expected, got "+t)}function o(t,...e){if(!((n=t)instanceof Uint8Array||ArrayBuffer.isView(n)&&"Uint8Array"===n.constructor.name))throw new Error("Uint8Array expected");var n;if(e.length>0&&!e.includes(t.length))throw new Error("Uint8Array expected of length "+e+", got length="+t.length)}Object.defineProperty(e,"__esModule",{value:!0}),e.anumber=r,e.abytes=o,e.ahash=function(t){if("function"!=typeof t||"function"!=typeof t.create)throw new Error("Hash should be wrapped by utils.wrapConstructor");r(t.outputLen),r(t.blockLen)},e.aexists=function(t,e=!0){if(t.destroyed)throw new Error("Hash instance has been destroyed");if(e&&t.finished)throw new Error("Hash#digest() has already been called")},e.aoutput=function(t,e){o(t);const n=e.outputLen;if(t.length<n)throw new Error("digestInto() expects output buffer of length at least "+n)}},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.HashMD=e.Maj=e.Chi=void 0,e.setBigUint64=s;const r=n(4),o=n(1);function s(t,e,n,r){if("function"==typeof t.setBigUint64)return t.setBigUint64(e,n,r);const o=BigInt(32),s=BigInt(4294967295),i=Number(n>>o&s),c=Number(n&s),a=r?4:0,u=r?0:4;t.setUint32(e+a,i,r),t.setUint32(e+u,c,r)}e.Chi=(t,e,n)=>t&e^~t&n;e.Maj=(t,e,n)=>t&e^t&n^e&n;class i extends o.Hash{constructor(t,e,n,r){super(),this.blockLen=t,this.outputLen=e,this.padOffset=n,this.isLE=r,this.finished=!1,this.length=0,this.pos=0,this.destroyed=!1,this.buffer=new Uint8Array(t),this.view=(0,o.createView)(this.buffer)}update(t){(0,r.aexists)(this);const{view:e,buffer:n,blockLen:s}=this,i=(t=(0,o.toBytes)(t)).length;for(let r=0;r<i;){const c=Math.min(s-this.pos,i-r);if(c!==s)n.set(t.subarray(r,r+c),this.pos),this.pos+=c,r+=c,this.pos===s&&(this.process(e,0),this.pos=0);else{const e=(0,o.createView)(t);for(;s<=i-r;r+=s)this.process(e,r)}}return this.length+=t.length,this.roundClean(),this}digestInto(t){(0,r.aexists)(this),(0,r.aoutput)(t,this),this.finished=!0;const{buffer:e,view:n,blockLen:i,isLE:c}=this;let{pos:a}=this;e[a++]=128,this.buffer.subarray(a).fill(0),this.padOffset>i-a&&(this.process(n,0),a=0);for(let t=a;t<i;t++)e[t]=0;s(n,i-8,BigInt(8*this.length),c),this.process(n,0);const u=(0,o.createView)(t),h=this.outputLen;if(h%4)throw new Error("_sha2: outputLen should be aligned to 32bit");const l=h/4,f=this.get();if(l>f.length)throw new Error("_sha2: outputLen bigger than state");for(let t=0;t<l;t++)u.setUint32(4*t,f[t],c)}digest(){const{buffer:t,outputLen:e}=this;this.digestInto(t);const n=t.slice(0,e);return this.destroy(),n}_cloneInto(t){t||(t=new this.constructor),t.set(...this.get());const{blockLen:e,buffer:n,length:r,finished:o,destroyed:s,pos:i}=this;return t.length=r,t.pos=i,t.finished=o,t.destroyed=s,r%e&&t.buffer.set(n),t}}e.HashMD=i},function(t,e,n){"use strict";n.r(e),function(t,r){function o(){return(null==t?void 0:t.crypto)||(null==t?void 0:t.msCrypto)||{}}function s(){const t=o();return t.subtle||t.webkitSubtle}function i(){return!!o()&&!!s()}function c(){return typeof document>"u"&&typeof navigator<"u"&&"ReactNative"===navigator.product}function a(){return typeof r<"u"&&typeof r.versions<"u"&&typeof r.versions.node<"u"}function u(){return!c()&&!a()}n.d(e,"getBrowerCrypto",(function(){return o})),n.d(e,"getSubtleCrypto",(function(){return s})),n.d(e,"isBrowser",(function(){return u})),n.d(e,"isBrowserCryptoAvailable",(function(){return i})),n.d(e,"isNode",(function(){return a})),n.d(e,"isReactNative",(function(){return c}))}.call(this,n(12),n(13))},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.wrapCipher=e.Hash=e.nextTick=e.isLE=e.createView=e.u32=e.u8=void 0,e.bytesToHex=s,e.hexToBytes=d,e.hexToNumber=p,e.bytesToNumberBE=function(t){return p(s(t))},e.numberToBytesBE=function(t,e){return d(t.toString(16).padStart(2*e,"0"))},e.utf8ToBytes=y,e.bytesToUtf8=function(t){return(new TextDecoder).decode(t)},e.toBytes=function(t){if("string"==typeof t)t=y(t);else{if(!(0,r.isBytes)(t))throw new Error("Uint8Array expected, got "+typeof t);t=_(t)}return t},e.overlapBytes=b,e.complexOverlapBytes=function(t,e){if(b(t,e)&&t.byteOffset<e.byteOffset)throw new Error("complex overlap of input and output is not supported")},e.concatBytes=function(...t){let e=0;for(let n=0;n<t.length;n++){const o=t[n];(0,r.abytes)(o),e+=o.length}const n=new Uint8Array(e);for(let e=0,r=0;e<t.length;e++){const o=t[e];n.set(o,r),r+=o.length}return n},e.checkOpts=function(t,e){if(null==e||"object"!=typeof e)throw new Error("options must be defined");return Object.assign(t,e)},e.equalBytes=function(t,e){if(t.length!==e.length)return!1;let n=0;for(let r=0;r<t.length;r++)n|=t[r]^e[r];return 0===n},e.getOutput=function(t,e,n=!0){if(void 0===e)return new Uint8Array(t);if(e.length!==t)throw new Error("invalid output length, expected "+t+", got: "+e.length);if(n&&!w(e))throw new Error("invalid output, must be aligned");return e},e.setBigUint64=g,e.u64Lengths=function(t,n){const r=new Uint8Array(16),o=(0,e.createView)(r);return g(o,0,BigInt(n?n.length:0),!0),g(o,8,BigInt(t.length),!0),r},e.isAligned32=w,e.copyBytes=_,e.clean=function(...t){for(let e=0;e<t.length;e++)t[e].fill(0)};
/*! noble-ciphers - MIT License (c) 2023 Paul Miller (paulmillr.com) */
const r=n(3);e.u8=t=>new Uint8Array(t.buffer,t.byteOffset,t.byteLength);e.u32=t=>new Uint32Array(t.buffer,t.byteOffset,Math.floor(t.byteLength/4));if(e.createView=t=>new DataView(t.buffer,t.byteOffset,t.byteLength),e.isLE=68===new Uint8Array(new Uint32Array([287454020]).buffer)[0],!e.isLE)throw new Error("Non little-endian hardware is not supported");const o=Array.from({length:256},(t,e)=>e.toString(16).padStart(2,"0"));function s(t){(0,r.abytes)(t);let e="";for(let n=0;n<t.length;n++)e+=o[t[n]];return e}const i=48,c=57,a=65,u=70,h=97,l=102;function f(t){return t>=i&&t<=c?t-i:t>=a&&t<=u?t-(a-10):t>=h&&t<=l?t-(h-10):void 0}function d(t){if("string"!=typeof t)throw new Error("hex string expected, got "+typeof t);const e=t.length,n=e/2;if(e%2)throw new Error("hex string expected, got unpadded hex of length "+e);const r=new Uint8Array(n);for(let e=0,o=0;e<n;e++,o+=2){const n=f(t.charCodeAt(o)),s=f(t.charCodeAt(o+1));if(void 0===n||void 0===s){const e=t[o]+t[o+1];throw new Error('hex string expected, got non-hex character "'+e+'" at index '+o)}r[e]=16*n+s}return r}function p(t){if("string"!=typeof t)throw new Error("hex string expected, got "+typeof t);return BigInt(""===t?"0":"0x"+t)}function y(t){if("string"!=typeof t)throw new Error("string expected");return new Uint8Array((new TextEncoder).encode(t))}function b(t,e){return t.buffer===e.buffer&&t.byteOffset<e.byteOffset+e.byteLength&&e.byteOffset<t.byteOffset+t.byteLength}e.nextTick=async()=>{};e.Hash=class{};function g(t,e,n,r){if("function"==typeof t.setBigUint64)return t.setBigUint64(e,n,r);const o=BigInt(32),s=BigInt(4294967295),i=Number(n>>o&s),c=Number(n&s),a=r?4:0,u=r?0:4;t.setUint32(e+a,i,r),t.setUint32(e+u,c,r)}function w(t){return t.byteOffset%4==0}function _(t){return Uint8Array.from(t)}e.wrapCipher=(t,e)=>{function n(n,...o){if((0,r.abytes)(n),void 0!==t.nonceLength){const e=o[0];if(!e)throw new Error("nonce / iv required");t.varSizeNonce?(0,r.abytes)(e):(0,r.abytes)(e,t.nonceLength)}const s=t.tagLength;s&&void 0!==o[1]&&(0,r.abytes)(o[1]);const i=e(n,...o),c=(t,e)=>{if(void 0!==e){if(2!==t)throw new Error("cipher output not supported");(0,r.abytes)(e)}};let a=!1;return{encrypt(t,e){if(a)throw new Error("cannot encrypt() twice with same key + nonce");return a=!0,(0,r.abytes)(t),c(i.encrypt.length,e),i.encrypt(t,e)},decrypt(t,e){if((0,r.abytes)(t),s&&t.length<s)throw new Error("invalid ciphertext length: smaller than tagLength="+s);return c(i.decrypt.length,e),i.decrypt(t,e)}}}return Object.assign(n,t),n}},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});const r=n(0);r.__exportStar(n(24),e),r.__exportStar(n(25),e),r.__exportStar(n(26),e),r.__exportStar(n(27),e)},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.LENGTH_1024=e.LENGTH_512=e.LENGTH_256=e.LENGTH_128=e.LENGTH_64=e.LENGTH_32=e.LENGTH_16=e.LENGTH_1=e.LENGTH_0=void 0,e.LENGTH_0=0,e.LENGTH_1=1,e.LENGTH_16=16,e.LENGTH_32=32,e.LENGTH_64=64,e.LENGTH_128=128,e.LENGTH_256=256,e.LENGTH_512=512,e.LENGTH_1024=1024},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});const r=n(0);r.__exportStar(n(11),e),r.__exportStar(n(14),e),r.__exportStar(n(23),e),r.__exportStar(n(28),e),r.__exportStar(n(8),e),r.__exportStar(n(29),e)},function(t,e,n){"use strict";n.r(e),n.d(e,"randomBytes",(function(){return o}));var r=n(6);function o(t){return r.getBrowerCrypto().getRandomValues(new Uint8Array(t))}},function(t,e){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(t){"object"==typeof window&&(n=window)}t.exports=n},function(t,e){var n,r,o=t.exports={};function s(){throw new Error("setTimeout has not been defined")}function i(){throw new Error("clearTimeout has not been defined")}function c(t){if(n===setTimeout)return setTimeout(t,0);if((n===s||!n)&&setTimeout)return n=setTimeout,setTimeout(t,0);try{return n(t,0)}catch(e){try{return n.call(null,t,0)}catch(e){return n.call(this,t,0)}}}!function(){try{n="function"==typeof setTimeout?setTimeout:s}catch(t){n=s}try{r="function"==typeof clearTimeout?clearTimeout:i}catch(t){r=i}}();var a,u=[],h=!1,l=-1;function f(){h&&a&&(h=!1,a.length?u=a.concat(u):l=-1,u.length&&d())}function d(){if(!h){var t=c(f);h=!0;for(var e=u.length;e;){for(a=u,u=[];++l<e;)a&&a[l].run();l=-1,e=u.length}a=null,h=!1,function(t){if(r===clearTimeout)return clearTimeout(t);if((r===i||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(t);try{r(t)}catch(e){try{return r.call(null,t)}catch(e){return r.call(this,t)}}}(t)}}function p(t,e){this.fun=t,this.array=e}function y(){}o.nextTick=function(t){var e=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)e[n-1]=arguments[n];u.push(new p(t,e)),1!==u.length||h||c(d)},p.prototype.run=function(){this.fun.apply(null,this.array)},o.title="browser",o.browser=!0,o.env={},o.argv=[],o.version="",o.versions={},o.on=y,o.addListener=y,o.once=y,o.off=y,o.removeListener=y,o.removeAllListeners=y,o.emit=y,o.prependListener=y,o.prependOnceListener=y,o.listeners=function(t){return[]},o.binding=function(t){throw new Error("process.binding is not supported")},o.cwd=function(){return"/"},o.chdir=function(t){throw new Error("process.chdir is not supported")},o.umask=function(){return 0}},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.aesCbcDecrypt=e.aesCbcEncrypt=void 0;const r=n(0),o=n(2);e.aesCbcEncrypt=function(t,e,n){return r.__awaiter(this,void 0,void 0,(function*(){return(0,o.fallbackAesEncrypt)(t,e,n)}))},e.aesCbcDecrypt=function(t,e,n){return r.__awaiter(this,void 0,void 0,(function*(){return(0,o.fallbackAesDecrypt)(t,e,n)}))}},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.unsafe=e.aeskwp=e.aeskw=e.siv=e.gcm=e.cfb=e.cbc=e.ecb=e.ctr=void 0;const r=n(3),o=n(16),s=n(7),i=new Uint8Array(16);function c(t){return t<<1^283&-(t>>7)}function a(t,e){let n=0;for(;e>0;e>>=1)n^=t&-(1&e),t=c(t);return n}const u=(()=>{const t=new Uint8Array(256);for(let e=0,n=1;e<256;e++,n^=c(n))t[e]=n;const e=new Uint8Array(256);e[0]=99;for(let n=0;n<255;n++){let r=t[255-n];r|=r<<8,e[t[n]]=255&(r^r>>4^r>>5^r>>6^r>>7^99)}return(0,s.clean)(t),e})(),h=u.map((t,e)=>u.indexOf(e)),l=t=>t<<8|t>>>24,f=t=>t<<24&4278190080|t<<8&16711680|t>>>8&65280|t>>>24&255;function d(t,e){if(256!==t.length)throw new Error("Wrong sbox length");const n=new Uint32Array(256).map((n,r)=>e(t[r])),r=n.map(l),o=r.map(l),s=o.map(l),i=new Uint32Array(65536),c=new Uint32Array(65536),a=new Uint16Array(65536);for(let e=0;e<256;e++)for(let u=0;u<256;u++){const h=256*e+u;i[h]=n[e]^r[u],c[h]=o[e]^s[u],a[h]=t[e]<<8|t[u]}return{sbox:t,sbox2:a,T0:n,T1:r,T2:o,T3:s,T01:i,T23:c}}const p=d(u,t=>a(t,3)<<24|t<<16|t<<8|a(t,2)),y=d(h,t=>a(t,11)<<24|a(t,13)<<16|a(t,9)<<8|a(t,14)),b=(()=>{const t=new Uint8Array(16);for(let e=0,n=1;e<16;e++,n=c(n))t[e]=n;return t})();function g(t){(0,r.abytes)(t);const e=t.length;if(![16,24,32].includes(e))throw new Error("aes: invalid key size, should be 16, 24 or 32, got "+e);const{sbox2:n}=p,o=[];(0,s.isAligned32)(t)||o.push(t=(0,s.copyBytes)(t));const i=(0,s.u32)(t),c=i.length,a=t=>x(n,t,t,t,t),u=new Uint32Array(e+28);u.set(i);for(let t=c;t<u.length;t++){let e=u[t-1];t%c==0?e=a((h=e)<<24|h>>>8)^b[t/c-1]:c>6&&t%c==4&&(e=a(e)),u[t]=u[t-c]^e}var h;return(0,s.clean)(...o),u}function w(t){const e=g(t),n=e.slice(),r=e.length,{sbox2:o}=p,{T0:i,T1:c,T2:a,T3:u}=y;for(let t=0;t<r;t+=4)for(let o=0;o<4;o++)n[t+o]=e[r-t-4+o];(0,s.clean)(e);for(let t=4;t<r-4;t++){const e=n[t],r=x(o,e,e,e,e);n[t]=i[255&r]^c[r>>>8&255]^a[r>>>16&255]^u[r>>>24]}return n}function _(t,e,n,r,o,s){return t[n<<8&65280|r>>>8&255]^e[o>>>8&65280|s>>>24&255]}function x(t,e,n,r,o){return t[255&e|65280&n]|t[r>>>16&255|o>>>16&65280]<<16}function E(t,e,n,r,o){const{sbox2:s,T01:i,T23:c}=p;let a=0;e^=t[a++],n^=t[a++],r^=t[a++],o^=t[a++];const u=t.length/4-2;for(let s=0;s<u;s++){const s=t[a++]^_(i,c,e,n,r,o),u=t[a++]^_(i,c,n,r,o,e),h=t[a++]^_(i,c,r,o,e,n),l=t[a++]^_(i,c,o,e,n,r);e=s,n=u,r=h,o=l}return{s0:t[a++]^x(s,e,n,r,o),s1:t[a++]^x(s,n,r,o,e),s2:t[a++]^x(s,r,o,e,n),s3:t[a++]^x(s,o,e,n,r)}}function v(t,e,n,r,o){const{sbox2:s,T01:i,T23:c}=y;let a=0;e^=t[a++],n^=t[a++],r^=t[a++],o^=t[a++];const u=t.length/4-2;for(let s=0;s<u;s++){const s=t[a++]^_(i,c,e,o,r,n),u=t[a++]^_(i,c,n,e,o,r),h=t[a++]^_(i,c,r,n,e,o),l=t[a++]^_(i,c,o,r,n,e);e=s,n=u,r=h,o=l}return{s0:t[a++]^x(s,e,o,r,n),s1:t[a++]^x(s,n,e,o,r),s2:t[a++]^x(s,r,n,e,o),s3:t[a++]^x(s,o,r,n,e)}}function A(t,e,n,o){(0,r.abytes)(e,16),(0,r.abytes)(n);const i=n.length;o=(0,s.getOutput)(i,o),(0,s.complexOverlapBytes)(n,o);const c=e,a=(0,s.u32)(c);let{s0:u,s1:h,s2:l,s3:f}=E(t,a[0],a[1],a[2],a[3]);const d=(0,s.u32)(n),p=(0,s.u32)(o);for(let e=0;e+4<=d.length;e+=4){p[e+0]=d[e+0]^u,p[e+1]=d[e+1]^h,p[e+2]=d[e+2]^l,p[e+3]=d[e+3]^f;let n=1;for(let t=c.length-1;t>=0;t--)n=n+(255&c[t])|0,c[t]=255&n,n>>>=8;({s0:u,s1:h,s2:l,s3:f}=E(t,a[0],a[1],a[2],a[3]))}const y=16*Math.floor(d.length/4);if(y<i){const t=new Uint32Array([u,h,l,f]),e=(0,s.u8)(t);for(let t=y,r=0;t<i;t++,r++)o[t]=n[t]^e[r];(0,s.clean)(t)}return o}function L(t,e,n,o,i){(0,r.abytes)(n,16),(0,r.abytes)(o),i=(0,s.getOutput)(o.length,i);const c=n,a=(0,s.u32)(c),u=(0,s.createView)(c),h=(0,s.u32)(o),l=(0,s.u32)(i),f=e?0:12,d=o.length;let p=u.getUint32(f,e),{s0:y,s1:b,s2:g,s3:w}=E(t,a[0],a[1],a[2],a[3]);for(let n=0;n+4<=h.length;n+=4)l[n+0]=h[n+0]^y,l[n+1]=h[n+1]^b,l[n+2]=h[n+2]^g,l[n+3]=h[n+3]^w,p=p+1>>>0,u.setUint32(f,p,e),({s0:y,s1:b,s2:g,s3:w}=E(t,a[0],a[1],a[2],a[3]));const _=16*Math.floor(h.length/4);if(_<d){const t=new Uint32Array([y,b,g,w]),e=(0,s.u8)(t);for(let t=_,n=0;t<d;t++,n++)i[t]=o[t]^e[n];(0,s.clean)(t)}return i}function H(t){if((0,r.abytes)(t),t.length%16!=0)throw new Error("aes-(cbc/ecb).decrypt ciphertext should consist of blocks with size 16")}function S(t,e,n){(0,r.abytes)(t);let o=t.length;const i=o%16;if(!e&&0!==i)throw new Error("aec/(cbc-ecb): unpadded plaintext with disabled padding");(0,s.isAligned32)(t)||(t=(0,s.copyBytes)(t));const c=(0,s.u32)(t);if(e){let t=16-i;t||(t=16),o+=t}n=(0,s.getOutput)(o,n),(0,s.complexOverlapBytes)(t,n);return{b:c,o:(0,s.u32)(n),out:n}}function m(t,e){if(!e)return t;const n=t.length;if(!n)throw new Error("aes/pcks5: empty ciphertext not allowed");const r=t[n-1];if(r<=0||r>16)throw new Error("aes/pcks5: wrong padding");const o=t.subarray(0,-r);for(let e=0;e<r;e++)if(t[n-e-1]!==r)throw new Error("aes/pcks5: wrong padding");return o}function B(t){const e=new Uint8Array(16),n=(0,s.u32)(e);e.set(t);const r=16-t.length;for(let t=16-r;t<16;t++)e[t]=r;return n}function O(t,e,n,r,o){const i=null==o?0:o.length,c=t.create(n,r.length+i);o&&c.update(o),c.update(r);const a=new Uint8Array(16),u=(0,s.createView)(a);o&&(0,s.setBigUint64)(u,0,BigInt(8*i),e),(0,s.setBigUint64)(u,8,BigInt(8*r.length),e),c.update(a);const h=c.digest();return(0,s.clean)(a),h}e.ctr=(0,s.wrapCipher)({blockSize:16,nonceLength:16},(function(t,e){function n(n,o){if((0,r.abytes)(n),void 0!==o&&((0,r.abytes)(o),!(0,s.isAligned32)(o)))throw new Error("unaligned destination");const i=g(t),c=(0,s.copyBytes)(e),a=[i,c];(0,s.isAligned32)(n)||a.push(n=(0,s.copyBytes)(n));const u=A(i,c,n,o);return(0,s.clean)(...a),u}return{encrypt:(t,e)=>n(t,e),decrypt:(t,e)=>n(t,e)}})),e.ecb=(0,s.wrapCipher)({blockSize:16},(function(t,e={}){const n=!e.disablePadding;return{encrypt(e,r){const{b:o,o:i,out:c}=S(e,n,r),a=g(t);let u=0;for(;u+4<=o.length;){const{s0:t,s1:e,s2:n,s3:r}=E(a,o[u+0],o[u+1],o[u+2],o[u+3]);i[u++]=t,i[u++]=e,i[u++]=n,i[u++]=r}if(n){const t=B(e.subarray(4*u)),{s0:n,s1:r,s2:o,s3:s}=E(a,t[0],t[1],t[2],t[3]);i[u++]=n,i[u++]=r,i[u++]=o,i[u++]=s}return(0,s.clean)(a),c},decrypt(e,r){H(e);const o=w(t);r=(0,s.getOutput)(e.length,r);const i=[o];(0,s.isAligned32)(e)||i.push(e=(0,s.copyBytes)(e)),(0,s.complexOverlapBytes)(e,r);const c=(0,s.u32)(e),a=(0,s.u32)(r);for(let t=0;t+4<=c.length;){const{s0:e,s1:n,s2:r,s3:s}=v(o,c[t+0],c[t+1],c[t+2],c[t+3]);a[t++]=e,a[t++]=n,a[t++]=r,a[t++]=s}return(0,s.clean)(...i),m(r,n)}}})),e.cbc=(0,s.wrapCipher)({blockSize:16,nonceLength:16},(function(t,e,n={}){const r=!n.disablePadding;return{encrypt(n,o){const i=g(t),{b:c,o:a,out:u}=S(n,r,o);let h=e;const l=[i];(0,s.isAligned32)(h)||l.push(h=(0,s.copyBytes)(h));const f=(0,s.u32)(h);let d=f[0],p=f[1],y=f[2],b=f[3],w=0;for(;w+4<=c.length;)d^=c[w+0],p^=c[w+1],y^=c[w+2],b^=c[w+3],({s0:d,s1:p,s2:y,s3:b}=E(i,d,p,y,b)),a[w++]=d,a[w++]=p,a[w++]=y,a[w++]=b;if(r){const t=B(n.subarray(4*w));d^=t[0],p^=t[1],y^=t[2],b^=t[3],({s0:d,s1:p,s2:y,s3:b}=E(i,d,p,y,b)),a[w++]=d,a[w++]=p,a[w++]=y,a[w++]=b}return(0,s.clean)(...l),u},decrypt(n,o){H(n);const i=w(t);let c=e;const a=[i];(0,s.isAligned32)(c)||a.push(c=(0,s.copyBytes)(c));const u=(0,s.u32)(c);o=(0,s.getOutput)(n.length,o),(0,s.isAligned32)(n)||a.push(n=(0,s.copyBytes)(n)),(0,s.complexOverlapBytes)(n,o);const h=(0,s.u32)(n),l=(0,s.u32)(o);let f=u[0],d=u[1],p=u[2],y=u[3];for(let t=0;t+4<=h.length;){const e=f,n=d,r=p,o=y;f=h[t+0],d=h[t+1],p=h[t+2],y=h[t+3];const{s0:s,s1:c,s2:a,s3:u}=v(i,f,d,p,y);l[t++]=s^e,l[t++]=c^n,l[t++]=a^r,l[t++]=u^o}return(0,s.clean)(...a),m(o,r)}}})),e.cfb=(0,s.wrapCipher)({blockSize:16,nonceLength:16},(function(t,e){function n(n,o,i){(0,r.abytes)(n);const c=n.length;if(i=(0,s.getOutput)(c,i),(0,s.overlapBytes)(n,i))throw new Error("overlapping src and dst not supported.");const a=g(t);let u=e;const h=[a];(0,s.isAligned32)(u)||h.push(u=(0,s.copyBytes)(u)),(0,s.isAligned32)(n)||h.push(n=(0,s.copyBytes)(n));const l=(0,s.u32)(n),f=(0,s.u32)(i),d=o?f:l,p=(0,s.u32)(u);let y=p[0],b=p[1],w=p[2],_=p[3];for(let t=0;t+4<=l.length;){const{s0:e,s1:n,s2:r,s3:o}=E(a,y,b,w,_);f[t+0]=l[t+0]^e,f[t+1]=l[t+1]^n,f[t+2]=l[t+2]^r,f[t+3]=l[t+3]^o,y=d[t++],b=d[t++],w=d[t++],_=d[t++]}const x=16*Math.floor(l.length/4);if(x<c){({s0:y,s1:b,s2:w,s3:_}=E(a,y,b,w,_));const t=(0,s.u8)(new Uint32Array([y,b,w,_]));for(let e=x,r=0;e<c;e++,r++)i[e]=n[e]^t[r];(0,s.clean)(t)}return(0,s.clean)(...h),i}return{encrypt:(t,e)=>n(t,!0,e),decrypt:(t,e)=>n(t,!1,e)}})),e.gcm=(0,s.wrapCipher)({blockSize:16,nonceLength:12,tagLength:16,varSizeNonce:!0},(function(t,e,n){if(e.length<8)throw new Error("aes/gcm: invalid nonce length");function r(t,e,r){const s=O(o.ghash,!1,t,r,n);for(let t=0;t<e.length;t++)s[t]^=e[t];return s}function c(){const n=g(t),r=i.slice(),c=i.slice();if(L(n,!1,c,c,r),12===e.length)c.set(e);else{const t=i.slice(),n=(0,s.createView)(t);(0,s.setBigUint64)(n,8,BigInt(8*e.length),!1);const a=o.ghash.create(r).update(e).update(t);a.digestInto(c),a.destroy()}return{xk:n,authKey:r,counter:c,tagMask:L(n,!1,c,i)}}return{encrypt(t){const{xk:e,authKey:n,counter:o,tagMask:i}=c(),a=new Uint8Array(t.length+16),u=[e,n,o,i];(0,s.isAligned32)(t)||u.push(t=(0,s.copyBytes)(t)),L(e,!1,o,t,a.subarray(0,t.length));const h=r(n,i,a.subarray(0,a.length-16));return u.push(h),a.set(h,t.length),(0,s.clean)(...u),a},decrypt(t){const{xk:e,authKey:n,counter:o,tagMask:i}=c(),a=[e,n,i,o];(0,s.isAligned32)(t)||a.push(t=(0,s.copyBytes)(t));const u=t.subarray(0,-16),h=t.subarray(-16),l=r(n,i,u);if(a.push(l),!(0,s.equalBytes)(l,h))throw new Error("aes/gcm: invalid ghash tag");const f=L(e,!1,o,u);return(0,s.clean)(...a),f}}}));const T=(t,e,n)=>r=>{if(!Number.isSafeInteger(r)||e>r||r>n){throw new Error(t+": expected value in range "+("["+e+".."+n+"]")+", got "+r)}};function C(t){return t instanceof Uint32Array||ArrayBuffer.isView(t)&&"Uint32Array"===t.constructor.name}function U(t,e){if((0,r.abytes)(e,16),!C(t))throw new Error("_encryptBlock accepts result of expandKeyLE");const n=(0,s.u32)(e);let{s0:o,s1:i,s2:c,s3:a}=E(t,n[0],n[1],n[2],n[3]);return n[0]=o,n[1]=i,n[2]=c,n[3]=a,e}function k(t,e){if((0,r.abytes)(e,16),!C(t))throw new Error("_decryptBlock accepts result of expandKeyLE");const n=(0,s.u32)(e);let{s0:o,s1:i,s2:c,s3:a}=v(t,n[0],n[1],n[2],n[3]);return n[0]=o,n[1]=i,n[2]=c,n[3]=a,e}e.siv=(0,s.wrapCipher)({blockSize:16,nonceLength:12,tagLength:16,varSizeNonce:!0},(function(t,e,n){const i=T("AAD",0,2**36),c=T("plaintext",0,2**36),a=T("nonce",12,12),u=T("ciphertext",16,2**36+16);function h(){const n=g(t),r=new Uint8Array(t.length),o=new Uint8Array(16),i=[n,r];let c=e;(0,s.isAligned32)(c)||i.push(c=(0,s.copyBytes)(c));const a=(0,s.u32)(c);let u=0,h=a[0],l=a[1],f=a[2],d=0;for(const t of[o,r].map(s.u32)){const e=(0,s.u32)(t);for(let t=0;t<e.length;t+=2){const{s0:r,s1:o}=E(n,u,h,l,f);e[t+0]=r,e[t+1]=o,u=++d}}const p={authKey:o,encKey:g(r)};return(0,s.clean)(...i),p}function l(t,r,i){const c=O(o.polyval,!0,r,i,n);for(let t=0;t<12;t++)c[t]^=e[t];c[15]&=127;const a=(0,s.u32)(c);let u=a[0],h=a[1],l=a[2],f=a[3];return({s0:u,s1:h,s2:l,s3:f}=E(t,u,h,l,f)),a[0]=u,a[1]=h,a[2]=l,a[3]=f,c}function f(t,e,n){let r=(0,s.copyBytes)(e);r[15]|=128;const o=L(t,!0,r,n);return(0,s.clean)(r),o}return(0,r.abytes)(t,16,24,32),a(e.length),void 0!==n&&i(n.length),{encrypt(t){c(t.length);const{encKey:e,authKey:n}=h(),r=l(e,n,t),o=[e,n,r];(0,s.isAligned32)(t)||o.push(t=(0,s.copyBytes)(t));const i=new Uint8Array(t.length+16);return i.set(r,t.length),i.set(f(e,r,t)),(0,s.clean)(...o),i},decrypt(t){u(t.length);const e=t.subarray(-16),{encKey:n,authKey:r}=h(),o=[n,r];(0,s.isAligned32)(t)||o.push(t=(0,s.copyBytes)(t));const i=f(n,e,t.subarray(0,-16)),c=l(n,r,i);if(o.push(c),!(0,s.equalBytes)(e,c))throw(0,s.clean)(...o),new Error("invalid polyval tag");return(0,s.clean)(...o),i}}}));const G={encrypt(t,e){if(e.length>=2**32)throw new Error("plaintext should be less than 4gb");const n=g(t);if(16===e.length)U(n,e);else{const t=(0,s.u32)(e);let r=t[0],o=t[1];for(let e=0,s=1;e<6;e++)for(let e=2;e<t.length;e+=2,s++){const{s0:i,s1:c,s2:a,s3:u}=E(n,r,o,t[e],t[e+1]);r=i,o=c^f(s),t[e]=a,t[e+1]=u}t[0]=r,t[1]=o}n.fill(0)},decrypt(t,e){if(e.length-8>=2**32)throw new Error("ciphertext should be less than 4gb");const n=w(t),r=e.length/8-1;if(1===r)k(n,e);else{const t=(0,s.u32)(e);let o=t[0],i=t[1];for(let e=0,s=6*r;e<6;e++)for(let e=2*r;e>=1;e-=2,s--){i^=f(s);const{s0:r,s1:c,s2:a,s3:u}=v(n,o,i,t[e],t[e+1]);o=r,i=c,t[e]=a,t[e+1]=u}t[0]=o,t[1]=i}n.fill(0)}},M=new Uint8Array(8).fill(166);e.aeskw=(0,s.wrapCipher)({blockSize:8},t=>({encrypt(e){if(!e.length||e.length%8!=0)throw new Error("invalid plaintext length");if(8===e.length)throw new Error("8-byte keys not allowed in AESKW, use AESKWP instead");const n=(0,s.concatBytes)(M,e);return G.encrypt(t,n),n},decrypt(e){if(e.length%8!=0||e.length<24)throw new Error("invalid ciphertext length");const n=(0,s.copyBytes)(e);if(G.decrypt(t,n),!(0,s.equalBytes)(n.subarray(0,8),M))throw new Error("integrity check failed");return n.subarray(0,8).fill(0),n.subarray(8)}}));e.aeskwp=(0,s.wrapCipher)({blockSize:8},t=>({encrypt(e){if(!e.length)throw new Error("invalid plaintext length");const n=8*Math.ceil(e.length/8),r=new Uint8Array(8+n);r.set(e,8);const o=(0,s.u32)(r);return o[0]=2790873510,o[1]=f(e.length),G.encrypt(t,r),r},decrypt(e){if(e.length<16)throw new Error("invalid ciphertext length");const n=(0,s.copyBytes)(e),r=(0,s.u32)(n);G.decrypt(t,n);const o=f(r[1])>>>0,i=8*Math.ceil(o/8);if(2790873510!==r[0]||n.length-8!==i)throw new Error("integrity check failed");for(let t=o;t<i;t++)if(0!==n[8+t])throw new Error("integrity check failed");return n.subarray(0,8).fill(0),n.subarray(8,8+o)}})),e.unsafe={expandKeyLE:g,expandKeyDecLE:w,encrypt:E,decrypt:v,encryptBlock:U,decryptBlock:k,ctrCounter:A,ctr32:L}},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.polyval=e.ghash=void 0,e._toGHASHKey=a;const r=n(3),o=n(7),s=new Uint8Array(16),i=(0,o.u32)(s),c=t=>(t>>>0&255)<<24|(t>>>8&255)<<16|(t>>>16&255)<<8|t>>>24&255|0;function a(t){t.reverse();const e=1&t[15];let n=0;for(let e=0;e<t.length;e++){const r=t[e];t[e]=r>>>1|n,n=(1&r)<<7}return t[0]^=225&-e,t}class u{constructor(t,e){this.blockLen=16,this.outputLen=16,this.s0=0,this.s1=0,this.s2=0,this.s3=0,this.finished=!1,t=(0,o.toBytes)(t),(0,r.abytes)(t,16);const n=(0,o.createView)(t);let s=n.getUint32(0,!1),i=n.getUint32(4,!1),a=n.getUint32(8,!1),u=n.getUint32(12,!1);const h=[];for(let t=0;t<128;t++)h.push({s0:c(s),s1:c(i),s2:c(a),s3:c(u)}),({s0:s,s1:i,s2:a,s3:u}={s3:(d=a)<<31|(p=u)>>>1,s2:(f=i)<<31|d>>>1,s1:(l=s)<<31|f>>>1,s0:l>>>1^225<<24&-(1&p)});var l,f,d,p;const y=(b=e||1024)>65536?8:b>1024?4:2;var b;if(![1,2,4,8].includes(y))throw new Error("ghash: invalid window size, expected 2, 4 or 8");this.W=y;const g=128/y,w=this.windowSize=2**y,_=[];for(let t=0;t<g;t++)for(let e=0;e<w;e++){let n=0,r=0,o=0,s=0;for(let i=0;i<y;i++){if(!(e>>>y-i-1&1))continue;const{s0:c,s1:a,s2:u,s3:l}=h[y*t+i];n^=c,r^=a,o^=u,s^=l}_.push({s0:n,s1:r,s2:o,s3:s})}this.t=_}_updateBlock(t,e,n,r){t^=this.s0,e^=this.s1,n^=this.s2,r^=this.s3;const{W:o,t:s,windowSize:i}=this;let c=0,a=0,u=0,h=0;const l=(1<<o)-1;let f=0;for(const d of[t,e,n,r])for(let t=0;t<4;t++){const e=d>>>8*t&255;for(let t=8/o-1;t>=0;t--){const n=e>>>o*t&l,{s0:r,s1:d,s2:p,s3:y}=s[f*i+n];c^=r,a^=d,u^=p,h^=y,f+=1}}this.s0=c,this.s1=a,this.s2=u,this.s3=h}update(t){t=(0,o.toBytes)(t),(0,r.aexists)(this);const e=(0,o.u32)(t),n=Math.floor(t.length/16),c=t.length%16;for(let t=0;t<n;t++)this._updateBlock(e[4*t+0],e[4*t+1],e[4*t+2],e[4*t+3]);return c&&(s.set(t.subarray(16*n)),this._updateBlock(i[0],i[1],i[2],i[3]),(0,o.clean)(i)),this}destroy(){const{t:t}=this;for(const e of t)e.s0=0,e.s1=0,e.s2=0,e.s3=0}digestInto(t){(0,r.aexists)(this),(0,r.aoutput)(t,this),this.finished=!0;const{s0:e,s1:n,s2:s,s3:i}=this,c=(0,o.u32)(t);return c[0]=e,c[1]=n,c[2]=s,c[3]=i,t}digest(){const t=new Uint8Array(16);return this.digestInto(t),this.destroy(),t}}class h extends u{constructor(t,e){t=(0,o.toBytes)(t);const n=a((0,o.copyBytes)(t));super(n,e),(0,o.clean)(n)}update(t){t=(0,o.toBytes)(t),(0,r.aexists)(this);const e=(0,o.u32)(t),n=t.length%16,a=Math.floor(t.length/16);for(let t=0;t<a;t++)this._updateBlock(c(e[4*t+3]),c(e[4*t+2]),c(e[4*t+1]),c(e[4*t+0]));return n&&(s.set(t.subarray(16*a)),this._updateBlock(c(i[3]),c(i[2]),c(i[1]),c(i[0])),(0,o.clean)(i)),this}digestInto(t){(0,r.aexists)(this),(0,r.aoutput)(t,this),this.finished=!0;const{s0:e,s1:n,s2:s,s3:i}=this,c=(0,o.u32)(t);return c[0]=e,c[1]=n,c[2]=s,c[3]=i,t.reverse()}}function l(t){const e=(e,n)=>t(n,e.length).update((0,o.toBytes)(e)).digest(),n=t(new Uint8Array(16),0);return e.outputLen=n.outputLen,e.blockLen=n.blockLen,e.create=(e,n)=>t(e,n),e}e.ghash=l((t,e)=>new u(t,e)),e.polyval=l((t,e)=>new h(t,e))},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.hmac=e.HMAC=void 0;const r=n(4),o=n(1);class s extends o.Hash{constructor(t,e){super(),this.finished=!1,this.destroyed=!1,(0,r.ahash)(t);const n=(0,o.toBytes)(e);if(this.iHash=t.create(),"function"!=typeof this.iHash.update)throw new Error("Expected instance of class which extends utils.Hash");this.blockLen=this.iHash.blockLen,this.outputLen=this.iHash.outputLen;const s=this.blockLen,i=new Uint8Array(s);i.set(n.length>s?t.create().update(n).digest():n);for(let t=0;t<i.length;t++)i[t]^=54;this.iHash.update(i),this.oHash=t.create();for(let t=0;t<i.length;t++)i[t]^=106;this.oHash.update(i),i.fill(0)}update(t){return(0,r.aexists)(this),this.iHash.update(t),this}digestInto(t){(0,r.aexists)(this),(0,r.abytes)(t,this.outputLen),this.finished=!0,this.iHash.digestInto(t),this.oHash.update(t),this.oHash.digestInto(t),this.destroy()}digest(){const t=new Uint8Array(this.oHash.outputLen);return this.digestInto(t),t}_cloneInto(t){t||(t=Object.create(Object.getPrototypeOf(this),{}));const{oHash:e,iHash:n,finished:r,destroyed:o,blockLen:s,outputLen:i}=this;return(t=t).finished=r,t.destroyed=o,t.blockLen=s,t.outputLen=i,t.oHash=e._cloneInto(t.oHash),t.iHash=n._cloneInto(t.iHash),t}destroy(){this.destroyed=!0,this.oHash.destroy(),this.iHash.destroy()}}e.HMAC=s;e.hmac=(t,e,n)=>new s(t,e).update(n).digest(),e.hmac.create=(t,e)=>new s(t,e)},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.crypto=void 0,e.crypto="object"==typeof globalThis&&"crypto"in globalThis?globalThis.crypto:void 0},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.sha224=e.sha256=e.SHA256=void 0;const r=n(5),o=n(1),s=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]),i=new Uint32Array([1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225]),c=new Uint32Array(64);class a extends r.HashMD{constructor(){super(64,32,8,!1),this.A=0|i[0],this.B=0|i[1],this.C=0|i[2],this.D=0|i[3],this.E=0|i[4],this.F=0|i[5],this.G=0|i[6],this.H=0|i[7]}get(){const{A:t,B:e,C:n,D:r,E:o,F:s,G:i,H:c}=this;return[t,e,n,r,o,s,i,c]}set(t,e,n,r,o,s,i,c){this.A=0|t,this.B=0|e,this.C=0|n,this.D=0|r,this.E=0|o,this.F=0|s,this.G=0|i,this.H=0|c}process(t,e){for(let n=0;n<16;n++,e+=4)c[n]=t.getUint32(e,!1);for(let t=16;t<64;t++){const e=c[t-15],n=c[t-2],r=(0,o.rotr)(e,7)^(0,o.rotr)(e,18)^e>>>3,s=(0,o.rotr)(n,17)^(0,o.rotr)(n,19)^n>>>10;c[t]=s+c[t-7]+r+c[t-16]|0}let{A:n,B:i,C:a,D:u,E:h,F:l,G:f,H:d}=this;for(let t=0;t<64;t++){const e=d+((0,o.rotr)(h,6)^(0,o.rotr)(h,11)^(0,o.rotr)(h,25))+(0,r.Chi)(h,l,f)+s[t]+c[t]|0,p=((0,o.rotr)(n,2)^(0,o.rotr)(n,13)^(0,o.rotr)(n,22))+(0,r.Maj)(n,i,a)|0;d=f,f=l,l=h,h=u+e|0,u=a,a=i,i=n,n=e+p|0}n=n+this.A|0,i=i+this.B|0,a=a+this.C|0,u=u+this.D|0,h=h+this.E|0,l=l+this.F|0,f=f+this.G|0,d=d+this.H|0,this.set(n,i,a,u,h,l,f,d)}roundClean(){c.fill(0)}destroy(){this.set(0,0,0,0,0,0,0,0),this.buffer.fill(0)}}e.SHA256=a;class u extends a{constructor(){super(),this.A=-1056596264,this.B=914150663,this.C=812702999,this.D=-150054599,this.E=-4191439,this.F=1750603025,this.G=1694076839,this.H=-1090891868,this.outputLen=28}}e.sha256=(0,o.wrapConstructor)(()=>new a),e.sha224=(0,o.wrapConstructor)(()=>new u)},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.sha384=e.sha512_256=e.sha512_224=e.sha512=e.SHA384=e.SHA512_256=e.SHA512_224=e.SHA512=void 0;const r=n(5),o=n(21),s=n(1),[i,c]=(()=>o.default.split(["0x428a2f98d728ae22","0x7137449123ef65cd","0xb5c0fbcfec4d3b2f","0xe9b5dba58189dbbc","0x3956c25bf348b538","0x59f111f1b605d019","0x923f82a4af194f9b","0xab1c5ed5da6d8118","0xd807aa98a3030242","0x12835b0145706fbe","0x243185be4ee4b28c","0x550c7dc3d5ffb4e2","0x72be5d74f27b896f","0x80deb1fe3b1696b1","0x9bdc06a725c71235","0xc19bf174cf692694","0xe49b69c19ef14ad2","0xefbe4786384f25e3","0x0fc19dc68b8cd5b5","0x240ca1cc77ac9c65","0x2de92c6f592b0275","0x4a7484aa6ea6e483","0x5cb0a9dcbd41fbd4","0x76f988da831153b5","0x983e5152ee66dfab","0xa831c66d2db43210","0xb00327c898fb213f","0xbf597fc7beef0ee4","0xc6e00bf33da88fc2","0xd5a79147930aa725","0x06ca6351e003826f","0x142929670a0e6e70","0x27b70a8546d22ffc","0x2e1b21385c26c926","0x4d2c6dfc5ac42aed","0x53380d139d95b3df","0x650a73548baf63de","0x766a0abb3c77b2a8","0x81c2c92e47edaee6","0x92722c851482353b","0xa2bfe8a14cf10364","0xa81a664bbc423001","0xc24b8b70d0f89791","0xc76c51a30654be30","0xd192e819d6ef5218","0xd69906245565a910","0xf40e35855771202a","0x106aa07032bbd1b8","0x19a4c116b8d2d0c8","0x1e376c085141ab53","0x2748774cdf8eeb99","0x34b0bcb5e19b48a8","0x391c0cb3c5c95a63","0x4ed8aa4ae3418acb","0x5b9cca4f7763e373","0x682e6ff3d6b2b8a3","0x748f82ee5defb2fc","0x78a5636f43172f60","0x84c87814a1f0ab72","0x8cc702081a6439ec","0x90befffa23631e28","0xa4506cebde82bde9","0xbef9a3f7b2c67915","0xc67178f2e372532b","0xca273eceea26619c","0xd186b8c721c0c207","0xeada7dd6cde0eb1e","0xf57d4f7fee6ed178","0x06f067aa72176fba","0x0a637dc5a2c898a6","0x113f9804bef90dae","0x1b710b35131c471b","0x28db77f523047d84","0x32caab7b40c72493","0x3c9ebe0a15c9bebc","0x431d67c49c100d4c","0x4cc5d4becb3e42b6","0x597f299cfc657e2a","0x5fcb6fab3ad6faec","0x6c44198c4a475817"].map(t=>BigInt(t))))(),a=new Uint32Array(80),u=new Uint32Array(80);class h extends r.HashMD{constructor(){super(128,64,16,!1),this.Ah=1779033703,this.Al=-205731576,this.Bh=-1150833019,this.Bl=-2067093701,this.Ch=1013904242,this.Cl=-23791573,this.Dh=-1521486534,this.Dl=1595750129,this.Eh=1359893119,this.El=-1377402159,this.Fh=-1694144372,this.Fl=725511199,this.Gh=528734635,this.Gl=-79577749,this.Hh=1541459225,this.Hl=327033209}get(){const{Ah:t,Al:e,Bh:n,Bl:r,Ch:o,Cl:s,Dh:i,Dl:c,Eh:a,El:u,Fh:h,Fl:l,Gh:f,Gl:d,Hh:p,Hl:y}=this;return[t,e,n,r,o,s,i,c,a,u,h,l,f,d,p,y]}set(t,e,n,r,o,s,i,c,a,u,h,l,f,d,p,y){this.Ah=0|t,this.Al=0|e,this.Bh=0|n,this.Bl=0|r,this.Ch=0|o,this.Cl=0|s,this.Dh=0|i,this.Dl=0|c,this.Eh=0|a,this.El=0|u,this.Fh=0|h,this.Fl=0|l,this.Gh=0|f,this.Gl=0|d,this.Hh=0|p,this.Hl=0|y}process(t,e){for(let n=0;n<16;n++,e+=4)a[n]=t.getUint32(e),u[n]=t.getUint32(e+=4);for(let t=16;t<80;t++){const e=0|a[t-15],n=0|u[t-15],r=o.default.rotrSH(e,n,1)^o.default.rotrSH(e,n,8)^o.default.shrSH(e,n,7),s=o.default.rotrSL(e,n,1)^o.default.rotrSL(e,n,8)^o.default.shrSL(e,n,7),i=0|a[t-2],c=0|u[t-2],h=o.default.rotrSH(i,c,19)^o.default.rotrBH(i,c,61)^o.default.shrSH(i,c,6),l=o.default.rotrSL(i,c,19)^o.default.rotrBL(i,c,61)^o.default.shrSL(i,c,6),f=o.default.add4L(s,l,u[t-7],u[t-16]),d=o.default.add4H(f,r,h,a[t-7],a[t-16]);a[t]=0|d,u[t]=0|f}let{Ah:n,Al:r,Bh:s,Bl:h,Ch:l,Cl:f,Dh:d,Dl:p,Eh:y,El:b,Fh:g,Fl:w,Gh:_,Gl:x,Hh:E,Hl:v}=this;for(let t=0;t<80;t++){const e=o.default.rotrSH(y,b,14)^o.default.rotrSH(y,b,18)^o.default.rotrBH(y,b,41),A=o.default.rotrSL(y,b,14)^o.default.rotrSL(y,b,18)^o.default.rotrBL(y,b,41),L=y&g^~y&_,H=b&w^~b&x,S=o.default.add5L(v,A,H,c[t],u[t]),m=o.default.add5H(S,E,e,L,i[t],a[t]),B=0|S,O=o.default.rotrSH(n,r,28)^o.default.rotrBH(n,r,34)^o.default.rotrBH(n,r,39),T=o.default.rotrSL(n,r,28)^o.default.rotrBL(n,r,34)^o.default.rotrBL(n,r,39),C=n&s^n&l^s&l,U=r&h^r&f^h&f;E=0|_,v=0|x,_=0|g,x=0|w,g=0|y,w=0|b,({h:y,l:b}=o.default.add(0|d,0|p,0|m,0|B)),d=0|l,p=0|f,l=0|s,f=0|h,s=0|n,h=0|r;const k=o.default.add3L(B,T,U);n=o.default.add3H(k,m,O,C),r=0|k}({h:n,l:r}=o.default.add(0|this.Ah,0|this.Al,0|n,0|r)),({h:s,l:h}=o.default.add(0|this.Bh,0|this.Bl,0|s,0|h)),({h:l,l:f}=o.default.add(0|this.Ch,0|this.Cl,0|l,0|f)),({h:d,l:p}=o.default.add(0|this.Dh,0|this.Dl,0|d,0|p)),({h:y,l:b}=o.default.add(0|this.Eh,0|this.El,0|y,0|b)),({h:g,l:w}=o.default.add(0|this.Fh,0|this.Fl,0|g,0|w)),({h:_,l:x}=o.default.add(0|this.Gh,0|this.Gl,0|_,0|x)),({h:E,l:v}=o.default.add(0|this.Hh,0|this.Hl,0|E,0|v)),this.set(n,r,s,h,l,f,d,p,y,b,g,w,_,x,E,v)}roundClean(){a.fill(0),u.fill(0)}destroy(){this.buffer.fill(0),this.set(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)}}e.SHA512=h;class l extends h{constructor(){super(),this.Ah=-1942145080,this.Al=424955298,this.Bh=1944164710,this.Bl=-1982016298,this.Ch=502970286,this.Cl=855612546,this.Dh=1738396948,this.Dl=1479516111,this.Eh=258812777,this.El=2077511080,this.Fh=2011393907,this.Fl=79989058,this.Gh=1067287976,this.Gl=1780299464,this.Hh=286451373,this.Hl=-1848208735,this.outputLen=28}}e.SHA512_224=l;class f extends h{constructor(){super(),this.Ah=573645204,this.Al=-64227540,this.Bh=-1621794909,this.Bl=-934517566,this.Ch=596883563,this.Cl=1867755857,this.Dh=-1774684391,this.Dl=1497426621,this.Eh=-1775747358,this.El=-1467023389,this.Fh=-1101128155,this.Fl=1401305490,this.Gh=721525244,this.Gl=746961066,this.Hh=246885852,this.Hl=-2117784414,this.outputLen=32}}e.SHA512_256=f;class d extends h{constructor(){super(),this.Ah=-876896931,this.Al=-1056596264,this.Bh=1654270250,this.Bl=914150663,this.Ch=-1856437926,this.Cl=812702999,this.Dh=355462360,this.Dl=-150054599,this.Eh=1731405415,this.El=-4191439,this.Fh=-1900787065,this.Fl=1750603025,this.Gh=-619958771,this.Gl=1694076839,this.Hh=1203062813,this.Hl=-1090891868,this.outputLen=48}}e.SHA384=d,e.sha512=(0,s.wrapConstructor)(()=>new h),e.sha512_224=(0,s.wrapConstructor)(()=>new l),e.sha512_256=(0,s.wrapConstructor)(()=>new f),e.sha384=(0,s.wrapConstructor)(()=>new d)},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.add5L=e.add5H=e.add4H=e.add4L=e.add3H=e.add3L=e.rotlBL=e.rotlBH=e.rotlSL=e.rotlSH=e.rotr32L=e.rotr32H=e.rotrBL=e.rotrBH=e.rotrSL=e.rotrSH=e.shrSL=e.shrSH=e.toBig=void 0,e.fromBig=s,e.split=i,e.add=x;const r=BigInt(2**32-1),o=BigInt(32);function s(t,e=!1){return e?{h:Number(t&r),l:Number(t>>o&r)}:{h:0|Number(t>>o&r),l:0|Number(t&r)}}function i(t,e=!1){let n=new Uint32Array(t.length),r=new Uint32Array(t.length);for(let o=0;o<t.length;o++){const{h:i,l:c}=s(t[o],e);[n[o],r[o]]=[i,c]}return[n,r]}const c=(t,e)=>BigInt(t>>>0)<<o|BigInt(e>>>0);e.toBig=c;const a=(t,e,n)=>t>>>n;e.shrSH=a;const u=(t,e,n)=>t<<32-n|e>>>n;e.shrSL=u;const h=(t,e,n)=>t>>>n|e<<32-n;e.rotrSH=h;const l=(t,e,n)=>t<<32-n|e>>>n;e.rotrSL=l;const f=(t,e,n)=>t<<64-n|e>>>n-32;e.rotrBH=f;const d=(t,e,n)=>t>>>n-32|e<<64-n;e.rotrBL=d;const p=(t,e)=>e;e.rotr32H=p;const y=(t,e)=>t;e.rotr32L=y;const b=(t,e,n)=>t<<n|e>>>32-n;e.rotlSH=b;const g=(t,e,n)=>e<<n|t>>>32-n;e.rotlSL=g;const w=(t,e,n)=>e<<n-32|t>>>64-n;e.rotlBH=w;const _=(t,e,n)=>t<<n-32|e>>>64-n;function x(t,e,n,r){const o=(e>>>0)+(r>>>0);return{h:t+n+(o/2**32|0)|0,l:0|o}}e.rotlBL=_;const E=(t,e,n)=>(t>>>0)+(e>>>0)+(n>>>0);e.add3L=E;const v=(t,e,n,r)=>e+n+r+(t/2**32|0)|0;e.add3H=v;const A=(t,e,n,r)=>(t>>>0)+(e>>>0)+(n>>>0)+(r>>>0);e.add4L=A;const L=(t,e,n,r,o)=>e+n+r+o+(t/2**32|0)|0;e.add4H=L;const H=(t,e,n,r,o)=>(t>>>0)+(e>>>0)+(n>>>0)+(r>>>0)+(o>>>0);e.add5L=H;const S=(t,e,n,r,o,s)=>e+n+r+o+s+(t/2**32|0)|0;e.add5H=S;const m={fromBig:s,split:i,toBig:c,shrSH:a,shrSL:u,rotrSH:h,rotrSL:l,rotrBH:f,rotrBL:d,rotr32H:p,rotr32L:y,rotlSH:b,rotlSL:g,rotlBH:w,rotlBL:_,add:x,add3L:E,add3H:v,add4L:A,add4H:L,add5H:S,add5L:H};e.default=m},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.ripemd160=e.RIPEMD160=void 0;const r=n(5),o=n(1),s=new Uint8Array([7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8]),i=new Uint8Array(new Array(16).fill(0).map((t,e)=>e));let c=[i],a=[i.map(t=>(9*t+5)%16)];for(let t=0;t<4;t++)for(let e of[c,a])e.push(e[t].map(t=>s[t]));const u=[[11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8],[12,13,11,15,6,9,9,7,12,15,11,13,7,8,7,7],[13,15,14,11,7,7,6,8,13,14,13,12,5,5,6,9],[14,11,12,14,8,6,5,5,15,12,15,14,9,9,8,6],[15,12,13,13,9,5,8,6,14,11,12,11,8,6,5,5]].map(t=>new Uint8Array(t)),h=c.map((t,e)=>t.map(t=>u[e][t])),l=a.map((t,e)=>t.map(t=>u[e][t])),f=new Uint32Array([0,1518500249,1859775393,2400959708,2840853838]),d=new Uint32Array([1352829926,1548603684,1836072691,2053994217,0]);function p(t,e,n,r){return 0===t?e^n^r:1===t?e&n|~e&r:2===t?(e|~n)^r:3===t?e&r|n&~r:e^(n|~r)}const y=new Uint32Array(16);class b extends r.HashMD{constructor(){super(64,20,8,!0),this.h0=1732584193,this.h1=-271733879,this.h2=-1732584194,this.h3=271733878,this.h4=-1009589776}get(){const{h0:t,h1:e,h2:n,h3:r,h4:o}=this;return[t,e,n,r,o]}set(t,e,n,r,o){this.h0=0|t,this.h1=0|e,this.h2=0|n,this.h3=0|r,this.h4=0|o}process(t,e){for(let n=0;n<16;n++,e+=4)y[n]=t.getUint32(e,!0);let n=0|this.h0,r=n,s=0|this.h1,i=s,u=0|this.h2,b=u,g=0|this.h3,w=g,_=0|this.h4,x=_;for(let t=0;t<5;t++){const e=4-t,E=f[t],v=d[t],A=c[t],L=a[t],H=h[t],S=l[t];for(let e=0;e<16;e++){const r=(0,o.rotl)(n+p(t,s,u,g)+y[A[e]]+E,H[e])+_|0;n=_,_=g,g=0|(0,o.rotl)(u,10),u=s,s=r}for(let t=0;t<16;t++){const n=(0,o.rotl)(r+p(e,i,b,w)+y[L[t]]+v,S[t])+x|0;r=x,x=w,w=0|(0,o.rotl)(b,10),b=i,i=n}}this.set(this.h1+u+w|0,this.h2+g+x|0,this.h3+_+r|0,this.h4+n+i|0,this.h0+s+b|0)}roundClean(){y.fill(0)}destroy(){this.destroyed=!0,this.buffer.fill(0),this.set(0,0,0,0,0)}}e.RIPEMD160=b,e.ripemd160=(0,o.wrapConstructor)(()=>new b)},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.hmacSha512Verify=e.hmacSha512Sign=e.hmacSha256Verify=e.hmacSha256Sign=void 0;const r=n(0),o=n(8),s=n(2);e.hmacSha256Sign=function(t,e){return r.__awaiter(this,void 0,void 0,(function*(){return(0,s.fallbackHmacSha256Sign)(t,e)}))},e.hmacSha256Verify=function(t,e,n){return r.__awaiter(this,void 0,void 0,(function*(){const r=(0,s.fallbackHmacSha256Sign)(t,e);return(0,o.isConstantTime)(r,n)}))},e.hmacSha512Sign=function(t,e){return r.__awaiter(this,void 0,void 0,(function*(){return(0,s.fallbackHmacSha512Sign)(t,e)}))},e.hmacSha512Verify=function(t,e,n){return r.__awaiter(this,void 0,void 0,(function*(){const r=(0,s.fallbackHmacSha512Sign)(t,e);return(0,o.isConstantTime)(r,n)}))}},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});n(0).__exportStar(n(6),e)},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.pkcs7=void 0;const r=[[16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16],[15,15,15,15,15,15,15,15,15,15,15,15,15,15,15],[14,14,14,14,14,14,14,14,14,14,14,14,14,14],[13,13,13,13,13,13,13,13,13,13,13,13,13],[12,12,12,12,12,12,12,12,12,12,12,12],[11,11,11,11,11,11,11,11,11,11,11],[10,10,10,10,10,10,10,10,10,10],[9,9,9,9,9,9,9,9,9],[8,8,8,8,8,8,8,8],[7,7,7,7,7,7,7],[6,6,6,6,6,6],[5,5,5,5,5],[4,4,4,4],[3,3,3],[2,2],[1]];e.pkcs7={pad(t){const e=r[t.byteLength%16||0],n=new Uint8Array(t.byteLength+e.length);return n.set(t),n.set(e,t.byteLength),n},unpad:t=>t.subarray(0,t.byteLength-t[t.byteLength-1])}},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0})},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.isConstantTime=e.assert=void 0,e.assert=function(t,e){if(!t)throw new Error(e||"Assertion failed")},e.isConstantTime=function(t,e){if(t.length!==e.length)return!1;let n=0;for(let r=0;r<t.length;r++)n|=t[r]^e[r];return 0===n}},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.ripemd160=e.sha512=e.sha256=void 0;const r=n(0),o=n(2);e.sha256=function(t){return r.__awaiter(this,void 0,void 0,(function*(){return(0,o.fallbackSha256)(t)}))},e.sha512=function(t){return r.__awaiter(this,void 0,void 0,(function*(){return(0,o.fallbackSha512)(t)}))},e.ripemd160=function(t){return r.__awaiter(this,void 0,void 0,(function*(){return(0,o.fallbackRipemd160)(t)}))}},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});const r=n(0);r.__exportStar(n(30),e),r.__exportStar(n(31),e),r.__exportStar(n(32),e),r.__exportStar(n(9),e),r.__exportStar(n(33),e)},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MAC_LENGTH=e.IV_LENGTH=e.KEY_LENGTH=e.PREFIX_LENGTH=e.RIPEMD160_NODE_ALGO=e.SHA512_NODE_ALGO=e.SHA256_NODE_ALGO=e.HMAC_NODE_ALGO=e.AES_NODE_ALGO=e.SHA512_BROWSER_ALGO=e.SHA256_BROWSER_ALGO=e.HMAC_BROWSER=e.HMAC_BROWSER_ALGO=e.AES_BROWSER_ALGO=e.HMAC_LENGTH=e.AES_LENGTH=void 0;const r=n(9);e.AES_LENGTH=r.LENGTH_256,e.HMAC_LENGTH=r.LENGTH_256,e.AES_BROWSER_ALGO="AES-CBC",e.HMAC_BROWSER_ALGO="SHA-"+e.AES_LENGTH,e.HMAC_BROWSER="HMAC",e.SHA256_BROWSER_ALGO="SHA-256",e.SHA512_BROWSER_ALGO="SHA-512",e.AES_NODE_ALGO=`aes-${e.AES_LENGTH}-cbc`,e.HMAC_NODE_ALGO="sha"+e.HMAC_LENGTH,e.SHA256_NODE_ALGO="sha256",e.SHA512_NODE_ALGO="sha512",e.RIPEMD160_NODE_ALGO="ripemd160",e.PREFIX_LENGTH=r.LENGTH_1,e.KEY_LENGTH=r.LENGTH_32,e.IV_LENGTH=r.LENGTH_16,e.MAC_LENGTH=r.LENGTH_32},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.UTF8_ENC=e.HEX_ENC=void 0,e.HEX_ENC="hex",e.UTF8_ENC="utf8"},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.ERROR_BAD_MAC=void 0,e.ERROR_BAD_MAC="Bad MAC"},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.VERIFY_OP=e.SIGN_OP=e.DECRYPT_OP=e.ENCRYPT_OP=void 0,e.ENCRYPT_OP="encrypt",e.DECRYPT_OP="decrypt",e.SIGN_OP="sign",e.VERIFY_OP="verify"}])}));