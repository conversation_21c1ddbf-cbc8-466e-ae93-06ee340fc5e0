export declare const AES_LENGTH = 256;
export declare const HMAC_LENGTH = 256;
export declare const AES_BROWSER_ALGO = "AES-CBC";
export declare const HMAC_BROWSER_ALGO: string;
export declare const HMAC_BROWSER = "HMAC";
export declare const SHA256_BROWSER_ALGO = "SHA-256";
export declare const SHA512_BROWSER_ALGO = "SHA-512";
export declare const AES_NODE_ALGO: string;
export declare const HMAC_NODE_ALGO: string;
export declare const SHA256_NODE_ALGO = "sha256";
export declare const SHA512_NODE_ALGO = "sha512";
export declare const RIPEMD160_NODE_ALGO = "ripemd160";
export declare const PREFIX_LENGTH = 1;
export declare const KEY_LENGTH = 32;
export declare const IV_LENGTH = 16;
export declare const MAC_LENGTH = 32;
//# sourceMappingURL=default.d.ts.map