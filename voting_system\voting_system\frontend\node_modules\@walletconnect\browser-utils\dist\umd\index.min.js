!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define("WalletConnectBrowserUtils",[],e):"object"==typeof exports?exports.WalletConnectBrowserUtils=e():t.WalletConnectBrowserUtils=e()}(this,(function(){return function(t){var e={};function n(r){if(e[r])return e[r].exports;var o=e[r]={i:r,l:!1,exports:{}};return t[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=t,n.c=e,n.d=function(t,e,r){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var o in t)n.d(r,o,function(e){return t[e]}.bind(null,o));return r},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s=5)}([function(t,e,n){"use strict";n.r(e),n.d(e,"__extends",(function(){return o})),n.d(e,"__assign",(function(){return i})),n.d(e,"__rest",(function(){return u})),n.d(e,"__decorate",(function(){return a})),n.d(e,"__param",(function(){return c})),n.d(e,"__metadata",(function(){return s})),n.d(e,"__awaiter",(function(){return f})),n.d(e,"__generator",(function(){return l})),n.d(e,"__exportStar",(function(){return d})),n.d(e,"__values",(function(){return g})),n.d(e,"__read",(function(){return p})),n.d(e,"__spread",(function(){return h})),n.d(e,"__await",(function(){return w})),n.d(e,"__asyncGenerator",(function(){return y})),n.d(e,"__asyncDelegator",(function(){return m})),n.d(e,"__asyncValues",(function(){return v})),n.d(e,"__makeTemplateObject",(function(){return b})),n.d(e,"__importStar",(function(){return O})),n.d(e,"__importDefault",(function(){return _}));
/*! *****************************************************************************
Copyright (c) Microsoft Corporation. All rights reserved.
Licensed under the Apache License, Version 2.0 (the "License"); you may not use
this file except in compliance with the License. You may obtain a copy of the
License at http://www.apache.org/licenses/LICENSE-2.0

THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
MERCHANTABLITY OR NON-INFRINGEMENT.

See the Apache Version 2.0 License for specific language governing permissions
and limitations under the License.
***************************************************************************** */
var r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)};function o(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}var i=function(){return(i=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)};function u(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(t);o<r.length;o++)e.indexOf(r[o])<0&&(n[r[o]]=t[r[o]])}return n}function a(t,e,n,r){var o,i=arguments.length,u=i<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)u=Reflect.decorate(t,e,n,r);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(u=(i<3?o(u):i>3?o(e,n,u):o(e,n))||u);return i>3&&u&&Object.defineProperty(e,n,u),u}function c(t,e){return function(n,r){e(n,r,t)}}function s(t,e){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(t,e)}function f(t,e,n,r){return new(n||(n=Promise))((function(o,i){function u(t){try{c(r.next(t))}catch(t){i(t)}}function a(t){try{c(r.throw(t))}catch(t){i(t)}}function c(t){t.done?o(t.value):new n((function(e){e(t.value)})).then(u,a)}c((r=r.apply(t,e||[])).next())}))}function l(t,e){var n,r,o,i,u={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(i){return function(a){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;u;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return u.label++,{value:i[1],done:!1};case 5:u.label++,r=i[1],i=[0];continue;case 7:i=u.ops.pop(),u.trys.pop();continue;default:if(!(o=u.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){u=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){u.label=i[1];break}if(6===i[0]&&u.label<o[1]){u.label=o[1],o=i;break}if(o&&u.label<o[2]){u.label=o[2],u.ops.push(i);break}o[2]&&u.ops.pop(),u.trys.pop();continue}i=e.call(t,u)}catch(t){i=[6,t],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,a])}}}function d(t,e){for(var n in t)e.hasOwnProperty(n)||(e[n]=t[n])}function g(t){var e="function"==typeof Symbol&&t[Symbol.iterator],n=0;return e?e.call(t):{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}}}function p(t,e){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var r,o,i=n.call(t),u=[];try{for(;(void 0===e||e-- >0)&&!(r=i.next()).done;)u.push(r.value)}catch(t){o={error:t}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return u}function h(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(p(arguments[e]));return t}function w(t){return this instanceof w?(this.v=t,this):new w(t)}function y(t,e,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r,o=n.apply(t,e||[]),i=[];return r={},u("next"),u("throw"),u("return"),r[Symbol.asyncIterator]=function(){return this},r;function u(t){o[t]&&(r[t]=function(e){return new Promise((function(n,r){i.push([t,e,n,r])>1||a(t,e)}))})}function a(t,e){try{(n=o[t](e)).value instanceof w?Promise.resolve(n.value.v).then(c,s):f(i[0][2],n)}catch(t){f(i[0][3],t)}var n}function c(t){a("next",t)}function s(t){a("throw",t)}function f(t,e){t(e),i.shift(),i.length&&a(i[0][0],i[0][1])}}function m(t){var e,n;return e={},r("next"),r("throw",(function(t){throw t})),r("return"),e[Symbol.iterator]=function(){return this},e;function r(r,o){e[r]=t[r]?function(e){return(n=!n)?{value:w(t[r](e)),done:"return"===r}:o?o(e):e}:o}}function v(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e,n=t[Symbol.asyncIterator];return n?n.call(t):(t=g(t),e={},r("next"),r("throw"),r("return"),e[Symbol.asyncIterator]=function(){return this},e);function r(n){e[n]=t[n]&&function(e){return new Promise((function(r,o){(function(t,e,n,r){Promise.resolve(r).then((function(e){t({value:e,done:n})}),e)})(r,o,(e=t[n](e)).done,e.value)}))}}}function b(t,e){return Object.defineProperty?Object.defineProperty(t,"raw",{value:e}):t.raw=e,t}function O(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var n in t)Object.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e.default=t,e}function _(t){return t&&t.__esModule?t:{default:t}}},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getClientMeta=e.getLocalStorage=e.getLocalStorageOrThrow=e.getCrypto=e.getCryptoOrThrow=e.getLocation=e.getLocationOrThrow=e.getNavigator=e.getNavigatorOrThrow=e.getDocument=e.getDocumentOrThrow=e.getFromWindowOrThrow=e.getFromWindow=e.isBrowser=e.isNode=e.isMobile=e.isIOS=e.isAndroid=e.detectOS=e.detectEnv=void 0;const r=n(0),o=r.__importStar(n(6)),i=r.__importStar(n(2)),u=n(7);function a(t){return(0,u.detect)(t)}function c(){const t=a();return t&&t.os?t.os:void 0}function s(){const t=c();return!!t&&t.toLowerCase().includes("android")}function f(){const t=c();return!!t&&(t.toLowerCase().includes("ios")||t.toLowerCase().includes("mac")&&navigator.maxTouchPoints>1)}function l(){const t=a();return!(!t||!t.name)&&"node"===t.name.toLowerCase()}e.detectEnv=a,e.detectOS=c,e.isAndroid=s,e.isIOS=f,e.isMobile=function(){return!!c()&&(s()||f())},e.isNode=l,e.isBrowser=function(){return!l()&&!!(0,e.getNavigator)()},e.getFromWindow=i.getFromWindow,e.getFromWindowOrThrow=i.getFromWindowOrThrow,e.getDocumentOrThrow=i.getDocumentOrThrow,e.getDocument=i.getDocument,e.getNavigatorOrThrow=i.getNavigatorOrThrow,e.getNavigator=i.getNavigator,e.getLocationOrThrow=i.getLocationOrThrow,e.getLocation=i.getLocation,e.getCryptoOrThrow=i.getCryptoOrThrow,e.getCrypto=i.getCrypto,e.getLocalStorageOrThrow=i.getLocalStorageOrThrow,e.getLocalStorage=i.getLocalStorage,e.getClientMeta=function(){return o.getWindowMetadata()}},function(t,e,n){"use strict";function r(t){let e=void 0;return"undefined"!=typeof window&&void 0!==window[t]&&(e=window[t]),e}function o(t){const e=r(t);if(!e)throw new Error(t+" is not defined in Window");return e}Object.defineProperty(e,"__esModule",{value:!0}),e.getLocalStorage=e.getLocalStorageOrThrow=e.getCrypto=e.getCryptoOrThrow=e.getLocation=e.getLocationOrThrow=e.getNavigator=e.getNavigatorOrThrow=e.getDocument=e.getDocumentOrThrow=e.getFromWindowOrThrow=e.getFromWindow=void 0,e.getFromWindow=r,e.getFromWindowOrThrow=o,e.getDocumentOrThrow=function(){return o("document")},e.getDocument=function(){return r("document")},e.getNavigatorOrThrow=function(){return o("navigator")},e.getNavigator=function(){return r("navigator")},e.getLocationOrThrow=function(){return o("location")},e.getLocation=function(){return r("location")},e.getCryptoOrThrow=function(){return o("crypto")},e.getCrypto=function(){return r("crypto")},e.getLocalStorageOrThrow=function(){return o("localStorage")},e.getLocalStorage=function(){return r("localStorage")}},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.safeJsonStringify=e.safeJsonParse=void 0;const r=n(0).__importStar(n(9));e.safeJsonParse=r.safeJsonParse,e.safeJsonStringify=r.safeJsonStringify},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.removeLocal=e.getLocal=e.setLocal=void 0;const r=n(3),o=n(1);e.setLocal=function(t,e){const n=(0,r.safeJsonStringify)(e),i=(0,o.getLocalStorage)();i&&i.setItem(t,n)},e.getLocal=function(t){let e=null,n=null;const i=(0,o.getLocalStorage)();return i&&(n=i.getItem(t)),e=n?(0,r.safeJsonParse)(n):n,e},e.removeLocal=function(t){const e=(0,o.getLocalStorage)();e&&e.removeItem(t)}},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});const r=n(0);r.__exportStar(n(1),e),r.__exportStar(n(3),e),r.__exportStar(n(4),e),r.__exportStar(n(10),e),r.__exportStar(n(11),e)},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getWindowMetadata=void 0;const r=n(2);e.getWindowMetadata=function(){let t,e;try{t=r.getDocumentOrThrow(),e=r.getLocationOrThrow()}catch(t){return null}function n(...e){const n=t.getElementsByTagName("meta");for(let t=0;t<n.length;t++){const r=n[t],o=["itemprop","property","name"].map(t=>r.getAttribute(t)).filter(t=>!!t&&e.includes(t));if(o.length&&o){const t=r.getAttribute("content");if(t)return t}}return""}const o=function(){let e=n("name","og:site_name","og:title","twitter:title");return e||(e=t.title),e}();return{description:n("description","og:description","twitter:description","keywords"),url:e.origin,icons:function(){const n=t.getElementsByTagName("link"),r=[];for(let t=0;t<n.length;t++){const o=n[t],i=o.getAttribute("rel");if(i&&i.toLowerCase().indexOf("icon")>-1){const t=o.getAttribute("href");if(t)if(-1===t.toLowerCase().indexOf("https:")&&-1===t.toLowerCase().indexOf("http:")&&0!==t.indexOf("//")){let n=e.protocol+"//"+e.host;if(0===t.indexOf("/"))n+=t;else{const r=e.pathname.split("/");r.pop();n+=r.join("/")+"/"+t}r.push(n)}else if(0===t.indexOf("//")){const n=e.protocol+t;r.push(n)}else r.push(t)}}return r}(),name:o}}},function(t,e,n){"use strict";n.r(e),function(t){n.d(e,"BrowserInfo",(function(){return o})),n.d(e,"NodeInfo",(function(){return i})),n.d(e,"SearchBotDeviceInfo",(function(){return u})),n.d(e,"BotInfo",(function(){return a})),n.d(e,"ReactNativeInfo",(function(){return c})),n.d(e,"detect",(function(){return d})),n.d(e,"browserName",(function(){return p})),n.d(e,"parseUserAgent",(function(){return h})),n.d(e,"detectOS",(function(){return w})),n.d(e,"getNodeVersion",(function(){return y}));var r=function(){for(var t=0,e=0,n=arguments.length;e<n;e++)t+=arguments[e].length;var r=Array(t),o=0;for(e=0;e<n;e++)for(var i=arguments[e],u=0,a=i.length;u<a;u++,o++)r[o]=i[u];return r},o=function(t,e,n){this.name=t,this.version=e,this.os=n,this.type="browser"},i=function(e){this.version=e,this.type="node",this.name="node",this.os=t.platform},u=function(t,e,n,r){this.name=t,this.version=e,this.os=n,this.bot=r,this.type="bot-device"},a=function(){this.type="bot",this.bot=!0,this.name="bot",this.version=null,this.os=null},c=function(){this.type="react-native",this.name="react-native",this.version=null,this.os=null},s=/(nuhk|Googlebot|Yammybot|Openbot|Slurp|MSNBot|Ask\ Jeeves\/Teoma|ia_archiver)/,f=[["aol",/AOLShield\/([0-9\._]+)/],["edge",/Edge\/([0-9\._]+)/],["edge-ios",/EdgiOS\/([0-9\._]+)/],["yandexbrowser",/YaBrowser\/([0-9\._]+)/],["kakaotalk",/KAKAOTALK\s([0-9\.]+)/],["samsung",/SamsungBrowser\/([0-9\.]+)/],["silk",/\bSilk\/([0-9._-]+)\b/],["miui",/MiuiBrowser\/([0-9\.]+)$/],["beaker",/BeakerBrowser\/([0-9\.]+)/],["edge-chromium",/EdgA?\/([0-9\.]+)/],["chromium-webview",/(?!Chrom.*OPR)wv\).*Chrom(?:e|ium)\/([0-9\.]+)(:?\s|$)/],["chrome",/(?!Chrom.*OPR)Chrom(?:e|ium)\/([0-9\.]+)(:?\s|$)/],["phantomjs",/PhantomJS\/([0-9\.]+)(:?\s|$)/],["crios",/CriOS\/([0-9\.]+)(:?\s|$)/],["firefox",/Firefox\/([0-9\.]+)(?:\s|$)/],["fxios",/FxiOS\/([0-9\.]+)/],["opera-mini",/Opera Mini.*Version\/([0-9\.]+)/],["opera",/Opera\/([0-9\.]+)(?:\s|$)/],["opera",/OPR\/([0-9\.]+)(:?\s|$)/],["ie",/Trident\/7\.0.*rv\:([0-9\.]+).*\).*Gecko$/],["ie",/MSIE\s([0-9\.]+);.*Trident\/[4-7].0/],["ie",/MSIE\s(7\.0)/],["bb10",/BB10;\sTouch.*Version\/([0-9\.]+)/],["android",/Android\s([0-9\.]+)/],["ios",/Version\/([0-9\._]+).*Mobile.*Safari.*/],["safari",/Version\/([0-9\._]+).*Safari/],["facebook",/FBAV\/([0-9\.]+)/],["instagram",/Instagram\s([0-9\.]+)/],["ios-webview",/AppleWebKit\/([0-9\.]+).*Mobile/],["ios-webview",/AppleWebKit\/([0-9\.]+).*Gecko\)$/],["searchbot",/alexa|bot|crawl(er|ing)|facebookexternalhit|feedburner|google web preview|nagios|postrank|pingdom|slurp|spider|yahoo!|yandex/]],l=[["iOS",/iP(hone|od|ad)/],["Android OS",/Android/],["BlackBerry OS",/BlackBerry|BB10/],["Windows Mobile",/IEMobile/],["Amazon OS",/Kindle/],["Windows 3.11",/Win16/],["Windows 95",/(Windows 95)|(Win95)|(Windows_95)/],["Windows 98",/(Windows 98)|(Win98)/],["Windows 2000",/(Windows NT 5.0)|(Windows 2000)/],["Windows XP",/(Windows NT 5.1)|(Windows XP)/],["Windows Server 2003",/(Windows NT 5.2)/],["Windows Vista",/(Windows NT 6.0)/],["Windows 7",/(Windows NT 6.1)/],["Windows 8",/(Windows NT 6.2)/],["Windows 8.1",/(Windows NT 6.3)/],["Windows 10",/(Windows NT 10.0)/],["Windows ME",/Windows ME/],["Open BSD",/OpenBSD/],["Sun OS",/SunOS/],["Chrome OS",/CrOS/],["Linux",/(Linux)|(X11)/],["Mac OS",/(Mac_PowerPC)|(Macintosh)/],["QNX",/QNX/],["BeOS",/BeOS/],["OS/2",/OS\/2/]];function d(t){return t?h(t):"undefined"==typeof document&&"undefined"!=typeof navigator&&"ReactNative"===navigator.product?new c:"undefined"!=typeof navigator?h(navigator.userAgent):y()}function g(t){return""!==t&&f.reduce((function(e,n){var r=n[0],o=n[1];if(e)return e;var i=o.exec(t);return!!i&&[r,i]}),!1)}function p(t){var e=g(t);return e?e[0]:null}function h(t){var e=g(t);if(!e)return null;var n=e[0],i=e[1];if("searchbot"===n)return new a;var c=i[1]&&i[1].split(/[._]/).slice(0,3);c?c.length<3&&(c=r(c,function(t){for(var e=[],n=0;n<t;n++)e.push("0");return e}(3-c.length))):c=[];var f=c.join("."),l=w(t),d=s.exec(t);return d&&d[1]?new u(n,f,l,d[1]):new o(n,f,l)}function w(t){for(var e=0,n=l.length;e<n;e++){var r=l[e],o=r[0];if(r[1].exec(t))return o}return null}function y(){return void 0!==t&&t.version?new i(t.version.slice(1)):null}}.call(this,n(8))},function(t,e){var n,r,o=t.exports={};function i(){throw new Error("setTimeout has not been defined")}function u(){throw new Error("clearTimeout has not been defined")}function a(t){if(n===setTimeout)return setTimeout(t,0);if((n===i||!n)&&setTimeout)return n=setTimeout,setTimeout(t,0);try{return n(t,0)}catch(e){try{return n.call(null,t,0)}catch(e){return n.call(this,t,0)}}}!function(){try{n="function"==typeof setTimeout?setTimeout:i}catch(t){n=i}try{r="function"==typeof clearTimeout?clearTimeout:u}catch(t){r=u}}();var c,s=[],f=!1,l=-1;function d(){f&&c&&(f=!1,c.length?s=c.concat(s):l=-1,s.length&&g())}function g(){if(!f){var t=a(d);f=!0;for(var e=s.length;e;){for(c=s,s=[];++l<e;)c&&c[l].run();l=-1,e=s.length}c=null,f=!1,function(t){if(r===clearTimeout)return clearTimeout(t);if((r===u||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(t);try{r(t)}catch(e){try{return r.call(null,t)}catch(e){return r.call(this,t)}}}(t)}}function p(t,e){this.fun=t,this.array=e}function h(){}o.nextTick=function(t){var e=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)e[n-1]=arguments[n];s.push(new p(t,e)),1!==s.length||f||a(g)},p.prototype.run=function(){this.fun.apply(null,this.array)},o.title="browser",o.browser=!0,o.env={},o.argv=[],o.version="",o.versions={},o.on=h,o.addListener=h,o.once=h,o.off=h,o.removeListener=h,o.removeAllListeners=h,o.emit=h,o.prependListener=h,o.prependOnceListener=h,o.listeners=function(t){return[]},o.binding=function(t){throw new Error("process.binding is not supported")},o.cwd=function(){return"/"},o.chdir=function(t){throw new Error("process.chdir is not supported")},o.umask=function(){return 0}},function(t,e,n){"use strict";function r(t){if("string"!=typeof t)throw new Error("Cannot safe json parse value of type "+typeof t);try{return JSON.parse(t)}catch(e){return t}}function o(t){return"string"==typeof t?t:JSON.stringify(t)}n.r(e),n.d(e,"safeJsonParse",(function(){return r})),n.d(e,"safeJsonStringify",(function(){return o}))},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getMobileLinkRegistry=e.getMobileRegistryEntry=e.saveMobileLinkInfo=e.formatIOSMobile=e.mobileLinkChoiceKey=void 0;const r=n(4);function o(t,e){return t.filter(t=>t.name.toLowerCase().includes(e.toLowerCase()))[0]}e.mobileLinkChoiceKey="WALLETCONNECT_DEEPLINK_CHOICE",e.formatIOSMobile=function(t,e){const n=encodeURIComponent(t);return e.universalLink?`${e.universalLink}/wc?uri=${n}`:e.deepLink?`${e.deepLink}${e.deepLink.endsWith(":")?"//":"/"}wc?uri=${n}`:""},e.saveMobileLinkInfo=function(t){const n=t.href.split("?")[0];(0,r.setLocal)(e.mobileLinkChoiceKey,Object.assign(Object.assign({},t),{href:n}))},e.getMobileRegistryEntry=o,e.getMobileLinkRegistry=function(t,e){let n=t;return e&&(n=e.map(e=>o(t,e)).filter(Boolean)),n}},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.formatMobileRegistry=e.formatMobileRegistryEntry=e.getDappRegistryUrl=e.getWalletRegistryUrl=void 0;const r="https://registry.walletconnect.com";function o(t,e="mobile"){var n;return{name:t.name||"",shortName:t.metadata.shortName||"",color:t.metadata.colors.primary||"",logo:null!==(n=t.image_url.sm)&&void 0!==n?n:"",universalLink:t[e].universal||"",deepLink:t[e].native||""}}e.getWalletRegistryUrl=function(){return r+"/api/v2/wallets"},e.getDappRegistryUrl=function(){return r+"/api/v2/dapps"},e.formatMobileRegistryEntry=o,e.formatMobileRegistry=function(t,e="mobile"){return Object.values(t).filter(t=>!!t[e].universal||!!t[e].native).map(t=>o(t,e))}}])}));