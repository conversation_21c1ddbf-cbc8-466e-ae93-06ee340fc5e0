function t(t,e){-1===t.indexOf(e)&&t.push(e)}function e(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const n=(t,e,n)=>Math.min(Math.max(n,t),e),i={duration:.3,delay:0,endDelay:0,repeat:0,easing:"ease"},r=t=>"number"==typeof t,o=t=>Array.isArray(t)&&!r(t[0]);function s(t,e){return o(t)?t[((t,e,n)=>{const i=e-t;return((n-t)%i+i)%i+t})(0,t.length,e)]:t}const a=(t,e,n)=>-n*t+n*e+t,c=()=>{},l=t=>t,u=(t,e,n)=>e-t==0?1:(n-t)/(e-t);function f(t,e){const n=t[t.length-1];for(let i=1;i<=e;i++){const r=u(0,e,i);t.push(a(n,1,r))}}function h(t){const e=[0];return f(e,t-1),e}function d(t,e=h(t.length),i=l){const r=t.length,o=r-e.length;return o>0&&f(e,o),o=>{let c=0;for(;c<r-2&&!(o<e[c+1]);c++);let l=n(0,1,u(e[c],e[c+1],o));return l=s(i,c)(l),a(t[c],t[c+1],l)}}const p=t=>Array.isArray(t)&&r(t[0]),g=t=>"object"==typeof t&&Boolean(t.createAnimation),m=t=>"function"==typeof t,v=t=>"string"==typeof t,y=t=>1e3*t,w=t=>t/1e3;function b(t,e){return e?t*(1e3/e):0}const E=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t;function O(t,e,n,i){if(t===e&&n===i)return l;const r=e=>function(t,e,n,i,r){let o,s,a=0;do{s=e+(n-e)/2,o=E(s,i,r)-t,o>0?n=s:e=s}while(Math.abs(o)>1e-7&&++a<12);return s}(e,0,1,t,n);return t=>0===t||1===t?t:E(r(t),e,i)}const x={ease:O(.25,.1,.25,1),"ease-in":O(.42,0,1,1),"ease-in-out":O(.42,0,.58,1),"ease-out":O(0,0,.58,1)},T=/\((.*?)\)/;function M(t){if(m(t))return t;if(p(t))return O(...t);if(x[t])return x[t];if(t.startsWith("steps")){const e=T.exec(t);if(e){const t=e[1].split(",");return((t,e="end")=>i=>{const r=(i="end"===e?Math.min(i,.999):Math.max(i,.001))*t,o="end"===e?Math.floor(r):Math.ceil(r);return n(0,1,o/t)})(parseFloat(t[0]),t[1].trim())}}return l}class A{constructor(t,e=[0,1],{easing:n,duration:r=i.duration,delay:s=i.delay,endDelay:a=i.endDelay,repeat:c=i.repeat,offset:u,direction:f="normal"}={}){if(this.startTime=null,this.rate=1,this.t=0,this.cancelTimestamp=null,this.easing=l,this.duration=0,this.totalDuration=0,this.repeat=0,this.playState="idle",this.finished=new Promise(((t,e)=>{this.resolve=t,this.reject=e})),n=n||i.easing,g(n)){const t=n.createAnimation(e);n=t.easing,e=t.keyframes||e,r=t.duration||r}this.repeat=c,this.easing=o(n)?l:M(n),this.updateDuration(r);const h=d(e,u,o(n)?n.map(M):l);this.tick=e=>{var n;let i=0;i=void 0!==this.pauseTime?this.pauseTime:(e-this.startTime)*this.rate,this.t=i,i/=1e3,i=Math.max(i-s,0),"finished"===this.playState&&void 0===this.pauseTime&&(i=this.totalDuration);const r=i/this.duration;let o=Math.floor(r),c=r%1;!c&&r>=1&&(c=1),1===c&&o--;const l=o%2;("reverse"===f||"alternate"===f&&l||"alternate-reverse"===f&&!l)&&(c=1-c);const u=i>=this.totalDuration?1:Math.min(c,1),d=h(this.easing(u));t(d);void 0===this.pauseTime&&("finished"===this.playState||i>=this.totalDuration+a)?(this.playState="finished",null===(n=this.resolve)||void 0===n||n.call(this,d)):"idle"!==this.playState&&(this.frameRequestId=requestAnimationFrame(this.tick))},this.play()}play(){const t=performance.now();this.playState="running",void 0!==this.pauseTime?this.startTime=t-this.pauseTime:this.startTime||(this.startTime=t),this.cancelTimestamp=this.startTime,this.pauseTime=void 0,this.frameRequestId=requestAnimationFrame(this.tick)}pause(){this.playState="paused",this.pauseTime=this.t}finish(){this.playState="finished",this.tick(0)}stop(){var t;this.playState="idle",void 0!==this.frameRequestId&&cancelAnimationFrame(this.frameRequestId),null===(t=this.reject)||void 0===t||t.call(this,!1)}cancel(){this.stop(),this.tick(this.cancelTimestamp)}reverse(){this.rate*=-1}commitStyles(){}updateDuration(t){this.duration=t,this.totalDuration=t*(this.repeat+1)}get currentTime(){return this.t}set currentTime(t){void 0!==this.pauseTime||0===this.rate?this.pauseTime=t:this.startTime=performance.now()-t/this.rate}get playbackRate(){return this.rate}set playbackRate(t){this.rate=t}}var S=function(){};"production"!==process.env.NODE_ENV&&(S=function(t,e){if(!t)throw new Error(e)});class D{setAnimation(t){this.animation=t,null==t||t.finished.then((()=>this.clearAnimation())).catch((()=>{}))}clearAnimation(){this.animation=this.generator=void 0}}const L=new WeakMap;function k(t){return L.has(t)||L.set(t,{transforms:[],values:new Map}),L.get(t)}const W=["","X","Y","Z"],j={x:"translateX",y:"translateY",z:"translateZ"},B={syntax:"<angle>",initialValue:"0deg",toDefaultUnit:t=>t+"deg"},R={translate:{syntax:"<length-percentage>",initialValue:"0px",toDefaultUnit:t=>t+"px"},rotate:B,scale:{syntax:"<number>",initialValue:1,toDefaultUnit:l},skew:B},z=new Map,P=t=>`--motion-${t}`,V=["x","y","z"];["translate","scale","rotate","skew"].forEach((t=>{W.forEach((e=>{V.push(t+e),z.set(P(t+e),R[t])}))}));const $=(t,e)=>V.indexOf(t)-V.indexOf(e),q=new Set(V),F=t=>q.has(t),C=t=>t.sort($).reduce(H,"").trim(),H=(t,e)=>`${t} ${e}(var(${P(e)}))`,N=t=>t.startsWith("--"),I=new Set;const U=(t,e)=>document.createElement("div").animate(t,e),_={cssRegisterProperty:()=>"undefined"!=typeof CSS&&Object.hasOwnProperty.call(CSS,"registerProperty"),waapi:()=>Object.hasOwnProperty.call(Element.prototype,"animate"),partialKeyframes:()=>{try{U({opacity:[1]})}catch(t){return!1}return!0},finished:()=>Boolean(U({opacity:[0,1]},{duration:.001}).finished),linearEasing:()=>{try{U({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0}},G={},Z={};for(const t in _)Z[t]=()=>(void 0===G[t]&&(G[t]=_[t]()),G[t]);const K=(t,e)=>m(t)?Z.linearEasing()?`linear(${((t,e)=>{let n="";const i=Math.round(e/.015);for(let e=0;e<i;e++)n+=t(u(0,i-1,e))+", ";return n.substring(0,n.length-2)})(t,e)})`:i.easing:p(t)?X(t):t,X=([t,e,n,i])=>`cubic-bezier(${t}, ${e}, ${n}, ${i})`;const Y=t=>Array.isArray(t)?t:[t];function J(t){return j[t]&&(t=j[t]),F(t)?P(t):t}const Q={get:(t,e)=>{e=J(e);let n=N(e)?t.style.getPropertyValue(e):getComputedStyle(t)[e];if(!n&&0!==n){const t=z.get(e);t&&(n=t.initialValue)}return n},set:(t,e,n)=>{e=J(e),N(e)?t.style.setProperty(e,n):t.style[e]=n}};function tt(t,e=!0){if(t&&"finished"!==t.playState)try{t.stop?t.stop():(e&&t.commitStyles(),t.cancel())}catch(t){}}function et(t,e){var n;let i=(null==e?void 0:e.toDefaultUnit)||l;const r=t[t.length-1];if(v(r)){const t=(null===(n=r.match(/(-?[\d.]+)([a-z%]*)/))||void 0===n?void 0:n[2])||"";t&&(i=e=>e+t)}return i}function nt(e,n,s,a={},l){const u=window.__MOTION_DEV_TOOLS_RECORD,f=!1!==a.record&&u;let h,{duration:d=i.duration,delay:p=i.delay,endDelay:v=i.endDelay,repeat:w=i.repeat,easing:b=i.easing,persist:E=!1,direction:O,offset:x,allowWebkitAcceleration:T=!1}=a;const M=k(e),A=F(n);let S=Z.waapi();A&&((e,n)=>{j[n]&&(n=j[n]);const{transforms:i}=k(e);t(i,n),e.style.transform=C(i)})(e,n);const L=J(n),W=function(t,e){return t.has(e)||t.set(e,new D),t.get(e)}(M.values,L),B=z.get(L);return tt(W.animation,!(g(b)&&W.generator)&&!1!==a.record),()=>{const t=()=>{var t,n;return null!==(n=null!==(t=Q.get(e,L))&&void 0!==t?t:null==B?void 0:B.initialValue)&&void 0!==n?n:0};let i=function(t,e){for(let n=0;n<t.length;n++)null===t[n]&&(t[n]=n?t[n-1]:e());return t}(Y(s),t);const M=et(i,B);if(g(b)){const e=b.createAnimation(i,"opacity"!==n,t,L,W);b=e.easing,i=e.keyframes||i,d=e.duration||d}if(N(L)&&(Z.cssRegisterProperty()?function(t){if(!I.has(t)){I.add(t);try{const{syntax:e,initialValue:n}=z.has(t)?z.get(t):{};CSS.registerProperty({name:t,inherits:!1,syntax:e,initialValue:n})}catch(t){}}}(L):S=!1),A&&!Z.linearEasing()&&(m(b)||o(b)&&b.some(m))&&(S=!1),S){B&&(i=i.map((t=>r(t)?B.toDefaultUnit(t):t))),1!==i.length||Z.partialKeyframes()&&!f||i.unshift(t());const n={delay:y(p),duration:y(d),endDelay:y(v),easing:o(b)?void 0:K(b,d),direction:O,iterations:w+1,fill:"both"};h=e.animate({[L]:i,offset:x,easing:o(b)?b.map((t=>K(t,d))):void 0},n),h.finished||(h.finished=new Promise(((t,e)=>{h.onfinish=t,h.oncancel=e})));const s=i[i.length-1];h.finished.then((()=>{E||(Q.set(e,L,s),h.cancel())})).catch(c),T||(h.playbackRate=1.000001)}else if(l&&A)i=i.map((t=>"string"==typeof t?parseFloat(t):t)),1===i.length&&i.unshift(parseFloat(t())),h=new l((t=>{Q.set(e,L,M?M(t):t)}),i,Object.assign(Object.assign({},a),{duration:d,easing:b}));else{const t=i[i.length-1];Q.set(e,L,B&&r(t)?B.toDefaultUnit(t):t)}return f&&u(e,n,i,{duration:d,delay:p,easing:b,repeat:w,offset:x},"motion-one"),W.setAnimation(h),h}}const it=(t,e)=>t[e]?Object.assign(Object.assign({},t),t[e]):Object.assign({},t);function rt(t,e){var n;return"string"==typeof t?e?(null!==(n=e[t])&&void 0!==n||(e[t]=document.querySelectorAll(t)),t=e[t]):t=document.querySelectorAll(t):t instanceof Element&&(t=[t]),Array.from(t||[])}const ot=t=>t(),st=(t,e,n=i.duration)=>new Proxy({animations:t.map(ot).filter(Boolean),duration:n,options:e},at),at={get:(t,e)=>{const n=t.animations[0];switch(e){case"duration":return t.duration;case"currentTime":return w((null==n?void 0:n[e])||0);case"playbackRate":case"playState":return null==n?void 0:n[e];case"finished":return t.finished||(t.finished=Promise.all(t.animations.map(ct)).catch(c)),t.finished;case"stop":return()=>{t.animations.forEach((t=>tt(t)))};case"forEachNative":return e=>{t.animations.forEach((n=>e(n,t)))};default:return void 0===(null==n?void 0:n[e])?void 0:()=>t.animations.forEach((t=>t[e]()))}},set:(t,e,n)=>{switch(e){case"currentTime":n=y(n);case"currentTime":case"playbackRate":for(let i=0;i<t.animations.length;i++)t.animations[i][e]=n;return!0}return!1}},ct=t=>t.finished;function lt(t=.1,{start:e=0,from:n=0,easing:i}={}){return(o,s)=>{const a=r(n)?n:function(t,e){if("first"===t)return 0;{const n=e-1;return"last"===t?n:n/2}}(n,s),c=Math.abs(a-o);let l=t*c;if(i){const e=s*t;l=M(i)(l/e)*e}return e+l}}function ut(t,e,n){return m(t)?t(e,n):t}function ft(t){return function(e,n,i={}){const r=(e=rt(e)).length;S(Boolean(r),"No valid element provided."),S(Boolean(n),"No keyframes defined.");const o=[];for(let s=0;s<r;s++){const a=e[s];for(const e in n){const c=it(i,e);c.delay=ut(c.delay,s,r);const l=nt(a,e,n[e],c,t);o.push(l)}}return st(o,i,i.duration)}}const ht=ft(A);function dt(t,e){var n={};for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&e.indexOf(i)<0&&(n[i]=t[i]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(i=Object.getOwnPropertySymbols(t);r<i.length;r++)e.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(t,i[r])&&(n[i[r]]=t[i[r]])}return n}function pt(t,e,n,i){var o;return r(e)?e:e.startsWith("-")||e.startsWith("+")?Math.max(0,t+parseFloat(e)):"<"===e?n:null!==(o=i.get(e))&&void 0!==o?o:t}function gt(t,n,i,r,o,c){!function(t,n,i){for(let r=0;r<t.length;r++){const o=t[r];o.at>n&&o.at<i&&(e(t,o),r--)}}(t,o,c);for(let e=0;e<n.length;e++)t.push({value:n[e],at:a(o,c,r[e]),easing:s(i,e)})}function mt(t,e){return t.at===e.at?null===t.value?1:-1:t.at-e.at}function vt(t,e={}){var n;const r=function(t,e={}){var{defaultOptions:n={}}=e,r=dt(e,["defaultOptions"]);const o=[],s=new Map,a={},c=new Map;let l=0,d=0,p=0;for(let e=0;e<t.length;e++){const r=t[e];if(v(r)){c.set(r,d);continue}if(!Array.isArray(r)){c.set(r.name,pt(d,r.at,l,c));continue}const[o,u,m={}]=r;void 0!==m.at&&(d=pt(d,m.at,l,c));let y=0;const w=rt(o,a),b=w.length;for(let t=0;t<b;t++){const e=yt(w[t],s);for(const r in u){const o=wt(r,e);let s=Y(u[r]);const a=it(m,r);let{duration:c=n.duration||i.duration,easing:l=n.easing||i.easing}=a;if(g(l)){S("opacity"===r||s.length>1,"spring must be provided 2 keyframes within timeline()");const t=l.createAnimation(s,"opacity"!==r,(()=>0),r);l=t.easing,s=t.keyframes||s,c=t.duration||c}const v=ut(m.delay,t,b)||0,w=d+v,E=w+c;let{offset:O=h(s.length)}=a;1===O.length&&0===O[0]&&(O[1]=1);const x=O.length-s.length;x>0&&f(O,x),1===s.length&&s.unshift(null),gt(o,s,l,O,w,E),y=Math.max(v+c,y),p=Math.max(E,p)}}l=d,d+=y}return s.forEach(((t,e)=>{for(const s in t){const a=t[s];a.sort(mt);const c=[],l=[],f=[];for(let t=0;t<a.length;t++){const{at:e,value:n,easing:r}=a[t];c.push(n),l.push(u(0,p,e)),f.push(r||i.easing)}0!==l[0]&&(l.unshift(0),c.unshift(c[0]),f.unshift("linear")),1!==l[l.length-1]&&(l.push(1),c.push(null)),o.push([e,s,c,Object.assign(Object.assign(Object.assign({},n),{duration:p,easing:f,offset:l}),r)])}})),o}(t,e),o=r.map((t=>nt(...t,A))).filter(Boolean);return st(o,e,null===(n=r[0])||void 0===n?void 0:n[3].duration)}function yt(t,e){return!e.has(t)&&e.set(t,{}),e.get(t)}function wt(t,e){return e[t]||(e[t]=[]),e[t]}function bt(t,e,n){const i=Math.max(e-5,0);return b(n-t(i),e-i)}const Et=100,Ot=10,xt=1;const Tt=({stiffness:t=Et,damping:e=Ot,mass:n=xt,from:i=0,to:r=1,velocity:o=0,restSpeed:s=2,restDistance:a=.5}={})=>{o=o?w(o):0;const c={done:!1,hasReachedTarget:!1,current:i,target:r},l=r-i,u=Math.sqrt(t/n)/1e3,f=((t=Et,e=Ot,n=xt)=>e/(2*Math.sqrt(t*n)))(t,e,n);let h;if(f<1){const t=u*Math.sqrt(1-f*f);h=e=>r-Math.exp(-f*u*e)*((f*u*l-o)/t*Math.sin(t*e)+l*Math.cos(t*e))}else h=t=>r-Math.exp(-u*t)*(l+(u*l-o)*t);return t=>{c.current=h(t);const e=0===t?o:bt(h,t,c.current),n=Math.abs(e)<=s,l=Math.abs(r-c.current)<=a;var u,f,d;return c.done=n&&l,c.hasReachedTarget=(u=i,f=r,d=c.current,u<f&&d>=f||u>f&&d<=f),c}};function Mt(t){return r(t)&&!isNaN(t)}function At(t){return v(t)?parseFloat(t):t}function St(t){const e=new WeakMap;return(n={})=>{const i=new Map,r=(e=0,r=100,o=0,s=!1)=>{const a=`${e}-${r}-${o}-${s}`;return i.has(a)||i.set(a,t(Object.assign({from:e,to:r,velocity:o,restSpeed:s?.05:2,restDistance:s?.01:.5},n))),i.get(a)},o=(t,n)=>(e.has(t)||e.set(t,function(t,e=l){let n,i=10,r=t(0);const o=[e(r.current)];for(;!r.done&&i<1e4;)r=t(i),o.push(e(r.done?r.target:r.current)),void 0===n&&r.hasReachedTarget&&(n=i),i+=10;const s=i-10;return 1===o.length&&o.push(r.current),{keyframes:o,duration:s/1e3,overshootDuration:(null!=n?n:s)/1e3}}(t,n)),e.get(t));return{createAnimation:(t,e=!0,n,i,s)=>{let a,c,u,f=0,h=l;const d=t.length;if(e){h=et(t,i?z.get(J(i)):void 0);if(u=At(t[d-1]),d>1&&null!==t[0])c=At(t[0]);else{const t=null==s?void 0:s.generator;if(t){const{animation:e,generatorStartTime:n}=s,i=(null==e?void 0:e.startTime)||n||0,r=(null==e?void 0:e.currentTime)||performance.now()-i,o=t(r).current;c=o,f=bt((e=>t(e).current),r,o)}else n&&(c=At(n()))}}if(Mt(c)&&Mt(u)){const t=r(c,u,f,null==i?void 0:i.includes("scale"));a=Object.assign(Object.assign({},o(t,h)),{easing:"linear"}),s&&(s.generator=t,s.generatorStartTime=performance.now())}if(!a){a={easing:"ease",duration:o(r(0,100)).overshootDuration}}return a}}}}const Dt=St(Tt),Lt=St((({from:t=0,velocity:e=0,power:n=.8,decay:i=.325,bounceDamping:r,bounceStiffness:o,changeTarget:s,min:a,max:c,restDistance:l=.5,restSpeed:u})=>{i=y(i);const f={hasReachedTarget:!1,done:!1,current:t,target:t},h=t=>void 0===a?c:void 0===c||Math.abs(a-t)<Math.abs(c-t)?a:c;let d=n*e;const p=t+d,g=void 0===s?p:s(p);f.target=g,g!==p&&(d=g-t);const m=t=>-d*Math.exp(-t/i),v=t=>g+m(t),w=t=>{const e=m(t),n=v(t);f.done=Math.abs(e)<=l,f.current=f.done?g:n};let b,E;const O=t=>{var e;(e=f.current,void 0!==a&&e<a||void 0!==c&&e>c)&&(b=t,E=Tt({from:f.current,to:h(f.current),velocity:bt(v,t,f.current),damping:r,stiffness:o,restDistance:l,restSpeed:u}))};return O(0),t=>{let e=!1;return E||void 0!==b||(e=!0,w(t),O(t)),void 0!==b&&t>b?(f.hasReachedTarget=!0,E(t-b)):(f.hasReachedTarget=!1,!e&&w(t),f)}})),kt={any:0,all:1};function Wt(t,e,{root:n,margin:i,amount:r="any"}={}){if("undefined"==typeof IntersectionObserver)return()=>{};const o=rt(t),s=new WeakMap,a=new IntersectionObserver((t=>{t.forEach((t=>{const n=s.get(t.target);if(t.isIntersecting!==Boolean(n))if(t.isIntersecting){const n=e(t);m(n)?s.set(t.target,n):a.unobserve(t.target)}else n&&(n(t),s.delete(t.target))}))}),{root:n,rootMargin:i,threshold:"number"==typeof r?r:kt[r]});return o.forEach((t=>a.observe(t))),()=>a.disconnect()}const jt=new WeakMap;let Bt;function Rt({target:t,contentRect:e,borderBoxSize:n}){var i;null===(i=jt.get(t))||void 0===i||i.forEach((i=>{i({target:t,contentSize:e,get size(){return function(t,e){if(e){const{inlineSize:t,blockSize:n}=e[0];return{width:t,height:n}}return t instanceof SVGElement&&"getBBox"in t?t.getBBox():{width:t.offsetWidth,height:t.offsetHeight}}(t,n)}})}))}function zt(t){t.forEach(Rt)}function Pt(t,e){Bt||"undefined"!=typeof ResizeObserver&&(Bt=new ResizeObserver(zt));const n=rt(t);return n.forEach((t=>{let n=jt.get(t);n||(n=new Set,jt.set(t,n)),n.add(e),null==Bt||Bt.observe(t)})),()=>{n.forEach((t=>{const n=jt.get(t);null==n||n.delete(e),(null==n?void 0:n.size)||null==Bt||Bt.unobserve(t)}))}}const Vt=new Set;let $t;function qt(t){return Vt.add(t),$t||($t=()=>{const t={width:window.innerWidth,height:window.innerHeight},e={target:window,size:t,contentSize:t};Vt.forEach((t=>t(e)))},window.addEventListener("resize",$t)),()=>{Vt.delete(t),!Vt.size&&$t&&($t=void 0)}}function Ft(t,e){return m(t)?qt(t):Pt(t,e)}const Ct={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};function Ht(t,e,n,i){const r=n[e],{length:o,position:s}=Ct[e],a=r.current,c=n.time;r.current=t["scroll"+s],r.scrollLength=t["scroll"+o]-t["client"+o],r.offset.length=0,r.offset[0]=0,r.offset[1]=r.scrollLength,r.progress=u(0,r.scrollLength,r.current);const l=i-c;r.velocity=l>50?0:b(r.current-a,l)}const Nt={Enter:[[0,1],[1,1]],Exit:[[0,0],[1,0]],Any:[[1,0],[0,1]],All:[[0,0],[1,1]]},It={start:0,center:.5,end:1};function Ut(t,e,n=0){let i=0;if(void 0!==It[t]&&(t=It[t]),v(t)){const e=parseFloat(t);t.endsWith("px")?i=e:t.endsWith("%")?t=e/100:t.endsWith("vw")?i=e/100*document.documentElement.clientWidth:t.endsWith("vh")?i=e/100*document.documentElement.clientHeight:t=e}return r(t)&&(i=e*t),n+i}const _t=[0,0];function Gt(t,e,n,i){let o=Array.isArray(t)?t:_t,s=0,a=0;return r(t)?o=[t,t]:v(t)&&(o=(t=t.trim()).includes(" ")?t.split(" "):[t,It[t]?t:"0"]),s=Ut(o[0],n,i),a=Ut(o[1],e),s-a}const Zt={x:0,y:0};function Kt(t,e,n){let{offset:i=Nt.All}=n;const{target:r=t,axis:o="y"}=n,s="y"===o?"height":"width",a=r!==t?function(t,e){let n={x:0,y:0},i=t;for(;i&&i!==e;)if(i instanceof HTMLElement)n.x+=i.offsetLeft,n.y+=i.offsetTop,i=i.offsetParent;else if(i instanceof SVGGraphicsElement&&"getBBox"in i){const{top:t,left:e}=i.getBBox();for(n.x+=e,n.y+=t;i&&"svg"!==i.tagName;)i=i.parentNode}return n}(r,t):Zt,c=r===t?{width:t.scrollWidth,height:t.scrollHeight}:{width:r.clientWidth,height:r.clientHeight},l={width:t.clientWidth,height:t.clientHeight};e[o].offset.length=0;let u=!e[o].interpolate;const f=i.length;for(let t=0;t<f;t++){const n=Gt(i[t],l[s],c[s],a[o]);u||n===e[o].interpolatorOffsets[t]||(u=!0),e[o].offset[t]=n}u&&(e[o].interpolate=d(h(f),e[o].offset),e[o].interpolatorOffsets=[...e[o].offset]),e[o].progress=e[o].interpolate(e[o].current)}function Xt(t,e,n,i={}){const r=i.axis||"y";return{measure:()=>function(t,e=t,n){if(n.x.targetOffset=0,n.y.targetOffset=0,e!==t){let i=e;for(;i&&i!=t;)n.x.targetOffset+=i.offsetLeft,n.y.targetOffset+=i.offsetTop,i=i.offsetParent}n.x.targetLength=e===t?e.scrollWidth:e.clientWidth,n.y.targetLength=e===t?e.scrollHeight:e.clientHeight,n.x.containerLength=t.clientWidth,n.y.containerLength=t.clientHeight}(t,i.target,n),update:e=>{!function(t,e,n){Ht(t,"x",e,n),Ht(t,"y",e,n),e.time=n}(t,n,e),(i.offset||i.target)&&Kt(t,n,i)},notify:m(e)?()=>e(n):Yt(e,n[r])}}function Yt(t,e){return t.pause(),t.forEachNative(((t,{easing:e})=>{var n,i;if(t.updateDuration)e||(t.easing=l),t.updateDuration(1);else{const r={duration:1e3};e||(r.easing="linear"),null===(i=null===(n=t.effect)||void 0===n?void 0:n.updateTiming)||void 0===i||i.call(n,r)}})),()=>{t.currentTime=e.progress}}const Jt=new WeakMap,Qt=new WeakMap,te=new WeakMap,ee=t=>t===document.documentElement?window:t;function ne(t,e={}){var{container:n=document.documentElement}=e,i=dt(e,["container"]);let r=te.get(n);r||(r=new Set,te.set(n,r));const o=Xt(n,t,{time:0,x:{current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0},y:{current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0}},i);if(r.add(o),!Jt.has(n)){const t=()=>{const t=performance.now();for(const t of r)t.measure();for(const e of r)e.update(t);for(const t of r)t.notify()};Jt.set(n,t);const e=ee(n);window.addEventListener("resize",t,{passive:!0}),n!==document.documentElement&&Qt.set(n,Ft(n,t)),e.addEventListener("scroll",t,{passive:!0})}const s=Jt.get(n),a=requestAnimationFrame(s);return()=>{var e;"function"!=typeof t&&t.stop(),cancelAnimationFrame(a);const i=te.get(n);if(!i)return;if(i.delete(o),i.size)return;const r=Jt.get(n);Jt.delete(n),r&&(ee(n).removeEventListener("scroll",r),null===(e=Qt.get(n))||void 0===e||e(),window.removeEventListener("resize",r))}}function ie(t,e){return function(t){return"object"==typeof t}(t)?t:t&&e?e[t]:void 0}let re;function oe(){if(!re)return;const t=re.sort(ae).map(ce);t.forEach(le),t.forEach(le),re=void 0}function se(e){re?t(re,e):(re=[e],requestAnimationFrame(oe))}const ae=(t,e)=>t.getDepth()-e.getDepth(),ce=t=>t.animateUpdates(),le=t=>t.next(),ue=(t,e)=>new CustomEvent(t,{detail:{target:e}});function fe(t,e,n){t.dispatchEvent(new CustomEvent(e,{detail:{originalEvent:n}}))}function he(t,e,n){t.dispatchEvent(new CustomEvent(e,{detail:{originalEntry:n}}))}const de=(t,e,n)=>i=>{i.pointerType&&"mouse"!==i.pointerType||(n(),fe(t,e,i))},pe={inView:{isActive:t=>Boolean(t.inView),subscribe:(t,{enable:e,disable:n},{inViewOptions:i={}})=>{const{once:r}=i,o=dt(i,["once"]);return Wt(t,(i=>{if(e(),he(t,"viewenter",i),!r)return e=>{n(),he(t,"viewleave",e)}}),o)}},hover:{isActive:t=>Boolean(t.hover),subscribe:(t,{enable:e,disable:n})=>{const i=de(t,"hoverstart",e),r=de(t,"hoverend",n);return t.addEventListener("pointerenter",i),t.addEventListener("pointerleave",r),()=>{t.removeEventListener("pointerenter",i),t.removeEventListener("pointerleave",r)}}},press:{isActive:t=>Boolean(t.press),subscribe:(t,{enable:e,disable:n})=>{const i=e=>{n(),fe(t,"pressend",e),window.removeEventListener("pointerup",i)},r=n=>{e(),fe(t,"pressstart",n),window.addEventListener("pointerup",i)};return t.addEventListener("pointerdown",r),()=>{t.removeEventListener("pointerdown",r),window.removeEventListener("pointerup",i)}}}},ge=["initial","animate",...Object.keys(pe),"exit"],me=new WeakMap;function ve(t={},n){let i,r=n?n.getDepth()+1:0;const o={initial:!0,animate:!0},s={},a={};for(const e of ge)a[e]="string"==typeof t[e]?t[e]:null==n?void 0:n.getContext()[e];const l=!1===t.initial?"animate":"initial";let u=dt(ie(t[l]||a[l],t.variants)||{},["transition"]);const f=Object.assign({},u);const h=(t,e)=>()=>{o[t]=e,se(p)},d=()=>{for(const e in pe){const n=pe[e].isActive(t),r=s[e];n&&!r?s[e]=pe[e].subscribe(i,{enable:h(e,!0),disable:h(e,!1)},t):!n&&r&&(r(),delete s[e])}},p={update:e=>{i&&(t=e,d(),se(p))},setActive:(t,e)=>{i&&(o[t]=e,se(p))},animateUpdates:function*(){var e,n;const r=u;u={};const s={};for(const i of ge){if(!o[i])continue;const r=ie(t[i]);if(r)for(const i in r)"transition"!==i&&(u[i]=r[i],s[i]=it(null!==(n=null!==(e=r.transition)&&void 0!==e?e:t.transition)&&void 0!==n?n:{},i))}const a=new Set([...Object.keys(u),...Object.keys(r)]),l=[];a.forEach((t=>{var e,n,o;void 0===u[t]&&(u[t]=f[t]),n=r[t],o=u[t],typeof n==typeof o&&(Array.isArray(n)&&Array.isArray(o)?function(t,e){const n=e.length;if(n!==t.length)return!1;for(let i=0;i<n;i++)if(e[i]!==t[i])return!1;return!0}(n,o):n===o)||(null!==(e=f[t])&&void 0!==e||(f[t]=Q.get(i,t)),l.push(nt(i,t,u[t],s[t],A)))})),yield;const h=l.map((t=>t())).filter(Boolean);if(!h.length)return;const d=u;i.dispatchEvent(ue("motionstart",d)),Promise.all(h.map((t=>t.finished))).then((()=>{i.dispatchEvent(ue("motioncomplete",d))})).catch(c)},getDepth:()=>r,getTarget:()=>u,getOptions:()=>t,getContext:()=>a,mount:t=>(S(Boolean(t),"Animation state must be mounted with valid Element"),i=t,me.set(i,p),d(),()=>{me.delete(i),function(t){re&&e(re,t)}(p);for(const t in s)s[t]()}),isMounted:()=>Boolean(i)};return p}function ye(t){const e={},n=[];for(let i in t){const o=t[i];F(i)&&(j[i]&&(i=j[i]),n.push(i),i=P(i));let s=Array.isArray(o)?o[0]:o;const a=z.get(i);a&&(s=r(o)?a.toDefaultUnit(o):o),e[i]=s}return n.length&&(e.transform=C(n)),e}const we=t=>`-${t.toLowerCase()}`;function be(t={}){const e=ye(t);let n="";for(const t in e)n+=t.startsWith("--")?t:t.replace(/[A-Z]/g,we),n+=`: ${e[t]}; `;return n}function Ee(t,e={}){return st([()=>{const n=new A(t,[0,1],e);return n.finished.catch((()=>{})),n}],e,e.duration)}function Oe(t,e,n){return(m(t)?Ee:ht)(t,e,n)}export{D as MotionValue,Nt as ScrollOffset,Oe as animate,nt as animateStyle,ft as createAnimate,ve as createMotionState,be as createStyleString,ye as createStyles,k as getAnimationData,J as getStyleName,Lt as glide,Wt as inView,me as mountedStates,Ft as resize,ne as scroll,Dt as spring,lt as stagger,Q as style,vt as timeline,st as withControls};
