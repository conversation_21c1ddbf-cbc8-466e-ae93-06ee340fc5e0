!function(n,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("preact"),require("preact/devtools")):"function"==typeof define&&define.amd?define(["exports","preact","preact/devtools"],e):e(n.preactDebug={},n.preact)}(this,function(n,e){var t={};function o(n){return n.type===e.Fragment?"Fragment":"function"==typeof n.type?n.type.displayName||n.type.name:"string"==typeof n.type?n.type:"#text"}var r=[],i=[];function a(){return r.length>0?r[r.length-1]:null}var s=!1;function c(n){return"function"==typeof n.type&&n.type!=e.Fragment}function l(n){for(var e=[n],t=n;null!=t.__o;)e.push(t.__o),t=t.__o;return e.reduce(function(n,e){n+="  in "+o(e);var t=e.__source;return t?n+=" (at "+t.fileName+":"+t.lineNumber+")":s||(s=!0,console.warn("Add @babel/plugin-transform-react-jsx-source to get a more detailed component stack. Note that you should not add it to production builds of your App for bundle size reasons.")),n+"\n"},"")}var u="function"==typeof WeakMap,f=e.Component.prototype.setState;e.Component.prototype.setState=function(n,e){return null==this.__v?null==this.state&&console.warn('Calling "this.setState" inside the constructor of a component is a no-op and might be a bug in your application. Instead, set "this.state = {}" directly.\n\n'+l(a())):null==this.__P&&console.warn('Can\'t call "this.setState" on an unmounted component. This is a no-op, but it indicates a memory leak in your application. To fix, cancel all subscriptions and asynchronous tasks in the componentWillUnmount method.\n\n'+l(this.__v)),f.call(this,n,e)};var p=e.Component.prototype.forceUpdate;function d(n){var e=n.props,t=o(n),r="";for(var i in e)if(e.hasOwnProperty(i)&&"children"!==i){var a=e[i];"function"==typeof a&&(a="function "+(a.displayName||a.name)+"() {}"),a=Object(a)!==a||a.toString?a+"":Object.prototype.toString.call(a),r+=" "+i+"="+JSON.stringify(a)}var s=e.children;return"<"+t+r+(s&&s.length?">..</"+t+">":" />")}e.Component.prototype.forceUpdate=function(n){return null==this.__v?console.warn('Calling "this.forceUpdate" inside the constructor of a component is a no-op and might be a bug in your application.\n\n'+l(a())):null==this.__P&&console.warn('Can\'t call "this.forceUpdate" on an unmounted component. This is a no-op, but it indicates a memory leak in your application. To fix, cancel all subscriptions and asynchronous tasks in the componentWillUnmount method.\n\n'+l(this.__v)),p.call(this,n)},function(){!function(){var n=e.options.__b,t=e.options.diffed,o=e.options.__,a=e.options.vnode,s=e.options.__r;e.options.diffed=function(n){c(n)&&i.pop(),r.pop(),t&&t(n)},e.options.__b=function(e){c(e)&&r.push(e),n&&n(e)},e.options.__=function(n,e){i=[],o&&o(n,e)},e.options.vnode=function(n){n.__o=i.length>0?i[i.length-1]:null,a&&a(n)},e.options.__r=function(n){c(n)&&i.push(n),s&&s(n)}}();var n=e.options.__b,a=e.options.diffed,s=e.options.vnode,f=e.options.__e,p=e.options.__,h=e.options.__h,y=u?{useEffect:new WeakMap,useLayoutEffect:new WeakMap,lazyPropTypes:new WeakMap}:null;e.options.__e=function(n,e,t){if(e&&e.__c&&"function"==typeof n.then){var r=n;n=new Error("Missing Suspense. The throwing component was: "+o(e));for(var i=e;i;i=i.__)if(i.__c&&i.__c.__c){n=r;break}if(n instanceof Error)throw n}f(n,e,t)},e.options.__=function(n,e){if(!e)throw new Error("Undefined parent passed to render(), this is the second argument.\nCheck if the element is available in the DOM/has the correct id.");var t;switch(e.nodeType){case 1:case 11:case 9:t=!0;break;default:t=!1}if(!t){var r=o(n);throw new Error("Expected a valid HTML node as a second argument to render.\tReceived "+e+" instead: render(<"+r+" />, "+e+");")}p&&p(n,e)},e.options.__b=function(e){var r,i,a,s=e.type,c=function n(e){return e?"function"==typeof e.type?n(e.__):e:{}}(e.__);if(void 0===s)throw new Error("Undefined component passed to createElement()\n\nYou likely forgot to export your component or might have mixed up default and named imports"+d(e)+"\n\n"+l(e));if(null!=s&&"object"==typeof s){if(void 0!==s.__k&&void 0!==s.__e)throw new Error("Invalid type passed to createElement(): "+s+"\n\nDid you accidentally pass a JSX literal as JSX twice?\n\n  let My"+o(e)+" = "+d(s)+";\n  let vnode = <My"+o(e)+" />;\n\nThis usually happens when you export a JSX literal and not the component.\n\n"+l(e));throw new Error("Invalid type passed to createElement(): "+(Array.isArray(s)?"array":s))}if("thead"!==s&&"tfoot"!==s&&"tbody"!==s||"table"===c.type?"tr"===s&&"thead"!==c.type&&"tfoot"!==c.type&&"tbody"!==c.type&&"table"!==c.type?console.error("Improper nesting of table. Your <tr> should have a <thead/tbody/tfoot/table> parent."+d(e)+"\n\n"+l(e)):"td"===s&&"tr"!==c.type?console.error("Improper nesting of table. Your <td> should have a <tr> parent."+d(e)+"\n\n"+l(e)):"th"===s&&"tr"!==c.type&&console.error("Improper nesting of table. Your <th> should have a <tr>."+d(e)+"\n\n"+l(e)):console.error("Improper nesting of table. Your <thead/tbody/tfoot> should have a <table> parent."+d(e)+"\n\n"+l(e)),void 0!==e.ref&&"function"!=typeof e.ref&&"object"!=typeof e.ref&&!("$$typeof"in e))throw new Error('Component\'s "ref" property should be a function, or an object created by createRef(), but got ['+typeof e.ref+"] instead\n"+d(e)+"\n\n"+l(e));if("string"==typeof e.type)for(var u in e.props)if("o"===u[0]&&"n"===u[1]&&"function"!=typeof e.props[u]&&null!=e.props[u])throw new Error("Component's \""+u+'" property should be a function, but got ['+typeof e.props[u]+"] instead\n"+d(e)+"\n\n"+l(e));if("function"==typeof e.type&&e.type.propTypes){if("Lazy"===e.type.displayName&&y&&!y.lazyPropTypes.has(e.type)){var f="PropTypes are not supported on lazy(). Use propTypes on the wrapped component itself. ";try{var p=e.type();y.lazyPropTypes.set(e.type,!0),console.warn(f+"Component wrapped in lazy() is "+o(p))}catch(n){console.warn(f+"We will log the wrapped component's name once it is loaded.")}}r=e.type.propTypes,i=e.props,a=o(e),Object.keys(r).forEach(function(n){var e;try{e=r[n](i,n,a,"prop",null,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED")}catch(n){e=n}!e||e.message in t||(t[e.message]=!0,console.error("Failed prop type: "+e.message))})}n&&n(e)},e.options.__h=function(n,e,t){if(!n)throw new Error("Hook can only be invoked from render methods.");h&&h(n,e,t)};var m=function(n,e){return{get:function(){console.warn("getting vnode."+n+" is deprecated, "+e)},set:function(){console.warn("setting vnode."+n+" is not allowed, "+e)}}},v={nodeName:m("nodeName","use vnode.type"),attributes:m("attributes","use vnode.props"),children:m("children","use vnode.props.children")},b=Object.create({},v);e.options.vnode=function(n){var e=n.props;if(null!==n.type&&null!=e&&("__source"in e||"__self"in e)){var t=n.props={};for(var o in e){var r=e[o];"__source"===o?n.__source=r:"__self"===o?n.__self=r:t[o]=r}}Object.setPrototypeOf(n,b),s&&s(n)},e.options.diffed=function(n){n.__k&&n.__k.forEach(function(e){if(e&&void 0===e.type){delete e.__,delete e.__b;var t=Object.keys(e).join(",");throw new Error("Objects are not valid as a child. Encountered an object with the keys {"+t+"}.\n\n"+l(n))}});var e=n.__c;if(e&&e.__H){var t=e.__H;Array.isArray(t.__)&&t.__.forEach(function(e){if(e.__h&&(!e.__H||!Array.isArray(e.__H))){var t=o(n);console.warn("In "+t+" you are calling useMemo/useCallback without passing arguments.\nThis is a noop since it will not be able to memoize, it will execute it every render.\n\n"+l(n))}})}if(a&&a(n),null!=n.__k)for(var r=[],i=0;i<n.__k.length;i++){var s=n.__k[i];if(s&&null!=s.key){var c=s.key;if(-1!==r.indexOf(c)){console.error('Following component has two or more children with the same key attribute: "'+c+'". This may cause glitches and misbehavior in rendering process. Component: \n\n'+d(n)+"\n\n"+l(n));break}r.push(c)}}}}(),n.resetPropWarnings=function(){t={}}});
//# sourceMappingURL=debug.umd.js.map
