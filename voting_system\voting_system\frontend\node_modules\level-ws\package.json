{"name": "level-ws", "description": "A basic WriteStream implementation for LevelUP", "version": "0.0.0", "contributors": ["<PERSON>g <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/chesles/)", "<PERSON> <<EMAIL>> (https://github.com/raynos)", "<PERSON> <<EMAIL>> (https://github.com/dominictarr)", "<PERSON> <<EMAIL>> (https://github.com/maxogden)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/ralphtheninja)", "<PERSON> <<EMAIL>> (https://github.com/kesla)", "<PERSON> <<EMAIL>> (https://github.com/juliangruber)", "<PERSON> <<EMAIL>> (https://github.com/hij1nx)", "<PERSON> <<EMAIL>> (https://github.com/No9)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON> <<EMAIL>> (https://github.com/pgte)", "<PERSON> <<EMAIL>> (https://github.com/substack)"], "repository": {"type": "git", "url": "https://github.com/Level/level-ws.git"}, "homepage": "https://github.com/Level/level-ws", "keywords": ["leveldb", "stream", "levelup"], "main": "level-ws.js", "dependencies": {"readable-stream": "~1.0.15", "xtend": "~2.1.1"}, "devDependencies": {"tape": "*", "level": "*", "after": "*", "rimraf": "*"}, "scripts": {"test": "node test.js"}, "license": "MIT"}