!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define("WalletConnectQRCodeModal",[],e):"object"==typeof exports?exports.WalletConnectQRCodeModal=e():t.WalletConnectQRCodeModal=e()}(this,(function(){return function(t){var e={};function n(r){if(e[r])return e[r].exports;var o=e[r]={i:r,l:!1,exports:{}};return t[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=t,n.c=e,n.d=function(t,e,r){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var o in t)n.d(r,o,function(e){return t[e]}.bind(null,o));return r},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s=17)}([function(t,e,n){"use strict";function r(t){let e=void 0;return"undefined"!=typeof window&&void 0!==window[t]&&(e=window[t]),e}function o(t){const e=r(t);if(!e)throw new Error(t+" is not defined in Window");return e}Object.defineProperty(e,"__esModule",{value:!0}),e.getLocalStorage=e.getLocalStorageOrThrow=e.getCrypto=e.getCryptoOrThrow=e.getLocation=e.getLocationOrThrow=e.getNavigator=e.getNavigatorOrThrow=e.getDocument=e.getDocumentOrThrow=e.getFromWindowOrThrow=e.getFromWindow=void 0,e.getFromWindow=r,e.getFromWindowOrThrow=o,e.getDocumentOrThrow=function(){return o("document")},e.getDocument=function(){return r("document")},e.getNavigatorOrThrow=function(){return o("navigator")},e.getNavigator=function(){return r("navigator")},e.getLocationOrThrow=function(){return o("location")},e.getLocation=function(){return r("location")},e.getCryptoOrThrow=function(){return o("crypto")},e.getCrypto=function(){return r("crypto")},e.getLocalStorageOrThrow=function(){return o("localStorage")},e.getLocalStorage=function(){return r("localStorage")}},function(t,e){var n,r=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];e.getSymbolSize=function(t){if(!t)throw new Error('"version" cannot be null or undefined');if(t<1||t>40)throw new Error('"version" should be in range from 1 to 40');return 4*t+17},e.getSymbolTotalCodewords=function(t){return r[t]},e.getBCHDigit=function(t){for(var e=0;0!==t;)e++,t>>>=1;return e},e.setToSJISFunction=function(t){if("function"!=typeof t)throw new Error('"toSJISFunc" is not a valid function.');n=t},e.isKanjiModeEnabled=function(){return void 0!==n},e.toSJIS=function(t){return n(t)}},function(t,e,n){var r=n(11),o=n(12);e.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},e.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},e.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},e.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},e.MIXED={bit:-1},e.getCharCountIndicator=function(t,e){if(!t.ccBits)throw new Error("Invalid mode: "+t);if(!r.isValid(e))throw new Error("Invalid version: "+e);return e>=1&&e<10?t.ccBits[0]:e<27?t.ccBits[1]:t.ccBits[2]},e.getBestModeForData=function(t){return o.testNumeric(t)?e.NUMERIC:o.testAlphanumeric(t)?e.ALPHANUMERIC:o.testKanji(t)?e.KANJI:e.BYTE},e.toString=function(t){if(t&&t.id)return t.id;throw new Error("Invalid mode")},e.isValid=function(t){return t&&t.bit&&t.ccBits},e.from=function(t,n){if(e.isValid(t))return t;try{return function(t){if("string"!=typeof t)throw new Error("Param is not a string");switch(t.toLowerCase()){case"numeric":return e.NUMERIC;case"alphanumeric":return e.ALPHANUMERIC;case"kanji":return e.KANJI;case"byte":return e.BYTE;default:throw new Error("Unknown mode: "+t)}}(t)}catch(t){return n}}},function(t,e,n){"use strict";var r=n(5);i.TYPED_ARRAY_SUPPORT=function(){try{var t=new Uint8Array(1);return t.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===t.foo()}catch(t){return!1}}();var o=i.TYPED_ARRAY_SUPPORT?**********:**********;function i(t,e,n){return i.TYPED_ARRAY_SUPPORT||this instanceof i?"number"==typeof t?c(this,t):function(t,e,n,r){if("number"==typeof e)throw new TypeError('"value" argument must not be a number');if("undefined"!=typeof ArrayBuffer&&e instanceof ArrayBuffer)return function(t,e,n,r){if(n<0||e.byteLength<n)throw new RangeError("'offset' is out of bounds");if(e.byteLength<n+(r||0))throw new RangeError("'length' is out of bounds");var o;o=void 0===n&&void 0===r?new Uint8Array(e):void 0===r?new Uint8Array(e,n):new Uint8Array(e,n,r);i.TYPED_ARRAY_SUPPORT?o.__proto__=i.prototype:o=s(t,o);return o}(t,e,n,r);if("string"==typeof e)return function(t,e){var n=0|f(e),r=l(t,n),o=r.write(e);o!==n&&(r=r.slice(0,o));return r}(t,e);return function(t,e){if(i.isBuffer(e)){var n=0|a(e.length),r=l(t,n);return 0===r.length||e.copy(r,0,0,n),r}if(e){if("undefined"!=typeof ArrayBuffer&&e.buffer instanceof ArrayBuffer||"length"in e)return"number"!=typeof e.length||(o=e.length)!=o?l(t,0):s(t,e);if("Buffer"===e.type&&Array.isArray(e.data))return s(t,e.data)}var o;throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(t,e)}(this,t,e,n):new i(t,e,n)}function a(t){if(t>=o)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+o.toString(16)+" bytes");return 0|t}function l(t,e){var n;return i.TYPED_ARRAY_SUPPORT?(n=new Uint8Array(e)).__proto__=i.prototype:(null===(n=t)&&(n=new i(e)),n.length=e),n}function c(t,e){var n=l(t,e<0?0:0|a(e));if(!i.TYPED_ARRAY_SUPPORT)for(var r=0;r<e;++r)n[r]=0;return n}function s(t,e){for(var n=e.length<0?0:0|a(e.length),r=l(t,n),o=0;o<n;o+=1)r[o]=255&e[o];return r}function u(t,e){var n;e=e||1/0;for(var r=t.length,o=null,i=[],a=0;a<r;++a){if((n=t.charCodeAt(a))>55295&&n<57344){if(!o){if(n>56319){(e-=3)>-1&&i.push(239,191,189);continue}if(a+1===r){(e-=3)>-1&&i.push(239,191,189);continue}o=n;continue}if(n<56320){(e-=3)>-1&&i.push(239,191,189),o=n;continue}n=65536+(o-55296<<10|n-56320)}else o&&(e-=3)>-1&&i.push(239,191,189);if(o=null,n<128){if((e-=1)<0)break;i.push(n)}else if(n<2048){if((e-=2)<0)break;i.push(n>>6|192,63&n|128)}else if(n<65536){if((e-=3)<0)break;i.push(n>>12|224,n>>6&63|128,63&n|128)}else{if(!(n<1114112))throw new Error("Invalid code point");if((e-=4)<0)break;i.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}}return i}function f(t){return i.isBuffer(t)?t.length:"undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(t)||t instanceof ArrayBuffer)?t.byteLength:("string"!=typeof t&&(t=""+t),0===t.length?0:u(t).length)}i.TYPED_ARRAY_SUPPORT&&(i.prototype.__proto__=Uint8Array.prototype,i.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&i[Symbol.species]===i&&Object.defineProperty(i,Symbol.species,{value:null,configurable:!0,enumerable:!1,writable:!1})),i.prototype.write=function(t,e,n){void 0===e||void 0===n&&"string"==typeof e?(n=this.length,e=0):isFinite(e)&&(e|=0,isFinite(n)?n|=0:n=void 0);var r=this.length-e;if((void 0===n||n>r)&&(n=r),t.length>0&&(n<0||e<0)||e>this.length)throw new RangeError("Attempt to write outside buffer bounds");return function(t,e,n,r){return function(t,e,n,r){for(var o=0;o<r&&!(o+n>=e.length||o>=t.length);++o)e[o+n]=t[o];return o}(u(e,t.length-n),t,n,r)}(this,t,e,n)},i.prototype.slice=function(t,e){var n,r=this.length;if((t=~~t)<0?(t+=r)<0&&(t=0):t>r&&(t=r),(e=void 0===e?r:~~e)<0?(e+=r)<0&&(e=0):e>r&&(e=r),e<t&&(e=t),i.TYPED_ARRAY_SUPPORT)(n=this.subarray(t,e)).__proto__=i.prototype;else{var o=e-t;n=new i(o,void 0);for(var a=0;a<o;++a)n[a]=this[a+t]}return n},i.prototype.copy=function(t,e,n,r){if(n||(n=0),r||0===r||(r=this.length),e>=t.length&&(e=t.length),e||(e=0),r>0&&r<n&&(r=n),r===n)return 0;if(0===t.length||0===this.length)return 0;if(e<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("sourceStart out of bounds");if(r<0)throw new RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),t.length-e<r-n&&(r=t.length-e+n);var o,a=r-n;if(this===t&&n<e&&e<r)for(o=a-1;o>=0;--o)t[o+e]=this[o+n];else if(a<1e3||!i.TYPED_ARRAY_SUPPORT)for(o=0;o<a;++o)t[o+e]=this[o+n];else Uint8Array.prototype.set.call(t,this.subarray(n,n+a),e);return a},i.prototype.fill=function(t,e,n){if("string"==typeof t){if("string"==typeof e?(e=0,n=this.length):"string"==typeof n&&(n=this.length),1===t.length){var r=t.charCodeAt(0);r<256&&(t=r)}}else"number"==typeof t&&(t&=255);if(e<0||this.length<e||this.length<n)throw new RangeError("Out of range index");if(n<=e)return this;var o;if(e>>>=0,n=void 0===n?this.length:n>>>0,t||(t=0),"number"==typeof t)for(o=e;o<n;++o)this[o]=t;else{var a=i.isBuffer(t)?t:new i(t),l=a.length;for(o=0;o<n-e;++o)this[o+e]=a[o%l]}return this},i.concat=function(t,e){if(!r(t))throw new TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return l(null,0);var n;if(void 0===e)for(e=0,n=0;n<t.length;++n)e+=t[n].length;var o=c(null,e),a=0;for(n=0;n<t.length;++n){var s=t[n];if(!i.isBuffer(s))throw new TypeError('"list" argument must be an Array of Buffers');s.copy(o,a),a+=s.length}return o},i.byteLength=f,i.prototype._isBuffer=!0,i.isBuffer=function(t){return!(null==t||!t._isBuffer)},t.exports.alloc=function(t){var e=new i(t);return e.fill(0),e},t.exports.from=function(t){return new i(t)}},function(t,e,n){var r=n(18),o=n(19),i=n(41),a=n(42);function l(t,e,n,i,a){var l=[].slice.call(arguments,1),c=l.length,s="function"==typeof l[c-1];if(!s&&!r())throw new Error("Callback required as last argument");if(!s){if(c<1)throw new Error("Too few arguments provided");return 1===c?(n=e,e=i=void 0):2!==c||e.getContext||(i=n,n=e,e=void 0),new Promise((function(r,a){try{var l=o.create(n,i);r(t(l,e,i))}catch(t){a(t)}}))}if(c<2)throw new Error("Too few arguments provided");2===c?(a=n,n=e,e=i=void 0):3===c&&(e.getContext&&void 0===a?(a=i,i=void 0):(a=i,i=n,n=e,e=void 0));try{var u=o.create(n,i);a(null,t(u,e,i))}catch(t){a(t)}}e.create=o.create,e.toCanvas=l.bind(null,i.render),e.toDataURL=l.bind(null,i.renderToDataURL),e.toString=l.bind(null,(function(t,e,n){return a.render(t,n)}))},function(t,e){var n={}.toString;t.exports=Array.isArray||function(t){return"[object Array]"==n.call(t)}},function(t,e){e.L={bit:1},e.M={bit:0},e.Q={bit:3},e.H={bit:2},e.isValid=function(t){return t&&void 0!==t.bit&&t.bit>=0&&t.bit<4},e.from=function(t,n){if(e.isValid(t))return t;try{return function(t){if("string"!=typeof t)throw new Error("Param is not a string");switch(t.toLowerCase()){case"l":case"low":return e.L;case"m":case"medium":return e.M;case"q":case"quartile":return e.Q;case"h":case"high":return e.H;default:throw new Error("Unknown EC Level: "+t)}}(t)}catch(t){return n}}},function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"a",(function(){return a}));var r=n(4),o=n.n(r);function i(t){o.a.toString(t,{type:"terminal"}).then(console.log)}function a(){}},function(t,e,n){"use strict";var r,o,i,a,l,c,s={},u=[],f=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord/i;function h(t,e){for(var n in e)t[n]=e[n];return t}function d(t){var e=t.parentNode;e&&e.removeChild(t)}function p(t,e,n){var r,o=arguments,i={};for(r in e)"key"!==r&&"ref"!==r&&(i[r]=e[r]);if(arguments.length>3)for(n=[n],r=3;r<arguments.length;r++)n.push(o[r]);if(null!=n&&(i.children=n),"function"==typeof t&&null!=t.defaultProps)for(r in t.defaultProps)void 0===i[r]&&(i[r]=t.defaultProps[r]);return _(t,i,e&&e.key,e&&e.ref,null)}function _(t,e,n,o,i){var a={type:t,props:e,key:n,ref:o,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:i};return null==i&&(a.__v=a),r.vnode&&r.vnode(a),a}function g(t){return t.children}function m(t,e){this.props=t,this.context=e}function w(t,e){if(null==e)return t.__?w(t.__,t.__.__k.indexOf(t)+1):null;for(var n;e<t.__k.length;e++)if(null!=(n=t.__k[e])&&null!=n.__e)return n.__e;return"function"==typeof t.type?w(t):null}function v(t){var e,n;if(null!=(t=t.__)&&null!=t.__c){for(t.__e=t.__c.base=null,e=0;e<t.__k.length;e++)if(null!=(n=t.__k[e])&&null!=n.__e){t.__e=t.__c.base=n.__e;break}return v(t)}}function y(t){(!t.__d&&(t.__d=!0)&&o.push(t)&&!i++||l!==r.debounceRendering)&&((l=r.debounceRendering)||a)(b)}function b(){for(var t;i=o.length;)t=o.sort((function(t,e){return t.__v.__b-e.__v.__b})),o=[],t.some((function(t){var e,n,r,o,i,a,l;t.__d&&(a=(i=(e=t).__v).__e,(l=e.__P)&&(n=[],(r=h({},i)).__v=r,o=R(l,i,r,e.__n,void 0!==l.ownerSVGElement,null,n,null==a?w(i):a),k(n,i),o!=a&&v(i)))}))}function x(t,e,n,r,o,i,a,l,c){var f,h,p,_,g,m,v,y=n&&n.__k||u,b=y.length;if(l==s&&(l=null!=i?i[0]:b?w(n,0):null),f=0,e.__k=E(e.__k,(function(n){if(null!=n){if(n.__=e,n.__b=e.__b+1,null===(p=y[f])||p&&n.key==p.key&&n.type===p.type)y[f]=void 0;else for(h=0;h<b;h++){if((p=y[h])&&n.key==p.key&&n.type===p.type){y[h]=void 0;break}p=null}if(_=R(t,n,p=p||s,r,o,i,a,l,c),(h=n.ref)&&p.ref!=h&&(v||(v=[]),p.ref&&v.push(p.ref,null,n),v.push(h,n.__c||_,n)),null!=_){var u;if(null==m&&(m=_),void 0!==n.__d)u=n.__d,n.__d=void 0;else if(i==p||_!=l||null==_.parentNode){t:if(null==l||l.parentNode!==t)t.appendChild(_),u=null;else{for(g=l,h=0;(g=g.nextSibling)&&h<b;h+=2)if(g==_)break t;t.insertBefore(_,l),u=l}"option"==e.type&&(t.value="")}l=void 0!==u?u:_.nextSibling,"function"==typeof e.type&&(e.__d=l)}else l&&p.__e==l&&l.parentNode!=t&&(l=w(p))}return f++,n})),e.__e=m,null!=i&&"function"!=typeof e.type)for(f=i.length;f--;)null!=i[f]&&d(i[f]);for(f=b;f--;)null!=y[f]&&B(y[f],y[f]);if(v)for(f=0;f<v.length;f++)S(v[f],v[++f],v[++f])}function E(t,e,n){if(null==n&&(n=[]),null==t||"boolean"==typeof t)e&&n.push(e(null));else if(Array.isArray(t))for(var r=0;r<t.length;r++)E(t[r],e,n);else n.push(e?e("string"==typeof t||"number"==typeof t?_(null,t,null,null,t):null!=t.__e||null!=t.__c?_(t.type,t.props,t.key,null,t.__v):t):t);return n}function A(t,e,n){"-"===e[0]?t.setProperty(e,n):t[e]="number"==typeof n&&!1===f.test(e)?n+"px":null==n?"":n}function C(t,e,n,r,o){var i,a,l,c,s;if(o?"className"===e&&(e="class"):"class"===e&&(e="className"),"style"===e)if(i=t.style,"string"==typeof n)i.cssText=n;else{if("string"==typeof r&&(i.cssText="",r=null),r)for(c in r)n&&c in n||A(i,c,"");if(n)for(s in n)r&&n[s]===r[s]||A(i,s,n[s])}else"o"===e[0]&&"n"===e[1]?(a=e!==(e=e.replace(/Capture$/,"")),l=e.toLowerCase(),e=(l in t?l:e).slice(2),n?(r||t.addEventListener(e,T,a),(t.l||(t.l={}))[e]=n):t.removeEventListener(e,T,a)):"list"!==e&&"tagName"!==e&&"form"!==e&&"type"!==e&&"size"!==e&&!o&&e in t?t[e]=null==n?"":n:"function"!=typeof n&&"dangerouslySetInnerHTML"!==e&&(e!==(e=e.replace(/^xlink:?/,""))?null==n||!1===n?t.removeAttributeNS("http://www.w3.org/1999/xlink",e.toLowerCase()):t.setAttributeNS("http://www.w3.org/1999/xlink",e.toLowerCase(),n):null==n||!1===n&&!/^ar/.test(e)?t.removeAttribute(e):t.setAttribute(e,n))}function T(t){this.l[t.type](r.event?r.event(t):t)}function R(t,e,n,o,i,a,l,c,s){var u,f,d,p,_,w,v,y,b,E,A=e.type;if(void 0!==e.constructor)return null;(u=r.__b)&&u(e);try{t:if("function"==typeof A){if(y=e.props,b=(u=A.contextType)&&o[u.__c],E=u?b?b.props.value:u.__:o,n.__c?v=(f=e.__c=n.__c).__=f.__E:("prototype"in A&&A.prototype.render?e.__c=f=new A(y,E):(e.__c=f=new m(y,E),f.constructor=A,f.render=N),b&&b.sub(f),f.props=y,f.state||(f.state={}),f.context=E,f.__n=o,d=f.__d=!0,f.__h=[]),null==f.__s&&(f.__s=f.state),null!=A.getDerivedStateFromProps&&(f.__s==f.state&&(f.__s=h({},f.__s)),h(f.__s,A.getDerivedStateFromProps(y,f.__s))),p=f.props,_=f.state,d)null==A.getDerivedStateFromProps&&null!=f.componentWillMount&&f.componentWillMount(),null!=f.componentDidMount&&f.__h.push(f.componentDidMount);else{if(null==A.getDerivedStateFromProps&&y!==p&&null!=f.componentWillReceiveProps&&f.componentWillReceiveProps(y,E),!f.__e&&null!=f.shouldComponentUpdate&&!1===f.shouldComponentUpdate(y,f.__s,E)||e.__v===n.__v&&!f.__){for(f.props=y,f.state=f.__s,e.__v!==n.__v&&(f.__d=!1),f.__v=e,e.__e=n.__e,e.__k=n.__k,f.__h.length&&l.push(f),u=0;u<e.__k.length;u++)e.__k[u]&&(e.__k[u].__=e);break t}null!=f.componentWillUpdate&&f.componentWillUpdate(y,f.__s,E),null!=f.componentDidUpdate&&f.__h.push((function(){f.componentDidUpdate(p,_,w)}))}f.context=E,f.props=y,f.state=f.__s,(u=r.__r)&&u(e),f.__d=!1,f.__v=e,f.__P=t,u=f.render(f.props,f.state,f.context),e.__k=null!=u&&u.type==g&&null==u.key?u.props.children:Array.isArray(u)?u:[u],null!=f.getChildContext&&(o=h(h({},o),f.getChildContext())),d||null==f.getSnapshotBeforeUpdate||(w=f.getSnapshotBeforeUpdate(p,_)),x(t,e,n,o,i,a,l,c,s),f.base=e.__e,f.__h.length&&l.push(f),v&&(f.__E=f.__=null),f.__e=!1}else null==a&&e.__v===n.__v?(e.__k=n.__k,e.__e=n.__e):e.__e=P(n.__e,e,n,o,i,a,l,s);(u=r.diffed)&&u(e)}catch(t){e.__v=null,r.__e(t,e,n)}return e.__e}function k(t,e){r.__c&&r.__c(e,t),t.some((function(e){try{t=e.__h,e.__h=[],t.some((function(t){t.call(e)}))}catch(t){r.__e(t,e.__v)}}))}function P(t,e,n,r,o,i,a,l){var c,f,h,d,p,_=n.props,g=e.props;if(o="svg"===e.type||o,null!=i)for(c=0;c<i.length;c++)if(null!=(f=i[c])&&((null===e.type?3===f.nodeType:f.localName===e.type)||t==f)){t=f,i[c]=null;break}if(null==t){if(null===e.type)return document.createTextNode(g);t=o?document.createElementNS("http://www.w3.org/2000/svg",e.type):document.createElement(e.type,g.is&&{is:g.is}),i=null,l=!1}if(null===e.type)_!==g&&t.data!=g&&(t.data=g);else{if(null!=i&&(i=u.slice.call(t.childNodes)),h=(_=n.props||s).dangerouslySetInnerHTML,d=g.dangerouslySetInnerHTML,!l){if(_===s)for(_={},p=0;p<t.attributes.length;p++)_[t.attributes[p].name]=t.attributes[p].value;(d||h)&&(d&&h&&d.__html==h.__html||(t.innerHTML=d&&d.__html||""))}(function(t,e,n,r,o){var i;for(i in n)"children"===i||"key"===i||i in e||C(t,i,null,n[i],r);for(i in e)o&&"function"!=typeof e[i]||"children"===i||"key"===i||"value"===i||"checked"===i||n[i]===e[i]||C(t,i,e[i],n[i],r)})(t,g,_,o,l),d?e.__k=[]:(e.__k=e.props.children,x(t,e,n,r,"foreignObject"!==e.type&&o,i,a,s,l)),l||("value"in g&&void 0!==(c=g.value)&&c!==t.value&&C(t,"value",c,_.value,!1),"checked"in g&&void 0!==(c=g.checked)&&c!==t.checked&&C(t,"checked",c,_.checked,!1))}return t}function S(t,e,n){try{"function"==typeof t?t(e):t.current=e}catch(t){r.__e(t,n)}}function B(t,e,n){var o,i,a;if(r.unmount&&r.unmount(t),(o=t.ref)&&(o.current&&o.current!==t.__e||S(o,null,e)),n||"function"==typeof t.type||(n=null!=(i=t.__e)),t.__e=t.__d=void 0,null!=(o=t.__c)){if(o.componentWillUnmount)try{o.componentWillUnmount()}catch(t){r.__e(t,e)}o.base=o.__P=null}if(o=t.__k)for(a=0;a<o.length;a++)o[a]&&B(o[a],e,n);null!=i&&d(i)}function N(t,e,n){return this.constructor(t,n)}function M(t,e,n){var o,i,a;r.__&&r.__(t,e),i=(o=n===c)?null:n&&n.__k||e.__k,t=p(g,null,[t]),a=[],R(e,(o?e:n||e).__k=t,i||s,s,void 0!==e.ownerSVGElement,n&&!o?[n]:i?null:u.slice.call(e.childNodes),a,n||s,o),k(a,t)}r={__e:function(t,e){for(var n,r;e=e.__;)if((n=e.__c)&&!n.__)try{if(n.constructor&&null!=n.constructor.getDerivedStateFromError&&(r=!0,n.setState(n.constructor.getDerivedStateFromError(t))),null!=n.componentDidCatch&&(r=!0,n.componentDidCatch(t)),r)return y(n.__E=n)}catch(e){t=e}throw t}},m.prototype.setState=function(t,e){var n;n=this.__s!==this.state?this.__s:this.__s=h({},this.state),"function"==typeof t&&(t=t(n,this.props)),t&&h(n,t),null!=t&&this.__v&&(e&&this.__h.push(e),y(this))},m.prototype.forceUpdate=function(t){this.__v&&(this.__e=!0,t&&this.__h.push(t),y(this))},m.prototype.render=g,o=[],i=0,a="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,c=s;var L,O,I,U=0,D=[],Y=r.__r,z=r.diffed,W=r.__c,q=r.unmount;function F(t,e){r.__h&&r.__h(O,t,U||e),U=0;var n=O.__H||(O.__H={__:[],__h:[]});return t>=n.__.length&&n.__.push({}),n.__[t]}function j(t){return U=1,H(G,t)}function H(t,e,n){var r=F(L++,2);return r.__c||(r.__c=O,r.__=[n?n(e):G(void 0,e),function(e){var n=t(r.__[0],e);r.__[0]!==n&&(r.__[0]=n,r.__c.setState({}))}]),r.__}function V(t,e){var n=F(L++,3);!r.__s&&Z(n.__H,e)&&(n.__=t,n.__H=e,O.__H.__h.push(n))}function Q(t,e){var n=F(L++,7);return Z(n.__H,e)?(n.__H=e,n.__h=t,n.__=t()):n.__}function $(t,e){return U=8,Q((function(){return t}),e)}function K(){D.some((function(t){if(t.__P)try{t.__H.__h.forEach(J),t.__H.__h.forEach(X),t.__H.__h=[]}catch(e){return t.__H.__h=[],r.__e(e,t.__v),!0}})),D=[]}function J(t){t.t&&t.t()}function X(t){var e=t.__();"function"==typeof e&&(t.t=e)}function Z(t,e){return!t||e.some((function(e,n){return e!==t[n]}))}function G(t,e){return"function"==typeof e?e(t):e}function tt(t,e){for(var n in e)t[n]=e[n];return t}function et(t,e){for(var n in t)if("__source"!==n&&!(n in e))return!0;for(var r in e)if("__source"!==r&&t[r]!==e[r])return!0;return!1}r.__r=function(t){Y&&Y(t),L=0,(O=t.__c).__H&&(O.__H.__h.forEach(J),O.__H.__h.forEach(X),O.__H.__h=[])},r.diffed=function(t){z&&z(t);var e=t.__c;if(e){var n=e.__H;n&&n.__h.length&&(1!==D.push(e)&&I===r.requestAnimationFrame||((I=r.requestAnimationFrame)||function(t){var e,n=function(){clearTimeout(r),cancelAnimationFrame(e),setTimeout(t)},r=setTimeout(n,100);"undefined"!=typeof window&&(e=requestAnimationFrame(n))})(K))}},r.__c=function(t,e){e.some((function(t){try{t.__h.forEach(J),t.__h=t.__h.filter((function(t){return!t.__||X(t)}))}catch(n){e.some((function(t){t.__h&&(t.__h=[])})),e=[],r.__e(n,t.__v)}})),W&&W(t,e)},r.unmount=function(t){q&&q(t);var e=t.__c;if(e){var n=e.__H;if(n)try{n.__.forEach((function(t){return t.t&&t.t()}))}catch(t){r.__e(t,e.__v)}}};!function(t){var e,n;function r(e){var n;return(n=t.call(this,e)||this).isPureReactComponent=!0,n}n=t,(e=r).prototype=Object.create(n.prototype),e.prototype.constructor=e,e.__proto__=n,r.prototype.shouldComponentUpdate=function(t,e){return et(this.props,t)||et(this.state,e)}}(m);var nt=r.__b;r.__b=function(t){t.type&&t.type.t&&t.ref&&(t.props.ref=t.ref,t.ref=null),nt&&nt(t)};var rt=r.__e;function ot(t){return t&&((t=tt({},t)).__c=null,t.__k=t.__k&&t.__k.map(ot)),t}function it(){this.__u=0,this.o=null,this.__b=null}function at(t){var e=t.__.__c;return e&&e.u&&e.u(t)}function lt(){this.i=null,this.l=null}r.__e=function(t,e,n){if(t.then)for(var r,o=e;o=o.__;)if((r=o.__c)&&r.__c)return r.__c(t,e.__c);rt(t,e,n)},(it.prototype=new m).__c=function(t,e){var n=this;null==n.o&&(n.o=[]),n.o.push(e);var r=at(n.__v),o=!1,i=function(){o||(o=!0,r?r(a):a())};e.__c=e.componentWillUnmount,e.componentWillUnmount=function(){i(),e.__c&&e.__c()};var a=function(){var t;if(!--n.__u)for(n.__v.__k[0]=n.state.u,n.setState({u:n.__b=null});t=n.o.pop();)t.forceUpdate()};n.__u++||n.setState({u:n.__b=n.__v.__k[0]}),t.then(i,i)},it.prototype.render=function(t,e){return this.__b&&(this.__v.__k[0]=ot(this.__b),this.__b=null),[p(m,null,e.u?null:t.children),e.u&&t.fallback]};var ct=function(t,e,n){if(++n[1]===n[0]&&t.l.delete(e),t.props.revealOrder&&("t"!==t.props.revealOrder[0]||!t.l.size))for(n=t.i;n;){for(;n.length>3;)n.pop()();if(n[1]<n[0])break;t.i=n=n[2]}};(lt.prototype=new m).u=function(t){var e=this,n=at(e.__v),r=e.l.get(t);return r[0]++,function(o){var i=function(){e.props.revealOrder?(r.push(o),ct(e,t,r)):o()};n?n(i):i()}},lt.prototype.render=function(t){this.i=null,this.l=new Map;var e=E(t.children);t.revealOrder&&"b"===t.revealOrder[0]&&e.reverse();for(var n=e.length;n--;)this.l.set(e[n],this.i=[1,0,this.i]);return t.children},lt.prototype.componentDidUpdate=lt.prototype.componentDidMount=function(){var t=this;t.l.forEach((function(e,n){ct(t,n,e)}))};!function(){function t(){}var e=t.prototype;e.getChildContext=function(){return this.props.context},e.render=function(t){return t.children}}();var st=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|fill|flood|font|glyph(?!R)|horiz|marker(?!H|W|U)|overline|paint|stop|strikethrough|stroke|text(?!L)|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/;m.prototype.isReactComponent={};var ut="undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103;function ft(t,e,n){if(null==e.__k)for(;e.firstChild;)e.removeChild(e.firstChild);return M(t,e),"function"==typeof n&&n(),t?t.__c:null}var ht=r.event;function dt(t,e){t["UNSAFE_"+e]&&!t[e]&&Object.defineProperty(t,e,{configurable:!1,get:function(){return this["UNSAFE_"+e]},set:function(t){this["UNSAFE_"+e]=t}})}r.event=function(t){ht&&(t=ht(t)),t.persist=function(){};var e=!1,n=!1,r=t.stopPropagation;t.stopPropagation=function(){r.call(t),e=!0};var o=t.preventDefault;return t.preventDefault=function(){o.call(t),n=!0},t.isPropagationStopped=function(){return e},t.isDefaultPrevented=function(){return n},t.nativeEvent=t};var pt={configurable:!0,get:function(){return this.class}},_t=r.vnode;r.vnode=function(t){t.$$typeof=ut;var e=t.type,n=t.props;if(e){if(n.class!=n.className&&(pt.enumerable="className"in n,null!=n.className&&(n.class=n.className),Object.defineProperty(n,"className",pt)),"function"!=typeof e){var r,o,i;for(i in n.defaultValue&&void 0!==n.value&&(n.value||0===n.value||(n.value=n.defaultValue),delete n.defaultValue),Array.isArray(n.value)&&n.multiple&&"select"===e&&(E(n.children).forEach((function(t){-1!=n.value.indexOf(t.props.value)&&(t.props.selected=!0)})),delete n.value),n)if(r=st.test(i))break;if(r)for(i in o=t.props={},n)o[st.test(i)?i.replace(/[A-Z0-9]/,"-$&").toLowerCase():i]=n[i]}!function(e){var n=t.type,r=t.props;if(r&&"string"==typeof n){var o={};for(var i in r)/^on(Ani|Tra|Tou)/.test(i)&&(r[i.toLowerCase()]=r[i],delete r[i]),o[i.toLowerCase()]=i;if(o.ondoubleclick&&(r.ondblclick=r[o.ondoubleclick],delete r[o.ondoubleclick]),o.onbeforeinput&&(r.onbeforeinput=r[o.onbeforeinput],delete r[o.onbeforeinput]),o.onchange&&("textarea"===n||"input"===n.toLowerCase()&&!/^fil|che|ra/i.test(r.type))){var a=o.oninput||"oninput";r[a]||(r[a]=r[o.onchange],delete r[o.onchange])}}}(),"function"==typeof e&&!e.m&&e.prototype&&(dt(e.prototype,"componentWillMount"),dt(e.prototype,"componentWillReceiveProps"),dt(e.prototype,"componentWillUpdate"),e.m=!0)}_t&&_t(t)};n(14);var gt=n(0),mt=n(15);function wt(t){return Object(mt.a)(t)}function vt(){const t=wt();return t&&t.os?t.os:void 0}function yt(){const t=vt();return!!t&&t.toLowerCase().includes("android")}function bt(){return!!vt()&&(yt()||function(){const t=vt();return!!t&&(t.toLowerCase().includes("ios")||t.toLowerCase().includes("mac")&&navigator.maxTouchPoints>1)}())}gt.getFromWindow,gt.getFromWindowOrThrow;const xt=gt.getDocumentOrThrow,Et=(gt.getDocument,gt.getNavigatorOrThrow),At=(gt.getNavigator,gt.getLocationOrThrow,gt.getLocation,gt.getCryptoOrThrow,gt.getCrypto,gt.getLocalStorageOrThrow,gt.getLocalStorage);const Ct=function(t){return"string"==typeof t?t:JSON.stringify(t)};function Tt(t,e){const n=encodeURIComponent(t);return e.universalLink?`${e.universalLink}/wc?uri=${n}`:e.deepLink?`${e.deepLink}${e.deepLink.endsWith(":")?"//":"/"}wc?uri=${n}`:""}function Rt(t){const e=t.href.split("?")[0];!function(t,e){const n=Ct(e),r=At();r&&r.setItem(t,n)}("WALLETCONNECT_DEEPLINK_CHOICE",Object.assign(Object.assign({},t),{href:e}))}const kt="https://registry.walletconnect.com";var Pt=function(t){return p("div",{className:"walletconnect-modal__header"},p("img",{src:"data:image/svg+xml,%3Csvg height='185' viewBox='0 0 300 185' width='300' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='m61.4385429 36.2562612c48.9112241-47.8881663 128.2119871-47.8881663 177.1232091 0l5.886545 5.7634174c2.445561 2.3944081 2.445561 6.2765112 0 8.6709204l-20.136695 19.715503c-1.222781 1.1972051-3.2053 1.1972051-4.428081 0l-8.100584-7.9311479c-34.121692-33.4079817-89.443886-33.4079817-123.5655788 0l-8.6750562 8.4936051c-1.2227816 1.1972041-3.205301 1.1972041-4.4280806 0l-20.1366949-19.7155031c-2.4455612-2.3944092-2.4455612-6.2765122 0-8.6709204zm218.7677961 40.7737449 17.921697 17.546897c2.445549 2.3943969 2.445563 6.2764769.000031 8.6708899l-80.810171 79.121134c-2.445544 2.394426-6.410582 2.394453-8.85616.000062-.00001-.00001-.000022-.000022-.000032-.000032l-57.354143-56.154572c-.61139-.598602-1.60265-.598602-2.21404 0-.000004.000004-.000007.000008-.000011.000011l-57.3529212 56.154531c-2.4455368 2.394432-6.4105755 2.394472-8.8561612.000087-.0000143-.000014-.0000296-.000028-.0000449-.000044l-80.81241943-79.122185c-2.44556021-2.394408-2.44556021-6.2765115 0-8.6709197l17.92172963-17.5468673c2.4455602-2.3944082 6.4105989-2.3944082 8.8561602 0l57.3549775 56.155357c.6113908.598602 1.602649.598602 2.2140398 0 .0000092-.000009.0000174-.000017.0000265-.000024l57.3521031-56.155333c2.445505-2.3944633 6.410544-2.3945531 8.856161-.0002.000034.0000336.000068.0000673.000101.000101l57.354902 56.155432c.61139.598601 1.60265.598601 2.21404 0l57.353975-56.1543249c2.445561-2.3944092 6.410599-2.3944092 8.85616 0z' fill='%233b99fc'/%3E%3C/svg%3E",className:"walletconnect-modal__headerLogo"}),p("p",null,"WalletConnect"),p("div",{className:"walletconnect-modal__close__wrapper",onClick:t.onClose},p("div",{id:"walletconnect-qrcode-close",className:"walletconnect-modal__close__icon"},p("div",{className:"walletconnect-modal__close__line1"}),p("div",{className:"walletconnect-modal__close__line2"}))))};var St=function(t){return p("a",{className:"walletconnect-connect__button",href:t.href,id:"walletconnect-connect-button-"+t.name,onClick:t.onClick,rel:"noopener noreferrer",style:{backgroundColor:t.color},target:"_blank"},t.name)};var Bt=function(t){const{color:e,href:n,name:r,logo:o,onClick:i}=t;return p("a",{className:"walletconnect-modal__base__row",href:n,onClick:i,rel:"noopener noreferrer",target:"_blank"},p("h3",{className:"walletconnect-modal__base__row__h3"},r),p("div",{className:"walletconnect-modal__base__row__right"},p("div",{className:"walletconnect-modal__base__row__right__app-icon",style:{background:`url('${o}') ${e}`,backgroundSize:"100%"}}),p("img",{src:"data:image/svg+xml,%3Csvg fill='none' height='18' viewBox='0 0 8 18' width='8' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath clip-rule='evenodd' d='m.586301.213898c-.435947.33907-.5144813.967342-.175411 1.403292l4.87831 6.27212c.28087.36111.28087.86677 0 1.22788l-4.878311 6.27211c-.33907.436-.260536 1.0642.175412 1.4033.435949.3391 1.064219.2605 1.403289-.1754l4.87832-6.2721c.84259-1.08336.84259-2.60034 0-3.68367l-4.87832-6.27212c-.33907-.4359474-.96734-.514482-1.403289-.175412z' fill='%233c4252' fill-rule='evenodd'/%3E%3C/svg%3E",className:"walletconnect-modal__base__row__right__caret"})))};var Nt=function(t){const{color:e,href:n,name:r,logo:o,onClick:i}=t,a=window.innerWidth<768?(r.length>8?2.5:2.7)+"vw":"inherit";return p("a",{className:"walletconnect-connect__button__icon_anchor",href:n,onClick:i,rel:"noopener noreferrer",target:"_blank"},p("div",{className:"walletconnect-connect__button__icon",style:{background:`url('${o}') ${e}`,backgroundSize:"100%"}}),p("div",{style:{fontSize:a},className:"walletconnect-connect__button__text"},r))};var Mt=function(t){const e=yt(),[n,r]=j(""),[o,i]=j(""),[a,l]=j(1),c=o?t.links.filter(t=>t.name.toLowerCase().includes(o.toLowerCase())):t.links,s=t.errorMessage,u=o||c.length>5,f=Math.ceil(c.length/12),h=[12*(a-1)+1,12*a],d=c.length?c.filter((t,e)=>e+1>=h[0]&&e+1<=h[1]):[],_=!(e||!(f>1));let m=void 0;return p("div",null,p("p",{id:"walletconnect-qrcode-text",className:"walletconnect-qrcode__text"},e?t.text.connect_mobile_wallet:t.text.choose_preferred_wallet),!e&&p("input",{className:"walletconnect-search__input",placeholder:"Search",value:n,onChange:function(t){r(t.target.value),clearTimeout(m),t.target.value?m=setTimeout(()=>{i(t.target.value),l(1)},1e3):(r(""),i(""),l(1))}}),p("div",{className:"walletconnect-connect__buttons__wrapper"+(e?"__android":u&&c.length?"__wrap":"")},e?p(St,{name:t.text.connect,color:"rgb(64, 153, 255)",href:t.uri,onClick:$(()=>{Rt({name:"Unknown",href:t.uri})},[])}):d.length?d.map(e=>{const{color:n,name:r,shortName:o,logo:i}=e,a=Tt(t.uri,e),l=$(()=>{Rt({name:r,href:a})},[d]);return u?p(Nt,{color:n,href:a,name:o||r,logo:i,onClick:l}):p(Bt,{color:n,href:a,name:r,logo:i,onClick:l})}):p(g,null,p("p",null,s.length?t.errorMessage:t.links.length&&!c.length?t.text.no_wallets_found:t.text.loading))),_&&p("div",{className:"walletconnect-modal__footer"},Array(f).fill(0).map((t,e)=>{const n=e+1;return p("a",{style:{margin:"auto 10px",fontWeight:a===n?"bold":"normal"},onClick:()=>l(n)},n)})))},Lt=n(4),Ot=n.n(Lt),It=n(16),Ut=n.n(It);var Dt=function(t){return p("div",{className:"walletconnect-qrcode__notification"+(!!t.message.trim()?" notification__show":"")},t.message)};var Yt=function(t){const[e,n]=j(""),[r,o]=j("");return V(()=>{(async()=>{o(await async function(t){let e="";const n=await Ot.a.toString(t,{margin:0,type:"svg"});return"string"==typeof n&&(e=n.replace("<svg",'<svg class="walletconnect-qrcode__image"')),e}(t.uri))})()},[]),p("div",null,p("p",{id:"walletconnect-qrcode-text",className:"walletconnect-qrcode__text"},t.text.scan_qrcode_with_wallet),p("div",{dangerouslySetInnerHTML:{__html:r}}),p("div",{className:"walletconnect-modal__footer"},p("a",{onClick:()=>{Ut()(t.uri)?(n(t.text.copied_to_clipboard),setInterval(()=>n(""),1200)):(n("Error"),setInterval(()=>n(""),1200))}},t.text.copy_to_clipboard)),p(Dt,{message:e}))};var zt=function(t){const e=yt(),n=bt(),r=n?t.qrcodeModalOptions&&t.qrcodeModalOptions.mobileLinks?t.qrcodeModalOptions.mobileLinks:void 0:t.qrcodeModalOptions&&t.qrcodeModalOptions.desktopLinks?t.qrcodeModalOptions.desktopLinks:void 0,[o,i]=j(!1),[a,l]=j(!1),[c,s]=j(!n),u={mobile:n,text:t.text,uri:t.uri,qrcodeModalOptions:t.qrcodeModalOptions},[f,h]=j(""),[d,_]=j(!1),[m,w]=j([]),[v,y]=j(""),b=()=>{a||o||r&&!r.length||m.length>0||V(()=>{(async()=>{if(!e){i(!0);try{const e=t.qrcodeModalOptions&&t.qrcodeModalOptions.registryUrl?t.qrcodeModalOptions.registryUrl:kt+"/api/v2/wallets",o=await fetch(e),a=(await o.json()).listings,c=function(t,e){let n=t;return e&&(n=e.map(e=>function(t,e){return t.filter(t=>t.name.toLowerCase().includes(e.toLowerCase()))[0]}(t,e)).filter(Boolean)),n}(function(t,e="mobile"){return Object.values(t).filter(t=>!!t[e].universal||!!t[e].native).map(t=>function(t,e="mobile"){var n;return{name:t.name||"",shortName:t.metadata.shortName||"",color:t.metadata.colors.primary||"",logo:null!==(n=t.image_url.sm)&&void 0!==n?n:"",universalLink:t[e].universal||"",deepLink:t[e].native||""}}(t,e))}(a,n?"mobile":"desktop"),r);i(!1),l(!0),y(c.length?"":t.text.no_supported_wallets),w(c);const u=1===c.length;u&&(h(Tt(t.uri,c[0])),s(!0)),_(u)}catch(e){i(!1),l(!0),y(t.text.something_went_wrong),console.error(e)}}})()})};b();const x=n?c:!c;return p("div",{id:"walletconnect-qrcode-modal",className:"walletconnect-qrcode__base animated fadeIn"},p("div",{className:"walletconnect-modal__base"},p(Pt,{onClose:t.onClose}),d&&c?p("div",{className:"walletconnect-modal__single_wallet"},p("a",{onClick:()=>Rt({name:m[0].name,href:f}),href:f,rel:"noopener noreferrer",target:"_blank"},t.text.connect_with+" "+(d?m[0].name:"")+" ›")):e||o||!o&&m.length?p("div",{className:"walletconnect-modal__mobile__toggle"+(x?" right__selected":"")},p("div",{className:"walletconnect-modal__mobile__toggle_selector"}),n?p(g,null,p("a",{onClick:()=>(s(!1),b())},t.text.mobile),p("a",{onClick:()=>s(!0)},t.text.qrcode)):p(g,null,p("a",{onClick:()=>s(!0)},t.text.qrcode),p("a",{onClick:()=>(s(!1),b())},t.text.desktop))):null,p("div",null,c||!e&&!o&&!m.length?p(Yt,Object.assign({},u)):p(Mt,Object.assign({},u,{links:m,errorMessage:v})))))};var Wt={de:{choose_preferred_wallet:"Wähle bevorzugte Wallet",connect_mobile_wallet:"Verbinde mit Mobile Wallet",scan_qrcode_with_wallet:"Scanne den QR-code mit einer WalletConnect kompatiblen Wallet",connect:"Verbinden",qrcode:"QR-Code",mobile:"Mobile",desktop:"Desktop",copy_to_clipboard:"In die Zwischenablage kopieren",copied_to_clipboard:"In die Zwischenablage kopiert!",connect_with:"Verbinden mit Hilfe von",loading:"Laden...",something_went_wrong:"Etwas ist schief gelaufen",no_supported_wallets:"Es gibt noch keine unterstützten Wallet",no_wallets_found:"keine Wallet gefunden"},en:{choose_preferred_wallet:"Choose your preferred wallet",connect_mobile_wallet:"Connect to Mobile Wallet",scan_qrcode_with_wallet:"Scan QR code with a WalletConnect-compatible wallet",connect:"Connect",qrcode:"QR Code",mobile:"Mobile",desktop:"Desktop",copy_to_clipboard:"Copy to clipboard",copied_to_clipboard:"Copied to clipboard!",connect_with:"Connect with",loading:"Loading...",something_went_wrong:"Something went wrong",no_supported_wallets:"There are no supported wallets yet",no_wallets_found:"No wallets found"},es:{choose_preferred_wallet:"Elige tu billetera preferida",connect_mobile_wallet:"Conectar a billetera móvil",scan_qrcode_with_wallet:"Escanea el código QR con una billetera compatible con WalletConnect",connect:"Conectar",qrcode:"Código QR",mobile:"Móvil",desktop:"Desktop",copy_to_clipboard:"Copiar",copied_to_clipboard:"Copiado!",connect_with:"Conectar mediante",loading:"Cargando...",something_went_wrong:"Algo salió mal",no_supported_wallets:"Todavía no hay billeteras compatibles",no_wallets_found:"No se encontraron billeteras"},fr:{choose_preferred_wallet:"Choisissez votre portefeuille préféré",connect_mobile_wallet:"Se connecter au portefeuille mobile",scan_qrcode_with_wallet:"Scannez le QR code avec un portefeuille compatible WalletConnect",connect:"Se connecter",qrcode:"QR Code",mobile:"Mobile",desktop:"Desktop",copy_to_clipboard:"Copier",copied_to_clipboard:"Copié!",connect_with:"Connectez-vous à l'aide de",loading:"Chargement...",something_went_wrong:"Quelque chose a mal tourné",no_supported_wallets:"Il n'y a pas encore de portefeuilles pris en charge",no_wallets_found:"Aucun portefeuille trouvé"},ko:{choose_preferred_wallet:"원하는 지갑을 선택하세요",connect_mobile_wallet:"모바일 지갑과 연결",scan_qrcode_with_wallet:"WalletConnect 지원 지갑에서 QR코드를 스캔하세요",connect:"연결",qrcode:"QR 코드",mobile:"모바일",desktop:"데스크탑",copy_to_clipboard:"클립보드에 복사",copied_to_clipboard:"클립보드에 복사되었습니다!",connect_with:"와 연결하다",loading:"로드 중...",something_went_wrong:"문제가 발생했습니다.",no_supported_wallets:"아직 지원되는 지갑이 없습니다",no_wallets_found:"지갑을 찾을 수 없습니다"},pt:{choose_preferred_wallet:"Escolha sua carteira preferida",connect_mobile_wallet:"Conectar-se à carteira móvel",scan_qrcode_with_wallet:"Ler o código QR com uma carteira compatível com WalletConnect",connect:"Conectar",qrcode:"Código QR",mobile:"Móvel",desktop:"Desktop",copy_to_clipboard:"Copiar",copied_to_clipboard:"Copiado!",connect_with:"Ligar por meio de",loading:"Carregamento...",something_went_wrong:"Algo correu mal",no_supported_wallets:"Ainda não há carteiras suportadas",no_wallets_found:"Nenhuma carteira encontrada"},zh:{choose_preferred_wallet:"选择你的钱包",connect_mobile_wallet:"连接至移动端钱包",scan_qrcode_with_wallet:"使用兼容 WalletConnect 的钱包扫描二维码",connect:"连接",qrcode:"二维码",mobile:"移动",desktop:"桌面",copy_to_clipboard:"复制到剪贴板",copied_to_clipboard:"复制到剪贴板成功！",connect_with:"通过以下方式连接",loading:"正在加载...",something_went_wrong:"出了问题",no_supported_wallets:"目前还没有支持的钱包",no_wallets_found:"没有找到钱包"},fa:{choose_preferred_wallet:"کیف پول مورد نظر خود را انتخاب کنید",connect_mobile_wallet:"به کیف پول موبایل وصل شوید",scan_qrcode_with_wallet:"کد QR را با یک کیف پول سازگار با WalletConnect اسکن کنید",connect:"اتصال",qrcode:"کد QR",mobile:"سیار",desktop:"دسکتاپ",copy_to_clipboard:"کپی به کلیپ بورد",copied_to_clipboard:"در کلیپ بورد کپی شد!",connect_with:"ارتباط با",loading:"...بارگذاری",something_went_wrong:"مشکلی پیش آمد",no_supported_wallets:"هنوز هیچ کیف پول پشتیبانی شده ای وجود ندارد",no_wallets_found:"هیچ کیف پولی پیدا نشد"}};function qt(){const t=xt(),e=t.getElementById("walletconnect-qrcode-modal");e&&(e.className=e.className.replace("fadeIn","fadeOut"),setTimeout(()=>{const e=t.getElementById("walletconnect-wrapper");e&&t.body.removeChild(e)},300))}function Ft(t){return()=>{qt(),t&&t()}}function jt(){const t=Et().language.split("-")[0]||"en";return Wt[t]||Wt.en}function Ht(t,e,n){!function(){const t=xt(),e=t.getElementById("walletconnect-style-sheet");e&&t.head.removeChild(e);const n=t.createElement("style");n.setAttribute("id","walletconnect-style-sheet"),n.innerText=':root {\n  --animation-duration: 300ms;\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n\n@keyframes fadeOut {\n  from {\n    opacity: 1;\n  }\n  to {\n    opacity: 0;\n  }\n}\n\n.animated {\n  animation-duration: var(--animation-duration);\n  animation-fill-mode: both;\n}\n\n.fadeIn {\n  animation-name: fadeIn;\n}\n\n.fadeOut {\n  animation-name: fadeOut;\n}\n\n#walletconnect-wrapper {\n  -webkit-user-select: none;\n  align-items: center;\n  display: flex;\n  height: 100%;\n  justify-content: center;\n  left: 0;\n  pointer-events: none;\n  position: fixed;\n  top: 0;\n  user-select: none;\n  width: 100%;\n  z-index: 99999999999999;\n}\n\n.walletconnect-modal__headerLogo {\n  height: 21px;\n}\n\n.walletconnect-modal__header p {\n  color: #ffffff;\n  font-size: 20px;\n  font-weight: 600;\n  margin: 0;\n  align-items: flex-start;\n  display: flex;\n  flex: 1;\n  margin-left: 5px;\n}\n\n.walletconnect-modal__close__wrapper {\n  position: absolute;\n  top: 0px;\n  right: 0px;\n  z-index: 10000;\n  background: white;\n  border-radius: 26px;\n  padding: 6px;\n  box-sizing: border-box;\n  width: 26px;\n  height: 26px;\n  cursor: pointer;\n}\n\n.walletconnect-modal__close__icon {\n  position: relative;\n  top: 7px;\n  right: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transform: rotate(45deg);\n}\n\n.walletconnect-modal__close__line1 {\n  position: absolute;\n  width: 100%;\n  border: 1px solid rgb(48, 52, 59);\n}\n\n.walletconnect-modal__close__line2 {\n  position: absolute;\n  width: 100%;\n  border: 1px solid rgb(48, 52, 59);\n  transform: rotate(90deg);\n}\n\n.walletconnect-qrcode__base {\n  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\n  background: rgba(37, 41, 46, 0.95);\n  height: 100%;\n  left: 0;\n  pointer-events: auto;\n  position: fixed;\n  top: 0;\n  transition: 0.4s cubic-bezier(0.19, 1, 0.22, 1);\n  width: 100%;\n  will-change: opacity;\n  padding: 40px;\n  box-sizing: border-box;\n}\n\n.walletconnect-qrcode__text {\n  color: rgba(60, 66, 82, 0.6);\n  font-size: 16px;\n  font-weight: 600;\n  letter-spacing: 0;\n  line-height: 1.1875em;\n  margin: 10px 0 20px 0;\n  text-align: center;\n  width: 100%;\n}\n\n@media only screen and (max-width: 768px) {\n  .walletconnect-qrcode__text {\n    font-size: 4vw;\n  }\n}\n\n@media only screen and (max-width: 320px) {\n  .walletconnect-qrcode__text {\n    font-size: 14px;\n  }\n}\n\n.walletconnect-qrcode__image {\n  width: calc(100% - 30px);\n  box-sizing: border-box;\n  cursor: none;\n  margin: 0 auto;\n}\n\n.walletconnect-qrcode__notification {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  font-size: 16px;\n  padding: 16px 20px;\n  border-radius: 16px;\n  text-align: center;\n  transition: all 0.1s ease-in-out;\n  background: white;\n  color: black;\n  margin-bottom: -60px;\n  opacity: 0;\n}\n\n.walletconnect-qrcode__notification.notification__show {\n  opacity: 1;\n}\n\n@media only screen and (max-width: 768px) {\n  .walletconnect-modal__header {\n    height: 130px;\n  }\n  .walletconnect-modal__base {\n    overflow: auto;\n  }\n}\n\n@media only screen and (min-device-width: 415px) and (max-width: 768px) {\n  #content {\n    max-width: 768px;\n    box-sizing: border-box;\n  }\n}\n\n@media only screen and (min-width: 375px) and (max-width: 415px) {\n  #content {\n    max-width: 414px;\n    box-sizing: border-box;\n  }\n}\n\n@media only screen and (min-width: 320px) and (max-width: 375px) {\n  #content {\n    max-width: 375px;\n    box-sizing: border-box;\n  }\n}\n\n@media only screen and (max-width: 320px) {\n  #content {\n    max-width: 320px;\n    box-sizing: border-box;\n  }\n}\n\n.walletconnect-modal__base {\n  -webkit-font-smoothing: antialiased;\n  background: #ffffff;\n  border-radius: 24px;\n  box-shadow: 0 10px 50px 5px rgba(0, 0, 0, 0.4);\n  font-family: ui-rounded, "SF Pro Rounded", "SF Pro Text", medium-content-sans-serif-font,\n    -apple-system, BlinkMacSystemFont, ui-sans-serif, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell,\n    "Open Sans", "Helvetica Neue", sans-serif;\n  margin-top: 41px;\n  padding: 24px 24px 22px;\n  pointer-events: auto;\n  position: relative;\n  text-align: center;\n  transition: 0.4s cubic-bezier(0.19, 1, 0.22, 1);\n  will-change: transform;\n  overflow: visible;\n  transform: translateY(-50%);\n  top: 50%;\n  max-width: 500px;\n  margin: auto;\n}\n\n@media only screen and (max-width: 320px) {\n  .walletconnect-modal__base {\n    padding: 24px 12px;\n  }\n}\n\n.walletconnect-modal__base .hidden {\n  transform: translateY(150%);\n  transition: 0.125s cubic-bezier(0.4, 0, 1, 1);\n}\n\n.walletconnect-modal__header {\n  align-items: center;\n  display: flex;\n  height: 26px;\n  left: 0;\n  justify-content: space-between;\n  position: absolute;\n  top: -42px;\n  width: 100%;\n}\n\n.walletconnect-modal__base .wc-logo {\n  align-items: center;\n  display: flex;\n  height: 26px;\n  margin-top: 15px;\n  padding-bottom: 15px;\n  pointer-events: auto;\n}\n\n.walletconnect-modal__base .wc-logo div {\n  background-color: #3399ff;\n  height: 21px;\n  margin-right: 5px;\n  mask-image: url("images/wc-logo.svg") center no-repeat;\n  width: 32px;\n}\n\n.walletconnect-modal__base .wc-logo p {\n  color: #ffffff;\n  font-size: 20px;\n  font-weight: 600;\n  margin: 0;\n}\n\n.walletconnect-modal__base h2 {\n  color: rgba(60, 66, 82, 0.6);\n  font-size: 16px;\n  font-weight: 600;\n  letter-spacing: 0;\n  line-height: 1.1875em;\n  margin: 0 0 19px 0;\n  text-align: center;\n  width: 100%;\n}\n\n.walletconnect-modal__base__row {\n  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\n  align-items: center;\n  border-radius: 20px;\n  cursor: pointer;\n  display: flex;\n  height: 56px;\n  justify-content: space-between;\n  padding: 0 15px;\n  position: relative;\n  margin: 0px 0px 8px;\n  text-align: left;\n  transition: 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);\n  will-change: transform;\n  text-decoration: none;\n}\n\n.walletconnect-modal__base__row:hover {\n  background: rgba(60, 66, 82, 0.06);\n}\n\n.walletconnect-modal__base__row:active {\n  background: rgba(60, 66, 82, 0.06);\n  transform: scale(0.975);\n  transition: 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94);\n}\n\n.walletconnect-modal__base__row__h3 {\n  color: #25292e;\n  font-size: 20px;\n  font-weight: 700;\n  margin: 0;\n  padding-bottom: 3px;\n}\n\n.walletconnect-modal__base__row__right {\n  align-items: center;\n  display: flex;\n  justify-content: center;\n}\n\n.walletconnect-modal__base__row__right__app-icon {\n  border-radius: 8px;\n  height: 34px;\n  margin: 0 11px 2px 0;\n  width: 34px;\n  background-size: 100%;\n  box-shadow: 0 4px 12px 0 rgba(37, 41, 46, 0.25);\n}\n\n.walletconnect-modal__base__row__right__caret {\n  height: 18px;\n  opacity: 0.3;\n  transition: 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94);\n  width: 8px;\n  will-change: opacity;\n}\n\n.walletconnect-modal__base__row:hover .caret,\n.walletconnect-modal__base__row:active .caret {\n  opacity: 0.6;\n}\n\n.walletconnect-modal__mobile__toggle {\n  width: 80%;\n  display: flex;\n  margin: 0 auto;\n  position: relative;\n  overflow: hidden;\n  border-radius: 8px;\n  margin-bottom: 18px;\n  background: #d4d5d9;\n}\n\n.walletconnect-modal__single_wallet {\n  display: flex;\n  justify-content: center;\n  margin-top: 7px;\n  margin-bottom: 18px;\n}\n\n.walletconnect-modal__single_wallet a {\n  cursor: pointer;\n  color: rgb(64, 153, 255);\n  font-size: 21px;\n  font-weight: 800;\n  text-decoration: none !important;\n  margin: 0 auto;\n}\n\n.walletconnect-modal__mobile__toggle_selector {\n  width: calc(50% - 8px);\n  background: white;\n  position: absolute;\n  border-radius: 5px;\n  height: calc(100% - 8px);\n  top: 4px;\n  transition: all 0.2s ease-in-out;\n  transform: translate3d(4px, 0, 0);\n}\n\n.walletconnect-modal__mobile__toggle.right__selected .walletconnect-modal__mobile__toggle_selector {\n  transform: translate3d(calc(100% + 12px), 0, 0);\n}\n\n.walletconnect-modal__mobile__toggle a {\n  font-size: 12px;\n  width: 50%;\n  text-align: center;\n  padding: 8px;\n  margin: 0;\n  font-weight: 600;\n  z-index: 1;\n}\n\n.walletconnect-modal__footer {\n  display: flex;\n  justify-content: center;\n  margin-top: 20px;\n}\n\n@media only screen and (max-width: 768px) {\n  .walletconnect-modal__footer {\n    margin-top: 5vw;\n  }\n}\n\n.walletconnect-modal__footer a {\n  cursor: pointer;\n  color: #898d97;\n  font-size: 15px;\n  margin: 0 auto;\n}\n\n@media only screen and (max-width: 320px) {\n  .walletconnect-modal__footer a {\n    font-size: 14px;\n  }\n}\n\n.walletconnect-connect__buttons__wrapper {\n  max-height: 44vh;\n}\n\n.walletconnect-connect__buttons__wrapper__android {\n  margin: 50% 0;\n}\n\n.walletconnect-connect__buttons__wrapper__wrap {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  margin: 10px 0;\n}\n\n@media only screen and (min-width: 768px) {\n  .walletconnect-connect__buttons__wrapper__wrap {\n    margin-top: 40px;\n  }\n}\n\n.walletconnect-connect__button {\n  background-color: rgb(64, 153, 255);\n  padding: 12px;\n  border-radius: 8px;\n  text-decoration: none;\n  color: rgb(255, 255, 255);\n  font-weight: 500;\n}\n\n.walletconnect-connect__button__icon_anchor {\n  cursor: pointer;\n  display: flex;\n  justify-content: flex-start;\n  align-items: center;\n  margin: 8px;\n  width: 42px;\n  justify-self: center;\n  flex-direction: column;\n  text-decoration: none !important;\n}\n\n@media only screen and (max-width: 320px) {\n  .walletconnect-connect__button__icon_anchor {\n    margin: 4px;\n  }\n}\n\n.walletconnect-connect__button__icon {\n  border-radius: 10px;\n  height: 42px;\n  margin: 0;\n  width: 42px;\n  background-size: cover !important;\n  box-shadow: 0 4px 12px 0 rgba(37, 41, 46, 0.25);\n}\n\n.walletconnect-connect__button__text {\n  color: #424952;\n  font-size: 2.7vw;\n  text-decoration: none !important;\n  padding: 0;\n  margin-top: 1.8vw;\n  font-weight: 600;\n}\n\n@media only screen and (min-width: 768px) {\n  .walletconnect-connect__button__text {\n    font-size: 16px;\n    margin-top: 12px;\n  }\n}\n\n.walletconnect-search__input {\n  border: none;\n  background: #d4d5d9;\n  border-style: none;\n  padding: 8px 16px;\n  outline: none;\n  font-style: normal;\n  font-stretch: normal;\n  font-size: 16px;\n  font-style: normal;\n  font-stretch: normal;\n  line-height: normal;\n  letter-spacing: normal;\n  text-align: left;\n  border-radius: 8px;\n  width: calc(100% - 16px);\n  margin: 0;\n  margin-bottom: 8px;\n}\n',t.head.appendChild(n)}();const r=function(){const t=xt(),e=t.createElement("div");return e.setAttribute("id","walletconnect-wrapper"),t.body.appendChild(e),e}();ft(p(zt,{text:jt(),uri:t,onClose:Ft(e),qrcodeModalOptions:n}),r)}function Vt(){qt()}n.d(e,"b",(function(){return Ht})),n.d(e,"a",(function(){return Vt}))},function(t,e){var n,r,o=t.exports={};function i(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function l(t){if(n===setTimeout)return setTimeout(t,0);if((n===i||!n)&&setTimeout)return n=setTimeout,setTimeout(t,0);try{return n(t,0)}catch(e){try{return n.call(null,t,0)}catch(e){return n.call(this,t,0)}}}!function(){try{n="function"==typeof setTimeout?setTimeout:i}catch(t){n=i}try{r="function"==typeof clearTimeout?clearTimeout:a}catch(t){r=a}}();var c,s=[],u=!1,f=-1;function h(){u&&c&&(u=!1,c.length?s=c.concat(s):f=-1,s.length&&d())}function d(){if(!u){var t=l(h);u=!0;for(var e=s.length;e;){for(c=s,s=[];++f<e;)c&&c[f].run();f=-1,e=s.length}c=null,u=!1,function(t){if(r===clearTimeout)return clearTimeout(t);if((r===a||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(t);try{r(t)}catch(e){try{return r.call(null,t)}catch(e){return r.call(this,t)}}}(t)}}function p(t,e){this.fun=t,this.array=e}function _(){}o.nextTick=function(t){var e=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)e[n-1]=arguments[n];s.push(new p(t,e)),1!==s.length||u||l(d)},p.prototype.run=function(){this.fun.apply(null,this.array)},o.title="browser",o.browser=!0,o.env={},o.argv=[],o.version="",o.versions={},o.on=_,o.addListener=_,o.once=_,o.off=_,o.removeListener=_,o.removeAllListeners=_,o.emit=_,o.prependListener=_,o.prependOnceListener=_,o.listeners=function(t){return[]},o.binding=function(t){throw new Error("process.binding is not supported")},o.cwd=function(){return"/"},o.chdir=function(t){throw new Error("process.chdir is not supported")},o.umask=function(){return 0}},function(t,e,n){var r=n(6),o=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],i=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];e.getBlocksCount=function(t,e){switch(e){case r.L:return o[4*(t-1)+0];case r.M:return o[4*(t-1)+1];case r.Q:return o[4*(t-1)+2];case r.H:return o[4*(t-1)+3];default:return}},e.getTotalCodewordsCount=function(t,e){switch(e){case r.L:return i[4*(t-1)+0];case r.M:return i[4*(t-1)+1];case r.Q:return i[4*(t-1)+2];case r.H:return i[4*(t-1)+3];default:return}}},function(t,e){e.isValid=function(t){return!isNaN(t)&&t>=1&&t<=40}},function(t,e){var n="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+",r="(?:(?![A-Z0-9 $%*+\\-./:]|"+(n=n.replace(/u/g,"\\u"))+")(?:.|[\r\n]))+";e.KANJI=new RegExp(n,"g"),e.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),e.BYTE=new RegExp(r,"g"),e.NUMERIC=new RegExp("[0-9]+","g"),e.ALPHANUMERIC=new RegExp("[A-Z $%*+\\-./:]+","g");var o=new RegExp("^"+n+"$"),i=new RegExp("^[0-9]+$"),a=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");e.testKanji=function(t){return o.test(t)},e.testNumeric=function(t){return i.test(t)},e.testAlphanumeric=function(t){return a.test(t)}},function(t,e){function n(t){if("number"==typeof t&&(t=t.toString()),"string"!=typeof t)throw new Error("Color should be defined as hex string");var e=t.slice().replace("#","").split("");if(e.length<3||5===e.length||e.length>8)throw new Error("Invalid hex color: "+t);3!==e.length&&4!==e.length||(e=Array.prototype.concat.apply([],e.map((function(t){return[t,t]})))),6===e.length&&e.push("F","F");var n=parseInt(e.join(""),16);return{r:n>>24&255,g:n>>16&255,b:n>>8&255,a:255&n,hex:"#"+e.slice(0,6).join("")}}e.getOptions=function(t){t||(t={}),t.color||(t.color={});var e=void 0===t.margin||null===t.margin||t.margin<0?4:t.margin,r=t.width&&t.width>=21?t.width:void 0,o=t.scale||4;return{width:r,scale:r?4:o,margin:e,color:{dark:n(t.color.dark||"#000000ff"),light:n(t.color.light||"#ffffffff")},type:t.type,rendererOpts:t.rendererOpts||{}}},e.getScale=function(t,e){return e.width&&e.width>=t+2*e.margin?e.width/(t+2*e.margin):e.scale},e.getImageWidth=function(t,n){var r=e.getScale(t,n);return Math.floor((t+2*n.margin)*r)},e.qrToImageData=function(t,n,r){for(var o=n.modules.size,i=n.modules.data,a=e.getScale(o,r),l=Math.floor((o+2*r.margin)*a),c=r.margin*a,s=[r.color.light,r.color.dark],u=0;u<l;u++)for(var f=0;f<l;f++){var h=4*(u*l+f),d=r.color.light;if(u>=c&&f>=c&&u<l-c&&f<l-c)d=s[i[Math.floor((u-c)/a)*o+Math.floor((f-c)/a)]?1:0];t[h++]=d.r,t[h++]=d.g,t[h++]=d.b,t[h]=d.a}}},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getWindowMetadata=void 0;const r=n(0);e.getWindowMetadata=function(){let t,e;try{t=r.getDocumentOrThrow(),e=r.getLocationOrThrow()}catch(t){return null}function n(...e){const n=t.getElementsByTagName("meta");for(let t=0;t<n.length;t++){const r=n[t],o=["itemprop","property","name"].map(t=>r.getAttribute(t)).filter(t=>!!t&&e.includes(t));if(o.length&&o){const t=r.getAttribute("content");if(t)return t}}return""}const o=function(){let e=n("name","og:site_name","og:title","twitter:title");return e||(e=t.title),e}();return{description:n("description","og:description","twitter:description","keywords"),url:e.origin,icons:function(){const n=t.getElementsByTagName("link"),r=[];for(let t=0;t<n.length;t++){const o=n[t],i=o.getAttribute("rel");if(i&&i.toLowerCase().indexOf("icon")>-1){const t=o.getAttribute("href");if(t)if(-1===t.toLowerCase().indexOf("https:")&&-1===t.toLowerCase().indexOf("http:")&&0!==t.indexOf("//")){let n=e.protocol+"//"+e.host;if(0===t.indexOf("/"))n+=t;else{const r=e.pathname.split("/");r.pop();n+=r.join("/")+"/"+t}r.push(n)}else if(0===t.indexOf("//")){const n=e.protocol+t;r.push(n)}else r.push(t)}}return r}(),name:o}}},function(t,e,n){"use strict";(function(t){n.d(e,"a",(function(){return h}));var r=function(){for(var t=0,e=0,n=arguments.length;e<n;e++)t+=arguments[e].length;var r=Array(t),o=0;for(e=0;e<n;e++)for(var i=arguments[e],a=0,l=i.length;a<l;a++,o++)r[o]=i[a];return r},o=function(t,e,n){this.name=t,this.version=e,this.os=n,this.type="browser"},i=function(e){this.version=e,this.type="node",this.name="node",this.os=t.platform},a=function(t,e,n,r){this.name=t,this.version=e,this.os=n,this.bot=r,this.type="bot-device"},l=function(){this.type="bot",this.bot=!0,this.name="bot",this.version=null,this.os=null},c=function(){this.type="react-native",this.name="react-native",this.version=null,this.os=null},s=/(nuhk|Googlebot|Yammybot|Openbot|Slurp|MSNBot|Ask\ Jeeves\/Teoma|ia_archiver)/,u=[["aol",/AOLShield\/([0-9\._]+)/],["edge",/Edge\/([0-9\._]+)/],["edge-ios",/EdgiOS\/([0-9\._]+)/],["yandexbrowser",/YaBrowser\/([0-9\._]+)/],["kakaotalk",/KAKAOTALK\s([0-9\.]+)/],["samsung",/SamsungBrowser\/([0-9\.]+)/],["silk",/\bSilk\/([0-9._-]+)\b/],["miui",/MiuiBrowser\/([0-9\.]+)$/],["beaker",/BeakerBrowser\/([0-9\.]+)/],["edge-chromium",/EdgA?\/([0-9\.]+)/],["chromium-webview",/(?!Chrom.*OPR)wv\).*Chrom(?:e|ium)\/([0-9\.]+)(:?\s|$)/],["chrome",/(?!Chrom.*OPR)Chrom(?:e|ium)\/([0-9\.]+)(:?\s|$)/],["phantomjs",/PhantomJS\/([0-9\.]+)(:?\s|$)/],["crios",/CriOS\/([0-9\.]+)(:?\s|$)/],["firefox",/Firefox\/([0-9\.]+)(?:\s|$)/],["fxios",/FxiOS\/([0-9\.]+)/],["opera-mini",/Opera Mini.*Version\/([0-9\.]+)/],["opera",/Opera\/([0-9\.]+)(?:\s|$)/],["opera",/OPR\/([0-9\.]+)(:?\s|$)/],["ie",/Trident\/7\.0.*rv\:([0-9\.]+).*\).*Gecko$/],["ie",/MSIE\s([0-9\.]+);.*Trident\/[4-7].0/],["ie",/MSIE\s(7\.0)/],["bb10",/BB10;\sTouch.*Version\/([0-9\.]+)/],["android",/Android\s([0-9\.]+)/],["ios",/Version\/([0-9\._]+).*Mobile.*Safari.*/],["safari",/Version\/([0-9\._]+).*Safari/],["facebook",/FBAV\/([0-9\.]+)/],["instagram",/Instagram\s([0-9\.]+)/],["ios-webview",/AppleWebKit\/([0-9\.]+).*Mobile/],["ios-webview",/AppleWebKit\/([0-9\.]+).*Gecko\)$/],["searchbot",/alexa|bot|crawl(er|ing)|facebookexternalhit|feedburner|google web preview|nagios|postrank|pingdom|slurp|spider|yahoo!|yandex/]],f=[["iOS",/iP(hone|od|ad)/],["Android OS",/Android/],["BlackBerry OS",/BlackBerry|BB10/],["Windows Mobile",/IEMobile/],["Amazon OS",/Kindle/],["Windows 3.11",/Win16/],["Windows 95",/(Windows 95)|(Win95)|(Windows_95)/],["Windows 98",/(Windows 98)|(Win98)/],["Windows 2000",/(Windows NT 5.0)|(Windows 2000)/],["Windows XP",/(Windows NT 5.1)|(Windows XP)/],["Windows Server 2003",/(Windows NT 5.2)/],["Windows Vista",/(Windows NT 6.0)/],["Windows 7",/(Windows NT 6.1)/],["Windows 8",/(Windows NT 6.2)/],["Windows 8.1",/(Windows NT 6.3)/],["Windows 10",/(Windows NT 10.0)/],["Windows ME",/Windows ME/],["Open BSD",/OpenBSD/],["Sun OS",/SunOS/],["Chrome OS",/CrOS/],["Linux",/(Linux)|(X11)/],["Mac OS",/(Mac_PowerPC)|(Macintosh)/],["QNX",/QNX/],["BeOS",/BeOS/],["OS/2",/OS\/2/]];function h(e){return e?p(e):"undefined"==typeof document&&"undefined"!=typeof navigator&&"ReactNative"===navigator.product?new c:"undefined"!=typeof navigator?p(navigator.userAgent):void 0!==t&&t.version?new i(t.version.slice(1)):null}function d(t){return""!==t&&u.reduce((function(e,n){var r=n[0],o=n[1];if(e)return e;var i=o.exec(t);return!!i&&[r,i]}),!1)}function p(t){var e=d(t);if(!e)return null;var n=e[0],i=e[1];if("searchbot"===n)return new l;var c=i[1]&&i[1].split(/[._]/).slice(0,3);c?c.length<3&&(c=r(c,function(t){for(var e=[],n=0;n<t;n++)e.push("0");return e}(3-c.length))):c=[];var u=c.join("."),h=function(t){for(var e=0,n=f.length;e<n;e++){var r=f[e],o=r[0];if(r[1].exec(t))return o}return null}(t),p=s.exec(t);return p&&p[1]?new a(n,u,h,p[1]):new o(n,u,h)}}).call(this,n(9))},function(t,e,n){"use strict";var r=n(43),o={"text/plain":"Text","text/html":"Url",default:"Text"};t.exports=function(t,e){var n,i,a,l,c,s,u=!1;e||(e={}),n=e.debug||!1;try{if(a=r(),l=document.createRange(),c=document.getSelection(),(s=document.createElement("span")).textContent=t,s.style.all="unset",s.style.position="fixed",s.style.top=0,s.style.clip="rect(0, 0, 0, 0)",s.style.whiteSpace="pre",s.style.webkitUserSelect="text",s.style.MozUserSelect="text",s.style.msUserSelect="text",s.style.userSelect="text",s.addEventListener("copy",(function(r){if(r.stopPropagation(),e.format)if(r.preventDefault(),void 0===r.clipboardData){n&&console.warn("unable to use e.clipboardData"),n&&console.warn("trying IE specific stuff"),window.clipboardData.clearData();var i=o[e.format]||o.default;window.clipboardData.setData(i,t)}else r.clipboardData.clearData(),r.clipboardData.setData(e.format,t);e.onCopy&&(r.preventDefault(),e.onCopy(r.clipboardData))})),document.body.appendChild(s),l.selectNodeContents(s),c.addRange(l),!document.execCommand("copy"))throw new Error("copy command was unsuccessful");u=!0}catch(r){n&&console.error("unable to copy using execCommand: ",r),n&&console.warn("trying IE specific stuff");try{window.clipboardData.setData(e.format||"text",t),e.onCopy&&e.onCopy(window.clipboardData),u=!0}catch(r){n&&console.error("unable to copy using clipboardData: ",r),n&&console.error("falling back to prompt"),i=function(t){var e=(/mac os x/i.test(navigator.userAgent)?"⌘":"Ctrl")+"+C";return t.replace(/#{\s*key\s*}/g,e)}("message"in e?e.message:"Copy to clipboard: #{key}, Enter"),window.prompt(i,t)}}finally{c&&("function"==typeof c.removeRange?c.removeRange(l):c.removeAllRanges()),s&&document.body.removeChild(s),a()}return u}},function(t,e,n){"use strict";n.r(e),function(t){var r=n(7),o=n(8);const i=()=>void 0!==t&&void 0!==t.versions&&void 0!==t.versions.node;e.default={open:function(t,e,n){console.log(t),i()?r.b(t):o.b(t,e,n)},close:function(){i()?r.a():o.a()}}}.call(this,n(9))},function(t,e){t.exports=function(){return"function"==typeof Promise&&Promise.prototype&&Promise.prototype.then}},function(t,e,n){var r=n(3),o=n(1),i=n(6),a=n(20),l=n(21),c=n(22),s=n(23),u=n(24),f=n(10),h=n(25),d=n(33),p=n(34),_=n(2),g=n(35),m=n(5);function w(t,e,n){var r,o,i=t.size,a=p.getEncodedBits(e,n);for(r=0;r<15;r++)o=1==(a>>r&1),r<6?t.set(r,8,o,!0):r<8?t.set(r+1,8,o,!0):t.set(i-15+r,8,o,!0),r<8?t.set(8,i-r-1,o,!0):r<9?t.set(8,15-r-1+1,o,!0):t.set(8,15-r-1,o,!0);t.set(i-8,8,1,!0)}function v(t,e,n){var i=new a;n.forEach((function(e){i.put(e.mode.bit,4),i.put(e.getLength(),_.getCharCountIndicator(e.mode,t)),e.write(i)}));var l=8*(o.getSymbolTotalCodewords(t)-f.getTotalCodewordsCount(t,e));for(i.getLengthInBits()+4<=l&&i.put(0,4);i.getLengthInBits()%8!=0;)i.putBit(0);for(var c=(l-i.getLengthInBits())/8,s=0;s<c;s++)i.put(s%2?17:236,8);return function(t,e,n){for(var i=o.getSymbolTotalCodewords(e),a=f.getTotalCodewordsCount(e,n),l=i-a,c=f.getBlocksCount(e,n),s=c-i%c,u=Math.floor(i/c),d=Math.floor(l/c),p=d+1,_=u-d,g=new h(_),m=0,w=new Array(c),v=new Array(c),y=0,b=r.from(t.buffer),x=0;x<c;x++){var E=x<s?d:p;w[x]=b.slice(m,m+E),v[x]=g.encode(w[x]),m+=E,y=Math.max(y,E)}var A,C,T=r.alloc(i),R=0;for(A=0;A<y;A++)for(C=0;C<c;C++)A<w[C].length&&(T[R++]=w[C][A]);for(A=0;A<_;A++)for(C=0;C<c;C++)T[R++]=v[C][A];return T}(i,t,e)}function y(t,e,n,r){var i;if(m(t))i=g.fromArray(t);else{if("string"!=typeof t)throw new Error("Invalid data");var a=e;if(!a){var f=g.rawSplit(t);a=d.getBestVersionForData(f,n)}i=g.fromString(t,a||40)}var h=d.getBestVersionForData(i,n);if(!h)throw new Error("The amount of data is too big to be stored in a QR Code");if(e){if(e<h)throw new Error("\nThe chosen QR Code version cannot contain this amount of data.\nMinimum version required to store current data is: "+h+".\n")}else e=h;var p=v(e,n,i),_=o.getSymbolSize(e),y=new l(_);return function(t,e){for(var n=t.size,r=s.getPositions(e),o=0;o<r.length;o++)for(var i=r[o][0],a=r[o][1],l=-1;l<=7;l++)if(!(i+l<=-1||n<=i+l))for(var c=-1;c<=7;c++)a+c<=-1||n<=a+c||(l>=0&&l<=6&&(0===c||6===c)||c>=0&&c<=6&&(0===l||6===l)||l>=2&&l<=4&&c>=2&&c<=4?t.set(i+l,a+c,!0,!0):t.set(i+l,a+c,!1,!0))}(y,e),function(t){for(var e=t.size,n=8;n<e-8;n++){var r=n%2==0;t.set(n,6,r,!0),t.set(6,n,r,!0)}}(y),function(t,e){for(var n=c.getPositions(e),r=0;r<n.length;r++)for(var o=n[r][0],i=n[r][1],a=-2;a<=2;a++)for(var l=-2;l<=2;l++)-2===a||2===a||-2===l||2===l||0===a&&0===l?t.set(o+a,i+l,!0,!0):t.set(o+a,i+l,!1,!0)}(y,e),w(y,n,0),e>=7&&function(t,e){for(var n,r,o,i=t.size,a=d.getEncodedBits(e),l=0;l<18;l++)n=Math.floor(l/3),r=l%3+i-8-3,o=1==(a>>l&1),t.set(n,r,o,!0),t.set(r,n,o,!0)}(y,e),function(t,e){for(var n=t.size,r=-1,o=n-1,i=7,a=0,l=n-1;l>0;l-=2)for(6===l&&l--;;){for(var c=0;c<2;c++)if(!t.isReserved(o,l-c)){var s=!1;a<e.length&&(s=1==(e[a]>>>i&1)),t.set(o,l-c,s),-1===--i&&(a++,i=7)}if((o+=r)<0||n<=o){o-=r,r=-r;break}}}(y,p),isNaN(r)&&(r=u.getBestMask(y,w.bind(null,y,n))),u.applyMask(r,y),w(y,n,r),{modules:y,version:e,errorCorrectionLevel:n,maskPattern:r,segments:i}}e.create=function(t,e){if(void 0===t||""===t)throw new Error("No input text");var n,r,a=i.M;return void 0!==e&&(a=i.from(e.errorCorrectionLevel,i.M),n=d.from(e.version),r=u.from(e.maskPattern),e.toSJISFunc&&o.setToSJISFunction(e.toSJISFunc)),y(t,n,a,r)}},function(t,e){function n(){this.buffer=[],this.length=0}n.prototype={get:function(t){var e=Math.floor(t/8);return 1==(this.buffer[e]>>>7-t%8&1)},put:function(t,e){for(var n=0;n<e;n++)this.putBit(1==(t>>>e-n-1&1))},getLengthInBits:function(){return this.length},putBit:function(t){var e=Math.floor(this.length/8);this.buffer.length<=e&&this.buffer.push(0),t&&(this.buffer[e]|=128>>>this.length%8),this.length++}},t.exports=n},function(t,e,n){var r=n(3);function o(t){if(!t||t<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=t,this.data=r.alloc(t*t),this.reservedBit=r.alloc(t*t)}o.prototype.set=function(t,e,n,r){var o=t*this.size+e;this.data[o]=n,r&&(this.reservedBit[o]=!0)},o.prototype.get=function(t,e){return this.data[t*this.size+e]},o.prototype.xor=function(t,e,n){this.data[t*this.size+e]^=n},o.prototype.isReserved=function(t,e){return this.reservedBit[t*this.size+e]},t.exports=o},function(t,e,n){var r=n(1).getSymbolSize;e.getRowColCoords=function(t){if(1===t)return[];for(var e=Math.floor(t/7)+2,n=r(t),o=145===n?26:2*Math.ceil((n-13)/(2*e-2)),i=[n-7],a=1;a<e-1;a++)i[a]=i[a-1]-o;return i.push(6),i.reverse()},e.getPositions=function(t){for(var n=[],r=e.getRowColCoords(t),o=r.length,i=0;i<o;i++)for(var a=0;a<o;a++)0===i&&0===a||0===i&&a===o-1||i===o-1&&0===a||n.push([r[i],r[a]]);return n}},function(t,e,n){var r=n(1).getSymbolSize;e.getPositions=function(t){var e=r(t);return[[0,0],[e-7,0],[0,e-7]]}},function(t,e){e.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};var n=3,r=3,o=40,i=10;function a(t,n,r){switch(t){case e.Patterns.PATTERN000:return(n+r)%2==0;case e.Patterns.PATTERN001:return n%2==0;case e.Patterns.PATTERN010:return r%3==0;case e.Patterns.PATTERN011:return(n+r)%3==0;case e.Patterns.PATTERN100:return(Math.floor(n/2)+Math.floor(r/3))%2==0;case e.Patterns.PATTERN101:return n*r%2+n*r%3==0;case e.Patterns.PATTERN110:return(n*r%2+n*r%3)%2==0;case e.Patterns.PATTERN111:return(n*r%3+(n+r)%2)%2==0;default:throw new Error("bad maskPattern:"+t)}}e.isValid=function(t){return null!=t&&""!==t&&!isNaN(t)&&t>=0&&t<=7},e.from=function(t){return e.isValid(t)?parseInt(t,10):void 0},e.getPenaltyN1=function(t){for(var e=t.size,r=0,o=0,i=0,a=null,l=null,c=0;c<e;c++){o=i=0,a=l=null;for(var s=0;s<e;s++){var u=t.get(c,s);u===a?o++:(o>=5&&(r+=n+(o-5)),a=u,o=1),(u=t.get(s,c))===l?i++:(i>=5&&(r+=n+(i-5)),l=u,i=1)}o>=5&&(r+=n+(o-5)),i>=5&&(r+=n+(i-5))}return r},e.getPenaltyN2=function(t){for(var e=t.size,n=0,o=0;o<e-1;o++)for(var i=0;i<e-1;i++){var a=t.get(o,i)+t.get(o,i+1)+t.get(o+1,i)+t.get(o+1,i+1);4!==a&&0!==a||n++}return n*r},e.getPenaltyN3=function(t){for(var e=t.size,n=0,r=0,i=0,a=0;a<e;a++){r=i=0;for(var l=0;l<e;l++)r=r<<1&2047|t.get(a,l),l>=10&&(1488===r||93===r)&&n++,i=i<<1&2047|t.get(l,a),l>=10&&(1488===i||93===i)&&n++}return n*o},e.getPenaltyN4=function(t){for(var e=0,n=t.data.length,r=0;r<n;r++)e+=t.data[r];return Math.abs(Math.ceil(100*e/n/5)-10)*i},e.applyMask=function(t,e){for(var n=e.size,r=0;r<n;r++)for(var o=0;o<n;o++)e.isReserved(o,r)||e.xor(o,r,a(t,o,r))},e.getBestMask=function(t,n){for(var r=Object.keys(e.Patterns).length,o=0,i=1/0,a=0;a<r;a++){n(a),e.applyMask(a,t);var l=e.getPenaltyN1(t)+e.getPenaltyN2(t)+e.getPenaltyN3(t)+e.getPenaltyN4(t);e.applyMask(a,t),l<i&&(i=l,o=a)}return o}},function(t,e,n){var r=n(3),o=n(26),i=n(28).Buffer;function a(t){this.genPoly=void 0,this.degree=t,this.degree&&this.initialize(this.degree)}a.prototype.initialize=function(t){this.degree=t,this.genPoly=o.generateECPolynomial(this.degree)},a.prototype.encode=function(t){if(!this.genPoly)throw new Error("Encoder not initialized");var e=r.alloc(this.degree),n=i.concat([t,e],t.length+this.degree),a=o.mod(n,this.genPoly),l=this.degree-a.length;if(l>0){var c=r.alloc(this.degree);return a.copy(c,l),c}return a},t.exports=a},function(t,e,n){var r=n(3),o=n(27);e.mul=function(t,e){for(var n=r.alloc(t.length+e.length-1),i=0;i<t.length;i++)for(var a=0;a<e.length;a++)n[i+a]^=o.mul(t[i],e[a]);return n},e.mod=function(t,e){for(var n=r.from(t);n.length-e.length>=0;){for(var i=n[0],a=0;a<e.length;a++)n[a]^=o.mul(e[a],i);for(var l=0;l<n.length&&0===n[l];)l++;n=n.slice(l)}return n},e.generateECPolynomial=function(t){for(var n=r.from([1]),i=0;i<t;i++)n=e.mul(n,[1,o.exp(i)]);return n}},function(t,e,n){var r=n(3),o=r.alloc(512),i=r.alloc(256);!function(){for(var t=1,e=0;e<255;e++)o[e]=t,i[t]=e,256&(t<<=1)&&(t^=285);for(e=255;e<512;e++)o[e]=o[e-255]}(),e.log=function(t){if(t<1)throw new Error("log("+t+")");return i[t]},e.exp=function(t){return o[t]},e.mul=function(t,e){return 0===t||0===e?0:o[i[t]+i[e]]}},function(t,e,n){"use strict";(function(t){
/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <http://feross.org>
 * @license  MIT
 */
var r=n(30),o=n(31),i=n(32);function a(){return c.TYPED_ARRAY_SUPPORT?**********:**********}function l(t,e){if(a()<e)throw new RangeError("Invalid typed array length");return c.TYPED_ARRAY_SUPPORT?(t=new Uint8Array(e)).__proto__=c.prototype:(null===t&&(t=new c(e)),t.length=e),t}function c(t,e,n){if(!(c.TYPED_ARRAY_SUPPORT||this instanceof c))return new c(t,e,n);if("number"==typeof t){if("string"==typeof e)throw new Error("If encoding is specified then the first argument must be a string");return f(this,t)}return s(this,t,e,n)}function s(t,e,n,r){if("number"==typeof e)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&e instanceof ArrayBuffer?function(t,e,n,r){if(e.byteLength,n<0||e.byteLength<n)throw new RangeError("'offset' is out of bounds");if(e.byteLength<n+(r||0))throw new RangeError("'length' is out of bounds");e=void 0===n&&void 0===r?new Uint8Array(e):void 0===r?new Uint8Array(e,n):new Uint8Array(e,n,r);c.TYPED_ARRAY_SUPPORT?(t=e).__proto__=c.prototype:t=h(t,e);return t}(t,e,n,r):"string"==typeof e?function(t,e,n){"string"==typeof n&&""!==n||(n="utf8");if(!c.isEncoding(n))throw new TypeError('"encoding" must be a valid string encoding');var r=0|p(e,n),o=(t=l(t,r)).write(e,n);o!==r&&(t=t.slice(0,o));return t}(t,e,n):function(t,e){if(c.isBuffer(e)){var n=0|d(e.length);return 0===(t=l(t,n)).length||e.copy(t,0,0,n),t}if(e){if("undefined"!=typeof ArrayBuffer&&e.buffer instanceof ArrayBuffer||"length"in e)return"number"!=typeof e.length||(r=e.length)!=r?l(t,0):h(t,e);if("Buffer"===e.type&&i(e.data))return h(t,e.data)}var r;throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(t,e)}function u(t){if("number"!=typeof t)throw new TypeError('"size" argument must be a number');if(t<0)throw new RangeError('"size" argument must not be negative')}function f(t,e){if(u(e),t=l(t,e<0?0:0|d(e)),!c.TYPED_ARRAY_SUPPORT)for(var n=0;n<e;++n)t[n]=0;return t}function h(t,e){var n=e.length<0?0:0|d(e.length);t=l(t,n);for(var r=0;r<n;r+=1)t[r]=255&e[r];return t}function d(t){if(t>=a())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+a().toString(16)+" bytes");return 0|t}function p(t,e){if(c.isBuffer(t))return t.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(t)||t instanceof ArrayBuffer))return t.byteLength;"string"!=typeof t&&(t=""+t);var n=t.length;if(0===n)return 0;for(var r=!1;;)switch(e){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":case void 0:return z(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return W(t).length;default:if(r)return z(t).length;e=(""+e).toLowerCase(),r=!0}}function _(t,e,n){var r=!1;if((void 0===e||e<0)&&(e=0),e>this.length)return"";if((void 0===n||n>this.length)&&(n=this.length),n<=0)return"";if((n>>>=0)<=(e>>>=0))return"";for(t||(t="utf8");;)switch(t){case"hex":return P(this,e,n);case"utf8":case"utf-8":return T(this,e,n);case"ascii":return R(this,e,n);case"latin1":case"binary":return k(this,e,n);case"base64":return C(this,e,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return S(this,e,n);default:if(r)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),r=!0}}function g(t,e,n){var r=t[e];t[e]=t[n],t[n]=r}function m(t,e,n,r,o){if(0===t.length)return-1;if("string"==typeof n?(r=n,n=0):n>**********?n=**********:n<-2147483648&&(n=-2147483648),n=+n,isNaN(n)&&(n=o?0:t.length-1),n<0&&(n=t.length+n),n>=t.length){if(o)return-1;n=t.length-1}else if(n<0){if(!o)return-1;n=0}if("string"==typeof e&&(e=c.from(e,r)),c.isBuffer(e))return 0===e.length?-1:w(t,e,n,r,o);if("number"==typeof e)return e&=255,c.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?o?Uint8Array.prototype.indexOf.call(t,e,n):Uint8Array.prototype.lastIndexOf.call(t,e,n):w(t,[e],n,r,o);throw new TypeError("val must be string, number or Buffer")}function w(t,e,n,r,o){var i,a=1,l=t.length,c=e.length;if(void 0!==r&&("ucs2"===(r=String(r).toLowerCase())||"ucs-2"===r||"utf16le"===r||"utf-16le"===r)){if(t.length<2||e.length<2)return-1;a=2,l/=2,c/=2,n/=2}function s(t,e){return 1===a?t[e]:t.readUInt16BE(e*a)}if(o){var u=-1;for(i=n;i<l;i++)if(s(t,i)===s(e,-1===u?0:i-u)){if(-1===u&&(u=i),i-u+1===c)return u*a}else-1!==u&&(i-=i-u),u=-1}else for(n+c>l&&(n=l-c),i=n;i>=0;i--){for(var f=!0,h=0;h<c;h++)if(s(t,i+h)!==s(e,h)){f=!1;break}if(f)return i}return-1}function v(t,e,n,r){n=Number(n)||0;var o=t.length-n;r?(r=Number(r))>o&&(r=o):r=o;var i=e.length;if(i%2!=0)throw new TypeError("Invalid hex string");r>i/2&&(r=i/2);for(var a=0;a<r;++a){var l=parseInt(e.substr(2*a,2),16);if(isNaN(l))return a;t[n+a]=l}return a}function y(t,e,n,r){return q(z(e,t.length-n),t,n,r)}function b(t,e,n,r){return q(function(t){for(var e=[],n=0;n<t.length;++n)e.push(255&t.charCodeAt(n));return e}(e),t,n,r)}function x(t,e,n,r){return b(t,e,n,r)}function E(t,e,n,r){return q(W(e),t,n,r)}function A(t,e,n,r){return q(function(t,e){for(var n,r,o,i=[],a=0;a<t.length&&!((e-=2)<0);++a)n=t.charCodeAt(a),r=n>>8,o=n%256,i.push(o),i.push(r);return i}(e,t.length-n),t,n,r)}function C(t,e,n){return 0===e&&n===t.length?r.fromByteArray(t):r.fromByteArray(t.slice(e,n))}function T(t,e,n){n=Math.min(t.length,n);for(var r=[],o=e;o<n;){var i,a,l,c,s=t[o],u=null,f=s>239?4:s>223?3:s>191?2:1;if(o+f<=n)switch(f){case 1:s<128&&(u=s);break;case 2:128==(192&(i=t[o+1]))&&(c=(31&s)<<6|63&i)>127&&(u=c);break;case 3:i=t[o+1],a=t[o+2],128==(192&i)&&128==(192&a)&&(c=(15&s)<<12|(63&i)<<6|63&a)>2047&&(c<55296||c>57343)&&(u=c);break;case 4:i=t[o+1],a=t[o+2],l=t[o+3],128==(192&i)&&128==(192&a)&&128==(192&l)&&(c=(15&s)<<18|(63&i)<<12|(63&a)<<6|63&l)>65535&&c<1114112&&(u=c)}null===u?(u=65533,f=1):u>65535&&(u-=65536,r.push(u>>>10&1023|55296),u=56320|1023&u),r.push(u),o+=f}return function(t){var e=t.length;if(e<=4096)return String.fromCharCode.apply(String,t);var n="",r=0;for(;r<e;)n+=String.fromCharCode.apply(String,t.slice(r,r+=4096));return n}(r)}e.Buffer=c,e.SlowBuffer=function(t){+t!=t&&(t=0);return c.alloc(+t)},e.INSPECT_MAX_BYTES=50,c.TYPED_ARRAY_SUPPORT=void 0!==t.TYPED_ARRAY_SUPPORT?t.TYPED_ARRAY_SUPPORT:function(){try{var t=new Uint8Array(1);return t.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===t.foo()&&"function"==typeof t.subarray&&0===t.subarray(1,1).byteLength}catch(t){return!1}}(),e.kMaxLength=a(),c.poolSize=8192,c._augment=function(t){return t.__proto__=c.prototype,t},c.from=function(t,e,n){return s(null,t,e,n)},c.TYPED_ARRAY_SUPPORT&&(c.prototype.__proto__=Uint8Array.prototype,c.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&c[Symbol.species]===c&&Object.defineProperty(c,Symbol.species,{value:null,configurable:!0})),c.alloc=function(t,e,n){return function(t,e,n,r){return u(e),e<=0?l(t,e):void 0!==n?"string"==typeof r?l(t,e).fill(n,r):l(t,e).fill(n):l(t,e)}(null,t,e,n)},c.allocUnsafe=function(t){return f(null,t)},c.allocUnsafeSlow=function(t){return f(null,t)},c.isBuffer=function(t){return!(null==t||!t._isBuffer)},c.compare=function(t,e){if(!c.isBuffer(t)||!c.isBuffer(e))throw new TypeError("Arguments must be Buffers");if(t===e)return 0;for(var n=t.length,r=e.length,o=0,i=Math.min(n,r);o<i;++o)if(t[o]!==e[o]){n=t[o],r=e[o];break}return n<r?-1:r<n?1:0},c.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},c.concat=function(t,e){if(!i(t))throw new TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return c.alloc(0);var n;if(void 0===e)for(e=0,n=0;n<t.length;++n)e+=t[n].length;var r=c.allocUnsafe(e),o=0;for(n=0;n<t.length;++n){var a=t[n];if(!c.isBuffer(a))throw new TypeError('"list" argument must be an Array of Buffers');a.copy(r,o),o+=a.length}return r},c.byteLength=p,c.prototype._isBuffer=!0,c.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)g(this,e,e+1);return this},c.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)g(this,e,e+3),g(this,e+1,e+2);return this},c.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)g(this,e,e+7),g(this,e+1,e+6),g(this,e+2,e+5),g(this,e+3,e+4);return this},c.prototype.toString=function(){var t=0|this.length;return 0===t?"":0===arguments.length?T(this,0,t):_.apply(this,arguments)},c.prototype.equals=function(t){if(!c.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t||0===c.compare(this,t)},c.prototype.inspect=function(){var t="",n=e.INSPECT_MAX_BYTES;return this.length>0&&(t=this.toString("hex",0,n).match(/.{2}/g).join(" "),this.length>n&&(t+=" ... ")),"<Buffer "+t+">"},c.prototype.compare=function(t,e,n,r,o){if(!c.isBuffer(t))throw new TypeError("Argument must be a Buffer");if(void 0===e&&(e=0),void 0===n&&(n=t?t.length:0),void 0===r&&(r=0),void 0===o&&(o=this.length),e<0||n>t.length||r<0||o>this.length)throw new RangeError("out of range index");if(r>=o&&e>=n)return 0;if(r>=o)return-1;if(e>=n)return 1;if(this===t)return 0;for(var i=(o>>>=0)-(r>>>=0),a=(n>>>=0)-(e>>>=0),l=Math.min(i,a),s=this.slice(r,o),u=t.slice(e,n),f=0;f<l;++f)if(s[f]!==u[f]){i=s[f],a=u[f];break}return i<a?-1:a<i?1:0},c.prototype.includes=function(t,e,n){return-1!==this.indexOf(t,e,n)},c.prototype.indexOf=function(t,e,n){return m(this,t,e,n,!0)},c.prototype.lastIndexOf=function(t,e,n){return m(this,t,e,n,!1)},c.prototype.write=function(t,e,n,r){if(void 0===e)r="utf8",n=this.length,e=0;else if(void 0===n&&"string"==typeof e)r=e,n=this.length,e=0;else{if(!isFinite(e))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");e|=0,isFinite(n)?(n|=0,void 0===r&&(r="utf8")):(r=n,n=void 0)}var o=this.length-e;if((void 0===n||n>o)&&(n=o),t.length>0&&(n<0||e<0)||e>this.length)throw new RangeError("Attempt to write outside buffer bounds");r||(r="utf8");for(var i=!1;;)switch(r){case"hex":return v(this,t,e,n);case"utf8":case"utf-8":return y(this,t,e,n);case"ascii":return b(this,t,e,n);case"latin1":case"binary":return x(this,t,e,n);case"base64":return E(this,t,e,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return A(this,t,e,n);default:if(i)throw new TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),i=!0}},c.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};function R(t,e,n){var r="";n=Math.min(t.length,n);for(var o=e;o<n;++o)r+=String.fromCharCode(127&t[o]);return r}function k(t,e,n){var r="";n=Math.min(t.length,n);for(var o=e;o<n;++o)r+=String.fromCharCode(t[o]);return r}function P(t,e,n){var r=t.length;(!e||e<0)&&(e=0),(!n||n<0||n>r)&&(n=r);for(var o="",i=e;i<n;++i)o+=Y(t[i]);return o}function S(t,e,n){for(var r=t.slice(e,n),o="",i=0;i<r.length;i+=2)o+=String.fromCharCode(r[i]+256*r[i+1]);return o}function B(t,e,n){if(t%1!=0||t<0)throw new RangeError("offset is not uint");if(t+e>n)throw new RangeError("Trying to access beyond buffer length")}function N(t,e,n,r,o,i){if(!c.isBuffer(t))throw new TypeError('"buffer" argument must be a Buffer instance');if(e>o||e<i)throw new RangeError('"value" argument is out of bounds');if(n+r>t.length)throw new RangeError("Index out of range")}function M(t,e,n,r){e<0&&(e=65535+e+1);for(var o=0,i=Math.min(t.length-n,2);o<i;++o)t[n+o]=(e&255<<8*(r?o:1-o))>>>8*(r?o:1-o)}function L(t,e,n,r){e<0&&(e=4294967295+e+1);for(var o=0,i=Math.min(t.length-n,4);o<i;++o)t[n+o]=e>>>8*(r?o:3-o)&255}function O(t,e,n,r,o,i){if(n+r>t.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("Index out of range")}function I(t,e,n,r,i){return i||O(t,0,n,4),o.write(t,e,n,r,23,4),n+4}function U(t,e,n,r,i){return i||O(t,0,n,8),o.write(t,e,n,r,52,8),n+8}c.prototype.slice=function(t,e){var n,r=this.length;if((t=~~t)<0?(t+=r)<0&&(t=0):t>r&&(t=r),(e=void 0===e?r:~~e)<0?(e+=r)<0&&(e=0):e>r&&(e=r),e<t&&(e=t),c.TYPED_ARRAY_SUPPORT)(n=this.subarray(t,e)).__proto__=c.prototype;else{var o=e-t;n=new c(o,void 0);for(var i=0;i<o;++i)n[i]=this[i+t]}return n},c.prototype.readUIntLE=function(t,e,n){t|=0,e|=0,n||B(t,e,this.length);for(var r=this[t],o=1,i=0;++i<e&&(o*=256);)r+=this[t+i]*o;return r},c.prototype.readUIntBE=function(t,e,n){t|=0,e|=0,n||B(t,e,this.length);for(var r=this[t+--e],o=1;e>0&&(o*=256);)r+=this[t+--e]*o;return r},c.prototype.readUInt8=function(t,e){return e||B(t,1,this.length),this[t]},c.prototype.readUInt16LE=function(t,e){return e||B(t,2,this.length),this[t]|this[t+1]<<8},c.prototype.readUInt16BE=function(t,e){return e||B(t,2,this.length),this[t]<<8|this[t+1]},c.prototype.readUInt32LE=function(t,e){return e||B(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},c.prototype.readUInt32BE=function(t,e){return e||B(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},c.prototype.readIntLE=function(t,e,n){t|=0,e|=0,n||B(t,e,this.length);for(var r=this[t],o=1,i=0;++i<e&&(o*=256);)r+=this[t+i]*o;return r>=(o*=128)&&(r-=Math.pow(2,8*e)),r},c.prototype.readIntBE=function(t,e,n){t|=0,e|=0,n||B(t,e,this.length);for(var r=e,o=1,i=this[t+--r];r>0&&(o*=256);)i+=this[t+--r]*o;return i>=(o*=128)&&(i-=Math.pow(2,8*e)),i},c.prototype.readInt8=function(t,e){return e||B(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},c.prototype.readInt16LE=function(t,e){e||B(t,2,this.length);var n=this[t]|this[t+1]<<8;return 32768&n?4294901760|n:n},c.prototype.readInt16BE=function(t,e){e||B(t,2,this.length);var n=this[t+1]|this[t]<<8;return 32768&n?4294901760|n:n},c.prototype.readInt32LE=function(t,e){return e||B(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},c.prototype.readInt32BE=function(t,e){return e||B(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},c.prototype.readFloatLE=function(t,e){return e||B(t,4,this.length),o.read(this,t,!0,23,4)},c.prototype.readFloatBE=function(t,e){return e||B(t,4,this.length),o.read(this,t,!1,23,4)},c.prototype.readDoubleLE=function(t,e){return e||B(t,8,this.length),o.read(this,t,!0,52,8)},c.prototype.readDoubleBE=function(t,e){return e||B(t,8,this.length),o.read(this,t,!1,52,8)},c.prototype.writeUIntLE=function(t,e,n,r){(t=+t,e|=0,n|=0,r)||N(this,t,e,n,Math.pow(2,8*n)-1,0);var o=1,i=0;for(this[e]=255&t;++i<n&&(o*=256);)this[e+i]=t/o&255;return e+n},c.prototype.writeUIntBE=function(t,e,n,r){(t=+t,e|=0,n|=0,r)||N(this,t,e,n,Math.pow(2,8*n)-1,0);var o=n-1,i=1;for(this[e+o]=255&t;--o>=0&&(i*=256);)this[e+o]=t/i&255;return e+n},c.prototype.writeUInt8=function(t,e,n){return t=+t,e|=0,n||N(this,t,e,1,255,0),c.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),this[e]=255&t,e+1},c.prototype.writeUInt16LE=function(t,e,n){return t=+t,e|=0,n||N(this,t,e,2,65535,0),c.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8):M(this,t,e,!0),e+2},c.prototype.writeUInt16BE=function(t,e,n){return t=+t,e|=0,n||N(this,t,e,2,65535,0),c.TYPED_ARRAY_SUPPORT?(this[e]=t>>>8,this[e+1]=255&t):M(this,t,e,!1),e+2},c.prototype.writeUInt32LE=function(t,e,n){return t=+t,e|=0,n||N(this,t,e,4,4294967295,0),c.TYPED_ARRAY_SUPPORT?(this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t):L(this,t,e,!0),e+4},c.prototype.writeUInt32BE=function(t,e,n){return t=+t,e|=0,n||N(this,t,e,4,4294967295,0),c.TYPED_ARRAY_SUPPORT?(this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t):L(this,t,e,!1),e+4},c.prototype.writeIntLE=function(t,e,n,r){if(t=+t,e|=0,!r){var o=Math.pow(2,8*n-1);N(this,t,e,n,o-1,-o)}var i=0,a=1,l=0;for(this[e]=255&t;++i<n&&(a*=256);)t<0&&0===l&&0!==this[e+i-1]&&(l=1),this[e+i]=(t/a>>0)-l&255;return e+n},c.prototype.writeIntBE=function(t,e,n,r){if(t=+t,e|=0,!r){var o=Math.pow(2,8*n-1);N(this,t,e,n,o-1,-o)}var i=n-1,a=1,l=0;for(this[e+i]=255&t;--i>=0&&(a*=256);)t<0&&0===l&&0!==this[e+i+1]&&(l=1),this[e+i]=(t/a>>0)-l&255;return e+n},c.prototype.writeInt8=function(t,e,n){return t=+t,e|=0,n||N(this,t,e,1,127,-128),c.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),t<0&&(t=255+t+1),this[e]=255&t,e+1},c.prototype.writeInt16LE=function(t,e,n){return t=+t,e|=0,n||N(this,t,e,2,32767,-32768),c.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8):M(this,t,e,!0),e+2},c.prototype.writeInt16BE=function(t,e,n){return t=+t,e|=0,n||N(this,t,e,2,32767,-32768),c.TYPED_ARRAY_SUPPORT?(this[e]=t>>>8,this[e+1]=255&t):M(this,t,e,!1),e+2},c.prototype.writeInt32LE=function(t,e,n){return t=+t,e|=0,n||N(this,t,e,4,**********,-2147483648),c.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24):L(this,t,e,!0),e+4},c.prototype.writeInt32BE=function(t,e,n){return t=+t,e|=0,n||N(this,t,e,4,**********,-2147483648),t<0&&(t=4294967295+t+1),c.TYPED_ARRAY_SUPPORT?(this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t):L(this,t,e,!1),e+4},c.prototype.writeFloatLE=function(t,e,n){return I(this,t,e,!0,n)},c.prototype.writeFloatBE=function(t,e,n){return I(this,t,e,!1,n)},c.prototype.writeDoubleLE=function(t,e,n){return U(this,t,e,!0,n)},c.prototype.writeDoubleBE=function(t,e,n){return U(this,t,e,!1,n)},c.prototype.copy=function(t,e,n,r){if(n||(n=0),r||0===r||(r=this.length),e>=t.length&&(e=t.length),e||(e=0),r>0&&r<n&&(r=n),r===n)return 0;if(0===t.length||0===this.length)return 0;if(e<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("sourceStart out of bounds");if(r<0)throw new RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),t.length-e<r-n&&(r=t.length-e+n);var o,i=r-n;if(this===t&&n<e&&e<r)for(o=i-1;o>=0;--o)t[o+e]=this[o+n];else if(i<1e3||!c.TYPED_ARRAY_SUPPORT)for(o=0;o<i;++o)t[o+e]=this[o+n];else Uint8Array.prototype.set.call(t,this.subarray(n,n+i),e);return i},c.prototype.fill=function(t,e,n,r){if("string"==typeof t){if("string"==typeof e?(r=e,e=0,n=this.length):"string"==typeof n&&(r=n,n=this.length),1===t.length){var o=t.charCodeAt(0);o<256&&(t=o)}if(void 0!==r&&"string"!=typeof r)throw new TypeError("encoding must be a string");if("string"==typeof r&&!c.isEncoding(r))throw new TypeError("Unknown encoding: "+r)}else"number"==typeof t&&(t&=255);if(e<0||this.length<e||this.length<n)throw new RangeError("Out of range index");if(n<=e)return this;var i;if(e>>>=0,n=void 0===n?this.length:n>>>0,t||(t=0),"number"==typeof t)for(i=e;i<n;++i)this[i]=t;else{var a=c.isBuffer(t)?t:z(new c(t,r).toString()),l=a.length;for(i=0;i<n-e;++i)this[i+e]=a[i%l]}return this};var D=/[^+\/0-9A-Za-z-_]/g;function Y(t){return t<16?"0"+t.toString(16):t.toString(16)}function z(t,e){var n;e=e||1/0;for(var r=t.length,o=null,i=[],a=0;a<r;++a){if((n=t.charCodeAt(a))>55295&&n<57344){if(!o){if(n>56319){(e-=3)>-1&&i.push(239,191,189);continue}if(a+1===r){(e-=3)>-1&&i.push(239,191,189);continue}o=n;continue}if(n<56320){(e-=3)>-1&&i.push(239,191,189),o=n;continue}n=65536+(o-55296<<10|n-56320)}else o&&(e-=3)>-1&&i.push(239,191,189);if(o=null,n<128){if((e-=1)<0)break;i.push(n)}else if(n<2048){if((e-=2)<0)break;i.push(n>>6|192,63&n|128)}else if(n<65536){if((e-=3)<0)break;i.push(n>>12|224,n>>6&63|128,63&n|128)}else{if(!(n<1114112))throw new Error("Invalid code point");if((e-=4)<0)break;i.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}}return i}function W(t){return r.toByteArray(function(t){if((t=function(t){return t.trim?t.trim():t.replace(/^\s+|\s+$/g,"")}(t).replace(D,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function q(t,e,n,r){for(var o=0;o<r&&!(o+n>=e.length||o>=t.length);++o)e[o+n]=t[o];return o}}).call(this,n(29))},function(t,e){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(t){"object"==typeof window&&(n=window)}t.exports=n},function(t,e,n){"use strict";e.byteLength=function(t){var e=s(t),n=e[0],r=e[1];return 3*(n+r)/4-r},e.toByteArray=function(t){var e,n,r=s(t),a=r[0],l=r[1],c=new i(function(t,e,n){return 3*(e+n)/4-n}(0,a,l)),u=0,f=l>0?a-4:a;for(n=0;n<f;n+=4)e=o[t.charCodeAt(n)]<<18|o[t.charCodeAt(n+1)]<<12|o[t.charCodeAt(n+2)]<<6|o[t.charCodeAt(n+3)],c[u++]=e>>16&255,c[u++]=e>>8&255,c[u++]=255&e;2===l&&(e=o[t.charCodeAt(n)]<<2|o[t.charCodeAt(n+1)]>>4,c[u++]=255&e);1===l&&(e=o[t.charCodeAt(n)]<<10|o[t.charCodeAt(n+1)]<<4|o[t.charCodeAt(n+2)]>>2,c[u++]=e>>8&255,c[u++]=255&e);return c},e.fromByteArray=function(t){for(var e,n=t.length,o=n%3,i=[],a=0,l=n-o;a<l;a+=16383)i.push(u(t,a,a+16383>l?l:a+16383));1===o?(e=t[n-1],i.push(r[e>>2]+r[e<<4&63]+"==")):2===o&&(e=(t[n-2]<<8)+t[n-1],i.push(r[e>>10]+r[e>>4&63]+r[e<<2&63]+"="));return i.join("")};for(var r=[],o=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",l=0,c=a.length;l<c;++l)r[l]=a[l],o[a.charCodeAt(l)]=l;function s(t){var e=t.length;if(e%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var n=t.indexOf("=");return-1===n&&(n=e),[n,n===e?0:4-n%4]}function u(t,e,n){for(var o,i,a=[],l=e;l<n;l+=3)o=(t[l]<<16&16711680)+(t[l+1]<<8&65280)+(255&t[l+2]),a.push(r[(i=o)>>18&63]+r[i>>12&63]+r[i>>6&63]+r[63&i]);return a.join("")}o["-".charCodeAt(0)]=62,o["_".charCodeAt(0)]=63},function(t,e){
/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */
e.read=function(t,e,n,r,o){var i,a,l=8*o-r-1,c=(1<<l)-1,s=c>>1,u=-7,f=n?o-1:0,h=n?-1:1,d=t[e+f];for(f+=h,i=d&(1<<-u)-1,d>>=-u,u+=l;u>0;i=256*i+t[e+f],f+=h,u-=8);for(a=i&(1<<-u)-1,i>>=-u,u+=r;u>0;a=256*a+t[e+f],f+=h,u-=8);if(0===i)i=1-s;else{if(i===c)return a?NaN:1/0*(d?-1:1);a+=Math.pow(2,r),i-=s}return(d?-1:1)*a*Math.pow(2,i-r)},e.write=function(t,e,n,r,o,i){var a,l,c,s=8*i-o-1,u=(1<<s)-1,f=u>>1,h=23===o?Math.pow(2,-24)-Math.pow(2,-77):0,d=r?0:i-1,p=r?1:-1,_=e<0||0===e&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(l=isNaN(e)?1:0,a=u):(a=Math.floor(Math.log(e)/Math.LN2),e*(c=Math.pow(2,-a))<1&&(a--,c*=2),(e+=a+f>=1?h/c:h*Math.pow(2,1-f))*c>=2&&(a++,c/=2),a+f>=u?(l=0,a=u):a+f>=1?(l=(e*c-1)*Math.pow(2,o),a+=f):(l=e*Math.pow(2,f-1)*Math.pow(2,o),a=0));o>=8;t[n+d]=255&l,d+=p,l/=256,o-=8);for(a=a<<o|l,s+=o;s>0;t[n+d]=255&a,d+=p,a/=256,s-=8);t[n+d-p]|=128*_}},function(t,e){var n={}.toString;t.exports=Array.isArray||function(t){return"[object Array]"==n.call(t)}},function(t,e,n){var r=n(1),o=n(10),i=n(6),a=n(2),l=n(11),c=n(5),s=r.getBCHDigit(7973);function u(t,e){return a.getCharCountIndicator(t,e)+4}function f(t,e){var n=0;return t.forEach((function(t){var r=u(t.mode,e);n+=r+t.getBitsLength()})),n}e.from=function(t,e){return l.isValid(t)?parseInt(t,10):e},e.getCapacity=function(t,e,n){if(!l.isValid(t))throw new Error("Invalid QR Code version");void 0===n&&(n=a.BYTE);var i=8*(r.getSymbolTotalCodewords(t)-o.getTotalCodewordsCount(t,e));if(n===a.MIXED)return i;var c=i-u(n,t);switch(n){case a.NUMERIC:return Math.floor(c/10*3);case a.ALPHANUMERIC:return Math.floor(c/11*2);case a.KANJI:return Math.floor(c/13);case a.BYTE:default:return Math.floor(c/8)}},e.getBestVersionForData=function(t,n){var r,o=i.from(n,i.M);if(c(t)){if(t.length>1)return function(t,n){for(var r=1;r<=40;r++){if(f(t,r)<=e.getCapacity(r,n,a.MIXED))return r}}(t,o);if(0===t.length)return 1;r=t[0]}else r=t;return function(t,n,r){for(var o=1;o<=40;o++)if(n<=e.getCapacity(o,r,t))return o}(r.mode,r.getLength(),o)},e.getEncodedBits=function(t){if(!l.isValid(t)||t<7)throw new Error("Invalid QR Code version");for(var e=t<<12;r.getBCHDigit(e)-s>=0;)e^=7973<<r.getBCHDigit(e)-s;return t<<12|e}},function(t,e,n){var r=n(1),o=r.getBCHDigit(1335);e.getEncodedBits=function(t,e){for(var n=t.bit<<3|e,i=n<<10;r.getBCHDigit(i)-o>=0;)i^=1335<<r.getBCHDigit(i)-o;return 21522^(n<<10|i)}},function(t,e,n){var r=n(2),o=n(36),i=n(37),a=n(38),l=n(39),c=n(12),s=n(1),u=n(40);function f(t){return unescape(encodeURIComponent(t)).length}function h(t,e,n){for(var r,o=[];null!==(r=t.exec(n));)o.push({data:r[0],index:r.index,mode:e,length:r[0].length});return o}function d(t){var e,n,o=h(c.NUMERIC,r.NUMERIC,t),i=h(c.ALPHANUMERIC,r.ALPHANUMERIC,t);return s.isKanjiModeEnabled()?(e=h(c.BYTE,r.BYTE,t),n=h(c.KANJI,r.KANJI,t)):(e=h(c.BYTE_KANJI,r.BYTE,t),n=[]),o.concat(i,e,n).sort((function(t,e){return t.index-e.index})).map((function(t){return{data:t.data,mode:t.mode,length:t.length}}))}function p(t,e){switch(e){case r.NUMERIC:return o.getBitsLength(t);case r.ALPHANUMERIC:return i.getBitsLength(t);case r.KANJI:return l.getBitsLength(t);case r.BYTE:return a.getBitsLength(t)}}function _(t,e){var n,c=r.getBestModeForData(t);if((n=r.from(e,c))!==r.BYTE&&n.bit<c.bit)throw new Error('"'+t+'" cannot be encoded with mode '+r.toString(n)+".\n Suggested mode is: "+r.toString(c));switch(n!==r.KANJI||s.isKanjiModeEnabled()||(n=r.BYTE),n){case r.NUMERIC:return new o(t);case r.ALPHANUMERIC:return new i(t);case r.KANJI:return new l(t);case r.BYTE:return new a(t)}}e.fromArray=function(t){return t.reduce((function(t,e){return"string"==typeof e?t.push(_(e,null)):e.data&&t.push(_(e.data,e.mode)),t}),[])},e.fromString=function(t,n){for(var o=function(t,e){for(var n={},o={start:{}},i=["start"],a=0;a<t.length;a++){for(var l=t[a],c=[],s=0;s<l.length;s++){var u=l[s],f=""+a+s;c.push(f),n[f]={node:u,lastCount:0},o[f]={};for(var h=0;h<i.length;h++){var d=i[h];n[d]&&n[d].node.mode===u.mode?(o[d][f]=p(n[d].lastCount+u.length,u.mode)-p(n[d].lastCount,u.mode),n[d].lastCount+=u.length):(n[d]&&(n[d].lastCount=u.length),o[d][f]=p(u.length,u.mode)+4+r.getCharCountIndicator(u.mode,e))}}i=c}for(h=0;h<i.length;h++)o[i[h]].end=0;return{map:o,table:n}}(function(t){for(var e=[],n=0;n<t.length;n++){var o=t[n];switch(o.mode){case r.NUMERIC:e.push([o,{data:o.data,mode:r.ALPHANUMERIC,length:o.length},{data:o.data,mode:r.BYTE,length:o.length}]);break;case r.ALPHANUMERIC:e.push([o,{data:o.data,mode:r.BYTE,length:o.length}]);break;case r.KANJI:e.push([o,{data:o.data,mode:r.BYTE,length:f(o.data)}]);break;case r.BYTE:e.push([{data:o.data,mode:r.BYTE,length:f(o.data)}])}}return e}(d(t,s.isKanjiModeEnabled())),n),i=u.find_path(o.map,"start","end"),a=[],l=1;l<i.length-1;l++)a.push(o.table[i[l]].node);return e.fromArray(function(t){return t.reduce((function(t,e){var n=t.length-1>=0?t[t.length-1]:null;return n&&n.mode===e.mode?(t[t.length-1].data+=e.data,t):(t.push(e),t)}),[])}(a))},e.rawSplit=function(t){return e.fromArray(d(t,s.isKanjiModeEnabled()))}},function(t,e,n){var r=n(2);function o(t){this.mode=r.NUMERIC,this.data=t.toString()}o.getBitsLength=function(t){return 10*Math.floor(t/3)+(t%3?t%3*3+1:0)},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(t){var e,n,r;for(e=0;e+3<=this.data.length;e+=3)n=this.data.substr(e,3),r=parseInt(n,10),t.put(r,10);var o=this.data.length-e;o>0&&(n=this.data.substr(e),r=parseInt(n,10),t.put(r,3*o+1))},t.exports=o},function(t,e,n){var r=n(2),o=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function i(t){this.mode=r.ALPHANUMERIC,this.data=t}i.getBitsLength=function(t){return 11*Math.floor(t/2)+t%2*6},i.prototype.getLength=function(){return this.data.length},i.prototype.getBitsLength=function(){return i.getBitsLength(this.data.length)},i.prototype.write=function(t){var e;for(e=0;e+2<=this.data.length;e+=2){var n=45*o.indexOf(this.data[e]);n+=o.indexOf(this.data[e+1]),t.put(n,11)}this.data.length%2&&t.put(o.indexOf(this.data[e]),6)},t.exports=i},function(t,e,n){var r=n(3),o=n(2);function i(t){this.mode=o.BYTE,this.data=r.from(t)}i.getBitsLength=function(t){return 8*t},i.prototype.getLength=function(){return this.data.length},i.prototype.getBitsLength=function(){return i.getBitsLength(this.data.length)},i.prototype.write=function(t){for(var e=0,n=this.data.length;e<n;e++)t.put(this.data[e],8)},t.exports=i},function(t,e,n){var r=n(2),o=n(1);function i(t){this.mode=r.KANJI,this.data=t}i.getBitsLength=function(t){return 13*t},i.prototype.getLength=function(){return this.data.length},i.prototype.getBitsLength=function(){return i.getBitsLength(this.data.length)},i.prototype.write=function(t){var e;for(e=0;e<this.data.length;e++){var n=o.toSJIS(this.data[e]);if(n>=33088&&n<=40956)n-=33088;else{if(!(n>=57408&&n<=60351))throw new Error("Invalid SJIS character: "+this.data[e]+"\nMake sure your charset is UTF-8");n-=49472}n=192*(n>>>8&255)+(255&n),t.put(n,13)}},t.exports=i},function(t,e,n){"use strict";var r={single_source_shortest_paths:function(t,e,n){var o={},i={};i[e]=0;var a,l,c,s,u,f,h,d=r.PriorityQueue.make();for(d.push(e,0);!d.empty();)for(c in l=(a=d.pop()).value,s=a.cost,u=t[l]||{})u.hasOwnProperty(c)&&(f=s+u[c],h=i[c],(void 0===i[c]||h>f)&&(i[c]=f,d.push(c,f),o[c]=l));if(void 0!==n&&void 0===i[n]){var p=["Could not find a path from ",e," to ",n,"."].join("");throw new Error(p)}return o},extract_shortest_path_from_predecessor_list:function(t,e){for(var n=[],r=e;r;)n.push(r),t[r],r=t[r];return n.reverse(),n},find_path:function(t,e,n){var o=r.single_source_shortest_paths(t,e,n);return r.extract_shortest_path_from_predecessor_list(o,n)},PriorityQueue:{make:function(t){var e,n=r.PriorityQueue,o={};for(e in t=t||{},n)n.hasOwnProperty(e)&&(o[e]=n[e]);return o.queue=[],o.sorter=t.sorter||n.default_sorter,o},default_sorter:function(t,e){return t.cost-e.cost},push:function(t,e){var n={value:t,cost:e};this.queue.push(n),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return 0===this.queue.length}}};t.exports=r},function(t,e,n){var r=n(13);e.render=function(t,e,n){var o=n,i=e;void 0!==o||e&&e.getContext||(o=e,e=void 0),e||(i=function(){try{return document.createElement("canvas")}catch(t){throw new Error("You need to specify a canvas element")}}()),o=r.getOptions(o);var a=r.getImageWidth(t.modules.size,o),l=i.getContext("2d"),c=l.createImageData(a,a);return r.qrToImageData(c.data,t,o),function(t,e,n){t.clearRect(0,0,e.width,e.height),e.style||(e.style={}),e.height=n,e.width=n,e.style.height=n+"px",e.style.width=n+"px"}(l,i,a),l.putImageData(c,0,0),i},e.renderToDataURL=function(t,n,r){var o=r;void 0!==o||n&&n.getContext||(o=n,n=void 0),o||(o={});var i=e.render(t,n,o),a=o.type||"image/png",l=o.rendererOpts||{};return i.toDataURL(a,l.quality)}},function(t,e,n){var r=n(13);function o(t,e){var n=t.a/255,r=e+'="'+t.hex+'"';return n<1?r+" "+e+'-opacity="'+n.toFixed(2).slice(1)+'"':r}function i(t,e,n){var r=t+e;return void 0!==n&&(r+=" "+n),r}e.render=function(t,e,n){var a=r.getOptions(e),l=t.modules.size,c=t.modules.data,s=l+2*a.margin,u=a.color.light.a?"<path "+o(a.color.light,"fill")+' d="M0 0h'+s+"v"+s+'H0z"/>':"",f="<path "+o(a.color.dark,"stroke")+' d="'+function(t,e,n){for(var r="",o=0,a=!1,l=0,c=0;c<t.length;c++){var s=Math.floor(c%e),u=Math.floor(c/e);s||a||(a=!0),t[c]?(l++,c>0&&s>0&&t[c-1]||(r+=a?i("M",s+n,.5+u+n):i("m",o,0),o=0,a=!1),s+1<e&&t[c+1]||(r+=i("h",l),l=0)):o++}return r}(c,l,a.margin)+'"/>',h='viewBox="0 0 '+s+" "+s+'"',d='<svg xmlns="http://www.w3.org/2000/svg" '+(a.width?'width="'+a.width+'" height="'+a.width+'" ':"")+h+' shape-rendering="crispEdges">'+u+f+"</svg>\n";return"function"==typeof n&&n(null,d),d}},function(t,e){t.exports=function(){var t=document.getSelection();if(!t.rangeCount)return function(){};for(var e=document.activeElement,n=[],r=0;r<t.rangeCount;r++)n.push(t.getRangeAt(r));switch(e.tagName.toUpperCase()){case"INPUT":case"TEXTAREA":e.blur();break;default:e=null}return t.removeAllRanges(),function(){"Caret"===t.type&&t.removeAllRanges(),t.rangeCount||n.forEach((function(e){t.addRange(e)})),e&&e.focus()}}}])}));