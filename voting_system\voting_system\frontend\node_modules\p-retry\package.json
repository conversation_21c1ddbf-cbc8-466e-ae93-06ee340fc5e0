{"name": "p-retry", "version": "6.2.1", "description": "Retry a promise-returning or async function", "license": "MIT", "repository": "sindresorhus/p-retry", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "types": "./index.d.ts", "sideEffects": false, "engines": {"node": ">=16.17"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["promise", "retry", "retries", "operation", "failed", "rejected", "try", "exponential", "backoff", "attempt", "async", "await", "promises", "concurrently", "concurrency", "parallel", "bluebird"], "dependencies": {"@types/retry": "0.12.2", "is-network-error": "^1.0.0", "retry": "^0.13.1"}, "devDependencies": {"ava": "^5.3.1", "delay": "^6.0.0", "tsd": "^0.28.1", "xo": "^0.56.0"}}