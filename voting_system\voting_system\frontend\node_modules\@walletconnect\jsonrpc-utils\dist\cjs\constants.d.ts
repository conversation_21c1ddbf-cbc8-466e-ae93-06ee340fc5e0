export declare const PARSE_ERROR = "PARSE_ERROR";
export declare const INVALID_REQUEST = "INVALID_REQUEST";
export declare const METHOD_NOT_FOUND = "METHOD_NOT_FOUND";
export declare const INVALID_PARAMS = "INVALID_PARAMS";
export declare const INTERNAL_ERROR = "INTERNAL_ERROR";
export declare const SERVER_ERROR = "SERVER_ERROR";
export declare const RESERVED_ERROR_CODES: number[];
export declare const SERVER_ERROR_CODE_RANGE: number[];
export declare const STANDARD_ERROR_MAP: {
    PARSE_ERROR: {
        code: number;
        message: string;
    };
    INVALID_REQUEST: {
        code: number;
        message: string;
    };
    METHOD_NOT_FOUND: {
        code: number;
        message: string;
    };
    INVALID_PARAMS: {
        code: number;
        message: string;
    };
    INTERNAL_ERROR: {
        code: number;
        message: string;
    };
    SERVER_ERROR: {
        code: number;
        message: string;
    };
};
export declare const DEFAULT_ERROR = "SERVER_ERROR";
//# sourceMappingURL=constants.d.ts.map