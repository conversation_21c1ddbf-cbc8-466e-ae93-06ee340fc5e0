{"name": "@walletconnect/mobile-registry", "version": "1.4.0", "description": "Registry for WalletConnect mobile-to-mobile linking supported Wallets", "scripts": {"build": "node ./scripts/format.js"}, "keywords": ["WalletConnect", "mobile", "registry"], "author": "WalletConnect <walletconnect.org>", "homepage": "https://github.com/WalletConnect/walletconnect-monorepo#readme", "license": "MIT", "main": "registry.json", "repository": {"type": "git", "url": "git+ssh://**************/WalletConnect/walletconnect-monorepo.git"}, "bugs": {"url": "https://github.com/WalletConnect/walletconnect-monorepo/issues"}, "devDependencies": {"@walletconnect/utils": "^1.4.0", "axios": "^0.21.0"}}