import{ModalCtrl as r,ThemeCtrl as n,ConfigCtrl as l,OptionsCtrl as s}from"@web3modal/core";var c=Object.defineProperty,i=Object.getOwnPropertySymbols,d=Object.prototype.hasOwnProperty,b=Object.prototype.propertyIsEnumerable,a=(o,e,t)=>e in o?c(o,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):o[e]=t,m=(o,e)=>{for(var t in e||(e={}))d.call(e,t)&&a(o,t,e[t]);if(i)for(var t of i(e))b.call(e,t)&&a(o,t,e[t]);return o};class f{constructor(e){this.openModal=r.open,this.closeModal=r.close,this.subscribeModal=r.subscribe,this.setTheme=n.setThemeConfig,n.setThemeConfig(e),l.setConfig(m({enableStandaloneMode:!0},e)),this.initUi()}async initUi(){if(typeof window<"u"){await import("@web3modal/ui");const e=document.createElement("w3m-modal");document.body.insertAdjacentElement("beforeend",e),s.setIsUiLoaded(!0)}}}export{f as Web3Modal};
