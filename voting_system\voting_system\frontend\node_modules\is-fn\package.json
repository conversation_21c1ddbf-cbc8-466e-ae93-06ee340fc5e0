{"name": "is-fn", "version": "1.0.0", "description": "Check if a value is a function", "license": "MIT", "repository": "sindresorhus/is-fn", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["function", "func", "fn", "check", "detect", "is", "test", "type"], "devDependencies": {"ava": "*", "xo": "*"}}