{"version": 3, "file": "preact.umd.js", "sources": ["../src/options.js", "../src/create-element.js", "../src/component.js", "../src/render.js", "../src/create-context.js", "../src/constants.js", "../src/util.js", "../src/diff/children.js", "../src/diff/props.js", "../src/diff/index.js", "../src/diff/catch-error.js", "../src/clone-element.js"], "sourcesContent": ["import { _catchError } from './diff/catch-error';\n\n/**\n * The `option` object can potentially contain callback functions\n * that are called during various stages of our renderer. This is the\n * foundation on which all our addons like `preact/debug`, `preact/compat`,\n * and `preact/hooks` are based on. See the `Options` type in `internal.d.ts`\n * for a full list of available option hooks (most editors/IDEs allow you to\n * ctrl+click or cmd+click on mac the type definition below).\n * @type {import('./internal').Options}\n */\nconst options = {\n\t_catchError\n};\n\nexport default options;\n", "import options from './options';\n\n/**\n * Create an virtual node (used for JSX)\n * @param {import('./internal').VNode[\"type\"]} type The node name or Component\n * constructor for this virtual node\n * @param {object | null | undefined} [props] The properties of the virtual node\n * @param {Array<import('.').ComponentChildren>} [children] The children of the virtual node\n * @returns {import('./internal').VNode}\n */\nexport function createElement(type, props, children) {\n\tlet normalizedProps = {},\n\t\ti;\n\tfor (i in props) {\n\t\tif (i !== 'key' && i !== 'ref') normalizedProps[i] = props[i];\n\t}\n\n\tif (arguments.length > 3) {\n\t\tchildren = [children];\n\t\t// https://github.com/preactjs/preact/issues/1916\n\t\tfor (i = 3; i < arguments.length; i++) {\n\t\t\tchildren.push(arguments[i]);\n\t\t}\n\t}\n\tif (children != null) {\n\t\tnormalizedProps.children = children;\n\t}\n\n\t// If a Component VNode, check for and apply defaultProps\n\t// Note: type may be undefined in development, must never error here.\n\tif (typeof type == 'function' && type.defaultProps != null) {\n\t\tfor (i in type.defaultProps) {\n\t\t\tif (normalizedProps[i] === undefined) {\n\t\t\t\tnormalizedProps[i] = type.defaultProps[i];\n\t\t\t}\n\t\t}\n\t}\n\n\treturn createVNode(\n\t\ttype,\n\t\tnormalizedProps,\n\t\tprops && props.key,\n\t\tprops && props.ref,\n\t\tnull\n\t);\n}\n\n/**\n * Create a VNode (used internally by Preact)\n * @param {import('./internal').VNode[\"type\"]} type The node name or Component\n * Constructor for this virtual node\n * @param {object | string | number | null} props The properties of this virtual node.\n * If this virtual node represents a text node, this is the text of the node (string or number).\n * @param {string | number | null} key The key for this virtual node, used when\n * diffing it against its children\n * @param {import('./internal').VNode[\"ref\"]} ref The ref property that will\n * receive a reference to its created child\n * @returns {import('./internal').VNode}\n */\nexport function createVNode(type, props, key, ref, original) {\n\t// V8 seems to be better at detecting type shapes if the object is allocated from the same call site\n\t// Do not inline into createElement and coerceToVNode!\n\tconst vnode = {\n\t\ttype,\n\t\tprops,\n\t\tkey,\n\t\tref,\n\t\t_children: null,\n\t\t_parent: null,\n\t\t_depth: 0,\n\t\t_dom: null,\n\t\t// _nextDom must be initialized to undefined b/c it will eventually\n\t\t// be set to dom.nextSibling which can return `null` and it is important\n\t\t// to be able to distinguish between an uninitialized _nextDom and\n\t\t// a _nextDom that has been set to `null`\n\t\t_nextDom: undefined,\n\t\t_component: null,\n\t\tconstructor: undefined,\n\t\t_original: original\n\t};\n\n\tif (original == null) vnode._original = vnode;\n\tif (options.vnode) options.vnode(vnode);\n\n\treturn vnode;\n}\n\nexport function createRef() {\n\treturn {};\n}\n\nexport function Fragment(props) {\n\treturn props.children;\n}\n\n/**\n * Check if a the argument is a valid Preact VNode.\n * @param {*} vnode\n * @returns {vnode is import('./internal').VNode}\n */\nexport const isValidElement = vnode =>\n\tvnode != null && vnode.constructor === undefined;\n", "import { assign } from './util';\nimport { diff, commitRoot } from './diff/index';\nimport options from './options';\nimport { Fragment } from './create-element';\n\n/**\n * Base Component class. Provides `setState()` and `forceUpdate()`, which\n * trigger rendering\n * @param {object} props The initial component props\n * @param {object} context The initial context from parent components'\n * getChildContext\n */\nexport function Component(props, context) {\n\tthis.props = props;\n\tthis.context = context;\n}\n\n/**\n * Update component state and schedule a re-render.\n * @param {object | ((s: object, p: object) => object)} update A hash of state\n * properties to update with new values or a function that given the current\n * state and props returns a new partial state\n * @param {() => void} [callback] A function to be called once component state is\n * updated\n */\nComponent.prototype.setState = function(update, callback) {\n\t// only clone state when copying to nextState the first time.\n\tlet s;\n\tif (this._nextState !== this.state) {\n\t\ts = this._nextState;\n\t} else {\n\t\ts = this._nextState = assign({}, this.state);\n\t}\n\n\tif (typeof update == 'function') {\n\t\tupdate = update(s, this.props);\n\t}\n\n\tif (update) {\n\t\tassign(s, update);\n\t}\n\n\t// Skip update if updater function returned null\n\tif (update == null) return;\n\n\tif (this._vnode) {\n\t\tif (callback) this._renderCallbacks.push(callback);\n\t\tenqueueRender(this);\n\t}\n};\n\n/**\n * Immediately perform a synchronous re-render of the component\n * @param {() => void} [callback] A function to be called after component is\n * re-rendered\n */\nComponent.prototype.forceUpdate = function(callback) {\n\tif (this._vnode) {\n\t\t// Set render mode so that we can differentiate where the render request\n\t\t// is coming from. We need this because forceUpdate should never call\n\t\t// shouldComponentUpdate\n\t\tthis._force = true;\n\t\tif (callback) this._renderCallbacks.push(callback);\n\t\tenqueueRender(this);\n\t}\n};\n\n/**\n * Accepts `props` and `state`, and returns a new Virtual DOM tree to build.\n * Virtual DOM is generally constructed via [JSX](http://jasonformat.com/wtf-is-jsx).\n * @param {object} props Props (eg: JSX attributes) received from parent\n * element/component\n * @param {object} state The component's current state\n * @param {object} context Context object, as returned by the nearest\n * ancestor's `getChildContext()`\n * @returns {import('./index').ComponentChildren | void}\n */\nComponent.prototype.render = Fragment;\n\n/**\n * @param {import('./internal').VNode} vnode\n * @param {number | null} [childIndex]\n */\nexport function getDomSibling(vnode, childIndex) {\n\tif (childIndex == null) {\n\t\t// Use childIndex==null as a signal to resume the search from the vnode's sibling\n\t\treturn vnode._parent\n\t\t\t? getDomSibling(vnode._parent, vnode._parent._children.indexOf(vnode) + 1)\n\t\t\t: null;\n\t}\n\n\tlet sibling;\n\tfor (; childIndex < vnode._children.length; childIndex++) {\n\t\tsibling = vnode._children[childIndex];\n\n\t\tif (sibling != null && sibling._dom != null) {\n\t\t\t// Since updateParentDomPointers keeps _dom pointer correct,\n\t\t\t// we can rely on _dom to tell us if this subtree contains a\n\t\t\t// rendered DOM node, and what the first rendered DOM node is\n\t\t\treturn sibling._dom;\n\t\t}\n\t}\n\n\t// If we get here, we have not found a DOM node in this vnode's children.\n\t// We must resume from this vnode's sibling (in it's parent _children array)\n\t// Only climb up and search the parent if we aren't searching through a DOM\n\t// VNode (meaning we reached the DOM parent of the original vnode that began\n\t// the search)\n\treturn typeof vnode.type == 'function' ? getDomSibling(vnode) : null;\n}\n\n/**\n * Trigger in-place re-rendering of a component.\n * @param {import('./internal').Component} component The component to rerender\n */\nfunction renderComponent(component) {\n\tlet vnode = component._vnode,\n\t\toldDom = vnode._dom,\n\t\tparentDom = component._parentDom;\n\n\tif (parentDom) {\n\t\tlet commitQueue = [];\n\t\tconst oldVNode = assign({}, vnode);\n\t\toldVNode._original = oldVNode;\n\n\t\tlet newDom = diff(\n\t\t\tparentDom,\n\t\t\tvnode,\n\t\t\toldVNode,\n\t\t\tcomponent._globalContext,\n\t\t\tparentDom.ownerSVGElement !== undefined,\n\t\t\tnull,\n\t\t\tcommitQueue,\n\t\t\toldDom == null ? getDomSibling(vnode) : oldDom\n\t\t);\n\t\tcommitRoot(commitQueue, vnode);\n\n\t\tif (newDom != oldDom) {\n\t\t\tupdateParentDomPointers(vnode);\n\t\t}\n\t}\n}\n\n/**\n * @param {import('./internal').VNode} vnode\n */\nfunction updateParentDomPointers(vnode) {\n\tif ((vnode = vnode._parent) != null && vnode._component != null) {\n\t\tvnode._dom = vnode._component.base = null;\n\t\tfor (let i = 0; i < vnode._children.length; i++) {\n\t\t\tlet child = vnode._children[i];\n\t\t\tif (child != null && child._dom != null) {\n\t\t\t\tvnode._dom = vnode._component.base = child._dom;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t\treturn updateParentDomPointers(vnode);\n\t}\n}\n\n/**\n * The render queue\n * @type {Array<import('./internal').Component>}\n */\nlet rerenderQueue = [];\nlet rerenderCount = 0;\n\n/**\n * Asynchronously schedule a callback\n * @type {(cb: () => void) => void}\n */\n/* istanbul ignore next */\n// Note the following line isn't tree-shaken by rollup cuz of rollup/rollup#2566\nconst defer =\n\ttypeof Promise == 'function'\n\t\t? Promise.prototype.then.bind(Promise.resolve())\n\t\t: setTimeout;\n\n/*\n * The value of `Component.debounce` must asynchronously invoke the passed in callback. It is\n * important that contributors to Preact can consistently reason about what calls to `setState`, etc.\n * do, and when their effects will be applied. See the links below for some further reading on designing\n * asynchronous APIs.\n * * [Designing APIs for Asynchrony](https://blog.izs.me/2013/08/designing-apis-for-asynchrony)\n * * [Callbacks synchronous and asynchronous](https://blog.ometer.com/2011/07/24/callbacks-synchronous-and-asynchronous/)\n */\n\nlet prevDebounce;\n\n/**\n * Enqueue a rerender of a component\n * @param {import('./internal').Component} c The component to rerender\n */\nexport function enqueueRender(c) {\n\tif (\n\t\t(!c._dirty &&\n\t\t\t(c._dirty = true) &&\n\t\t\trerenderQueue.push(c) &&\n\t\t\t!rerenderCount++) ||\n\t\tprevDebounce !== options.debounceRendering\n\t) {\n\t\tprevDebounce = options.debounceRendering;\n\t\t(prevDebounce || defer)(process);\n\t}\n}\n\n/** Flush the render queue by rerendering all queued components */\nfunction process() {\n\tlet queue;\n\twhile ((rerenderCount = rerenderQueue.length)) {\n\t\tqueue = rerenderQueue.sort((a, b) => a._vnode._depth - b._vnode._depth);\n\t\trerenderQueue = [];\n\t\t// Don't update `renderCount` yet. Keep its value non-zero to prevent unnecessary\n\t\t// process() calls from getting scheduled while `queue` is still being consumed.\n\t\tqueue.some(c => {\n\t\t\tif (c._dirty) renderComponent(c);\n\t\t});\n\t}\n}\n", "import { EMPTY_OBJ, EMPTY_ARR } from './constants';\nimport { commitRoot, diff } from './diff/index';\nimport { createElement, Fragment } from './create-element';\nimport options from './options';\n\nconst IS_HYDRATE = EMPTY_OBJ;\n\n/**\n * Render a Preact virtual node into a DOM element\n * @param {import('./index').ComponentChild} vnode The virtual node to render\n * @param {import('./internal').PreactElement} parentDom The DOM element to\n * render into\n * @param {Element | Text} [replaceNode] Optional: Attempt to re-use an\n * existing DOM tree rooted at `replaceNode`\n */\nexport function render(vnode, parentDom, replaceNode) {\n\tif (options._root) options._root(vnode, parentDom);\n\n\t// We abuse the `replaceNode` parameter in `hydrate()` to signal if we\n\t// are in hydration mode or not by passing `IS_HYDRATE` instead of a\n\t// DOM element.\n\tlet isHydrating = replaceNode === IS_HYDRATE;\n\n\t// To be able to support calling `render()` multiple times on the same\n\t// DOM node, we need to obtain a reference to the previous tree. We do\n\t// this by assigning a new `_children` property to DOM nodes which points\n\t// to the last rendered tree. By default this property is not present, which\n\t// means that we are mounting a new tree for the first time.\n\tlet oldVNode = isHydrating\n\t\t? null\n\t\t: (replaceNode && replaceNode._children) || parentDom._children;\n\tvnode = createElement(Fragment, null, [vnode]);\n\n\t// List of effects that need to be called after diffing.\n\tlet commitQueue = [];\n\tdiff(\n\t\tparentDom,\n\t\t// Determine the new vnode tree and store it on the DOM element on\n\t\t// our custom `_children` property.\n\t\t((isHydrating ? parentDom : replaceNode || parentDom)._children = vnode),\n\t\toldVNode || EMPTY_OBJ,\n\t\tEMPTY_OBJ,\n\t\tparentDom.ownerSVGElement !== undefined,\n\t\treplaceNode && !isHydrating\n\t\t\t? [replaceNode]\n\t\t\t: oldVNode\n\t\t\t? null\n\t\t\t: EMPTY_ARR.slice.call(parentDom.childNodes),\n\t\tcommitQueue,\n\t\treplaceNode || EMPTY_OBJ,\n\t\tisHydrating\n\t);\n\n\t// Flush all queued effects\n\tcommitRoot(commitQueue, vnode);\n}\n\n/**\n * Update an existing DOM element with data from a Preact virtual node\n * @param {import('./index').ComponentChild} vnode The virtual node to render\n * @param {import('./internal').PreactElement} parentDom The DOM element to\n * update\n */\nexport function hydrate(vnode, parentDom) {\n\trender(vnode, parentDom, IS_HYDRATE);\n}\n", "import { enqueueRender } from './component';\n\nexport let i = 0;\n\nexport function createContext(defaultValue) {\n\tconst ctx = {};\n\n\tconst context = {\n\t\t_id: '__cC' + i++,\n\t\t_defaultValue: defaultValue,\n\t\tConsumer(props, context) {\n\t\t\treturn props.children(context);\n\t\t},\n\t\tProvider(props) {\n\t\t\tif (!this.getChildContext) {\n\t\t\t\tconst subs = [];\n\t\t\t\tthis.getChildContext = () => {\n\t\t\t\t\tctx[context._id] = this;\n\t\t\t\t\treturn ctx;\n\t\t\t\t};\n\n\t\t\t\tthis.shouldComponentUpdate = _props => {\n\t\t\t\t\tif (this.props.value !== _props.value) {\n\t\t\t\t\t\tsubs.some(c => {\n\t\t\t\t\t\t\tc.context = _props.value;\n\t\t\t\t\t\t\tenqueueRender(c);\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t};\n\n\t\t\t\tthis.sub = c => {\n\t\t\t\t\tsubs.push(c);\n\t\t\t\t\tlet old = c.componentWillUnmount;\n\t\t\t\t\tc.componentWillUnmount = () => {\n\t\t\t\t\t\tsubs.splice(subs.indexOf(c), 1);\n\t\t\t\t\t\told && old.call(c);\n\t\t\t\t\t};\n\t\t\t\t};\n\t\t\t}\n\n\t\t\treturn props.children;\n\t\t}\n\t};\n\n\tcontext.Consumer.contextType = context;\n\n\t// Devtools needs access to the context object when it\n\t// encounters a Provider. This is necessary to support\n\t// setting `displayName` on the context object instead\n\t// of on the component itself. See:\n\t// https://reactjs.org/docs/context.html#contextdisplayname\n\tcontext.Provider._contextRef = context;\n\n\treturn context;\n}\n", "export const EMPTY_OBJ = {};\nexport const EMPTY_ARR = [];\nexport const IS_NON_DIMENSIONAL = /acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord/i;\n", "/**\n * Assign properties from `props` to `obj`\n * @template O, P The obj and props types\n * @param {O} obj The object to copy properties to\n * @param {P} props The object to copy properties from\n * @returns {O & P}\n */\nexport function assign(obj, props) {\n\tfor (let i in props) obj[i] = props[i];\n\treturn /** @type {O & P} */ (obj);\n}\n\n/**\n * Remove a child node from its parent if attached. This is a workaround for\n * IE11 which doesn't support `Element.prototype.remove()`. Using this function\n * is smaller than including a dedicated polyfill.\n * @param {Node} node The node to remove\n */\nexport function removeNode(node) {\n\tlet parentNode = node.parentNode;\n\tif (parentNode) parentNode.removeChild(node);\n}\n", "import { diff, unmount, applyRef } from './index';\nimport { createVNode } from '../create-element';\nimport { EMPTY_OBJ, EMPTY_ARR } from '../constants';\nimport { removeNode } from '../util';\nimport { getDomSibling } from '../component';\n\n/**\n * Diff the children of a virtual node\n * @param {import('../internal').PreactElement} parentDom The DOM element whose\n * children are being diffed\n * @param {import('../internal').VNode} newParentVNode The new virtual\n * node whose children should be diff'ed against oldParentVNode\n * @param {import('../internal').VNode} oldParentVNode The old virtual\n * node whose children should be diff'ed against newParentVNode\n * @param {object} globalContext The current context object - modified by getChildContext\n * @param {boolean} isSvg Whether or not this DOM node is an SVG node\n * @param {Array<import('../internal').PreactElement>} excessDomChildren\n * @param {Array<import('../internal').Component>} commitQueue List of components\n * which have callbacks to invoke in commitRoot\n * @param {Node | Text} oldDom The current attached DOM\n * element any new dom elements should be placed around. Likely `null` on first\n * render (except when hydrating). Can be a sibling DOM element when diffing\n * Fragments that have siblings. In most cases, it starts out as `oldChildren[0]._dom`.\n * @param {boolean} isHydrating Whether or not we are in hydration\n */\nexport function diffChildren(\n\tparentDom,\n\tnewParentVNode,\n\toldParentVNode,\n\tglobalContext,\n\tisSvg,\n\texcessDomChildren,\n\tcommitQueue,\n\toldDom,\n\tisHydrating\n) {\n\tlet i, j, oldVNode, newDom, sibDom, firstChildDom, refs;\n\n\t// This is a compression of oldParentVNode!=null && oldParentVNode != EMPTY_OBJ && oldParentVNode._children || EMPTY_ARR\n\t// as EMPTY_OBJ._children should be `undefined`.\n\tlet oldChildren = (oldParentVNode && oldParentVNode._children) || EMPTY_ARR;\n\n\tlet oldChildrenLength = oldChildren.length;\n\n\t// Only in very specific places should this logic be invoked (top level `render` and `diffElementNodes`).\n\t// I'm using `EMPTY_OBJ` to signal when `diffChildren` is invoked in these situations. I can't use `null`\n\t// for this purpose, because `null` is a valid value for `oldDom` which can mean to skip to this logic\n\t// (e.g. if mounting a new tree in which the old DOM should be ignored (usually for Fragments).\n\tif (oldDom == EMPTY_OBJ) {\n\t\tif (excessDomChildren != null) {\n\t\t\toldDom = excessDomChildren[0];\n\t\t} else if (oldChildrenLength) {\n\t\t\toldDom = getDomSibling(oldParentVNode, 0);\n\t\t} else {\n\t\t\toldDom = null;\n\t\t}\n\t}\n\n\ti = 0;\n\tnewParentVNode._children = toChildArray(\n\t\tnewParentVNode._children,\n\t\tchildVNode => {\n\t\t\tif (childVNode != null) {\n\t\t\t\tchildVNode._parent = newParentVNode;\n\t\t\t\tchildVNode._depth = newParentVNode._depth + 1;\n\n\t\t\t\t// Check if we find a corresponding element in oldChildren.\n\t\t\t\t// If found, delete the array item by setting to `undefined`.\n\t\t\t\t// We use `undefined`, as `null` is reserved for empty placeholders\n\t\t\t\t// (holes).\n\t\t\t\toldVNode = oldChildren[i];\n\n\t\t\t\tif (\n\t\t\t\t\toldVNode === null ||\n\t\t\t\t\t(oldVNode &&\n\t\t\t\t\t\tchildVNode.key == oldVNode.key &&\n\t\t\t\t\t\tchildVNode.type === oldVNode.type)\n\t\t\t\t) {\n\t\t\t\t\toldChildren[i] = undefined;\n\t\t\t\t} else {\n\t\t\t\t\t// Either oldVNode === undefined or oldChildrenLength > 0,\n\t\t\t\t\t// so after this loop oldVNode == null or oldVNode is a valid value.\n\t\t\t\t\tfor (j = 0; j < oldChildrenLength; j++) {\n\t\t\t\t\t\toldVNode = oldChildren[j];\n\t\t\t\t\t\t// If childVNode is unkeyed, we only match similarly unkeyed nodes, otherwise we match by key.\n\t\t\t\t\t\t// We always match by type (in either case).\n\t\t\t\t\t\tif (\n\t\t\t\t\t\t\toldVNode &&\n\t\t\t\t\t\t\tchildVNode.key == oldVNode.key &&\n\t\t\t\t\t\t\tchildVNode.type === oldVNode.type\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\toldChildren[j] = undefined;\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\t\t\t\t\t\toldVNode = null;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\toldVNode = oldVNode || EMPTY_OBJ;\n\n\t\t\t\t// Morph the old element into the new one, but don't append it to the dom yet\n\t\t\t\tnewDom = diff(\n\t\t\t\t\tparentDom,\n\t\t\t\t\tchildVNode,\n\t\t\t\t\toldVNode,\n\t\t\t\t\tglobalContext,\n\t\t\t\t\tisSvg,\n\t\t\t\t\texcessDomChildren,\n\t\t\t\t\tcommitQueue,\n\t\t\t\t\toldDom,\n\t\t\t\t\tisHydrating\n\t\t\t\t);\n\n\t\t\t\tif ((j = childVNode.ref) && oldVNode.ref != j) {\n\t\t\t\t\tif (!refs) refs = [];\n\t\t\t\t\tif (oldVNode.ref) refs.push(oldVNode.ref, null, childVNode);\n\t\t\t\t\trefs.push(j, childVNode._component || newDom, childVNode);\n\t\t\t\t}\n\n\t\t\t\t// Only proceed if the vnode has not been unmounted by `diff()` above.\n\t\t\t\tif (newDom != null) {\n\t\t\t\t\tif (firstChildDom == null) {\n\t\t\t\t\t\tfirstChildDom = newDom;\n\t\t\t\t\t}\n\n\t\t\t\t\tlet nextDom;\n\t\t\t\t\tif (childVNode._nextDom !== undefined) {\n\t\t\t\t\t\t// Only Fragments or components that return Fragment like VNodes will\n\t\t\t\t\t\t// have a non-undefined _nextDom. Continue the diff from the sibling\n\t\t\t\t\t\t// of last DOM child of this child VNode\n\t\t\t\t\t\tnextDom = childVNode._nextDom;\n\n\t\t\t\t\t\t// Eagerly cleanup _nextDom. We don't need to persist the value because\n\t\t\t\t\t\t// it is only used by `diffChildren` to determine where to resume the diff after\n\t\t\t\t\t\t// diffing Components and Fragments. Once we store it the nextDOM local var, we\n\t\t\t\t\t\t// can clean up the property\n\t\t\t\t\t\tchildVNode._nextDom = undefined;\n\t\t\t\t\t} else if (\n\t\t\t\t\t\texcessDomChildren == oldVNode ||\n\t\t\t\t\t\tnewDom != oldDom ||\n\t\t\t\t\t\tnewDom.parentNode == null\n\t\t\t\t\t) {\n\t\t\t\t\t\t// NOTE: excessDomChildren==oldVNode above:\n\t\t\t\t\t\t// This is a compression of excessDomChildren==null && oldVNode==null!\n\t\t\t\t\t\t// The values only have the same type when `null`.\n\n\t\t\t\t\t\touter: if (oldDom == null || oldDom.parentNode !== parentDom) {\n\t\t\t\t\t\t\tparentDom.appendChild(newDom);\n\t\t\t\t\t\t\tnextDom = null;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// `j<oldChildrenLength; j+=2` is an alternative to `j++<oldChildrenLength/2`\n\t\t\t\t\t\t\tfor (\n\t\t\t\t\t\t\t\tsibDom = oldDom, j = 0;\n\t\t\t\t\t\t\t\t(sibDom = sibDom.nextSibling) && j < oldChildrenLength;\n\t\t\t\t\t\t\t\tj += 2\n\t\t\t\t\t\t\t) {\n\t\t\t\t\t\t\t\tif (sibDom == newDom) {\n\t\t\t\t\t\t\t\t\tbreak outer;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tparentDom.insertBefore(newDom, oldDom);\n\t\t\t\t\t\t\tnextDom = oldDom;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// Browsers will infer an option's `value` from `textContent` when\n\t\t\t\t\t\t// no value is present. This essentially bypasses our code to set it\n\t\t\t\t\t\t// later in `diff()`. It works fine in all browsers except for IE11\n\t\t\t\t\t\t// where it breaks setting `select.value`. There it will be always set\n\t\t\t\t\t\t// to an empty string. Re-applying an options value will fix that, so\n\t\t\t\t\t\t// there are probably some internal data structures that aren't\n\t\t\t\t\t\t// updated properly.\n\t\t\t\t\t\t//\n\t\t\t\t\t\t// To fix it we make sure to reset the inferred value, so that our own\n\t\t\t\t\t\t// value check in `diff()` won't be skipped.\n\t\t\t\t\t\tif (newParentVNode.type == 'option') {\n\t\t\t\t\t\t\tparentDom.value = '';\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// If we have pre-calculated the nextDOM node, use it. Else calculate it now\n\t\t\t\t\t// Strictly check for `undefined` here cuz `null` is a valid value of `nextDom`.\n\t\t\t\t\t// See more detail in create-element.js:createVNode\n\t\t\t\t\tif (nextDom !== undefined) {\n\t\t\t\t\t\toldDom = nextDom;\n\t\t\t\t\t} else {\n\t\t\t\t\t\toldDom = newDom.nextSibling;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (typeof newParentVNode.type == 'function') {\n\t\t\t\t\t\t// Because the newParentVNode is Fragment-like, we need to set it's\n\t\t\t\t\t\t// _nextDom property to the nextSibling of its last child DOM node.\n\t\t\t\t\t\t//\n\t\t\t\t\t\t// `oldDom` contains the correct value here because if the last child\n\t\t\t\t\t\t// is a Fragment-like, then oldDom has already been set to that child's _nextDom.\n\t\t\t\t\t\t// If the last child is a DOM VNode, then oldDom will be set to that DOM\n\t\t\t\t\t\t// node's nextSibling.\n\n\t\t\t\t\t\tnewParentVNode._nextDom = oldDom;\n\t\t\t\t\t}\n\t\t\t\t} else if (\n\t\t\t\t\toldDom &&\n\t\t\t\t\toldVNode._dom == oldDom &&\n\t\t\t\t\toldDom.parentNode != parentDom\n\t\t\t\t) {\n\t\t\t\t\t// The above condition is to handle null placeholders. See test in placeholder.test.js:\n\t\t\t\t\t// `efficiently replace null placeholders in parent rerenders`\n\t\t\t\t\toldDom = getDomSibling(oldVNode);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\ti++;\n\t\t\treturn childVNode;\n\t\t}\n\t);\n\n\tnewParentVNode._dom = firstChildDom;\n\n\t// Remove children that are not part of any vnode.\n\tif (excessDomChildren != null && typeof newParentVNode.type != 'function') {\n\t\tfor (i = excessDomChildren.length; i--; ) {\n\t\t\tif (excessDomChildren[i] != null) removeNode(excessDomChildren[i]);\n\t\t}\n\t}\n\n\t// Remove remaining oldChildren if there are any.\n\tfor (i = oldChildrenLength; i--; ) {\n\t\tif (oldChildren[i] != null) unmount(oldChildren[i], oldChildren[i]);\n\t}\n\n\t// Set refs only after unmount\n\tif (refs) {\n\t\tfor (i = 0; i < refs.length; i++) {\n\t\t\tapplyRef(refs[i], refs[++i], refs[++i]);\n\t\t}\n\t}\n}\n\n/**\n * Flatten and loop through the children of a virtual node\n * @param {import('../index').ComponentChildren} children The unflattened\n * children of a virtual node\n * @param {(vnode: import('../internal').VNode) => import('../internal').VNode} [callback]\n * A function to invoke for each child before it is added to the flattened list.\n * @param {Array<import('../internal').VNode | string | number>} [flattened] An flat array of children to modify\n * @returns {import('../internal').VNode[]}\n */\nexport function toChildArray(children, callback, flattened) {\n\tif (flattened == null) flattened = [];\n\n\tif (children == null || typeof children == 'boolean') {\n\t\tif (callback) flattened.push(callback(null));\n\t} else if (Array.isArray(children)) {\n\t\tfor (let i = 0; i < children.length; i++) {\n\t\t\ttoChildArray(children[i], callback, flattened);\n\t\t}\n\t} else if (!callback) {\n\t\tflattened.push(children);\n\t} else if (typeof children == 'string' || typeof children == 'number') {\n\t\tflattened.push(callback(createVNode(null, children, null, null, children)));\n\t} else if (children._dom != null || children._component != null) {\n\t\tflattened.push(\n\t\t\tcallback(\n\t\t\t\tcreateVNode(\n\t\t\t\t\tchildren.type,\n\t\t\t\t\tchildren.props,\n\t\t\t\t\tchildren.key,\n\t\t\t\t\tnull,\n\t\t\t\t\tchildren._original\n\t\t\t\t)\n\t\t\t)\n\t\t);\n\t} else {\n\t\tflattened.push(callback(children));\n\t}\n\n\treturn flattened;\n}\n", "import { IS_NON_DIMENSIONAL } from '../constants';\nimport options from '../options';\n\n/**\n * Diff the old and new properties of a VNode and apply changes to the DOM node\n * @param {import('../internal').PreactElement} dom The DOM node to apply\n * changes to\n * @param {object} newProps The new props\n * @param {object} oldProps The old props\n * @param {boolean} isSvg Whether or not this node is an SVG node\n * @param {boolean} hydrate Whether or not we are in hydration mode\n */\nexport function diffProps(dom, newProps, oldProps, isSvg, hydrate) {\n\tlet i;\n\n\tfor (i in oldProps) {\n\t\tif (i !== 'children' && i !== 'key' && !(i in newProps)) {\n\t\t\tsetProperty(dom, i, null, oldProps[i], isSvg);\n\t\t}\n\t}\n\n\tfor (i in newProps) {\n\t\tif (\n\t\t\t(!hydrate || typeof newProps[i] == 'function') &&\n\t\t\ti !== 'children' &&\n\t\t\ti !== 'key' &&\n\t\t\ti !== 'value' &&\n\t\t\ti !== 'checked' &&\n\t\t\toldProps[i] !== newProps[i]\n\t\t) {\n\t\t\tsetProperty(dom, i, newProps[i], oldProps[i], isSvg);\n\t\t}\n\t}\n}\n\nfunction setStyle(style, key, value) {\n\tif (key[0] === '-') {\n\t\tstyle.setProperty(key, value);\n\t} else if (\n\t\ttypeof value == 'number' &&\n\t\tIS_NON_DIMENSIONAL.test(key) === false\n\t) {\n\t\tstyle[key] = value + 'px';\n\t} else if (value == null) {\n\t\tstyle[key] = '';\n\t} else {\n\t\tstyle[key] = value;\n\t}\n}\n\n/**\n * Set a property value on a DOM node\n * @param {import('../internal').PreactElement} dom The DOM node to modify\n * @param {string} name The name of the property to set\n * @param {*} value The value to set the property to\n * @param {*} oldValue The old value the property had\n * @param {boolean} isSvg Whether or not this DOM node is an SVG node or not\n */\nexport function setProperty(dom, name, value, oldValue, isSvg) {\n\tlet s, useCapture, nameLower;\n\n\tif (isSvg) {\n\t\tif (name === 'className') {\n\t\t\tname = 'class';\n\t\t}\n\t} else if (name === 'class') {\n\t\tname = 'className';\n\t}\n\n\tif (name === 'style') {\n\t\ts = dom.style;\n\n\t\tif (typeof value == 'string') {\n\t\t\ts.cssText = value;\n\t\t} else {\n\t\t\tif (typeof oldValue == 'string') {\n\t\t\t\ts.cssText = '';\n\t\t\t\toldValue = null;\n\t\t\t}\n\n\t\t\tif (oldValue) {\n\t\t\t\tfor (let i in oldValue) {\n\t\t\t\t\tif (!(value && i in value)) {\n\t\t\t\t\t\tsetStyle(s, i, '');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (value) {\n\t\t\t\tfor (let i in value) {\n\t\t\t\t\tif (!oldValue || value[i] !== oldValue[i]) {\n\t\t\t\t\t\tsetStyle(s, i, value[i]);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\t// Benchmark for comparison: https://esbench.com/bench/574c954bdb965b9a00965ac6\n\telse if (name[0] === 'o' && name[1] === 'n') {\n\t\tuseCapture = name !== (name = name.replace(/Capture$/, ''));\n\t\tnameLower = name.toLowerCase();\n\t\tname = (nameLower in dom ? nameLower : name).slice(2);\n\n\t\tif (value) {\n\t\t\tif (!oldValue) dom.addEventListener(name, eventProxy, useCapture);\n\t\t\t(dom._listeners || (dom._listeners = {}))[name] = value;\n\t\t} else {\n\t\t\tdom.removeEventListener(name, eventProxy, useCapture);\n\t\t}\n\t} else if (\n\t\tname !== 'list' &&\n\t\tname !== 'tagName' &&\n\t\t// HTMLButtonElement.form and HTMLInputElement.form are read-only but can be set using\n\t\t// setAttribute\n\t\tname !== 'form' &&\n\t\tname !== 'type' &&\n\t\tname !== 'size' &&\n\t\t!isSvg &&\n\t\tname in dom\n\t) {\n\t\tdom[name] = value == null ? '' : value;\n\t} else if (typeof value != 'function' && name !== 'dangerouslySetInnerHTML') {\n\t\tif (name !== (name = name.replace(/^xlink:?/, ''))) {\n\t\t\tif (value == null || value === false) {\n\t\t\t\tdom.removeAttributeNS(\n\t\t\t\t\t'http://www.w3.org/1999/xlink',\n\t\t\t\t\tname.toLowerCase()\n\t\t\t\t);\n\t\t\t} else {\n\t\t\t\tdom.setAttributeNS(\n\t\t\t\t\t'http://www.w3.org/1999/xlink',\n\t\t\t\t\tname.toLowerCase(),\n\t\t\t\t\tvalue\n\t\t\t\t);\n\t\t\t}\n\t\t} else if (\n\t\t\tvalue == null ||\n\t\t\t(value === false &&\n\t\t\t\t// ARIA-attributes have a different notion of boolean values.\n\t\t\t\t// The value `false` is different from the attribute not\n\t\t\t\t// existing on the DOM, so we can't remove it. For non-boolean\n\t\t\t\t// ARIA-attributes we could treat false as a removal, but the\n\t\t\t\t// amount of exceptions would cost us too many bytes. On top of\n\t\t\t\t// that other VDOM frameworks also always stringify `false`.\n\t\t\t\t!/^ar/.test(name))\n\t\t) {\n\t\t\tdom.removeAttribute(name);\n\t\t} else {\n\t\t\tdom.setAttribute(name, value);\n\t\t}\n\t}\n}\n\n/**\n * Proxy an event to hooked event handlers\n * @param {Event} e The event object from the browser\n * @private\n */\nfunction eventProxy(e) {\n\tthis._listeners[e.type](options.event ? options.event(e) : e);\n}\n", "import { EMPTY_OBJ, EMPTY_ARR } from '../constants';\nimport { Component } from '../component';\nimport { Fragment } from '../create-element';\nimport { diffChildren } from './children';\nimport { diffProps, setProperty } from './props';\nimport { assign, removeNode } from '../util';\nimport options from '../options';\n\n/**\n * Diff two virtual nodes and apply proper changes to the DOM\n * @param {import('../internal').PreactElement} parentDom The parent of the DOM element\n * @param {import('../internal').VNode} newVNode The new virtual node\n * @param {import('../internal').VNode} oldVNode The old virtual node\n * @param {object} globalContext The current context object. Modified by getChildContext\n * @param {boolean} isSvg Whether or not this element is an SVG node\n * @param {Array<import('../internal').PreactElement>} excessDomChildren\n * @param {Array<import('../internal').Component>} commitQueue List of components\n * which have callbacks to invoke in commitRoot\n * @param {Element | Text} oldDom The current attached DOM\n * element any new dom elements should be placed around. Likely `null` on first\n * render (except when hydrating). Can be a sibling DOM element when diffing\n * Fragments that have siblings. In most cases, it starts out as `oldChildren[0]._dom`.\n * @param {boolean} [isHydrating] Whether or not we are in hydration\n */\nexport function diff(\n\tparentDom,\n\tnewVNode,\n\toldVNode,\n\tglobalContext,\n\tisSvg,\n\texcessDomChildren,\n\tcommitQueue,\n\toldDom,\n\tisHydrating\n) {\n\tlet tmp,\n\t\tnewType = newVNode.type;\n\n\t// When passing through createElement it assigns the object\n\t// constructor as undefined. This to prevent JSON-injection.\n\tif (newVNode.constructor !== undefined) return null;\n\n\tif ((tmp = options._diff)) tmp(newVNode);\n\n\ttry {\n\t\touter: if (typeof newType == 'function') {\n\t\t\tlet c, isNew, oldProps, oldState, snapshot, clearProcessingException;\n\t\t\tlet newProps = newVNode.props;\n\n\t\t\t// Necessary for createContext api. Setting this property will pass\n\t\t\t// the context value as `this.context` just for this component.\n\t\t\ttmp = newType.contextType;\n\t\t\tlet provider = tmp && globalContext[tmp._id];\n\t\t\tlet componentContext = tmp\n\t\t\t\t? provider\n\t\t\t\t\t? provider.props.value\n\t\t\t\t\t: tmp._defaultValue\n\t\t\t\t: globalContext;\n\n\t\t\t// Get component and set it to `c`\n\t\t\tif (oldVNode._component) {\n\t\t\t\tc = newVNode._component = oldVNode._component;\n\t\t\t\tclearProcessingException = c._processingException = c._pendingError;\n\t\t\t} else {\n\t\t\t\t// Instantiate the new component\n\t\t\t\tif ('prototype' in newType && newType.prototype.render) {\n\t\t\t\t\tnewVNode._component = c = new newType(newProps, componentContext); // eslint-disable-line new-cap\n\t\t\t\t} else {\n\t\t\t\t\tnewVNode._component = c = new Component(newProps, componentContext);\n\t\t\t\t\tc.constructor = newType;\n\t\t\t\t\tc.render = doRender;\n\t\t\t\t}\n\t\t\t\tif (provider) provider.sub(c);\n\n\t\t\t\tc.props = newProps;\n\t\t\t\tif (!c.state) c.state = {};\n\t\t\t\tc.context = componentContext;\n\t\t\t\tc._globalContext = globalContext;\n\t\t\t\tisNew = c._dirty = true;\n\t\t\t\tc._renderCallbacks = [];\n\t\t\t}\n\n\t\t\t// Invoke getDerivedStateFromProps\n\t\t\tif (c._nextState == null) {\n\t\t\t\tc._nextState = c.state;\n\t\t\t}\n\t\t\tif (newType.getDerivedStateFromProps != null) {\n\t\t\t\tif (c._nextState == c.state) {\n\t\t\t\t\tc._nextState = assign({}, c._nextState);\n\t\t\t\t}\n\n\t\t\t\tassign(\n\t\t\t\t\tc._nextState,\n\t\t\t\t\tnewType.getDerivedStateFromProps(newProps, c._nextState)\n\t\t\t\t);\n\t\t\t}\n\n\t\t\toldProps = c.props;\n\t\t\toldState = c.state;\n\n\t\t\t// Invoke pre-render lifecycle methods\n\t\t\tif (isNew) {\n\t\t\t\tif (\n\t\t\t\t\tnewType.getDerivedStateFromProps == null &&\n\t\t\t\t\tc.componentWillMount != null\n\t\t\t\t) {\n\t\t\t\t\tc.componentWillMount();\n\t\t\t\t}\n\n\t\t\t\tif (c.componentDidMount != null) {\n\t\t\t\t\tc._renderCallbacks.push(c.componentDidMount);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tif (\n\t\t\t\t\tnewType.getDerivedStateFromProps == null &&\n\t\t\t\t\tnewProps !== oldProps &&\n\t\t\t\t\tc.componentWillReceiveProps != null\n\t\t\t\t) {\n\t\t\t\t\tc.componentWillReceiveProps(newProps, componentContext);\n\t\t\t\t}\n\n\t\t\t\tif (\n\t\t\t\t\t(!c._force &&\n\t\t\t\t\t\tc.shouldComponentUpdate != null &&\n\t\t\t\t\t\tc.shouldComponentUpdate(\n\t\t\t\t\t\t\tnewProps,\n\t\t\t\t\t\t\tc._nextState,\n\t\t\t\t\t\t\tcomponentContext\n\t\t\t\t\t\t) === false) ||\n\t\t\t\t\t(newVNode._original === oldVNode._original && !c._processingException)\n\t\t\t\t) {\n\t\t\t\t\tc.props = newProps;\n\t\t\t\t\tc.state = c._nextState;\n\t\t\t\t\t// More info about this here: https://gist.github.com/JoviDeCroock/bec5f2ce93544d2e6070ef8e0036e4e8\n\t\t\t\t\tif (newVNode._original !== oldVNode._original) c._dirty = false;\n\t\t\t\t\tc._vnode = newVNode;\n\t\t\t\t\tnewVNode._dom = oldVNode._dom;\n\t\t\t\t\tnewVNode._children = oldVNode._children;\n\t\t\t\t\tif (c._renderCallbacks.length) {\n\t\t\t\t\t\tcommitQueue.push(c);\n\t\t\t\t\t}\n\n\t\t\t\t\tfor (tmp = 0; tmp < newVNode._children.length; tmp++) {\n\t\t\t\t\t\tif (newVNode._children[tmp]) {\n\t\t\t\t\t\t\tnewVNode._children[tmp]._parent = newVNode;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tbreak outer;\n\t\t\t\t}\n\n\t\t\t\tif (c.componentWillUpdate != null) {\n\t\t\t\t\tc.componentWillUpdate(newProps, c._nextState, componentContext);\n\t\t\t\t}\n\n\t\t\t\tif (c.componentDidUpdate != null) {\n\t\t\t\t\tc._renderCallbacks.push(() => {\n\t\t\t\t\t\tc.componentDidUpdate(oldProps, oldState, snapshot);\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tc.context = componentContext;\n\t\t\tc.props = newProps;\n\t\t\tc.state = c._nextState;\n\n\t\t\tif ((tmp = options._render)) tmp(newVNode);\n\n\t\t\tc._dirty = false;\n\t\t\tc._vnode = newVNode;\n\t\t\tc._parentDom = parentDom;\n\n\t\t\ttmp = c.render(c.props, c.state, c.context);\n\t\t\tlet isTopLevelFragment =\n\t\t\t\ttmp != null && tmp.type == Fragment && tmp.key == null;\n\t\t\tnewVNode._children = isTopLevelFragment\n\t\t\t\t? tmp.props.children\n\t\t\t\t: Array.isArray(tmp)\n\t\t\t\t? tmp\n\t\t\t\t: [tmp];\n\n\t\t\tif (c.getChildContext != null) {\n\t\t\t\tglobalContext = assign(assign({}, globalContext), c.getChildContext());\n\t\t\t}\n\n\t\t\tif (!isNew && c.getSnapshotBeforeUpdate != null) {\n\t\t\t\tsnapshot = c.getSnapshotBeforeUpdate(oldProps, oldState);\n\t\t\t}\n\n\t\t\tdiffChildren(\n\t\t\t\tparentDom,\n\t\t\t\tnewVNode,\n\t\t\t\toldVNode,\n\t\t\t\tglobalContext,\n\t\t\t\tisSvg,\n\t\t\t\texcessDomChildren,\n\t\t\t\tcommitQueue,\n\t\t\t\toldDom,\n\t\t\t\tisHydrating\n\t\t\t);\n\n\t\t\tc.base = newVNode._dom;\n\n\t\t\tif (c._renderCallbacks.length) {\n\t\t\t\tcommitQueue.push(c);\n\t\t\t}\n\n\t\t\tif (clearProcessingException) {\n\t\t\t\tc._pendingError = c._processingException = null;\n\t\t\t}\n\n\t\t\tc._force = false;\n\t\t} else if (\n\t\t\texcessDomChildren == null &&\n\t\t\tnewVNode._original === oldVNode._original\n\t\t) {\n\t\t\tnewVNode._children = oldVNode._children;\n\t\t\tnewVNode._dom = oldVNode._dom;\n\t\t} else {\n\t\t\tnewVNode._dom = diffElementNodes(\n\t\t\t\toldVNode._dom,\n\t\t\t\tnewVNode,\n\t\t\t\toldVNode,\n\t\t\t\tglobalContext,\n\t\t\t\tisSvg,\n\t\t\t\texcessDomChildren,\n\t\t\t\tcommitQueue,\n\t\t\t\tisHydrating\n\t\t\t);\n\t\t}\n\n\t\tif ((tmp = options.diffed)) tmp(newVNode);\n\t} catch (e) {\n\t\tnewVNode._original = null;\n\t\toptions._catchError(e, newVNode, oldVNode);\n\t}\n\n\treturn newVNode._dom;\n}\n\n/**\n * @param {Array<import('../internal').Component>} commitQueue List of components\n * which have callbacks to invoke in commitRoot\n * @param {import('../internal').VNode} root\n */\nexport function commitRoot(commitQueue, root) {\n\tif (options._commit) options._commit(root, commitQueue);\n\n\tcommitQueue.some(c => {\n\t\ttry {\n\t\t\tcommitQueue = c._renderCallbacks;\n\t\t\tc._renderCallbacks = [];\n\t\t\tcommitQueue.some(cb => {\n\t\t\t\tcb.call(c);\n\t\t\t});\n\t\t} catch (e) {\n\t\t\toptions._catchError(e, c._vnode);\n\t\t}\n\t});\n}\n\n/**\n * Diff two virtual nodes representing DOM element\n * @param {import('../internal').PreactElement} dom The DOM element representing\n * the virtual nodes being diffed\n * @param {import('../internal').VNode} newVNode The new virtual node\n * @param {import('../internal').VNode} oldVNode The old virtual node\n * @param {object} globalContext The current context object\n * @param {boolean} isSvg Whether or not this DOM node is an SVG node\n * @param {*} excessDomChildren\n * @param {Array<import('../internal').Component>} commitQueue List of components\n * which have callbacks to invoke in commitRoot\n * @param {boolean} isHydrating Whether or not we are in hydration\n * @returns {import('../internal').PreactElement}\n */\nfunction diffElementNodes(\n\tdom,\n\tnewVNode,\n\toldVNode,\n\tglobalContext,\n\tisSvg,\n\texcessDomChildren,\n\tcommitQueue,\n\tisHydrating\n) {\n\tlet i;\n\tlet oldProps = oldVNode.props;\n\tlet newProps = newVNode.props;\n\n\t// Tracks entering and exiting SVG namespace when descending through the tree.\n\tisSvg = newVNode.type === 'svg' || isSvg;\n\n\tif (excessDomChildren != null) {\n\t\tfor (i = 0; i < excessDomChildren.length; i++) {\n\t\t\tconst child = excessDomChildren[i];\n\n\t\t\t// if newVNode matches an element in excessDomChildren or the `dom`\n\t\t\t// argument matches an element in excessDomChildren, remove it from\n\t\t\t// excessDomChildren so it isn't later removed in diffChildren\n\t\t\tif (\n\t\t\t\tchild != null &&\n\t\t\t\t((newVNode.type === null\n\t\t\t\t\t? child.nodeType === 3\n\t\t\t\t\t: child.localName === newVNode.type) ||\n\t\t\t\t\tdom == child)\n\t\t\t) {\n\t\t\t\tdom = child;\n\t\t\t\texcessDomChildren[i] = null;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t}\n\n\tif (dom == null) {\n\t\tif (newVNode.type === null) {\n\t\t\treturn document.createTextNode(newProps);\n\t\t}\n\n\t\tdom = isSvg\n\t\t\t? document.createElementNS('http://www.w3.org/2000/svg', newVNode.type)\n\t\t\t: document.createElement(\n\t\t\t\t\tnewVNode.type,\n\t\t\t\t\tnewProps.is && { is: newProps.is }\n\t\t\t  );\n\t\t// we created a new parent, so none of the previously attached children can be reused:\n\t\texcessDomChildren = null;\n\t\t// we are creating a new node, so we can assume this is a new subtree (in case we are hydrating), this deopts the hydrate\n\t\tisHydrating = false;\n\t}\n\n\tif (newVNode.type === null) {\n\t\tif (oldProps !== newProps && dom.data != newProps) {\n\t\t\tdom.data = newProps;\n\t\t}\n\t} else {\n\t\tif (excessDomChildren != null) {\n\t\t\texcessDomChildren = EMPTY_ARR.slice.call(dom.childNodes);\n\t\t}\n\n\t\toldProps = oldVNode.props || EMPTY_OBJ;\n\n\t\tlet oldHtml = oldProps.dangerouslySetInnerHTML;\n\t\tlet newHtml = newProps.dangerouslySetInnerHTML;\n\n\t\t// During hydration, props are not diffed at all (including dangerouslySetInnerHTML)\n\t\t// @TODO we should warn in debug mode when props don't match here.\n\t\tif (!isHydrating) {\n\t\t\tif (oldProps === EMPTY_OBJ) {\n\t\t\t\toldProps = {};\n\t\t\t\tfor (let i = 0; i < dom.attributes.length; i++) {\n\t\t\t\t\toldProps[dom.attributes[i].name] = dom.attributes[i].value;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (newHtml || oldHtml) {\n\t\t\t\t// Avoid re-applying the same '__html' if it did not changed between re-render\n\t\t\t\tif (!newHtml || !oldHtml || newHtml.__html != oldHtml.__html) {\n\t\t\t\t\tdom.innerHTML = (newHtml && newHtml.__html) || '';\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tdiffProps(dom, newProps, oldProps, isSvg, isHydrating);\n\n\t\t// If the new vnode didn't have dangerouslySetInnerHTML, diff its children\n\t\tif (newHtml) {\n\t\t\tnewVNode._children = [];\n\t\t} else {\n\t\t\tnewVNode._children = newVNode.props.children;\n\t\t\tdiffChildren(\n\t\t\t\tdom,\n\t\t\t\tnewVNode,\n\t\t\t\toldVNode,\n\t\t\t\tglobalContext,\n\t\t\t\tnewVNode.type === 'foreignObject' ? false : isSvg,\n\t\t\t\texcessDomChildren,\n\t\t\t\tcommitQueue,\n\t\t\t\tEMPTY_OBJ,\n\t\t\t\tisHydrating\n\t\t\t);\n\t\t}\n\n\t\t// (as above, don't diff props during hydration)\n\t\tif (!isHydrating) {\n\t\t\tif (\n\t\t\t\t'value' in newProps &&\n\t\t\t\t(i = newProps.value) !== undefined &&\n\t\t\t\ti !== dom.value\n\t\t\t) {\n\t\t\t\tsetProperty(dom, 'value', i, oldProps.value, false);\n\t\t\t}\n\t\t\tif (\n\t\t\t\t'checked' in newProps &&\n\t\t\t\t(i = newProps.checked) !== undefined &&\n\t\t\t\ti !== dom.checked\n\t\t\t) {\n\t\t\t\tsetProperty(dom, 'checked', i, oldProps.checked, false);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn dom;\n}\n\n/**\n * Invoke or update a ref, depending on whether it is a function or object ref.\n * @param {object|function} ref\n * @param {any} value\n * @param {import('../internal').VNode} vnode\n */\nexport function applyRef(ref, value, vnode) {\n\ttry {\n\t\tif (typeof ref == 'function') ref(value);\n\t\telse ref.current = value;\n\t} catch (e) {\n\t\toptions._catchError(e, vnode);\n\t}\n}\n\n/**\n * Unmount a virtual node from the tree and apply DOM changes\n * @param {import('../internal').VNode} vnode The virtual node to unmount\n * @param {import('../internal').VNode} parentVNode The parent of the VNode that\n * initiated the unmount\n * @param {boolean} [skipRemove] Flag that indicates that a parent node of the\n * current element is already detached from the DOM.\n */\nexport function unmount(vnode, parentVNode, skipRemove) {\n\tlet r;\n\tif (options.unmount) options.unmount(vnode);\n\n\tif ((r = vnode.ref)) {\n\t\tif (!r.current || r.current === vnode._dom) applyRef(r, null, parentVNode);\n\t}\n\n\tlet dom;\n\tif (!skipRemove && typeof vnode.type != 'function') {\n\t\tskipRemove = (dom = vnode._dom) != null;\n\t}\n\n\t// Must be set to `undefined` to properly clean up `_nextDom`\n\t// for which `null` is a valid value. See comment in `create-element.js`\n\tvnode._dom = vnode._nextDom = undefined;\n\n\tif ((r = vnode._component) != null) {\n\t\tif (r.componentWillUnmount) {\n\t\t\ttry {\n\t\t\t\tr.componentWillUnmount();\n\t\t\t} catch (e) {\n\t\t\t\toptions._catchError(e, parentVNode);\n\t\t\t}\n\t\t}\n\n\t\tr.base = r._parentDom = null;\n\t}\n\n\tif ((r = vnode._children)) {\n\t\tfor (let i = 0; i < r.length; i++) {\n\t\t\tif (r[i]) unmount(r[i], parentVNode, skipRemove);\n\t\t}\n\t}\n\n\tif (dom != null) removeNode(dom);\n}\n\n/** The `.render()` method for a PFC backing instance. */\nfunction doRender(props, state, context) {\n\treturn this.constructor(props, context);\n}\n", "import { enqueueRender } from '../component';\n\n/**\n * Find the closest error boundary to a thrown error and call it\n * @param {object} error The thrown value\n * @param {import('../internal').VNode} vnode The vnode that threw\n * the error that was caught (except for unmounting when this parameter\n * is the highest parent that was being unmounted)\n */\nexport function _catchError(error, vnode) {\n\t/** @type {import('../internal').Component} */\n\tlet component, hasCaught;\n\n\tfor (; (vnode = vnode._parent); ) {\n\t\tif ((component = vnode._component) && !component._processingException) {\n\t\t\ttry {\n\t\t\t\tif (\n\t\t\t\t\tcomponent.constructor &&\n\t\t\t\t\tcomponent.constructor.getDerivedStateFromError != null\n\t\t\t\t) {\n\t\t\t\t\thasCaught = true;\n\t\t\t\t\tcomponent.setState(\n\t\t\t\t\t\tcomponent.constructor.getDerivedStateFromError(error)\n\t\t\t\t\t);\n\t\t\t\t}\n\n\t\t\t\tif (component.componentDidCatch != null) {\n\t\t\t\t\thasCaught = true;\n\t\t\t\t\tcomponent.componentDidCatch(error);\n\t\t\t\t}\n\n\t\t\t\tif (hasCaught)\n\t\t\t\t\treturn enqueueRender((component._pendingError = component));\n\t\t\t} catch (e) {\n\t\t\t\terror = e;\n\t\t\t}\n\t\t}\n\t}\n\n\tthrow error;\n}\n", "import { assign } from './util';\nimport { EMPTY_ARR } from './constants';\nimport { createVNode } from './create-element';\n\n/**\n * Clones the given VNode, optionally adding attributes/props and replacing its children.\n * @param {import('./internal').VNode} vnode The virtual DOM element to clone\n * @param {object} props Attributes/props to add when cloning\n * @param {Array<import('./index').ComponentChildren>} rest Any additional arguments will be used as replacement children.\n * @returns {import('./internal').VNode}\n */\nexport function cloneElement(vnode, props) {\n\tprops = assign(assign({}, vnode.props), props);\n\tif (arguments.length > 2) props.children = EMPTY_ARR.slice.call(arguments, 2);\n\tlet normalizedProps = {};\n\tfor (const i in props) {\n\t\tif (i !== 'key' && i !== 'ref') normalizedProps[i] = props[i];\n\t}\n\n\treturn createVNode(\n\t\tvnode.type,\n\t\tnormalizedProps,\n\t\tprops.key || vnode.key,\n\t\tprops.ref || vnode.ref,\n\t\tnull\n\t);\n}\n"], "names": ["options", "isValidElement", "rerenderQueue", "rerenderCount", "defer", "prevDebounce", "IS_HYDRATE", "i", "EMPTY_OBJ", "EMPTY_ARR", "IS_NON_DIMENSIONAL", "assign", "obj", "props", "removeNode", "node", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "createElement", "type", "children", "normalizedProps", "arguments", "length", "push", "defaultProps", "undefined", "createVNode", "key", "ref", "original", "vnode", "_children", "_parent", "_depth", "_dom", "_nextDom", "_component", "constructor", "_original", "Fragment", "Component", "context", "getDomSibling", "childIndex", "indexOf", "sibling", "updateParentDomPointers", "child", "base", "enqueueRender", "c", "_dirty", "debounceRendering", "process", "queue", "sort", "a", "b", "_vnode", "some", "component", "commitQueue", "oldVNode", "newDom", "oldDom", "parentDom", "_parentDom", "diff", "_globalContext", "ownerSVGElement", "commitRoot", "diff<PERSON><PERSON><PERSON><PERSON>", "newParentVNode", "oldParentVNode", "globalContext", "isSvg", "excessDomChildren", "isHydrating", "j", "sibDom", "firstChildDom", "refs", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "childVNode", "nextDom", "outer", "append<PERSON><PERSON><PERSON>", "nextS<PERSON>ling", "insertBefore", "value", "unmount", "applyRef", "callback", "flattened", "Array", "isArray", "diffProps", "dom", "newProps", "oldProps", "hydrate", "setProperty", "setStyle", "style", "test", "name", "oldValue", "s", "useCapture", "nameLower", "cssText", "replace", "toLowerCase", "slice", "addEventListener", "eventProxy", "_listeners", "removeEventListener", "removeAttributeNS", "setAttributeNS", "removeAttribute", "setAttribute", "e", "event", "newVNode", "tmp", "isNew", "oldState", "snapshot", "clearProcessingException", "provider", "componentContext", "newType", "_diff", "contextType", "_id", "_defaultValue", "_processingException", "_pendingError", "prototype", "render", "doR<PERSON>", "sub", "state", "_renderCallbacks", "_nextState", "getDerivedStateFromProps", "componentWillMount", "componentDidMount", "componentWillReceiveProps", "_force", "shouldComponentUpdate", "componentWillUpdate", "componentDidUpdate", "_render", "getChildContext", "getSnapshotBeforeUpdate", "diffElementNodes", "diffed", "_catchError", "root", "_commit", "cb", "call", "oldHtml", "newHtml", "nodeType", "localName", "document", "createTextNode", "createElementNS", "is", "data", "childNodes", "dangerouslySetInnerHTML", "attributes", "__html", "innerHTML", "checked", "current", "parentVNode", "<PERSON><PERSON><PERSON><PERSON>", "r", "componentWillUnmount", "this", "replaceNode", "_root", "error", "<PERSON><PERSON><PERSON><PERSON>", "getDerivedStateFromError", "setState", "componentDidCatch", "update", "forceUpdate", "Promise", "then", "bind", "resolve", "setTimeout", "defaultValue", "ctx", "Consumer", "Provider", "subs", "_this", "_props", "old", "splice", "_contextRef"], "mappings": "gLAWMA,ECyFOC,ECiETC,EACAC,EAQEC,EAcFC,ECvLEC,ECHKC,ECFEC,EAAY,GACZC,EAAY,GACZC,EAAqB,8DCK3B,SAASC,EAAOC,EAAKC,OACtB,IAAIN,KAAKM,EAAOD,EAAIL,GAAKM,EAAMN,YAU9B,SAASO,EAAWC,OACtBC,EAAaD,EAAKC,WAClBA,GAAYA,EAAWC,YAAYF,GLVjC,SAASG,EAAcC,EAAMN,EAAOO,GAApC,IAELb,cADGc,EAAkB,OAEjBd,KAAKM,EACC,QAANN,GAAqB,QAANA,IAAac,EAAgBd,GAAKM,EAAMN,OAGxDe,UAAUC,OAAS,MACtBH,EAAW,CAACA,GAEPb,EAAI,EAAGA,EAAIe,UAAUC,OAAQhB,IACjCa,EAASI,KAAKF,EAAUf,OAGV,MAAZa,IACHC,EAAgBD,SAAWA,GAKT,mBAARD,GAA2C,MAArBA,EAAKM,iBAChClB,KAAKY,EAAKM,kBACaC,IAAvBL,EAAgBd,KACnBc,EAAgBd,GAAKY,EAAKM,aAAalB,WAKnCoB,EACNR,EACAE,EACAR,GAASA,EAAMe,IACff,GAASA,EAAMgB,IACf,MAgBK,SAASF,EAAYR,EAAMN,EAAOe,EAAKC,EAAKC,OAG5CC,EAAQ,CACbZ,KAAAA,EACAN,MAAAA,EACAe,IAAAA,EACAC,IAAAA,EACAG,IAAW,KACXC,GAAS,KACTC,IAAQ,EACRC,IAAM,KAKNC,SAAUV,EACVW,IAAY,KACZC,iBAAaZ,EACba,IAAWT,UAGI,MAAZA,IAAkBC,EAAMQ,IAAYR,GACpC/B,EAAQ+B,OAAO/B,EAAQ+B,MAAMA,GAE1BA,EAOD,SAASS,EAAS3B,UACjBA,EAAMO,SChFP,SAASqB,EAAU5B,EAAO6B,QAC3B7B,MAAQA,OACR6B,QAAUA,EAqET,SAASC,EAAcZ,EAAOa,MAClB,MAAdA,SAEIb,EAAME,GACVU,EAAcZ,EAAME,GAASF,EAAME,GAAQD,IAAUa,QAAQd,GAAS,GACtE,aAGAe,EACGF,EAAab,EAAMC,IAAUT,OAAQqB,OAG5B,OAFfE,EAAUf,EAAMC,IAAUY,KAEa,MAAhBE,EAAQX,WAIvBW,EAAQX,UASW,mBAAdJ,EAAMZ,KAAqBwB,EAAcZ,GAAS,KAsCjE,SAASgB,EAAwBhB,GAAjC,IAGWxB,EACJyC,KAHyB,OAA1BjB,EAAQA,EAAME,KAAwC,MAApBF,EAAMM,IAAoB,KAChEN,EAAMI,IAAOJ,EAAMM,IAAWY,KAAO,KAC5B1C,EAAI,EAAGA,EAAIwB,EAAMC,IAAUT,OAAQhB,OAE9B,OADTyC,EAAQjB,EAAMC,IAAUzB,KACO,MAAdyC,EAAMb,IAAc,CACxCJ,EAAMI,IAAOJ,EAAMM,IAAWY,KAAOD,EAAMb,iBAKtCY,EAAwBhB,IAqC1B,SAASmB,EAAcC,KAE1BA,EAAEC,MACFD,EAAEC,KAAS,IACZlD,EAAcsB,KAAK2B,KAClBhD,KACFE,IAAiBL,EAAQqD,sBAEzBhD,EAAeL,EAAQqD,oBACNjD,GAAOkD,GAK1B,SAASA,YACJC,EACIpD,EAAgBD,EAAcqB,QACrCgC,EAAQrD,EAAcsD,KAAK,SAACC,EAAGC,UAAMD,EAAEE,IAAOzB,IAASwB,EAAEC,IAAOzB,MAChEhC,EAAgB,GAGhBqD,EAAMK,KAAK,SAAAT,GApGb,IAAyBU,EAMnBC,EACEC,EAGFC,EATDjC,EACHkC,EACAC,EAkGKf,EAAEC,MAnGPa,GADGlC,GADoB8B,EAqGQV,GApGVQ,KACNxB,KACf+B,EAAYL,EAAUM,OAGlBL,EAAc,IACZC,EAAWpD,EAAO,GAAIoB,IACnBQ,IAAYwB,EAEjBC,EAASI,EACZF,EACAnC,EACAgC,EACAF,EAAUQ,SACoB3C,IAA9BwC,EAAUI,gBACV,KACAR,EACU,MAAVG,EAAiBtB,EAAcZ,GAASkC,GAEzCM,EAAWT,EAAa/B,GAEpBiC,GAAUC,GACblB,EAAwBhB,OKjHpB,SAASyC,EACfN,EACAO,EACAC,EACAC,EACAC,EACAC,EACAf,EACAG,EACAa,GATM,IAWFvE,EAAGwE,EAAGhB,EAAUC,EAAQgB,EAAQC,EAAeC,EAI/CC,EAAeT,GAAkBA,EAAe1C,KAAcvB,EAE9D2E,EAAoBD,EAAY5D,UAMhC0C,GAAUzD,IAEZyD,EADwB,MAArBY,EACMA,EAAkB,GACjBO,EACDzC,EAAc+B,EAAgB,GAE9B,MAIXnE,EAAI,EACJkE,EAAezC,IAAYqD,EAC1BZ,EAAezC,IACf,SAAAsD,MACmB,MAAdA,EAAoB,IACvBA,EAAWrD,GAAUwC,EACrBa,EAAWpD,IAASuC,EAAevC,IAAS,EAS9B,QAHd6B,EAAWoB,EAAY5E,KAIrBwD,GACAuB,EAAW1D,KAAOmC,EAASnC,KAC3B0D,EAAWnE,OAAS4C,EAAS5C,KAE9BgE,EAAY5E,QAAKmB,WAIZqD,EAAI,EAAGA,EAAIK,EAAmBL,IAAK,KACvChB,EAAWoB,EAAYJ,KAKtBO,EAAW1D,KAAOmC,EAASnC,KAC3B0D,EAAWnE,OAAS4C,EAAS5C,KAC5B,CACDgE,EAAYJ,QAAKrD,QAGlBqC,EAAW,QAObC,EAASI,EACRF,EACAoB,EALDvB,EAAWA,GAAYvD,EAOtBmE,EACAC,EACAC,EACAf,EACAG,EACAa,IAGIC,EAAIO,EAAWzD,MAAQkC,EAASlC,KAAOkD,IACtCG,IAAMA,EAAO,IACdnB,EAASlC,KAAKqD,EAAK1D,KAAKuC,EAASlC,IAAK,KAAMyD,GAChDJ,EAAK1D,KAAKuD,EAAGO,EAAWjD,KAAc2B,EAAQsB,IAIjC,MAAVtB,EAAgB,KAKfuB,KAJiB,MAAjBN,IACHA,EAAgBjB,QAIWtC,IAAxB4D,EAAWlD,IAIdmD,EAAUD,EAAWlD,IAMrBkD,EAAWlD,SAAWV,OAChB,GACNmD,GAAqBd,GACrBC,GAAUC,GACW,MAArBD,EAAOhD,WACN,CAKDwE,EAAO,GAAc,MAAVvB,GAAkBA,EAAOjD,aAAekD,EAClDA,EAAUuB,YAAYzB,GACtBuB,EAAU,SACJ,KAGLP,EAASf,EAAQc,EAAI,GACpBC,EAASA,EAAOU,cAAgBX,EAAIK,EACrCL,GAAK,KAEDC,GAAUhB,QACPwB,EAGRtB,EAAUyB,aAAa3B,EAAQC,GAC/BsB,EAAUtB,EAagB,UAAvBQ,EAAetD,OAClB+C,EAAU0B,MAAQ,IAQnB3B,OADevC,IAAZ6D,EACMA,EAEAvB,EAAO0B,YAGiB,mBAAvBjB,EAAetD,OASzBsD,EAAerC,IAAW6B,QAG3BA,GACAF,EAAS5B,KAAQ8B,GACjBA,EAAOjD,YAAckD,IAIrBD,EAAStB,EAAcoB,WAIzBxD,IACO+E,IAITb,EAAetC,IAAO8C,EAGG,MAArBJ,GAA2D,mBAAvBJ,EAAetD,SACjDZ,EAAIsE,EAAkBtD,OAAQhB,KACN,MAAxBsE,EAAkBtE,IAAYO,EAAW+D,EAAkBtE,QAK5DA,EAAI6E,EAAmB7E,KACL,MAAlB4E,EAAY5E,IAAYsF,EAAQV,EAAY5E,GAAI4E,EAAY5E,OAI7D2E,MACE3E,EAAI,EAAGA,EAAI2E,EAAK3D,OAAQhB,IAC5BuF,EAASZ,EAAK3E,GAAI2E,IAAO3E,GAAI2E,IAAO3E,IAchC,SAAS8E,EAAajE,EAAU2E,EAAUC,MAC/B,MAAbA,IAAmBA,EAAY,IAEnB,MAAZ5E,GAAuC,kBAAZA,EAC1B2E,GAAUC,EAAUxE,KAAKuE,EAAS,YAChC,GAAIE,MAAMC,QAAQ9E,OACnB,IAAIb,EAAI,EAAGA,EAAIa,EAASG,OAAQhB,IACpC8E,EAAajE,EAASb,GAAIwF,EAAUC,QAKrCA,EAAUxE,KAHCuE,EAGIA,EADc,iBAAZ3E,GAA2C,iBAAZA,EACxBO,EAAY,KAAMP,EAAU,KAAM,KAAMA,GACrC,MAAjBA,EAASe,KAAuC,MAAvBf,EAASiB,IAG1CV,EACCP,EAASD,KACTC,EAASP,MACTO,EAASQ,IACT,KACAR,EAASmB,KAKYnB,GAhBTA,UAmBT4E,ECvQD,SAASG,EAAUC,EAAKC,EAAUC,EAAU1B,EAAO2B,OACrDhG,MAECA,KAAK+F,EACC,aAAN/F,GAA0B,QAANA,GAAiBA,KAAK8F,GAC7CG,EAAYJ,EAAK7F,EAAG,KAAM+F,EAAS/F,GAAIqE,OAIpCrE,KAAK8F,EAENE,GAAiC,mBAAfF,EAAS9F,IACvB,aAANA,GACM,QAANA,GACM,UAANA,GACM,YAANA,GACA+F,EAAS/F,KAAO8F,EAAS9F,IAEzBiG,EAAYJ,EAAK7F,EAAG8F,EAAS9F,GAAI+F,EAAS/F,GAAIqE,GAKjD,SAAS6B,EAASC,EAAO9E,EAAKgE,GACd,MAAXhE,EAAI,GACP8E,EAAMF,YAAY5E,EAAKgE,GAKvBc,EAAM9E,GAHU,iBAATgE,IAC0B,IAAjClF,EAAmBiG,KAAK/E,GAEXgE,EAAQ,KACF,MAATA,EACG,GAEAA,EAYR,SAASY,EAAYJ,EAAKQ,EAAMhB,EAAOiB,EAAUjC,GAAjD,IACFkC,EAAGC,EAAYC,EAsBPzG,EAQAA,KA5BRqE,EACU,cAATgC,IACHA,EAAO,SAEW,UAATA,IACVA,EAAO,aAGK,UAATA,KACHE,EAAIV,EAAIM,MAEY,iBAATd,EACVkB,EAAEG,QAAUrB,MACN,IACiB,iBAAZiB,IACVC,EAAEG,QAAU,GACZJ,EAAW,MAGRA,MACMtG,KAAKsG,EACPjB,GAASrF,KAAKqF,GACnBa,EAASK,EAAGvG,EAAG,OAKdqF,MACMrF,KAAKqF,EACRiB,GAAYjB,EAAMrF,KAAOsG,EAAStG,IACtCkG,EAASK,EAAGvG,EAAGqF,EAAMrF,QAOL,MAAZqG,EAAK,IAA0B,MAAZA,EAAK,IAChCG,EAAaH,KAAUA,EAAOA,EAAKM,QAAQ,WAAY,KACvDF,EAAYJ,EAAKO,cACjBP,GAAQI,KAAaZ,EAAMY,EAAYJ,GAAMQ,MAAM,GAE/CxB,GACEiB,GAAUT,EAAIiB,iBAAiBT,EAAMU,EAAYP,IACrDX,EAAImB,IAAenB,EAAImB,EAAa,KAAKX,GAAQhB,GAElDQ,EAAIoB,oBAAoBZ,EAAMU,EAAYP,IAGlC,SAATH,GACS,YAATA,GAGS,SAATA,GACS,SAATA,GACS,SAATA,IACChC,GACDgC,KAAQR,EAERA,EAAIQ,GAAiB,MAAThB,EAAgB,GAAKA,EACP,mBAATA,GAAgC,4BAATgB,IACpCA,KAAUA,EAAOA,EAAKM,QAAQ,WAAY,KAChC,MAATtB,IAA2B,IAAVA,EACpBQ,EAAIqB,kBACH,+BACAb,EAAKO,eAGNf,EAAIsB,eACH,+BACAd,EAAKO,cACLvB,GAIO,MAATA,IACW,IAAVA,IAOC,MAAMe,KAAKC,GAEbR,EAAIuB,gBAAgBf,GAEpBR,EAAIwB,aAAahB,EAAMhB,IAU1B,SAAS0B,EAAWO,QACdN,EAAWM,EAAE1G,MAAMnB,EAAQ8H,MAAQ9H,EAAQ8H,MAAMD,GAAKA,GCvIrD,SAASzD,EACfF,EACA6D,EACAhE,EACAY,EACAC,EACAC,EACAf,EACAG,EACAa,GATM,IAWFkD,EAWE7E,EAAG8E,EAAO3B,EAAU4B,EAAUC,EAAUC,EACxC/B,EAKAgC,EACAC,EAjBLC,EAAUR,EAAS5G,aAISO,IAAzBqG,EAASzF,YAA2B,OAAO,MAE1C0F,EAAMhI,EAAQwI,MAAQR,EAAID,OAG9BvC,EAAO,GAAsB,mBAAX+C,EAAuB,IAEpClC,EAAW0B,EAASlH,MAKpBwH,GADJL,EAAMO,EAAQE,cACQ9D,EAAcqD,EAAIU,KACpCJ,EAAmBN,EACpBK,EACCA,EAASxH,MAAM+E,MACfoC,EAAIW,GACLhE,EAGCZ,EAAS1B,IAEZ+F,GADAjF,EAAI4E,EAAS1F,IAAa0B,EAAS1B,KACNuG,GAAuBzF,EAAE0F,KAGlD,cAAeN,GAAWA,EAAQO,UAAUC,OAC/ChB,EAAS1F,IAAac,EAAI,IAAIoF,EAAQlC,EAAUiC,IAEhDP,EAAS1F,IAAac,EAAI,IAAIV,EAAU4D,EAAUiC,GAClDnF,EAAEb,YAAciG,EAChBpF,EAAE4F,OAASC,GAERX,GAAUA,EAASY,IAAI9F,GAE3BA,EAAEtC,MAAQwF,EACLlD,EAAE+F,QAAO/F,EAAE+F,MAAQ,IACxB/F,EAAET,QAAU4F,EACZnF,EAAEkB,IAAiBM,EACnBsD,EAAQ9E,EAAEC,KAAS,EACnBD,EAAEgG,IAAmB,IAIF,MAAhBhG,EAAEiG,MACLjG,EAAEiG,IAAajG,EAAE+F,OAEsB,MAApCX,EAAQc,2BACPlG,EAAEiG,KAAcjG,EAAE+F,QACrB/F,EAAEiG,IAAazI,EAAO,GAAIwC,EAAEiG,MAG7BzI,EACCwC,EAAEiG,IACFb,EAAQc,yBAAyBhD,EAAUlD,EAAEiG,OAI/C9C,EAAWnD,EAAEtC,MACbqH,EAAW/E,EAAE+F,MAGTjB,EAEkC,MAApCM,EAAQc,0BACgB,MAAxBlG,EAAEmG,oBAEFnG,EAAEmG,qBAGwB,MAAvBnG,EAAEoG,mBACLpG,EAAEgG,IAAiB3H,KAAK2B,EAAEoG,uBAErB,IAE+B,MAApChB,EAAQc,0BACRhD,IAAaC,GACkB,MAA/BnD,EAAEqG,2BAEFrG,EAAEqG,0BAA0BnD,EAAUiC,IAIpCnF,EAAEsG,KACwB,MAA3BtG,EAAEuG,wBAKI,IAJNvG,EAAEuG,sBACDrD,EACAlD,EAAEiG,IACFd,IAEDP,EAASxF,MAAcwB,EAASxB,MAAcY,EAAEyF,GAChD,KACDzF,EAAEtC,MAAQwF,EACVlD,EAAE+F,MAAQ/F,EAAEiG,IAERrB,EAASxF,MAAcwB,EAASxB,MAAWY,EAAEC,KAAS,GAC1DD,EAAEQ,IAASoE,EACXA,EAAS5F,IAAO4B,EAAS5B,IACzB4F,EAAS/F,IAAY+B,EAAS/B,IAC1BmB,EAAEgG,IAAiB5H,QACtBuC,EAAYtC,KAAK2B,GAGb6E,EAAM,EAAGA,EAAMD,EAAS/F,IAAUT,OAAQyG,IAC1CD,EAAS/F,IAAUgG,KACtBD,EAAS/F,IAAUgG,GAAK/F,GAAU8F,SAI9BvC,EAGsB,MAAzBrC,EAAEwG,qBACLxG,EAAEwG,oBAAoBtD,EAAUlD,EAAEiG,IAAYd,GAGnB,MAAxBnF,EAAEyG,oBACLzG,EAAEgG,IAAiB3H,KAAK,WACvB2B,EAAEyG,mBAAmBtD,EAAU4B,EAAUC,KAK5ChF,EAAET,QAAU4F,EACZnF,EAAEtC,MAAQwF,EACVlD,EAAE+F,MAAQ/F,EAAEiG,KAEPpB,EAAMhI,EAAQ6J,MAAU7B,EAAID,GAEjC5E,EAAEC,KAAS,EACXD,EAAEQ,IAASoE,EACX5E,EAAEgB,IAAaD,EAEf8D,EAAM7E,EAAE4F,OAAO5F,EAAEtC,MAAOsC,EAAE+F,MAAO/F,EAAET,SAGnCqF,EAAS/F,IADD,MAAPgG,GAAeA,EAAI7G,MAAQqB,GAAuB,MAAXwF,EAAIpG,IAEzCoG,EAAInH,MAAMO,SACV6E,MAAMC,QAAQ8B,GACdA,EACA,CAACA,GAEqB,MAArB7E,EAAE2G,kBACLnF,EAAgBhE,EAAOA,EAAO,GAAIgE,GAAgBxB,EAAE2G,oBAGhD7B,GAAsC,MAA7B9E,EAAE4G,0BACf5B,EAAWhF,EAAE4G,wBAAwBzD,EAAU4B,IAGhD1D,EACCN,EACA6D,EACAhE,EACAY,EACAC,EACAC,EACAf,EACAG,EACAa,GAGD3B,EAAEF,KAAO8E,EAAS5F,IAEdgB,EAAEgG,IAAiB5H,QACtBuC,EAAYtC,KAAK2B,GAGdiF,IACHjF,EAAE0F,IAAgB1F,EAAEyF,GAAuB,MAG5CzF,EAAEsG,KAAS,OAEU,MAArB5E,GACAkD,EAASxF,MAAcwB,EAASxB,KAEhCwF,EAAS/F,IAAY+B,EAAS/B,IAC9B+F,EAAS5F,IAAO4B,EAAS5B,KAEzB4F,EAAS5F,IAAO6H,EACfjG,EAAS5B,IACT4F,EACAhE,EACAY,EACAC,EACAC,EACAf,EACAgB,IAIGkD,EAAMhI,EAAQiK,SAASjC,EAAID,GAC/B,MAAOF,GACRE,EAASxF,IAAY,KACrBvC,EAAQkK,IAAYrC,EAAGE,EAAUhE,UAG3BgE,EAAS5F,IAQV,SAASoC,EAAWT,EAAaqG,GACnCnK,EAAQoK,KAASpK,EAAQoK,IAAQD,EAAMrG,GAE3CA,EAAYF,KAAK,SAAAT,OAEfW,EAAcX,EAAEgG,IAChBhG,EAAEgG,IAAmB,GACrBrF,EAAYF,KAAK,SAAAyG,GAChBA,EAAGC,KAAKnH,KAER,MAAO0E,GACR7H,EAAQkK,IAAYrC,EAAG1E,EAAEQ,QAmB5B,SAASqG,EACR5D,EACA2B,EACAhE,EACAY,EACAC,EACAC,EACAf,EACAgB,GARD,IAUKvE,EASIyC,EA+CHuH,EACAC,EAOOjK,EA/DR+F,EAAWvC,EAASlD,MACpBwF,EAAW0B,EAASlH,SAGxB+D,EAA0B,QAAlBmD,EAAS5G,MAAkByD,EAEV,MAArBC,MACEtE,EAAI,EAAGA,EAAIsE,EAAkBtD,OAAQhB,OAO/B,OANJyC,EAAQ6B,EAAkBtE,OAOX,OAAlBwH,EAAS5G,KACW,IAAnB6B,EAAMyH,SACNzH,EAAM0H,YAAc3C,EAAS5G,OAC/BiF,GAAOpD,GACP,CACDoD,EAAMpD,EACN6B,EAAkBtE,GAAK,cAMf,MAAP6F,EAAa,IACM,OAAlB2B,EAAS5G,YACLwJ,SAASC,eAAevE,GAGhCD,EAAMxB,EACH+F,SAASE,gBAAgB,6BAA8B9C,EAAS5G,MAChEwJ,SAASzJ,cACT6G,EAAS5G,KACTkF,EAASyE,IAAM,CAAEA,GAAIzE,EAASyE,KAGjCjG,EAAoB,KAEpBC,GAAc,KAGO,OAAlBiD,EAAS5G,KACRmF,IAAaD,GAAYD,EAAI2E,MAAQ1E,IACxCD,EAAI2E,KAAO1E,OAEN,IACmB,MAArBxB,IACHA,EAAoBpE,EAAU2G,MAAMkD,KAAKlE,EAAI4E,aAK1CT,GAFJjE,EAAWvC,EAASlD,OAASL,GAENyK,wBACnBT,EAAUnE,EAAS4E,yBAIlBnG,EAAa,IACbwB,IAAa9F,MAChB8F,EAAW,GACF/F,EAAI,EAAGA,EAAI6F,EAAI8E,WAAW3J,OAAQhB,IAC1C+F,EAASF,EAAI8E,WAAW3K,GAAGqG,MAAQR,EAAI8E,WAAW3K,GAAGqF,OAInD4E,GAAWD,KAETC,GAAYD,GAAWC,EAAQW,QAAUZ,EAAQY,SACrD/E,EAAIgF,UAAaZ,GAAWA,EAAQW,QAAW,KAKlDhF,EAAUC,EAAKC,EAAUC,EAAU1B,EAAOE,GAGtC0F,EACHzC,EAAS/F,IAAY,IAErB+F,EAAS/F,IAAY+F,EAASlH,MAAMO,SACpCoD,EACC4B,EACA2B,EACAhE,EACAY,EACkB,kBAAlBoD,EAAS5G,MAAmCyD,EAC5CC,EACAf,EACAtD,EACAsE,IAKGA,IAEH,UAAWuB,QACc3E,KAAxBnB,EAAI8F,EAAST,QACdrF,IAAM6F,EAAIR,OAEVY,EAAYJ,EAAK,QAAS7F,EAAG+F,EAASV,OAAO,GAG7C,YAAaS,QACc3E,KAA1BnB,EAAI8F,EAASgF,UACd9K,IAAM6F,EAAIiF,SAEV7E,EAAYJ,EAAK,UAAW7F,EAAG+F,EAAS+E,SAAS,WAK7CjF,EASD,SAASN,EAASjE,EAAK+D,EAAO7D,OAEjB,mBAAPF,EAAmBA,EAAI+D,GAC7B/D,EAAIyJ,QAAU1F,EAClB,MAAOiC,GACR7H,EAAQkK,IAAYrC,EAAG9F,IAYlB,SAAS8D,EAAQ9D,EAAOwJ,EAAaC,GAArC,IACFC,EAOArF,EAsBM7F,KA5BNP,EAAQ6F,SAAS7F,EAAQ6F,QAAQ9D,IAEhC0J,EAAI1J,EAAMF,OACT4J,EAAEH,SAAWG,EAAEH,UAAYvJ,EAAMI,KAAM2D,EAAS2F,EAAG,KAAMF,IAI1DC,GAAmC,mBAAdzJ,EAAMZ,OAC/BqK,EAAmC,OAArBpF,EAAMrE,EAAMI,MAK3BJ,EAAMI,IAAOJ,EAAMK,SAAWV,EAEA,OAAzB+J,EAAI1J,EAAMM,KAAqB,IAC/BoJ,EAAEC,yBAEJD,EAAEC,uBACD,MAAO7D,GACR7H,EAAQkK,IAAYrC,EAAG0D,GAIzBE,EAAExI,KAAOwI,EAAEtH,IAAa,QAGpBsH,EAAI1J,EAAMC,QACLzB,EAAI,EAAGA,EAAIkL,EAAElK,OAAQhB,IACzBkL,EAAElL,IAAIsF,EAAQ4F,EAAElL,GAAIgL,EAAaC,GAI5B,MAAPpF,GAAatF,EAAWsF,GAI7B,SAAS4C,EAASnI,EAAOqI,EAAOxG,UACxBiJ,KAAKrJ,YAAYzB,EAAO6B,GNpczB,SAASqG,EAAOhH,EAAOmC,EAAW0H,GAAlC,IAMF9G,EAOAf,EAMAD,EAlBA9D,EAAQ6L,IAAO7L,EAAQ6L,GAAM9J,EAAOmC,GAYpCH,GAPAe,EAAc8G,IAAgBtL,GAQ/B,KACCsL,GAAeA,EAAY5J,KAAckC,EAAUlC,IACvDD,EAAQb,EAAcsB,EAAU,KAAM,CAACT,IAGnC+B,EAAc,GAClBM,EACCF,GAGEY,EAAcZ,EAAY0H,GAAe1H,GAAWlC,IAAYD,EAClEgC,GAAYvD,EACZA,OAC8BkB,IAA9BwC,EAAUI,gBACVsH,IAAgB9G,EACb,CAAC8G,GACD7H,EACA,KACAtD,EAAU2G,MAAMkD,KAAKpG,EAAU8G,YAClClH,EACA8H,GAAepL,EACfsE,GAIDP,EAAWT,EAAa/B,GH3CnB/B,EAAU,CACfkK,IUHM,SAAqB4B,EAAO/J,WAE9B8B,EAAWkI,EAEPhK,EAAQA,EAAME,QAChB4B,EAAY9B,EAAMM,OAAgBwB,EAAU+E,UAG9C/E,EAAUvB,aACwC,MAAlDuB,EAAUvB,YAAY0J,2BAEtBD,GAAY,EACZlI,EAAUoI,SACTpI,EAAUvB,YAAY0J,yBAAyBF,KAId,MAA/BjI,EAAUqI,oBACbH,GAAY,EACZlI,EAAUqI,kBAAkBJ,IAGzBC,EACH,OAAO7I,EAAeW,EAAUgF,IAAgBhF,GAChD,MAAOgE,GACRiE,EAAQjE,QAKLiE,IT6DM7L,EAAiB,SAAA8B,UACpB,MAATA,QAAuCL,IAAtBK,EAAMO,aC5ExBG,EAAUqG,UAAUmD,SAAW,SAASE,EAAQpG,OAE3Ce,EAEHA,EADG6E,KAAKvC,MAAeuC,KAAKzC,MACxByC,KAAKvC,IAELuC,KAAKvC,IAAazI,EAAO,GAAIgL,KAAKzC,OAGlB,mBAAViD,IACVA,EAASA,EAAOrF,EAAG6E,KAAK9K,QAGrBsL,GACHxL,EAAOmG,EAAGqF,GAIG,MAAVA,GAEAR,KAAKhI,MACJoC,GAAU4F,KAAKxC,IAAiB3H,KAAKuE,GACzC7C,EAAcyI,QAShBlJ,EAAUqG,UAAUsD,YAAc,SAASrG,GACtC4F,KAAKhI,WAIH8F,KAAS,EACV1D,GAAU4F,KAAKxC,IAAiB3H,KAAKuE,GACzC7C,EAAcyI,QAchBlJ,EAAUqG,UAAUC,OAASvG,EAwFzBtC,EAAgB,GAChBC,EAAgB,EAQdC,EACa,mBAAXiM,QACJA,QAAQvD,UAAUwD,KAAKC,KAAKF,QAAQG,WACpCC,WC5KEnM,EAAaE,ECHRD,EAAI,uBD6DR,SAAiBwB,EAAOmC,GAC9B6E,EAAOhH,EAAOmC,EAAW5D,qDFuBnB,iBACC,oDU7ED,SAAsByB,EAAOlB,GAA7B,IAGFQ,EACOd,MAAAA,KAHXM,EAAQF,EAAOA,EAAO,GAAIoB,EAAMlB,OAAQA,GACpCS,UAAUC,OAAS,IAAGV,EAAMO,SAAWX,EAAU2G,MAAMkD,KAAKhJ,UAAW,IACvED,EAAkB,GACNR,EACL,QAANN,GAAqB,QAANA,IAAac,EAAgBd,GAAKM,EAAMN,WAGrDoB,EACNI,EAAMZ,KACNE,EACAR,EAAMe,KAAOG,EAAMH,IACnBf,EAAMgB,KAAOE,EAAMF,IACnB,uBPpBK,SAAuB6K,GAAvB,IACAC,EAAM,GAENjK,EAAU,CACfgG,IAAK,OAASnI,IACdoI,GAAe+D,EACfE,kBAAS/L,EAAO6B,UACR7B,EAAMO,SAASsB,IAEvBmK,kBAAShM,OAEDiM,gBADFnB,KAAK7B,kBACHgD,EAAO,QACRhD,gBAAkB,kBACtB6C,EAAIjK,EAAQgG,KAAOqE,EACZJ,QAGHjD,sBAAwB,SAAAsD,GACxBD,EAAKlM,MAAM+E,QAAUoH,EAAOpH,OAC/BkH,EAAKlJ,KAAK,SAAAT,GACTA,EAAET,QAAUsK,EAAOpH,MACnB1C,EAAcC,WAKZ8F,IAAM,SAAA9F,GACV2J,EAAKtL,KAAK2B,OACN8J,EAAM9J,EAAEuI,qBACZvI,EAAEuI,qBAAuB,WACxBoB,EAAKI,OAAOJ,EAAKjK,QAAQM,GAAI,GAC7B8J,GAAOA,EAAI3C,KAAKnH,MAKZtC,EAAMO,kBAIfsB,EAAQkK,SAASnE,YAAc/F,EAO/BA,EAAQmK,SAASM,GAAczK,EAExBA"}