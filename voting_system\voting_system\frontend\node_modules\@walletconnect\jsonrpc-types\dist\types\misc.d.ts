/// <reference types="node" />
import { EventEmitter } from "events";
export declare abstract class IEvents {
    abstract events: EventEmitter;
    abstract on(event: string, listener: any): void;
    abstract once(event: string, listener: any): void;
    abstract off(event: string, listener: any): void;
    abstract removeListener(event: string, listener: any): void;
}
//# sourceMappingURL=misc.d.ts.map