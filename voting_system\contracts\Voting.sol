// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

contract Voting {
    // Structure for an election
    struct Election {
        uint256 id;
        string title;
        string description;
        uint256 electionType; // 1=Presidential, 2=Parliamentary, 3=<PERSON>, 4=Governor, 5=Women Rep, 6=MCA
        bool active;
        uint256 startTime;
        uint256 endTime;
    }

    // Structure for a candidate
    struct Candidate {
        uint256 id;
        uint256 electionId;
        string name;
        string party;
        string constituency;
        string county;
        bool isIndependent;
        uint256 voteCount;
    }

    // Structure for a voter
    struct Voter {
        bool isRegistered;
        string idNumber;
        string constituency;
        string county;
        mapping(uint256 => bool) hasVotedInElection; // election ID => has voted
    }

    // State variables
    address public admin;
    uint256 public electionCount;
    uint256 public candidateCount;
    uint256 public voterCount;

    mapping(uint256 => Election) public elections;
    mapping(uint256 => Candidate) public candidates;
    mapping(address => Voter) public voters;
    mapping(string => bool) public registeredIdNumbers; // Prevent duplicate ID numbers
    mapping(uint256 => uint256[]) public electionCandidates; // election ID => candidate IDs
    mapping(uint256 => mapping(address => uint256)) public votes; // election ID => voter address => candidate ID

    // Events
    event ElectionCreated(uint256 electionId, string title, uint256 electionType, uint256 startTime, uint256 endTime);
    event CandidateRegistered(uint256 candidateId, uint256 electionId, string name, string party);
    event VoterRegistered(address voterAddress, string idNumber);
    event VoteCast(address voter, uint256 electionId, uint256 candidateId);
    event ElectionEnded(uint256 electionId);

    // Modifiers
    modifier onlyAdmin() {
        require(msg.sender == admin, "Only admin can call this function");
        _;
    }

    modifier onlyRegisteredVoter() {
        require(voters[msg.sender].isRegistered, "Only registered voters can call this function");
        _;
    }

    // Constructor
    constructor() {
        admin = msg.sender;
        electionCount = 0;
        candidateCount = 0;
        voterCount = 0;
    }

    // Function to create a new election
    function createElection(
        string memory _title,
        string memory _description,
        uint256 _electionType,
        uint256 _startTimeUnix,
        uint256 _endTimeUnix
    ) public onlyAdmin {
        require(_electionType >= 1 && _electionType <= 6, "Invalid election type");
        require(_startTimeUnix > block.timestamp, "Start time must be in the future");
        require(_endTimeUnix > _startTimeUnix, "End time must be after start time");

        electionCount++;

        elections[electionCount] = Election({
            id: electionCount,
            title: _title,
            description: _description,
            electionType: _electionType,
            active: false, // Will be activated at start time
            startTime: _startTimeUnix,
            endTime: _endTimeUnix
        });

        emit ElectionCreated(electionCount, _title, _electionType, _startTimeUnix, _endTimeUnix);
    }

    // Function to register a candidate
    function registerCandidate(
        uint256 _electionId,
        string memory _name,
        string memory _party,
        string memory _constituency,
        string memory _county,
        bool _isIndependent
    ) public onlyAdmin {
        require(_electionId > 0 && _electionId <= electionCount, "Invalid election ID");

        candidateCount++;

        candidates[candidateCount] = Candidate({
            id: candidateCount,
            electionId: _electionId,
            name: _name,
            party: _party,
            constituency: _constituency,
            county: _county,
            isIndependent: _isIndependent,
            voteCount: 0
        });

        // Add candidate to election
        electionCandidates[_electionId].push(candidateCount);

        emit CandidateRegistered(candidateCount, _electionId, _name, _party);
    }

    // Function to register a voter
    function registerVoter(
        address _voterAddress,
        string memory _idNumber,
        string memory _constituency,
        string memory _county
    ) public onlyAdmin {
        require(!voters[_voterAddress].isRegistered, "Voter address already registered");
        require(!registeredIdNumbers[_idNumber], "ID number already registered");

        Voter storage voter = voters[_voterAddress];
        voter.isRegistered = true;
        voter.idNumber = _idNumber;
        voter.constituency = _constituency;
        voter.county = _county;

        registeredIdNumbers[_idNumber] = true;
        voterCount++;

        emit VoterRegistered(_voterAddress, _idNumber);
    }

    // Function to activate an election
    function activateElection(uint256 _electionId) public onlyAdmin {
        require(_electionId > 0 && _electionId <= electionCount, "Invalid election ID");
        require(!elections[_electionId].active, "Election is already active");
        require(block.timestamp >= elections[_electionId].startTime, "Election start time not reached");
        require(block.timestamp < elections[_electionId].endTime, "Election end time has passed");

        elections[_electionId].active = true;
    }

    // Function to cast a vote
    function vote(uint256 _electionId, uint256 _candidateId) public onlyRegisteredVoter {
        require(_electionId > 0 && _electionId <= electionCount, "Invalid election ID");
        require(elections[_electionId].active, "Election is not active");
        require(block.timestamp >= elections[_electionId].startTime, "Election has not started");
        require(block.timestamp < elections[_electionId].endTime, "Election has ended");
        require(!voters[msg.sender].hasVotedInElection[_electionId], "You have already voted in this election");

        // Verify candidate is in this election
        bool candidateFound = false;
        for (uint i = 0; i < electionCandidates[_electionId].length; i++) {
            if (electionCandidates[_electionId][i] == _candidateId) {
                candidateFound = true;
                break;
            }
        }
        require(candidateFound, "Candidate not found in this election");

        // Record vote
        candidates[_candidateId].voteCount++;
        voters[msg.sender].hasVotedInElection[_electionId] = true;
        votes[_electionId][msg.sender] = _candidateId;

        emit VoteCast(msg.sender, _electionId, _candidateId);
    }

    // Function to end an election
    function endElection(uint256 _electionId) public onlyAdmin {
        require(_electionId > 0 && _electionId <= electionCount, "Invalid election ID");
        require(elections[_electionId].active, "Election is not active");

        elections[_electionId].active = false;

        emit ElectionEnded(_electionId);
    }

    // Function to get election details
    function getElection(uint256 _electionId) public view returns (
        uint256 id,
        string memory title,
        string memory description,
        uint256 electionType,
        bool active,
        uint256 startTime,
        uint256 endTime
    ) {
        require(_electionId > 0 && _electionId <= electionCount, "Invalid election ID");

        Election memory election = elections[_electionId];

        return (
            election.id,
            election.title,
            election.description,
            election.electionType,
            election.active,
            election.startTime,
            election.endTime
        );
    }

    // Function to get candidate details
    function getCandidate(uint256 _candidateId) public view returns (
        uint256 id,
        uint256 electionId,
        string memory name,
        string memory party,
        string memory constituency,
        string memory county,
        bool isIndependent,
        uint256 voteCount
    ) {
        require(_candidateId > 0 && _candidateId <= candidateCount, "Invalid candidate ID");

        Candidate memory candidate = candidates[_candidateId];

        return (
            candidate.id,
            candidate.electionId,
            candidate.name,
            candidate.party,
            candidate.constituency,
            candidate.county,
            candidate.isIndependent,
            candidate.voteCount
        );
    }

    // Function to get all candidates for an election
    function getElectionCandidates(uint256 _electionId) public view returns (uint256[] memory) {
        require(_electionId > 0 && _electionId <= electionCount, "Invalid election ID");

        return electionCandidates[_electionId];
    }

    // Function to check if a voter has voted in an election
    function hasVoted(address _voter, uint256 _electionId) public view returns (bool) {
        require(_electionId > 0 && _electionId <= electionCount, "Invalid election ID");

        return voters[_voter].hasVotedInElection[_electionId];
    }

    // Function to get the candidate a voter voted for in an election
    function getVoterChoice(address _voter, uint256 _electionId) public view returns (uint256) {
        require(_electionId > 0 && _electionId <= electionCount, "Invalid election ID");
        require(voters[_voter].hasVotedInElection[_electionId], "Voter has not voted in this election");

        return votes[_electionId][_voter];
    }

    // Function to get the total number of elections
    function getElectionCount() public view returns (uint256) {
        return electionCount;
    }

    // Function to get the total number of candidates
    function getCandidateCount() public view returns (uint256) {
        return candidateCount;
    }

    // Function to get the total number of registered voters
    function getVoterCount() public view returns (uint256) {
        return voterCount;
    }
}
