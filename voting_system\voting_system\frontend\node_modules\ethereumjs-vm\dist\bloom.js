'use strict';

var assert = require('assert');
var utils = require('ethereumjs-util');
var byteSize = 256;

/**
 * Represents a Bloom
 * @constructor
 * @param {Buffer} bitvector
 */
var Bloom = module.exports = function (bitvector) {
  if (!bitvector) {
    this.bitvector = utils.zeros(byteSize);
  } else {
    assert(bitvector.length === byteSize, 'bitvectors must be 2048 bits long');
    this.bitvector = bitvector;
  }
};

/**
 * adds an element to a bit vector of a 64 byte bloom filter
 * @method add
 * @param {Buffer} e the element to add
 */
Bloom.prototype.add = function (e) {
  e = utils.keccak256(e);
  var mask = 2047; // binary 11111111111

  for (var i = 0; i < 3; i++) {
    var first2bytes = e.readUInt16BE(i * 2);
    var loc = mask & first2bytes;
    var byteLoc = loc >> 3;
    var bitLoc = 1 << loc % 8;
    this.bitvector[byteSize - byteLoc - 1] |= bitLoc;
  }
};

/**
 * checks if an element is in the bloom
 * @method check
 * @param {Buffer} e the element to check
 * @returns {boolean} Returns {@code true} if the element is in the bloom
 */
Bloom.prototype.check = function (e) {
  e = utils.keccak256(e);
  var mask = 2047; // binary 11111111111
  var match = true;

  for (var i = 0; i < 3 && match; i++) {
    var first2bytes = e.readUInt16BE(i * 2);
    var loc = mask & first2bytes;
    var byteLoc = loc >> 3;
    var bitLoc = 1 << loc % 8;
    match = this.bitvector[byteSize - byteLoc - 1] & bitLoc;
  }

  return Boolean(match);
};

/**
 * checks if multiple topics are in a bloom
 * @method multiCheck
 * @param {Buffer} topics
 * @returns {boolean} Returns {@code true} if every topic is in the bloom
 */
Bloom.prototype.multiCheck = function (topics) {
  var self = this;
  return topics.every(function (t) {
    return self.check(t);
  });
};

/**
 * bitwise or blooms together
 * @method or
 * @param {Bloom} bloom
 */
Bloom.prototype.or = function (bloom) {
  if (bloom) {
    for (var i = 0; i <= byteSize; i++) {
      this.bitvector[i] = this.bitvector[i] | bloom.bitvector[i];
    }
  }
};