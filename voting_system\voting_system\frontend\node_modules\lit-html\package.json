{"name": "lit-html", "version": "2.8.0", "description": "HTML templates literals in JavaScript", "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "https://github.com/lit/lit.git", "directory": "packages/lit-html"}, "author": "Google LLC", "homepage": "https://lit.dev/", "main": "lit-html.js", "type": "module", "exports": {".": {"types": "./development/lit-html.d.ts", "node": {"development": "./node/development/lit-html.js", "default": "./node/lit-html.js"}, "development": "./development/lit-html.js", "default": "./lit-html.js"}, "./async-directive.js": {"types": "./development/async-directive.d.ts", "node": {"development": "./node/development/async-directive.js", "default": "./node/async-directive.js"}, "development": "./development/async-directive.js", "default": "./async-directive.js"}, "./directive-helpers.js": {"types": "./development/directive-helpers.d.ts", "node": {"development": "./node/development/directive-helpers.js", "default": "./node/directive-helpers.js"}, "development": "./development/directive-helpers.js", "default": "./directive-helpers.js"}, "./directive.js": {"types": "./development/directive.d.ts", "node": {"development": "./node/development/directive.js", "default": "./node/directive.js"}, "development": "./development/directive.js", "default": "./directive.js"}, "./directives/async-append.js": {"types": "./development/directives/async-append.d.ts", "node": {"development": "./node/development/directives/async-append.js", "default": "./node/directives/async-append.js"}, "development": "./development/directives/async-append.js", "default": "./directives/async-append.js"}, "./directives/async-replace.js": {"types": "./development/directives/async-replace.d.ts", "node": {"development": "./node/development/directives/async-replace.js", "default": "./node/directives/async-replace.js"}, "development": "./development/directives/async-replace.js", "default": "./directives/async-replace.js"}, "./directives/cache.js": {"types": "./development/directives/cache.d.ts", "node": {"development": "./node/development/directives/cache.js", "default": "./node/directives/cache.js"}, "development": "./development/directives/cache.js", "default": "./directives/cache.js"}, "./directives/choose.js": {"types": "./development/directives/choose.d.ts", "node": {"development": "./node/development/directives/choose.js", "default": "./node/directives/choose.js"}, "development": "./development/directives/choose.js", "default": "./directives/choose.js"}, "./directives/class-map.js": {"types": "./development/directives/class-map.d.ts", "node": {"development": "./node/development/directives/class-map.js", "default": "./node/directives/class-map.js"}, "development": "./development/directives/class-map.js", "default": "./directives/class-map.js"}, "./directives/guard.js": {"types": "./development/directives/guard.d.ts", "node": {"development": "./node/development/directives/guard.js", "default": "./node/directives/guard.js"}, "development": "./development/directives/guard.js", "default": "./directives/guard.js"}, "./directives/if-defined.js": {"types": "./development/directives/if-defined.d.ts", "node": {"development": "./node/development/directives/if-defined.js", "default": "./node/directives/if-defined.js"}, "development": "./development/directives/if-defined.js", "default": "./directives/if-defined.js"}, "./directives/join.js": {"types": "./development/directives/join.d.ts", "node": {"development": "./node/development/directives/join.js", "default": "./node/directives/join.js"}, "development": "./development/directives/join.js", "default": "./directives/join.js"}, "./directives/keyed.js": {"types": "./development/directives/keyed.d.ts", "node": {"development": "./node/development/directives/keyed.js", "default": "./node/directives/keyed.js"}, "development": "./development/directives/keyed.js", "default": "./directives/keyed.js"}, "./directives/live.js": {"types": "./development/directives/live.d.ts", "node": {"development": "./node/development/directives/live.js", "default": "./node/directives/live.js"}, "development": "./development/directives/live.js", "default": "./directives/live.js"}, "./directives/map.js": {"types": "./development/directives/map.d.ts", "node": {"development": "./node/development/directives/map.js", "default": "./node/directives/map.js"}, "development": "./development/directives/map.js", "default": "./directives/map.js"}, "./directives/range.js": {"types": "./development/directives/range.d.ts", "node": {"development": "./node/development/directives/range.js", "default": "./node/directives/range.js"}, "development": "./development/directives/range.js", "default": "./directives/range.js"}, "./directives/ref.js": {"types": "./development/directives/ref.d.ts", "node": {"development": "./node/development/directives/ref.js", "default": "./node/directives/ref.js"}, "development": "./development/directives/ref.js", "default": "./directives/ref.js"}, "./directives/repeat.js": {"types": "./development/directives/repeat.d.ts", "node": {"development": "./node/development/directives/repeat.js", "default": "./node/directives/repeat.js"}, "development": "./development/directives/repeat.js", "default": "./directives/repeat.js"}, "./directives/style-map.js": {"types": "./development/directives/style-map.d.ts", "node": {"development": "./node/development/directives/style-map.js", "default": "./node/directives/style-map.js"}, "development": "./development/directives/style-map.js", "default": "./directives/style-map.js"}, "./directives/template-content.js": {"types": "./development/directives/template-content.d.ts", "node": {"development": "./node/development/directives/template-content.js", "default": "./node/directives/template-content.js"}, "development": "./development/directives/template-content.js", "default": "./directives/template-content.js"}, "./directives/unsafe-html.js": {"types": "./development/directives/unsafe-html.d.ts", "node": {"development": "./node/development/directives/unsafe-html.js", "default": "./node/directives/unsafe-html.js"}, "development": "./development/directives/unsafe-html.js", "default": "./directives/unsafe-html.js"}, "./directives/unsafe-svg.js": {"types": "./development/directives/unsafe-svg.d.ts", "node": {"development": "./node/development/directives/unsafe-svg.js", "default": "./node/directives/unsafe-svg.js"}, "development": "./development/directives/unsafe-svg.js", "default": "./directives/unsafe-svg.js"}, "./directives/until.js": {"types": "./development/directives/until.d.ts", "node": {"development": "./node/development/directives/until.js", "default": "./node/directives/until.js"}, "development": "./development/directives/until.js", "default": "./directives/until.js"}, "./directives/when.js": {"types": "./development/directives/when.d.ts", "node": {"development": "./node/development/directives/when.js", "default": "./node/directives/when.js"}, "development": "./development/directives/when.js", "default": "./directives/when.js"}, "./experimental-hydrate.js": {"types": "./development/experimental-hydrate.d.ts", "node": {"development": "./node/development/experimental-hydrate.js", "default": "./node/experimental-hydrate.js"}, "development": "./development/experimental-hydrate.js", "default": "./experimental-hydrate.js"}, "./polyfill-support.js": {"types": "./development/polyfill-support.d.ts", "node": {"development": "./node/development/polyfill-support.js", "default": "./node/polyfill-support.js"}, "development": "./development/polyfill-support.js", "default": "./polyfill-support.js"}, "./private-ssr-support.js": {"types": "./development/private-ssr-support.d.ts", "node": {"development": "./node/development/private-ssr-support.js", "default": "./node/private-ssr-support.js"}, "development": "./development/private-ssr-support.js", "default": "./private-ssr-support.js"}, "./static.js": {"types": "./development/static.d.ts", "node": {"development": "./node/development/static.js", "default": "./node/static.js"}, "development": "./development/static.js", "default": "./static.js"}, "./is-server.js": {"types": "./development/is-server.d.ts", "node": {"development": "./node/development/is-server.js", "default": "./node/is-server.js"}, "development": "./development/is-server.js", "default": "./is-server.js"}}, "scripts": {"build": "wireit", "build:ts": "wireit", "build:ts:types": "wireit", "build:rollup": "wireit", "build:version-stability-test": "wireit", "check-version": "wireit", "checksize": "wireit", "prepublishOnly": "npm run check-version", "test": "wireit", "test:dev": "wireit", "test:prod": "wireit", "test:node": "wireit", "test:node-dev": "wireit"}, "files": ["/async-directive.{d.ts,d.ts.map,js,js.map}", "/directive-helpers.{d.ts,d.ts.map,js,js.map}", "/directive.{d.ts,d.ts.map,js,js.map}", "/experimental-hydrate.{d.ts,d.ts.map,js,js.map}", "/lit-html.{d.ts,d.ts.map,js,js.map}", "/polyfill-support.{d.ts,d.ts.map,js,js.map}", "/private-ssr-support.{d.ts,d.ts.map,js,js.map}", "/static.{d.ts,d.ts.map,js,js.map}", "/is-server.{d.ts,d.ts.map,js,js.map}", "/development/", "!/development/test/", "/directives/", "/node/"], "wireit": {"build": {"dependencies": ["build:rollup", "build:ts", "build:ts:types", "build:version-stability-test"]}, "build:ts": {"#comment": "Note this also builds polyfill-support via a TypeScript project reference.", "command": "tsc --build --pretty", "dependencies": ["../labs/testing:build:ts:utils"], "clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json", "tsconfig.polyfill-support.json"], "output": ["development/**/*.{js,js.map,d.ts,d.ts.map}", "tsconfig.tsbuildinfo", "tsconfig.polyfill-support.tsbuildinfo"]}, "build:ts:types": {"command": "treemirror development . \"**/*.d.ts{,.map}\"", "dependencies": ["../internal-scripts:build", "build:ts"], "files": [], "output": ["*.d.ts{,.map}", "directives/*.d.ts{,.map}"]}, "build:rollup": {"command": "rollup -c", "dependencies": ["build:ts"], "files": ["rollup.config.js", "../../rollup-common.js", "src/test/*_test.html", "src/test/polyfill-support/*_test.html"], "output": ["async-directive.js{,.map}", "directive-helpers.js{,.map}", "directive.js{,.map}", "experimental-hydrate.js{,.map}", "lit-html.js{,.map}", "polyfill-support.js{,.map}", "private-ssr-support.js{,.map}", "static.js{,.map}", "is-server.js{,.map}", "directives/*.js{,.map}", "test/*_test.html", "development/test/*_test.html", "test/polyfill-support/*_test.html", "development/test/polyfill-support/*_test.html", "node/"]}, "build:version-stability-test": {"command": "rollup -c rollup-version-stability-test.config.js", "dependencies": ["build:ts"], "files": ["rollup-version-stability-test.config.js", "rollup.config.js", "../../rollup-common.js"], "output": ["version-stability-build"]}, "checksize": {"command": "rollup -c --environment=CHECKSIZE", "dependencies": ["build:ts"], "files": ["rollup.config.js", "../../rollup-common.js"], "output": []}, "check-version": {"command": "node scripts/check-version-tracker.js", "files": ["scripts/check-version-tracker.js", "package.json", "src/lit-html.ts"], "output": []}, "test": {"dependencies": ["test:dev", "test:prod", "test:node", "test:node-dev", "check-version"]}, "test:dev": {"command": "MODE=dev node ../tests/run-web-tests.js \"development/**/*_test.(js|html)\" --config ../tests/web-test-runner.config.js", "dependencies": ["build:ts", "../tests:build"], "env": {"BROWSERS": {"external": true}}, "files": [], "output": []}, "test:prod": {"command": "MODE=prod node ../tests/run-web-tests.js \"development/**/*_test.(js|html)\" --config ../tests/web-test-runner.config.js", "dependencies": ["build:rollup", "build:ts", "build:version-stability-test", "../tests:build"], "env": {"BROWSERS": {"external": true}}, "files": [], "output": []}, "test:node": {"command": "node development/test/node-imports.js", "dependencies": ["build:ts", "build:rollup"], "files": [], "output": []}, "test:node-dev": {"command": "node --conditions=development development/test/node-imports.js", "dependencies": ["build:ts", "build:rollup"], "files": [], "output": []}}, "dependencies": {"@types/trusted-types": "^2.0.2"}, "devDependencies": {"@lit-internal/scripts": "^1.0.0", "@lit-labs/testing": "^0.2.0", "@types/web-ie11": "^0.0.0", "@webcomponents/shadycss": "^1.8.0", "@webcomponents/template": "^1.4.4", "@webcomponents/webcomponentsjs": "^2.6.0"}, "typings": "lit-html.d.ts", "directories": {"test": "test"}}