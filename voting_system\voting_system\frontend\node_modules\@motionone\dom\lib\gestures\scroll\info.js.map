{"version": 3, "file": "info.js", "sourceRoot": "", "sources": ["../../../src/gestures/scroll/info.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,iBAAiB,EAAE,MAAM,kBAAkB,CAAA;AAG9D;;GAEG;AACH,MAAM,UAAU,GAAG,EAAE,CAAA;AAErB,MAAM,cAAc,GAAG,GAAmB,EAAE,CAAC,CAAC;IAC5C,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,EAAE;IACV,QAAQ,EAAE,CAAC;IACX,YAAY,EAAE,CAAC;IACf,YAAY,EAAE,CAAC;IACf,YAAY,EAAE,CAAC;IACf,eAAe,EAAE,CAAC;IAClB,QAAQ,EAAE,CAAC;CACZ,CAAC,CAAA;AAEF,MAAM,CAAC,MAAM,gBAAgB,GAAG,GAAe,EAAE,CAAC,CAAC;IACjD,IAAI,EAAE,CAAC;IACP,CAAC,EAAE,cAAc,EAAE;IACnB,CAAC,EAAE,cAAc,EAAE;CACpB,CAAC,CAAA;AAEF,MAAM,IAAI,GAAG;IACX,CAAC,EAAE;QACD,MAAM,EAAE,OAAO;QACf,QAAQ,EAAE,MAAM;KACjB;IACD,CAAC,EAAE;QACD,MAAM,EAAE,QAAQ;QAChB,QAAQ,EAAE,KAAK;KAChB;CACO,CAAA;AAEV,SAAS,cAAc,CACrB,OAAoB,EACpB,QAAmB,EACnB,IAAgB,EAChB,IAAY;IAEZ,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAA;IAC3B,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAA;IAE3C,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAA;IACzB,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAA;IAE1B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,SAAS,QAAQ,EAAE,CAAC,CAAA;IAC3C,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,SAAS,MAAM,EAAE,CAAC,GAAG,OAAO,CAAC,SAAS,MAAM,EAAE,CAAC,CAAA;IAC3E,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAA;IACtB,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;IAClB,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAA;IAClC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;IAE5D,MAAM,OAAO,GAAG,IAAI,GAAG,QAAQ,CAAA;IAC/B,IAAI,CAAC,QAAQ;QACX,OAAO,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,EAAE,OAAO,CAAC,CAAA;AAC9E,CAAC;AAED,MAAM,UAAU,gBAAgB,CAC9B,OAAoB,EACpB,IAAgB,EAChB,IAAY;IAEZ,cAAc,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;IACxC,cAAc,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;IACxC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;AAClB,CAAC"}