{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAAsB;AAOtB;;;;;IAKI;AACJ,SAAgB,MAAM,CAAC,KAAY;IACjC,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;QACxB,MAAM,MAAM,GAAa,EAAE,CAAA;QAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;SAC9B;QACD,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;QACjC,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,CAAA;KAC3D;SAAM;QACL,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAA;QAChC,OAAO,QAAQ,CAAC,MAAM,KAAK,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,GAAG,GAAG;YAC/C,CAAC,CAAC,QAAQ;YACV,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAA;KAClE;AACH,CAAC;AAdD,wBAcC;AAED;;;;GAIG;AACH,SAAS,YAAY,CAAC,CAAS,EAAE,IAAY;IAC3C,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QAChC,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAA;KAC5C;IAED,OAAO,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;AAC1B,CAAC;AAED,SAAS,YAAY,CAAC,GAAW,EAAE,MAAc;IAC/C,IAAI,GAAG,GAAG,EAAE,EAAE;QACZ,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,MAAM,CAAC,CAAC,CAAA;KACnC;SAAM;QACL,MAAM,SAAS,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAA;QAC/B,MAAM,OAAO,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,CAAA;QACpC,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM,GAAG,EAAE,GAAG,OAAO,CAAC,CAAA;QACjD,OAAO,MAAM,CAAC,IAAI,CAAC,SAAS,GAAG,SAAS,EAAE,KAAK,CAAC,CAAA;KACjD;AACH,CAAC;AAWD,SAAgB,MAAM,CAAC,KAAY,EAAE,SAAkB,KAAK;IAC1D,IAAI,CAAC,KAAK,IAAK,KAAa,CAAC,MAAM,KAAK,CAAC,EAAE;QACzC,OAAO,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;KACvB;IAED,MAAM,WAAW,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAA;IACnC,MAAM,OAAO,GAAG,OAAO,CAAC,WAAW,CAAC,CAAA;IAEpC,IAAI,MAAM,EAAE;QACV,OAAO,OAAO,CAAA;KACf;IACD,IAAI,OAAO,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;QAClC,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAA;KACrC;IAED,OAAO,OAAO,CAAC,IAAI,CAAA;AACrB,CAAC;AAhBD,wBAgBC;AAED;;;;GAIG;AACH,SAAgB,SAAS,CAAC,KAAY;IACpC,IAAI,CAAC,KAAK,IAAK,KAAa,CAAC,MAAM,KAAK,CAAC,EAAE;QACzC,OAAO,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;KACvB;IAED,MAAM,WAAW,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAA;IACnC,MAAM,SAAS,GAAG,WAAW,CAAC,CAAC,CAAC,CAAA;IAEhC,IAAI,SAAS,IAAI,IAAI,EAAE;QACrB,OAAO,WAAW,CAAC,MAAM,CAAA;KAC1B;SAAM,IAAI,SAAS,IAAI,IAAI,EAAE;QAC5B,OAAO,SAAS,GAAG,IAAI,CAAA;KACxB;SAAM,IAAI,SAAS,IAAI,IAAI,EAAE;QAC5B,OAAO,SAAS,GAAG,IAAI,CAAA;KACxB;SAAM,IAAI,SAAS,IAAI,IAAI,EAAE;QAC5B,kCAAkC;QAClC,OAAO,SAAS,GAAG,IAAI,CAAA;KACxB;SAAM;QACL,6BAA6B;QAC7B,MAAM,OAAO,GAAG,SAAS,GAAG,IAAI,CAAA;QAChC,MAAM,MAAM,GAAG,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,CAAA;QAC9E,OAAO,OAAO,GAAG,MAAM,CAAA;KACxB;AACH,CAAC;AAvBD,8BAuBC;AAED,+BAA+B;AAC/B,SAAS,OAAO,CAAC,KAAa;IAC5B,IAAI,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,cAAc,EAAE,CAAC,CAAA;IAC5C,MAAM,OAAO,GAAG,EAAE,CAAA;IAClB,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;IAE1B,IAAI,SAAS,IAAI,IAAI,EAAE;QACrB,6FAA6F;QAC7F,OAAO;YACL,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;YACvB,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;SAC1B,CAAA;KACF;SAAM,IAAI,SAAS,IAAI,IAAI,EAAE;QAC5B,gHAAgH;QAChH,8CAA8C;QAC9C,MAAM,GAAG,SAAS,GAAG,IAAI,CAAA;QAEzB,qBAAqB;QACrB,IAAI,SAAS,KAAK,IAAI,EAAE;YACtB,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;SACvB;aAAM;YACL,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,CAAA;SAC9B;QAED,IAAI,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,EAAE;YAClC,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAA;SAChE;QAED,OAAO;YACL,IAAI,EAAE,IAAI;YACV,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC;SAC/B,CAAA;KACF;SAAM,IAAI,SAAS,IAAI,IAAI,EAAE;QAC5B,2GAA2G;QAC3G,iDAAiD;QACjD,OAAO,GAAG,SAAS,GAAG,IAAI,CAAA;QAC1B,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,OAAO,EAAE;YAC9B,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAA;SACnE;QACD,MAAM,GAAG,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,CAAA;QAClE,IAAI,MAAM,IAAI,EAAE,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAA;SAC7E;QACD,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,CAAA;QAC7C,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,EAAE;YACxB,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAA;SAC5D;QAED,OAAO;YACL,IAAI,EAAE,IAAI;YACV,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC;SACzC,CAAA;KACF;SAAM,IAAI,SAAS,IAAI,IAAI,EAAE;QAC5B,kCAAkC;QAClC,MAAM,GAAG,SAAS,GAAG,IAAI,CAAA;QACzB,cAAc,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,CAAA;QACvC,OAAO,cAAc,CAAC,MAAM,EAAE;YAC5B,CAAC,GAAG,OAAO,CAAC,cAAc,CAAC,CAAA;YAC3B,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,IAAc,CAAC,CAAA;YAC9B,cAAc,GAAG,CAAC,CAAC,SAAS,CAAA;SAC7B;QAED,OAAO;YACL,IAAI,EAAE,OAAO;YACb,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC;SAC/B,CAAA;KACF;SAAM;QACL,6BAA6B;QAC7B,OAAO,GAAG,SAAS,GAAG,IAAI,CAAA;QAC1B,MAAM,GAAG,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,CAAA;QAClE,MAAM,WAAW,GAAG,OAAO,GAAG,MAAM,CAAA;QACpC,IAAI,WAAW,GAAG,KAAK,CAAC,MAAM,EAAE;YAC9B,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAA;SACrE;QAED,cAAc,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE,WAAW,CAAC,CAAA;QAClD,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE;YAC/B,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAA;SAC1D;QAED,OAAO,cAAc,CAAC,MAAM,EAAE;YAC5B,CAAC,GAAG,OAAO,CAAC,cAAc,CAAC,CAAA;YAC3B,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,IAAc,CAAC,CAAA;YAC9B,cAAc,GAAG,CAAC,CAAC,SAAS,CAAA;SAC7B;QACD,OAAO;YACL,IAAI,EAAE,OAAO;YACb,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC;SACpC,CAAA;KACF;AACH,CAAC;AAED,0CAA0C;AAC1C,SAAS,aAAa,CAAC,GAAW;IAChC,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI,CAAA;AACjC,CAAC;AAED,qCAAqC;AACrC,SAAS,cAAc,CAAC,GAAW;IACjC,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;QAC3B,OAAO,GAAG,CAAA;KACX;IACD,OAAO,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAA;AAChD,CAAC;AAED,sDAAsD;AACtD,SAAS,QAAQ,CAAC,OAAwB;IACxC,IAAI,OAAO,GAAG,CAAC,EAAE;QACf,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAA;KAClE;IACD,MAAM,GAAG,GAAG,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAA;IAChC,OAAO,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAA;AACzC,CAAC;AAED,8BAA8B;AAC9B,SAAS,SAAS,CAAC,CAAS;IAC1B,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAA;AACnC,CAAC;AAED,yCAAyC;AACzC,SAAS,WAAW,CAAC,OAAwB;IAC3C,MAAM,GAAG,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAA;IAC7B,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;AAChC,CAAC;AAED,uCAAuC;AACvC,SAAS,QAAQ,CAAC,CAAQ;IACxB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;QACvB,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;YACzB,IAAI,aAAa,CAAC,CAAC,CAAC,EAAE;gBACpB,OAAO,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;aACxD;iBAAM;gBACL,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;aACtB;SACF;aAAM,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;YACzD,IAAI,CAAC,CAAC,EAAE;gBACN,OAAO,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;aACvB;iBAAM;gBACL,OAAO,WAAW,CAAC,CAAC,CAAC,CAAA;aACtB;SACF;aAAM,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,SAAS,EAAE;YACxC,OAAO,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;SACvB;aAAM,IAAI,CAAC,YAAY,UAAU,EAAE;YAClC,OAAO,MAAM,CAAC,IAAI,CAAC,CAAQ,CAAC,CAAA;SAC7B;aAAM,IAAI,eAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;YACrB,4BAA4B;YAC5B,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAA;SAChC;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAA;SAChC;KACF;IACD,OAAO,CAAC,CAAA;AACV,CAAC"}