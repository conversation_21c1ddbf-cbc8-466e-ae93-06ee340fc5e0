import { LENGTH_1, <PERSON><PERSON><PERSON>TH_32, LENGTH_16, <PERSON><PERSON><PERSON>TH_256 } from "./length";
export const AES_LENGTH = LENGTH_256;
export const HMAC_LENGTH = LENGTH_256;
export const AES_BROWSER_ALGO = "AES-CBC";
export const HMAC_BROWSER_ALGO = `SHA-${AES_LENGTH}`;
export const HMAC_BROWSER = "HMAC";
export const SHA256_BROWSER_ALGO = "SHA-256";
export const SHA512_BROWSER_ALGO = "SHA-512";
export const AES_NODE_ALGO = `aes-${AES_LENGTH}-cbc`;
export const HMAC_NODE_ALGO = `sha${HMAC_LENGTH}`;
export const SHA256_NODE_ALGO = "sha256";
export const SHA512_NODE_ALGO = "sha512";
export const RIPEMD160_NODE_ALGO = "ripemd160";
export const PREFIX_LENGTH = LENGTH_1;
export const KEY_LENGTH = LENGTH_32;
export const IV_LENGTH = LENGTH_16;
export const MAC_LENGTH = LENGTH_32;
//# sourceMappingURL=default.js.map