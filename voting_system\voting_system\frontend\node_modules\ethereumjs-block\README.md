# SYNOPSIS  
[![NPM Package](https://img.shields.io/npm/v/ethereumjs-block.svg?style=flat-square)](https://www.npmjs.org/package/ethereumjs-block)
[![Build Status](https://img.shields.io/travis/ethereumjs/ethereumjs-block.svg?branch=master&style=flat-square)](https://travis-ci.org/ethereumjs/ethereumjs-block)
[![Coverage Status](https://img.shields.io/coveralls/ethereumjs/ethereumjs-block.svg?style=flat-square)](https://coveralls.io/r/ethereumjs/ethereumjs-block)
[![Gitter](https://img.shields.io/gitter/room/ethereum/ethereumjs-lib.svg?style=flat-square)]() or #ethereumjs on freenode  

[![js-standard-style](https://cdn.rawgit.com/feross/standard/master/badge.svg)](https://github.com/feross/standard)  


Implements schema and functions related to Ethereum's block. 

# INSTALL
`npm install ethereumjs-block`

# BROWSER  
This module work with `browserify`.

# API
[./docs](./docs/index.md)

# TESTING
Tests in the ``tests`` directory are partly outdated and testing is primarily done by running the ``BlockchainTests`` from within the [ethereumjs-vm](https://github.com/ethereumjs/ethereumjs-vm) repository.

Relevant test folders:
- ``bcTotalDifficultyTest``
- TODO

# LICENSE
[MPL-2.0](https://tldrlegal.com/license/mozilla-public-license-2.0-(mpl-2))
