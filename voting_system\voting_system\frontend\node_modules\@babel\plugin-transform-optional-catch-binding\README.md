# @babel/plugin-transform-optional-catch-binding

> Compile optional catch bindings

See our website [@babel/plugin-transform-optional-catch-binding](https://babeljs.io/docs/babel-plugin-transform-optional-catch-binding) for more information.

## Install

Using npm:

```sh
npm install --save-dev @babel/plugin-transform-optional-catch-binding
```

or using yarn:

```sh
yarn add @babel/plugin-transform-optional-catch-binding --dev
```
