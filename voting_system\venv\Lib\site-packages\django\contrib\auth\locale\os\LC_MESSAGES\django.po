# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2013
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-24 13:46+0200\n"
"PO-Revision-Date: 2017-09-24 14:24+0000\n"
"Last-Translator: <PERSON><PERSON> <jann<PERSON>@leidel.info>\n"
"Language-Team: Ossetic (http://www.transifex.com/django/django/language/"
"os/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: os\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Personal info"
msgstr "Персоналон инфо"

msgid "Permissions"
msgstr "Бартӕ"

msgid "Important dates"
msgstr "Ахсджиаг бонтӕ"

#, python-format
msgid "%(name)s object with primary key %(key)r does not exist."
msgstr ""

msgid "Password changed successfully."
msgstr "Пароль ивд ӕрцыд."

#, python-format
msgid "Change password: %s"
msgstr "Фӕив пароль: %s"

msgid "Authentication and Authorization"
msgstr ""

msgid "password"
msgstr "пароль"

msgid "last login"
msgstr "фӕстаг бахызт"

msgid "No password set."
msgstr "Ницы пароль уыд ӕвӕрд."

msgid "Invalid password format or unknown hashing algorithm."
msgstr "Паролы формат раст нӕу, кӕнӕ хӕшты алгоритм бӕрӕг нӕу."

msgid "The two password fields didn't match."
msgstr "Дыууӕ паролы ӕмхуызӕн не сты."

msgid "Password"
msgstr "Пароль"

msgid "Password confirmation"
msgstr "Паролы бӕлвырдгӕнӕн"

msgid "Enter the same password as before, for verification."
msgstr ""

msgid ""
"Raw passwords are not stored, so there is no way to see this user's "
"password, but you can change the password using <a href=\"{}\">this form</a>."
msgstr ""

#, python-format
msgid ""
"Please enter a correct %(username)s and password. Note that both fields may "
"be case-sensitive."
msgstr ""
"Дӕ хорзӕхӕй, раст %(username)s ӕмӕ пароль бафысс. Дӕ сӕры дар уый, ӕмӕ дыууӕ "
"дӕр гӕнӕн ис стыр ӕмӕ гыццыл дамгъӕтӕ ӕвзарой."

msgid "This account is inactive."
msgstr "Ацы аккаунт ницы архайы."

msgid "Email"
msgstr "Электрон пост"

msgid "New password"
msgstr "Ног пароль"

msgid "New password confirmation"
msgstr "Нӕуӕг паролы бӕлвырдгӕнӕн"

msgid "Your old password was entered incorrectly. Please enter it again."
msgstr "Дӕ зӕронд пароль раст амынд нӕу. Дӕ хорзӕхӕй, нӕуӕгӕй йӕ бафысс."

msgid "Old password"
msgstr "Зӕронд пароль"

msgid "Password (again)"
msgstr "Пароль (ногӕй)"

msgid "algorithm"
msgstr "алгоритм"

msgid "iterations"
msgstr "итерацитӕ"

msgid "salt"
msgstr "цӕхх"

msgid "hash"
msgstr "хӕш"

msgid "variety"
msgstr ""

msgid "version"
msgstr ""

msgid "memory cost"
msgstr ""

msgid "time cost"
msgstr ""

msgid "parallelism"
msgstr ""

msgid "work factor"
msgstr "куысты фактор"

msgid "checksum"
msgstr "бӕлвырдсуммӕ"

msgid "name"
msgstr "ном"

msgid "content type"
msgstr ""

msgid "codename"
msgstr "кодном"

msgid "permission"
msgstr "бар"

msgid "permissions"
msgstr "бартӕ"

msgid "group"
msgstr "къорд"

msgid "groups"
msgstr "къордтӕ"

msgid "superuser status"
msgstr "хистӕр архайӕджы статус"

msgid ""
"Designates that this user has all permissions without explicitly assigning "
"them."
msgstr "Ацы архайӕгӕн алы бар дӕр дӕтты, цӕмӕй сӕ хицӕнӕй дӕттын ма хъӕуа."

msgid ""
"The groups this user belongs to. A user will get all permissions granted to "
"each of their groups."
msgstr ""

msgid "user permissions"
msgstr "архайӕджы бартӕ"

msgid "Specific permissions for this user."
msgstr ""

msgid "username"
msgstr "фӕсномыг"

msgid "Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only."
msgstr ""

msgid "A user with that username already exists."
msgstr "Ахӕм фӕсномыгимӕ архайӕг нырид ис."

msgid "first name"
msgstr "ном"

msgid "last name"
msgstr "мыггаг"

msgid "email address"
msgstr "электрон посты адрис"

msgid "staff status"
msgstr "куысты уавӕр"

msgid "Designates whether the user can log into this admin site."
msgstr "Бӕрӕг кӕны архайӕгӕн йӕ бон у ацы армдарӕн сайтмӕ хизын, ӕви нӕ."

msgid "active"
msgstr "активон"

msgid ""
"Designates whether this user should be treated as active. Unselect this "
"instead of deleting accounts."
msgstr ""
"Бӕрӕг кӕны ацы архайӕгы хъӕуы нымайын активоныл, ӕви нӕ. Йӕ нысан ын сис "
"хафыны бӕсты."

msgid "date joined"
msgstr "баиуы бон"

msgid "user"
msgstr "архайӕг"

msgid "users"
msgstr "архайджытӕ"

#, python-format
msgid ""
"This password is too short. It must contain at least %(min_length)d "
"character."
msgid_plural ""
"This password is too short. It must contain at least %(min_length)d "
"characters."
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "Your password must contain at least %(min_length)d character."
msgid_plural "Your password must contain at least %(min_length)d characters."
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "The password is too similar to the %(verbose_name)s."
msgstr ""

msgid "Your password can't be too similar to your other personal information."
msgstr ""

msgid "This password is too common."
msgstr ""

msgid "Your password can't be a commonly used password."
msgstr ""

msgid "This password is entirely numeric."
msgstr ""

msgid "Your password can't be entirely numeric."
msgstr ""

#, python-format
msgid "Password reset on %(site_name)s"
msgstr "%(site_name)s-ы нӕуӕг пароль ӕвӕрӕн"

msgid ""
"Enter a valid username. This value may contain only English letters, "
"numbers, and @/./+/-/_ characters."
msgstr ""

msgid ""
"Enter a valid username. This value may contain only letters, numbers, and "
"@/./+/-/_ characters."
msgstr ""

msgid "Logged out"
msgstr "Рахизын"

msgid "Password reset"
msgstr ""

msgid "Password reset sent"
msgstr ""

msgid "Enter new password"
msgstr ""

msgid "Password reset unsuccessful"
msgstr ""

msgid "Password reset complete"
msgstr ""

msgid "Password change"
msgstr ""

msgid "Password change successful"
msgstr ""
