# Generated by Django 5.2 on 2025-05-06 10:56

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='County',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=3, unique=True)),
            ],
            options={
                'verbose_name_plural': 'Counties',
            },
        ),
        migrations.CreateModel(
            name='ElectionType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(choices=[('PRES', 'Presidential'), ('PARL', 'Parliamentary (National Assembly)'), ('SEN', 'Senate'), ('GOV', 'County Governor'), ('WOM', 'Women Representative'), ('MCA', 'Member of County Assembly')], max_length=4, unique=True)),
                ('description', models.TextField()),
            ],
        ),
        migrations.CreateModel(
            name='Party',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('abbreviation', models.CharField(max_length=10)),
                ('symbol', models.ImageField(blank=True, null=True, upload_to='party_symbols/')),
                ('description', models.TextField()),
                ('registration_number', models.CharField(max_length=50, unique=True)),
            ],
            options={
                'verbose_name_plural': 'Parties',
            },
        ),
        migrations.CreateModel(
            name='Constituency',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=5, unique=True)),
                ('county', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='constituencies', to='voting.county')),
            ],
            options={
                'verbose_name_plural': 'Constituencies',
            },
        ),
        migrations.CreateModel(
            name='Election',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('start_date', models.DateTimeField()),
                ('end_date', models.DateTimeField()),
                ('active', models.BooleanField(default=False)),
                ('contract_election_id', models.IntegerField(blank=True, null=True)),
                ('election_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='voting.electiontype')),
            ],
        ),
        migrations.CreateModel(
            name='Candidate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('bio', models.TextField()),
                ('photo', models.ImageField(blank=True, null=True, upload_to='candidate_photos/')),
                ('is_independent', models.BooleanField(default=False)),
                ('is_approved', models.BooleanField(default=False)),
                ('votes_count', models.IntegerField(default=0)),
                ('contract_candidate_id', models.IntegerField(blank=True, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('constituency', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='voting.constituency')),
                ('county', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='voting.county')),
                ('election', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='voting.election')),
                ('party', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='voting.party')),
            ],
        ),
        migrations.CreateModel(
            name='PollingStation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=10, unique=True)),
                ('location', models.CharField(max_length=255)),
                ('constituency', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='polling_stations', to='voting.constituency')),
            ],
        ),
        migrations.CreateModel(
            name='Voter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('id_number', models.CharField(max_length=8, unique=True, validators=[django.core.validators.RegexValidator('^\\d{8}$', 'ID number must be 8 digits')])),
                ('date_of_birth', models.DateField()),
                ('phone_number', models.CharField(max_length=12, validators=[django.core.validators.RegexValidator('^\\+254\\d{9}$', 'Phone number must be in format +254XXXXXXXXX')])),
                ('is_verified', models.BooleanField(default=False)),
                ('registration_date', models.DateTimeField(auto_now_add=True)),
                ('constituency', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='voting.constituency')),
                ('polling_station', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='voting.pollingstation')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Vote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('transaction_hash', models.CharField(blank=True, max_length=255, null=True)),
                ('candidate', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='voting.candidate')),
                ('election', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='voting.election')),
                ('polling_station', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='voting.pollingstation')),
                ('voter', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='voting.voter')),
            ],
            options={
                'unique_together': {('voter', 'election')},
            },
        ),
    ]
