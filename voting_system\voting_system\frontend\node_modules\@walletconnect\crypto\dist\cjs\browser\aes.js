"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.aesCbcDecrypt = exports.aesCbcEncrypt = void 0;
const browser_1 = require("../lib/browser");
function aesCbcEncrypt(iv, key, data) {
    return (0, browser_1.browserAesEncrypt)(iv, key, data);
}
exports.aesCbcEncrypt = aesCbcEncrypt;
function aesCbcDecrypt(iv, key, data) {
    return (0, browser_1.browserAesDecrypt)(iv, key, data);
}
exports.aesCbcDecrypt = aesCbcDecrypt;
//# sourceMappingURL=aes.js.map