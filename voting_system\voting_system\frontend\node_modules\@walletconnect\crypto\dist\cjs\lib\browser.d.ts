export declare function getAlgo(type: string): AesKeyAlgorithm | HmacImportParams;
export declare function getOps(type: string): string[];
export declare function browserExportKey(cryptoKey: CryptoKey, _type?: string): Promise<Uint8Array>;
export declare function browserImportKey(buffer: Uint8Array, type?: string): Promise<CryptoKey>;
export declare function browserAesEncrypt(iv: Uint8Array, key: Uint8Array, data: Uint8Array): Promise<Uint8Array>;
export declare function browserAesDecrypt(iv: Uint8Array, key: Uint8Array, data: Uint8Array): Promise<Uint8Array>;
export declare function browserHmacSha256Sign(key: Uint8Array, data: Uint8Array): Promise<Uint8Array>;
export declare function browserHmacSha512Sign(key: Uint8Array, data: Uint8Array): Promise<Uint8Array>;
export declare function browserSha256(data: Uint8Array): Promise<Uint8Array>;
export declare function browserSha512(data: Uint8Array): Promise<Uint8Array>;
//# sourceMappingURL=browser.d.ts.map