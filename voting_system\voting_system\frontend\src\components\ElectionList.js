import React from 'react';

const ElectionList = ({ elections, onSelect }) => {
    // Make sure elections is an array
    const electionsArray = Array.isArray(elections) ? elections : [];

    // Separate active and past elections
    const activeElections = electionsArray.filter(e => e.active);
    const pastElections = electionsArray.filter(e => !e.active);

    return (
        <div>
            <div className="d-flex justify-content-between align-items-center mb-4">
                <h2>Active Elections</h2>
            </div>

            {activeElections.length > 0 ? (
                <div className="row">
                    {activeElections.map(election => (
                        <div className="col-md-4" key={election.id}>
                            <div className="card election-card mb-4">
                                <div className="card-body">
                                    <h5 className="card-title">{election.title}</h5>
                                    <p className="card-text">
                                        {election.description.length > 100
                                            ? `${election.description.substring(0, 100)}...`
                                            : election.description}
                                    </p>
                                    <div className="d-flex justify-content-between align-items-center">
                                        <span className="badge bg-success">Active</span>
                                        <span className="text-muted">{election.election_type.name}</span>
                                    </div>
                                </div>
                                <div className="card-footer">
                                    <small className="text-muted">
                                        Ends: {new Date(election.end_date).toLocaleDateString()}
                                    </small>
                                    <button
                                        className="btn btn-primary btn-sm float-end"
                                        onClick={() => onSelect(election)}
                                    >
                                        View Details
                                    </button>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            ) : (
                <div className="alert alert-info">
                    No active elections at the moment.
                </div>
            )}

            <h2 className="mt-5 mb-4">Past Elections</h2>

            {pastElections.length > 0 ? (
                <div className="row">
                    {pastElections.map(election => (
                        <div className="col-md-4" key={election.id}>
                            <div className="card election-card mb-4">
                                <div className="card-body">
                                    <h5 className="card-title">{election.title}</h5>
                                    <p className="card-text">
                                        {election.description.length > 100
                                            ? `${election.description.substring(0, 100)}...`
                                            : election.description}
                                    </p>
                                    <div className="d-flex justify-content-between align-items-center">
                                        <span className="badge bg-secondary">Closed</span>
                                        <span className="text-muted">{election.election_type.name}</span>
                                    </div>
                                </div>
                                <div className="card-footer">
                                    <small className="text-muted">
                                        Ended: {new Date(election.end_date).toLocaleDateString()}
                                    </small>
                                    <button
                                        className="btn btn-outline-secondary btn-sm float-end"
                                        onClick={() => onSelect(election)}
                                    >
                                        View Results
                                    </button>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            ) : (
                <div className="alert alert-info">
                    No past elections.
                </div>
            )}
        </div>
    );
};

export default ElectionList;
