!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define("Connector",[],e):"object"==typeof exports?exports.Connector=e():t.Connector=e()}(this,(function(){return function(t){var e={};function r(n){if(e[n])return e[n].exports;var i=e[n]={i:n,l:!1,exports:{}};return t[n].call(i.exports,i,i.exports,r),i.l=!0,i.exports}return r.m=t,r.c=e,r.d=function(t,e,n){r.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)r.d(n,i,function(e){return t[e]}.bind(null,i));return n},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="",r(r.s=29)}([function(t,e,r){"use strict";(function(t){r.d(e,"f",(function(){return u})),r.d(e,"g",(function(){return a})),r.d(e,"i",(function(){return h})),r.d(e,"h",(function(){return c})),r.d(e,"b",(function(){return f})),r.d(e,"c",(function(){return l})),r.d(e,"e",(function(){return d})),r.d(e,"d",(function(){return p})),r.d(e,"n",(function(){return m})),r.d(e,"m",(function(){return g})),r.d(e,"o",(function(){return v})),r.d(e,"z",(function(){return y})),r.d(e,"y",(function(){return w})),r.d(e,"A",(function(){return _})),r.d(e,"u",(function(){return M})),r.d(e,"t",(function(){return b})),r.d(e,"r",(function(){return E})),r.d(e,"q",(function(){return O})),r.d(e,"s",(function(){return I})),r.d(e,"p",(function(){return k})),r.d(e,"l",(function(){return A})),r.d(e,"k",(function(){return T})),r.d(e,"j",(function(){return N})),r.d(e,"w",(function(){return B})),r.d(e,"a",(function(){return P})),r.d(e,"x",(function(){return L})),r.d(e,"v",(function(){return j}));var n=r(10),i=r.n(n),o=r(25),s=r.n(o);function u(t){return new Uint8Array(t)}function a(t,e=!1){const r=t.toString("hex");return e?P(r):r}function h(t){return t.toString("utf8")}function c(t){return t.readUIntBE(0,t.length)}function f(t){return s()(t)}function l(t,e=!1){return a(f(t),e)}function d(t){return h(f(t))}function p(t){return c(f(t))}function m(e){return t.from(B(e),"hex")}function g(t){return u(m(t))}function v(t){return h(m(t))}function y(e){return t.from(e,"utf8")}function w(t){return u(y(t))}function _(t,e=!1){return a(y(t),e)}function M(t){return f(S(R(t)))}function b(t){return S(R(t))}function R(t){return x((t>>>0).toString(2))}function S(t){return new Uint8Array(C(t).map(t=>parseInt(t,2)))}function E(t,e){return!("string"!=typeof t||!t.match(/^0x[0-9A-Fa-f]*$/))&&(!e||t.length===2+2*e)}function O(e){return t.isBuffer(e)}function I(t){return i.a.strict(t)&&!O(t)}function k(t){return!I(t)&&!O(t)&&void 0!==t.byteLength}function A(t){return O(t)?"buffer":I(t)?"typed-array":k(t)?"array-buffer":Array.isArray(t)?"array":typeof t}function T(t){return function(t){return!("string"!=typeof t||!new RegExp(/^[01]+$/).test(t))&&t.length%8==0}(t)?"binary":E(t)?"hex":"utf8"}function N(...e){return t.concat(e)}function C(t,e=8){const r=x(t).match(new RegExp(`.{${e}}`,"gi"));return Array.from(r||[])}function x(t,e=8,r="0"){return function(t,e,r="0"){return U(t,e,!0,r)}(t,function(t,e=8){const r=t%e;return r?(t-r)/e*e+e:t}(t.length,e),r)}function B(t){return t.replace(/^0x/,"")}function P(t){return t.startsWith("0x")?t:"0x"+t}function L(t){return(t=x(t=B(t),2))&&(t=P(t)),t}function j(t){const e=t.startsWith("0x");return t=(t=B(t)).startsWith("0")?t.substring(1):t,e?P(t):t}function U(t,e,r,n="0"){const i=e-t.length;let o=t;if(i>0){const e=n.repeat(i);o=r?e+t:t+e}return o}}).call(this,r(12).Buffer)},function(t,e,r){"use strict";r.d(e,"b",(function(){return n})),r.d(e,"d",(function(){return i})),r.d(e,"c",(function(){return o})),r.d(e,"e",(function(){return s})),r.d(e,"f",(function(){return u})),r.d(e,"a",(function(){return a}));const n="INTERNAL_ERROR",i="SERVER_ERROR",o=[-32700,-32600,-32601,-32602,-32603],s=[-32e3,-32099],u={PARSE_ERROR:{code:-32700,message:"Parse error"},INVALID_REQUEST:{code:-32600,message:"Invalid Request"},METHOD_NOT_FOUND:{code:-32601,message:"Method not found"},INVALID_PARAMS:{code:-32602,message:"Invalid params"},[n]:{code:-32603,message:"Internal error"},[i]:{code:-32e3,message:"Server error"}},a=i},function(t,e,r){"use strict";function n(t){let e=void 0;return"undefined"!=typeof window&&void 0!==window[t]&&(e=window[t]),e}function i(t){const e=n(t);if(!e)throw new Error(t+" is not defined in Window");return e}Object.defineProperty(e,"__esModule",{value:!0}),e.getLocalStorage=e.getLocalStorageOrThrow=e.getCrypto=e.getCryptoOrThrow=e.getLocation=e.getLocationOrThrow=e.getNavigator=e.getNavigatorOrThrow=e.getDocument=e.getDocumentOrThrow=e.getFromWindowOrThrow=e.getFromWindow=void 0,e.getFromWindow=n,e.getFromWindowOrThrow=i,e.getDocumentOrThrow=function(){return i("document")},e.getDocument=function(){return n("document")},e.getNavigatorOrThrow=function(){return i("navigator")},e.getNavigator=function(){return n("navigator")},e.getLocationOrThrow=function(){return i("location")},e.getLocation=function(){return n("location")},e.getCryptoOrThrow=function(){return i("crypto")},e.getCrypto=function(){return n("crypto")},e.getLocalStorageOrThrow=function(){return i("localStorage")},e.getLocalStorage=function(){return n("localStorage")}},function(t,e,r){"use strict";r.r(e);var n=r(23),i=r(2),o=r(24);function s(t){return Object(o.a)(t)}function u(){const t=s();return t&&t.os?t.os:void 0}function a(){const t=u();return!!t&&t.toLowerCase().includes("android")}function h(){const t=u();return!!t&&(t.toLowerCase().includes("ios")||t.toLowerCase().includes("mac")&&navigator.maxTouchPoints>1)}function c(){return!!u()&&(a()||h())}function f(){const t=s();return!(!t||!t.name)&&"node"===t.name.toLowerCase()}function l(){return!f()&&!!y()}const d=i.getFromWindow,p=i.getFromWindowOrThrow,m=i.getDocumentOrThrow,g=i.getDocument,v=i.getNavigatorOrThrow,y=i.getNavigator,w=i.getLocationOrThrow,_=i.getLocation,M=i.getCryptoOrThrow,b=i.getCrypto,R=i.getLocalStorageOrThrow,S=i.getLocalStorage;function E(){return n.getWindowMetadata()}const O=function(t){if("string"!=typeof t)throw new Error("Cannot safe json parse value of type "+typeof t);try{return JSON.parse(t)}catch(e){return t}},I=function(t){return"string"==typeof t?t:JSON.stringify(t)};function k(t,e){const r=I(e),n=S();n&&n.setItem(t,r)}function A(t){let e=null,r=null;const n=S();return n&&(r=n.getItem(t)),e=r?O(r):r,e}function T(t){const e=S();e&&e.removeItem(t)}function N(t,e){const r=encodeURIComponent(t);return e.universalLink?`${e.universalLink}/wc?uri=${r}`:e.deepLink?`${e.deepLink}${e.deepLink.endsWith(":")?"//":"/"}wc?uri=${r}`:""}function C(t){const e=t.href.split("?")[0];k("WALLETCONNECT_DEEPLINK_CHOICE",Object.assign(Object.assign({},t),{href:e}))}function x(t,e){return t.filter(t=>t.name.toLowerCase().includes(e.toLowerCase()))[0]}function B(t,e){let r=t;return e&&(r=e.map(e=>x(t,e)).filter(Boolean)),r}const P="https://registry.walletconnect.com";function L(){return P+"/api/v2/wallets"}function j(){return P+"/api/v2/dapps"}function U(t,e="mobile"){var r;return{name:t.name||"",shortName:t.metadata.shortName||"",color:t.metadata.colors.primary||"",logo:null!==(r=t.image_url.sm)&&void 0!==r?r:"",universalLink:t[e].universal||"",deepLink:t[e].native||""}}function D(t,e="mobile"){return Object.values(t).filter(t=>!!t[e].universal||!!t[e].native).map(t=>U(t,e))}const q=["session_request","session_update","exchange_key","connect","disconnect","display_uri","modal_closed","transport_open","transport_close","transport_error"],W=["eth_sendTransaction","eth_signTransaction","eth_sign","eth_signTypedData","eth_signTypedData_v1","eth_signTypedData_v2","eth_signTypedData_v3","eth_signTypedData_v4","personal_sign","wallet_addEthereumChain","wallet_switchEthereumChain","wallet_getPermissions","wallet_requestPermissions","wallet_registerOnboarding","wallet_watchAsset","wallet_scanQRCode"],F=["eth_accounts","eth_chainId","net_version"],Y={1:"mainnet",3:"ropsten",4:"rinkeby",5:"goerli",42:"kovan"};var J=r(6),$=r.n(J),z=r(0);function H(t){return z.b(new Uint8Array(t))}function V(t){return z.e(new Uint8Array(t))}function Z(t,e){return z.c(new Uint8Array(t),!e)}function Q(t){return z.d(new Uint8Array(t))}function G(...t){return z.m(t.map(t=>z.c(new Uint8Array(t))).join("")).buffer}function K(t){return z.f(t).buffer}function X(t){return z.i(t)}function tt(t,e){return z.g(t,!e)}function et(t){return z.h(t)}function rt(...t){return z.j(...t)}function nt(t){return z.y(t).buffer}function it(t){return z.z(t)}function ot(t,e){return z.A(t,!e)}function st(t){return new $.a(t,10).toNumber()}function ut(t){return z.n(t)}function at(t){return z.m(t).buffer}function ht(t){return z.o(t)}function ct(t){return new $.a(z.w(t),"hex").toNumber()}function ft(t){return z.u(t)}function lt(t){return z.t(t).buffer}function dt(t){return new $.a(t).toString()}function pt(t,e){const r=z.w(z.x(new $.a(t).toString(16)));return e?r:z.a(r)}var mt=r(26);function gt(t){return z.x(t)}function vt(t){return z.a(t)}function yt(t){return z.w(t)}function wt(t){return z.v(z.a(t))}const _t=r(27).payloadId;function Mt(){return((t,e)=>{for(e=t="";t++<36;e+=51*t&52?(15^t?8^Math.random()*(20^t?16:4):4).toString(16):"-");return e})()}function bt(){console.warn("DEPRECATION WARNING: This WalletConnect client library will be deprecated in favor of @walletconnect/client. Please check docs.walletconnect.org to learn more about this migration!")}function Rt(t,e){let r;const n=Y[t];return n&&(r=`https://${n}.infura.io/v3/${e}`),r}function St(t,e){let r;const n=Rt(t,e.infuraId);return e.custom&&e.custom[t]?r=e.custom[t]:n&&(r=n),r}function Et(t){return""===t||"string"==typeof t&&""===t.trim()}function Ot(t){return!(t&&t.length)}function It(t){return z.q(t)}function kt(t){return z.s(t)}function At(t){return z.p(t)}function Tt(t){return z.l(t)}function Nt(t){return z.k(t)}function Ct(t,e){return z.r(t,e)}function xt(t){return"object"==typeof t.params}function Bt(t){return void 0!==t.method}function Pt(t){return void 0!==t.result}function Lt(t){return void 0!==t.error}function jt(t){return void 0!==t.event}function Ut(t){return q.includes(t)||t.startsWith("wc_")}function Dt(t){return!!t.method.startsWith("wc_")||!W.includes(t.method)}function qt(t){t=Object(z.w)(t.toLowerCase());const e=Object(z.w)(Object(mt.keccak_256)(it(t)));let r="";for(let n=0;n<t.length;n++)parseInt(e[n],16)>7?r+=t[n].toUpperCase():r+=t[n];return Object(z.a)(r)}const Wt=t=>!!t&&("0x"===t.toLowerCase().substring(0,2)&&(!!/^(0x)?[0-9a-f]{40}$/i.test(t)&&(!(!/^(0x)?[0-9a-f]{40}$/.test(t)&&!/^(0x)?[0-9A-F]{40}$/.test(t))||t===qt(t))));function Ft(t){return Ot(t)||Ct(t[0])||(t[0]=ot(t[0])),t}function Yt(t){if(void 0!==t.type&&"0"!==t.type)return t;if(void 0===t.from||!Wt(t.from))throw new Error("Transaction object must include a valid 'from' value.");function e(t){let e=t;return("number"==typeof t||"string"==typeof t&&!Et(t))&&(Ct(t)?"string"==typeof t&&(e=gt(t)):e=pt(t)),"string"==typeof e&&(e=wt(e)),e}const r={from:gt(t.from),to:void 0===t.to?void 0:gt(t.to),gasPrice:void 0===t.gasPrice?"":e(t.gasPrice),gas:void 0===t.gas?void 0===t.gasLimit?"":e(t.gasLimit):e(t.gas),value:void 0===t.value?"":e(t.value),nonce:void 0===t.nonce?"":e(t.nonce),data:void 0===t.data?"":gt(t.data)||"0x"},n=["gasPrice","gas","value","nonce"];return Object.keys(r).forEach(t=>{(void 0===r[t]||"string"==typeof r[t]&&!r[t].trim().length)&&n.includes(t)&&delete r[t]}),r}function Jt(t,e){return async(...r)=>new Promise((n,i)=>{t.apply(e,[...r,(t,e)=>{null==t&&i(t),n(e)}])})}function $t(t){const e=t.message||"Failed or Rejected Request";let r=-32e3;if(t&&!t.code)switch(e){case"Parse error":r=-32700;break;case"Invalid request":r=-32600;break;case"Method not found":r=-32601;break;case"Invalid params":r=-32602;break;case"Internal error":r=-32603;break;default:r=-32e3}const n={code:r,message:e};return t.data&&(n.data=t.data),n}var zt=r(11);function Ht(t){const e=-1!==t.indexOf("?")?t.indexOf("?"):void 0;return void 0!==e?t.substr(e):""}function Vt(t,e){let r=Zt(t);return r=Object.assign(Object.assign({},r),e),t=Qt(r)}function Zt(t){return zt.parse(t)}function Qt(t){return zt.stringify(t)}function Gt(t){return void 0!==t.bridge}function Kt(t){const e=t.indexOf(":"),r=-1!==t.indexOf("?")?t.indexOf("?"):void 0,n=t.substring(0,e);const i=function(t){const e=t.split("@");return{handshakeTopic:e[0],version:parseInt(e[1],10)}}(t.substring(e+1,r));const o=function(t){const e=Zt(t);return{key:e.key||"",bridge:e.bridge||""}}(void 0!==r?t.substr(r):"");return Object.assign(Object.assign({protocol:n},i),o)}r.d(e,"detectEnv",(function(){return s})),r.d(e,"detectOS",(function(){return u})),r.d(e,"isAndroid",(function(){return a})),r.d(e,"isIOS",(function(){return h})),r.d(e,"isMobile",(function(){return c})),r.d(e,"isNode",(function(){return f})),r.d(e,"isBrowser",(function(){return l})),r.d(e,"getFromWindow",(function(){return d})),r.d(e,"getFromWindowOrThrow",(function(){return p})),r.d(e,"getDocumentOrThrow",(function(){return m})),r.d(e,"getDocument",(function(){return g})),r.d(e,"getNavigatorOrThrow",(function(){return v})),r.d(e,"getNavigator",(function(){return y})),r.d(e,"getLocationOrThrow",(function(){return w})),r.d(e,"getLocation",(function(){return _})),r.d(e,"getCryptoOrThrow",(function(){return M})),r.d(e,"getCrypto",(function(){return b})),r.d(e,"getLocalStorageOrThrow",(function(){return R})),r.d(e,"getLocalStorage",(function(){return S})),r.d(e,"getClientMeta",(function(){return E})),r.d(e,"safeJsonParse",(function(){return O})),r.d(e,"safeJsonStringify",(function(){return I})),r.d(e,"setLocal",(function(){return k})),r.d(e,"getLocal",(function(){return A})),r.d(e,"removeLocal",(function(){return T})),r.d(e,"mobileLinkChoiceKey",(function(){return"WALLETCONNECT_DEEPLINK_CHOICE"})),r.d(e,"formatIOSMobile",(function(){return N})),r.d(e,"saveMobileLinkInfo",(function(){return C})),r.d(e,"getMobileRegistryEntry",(function(){return x})),r.d(e,"getMobileLinkRegistry",(function(){return B})),r.d(e,"getWalletRegistryUrl",(function(){return L})),r.d(e,"getDappRegistryUrl",(function(){return j})),r.d(e,"formatMobileRegistryEntry",(function(){return U})),r.d(e,"formatMobileRegistry",(function(){return D})),r.d(e,"reservedEvents",(function(){return q})),r.d(e,"signingMethods",(function(){return W})),r.d(e,"stateMethods",(function(){return F})),r.d(e,"infuraNetworks",(function(){return Y})),r.d(e,"convertArrayBufferToBuffer",(function(){return H})),r.d(e,"convertArrayBufferToUtf8",(function(){return V})),r.d(e,"convertArrayBufferToHex",(function(){return Z})),r.d(e,"convertArrayBufferToNumber",(function(){return Q})),r.d(e,"concatArrayBuffers",(function(){return G})),r.d(e,"convertBufferToArrayBuffer",(function(){return K})),r.d(e,"convertBufferToUtf8",(function(){return X})),r.d(e,"convertBufferToHex",(function(){return tt})),r.d(e,"convertBufferToNumber",(function(){return et})),r.d(e,"concatBuffers",(function(){return rt})),r.d(e,"convertUtf8ToArrayBuffer",(function(){return nt})),r.d(e,"convertUtf8ToBuffer",(function(){return it})),r.d(e,"convertUtf8ToHex",(function(){return ot})),r.d(e,"convertUtf8ToNumber",(function(){return st})),r.d(e,"convertHexToBuffer",(function(){return ut})),r.d(e,"convertHexToArrayBuffer",(function(){return at})),r.d(e,"convertHexToUtf8",(function(){return ht})),r.d(e,"convertHexToNumber",(function(){return ct})),r.d(e,"convertNumberToBuffer",(function(){return ft})),r.d(e,"convertNumberToArrayBuffer",(function(){return lt})),r.d(e,"convertNumberToUtf8",(function(){return dt})),r.d(e,"convertNumberToHex",(function(){return pt})),r.d(e,"toChecksumAddress",(function(){return qt})),r.d(e,"isValidAddress",(function(){return Wt})),r.d(e,"parsePersonalSign",(function(){return Ft})),r.d(e,"parseTransactionData",(function(){return Yt})),r.d(e,"sanitizeHex",(function(){return gt})),r.d(e,"addHexPrefix",(function(){return vt})),r.d(e,"removeHexPrefix",(function(){return yt})),r.d(e,"removeHexLeadingZeros",(function(){return wt})),r.d(e,"payloadId",(function(){return _t})),r.d(e,"uuid",(function(){return Mt})),r.d(e,"logDeprecationWarning",(function(){return bt})),r.d(e,"getInfuraRpcUrl",(function(){return Rt})),r.d(e,"getRpcUrl",(function(){return St})),r.d(e,"promisify",(function(){return Jt})),r.d(e,"formatRpcError",(function(){return $t})),r.d(e,"isWalletConnectSession",(function(){return Gt})),r.d(e,"parseWalletConnectUri",(function(){return Kt})),r.d(e,"getQueryString",(function(){return Ht})),r.d(e,"appendToQueryString",(function(){return Vt})),r.d(e,"parseQueryString",(function(){return Zt})),r.d(e,"formatQueryString",(function(){return Qt})),r.d(e,"isEmptyString",(function(){return Et})),r.d(e,"isEmptyArray",(function(){return Ot})),r.d(e,"isBuffer",(function(){return It})),r.d(e,"isTypedArray",(function(){return kt})),r.d(e,"isArrayBuffer",(function(){return At})),r.d(e,"getType",(function(){return Tt})),r.d(e,"getEncoding",(function(){return Nt})),r.d(e,"isHexString",(function(){return Ct})),r.d(e,"isJsonRpcSubscription",(function(){return xt})),r.d(e,"isJsonRpcRequest",(function(){return Bt})),r.d(e,"isJsonRpcResponseSuccess",(function(){return Pt})),r.d(e,"isJsonRpcResponseError",(function(){return Lt})),r.d(e,"isInternalEvent",(function(){return jt})),r.d(e,"isReservedEvent",(function(){return Ut})),r.d(e,"isSilentPayload",(function(){return Dt}))},function(t,e,r){"use strict";r.d(e,"c",(function(){return i})),r.d(e,"a",(function(){return o})),r.d(e,"b",(function(){return s}));var n=r(1);function i(t){return n.c.includes(t)}function o(t){return Object.keys(n.f).includes(t)?n.f[t]:n.f[n.a]}function s(t){const e=Object.values(n.f).find(e=>e.code===t);return e||n.f[n.a]}},function(t,e,r){"use strict";r.d(e,"a",(function(){return n}));class n{}},function(t,e,r){(function(t){!function(t,e){"use strict";function n(t,e){if(!t)throw new Error(e||"Assertion failed")}function i(t,e){t.super_=e;var r=function(){};r.prototype=e.prototype,t.prototype=new r,t.prototype.constructor=t}function o(t,e,r){if(o.isBN(t))return t;this.negative=0,this.words=null,this.length=0,this.red=null,null!==t&&("le"!==e&&"be"!==e||(r=e,e=10),this._init(t||0,e||10,r||"be"))}var s;"object"==typeof t?t.exports=o:e.BN=o,o.BN=o,o.wordSize=26;try{s=r(32).Buffer}catch(t){}function u(t,e,r){for(var n=0,i=Math.min(t.length,r),o=e;o<i;o++){var s=t.charCodeAt(o)-48;n<<=4,n|=s>=49&&s<=54?s-49+10:s>=17&&s<=22?s-17+10:15&s}return n}function a(t,e,r,n){for(var i=0,o=Math.min(t.length,r),s=e;s<o;s++){var u=t.charCodeAt(s)-48;i*=n,i+=u>=49?u-49+10:u>=17?u-17+10:u}return i}o.isBN=function(t){return t instanceof o||null!==t&&"object"==typeof t&&t.constructor.wordSize===o.wordSize&&Array.isArray(t.words)},o.max=function(t,e){return t.cmp(e)>0?t:e},o.min=function(t,e){return t.cmp(e)<0?t:e},o.prototype._init=function(t,e,r){if("number"==typeof t)return this._initNumber(t,e,r);if("object"==typeof t)return this._initArray(t,e,r);"hex"===e&&(e=16),n(e===(0|e)&&e>=2&&e<=36);var i=0;"-"===(t=t.toString().replace(/\s+/g,""))[0]&&i++,16===e?this._parseHex(t,i):this._parseBase(t,e,i),"-"===t[0]&&(this.negative=1),this.strip(),"le"===r&&this._initArray(this.toArray(),e,r)},o.prototype._initNumber=function(t,e,r){t<0&&(this.negative=1,t=-t),t<67108864?(this.words=[67108863&t],this.length=1):t<4503599627370496?(this.words=[67108863&t,t/67108864&67108863],this.length=2):(n(t<9007199254740992),this.words=[67108863&t,t/67108864&67108863,1],this.length=3),"le"===r&&this._initArray(this.toArray(),e,r)},o.prototype._initArray=function(t,e,r){if(n("number"==typeof t.length),t.length<=0)return this.words=[0],this.length=1,this;this.length=Math.ceil(t.length/3),this.words=new Array(this.length);for(var i=0;i<this.length;i++)this.words[i]=0;var o,s,u=0;if("be"===r)for(i=t.length-1,o=0;i>=0;i-=3)s=t[i]|t[i-1]<<8|t[i-2]<<16,this.words[o]|=s<<u&67108863,this.words[o+1]=s>>>26-u&67108863,(u+=24)>=26&&(u-=26,o++);else if("le"===r)for(i=0,o=0;i<t.length;i+=3)s=t[i]|t[i+1]<<8|t[i+2]<<16,this.words[o]|=s<<u&67108863,this.words[o+1]=s>>>26-u&67108863,(u+=24)>=26&&(u-=26,o++);return this.strip()},o.prototype._parseHex=function(t,e){this.length=Math.ceil((t.length-e)/6),this.words=new Array(this.length);for(var r=0;r<this.length;r++)this.words[r]=0;var n,i,o=0;for(r=t.length-6,n=0;r>=e;r-=6)i=u(t,r,r+6),this.words[n]|=i<<o&67108863,this.words[n+1]|=i>>>26-o&4194303,(o+=24)>=26&&(o-=26,n++);r+6!==e&&(i=u(t,e,r+6),this.words[n]|=i<<o&67108863,this.words[n+1]|=i>>>26-o&4194303),this.strip()},o.prototype._parseBase=function(t,e,r){this.words=[0],this.length=1;for(var n=0,i=1;i<=67108863;i*=e)n++;n--,i=i/e|0;for(var o=t.length-r,s=o%n,u=Math.min(o,o-s)+r,h=0,c=r;c<u;c+=n)h=a(t,c,c+n,e),this.imuln(i),this.words[0]+h<67108864?this.words[0]+=h:this._iaddn(h);if(0!==s){var f=1;for(h=a(t,c,t.length,e),c=0;c<s;c++)f*=e;this.imuln(f),this.words[0]+h<67108864?this.words[0]+=h:this._iaddn(h)}},o.prototype.copy=function(t){t.words=new Array(this.length);for(var e=0;e<this.length;e++)t.words[e]=this.words[e];t.length=this.length,t.negative=this.negative,t.red=this.red},o.prototype.clone=function(){var t=new o(null);return this.copy(t),t},o.prototype._expand=function(t){for(;this.length<t;)this.words[this.length++]=0;return this},o.prototype.strip=function(){for(;this.length>1&&0===this.words[this.length-1];)this.length--;return this._normSign()},o.prototype._normSign=function(){return 1===this.length&&0===this.words[0]&&(this.negative=0),this},o.prototype.inspect=function(){return(this.red?"<BN-R: ":"<BN: ")+this.toString(16)+">"};var h=["","0","00","000","0000","00000","000000","0000000","00000000","000000000","0000000000","00000000000","000000000000","0000000000000","00000000000000","000000000000000","0000000000000000","00000000000000000","000000000000000000","0000000000000000000","00000000000000000000","000000000000000000000","0000000000000000000000","00000000000000000000000","000000000000000000000000","0000000000000000000000000"],c=[0,0,25,16,12,11,10,9,8,8,7,7,7,7,6,6,6,6,6,6,6,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5],f=[0,0,33554432,43046721,16777216,48828125,60466176,40353607,16777216,43046721,1e7,19487171,35831808,62748517,7529536,11390625,16777216,24137569,34012224,47045881,64e6,4084101,5153632,6436343,7962624,9765625,11881376,14348907,17210368,20511149,243e5,28629151,33554432,39135393,45435424,52521875,60466176];function l(t,e,r){r.negative=e.negative^t.negative;var n=t.length+e.length|0;r.length=n,n=n-1|0;var i=0|t.words[0],o=0|e.words[0],s=i*o,u=67108863&s,a=s/67108864|0;r.words[0]=u;for(var h=1;h<n;h++){for(var c=a>>>26,f=67108863&a,l=Math.min(h,e.length-1),d=Math.max(0,h-t.length+1);d<=l;d++){var p=h-d|0;c+=(s=(i=0|t.words[p])*(o=0|e.words[d])+f)/67108864|0,f=67108863&s}r.words[h]=0|f,a=0|c}return 0!==a?r.words[h]=0|a:r.length--,r.strip()}o.prototype.toString=function(t,e){var r;if(e=0|e||1,16===(t=t||10)||"hex"===t){r="";for(var i=0,o=0,s=0;s<this.length;s++){var u=this.words[s],a=(16777215&(u<<i|o)).toString(16);r=0!==(o=u>>>24-i&16777215)||s!==this.length-1?h[6-a.length]+a+r:a+r,(i+=2)>=26&&(i-=26,s--)}for(0!==o&&(r=o.toString(16)+r);r.length%e!=0;)r="0"+r;return 0!==this.negative&&(r="-"+r),r}if(t===(0|t)&&t>=2&&t<=36){var l=c[t],d=f[t];r="";var p=this.clone();for(p.negative=0;!p.isZero();){var m=p.modn(d).toString(t);r=(p=p.idivn(d)).isZero()?m+r:h[l-m.length]+m+r}for(this.isZero()&&(r="0"+r);r.length%e!=0;)r="0"+r;return 0!==this.negative&&(r="-"+r),r}n(!1,"Base should be between 2 and 36")},o.prototype.toNumber=function(){var t=this.words[0];return 2===this.length?t+=67108864*this.words[1]:3===this.length&&1===this.words[2]?t+=4503599627370496+67108864*this.words[1]:this.length>2&&n(!1,"Number can only safely store up to 53 bits"),0!==this.negative?-t:t},o.prototype.toJSON=function(){return this.toString(16)},o.prototype.toBuffer=function(t,e){return n(void 0!==s),this.toArrayLike(s,t,e)},o.prototype.toArray=function(t,e){return this.toArrayLike(Array,t,e)},o.prototype.toArrayLike=function(t,e,r){var i=this.byteLength(),o=r||Math.max(1,i);n(i<=o,"byte array longer than desired length"),n(o>0,"Requested array length <= 0"),this.strip();var s,u,a="le"===e,h=new t(o),c=this.clone();if(a){for(u=0;!c.isZero();u++)s=c.andln(255),c.iushrn(8),h[u]=s;for(;u<o;u++)h[u]=0}else{for(u=0;u<o-i;u++)h[u]=0;for(u=0;!c.isZero();u++)s=c.andln(255),c.iushrn(8),h[o-u-1]=s}return h},Math.clz32?o.prototype._countBits=function(t){return 32-Math.clz32(t)}:o.prototype._countBits=function(t){var e=t,r=0;return e>=4096&&(r+=13,e>>>=13),e>=64&&(r+=7,e>>>=7),e>=8&&(r+=4,e>>>=4),e>=2&&(r+=2,e>>>=2),r+e},o.prototype._zeroBits=function(t){if(0===t)return 26;var e=t,r=0;return 0==(8191&e)&&(r+=13,e>>>=13),0==(127&e)&&(r+=7,e>>>=7),0==(15&e)&&(r+=4,e>>>=4),0==(3&e)&&(r+=2,e>>>=2),0==(1&e)&&r++,r},o.prototype.bitLength=function(){var t=this.words[this.length-1],e=this._countBits(t);return 26*(this.length-1)+e},o.prototype.zeroBits=function(){if(this.isZero())return 0;for(var t=0,e=0;e<this.length;e++){var r=this._zeroBits(this.words[e]);if(t+=r,26!==r)break}return t},o.prototype.byteLength=function(){return Math.ceil(this.bitLength()/8)},o.prototype.toTwos=function(t){return 0!==this.negative?this.abs().inotn(t).iaddn(1):this.clone()},o.prototype.fromTwos=function(t){return this.testn(t-1)?this.notn(t).iaddn(1).ineg():this.clone()},o.prototype.isNeg=function(){return 0!==this.negative},o.prototype.neg=function(){return this.clone().ineg()},o.prototype.ineg=function(){return this.isZero()||(this.negative^=1),this},o.prototype.iuor=function(t){for(;this.length<t.length;)this.words[this.length++]=0;for(var e=0;e<t.length;e++)this.words[e]=this.words[e]|t.words[e];return this.strip()},o.prototype.ior=function(t){return n(0==(this.negative|t.negative)),this.iuor(t)},o.prototype.or=function(t){return this.length>t.length?this.clone().ior(t):t.clone().ior(this)},o.prototype.uor=function(t){return this.length>t.length?this.clone().iuor(t):t.clone().iuor(this)},o.prototype.iuand=function(t){var e;e=this.length>t.length?t:this;for(var r=0;r<e.length;r++)this.words[r]=this.words[r]&t.words[r];return this.length=e.length,this.strip()},o.prototype.iand=function(t){return n(0==(this.negative|t.negative)),this.iuand(t)},o.prototype.and=function(t){return this.length>t.length?this.clone().iand(t):t.clone().iand(this)},o.prototype.uand=function(t){return this.length>t.length?this.clone().iuand(t):t.clone().iuand(this)},o.prototype.iuxor=function(t){var e,r;this.length>t.length?(e=this,r=t):(e=t,r=this);for(var n=0;n<r.length;n++)this.words[n]=e.words[n]^r.words[n];if(this!==e)for(;n<e.length;n++)this.words[n]=e.words[n];return this.length=e.length,this.strip()},o.prototype.ixor=function(t){return n(0==(this.negative|t.negative)),this.iuxor(t)},o.prototype.xor=function(t){return this.length>t.length?this.clone().ixor(t):t.clone().ixor(this)},o.prototype.uxor=function(t){return this.length>t.length?this.clone().iuxor(t):t.clone().iuxor(this)},o.prototype.inotn=function(t){n("number"==typeof t&&t>=0);var e=0|Math.ceil(t/26),r=t%26;this._expand(e),r>0&&e--;for(var i=0;i<e;i++)this.words[i]=67108863&~this.words[i];return r>0&&(this.words[i]=~this.words[i]&67108863>>26-r),this.strip()},o.prototype.notn=function(t){return this.clone().inotn(t)},o.prototype.setn=function(t,e){n("number"==typeof t&&t>=0);var r=t/26|0,i=t%26;return this._expand(r+1),this.words[r]=e?this.words[r]|1<<i:this.words[r]&~(1<<i),this.strip()},o.prototype.iadd=function(t){var e,r,n;if(0!==this.negative&&0===t.negative)return this.negative=0,e=this.isub(t),this.negative^=1,this._normSign();if(0===this.negative&&0!==t.negative)return t.negative=0,e=this.isub(t),t.negative=1,e._normSign();this.length>t.length?(r=this,n=t):(r=t,n=this);for(var i=0,o=0;o<n.length;o++)e=(0|r.words[o])+(0|n.words[o])+i,this.words[o]=67108863&e,i=e>>>26;for(;0!==i&&o<r.length;o++)e=(0|r.words[o])+i,this.words[o]=67108863&e,i=e>>>26;if(this.length=r.length,0!==i)this.words[this.length]=i,this.length++;else if(r!==this)for(;o<r.length;o++)this.words[o]=r.words[o];return this},o.prototype.add=function(t){var e;return 0!==t.negative&&0===this.negative?(t.negative=0,e=this.sub(t),t.negative^=1,e):0===t.negative&&0!==this.negative?(this.negative=0,e=t.sub(this),this.negative=1,e):this.length>t.length?this.clone().iadd(t):t.clone().iadd(this)},o.prototype.isub=function(t){if(0!==t.negative){t.negative=0;var e=this.iadd(t);return t.negative=1,e._normSign()}if(0!==this.negative)return this.negative=0,this.iadd(t),this.negative=1,this._normSign();var r,n,i=this.cmp(t);if(0===i)return this.negative=0,this.length=1,this.words[0]=0,this;i>0?(r=this,n=t):(r=t,n=this);for(var o=0,s=0;s<n.length;s++)o=(e=(0|r.words[s])-(0|n.words[s])+o)>>26,this.words[s]=67108863&e;for(;0!==o&&s<r.length;s++)o=(e=(0|r.words[s])+o)>>26,this.words[s]=67108863&e;if(0===o&&s<r.length&&r!==this)for(;s<r.length;s++)this.words[s]=r.words[s];return this.length=Math.max(this.length,s),r!==this&&(this.negative=1),this.strip()},o.prototype.sub=function(t){return this.clone().isub(t)};var d=function(t,e,r){var n,i,o,s=t.words,u=e.words,a=r.words,h=0,c=0|s[0],f=8191&c,l=c>>>13,d=0|s[1],p=8191&d,m=d>>>13,g=0|s[2],v=8191&g,y=g>>>13,w=0|s[3],_=8191&w,M=w>>>13,b=0|s[4],R=8191&b,S=b>>>13,E=0|s[5],O=8191&E,I=E>>>13,k=0|s[6],A=8191&k,T=k>>>13,N=0|s[7],C=8191&N,x=N>>>13,B=0|s[8],P=8191&B,L=B>>>13,j=0|s[9],U=8191&j,D=j>>>13,q=0|u[0],W=8191&q,F=q>>>13,Y=0|u[1],J=8191&Y,$=Y>>>13,z=0|u[2],H=8191&z,V=z>>>13,Z=0|u[3],Q=8191&Z,G=Z>>>13,K=0|u[4],X=8191&K,tt=K>>>13,et=0|u[5],rt=8191&et,nt=et>>>13,it=0|u[6],ot=8191&it,st=it>>>13,ut=0|u[7],at=8191&ut,ht=ut>>>13,ct=0|u[8],ft=8191&ct,lt=ct>>>13,dt=0|u[9],pt=8191&dt,mt=dt>>>13;r.negative=t.negative^e.negative,r.length=19;var gt=(h+(n=Math.imul(f,W))|0)+((8191&(i=(i=Math.imul(f,F))+Math.imul(l,W)|0))<<13)|0;h=((o=Math.imul(l,F))+(i>>>13)|0)+(gt>>>26)|0,gt&=67108863,n=Math.imul(p,W),i=(i=Math.imul(p,F))+Math.imul(m,W)|0,o=Math.imul(m,F);var vt=(h+(n=n+Math.imul(f,J)|0)|0)+((8191&(i=(i=i+Math.imul(f,$)|0)+Math.imul(l,J)|0))<<13)|0;h=((o=o+Math.imul(l,$)|0)+(i>>>13)|0)+(vt>>>26)|0,vt&=67108863,n=Math.imul(v,W),i=(i=Math.imul(v,F))+Math.imul(y,W)|0,o=Math.imul(y,F),n=n+Math.imul(p,J)|0,i=(i=i+Math.imul(p,$)|0)+Math.imul(m,J)|0,o=o+Math.imul(m,$)|0;var yt=(h+(n=n+Math.imul(f,H)|0)|0)+((8191&(i=(i=i+Math.imul(f,V)|0)+Math.imul(l,H)|0))<<13)|0;h=((o=o+Math.imul(l,V)|0)+(i>>>13)|0)+(yt>>>26)|0,yt&=67108863,n=Math.imul(_,W),i=(i=Math.imul(_,F))+Math.imul(M,W)|0,o=Math.imul(M,F),n=n+Math.imul(v,J)|0,i=(i=i+Math.imul(v,$)|0)+Math.imul(y,J)|0,o=o+Math.imul(y,$)|0,n=n+Math.imul(p,H)|0,i=(i=i+Math.imul(p,V)|0)+Math.imul(m,H)|0,o=o+Math.imul(m,V)|0;var wt=(h+(n=n+Math.imul(f,Q)|0)|0)+((8191&(i=(i=i+Math.imul(f,G)|0)+Math.imul(l,Q)|0))<<13)|0;h=((o=o+Math.imul(l,G)|0)+(i>>>13)|0)+(wt>>>26)|0,wt&=67108863,n=Math.imul(R,W),i=(i=Math.imul(R,F))+Math.imul(S,W)|0,o=Math.imul(S,F),n=n+Math.imul(_,J)|0,i=(i=i+Math.imul(_,$)|0)+Math.imul(M,J)|0,o=o+Math.imul(M,$)|0,n=n+Math.imul(v,H)|0,i=(i=i+Math.imul(v,V)|0)+Math.imul(y,H)|0,o=o+Math.imul(y,V)|0,n=n+Math.imul(p,Q)|0,i=(i=i+Math.imul(p,G)|0)+Math.imul(m,Q)|0,o=o+Math.imul(m,G)|0;var _t=(h+(n=n+Math.imul(f,X)|0)|0)+((8191&(i=(i=i+Math.imul(f,tt)|0)+Math.imul(l,X)|0))<<13)|0;h=((o=o+Math.imul(l,tt)|0)+(i>>>13)|0)+(_t>>>26)|0,_t&=67108863,n=Math.imul(O,W),i=(i=Math.imul(O,F))+Math.imul(I,W)|0,o=Math.imul(I,F),n=n+Math.imul(R,J)|0,i=(i=i+Math.imul(R,$)|0)+Math.imul(S,J)|0,o=o+Math.imul(S,$)|0,n=n+Math.imul(_,H)|0,i=(i=i+Math.imul(_,V)|0)+Math.imul(M,H)|0,o=o+Math.imul(M,V)|0,n=n+Math.imul(v,Q)|0,i=(i=i+Math.imul(v,G)|0)+Math.imul(y,Q)|0,o=o+Math.imul(y,G)|0,n=n+Math.imul(p,X)|0,i=(i=i+Math.imul(p,tt)|0)+Math.imul(m,X)|0,o=o+Math.imul(m,tt)|0;var Mt=(h+(n=n+Math.imul(f,rt)|0)|0)+((8191&(i=(i=i+Math.imul(f,nt)|0)+Math.imul(l,rt)|0))<<13)|0;h=((o=o+Math.imul(l,nt)|0)+(i>>>13)|0)+(Mt>>>26)|0,Mt&=67108863,n=Math.imul(A,W),i=(i=Math.imul(A,F))+Math.imul(T,W)|0,o=Math.imul(T,F),n=n+Math.imul(O,J)|0,i=(i=i+Math.imul(O,$)|0)+Math.imul(I,J)|0,o=o+Math.imul(I,$)|0,n=n+Math.imul(R,H)|0,i=(i=i+Math.imul(R,V)|0)+Math.imul(S,H)|0,o=o+Math.imul(S,V)|0,n=n+Math.imul(_,Q)|0,i=(i=i+Math.imul(_,G)|0)+Math.imul(M,Q)|0,o=o+Math.imul(M,G)|0,n=n+Math.imul(v,X)|0,i=(i=i+Math.imul(v,tt)|0)+Math.imul(y,X)|0,o=o+Math.imul(y,tt)|0,n=n+Math.imul(p,rt)|0,i=(i=i+Math.imul(p,nt)|0)+Math.imul(m,rt)|0,o=o+Math.imul(m,nt)|0;var bt=(h+(n=n+Math.imul(f,ot)|0)|0)+((8191&(i=(i=i+Math.imul(f,st)|0)+Math.imul(l,ot)|0))<<13)|0;h=((o=o+Math.imul(l,st)|0)+(i>>>13)|0)+(bt>>>26)|0,bt&=67108863,n=Math.imul(C,W),i=(i=Math.imul(C,F))+Math.imul(x,W)|0,o=Math.imul(x,F),n=n+Math.imul(A,J)|0,i=(i=i+Math.imul(A,$)|0)+Math.imul(T,J)|0,o=o+Math.imul(T,$)|0,n=n+Math.imul(O,H)|0,i=(i=i+Math.imul(O,V)|0)+Math.imul(I,H)|0,o=o+Math.imul(I,V)|0,n=n+Math.imul(R,Q)|0,i=(i=i+Math.imul(R,G)|0)+Math.imul(S,Q)|0,o=o+Math.imul(S,G)|0,n=n+Math.imul(_,X)|0,i=(i=i+Math.imul(_,tt)|0)+Math.imul(M,X)|0,o=o+Math.imul(M,tt)|0,n=n+Math.imul(v,rt)|0,i=(i=i+Math.imul(v,nt)|0)+Math.imul(y,rt)|0,o=o+Math.imul(y,nt)|0,n=n+Math.imul(p,ot)|0,i=(i=i+Math.imul(p,st)|0)+Math.imul(m,ot)|0,o=o+Math.imul(m,st)|0;var Rt=(h+(n=n+Math.imul(f,at)|0)|0)+((8191&(i=(i=i+Math.imul(f,ht)|0)+Math.imul(l,at)|0))<<13)|0;h=((o=o+Math.imul(l,ht)|0)+(i>>>13)|0)+(Rt>>>26)|0,Rt&=67108863,n=Math.imul(P,W),i=(i=Math.imul(P,F))+Math.imul(L,W)|0,o=Math.imul(L,F),n=n+Math.imul(C,J)|0,i=(i=i+Math.imul(C,$)|0)+Math.imul(x,J)|0,o=o+Math.imul(x,$)|0,n=n+Math.imul(A,H)|0,i=(i=i+Math.imul(A,V)|0)+Math.imul(T,H)|0,o=o+Math.imul(T,V)|0,n=n+Math.imul(O,Q)|0,i=(i=i+Math.imul(O,G)|0)+Math.imul(I,Q)|0,o=o+Math.imul(I,G)|0,n=n+Math.imul(R,X)|0,i=(i=i+Math.imul(R,tt)|0)+Math.imul(S,X)|0,o=o+Math.imul(S,tt)|0,n=n+Math.imul(_,rt)|0,i=(i=i+Math.imul(_,nt)|0)+Math.imul(M,rt)|0,o=o+Math.imul(M,nt)|0,n=n+Math.imul(v,ot)|0,i=(i=i+Math.imul(v,st)|0)+Math.imul(y,ot)|0,o=o+Math.imul(y,st)|0,n=n+Math.imul(p,at)|0,i=(i=i+Math.imul(p,ht)|0)+Math.imul(m,at)|0,o=o+Math.imul(m,ht)|0;var St=(h+(n=n+Math.imul(f,ft)|0)|0)+((8191&(i=(i=i+Math.imul(f,lt)|0)+Math.imul(l,ft)|0))<<13)|0;h=((o=o+Math.imul(l,lt)|0)+(i>>>13)|0)+(St>>>26)|0,St&=67108863,n=Math.imul(U,W),i=(i=Math.imul(U,F))+Math.imul(D,W)|0,o=Math.imul(D,F),n=n+Math.imul(P,J)|0,i=(i=i+Math.imul(P,$)|0)+Math.imul(L,J)|0,o=o+Math.imul(L,$)|0,n=n+Math.imul(C,H)|0,i=(i=i+Math.imul(C,V)|0)+Math.imul(x,H)|0,o=o+Math.imul(x,V)|0,n=n+Math.imul(A,Q)|0,i=(i=i+Math.imul(A,G)|0)+Math.imul(T,Q)|0,o=o+Math.imul(T,G)|0,n=n+Math.imul(O,X)|0,i=(i=i+Math.imul(O,tt)|0)+Math.imul(I,X)|0,o=o+Math.imul(I,tt)|0,n=n+Math.imul(R,rt)|0,i=(i=i+Math.imul(R,nt)|0)+Math.imul(S,rt)|0,o=o+Math.imul(S,nt)|0,n=n+Math.imul(_,ot)|0,i=(i=i+Math.imul(_,st)|0)+Math.imul(M,ot)|0,o=o+Math.imul(M,st)|0,n=n+Math.imul(v,at)|0,i=(i=i+Math.imul(v,ht)|0)+Math.imul(y,at)|0,o=o+Math.imul(y,ht)|0,n=n+Math.imul(p,ft)|0,i=(i=i+Math.imul(p,lt)|0)+Math.imul(m,ft)|0,o=o+Math.imul(m,lt)|0;var Et=(h+(n=n+Math.imul(f,pt)|0)|0)+((8191&(i=(i=i+Math.imul(f,mt)|0)+Math.imul(l,pt)|0))<<13)|0;h=((o=o+Math.imul(l,mt)|0)+(i>>>13)|0)+(Et>>>26)|0,Et&=67108863,n=Math.imul(U,J),i=(i=Math.imul(U,$))+Math.imul(D,J)|0,o=Math.imul(D,$),n=n+Math.imul(P,H)|0,i=(i=i+Math.imul(P,V)|0)+Math.imul(L,H)|0,o=o+Math.imul(L,V)|0,n=n+Math.imul(C,Q)|0,i=(i=i+Math.imul(C,G)|0)+Math.imul(x,Q)|0,o=o+Math.imul(x,G)|0,n=n+Math.imul(A,X)|0,i=(i=i+Math.imul(A,tt)|0)+Math.imul(T,X)|0,o=o+Math.imul(T,tt)|0,n=n+Math.imul(O,rt)|0,i=(i=i+Math.imul(O,nt)|0)+Math.imul(I,rt)|0,o=o+Math.imul(I,nt)|0,n=n+Math.imul(R,ot)|0,i=(i=i+Math.imul(R,st)|0)+Math.imul(S,ot)|0,o=o+Math.imul(S,st)|0,n=n+Math.imul(_,at)|0,i=(i=i+Math.imul(_,ht)|0)+Math.imul(M,at)|0,o=o+Math.imul(M,ht)|0,n=n+Math.imul(v,ft)|0,i=(i=i+Math.imul(v,lt)|0)+Math.imul(y,ft)|0,o=o+Math.imul(y,lt)|0;var Ot=(h+(n=n+Math.imul(p,pt)|0)|0)+((8191&(i=(i=i+Math.imul(p,mt)|0)+Math.imul(m,pt)|0))<<13)|0;h=((o=o+Math.imul(m,mt)|0)+(i>>>13)|0)+(Ot>>>26)|0,Ot&=67108863,n=Math.imul(U,H),i=(i=Math.imul(U,V))+Math.imul(D,H)|0,o=Math.imul(D,V),n=n+Math.imul(P,Q)|0,i=(i=i+Math.imul(P,G)|0)+Math.imul(L,Q)|0,o=o+Math.imul(L,G)|0,n=n+Math.imul(C,X)|0,i=(i=i+Math.imul(C,tt)|0)+Math.imul(x,X)|0,o=o+Math.imul(x,tt)|0,n=n+Math.imul(A,rt)|0,i=(i=i+Math.imul(A,nt)|0)+Math.imul(T,rt)|0,o=o+Math.imul(T,nt)|0,n=n+Math.imul(O,ot)|0,i=(i=i+Math.imul(O,st)|0)+Math.imul(I,ot)|0,o=o+Math.imul(I,st)|0,n=n+Math.imul(R,at)|0,i=(i=i+Math.imul(R,ht)|0)+Math.imul(S,at)|0,o=o+Math.imul(S,ht)|0,n=n+Math.imul(_,ft)|0,i=(i=i+Math.imul(_,lt)|0)+Math.imul(M,ft)|0,o=o+Math.imul(M,lt)|0;var It=(h+(n=n+Math.imul(v,pt)|0)|0)+((8191&(i=(i=i+Math.imul(v,mt)|0)+Math.imul(y,pt)|0))<<13)|0;h=((o=o+Math.imul(y,mt)|0)+(i>>>13)|0)+(It>>>26)|0,It&=67108863,n=Math.imul(U,Q),i=(i=Math.imul(U,G))+Math.imul(D,Q)|0,o=Math.imul(D,G),n=n+Math.imul(P,X)|0,i=(i=i+Math.imul(P,tt)|0)+Math.imul(L,X)|0,o=o+Math.imul(L,tt)|0,n=n+Math.imul(C,rt)|0,i=(i=i+Math.imul(C,nt)|0)+Math.imul(x,rt)|0,o=o+Math.imul(x,nt)|0,n=n+Math.imul(A,ot)|0,i=(i=i+Math.imul(A,st)|0)+Math.imul(T,ot)|0,o=o+Math.imul(T,st)|0,n=n+Math.imul(O,at)|0,i=(i=i+Math.imul(O,ht)|0)+Math.imul(I,at)|0,o=o+Math.imul(I,ht)|0,n=n+Math.imul(R,ft)|0,i=(i=i+Math.imul(R,lt)|0)+Math.imul(S,ft)|0,o=o+Math.imul(S,lt)|0;var kt=(h+(n=n+Math.imul(_,pt)|0)|0)+((8191&(i=(i=i+Math.imul(_,mt)|0)+Math.imul(M,pt)|0))<<13)|0;h=((o=o+Math.imul(M,mt)|0)+(i>>>13)|0)+(kt>>>26)|0,kt&=67108863,n=Math.imul(U,X),i=(i=Math.imul(U,tt))+Math.imul(D,X)|0,o=Math.imul(D,tt),n=n+Math.imul(P,rt)|0,i=(i=i+Math.imul(P,nt)|0)+Math.imul(L,rt)|0,o=o+Math.imul(L,nt)|0,n=n+Math.imul(C,ot)|0,i=(i=i+Math.imul(C,st)|0)+Math.imul(x,ot)|0,o=o+Math.imul(x,st)|0,n=n+Math.imul(A,at)|0,i=(i=i+Math.imul(A,ht)|0)+Math.imul(T,at)|0,o=o+Math.imul(T,ht)|0,n=n+Math.imul(O,ft)|0,i=(i=i+Math.imul(O,lt)|0)+Math.imul(I,ft)|0,o=o+Math.imul(I,lt)|0;var At=(h+(n=n+Math.imul(R,pt)|0)|0)+((8191&(i=(i=i+Math.imul(R,mt)|0)+Math.imul(S,pt)|0))<<13)|0;h=((o=o+Math.imul(S,mt)|0)+(i>>>13)|0)+(At>>>26)|0,At&=67108863,n=Math.imul(U,rt),i=(i=Math.imul(U,nt))+Math.imul(D,rt)|0,o=Math.imul(D,nt),n=n+Math.imul(P,ot)|0,i=(i=i+Math.imul(P,st)|0)+Math.imul(L,ot)|0,o=o+Math.imul(L,st)|0,n=n+Math.imul(C,at)|0,i=(i=i+Math.imul(C,ht)|0)+Math.imul(x,at)|0,o=o+Math.imul(x,ht)|0,n=n+Math.imul(A,ft)|0,i=(i=i+Math.imul(A,lt)|0)+Math.imul(T,ft)|0,o=o+Math.imul(T,lt)|0;var Tt=(h+(n=n+Math.imul(O,pt)|0)|0)+((8191&(i=(i=i+Math.imul(O,mt)|0)+Math.imul(I,pt)|0))<<13)|0;h=((o=o+Math.imul(I,mt)|0)+(i>>>13)|0)+(Tt>>>26)|0,Tt&=67108863,n=Math.imul(U,ot),i=(i=Math.imul(U,st))+Math.imul(D,ot)|0,o=Math.imul(D,st),n=n+Math.imul(P,at)|0,i=(i=i+Math.imul(P,ht)|0)+Math.imul(L,at)|0,o=o+Math.imul(L,ht)|0,n=n+Math.imul(C,ft)|0,i=(i=i+Math.imul(C,lt)|0)+Math.imul(x,ft)|0,o=o+Math.imul(x,lt)|0;var Nt=(h+(n=n+Math.imul(A,pt)|0)|0)+((8191&(i=(i=i+Math.imul(A,mt)|0)+Math.imul(T,pt)|0))<<13)|0;h=((o=o+Math.imul(T,mt)|0)+(i>>>13)|0)+(Nt>>>26)|0,Nt&=67108863,n=Math.imul(U,at),i=(i=Math.imul(U,ht))+Math.imul(D,at)|0,o=Math.imul(D,ht),n=n+Math.imul(P,ft)|0,i=(i=i+Math.imul(P,lt)|0)+Math.imul(L,ft)|0,o=o+Math.imul(L,lt)|0;var Ct=(h+(n=n+Math.imul(C,pt)|0)|0)+((8191&(i=(i=i+Math.imul(C,mt)|0)+Math.imul(x,pt)|0))<<13)|0;h=((o=o+Math.imul(x,mt)|0)+(i>>>13)|0)+(Ct>>>26)|0,Ct&=67108863,n=Math.imul(U,ft),i=(i=Math.imul(U,lt))+Math.imul(D,ft)|0,o=Math.imul(D,lt);var xt=(h+(n=n+Math.imul(P,pt)|0)|0)+((8191&(i=(i=i+Math.imul(P,mt)|0)+Math.imul(L,pt)|0))<<13)|0;h=((o=o+Math.imul(L,mt)|0)+(i>>>13)|0)+(xt>>>26)|0,xt&=67108863;var Bt=(h+(n=Math.imul(U,pt))|0)+((8191&(i=(i=Math.imul(U,mt))+Math.imul(D,pt)|0))<<13)|0;return h=((o=Math.imul(D,mt))+(i>>>13)|0)+(Bt>>>26)|0,Bt&=67108863,a[0]=gt,a[1]=vt,a[2]=yt,a[3]=wt,a[4]=_t,a[5]=Mt,a[6]=bt,a[7]=Rt,a[8]=St,a[9]=Et,a[10]=Ot,a[11]=It,a[12]=kt,a[13]=At,a[14]=Tt,a[15]=Nt,a[16]=Ct,a[17]=xt,a[18]=Bt,0!==h&&(a[19]=h,r.length++),r};function p(t,e,r){return(new m).mulp(t,e,r)}function m(t,e){this.x=t,this.y=e}Math.imul||(d=l),o.prototype.mulTo=function(t,e){var r=this.length+t.length;return 10===this.length&&10===t.length?d(this,t,e):r<63?l(this,t,e):r<1024?function(t,e,r){r.negative=e.negative^t.negative,r.length=t.length+e.length;for(var n=0,i=0,o=0;o<r.length-1;o++){var s=i;i=0;for(var u=67108863&n,a=Math.min(o,e.length-1),h=Math.max(0,o-t.length+1);h<=a;h++){var c=o-h,f=(0|t.words[c])*(0|e.words[h]),l=67108863&f;u=67108863&(l=l+u|0),i+=(s=(s=s+(f/67108864|0)|0)+(l>>>26)|0)>>>26,s&=67108863}r.words[o]=u,n=s,s=i}return 0!==n?r.words[o]=n:r.length--,r.strip()}(this,t,e):p(this,t,e)},m.prototype.makeRBT=function(t){for(var e=new Array(t),r=o.prototype._countBits(t)-1,n=0;n<t;n++)e[n]=this.revBin(n,r,t);return e},m.prototype.revBin=function(t,e,r){if(0===t||t===r-1)return t;for(var n=0,i=0;i<e;i++)n|=(1&t)<<e-i-1,t>>=1;return n},m.prototype.permute=function(t,e,r,n,i,o){for(var s=0;s<o;s++)n[s]=e[t[s]],i[s]=r[t[s]]},m.prototype.transform=function(t,e,r,n,i,o){this.permute(o,t,e,r,n,i);for(var s=1;s<i;s<<=1)for(var u=s<<1,a=Math.cos(2*Math.PI/u),h=Math.sin(2*Math.PI/u),c=0;c<i;c+=u)for(var f=a,l=h,d=0;d<s;d++){var p=r[c+d],m=n[c+d],g=r[c+d+s],v=n[c+d+s],y=f*g-l*v;v=f*v+l*g,g=y,r[c+d]=p+g,n[c+d]=m+v,r[c+d+s]=p-g,n[c+d+s]=m-v,d!==u&&(y=a*f-h*l,l=a*l+h*f,f=y)}},m.prototype.guessLen13b=function(t,e){var r=1|Math.max(e,t),n=1&r,i=0;for(r=r/2|0;r;r>>>=1)i++;return 1<<i+1+n},m.prototype.conjugate=function(t,e,r){if(!(r<=1))for(var n=0;n<r/2;n++){var i=t[n];t[n]=t[r-n-1],t[r-n-1]=i,i=e[n],e[n]=-e[r-n-1],e[r-n-1]=-i}},m.prototype.normalize13b=function(t,e){for(var r=0,n=0;n<e/2;n++){var i=8192*Math.round(t[2*n+1]/e)+Math.round(t[2*n]/e)+r;t[n]=67108863&i,r=i<67108864?0:i/67108864|0}return t},m.prototype.convert13b=function(t,e,r,i){for(var o=0,s=0;s<e;s++)o+=0|t[s],r[2*s]=8191&o,o>>>=13,r[2*s+1]=8191&o,o>>>=13;for(s=2*e;s<i;++s)r[s]=0;n(0===o),n(0==(-8192&o))},m.prototype.stub=function(t){for(var e=new Array(t),r=0;r<t;r++)e[r]=0;return e},m.prototype.mulp=function(t,e,r){var n=2*this.guessLen13b(t.length,e.length),i=this.makeRBT(n),o=this.stub(n),s=new Array(n),u=new Array(n),a=new Array(n),h=new Array(n),c=new Array(n),f=new Array(n),l=r.words;l.length=n,this.convert13b(t.words,t.length,s,n),this.convert13b(e.words,e.length,h,n),this.transform(s,o,u,a,n,i),this.transform(h,o,c,f,n,i);for(var d=0;d<n;d++){var p=u[d]*c[d]-a[d]*f[d];a[d]=u[d]*f[d]+a[d]*c[d],u[d]=p}return this.conjugate(u,a,n),this.transform(u,a,l,o,n,i),this.conjugate(l,o,n),this.normalize13b(l,n),r.negative=t.negative^e.negative,r.length=t.length+e.length,r.strip()},o.prototype.mul=function(t){var e=new o(null);return e.words=new Array(this.length+t.length),this.mulTo(t,e)},o.prototype.mulf=function(t){var e=new o(null);return e.words=new Array(this.length+t.length),p(this,t,e)},o.prototype.imul=function(t){return this.clone().mulTo(t,this)},o.prototype.imuln=function(t){n("number"==typeof t),n(t<67108864);for(var e=0,r=0;r<this.length;r++){var i=(0|this.words[r])*t,o=(67108863&i)+(67108863&e);e>>=26,e+=i/67108864|0,e+=o>>>26,this.words[r]=67108863&o}return 0!==e&&(this.words[r]=e,this.length++),this},o.prototype.muln=function(t){return this.clone().imuln(t)},o.prototype.sqr=function(){return this.mul(this)},o.prototype.isqr=function(){return this.imul(this.clone())},o.prototype.pow=function(t){var e=function(t){for(var e=new Array(t.bitLength()),r=0;r<e.length;r++){var n=r/26|0,i=r%26;e[r]=(t.words[n]&1<<i)>>>i}return e}(t);if(0===e.length)return new o(1);for(var r=this,n=0;n<e.length&&0===e[n];n++,r=r.sqr());if(++n<e.length)for(var i=r.sqr();n<e.length;n++,i=i.sqr())0!==e[n]&&(r=r.mul(i));return r},o.prototype.iushln=function(t){n("number"==typeof t&&t>=0);var e,r=t%26,i=(t-r)/26,o=67108863>>>26-r<<26-r;if(0!==r){var s=0;for(e=0;e<this.length;e++){var u=this.words[e]&o,a=(0|this.words[e])-u<<r;this.words[e]=a|s,s=u>>>26-r}s&&(this.words[e]=s,this.length++)}if(0!==i){for(e=this.length-1;e>=0;e--)this.words[e+i]=this.words[e];for(e=0;e<i;e++)this.words[e]=0;this.length+=i}return this.strip()},o.prototype.ishln=function(t){return n(0===this.negative),this.iushln(t)},o.prototype.iushrn=function(t,e,r){var i;n("number"==typeof t&&t>=0),i=e?(e-e%26)/26:0;var o=t%26,s=Math.min((t-o)/26,this.length),u=67108863^67108863>>>o<<o,a=r;if(i-=s,i=Math.max(0,i),a){for(var h=0;h<s;h++)a.words[h]=this.words[h];a.length=s}if(0===s);else if(this.length>s)for(this.length-=s,h=0;h<this.length;h++)this.words[h]=this.words[h+s];else this.words[0]=0,this.length=1;var c=0;for(h=this.length-1;h>=0&&(0!==c||h>=i);h--){var f=0|this.words[h];this.words[h]=c<<26-o|f>>>o,c=f&u}return a&&0!==c&&(a.words[a.length++]=c),0===this.length&&(this.words[0]=0,this.length=1),this.strip()},o.prototype.ishrn=function(t,e,r){return n(0===this.negative),this.iushrn(t,e,r)},o.prototype.shln=function(t){return this.clone().ishln(t)},o.prototype.ushln=function(t){return this.clone().iushln(t)},o.prototype.shrn=function(t){return this.clone().ishrn(t)},o.prototype.ushrn=function(t){return this.clone().iushrn(t)},o.prototype.testn=function(t){n("number"==typeof t&&t>=0);var e=t%26,r=(t-e)/26,i=1<<e;return!(this.length<=r)&&!!(this.words[r]&i)},o.prototype.imaskn=function(t){n("number"==typeof t&&t>=0);var e=t%26,r=(t-e)/26;if(n(0===this.negative,"imaskn works only with positive numbers"),this.length<=r)return this;if(0!==e&&r++,this.length=Math.min(r,this.length),0!==e){var i=67108863^67108863>>>e<<e;this.words[this.length-1]&=i}return this.strip()},o.prototype.maskn=function(t){return this.clone().imaskn(t)},o.prototype.iaddn=function(t){return n("number"==typeof t),n(t<67108864),t<0?this.isubn(-t):0!==this.negative?1===this.length&&(0|this.words[0])<t?(this.words[0]=t-(0|this.words[0]),this.negative=0,this):(this.negative=0,this.isubn(t),this.negative=1,this):this._iaddn(t)},o.prototype._iaddn=function(t){this.words[0]+=t;for(var e=0;e<this.length&&this.words[e]>=67108864;e++)this.words[e]-=67108864,e===this.length-1?this.words[e+1]=1:this.words[e+1]++;return this.length=Math.max(this.length,e+1),this},o.prototype.isubn=function(t){if(n("number"==typeof t),n(t<67108864),t<0)return this.iaddn(-t);if(0!==this.negative)return this.negative=0,this.iaddn(t),this.negative=1,this;if(this.words[0]-=t,1===this.length&&this.words[0]<0)this.words[0]=-this.words[0],this.negative=1;else for(var e=0;e<this.length&&this.words[e]<0;e++)this.words[e]+=67108864,this.words[e+1]-=1;return this.strip()},o.prototype.addn=function(t){return this.clone().iaddn(t)},o.prototype.subn=function(t){return this.clone().isubn(t)},o.prototype.iabs=function(){return this.negative=0,this},o.prototype.abs=function(){return this.clone().iabs()},o.prototype._ishlnsubmul=function(t,e,r){var i,o,s=t.length+r;this._expand(s);var u=0;for(i=0;i<t.length;i++){o=(0|this.words[i+r])+u;var a=(0|t.words[i])*e;u=((o-=67108863&a)>>26)-(a/67108864|0),this.words[i+r]=67108863&o}for(;i<this.length-r;i++)u=(o=(0|this.words[i+r])+u)>>26,this.words[i+r]=67108863&o;if(0===u)return this.strip();for(n(-1===u),u=0,i=0;i<this.length;i++)u=(o=-(0|this.words[i])+u)>>26,this.words[i]=67108863&o;return this.negative=1,this.strip()},o.prototype._wordDiv=function(t,e){var r=(this.length,t.length),n=this.clone(),i=t,s=0|i.words[i.length-1];0!==(r=26-this._countBits(s))&&(i=i.ushln(r),n.iushln(r),s=0|i.words[i.length-1]);var u,a=n.length-i.length;if("mod"!==e){(u=new o(null)).length=a+1,u.words=new Array(u.length);for(var h=0;h<u.length;h++)u.words[h]=0}var c=n.clone()._ishlnsubmul(i,1,a);0===c.negative&&(n=c,u&&(u.words[a]=1));for(var f=a-1;f>=0;f--){var l=67108864*(0|n.words[i.length+f])+(0|n.words[i.length+f-1]);for(l=Math.min(l/s|0,67108863),n._ishlnsubmul(i,l,f);0!==n.negative;)l--,n.negative=0,n._ishlnsubmul(i,1,f),n.isZero()||(n.negative^=1);u&&(u.words[f]=l)}return u&&u.strip(),n.strip(),"div"!==e&&0!==r&&n.iushrn(r),{div:u||null,mod:n}},o.prototype.divmod=function(t,e,r){return n(!t.isZero()),this.isZero()?{div:new o(0),mod:new o(0)}:0!==this.negative&&0===t.negative?(u=this.neg().divmod(t,e),"mod"!==e&&(i=u.div.neg()),"div"!==e&&(s=u.mod.neg(),r&&0!==s.negative&&s.iadd(t)),{div:i,mod:s}):0===this.negative&&0!==t.negative?(u=this.divmod(t.neg(),e),"mod"!==e&&(i=u.div.neg()),{div:i,mod:u.mod}):0!=(this.negative&t.negative)?(u=this.neg().divmod(t.neg(),e),"div"!==e&&(s=u.mod.neg(),r&&0!==s.negative&&s.isub(t)),{div:u.div,mod:s}):t.length>this.length||this.cmp(t)<0?{div:new o(0),mod:this}:1===t.length?"div"===e?{div:this.divn(t.words[0]),mod:null}:"mod"===e?{div:null,mod:new o(this.modn(t.words[0]))}:{div:this.divn(t.words[0]),mod:new o(this.modn(t.words[0]))}:this._wordDiv(t,e);var i,s,u},o.prototype.div=function(t){return this.divmod(t,"div",!1).div},o.prototype.mod=function(t){return this.divmod(t,"mod",!1).mod},o.prototype.umod=function(t){return this.divmod(t,"mod",!0).mod},o.prototype.divRound=function(t){var e=this.divmod(t);if(e.mod.isZero())return e.div;var r=0!==e.div.negative?e.mod.isub(t):e.mod,n=t.ushrn(1),i=t.andln(1),o=r.cmp(n);return o<0||1===i&&0===o?e.div:0!==e.div.negative?e.div.isubn(1):e.div.iaddn(1)},o.prototype.modn=function(t){n(t<=67108863);for(var e=(1<<26)%t,r=0,i=this.length-1;i>=0;i--)r=(e*r+(0|this.words[i]))%t;return r},o.prototype.idivn=function(t){n(t<=67108863);for(var e=0,r=this.length-1;r>=0;r--){var i=(0|this.words[r])+67108864*e;this.words[r]=i/t|0,e=i%t}return this.strip()},o.prototype.divn=function(t){return this.clone().idivn(t)},o.prototype.egcd=function(t){n(0===t.negative),n(!t.isZero());var e=this,r=t.clone();e=0!==e.negative?e.umod(t):e.clone();for(var i=new o(1),s=new o(0),u=new o(0),a=new o(1),h=0;e.isEven()&&r.isEven();)e.iushrn(1),r.iushrn(1),++h;for(var c=r.clone(),f=e.clone();!e.isZero();){for(var l=0,d=1;0==(e.words[0]&d)&&l<26;++l,d<<=1);if(l>0)for(e.iushrn(l);l-- >0;)(i.isOdd()||s.isOdd())&&(i.iadd(c),s.isub(f)),i.iushrn(1),s.iushrn(1);for(var p=0,m=1;0==(r.words[0]&m)&&p<26;++p,m<<=1);if(p>0)for(r.iushrn(p);p-- >0;)(u.isOdd()||a.isOdd())&&(u.iadd(c),a.isub(f)),u.iushrn(1),a.iushrn(1);e.cmp(r)>=0?(e.isub(r),i.isub(u),s.isub(a)):(r.isub(e),u.isub(i),a.isub(s))}return{a:u,b:a,gcd:r.iushln(h)}},o.prototype._invmp=function(t){n(0===t.negative),n(!t.isZero());var e=this,r=t.clone();e=0!==e.negative?e.umod(t):e.clone();for(var i,s=new o(1),u=new o(0),a=r.clone();e.cmpn(1)>0&&r.cmpn(1)>0;){for(var h=0,c=1;0==(e.words[0]&c)&&h<26;++h,c<<=1);if(h>0)for(e.iushrn(h);h-- >0;)s.isOdd()&&s.iadd(a),s.iushrn(1);for(var f=0,l=1;0==(r.words[0]&l)&&f<26;++f,l<<=1);if(f>0)for(r.iushrn(f);f-- >0;)u.isOdd()&&u.iadd(a),u.iushrn(1);e.cmp(r)>=0?(e.isub(r),s.isub(u)):(r.isub(e),u.isub(s))}return(i=0===e.cmpn(1)?s:u).cmpn(0)<0&&i.iadd(t),i},o.prototype.gcd=function(t){if(this.isZero())return t.abs();if(t.isZero())return this.abs();var e=this.clone(),r=t.clone();e.negative=0,r.negative=0;for(var n=0;e.isEven()&&r.isEven();n++)e.iushrn(1),r.iushrn(1);for(;;){for(;e.isEven();)e.iushrn(1);for(;r.isEven();)r.iushrn(1);var i=e.cmp(r);if(i<0){var o=e;e=r,r=o}else if(0===i||0===r.cmpn(1))break;e.isub(r)}return r.iushln(n)},o.prototype.invm=function(t){return this.egcd(t).a.umod(t)},o.prototype.isEven=function(){return 0==(1&this.words[0])},o.prototype.isOdd=function(){return 1==(1&this.words[0])},o.prototype.andln=function(t){return this.words[0]&t},o.prototype.bincn=function(t){n("number"==typeof t);var e=t%26,r=(t-e)/26,i=1<<e;if(this.length<=r)return this._expand(r+1),this.words[r]|=i,this;for(var o=i,s=r;0!==o&&s<this.length;s++){var u=0|this.words[s];o=(u+=o)>>>26,u&=67108863,this.words[s]=u}return 0!==o&&(this.words[s]=o,this.length++),this},o.prototype.isZero=function(){return 1===this.length&&0===this.words[0]},o.prototype.cmpn=function(t){var e,r=t<0;if(0!==this.negative&&!r)return-1;if(0===this.negative&&r)return 1;if(this.strip(),this.length>1)e=1;else{r&&(t=-t),n(t<=67108863,"Number is too big");var i=0|this.words[0];e=i===t?0:i<t?-1:1}return 0!==this.negative?0|-e:e},o.prototype.cmp=function(t){if(0!==this.negative&&0===t.negative)return-1;if(0===this.negative&&0!==t.negative)return 1;var e=this.ucmp(t);return 0!==this.negative?0|-e:e},o.prototype.ucmp=function(t){if(this.length>t.length)return 1;if(this.length<t.length)return-1;for(var e=0,r=this.length-1;r>=0;r--){var n=0|this.words[r],i=0|t.words[r];if(n!==i){n<i?e=-1:n>i&&(e=1);break}}return e},o.prototype.gtn=function(t){return 1===this.cmpn(t)},o.prototype.gt=function(t){return 1===this.cmp(t)},o.prototype.gten=function(t){return this.cmpn(t)>=0},o.prototype.gte=function(t){return this.cmp(t)>=0},o.prototype.ltn=function(t){return-1===this.cmpn(t)},o.prototype.lt=function(t){return-1===this.cmp(t)},o.prototype.lten=function(t){return this.cmpn(t)<=0},o.prototype.lte=function(t){return this.cmp(t)<=0},o.prototype.eqn=function(t){return 0===this.cmpn(t)},o.prototype.eq=function(t){return 0===this.cmp(t)},o.red=function(t){return new b(t)},o.prototype.toRed=function(t){return n(!this.red,"Already a number in reduction context"),n(0===this.negative,"red works only with positives"),t.convertTo(this)._forceRed(t)},o.prototype.fromRed=function(){return n(this.red,"fromRed works only with numbers in reduction context"),this.red.convertFrom(this)},o.prototype._forceRed=function(t){return this.red=t,this},o.prototype.forceRed=function(t){return n(!this.red,"Already a number in reduction context"),this._forceRed(t)},o.prototype.redAdd=function(t){return n(this.red,"redAdd works only with red numbers"),this.red.add(this,t)},o.prototype.redIAdd=function(t){return n(this.red,"redIAdd works only with red numbers"),this.red.iadd(this,t)},o.prototype.redSub=function(t){return n(this.red,"redSub works only with red numbers"),this.red.sub(this,t)},o.prototype.redISub=function(t){return n(this.red,"redISub works only with red numbers"),this.red.isub(this,t)},o.prototype.redShl=function(t){return n(this.red,"redShl works only with red numbers"),this.red.shl(this,t)},o.prototype.redMul=function(t){return n(this.red,"redMul works only with red numbers"),this.red._verify2(this,t),this.red.mul(this,t)},o.prototype.redIMul=function(t){return n(this.red,"redMul works only with red numbers"),this.red._verify2(this,t),this.red.imul(this,t)},o.prototype.redSqr=function(){return n(this.red,"redSqr works only with red numbers"),this.red._verify1(this),this.red.sqr(this)},o.prototype.redISqr=function(){return n(this.red,"redISqr works only with red numbers"),this.red._verify1(this),this.red.isqr(this)},o.prototype.redSqrt=function(){return n(this.red,"redSqrt works only with red numbers"),this.red._verify1(this),this.red.sqrt(this)},o.prototype.redInvm=function(){return n(this.red,"redInvm works only with red numbers"),this.red._verify1(this),this.red.invm(this)},o.prototype.redNeg=function(){return n(this.red,"redNeg works only with red numbers"),this.red._verify1(this),this.red.neg(this)},o.prototype.redPow=function(t){return n(this.red&&!t.red,"redPow(normalNum)"),this.red._verify1(this),this.red.pow(this,t)};var g={k256:null,p224:null,p192:null,p25519:null};function v(t,e){this.name=t,this.p=new o(e,16),this.n=this.p.bitLength(),this.k=new o(1).iushln(this.n).isub(this.p),this.tmp=this._tmp()}function y(){v.call(this,"k256","ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffe fffffc2f")}function w(){v.call(this,"p224","ffffffff ffffffff ffffffff ffffffff 00000000 00000000 00000001")}function _(){v.call(this,"p192","ffffffff ffffffff ffffffff fffffffe ffffffff ffffffff")}function M(){v.call(this,"25519","7fffffffffffffff ffffffffffffffff ffffffffffffffff ffffffffffffffed")}function b(t){if("string"==typeof t){var e=o._prime(t);this.m=e.p,this.prime=e}else n(t.gtn(1),"modulus must be greater than 1"),this.m=t,this.prime=null}function R(t){b.call(this,t),this.shift=this.m.bitLength(),this.shift%26!=0&&(this.shift+=26-this.shift%26),this.r=new o(1).iushln(this.shift),this.r2=this.imod(this.r.sqr()),this.rinv=this.r._invmp(this.m),this.minv=this.rinv.mul(this.r).isubn(1).div(this.m),this.minv=this.minv.umod(this.r),this.minv=this.r.sub(this.minv)}v.prototype._tmp=function(){var t=new o(null);return t.words=new Array(Math.ceil(this.n/13)),t},v.prototype.ireduce=function(t){var e,r=t;do{this.split(r,this.tmp),e=(r=(r=this.imulK(r)).iadd(this.tmp)).bitLength()}while(e>this.n);var n=e<this.n?-1:r.ucmp(this.p);return 0===n?(r.words[0]=0,r.length=1):n>0?r.isub(this.p):r.strip(),r},v.prototype.split=function(t,e){t.iushrn(this.n,0,e)},v.prototype.imulK=function(t){return t.imul(this.k)},i(y,v),y.prototype.split=function(t,e){for(var r=Math.min(t.length,9),n=0;n<r;n++)e.words[n]=t.words[n];if(e.length=r,t.length<=9)return t.words[0]=0,void(t.length=1);var i=t.words[9];for(e.words[e.length++]=4194303&i,n=10;n<t.length;n++){var o=0|t.words[n];t.words[n-10]=(4194303&o)<<4|i>>>22,i=o}i>>>=22,t.words[n-10]=i,0===i&&t.length>10?t.length-=10:t.length-=9},y.prototype.imulK=function(t){t.words[t.length]=0,t.words[t.length+1]=0,t.length+=2;for(var e=0,r=0;r<t.length;r++){var n=0|t.words[r];e+=977*n,t.words[r]=67108863&e,e=64*n+(e/67108864|0)}return 0===t.words[t.length-1]&&(t.length--,0===t.words[t.length-1]&&t.length--),t},i(w,v),i(_,v),i(M,v),M.prototype.imulK=function(t){for(var e=0,r=0;r<t.length;r++){var n=19*(0|t.words[r])+e,i=67108863&n;n>>>=26,t.words[r]=i,e=n}return 0!==e&&(t.words[t.length++]=e),t},o._prime=function(t){if(g[t])return g[t];var e;if("k256"===t)e=new y;else if("p224"===t)e=new w;else if("p192"===t)e=new _;else{if("p25519"!==t)throw new Error("Unknown prime "+t);e=new M}return g[t]=e,e},b.prototype._verify1=function(t){n(0===t.negative,"red works only with positives"),n(t.red,"red works only with red numbers")},b.prototype._verify2=function(t,e){n(0==(t.negative|e.negative),"red works only with positives"),n(t.red&&t.red===e.red,"red works only with red numbers")},b.prototype.imod=function(t){return this.prime?this.prime.ireduce(t)._forceRed(this):t.umod(this.m)._forceRed(this)},b.prototype.neg=function(t){return t.isZero()?t.clone():this.m.sub(t)._forceRed(this)},b.prototype.add=function(t,e){this._verify2(t,e);var r=t.add(e);return r.cmp(this.m)>=0&&r.isub(this.m),r._forceRed(this)},b.prototype.iadd=function(t,e){this._verify2(t,e);var r=t.iadd(e);return r.cmp(this.m)>=0&&r.isub(this.m),r},b.prototype.sub=function(t,e){this._verify2(t,e);var r=t.sub(e);return r.cmpn(0)<0&&r.iadd(this.m),r._forceRed(this)},b.prototype.isub=function(t,e){this._verify2(t,e);var r=t.isub(e);return r.cmpn(0)<0&&r.iadd(this.m),r},b.prototype.shl=function(t,e){return this._verify1(t),this.imod(t.ushln(e))},b.prototype.imul=function(t,e){return this._verify2(t,e),this.imod(t.imul(e))},b.prototype.mul=function(t,e){return this._verify2(t,e),this.imod(t.mul(e))},b.prototype.isqr=function(t){return this.imul(t,t.clone())},b.prototype.sqr=function(t){return this.mul(t,t)},b.prototype.sqrt=function(t){if(t.isZero())return t.clone();var e=this.m.andln(3);if(n(e%2==1),3===e){var r=this.m.add(new o(1)).iushrn(2);return this.pow(t,r)}for(var i=this.m.subn(1),s=0;!i.isZero()&&0===i.andln(1);)s++,i.iushrn(1);n(!i.isZero());var u=new o(1).toRed(this),a=u.redNeg(),h=this.m.subn(1).iushrn(1),c=this.m.bitLength();for(c=new o(2*c*c).toRed(this);0!==this.pow(c,h).cmp(a);)c.redIAdd(a);for(var f=this.pow(c,i),l=this.pow(t,i.addn(1).iushrn(1)),d=this.pow(t,i),p=s;0!==d.cmp(u);){for(var m=d,g=0;0!==m.cmp(u);g++)m=m.redSqr();n(g<p);var v=this.pow(f,new o(1).iushln(p-g-1));l=l.redMul(v),f=v.redSqr(),d=d.redMul(f),p=g}return l},b.prototype.invm=function(t){var e=t._invmp(this.m);return 0!==e.negative?(e.negative=0,this.imod(e).redNeg()):this.imod(e)},b.prototype.pow=function(t,e){if(e.isZero())return new o(1).toRed(this);if(0===e.cmpn(1))return t.clone();var r=new Array(16);r[0]=new o(1).toRed(this),r[1]=t;for(var n=2;n<r.length;n++)r[n]=this.mul(r[n-1],t);var i=r[0],s=0,u=0,a=e.bitLength()%26;for(0===a&&(a=26),n=e.length-1;n>=0;n--){for(var h=e.words[n],c=a-1;c>=0;c--){var f=h>>c&1;i!==r[0]&&(i=this.sqr(i)),0!==f||0!==s?(s<<=1,s|=f,(4===++u||0===n&&0===c)&&(i=this.mul(i,r[s]),u=0,s=0)):u=0}a=26}return i},b.prototype.convertTo=function(t){var e=t.umod(this.m);return e===t?e.clone():e},b.prototype.convertFrom=function(t){var e=t.clone();return e.red=null,e},o.mont=function(t){return new R(t)},i(R,b),R.prototype.convertTo=function(t){return this.imod(t.ushln(this.shift))},R.prototype.convertFrom=function(t){var e=this.imod(t.mul(this.rinv));return e.red=null,e},R.prototype.imul=function(t,e){if(t.isZero()||e.isZero())return t.words[0]=0,t.length=1,t;var r=t.imul(e),n=r.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m),i=r.isub(n).iushrn(this.shift),o=i;return i.cmp(this.m)>=0?o=i.isub(this.m):i.cmpn(0)<0&&(o=i.iadd(this.m)),o._forceRed(this)},R.prototype.mul=function(t,e){if(t.isZero()||e.isZero())return new o(0)._forceRed(this);var r=t.mul(e),n=r.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m),i=r.isub(n).iushrn(this.shift),s=i;return i.cmp(this.m)>=0?s=i.isub(this.m):i.cmpn(0)<0&&(s=i.iadd(this.m)),s._forceRed(this)},R.prototype.invm=function(t){return this.imod(t._invmp(this.m).mul(this.r2))._forceRed(this)}}(t,this)}).call(this,r(31)(t))},function(t,e){var r;r=function(){return this}();try{r=r||new Function("return this")()}catch(t){"object"==typeof window&&(r=window)}t.exports=r},function(t,e,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(t,e,r,n){void 0===n&&(n=r),Object.defineProperty(t,n,{enumerable:!0,get:function(){return e[r]}})}:function(t,e,r,n){void 0===n&&(n=r),t[n]=e[r]}),i=this&&this.__exportStar||function(t,e){for(var r in t)"default"===r||e.hasOwnProperty(r)||n(e,t,r)};Object.defineProperty(e,"__esModule",{value:!0}),i(r(37),e),i(r(38),e)},function(t,e){var r,n,i=t.exports={};function o(){throw new Error("setTimeout has not been defined")}function s(){throw new Error("clearTimeout has not been defined")}function u(t){if(r===setTimeout)return setTimeout(t,0);if((r===o||!r)&&setTimeout)return r=setTimeout,setTimeout(t,0);try{return r(t,0)}catch(e){try{return r.call(null,t,0)}catch(e){return r.call(this,t,0)}}}!function(){try{r="function"==typeof setTimeout?setTimeout:o}catch(t){r=o}try{n="function"==typeof clearTimeout?clearTimeout:s}catch(t){n=s}}();var a,h=[],c=!1,f=-1;function l(){c&&a&&(c=!1,a.length?h=a.concat(h):f=-1,h.length&&d())}function d(){if(!c){var t=u(l);c=!0;for(var e=h.length;e;){for(a=h,h=[];++f<e;)a&&a[f].run();f=-1,e=h.length}a=null,c=!1,function(t){if(n===clearTimeout)return clearTimeout(t);if((n===s||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(t);try{n(t)}catch(e){try{return n.call(null,t)}catch(e){return n.call(this,t)}}}(t)}}function p(t,e){this.fun=t,this.array=e}function m(){}i.nextTick=function(t){var e=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)e[r-1]=arguments[r];h.push(new p(t,e)),1!==h.length||c||u(d)},p.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=m,i.addListener=m,i.once=m,i.off=m,i.removeListener=m,i.removeAllListeners=m,i.emit=m,i.prependListener=m,i.prependOnceListener=m,i.listeners=function(t){return[]},i.binding=function(t){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(t){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},function(t,e){t.exports=i,i.strict=o,i.loose=s;var r=Object.prototype.toString,n={"[object Int8Array]":!0,"[object Int16Array]":!0,"[object Int32Array]":!0,"[object Uint8Array]":!0,"[object Uint8ClampedArray]":!0,"[object Uint16Array]":!0,"[object Uint32Array]":!0,"[object Float32Array]":!0,"[object Float64Array]":!0};function i(t){return o(t)||s(t)}function o(t){return t instanceof Int8Array||t instanceof Int16Array||t instanceof Int32Array||t instanceof Uint8Array||t instanceof Uint8ClampedArray||t instanceof Uint16Array||t instanceof Uint32Array||t instanceof Float32Array||t instanceof Float64Array}function s(t){return n[r.call(t)]}},function(t,e,r){"use strict";const n=r(39),i=r(40),o=r(41);function s(t){if("string"!=typeof t||1!==t.length)throw new TypeError("arrayFormatSeparator must be single character string")}function u(t,e){return e.encode?e.strict?n(t):encodeURIComponent(t):t}function a(t,e){return e.decode?i(t):t}function h(t){const e=t.indexOf("#");return-1!==e&&(t=t.slice(0,e)),t}function c(t){const e=(t=h(t)).indexOf("?");return-1===e?"":t.slice(e+1)}function f(t,e){return e.parseNumbers&&!Number.isNaN(Number(t))&&"string"==typeof t&&""!==t.trim()?t=Number(t):!e.parseBooleans||null===t||"true"!==t.toLowerCase()&&"false"!==t.toLowerCase()||(t="true"===t.toLowerCase()),t}function l(t,e){s((e=Object.assign({decode:!0,sort:!0,arrayFormat:"none",arrayFormatSeparator:",",parseNumbers:!1,parseBooleans:!1},e)).arrayFormatSeparator);const r=function(t){let e;switch(t.arrayFormat){case"index":return(t,r,n)=>{e=/\[(\d*)\]$/.exec(t),t=t.replace(/\[\d*\]$/,""),e?(void 0===n[t]&&(n[t]={}),n[t][e[1]]=r):n[t]=r};case"bracket":return(t,r,n)=>{e=/(\[\])$/.exec(t),t=t.replace(/\[\]$/,""),e?void 0!==n[t]?n[t]=[].concat(n[t],r):n[t]=[r]:n[t]=r};case"comma":case"separator":return(e,r,n)=>{const i="string"==typeof r&&r.split("").indexOf(t.arrayFormatSeparator)>-1?r.split(t.arrayFormatSeparator).map(e=>a(e,t)):null===r?r:a(r,t);n[e]=i};default:return(t,e,r)=>{void 0!==r[t]?r[t]=[].concat(r[t],e):r[t]=e}}}(e),n=Object.create(null);if("string"!=typeof t)return n;if(!(t=t.trim().replace(/^[?#&]/,"")))return n;for(const i of t.split("&")){let[t,s]=o(e.decode?i.replace(/\+/g," "):i,"=");s=void 0===s?null:["comma","separator"].includes(e.arrayFormat)?s:a(s,e),r(a(t,e),s,n)}for(const t of Object.keys(n)){const r=n[t];if("object"==typeof r&&null!==r)for(const t of Object.keys(r))r[t]=f(r[t],e);else n[t]=f(r,e)}return!1===e.sort?n:(!0===e.sort?Object.keys(n).sort():Object.keys(n).sort(e.sort)).reduce((t,e)=>{const r=n[e];return Boolean(r)&&"object"==typeof r&&!Array.isArray(r)?t[e]=function t(e){return Array.isArray(e)?e.sort():"object"==typeof e?t(Object.keys(e)).sort((t,e)=>Number(t)-Number(e)).map(t=>e[t]):e}(r):t[e]=r,t},Object.create(null))}e.extract=c,e.parse=l,e.stringify=(t,e)=>{if(!t)return"";s((e=Object.assign({encode:!0,strict:!0,arrayFormat:"none",arrayFormatSeparator:","},e)).arrayFormatSeparator);const r=r=>e.skipNull&&null==t[r]||e.skipEmptyString&&""===t[r],n=function(t){switch(t.arrayFormat){case"index":return e=>(r,n)=>{const i=r.length;return void 0===n||t.skipNull&&null===n||t.skipEmptyString&&""===n?r:null===n?[...r,[u(e,t),"[",i,"]"].join("")]:[...r,[u(e,t),"[",u(i,t),"]=",u(n,t)].join("")]};case"bracket":return e=>(r,n)=>void 0===n||t.skipNull&&null===n||t.skipEmptyString&&""===n?r:null===n?[...r,[u(e,t),"[]"].join("")]:[...r,[u(e,t),"[]=",u(n,t)].join("")];case"comma":case"separator":return e=>(r,n)=>null==n||0===n.length?r:0===r.length?[[u(e,t),"=",u(n,t)].join("")]:[[r,u(n,t)].join(t.arrayFormatSeparator)];default:return e=>(r,n)=>void 0===n||t.skipNull&&null===n||t.skipEmptyString&&""===n?r:null===n?[...r,u(e,t)]:[...r,[u(e,t),"=",u(n,t)].join("")]}}(e),i={};for(const e of Object.keys(t))r(e)||(i[e]=t[e]);const o=Object.keys(i);return!1!==e.sort&&o.sort(e.sort),o.map(r=>{const i=t[r];return void 0===i?"":null===i?u(r,e):Array.isArray(i)?i.reduce(n(r),[]).join("&"):u(r,e)+"="+u(i,e)}).filter(t=>t.length>0).join("&")},e.parseUrl=(t,e)=>{e=Object.assign({decode:!0},e);const[r,n]=o(t,"#");return Object.assign({url:r.split("?")[0]||"",query:l(c(t),e)},e&&e.parseFragmentIdentifier&&n?{fragmentIdentifier:a(n,e)}:{})},e.stringifyUrl=(t,r)=>{r=Object.assign({encode:!0,strict:!0},r);const n=h(t.url).split("?")[0]||"",i=e.extract(t.url),o=e.parse(i,{sort:!1}),s=Object.assign(o,t.query);let a=e.stringify(s,r);a&&(a="?"+a);let c=function(t){let e="";const r=t.indexOf("#");return-1!==r&&(e=t.slice(r)),e}(t.url);return t.fragmentIdentifier&&(c="#"+u(t.fragmentIdentifier,r)),`${n}${a}${c}`}},function(t,e,r){"use strict";(function(t){
/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <http://feross.org>
 * @license  MIT
 */
var n=r(33),i=r(34),o=r(35);function s(){return a.TYPED_ARRAY_SUPPORT?**********:**********}function u(t,e){if(s()<e)throw new RangeError("Invalid typed array length");return a.TYPED_ARRAY_SUPPORT?(t=new Uint8Array(e)).__proto__=a.prototype:(null===t&&(t=new a(e)),t.length=e),t}function a(t,e,r){if(!(a.TYPED_ARRAY_SUPPORT||this instanceof a))return new a(t,e,r);if("number"==typeof t){if("string"==typeof e)throw new Error("If encoding is specified then the first argument must be a string");return f(this,t)}return h(this,t,e,r)}function h(t,e,r,n){if("number"==typeof e)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&e instanceof ArrayBuffer?function(t,e,r,n){if(e.byteLength,r<0||e.byteLength<r)throw new RangeError("'offset' is out of bounds");if(e.byteLength<r+(n||0))throw new RangeError("'length' is out of bounds");e=void 0===r&&void 0===n?new Uint8Array(e):void 0===n?new Uint8Array(e,r):new Uint8Array(e,r,n);a.TYPED_ARRAY_SUPPORT?(t=e).__proto__=a.prototype:t=l(t,e);return t}(t,e,r,n):"string"==typeof e?function(t,e,r){"string"==typeof r&&""!==r||(r="utf8");if(!a.isEncoding(r))throw new TypeError('"encoding" must be a valid string encoding');var n=0|p(e,r),i=(t=u(t,n)).write(e,r);i!==n&&(t=t.slice(0,i));return t}(t,e,r):function(t,e){if(a.isBuffer(e)){var r=0|d(e.length);return 0===(t=u(t,r)).length||e.copy(t,0,0,r),t}if(e){if("undefined"!=typeof ArrayBuffer&&e.buffer instanceof ArrayBuffer||"length"in e)return"number"!=typeof e.length||(n=e.length)!=n?u(t,0):l(t,e);if("Buffer"===e.type&&o(e.data))return l(t,e.data)}var n;throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(t,e)}function c(t){if("number"!=typeof t)throw new TypeError('"size" argument must be a number');if(t<0)throw new RangeError('"size" argument must not be negative')}function f(t,e){if(c(e),t=u(t,e<0?0:0|d(e)),!a.TYPED_ARRAY_SUPPORT)for(var r=0;r<e;++r)t[r]=0;return t}function l(t,e){var r=e.length<0?0:0|d(e.length);t=u(t,r);for(var n=0;n<r;n+=1)t[n]=255&e[n];return t}function d(t){if(t>=s())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+s().toString(16)+" bytes");return 0|t}function p(t,e){if(a.isBuffer(t))return t.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(t)||t instanceof ArrayBuffer))return t.byteLength;"string"!=typeof t&&(t=""+t);var r=t.length;if(0===r)return 0;for(var n=!1;;)switch(e){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":case void 0:return q(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return W(t).length;default:if(n)return q(t).length;e=(""+e).toLowerCase(),n=!0}}function m(t,e,r){var n=!1;if((void 0===e||e<0)&&(e=0),e>this.length)return"";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return"";if((r>>>=0)<=(e>>>=0))return"";for(t||(t="utf8");;)switch(t){case"hex":return A(this,e,r);case"utf8":case"utf-8":return O(this,e,r);case"ascii":return I(this,e,r);case"latin1":case"binary":return k(this,e,r);case"base64":return E(this,e,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return T(this,e,r);default:if(n)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),n=!0}}function g(t,e,r){var n=t[e];t[e]=t[r],t[r]=n}function v(t,e,r,n,i){if(0===t.length)return-1;if("string"==typeof r?(n=r,r=0):r>**********?r=**********:r<-**********&&(r=-**********),r=+r,isNaN(r)&&(r=i?0:t.length-1),r<0&&(r=t.length+r),r>=t.length){if(i)return-1;r=t.length-1}else if(r<0){if(!i)return-1;r=0}if("string"==typeof e&&(e=a.from(e,n)),a.isBuffer(e))return 0===e.length?-1:y(t,e,r,n,i);if("number"==typeof e)return e&=255,a.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(t,e,r):Uint8Array.prototype.lastIndexOf.call(t,e,r):y(t,[e],r,n,i);throw new TypeError("val must be string, number or Buffer")}function y(t,e,r,n,i){var o,s=1,u=t.length,a=e.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(t.length<2||e.length<2)return-1;s=2,u/=2,a/=2,r/=2}function h(t,e){return 1===s?t[e]:t.readUInt16BE(e*s)}if(i){var c=-1;for(o=r;o<u;o++)if(h(t,o)===h(e,-1===c?0:o-c)){if(-1===c&&(c=o),o-c+1===a)return c*s}else-1!==c&&(o-=o-c),c=-1}else for(r+a>u&&(r=u-a),o=r;o>=0;o--){for(var f=!0,l=0;l<a;l++)if(h(t,o+l)!==h(e,l)){f=!1;break}if(f)return o}return-1}function w(t,e,r,n){r=Number(r)||0;var i=t.length-r;n?(n=Number(n))>i&&(n=i):n=i;var o=e.length;if(o%2!=0)throw new TypeError("Invalid hex string");n>o/2&&(n=o/2);for(var s=0;s<n;++s){var u=parseInt(e.substr(2*s,2),16);if(isNaN(u))return s;t[r+s]=u}return s}function _(t,e,r,n){return F(q(e,t.length-r),t,r,n)}function M(t,e,r,n){return F(function(t){for(var e=[],r=0;r<t.length;++r)e.push(255&t.charCodeAt(r));return e}(e),t,r,n)}function b(t,e,r,n){return M(t,e,r,n)}function R(t,e,r,n){return F(W(e),t,r,n)}function S(t,e,r,n){return F(function(t,e){for(var r,n,i,o=[],s=0;s<t.length&&!((e-=2)<0);++s)r=t.charCodeAt(s),n=r>>8,i=r%256,o.push(i),o.push(n);return o}(e,t.length-r),t,r,n)}function E(t,e,r){return 0===e&&r===t.length?n.fromByteArray(t):n.fromByteArray(t.slice(e,r))}function O(t,e,r){r=Math.min(t.length,r);for(var n=[],i=e;i<r;){var o,s,u,a,h=t[i],c=null,f=h>239?4:h>223?3:h>191?2:1;if(i+f<=r)switch(f){case 1:h<128&&(c=h);break;case 2:128==(192&(o=t[i+1]))&&(a=(31&h)<<6|63&o)>127&&(c=a);break;case 3:o=t[i+1],s=t[i+2],128==(192&o)&&128==(192&s)&&(a=(15&h)<<12|(63&o)<<6|63&s)>2047&&(a<55296||a>57343)&&(c=a);break;case 4:o=t[i+1],s=t[i+2],u=t[i+3],128==(192&o)&&128==(192&s)&&128==(192&u)&&(a=(15&h)<<18|(63&o)<<12|(63&s)<<6|63&u)>65535&&a<1114112&&(c=a)}null===c?(c=65533,f=1):c>65535&&(c-=65536,n.push(c>>>10&1023|55296),c=56320|1023&c),n.push(c),i+=f}return function(t){var e=t.length;if(e<=4096)return String.fromCharCode.apply(String,t);var r="",n=0;for(;n<e;)r+=String.fromCharCode.apply(String,t.slice(n,n+=4096));return r}(n)}e.Buffer=a,e.SlowBuffer=function(t){+t!=t&&(t=0);return a.alloc(+t)},e.INSPECT_MAX_BYTES=50,a.TYPED_ARRAY_SUPPORT=void 0!==t.TYPED_ARRAY_SUPPORT?t.TYPED_ARRAY_SUPPORT:function(){try{var t=new Uint8Array(1);return t.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===t.foo()&&"function"==typeof t.subarray&&0===t.subarray(1,1).byteLength}catch(t){return!1}}(),e.kMaxLength=s(),a.poolSize=8192,a._augment=function(t){return t.__proto__=a.prototype,t},a.from=function(t,e,r){return h(null,t,e,r)},a.TYPED_ARRAY_SUPPORT&&(a.prototype.__proto__=Uint8Array.prototype,a.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&a[Symbol.species]===a&&Object.defineProperty(a,Symbol.species,{value:null,configurable:!0})),a.alloc=function(t,e,r){return function(t,e,r,n){return c(e),e<=0?u(t,e):void 0!==r?"string"==typeof n?u(t,e).fill(r,n):u(t,e).fill(r):u(t,e)}(null,t,e,r)},a.allocUnsafe=function(t){return f(null,t)},a.allocUnsafeSlow=function(t){return f(null,t)},a.isBuffer=function(t){return!(null==t||!t._isBuffer)},a.compare=function(t,e){if(!a.isBuffer(t)||!a.isBuffer(e))throw new TypeError("Arguments must be Buffers");if(t===e)return 0;for(var r=t.length,n=e.length,i=0,o=Math.min(r,n);i<o;++i)if(t[i]!==e[i]){r=t[i],n=e[i];break}return r<n?-1:n<r?1:0},a.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},a.concat=function(t,e){if(!o(t))throw new TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return a.alloc(0);var r;if(void 0===e)for(e=0,r=0;r<t.length;++r)e+=t[r].length;var n=a.allocUnsafe(e),i=0;for(r=0;r<t.length;++r){var s=t[r];if(!a.isBuffer(s))throw new TypeError('"list" argument must be an Array of Buffers');s.copy(n,i),i+=s.length}return n},a.byteLength=p,a.prototype._isBuffer=!0,a.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)g(this,e,e+1);return this},a.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)g(this,e,e+3),g(this,e+1,e+2);return this},a.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)g(this,e,e+7),g(this,e+1,e+6),g(this,e+2,e+5),g(this,e+3,e+4);return this},a.prototype.toString=function(){var t=0|this.length;return 0===t?"":0===arguments.length?O(this,0,t):m.apply(this,arguments)},a.prototype.equals=function(t){if(!a.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t||0===a.compare(this,t)},a.prototype.inspect=function(){var t="",r=e.INSPECT_MAX_BYTES;return this.length>0&&(t=this.toString("hex",0,r).match(/.{2}/g).join(" "),this.length>r&&(t+=" ... ")),"<Buffer "+t+">"},a.prototype.compare=function(t,e,r,n,i){if(!a.isBuffer(t))throw new TypeError("Argument must be a Buffer");if(void 0===e&&(e=0),void 0===r&&(r=t?t.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),e<0||r>t.length||n<0||i>this.length)throw new RangeError("out of range index");if(n>=i&&e>=r)return 0;if(n>=i)return-1;if(e>=r)return 1;if(this===t)return 0;for(var o=(i>>>=0)-(n>>>=0),s=(r>>>=0)-(e>>>=0),u=Math.min(o,s),h=this.slice(n,i),c=t.slice(e,r),f=0;f<u;++f)if(h[f]!==c[f]){o=h[f],s=c[f];break}return o<s?-1:s<o?1:0},a.prototype.includes=function(t,e,r){return-1!==this.indexOf(t,e,r)},a.prototype.indexOf=function(t,e,r){return v(this,t,e,r,!0)},a.prototype.lastIndexOf=function(t,e,r){return v(this,t,e,r,!1)},a.prototype.write=function(t,e,r,n){if(void 0===e)n="utf8",r=this.length,e=0;else if(void 0===r&&"string"==typeof e)n=e,r=this.length,e=0;else{if(!isFinite(e))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");e|=0,isFinite(r)?(r|=0,void 0===n&&(n="utf8")):(n=r,r=void 0)}var i=this.length-e;if((void 0===r||r>i)&&(r=i),t.length>0&&(r<0||e<0)||e>this.length)throw new RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var o=!1;;)switch(n){case"hex":return w(this,t,e,r);case"utf8":case"utf-8":return _(this,t,e,r);case"ascii":return M(this,t,e,r);case"latin1":case"binary":return b(this,t,e,r);case"base64":return R(this,t,e,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return S(this,t,e,r);default:if(o)throw new TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),o=!0}},a.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};function I(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(127&t[i]);return n}function k(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(t[i]);return n}function A(t,e,r){var n=t.length;(!e||e<0)&&(e=0),(!r||r<0||r>n)&&(r=n);for(var i="",o=e;o<r;++o)i+=D(t[o]);return i}function T(t,e,r){for(var n=t.slice(e,r),i="",o=0;o<n.length;o+=2)i+=String.fromCharCode(n[o]+256*n[o+1]);return i}function N(t,e,r){if(t%1!=0||t<0)throw new RangeError("offset is not uint");if(t+e>r)throw new RangeError("Trying to access beyond buffer length")}function C(t,e,r,n,i,o){if(!a.isBuffer(t))throw new TypeError('"buffer" argument must be a Buffer instance');if(e>i||e<o)throw new RangeError('"value" argument is out of bounds');if(r+n>t.length)throw new RangeError("Index out of range")}function x(t,e,r,n){e<0&&(e=65535+e+1);for(var i=0,o=Math.min(t.length-r,2);i<o;++i)t[r+i]=(e&255<<8*(n?i:1-i))>>>8*(n?i:1-i)}function B(t,e,r,n){e<0&&(e=4294967295+e+1);for(var i=0,o=Math.min(t.length-r,4);i<o;++i)t[r+i]=e>>>8*(n?i:3-i)&255}function P(t,e,r,n,i,o){if(r+n>t.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function L(t,e,r,n,o){return o||P(t,0,r,4),i.write(t,e,r,n,23,4),r+4}function j(t,e,r,n,o){return o||P(t,0,r,8),i.write(t,e,r,n,52,8),r+8}a.prototype.slice=function(t,e){var r,n=this.length;if((t=~~t)<0?(t+=n)<0&&(t=0):t>n&&(t=n),(e=void 0===e?n:~~e)<0?(e+=n)<0&&(e=0):e>n&&(e=n),e<t&&(e=t),a.TYPED_ARRAY_SUPPORT)(r=this.subarray(t,e)).__proto__=a.prototype;else{var i=e-t;r=new a(i,void 0);for(var o=0;o<i;++o)r[o]=this[o+t]}return r},a.prototype.readUIntLE=function(t,e,r){t|=0,e|=0,r||N(t,e,this.length);for(var n=this[t],i=1,o=0;++o<e&&(i*=256);)n+=this[t+o]*i;return n},a.prototype.readUIntBE=function(t,e,r){t|=0,e|=0,r||N(t,e,this.length);for(var n=this[t+--e],i=1;e>0&&(i*=256);)n+=this[t+--e]*i;return n},a.prototype.readUInt8=function(t,e){return e||N(t,1,this.length),this[t]},a.prototype.readUInt16LE=function(t,e){return e||N(t,2,this.length),this[t]|this[t+1]<<8},a.prototype.readUInt16BE=function(t,e){return e||N(t,2,this.length),this[t]<<8|this[t+1]},a.prototype.readUInt32LE=function(t,e){return e||N(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},a.prototype.readUInt32BE=function(t,e){return e||N(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},a.prototype.readIntLE=function(t,e,r){t|=0,e|=0,r||N(t,e,this.length);for(var n=this[t],i=1,o=0;++o<e&&(i*=256);)n+=this[t+o]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*e)),n},a.prototype.readIntBE=function(t,e,r){t|=0,e|=0,r||N(t,e,this.length);for(var n=e,i=1,o=this[t+--n];n>0&&(i*=256);)o+=this[t+--n]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*e)),o},a.prototype.readInt8=function(t,e){return e||N(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},a.prototype.readInt16LE=function(t,e){e||N(t,2,this.length);var r=this[t]|this[t+1]<<8;return 32768&r?4294901760|r:r},a.prototype.readInt16BE=function(t,e){e||N(t,2,this.length);var r=this[t+1]|this[t]<<8;return 32768&r?4294901760|r:r},a.prototype.readInt32LE=function(t,e){return e||N(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},a.prototype.readInt32BE=function(t,e){return e||N(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},a.prototype.readFloatLE=function(t,e){return e||N(t,4,this.length),i.read(this,t,!0,23,4)},a.prototype.readFloatBE=function(t,e){return e||N(t,4,this.length),i.read(this,t,!1,23,4)},a.prototype.readDoubleLE=function(t,e){return e||N(t,8,this.length),i.read(this,t,!0,52,8)},a.prototype.readDoubleBE=function(t,e){return e||N(t,8,this.length),i.read(this,t,!1,52,8)},a.prototype.writeUIntLE=function(t,e,r,n){(t=+t,e|=0,r|=0,n)||C(this,t,e,r,Math.pow(2,8*r)-1,0);var i=1,o=0;for(this[e]=255&t;++o<r&&(i*=256);)this[e+o]=t/i&255;return e+r},a.prototype.writeUIntBE=function(t,e,r,n){(t=+t,e|=0,r|=0,n)||C(this,t,e,r,Math.pow(2,8*r)-1,0);var i=r-1,o=1;for(this[e+i]=255&t;--i>=0&&(o*=256);)this[e+i]=t/o&255;return e+r},a.prototype.writeUInt8=function(t,e,r){return t=+t,e|=0,r||C(this,t,e,1,255,0),a.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),this[e]=255&t,e+1},a.prototype.writeUInt16LE=function(t,e,r){return t=+t,e|=0,r||C(this,t,e,2,65535,0),a.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8):x(this,t,e,!0),e+2},a.prototype.writeUInt16BE=function(t,e,r){return t=+t,e|=0,r||C(this,t,e,2,65535,0),a.TYPED_ARRAY_SUPPORT?(this[e]=t>>>8,this[e+1]=255&t):x(this,t,e,!1),e+2},a.prototype.writeUInt32LE=function(t,e,r){return t=+t,e|=0,r||C(this,t,e,4,4294967295,0),a.TYPED_ARRAY_SUPPORT?(this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t):B(this,t,e,!0),e+4},a.prototype.writeUInt32BE=function(t,e,r){return t=+t,e|=0,r||C(this,t,e,4,4294967295,0),a.TYPED_ARRAY_SUPPORT?(this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t):B(this,t,e,!1),e+4},a.prototype.writeIntLE=function(t,e,r,n){if(t=+t,e|=0,!n){var i=Math.pow(2,8*r-1);C(this,t,e,r,i-1,-i)}var o=0,s=1,u=0;for(this[e]=255&t;++o<r&&(s*=256);)t<0&&0===u&&0!==this[e+o-1]&&(u=1),this[e+o]=(t/s>>0)-u&255;return e+r},a.prototype.writeIntBE=function(t,e,r,n){if(t=+t,e|=0,!n){var i=Math.pow(2,8*r-1);C(this,t,e,r,i-1,-i)}var o=r-1,s=1,u=0;for(this[e+o]=255&t;--o>=0&&(s*=256);)t<0&&0===u&&0!==this[e+o+1]&&(u=1),this[e+o]=(t/s>>0)-u&255;return e+r},a.prototype.writeInt8=function(t,e,r){return t=+t,e|=0,r||C(this,t,e,1,127,-128),a.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),t<0&&(t=255+t+1),this[e]=255&t,e+1},a.prototype.writeInt16LE=function(t,e,r){return t=+t,e|=0,r||C(this,t,e,2,32767,-32768),a.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8):x(this,t,e,!0),e+2},a.prototype.writeInt16BE=function(t,e,r){return t=+t,e|=0,r||C(this,t,e,2,32767,-32768),a.TYPED_ARRAY_SUPPORT?(this[e]=t>>>8,this[e+1]=255&t):x(this,t,e,!1),e+2},a.prototype.writeInt32LE=function(t,e,r){return t=+t,e|=0,r||C(this,t,e,4,**********,-**********),a.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24):B(this,t,e,!0),e+4},a.prototype.writeInt32BE=function(t,e,r){return t=+t,e|=0,r||C(this,t,e,4,**********,-**********),t<0&&(t=4294967295+t+1),a.TYPED_ARRAY_SUPPORT?(this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t):B(this,t,e,!1),e+4},a.prototype.writeFloatLE=function(t,e,r){return L(this,t,e,!0,r)},a.prototype.writeFloatBE=function(t,e,r){return L(this,t,e,!1,r)},a.prototype.writeDoubleLE=function(t,e,r){return j(this,t,e,!0,r)},a.prototype.writeDoubleBE=function(t,e,r){return j(this,t,e,!1,r)},a.prototype.copy=function(t,e,r,n){if(r||(r=0),n||0===n||(n=this.length),e>=t.length&&(e=t.length),e||(e=0),n>0&&n<r&&(n=r),n===r)return 0;if(0===t.length||0===this.length)return 0;if(e<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("sourceStart out of bounds");if(n<0)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),t.length-e<n-r&&(n=t.length-e+r);var i,o=n-r;if(this===t&&r<e&&e<n)for(i=o-1;i>=0;--i)t[i+e]=this[i+r];else if(o<1e3||!a.TYPED_ARRAY_SUPPORT)for(i=0;i<o;++i)t[i+e]=this[i+r];else Uint8Array.prototype.set.call(t,this.subarray(r,r+o),e);return o},a.prototype.fill=function(t,e,r,n){if("string"==typeof t){if("string"==typeof e?(n=e,e=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),1===t.length){var i=t.charCodeAt(0);i<256&&(t=i)}if(void 0!==n&&"string"!=typeof n)throw new TypeError("encoding must be a string");if("string"==typeof n&&!a.isEncoding(n))throw new TypeError("Unknown encoding: "+n)}else"number"==typeof t&&(t&=255);if(e<0||this.length<e||this.length<r)throw new RangeError("Out of range index");if(r<=e)return this;var o;if(e>>>=0,r=void 0===r?this.length:r>>>0,t||(t=0),"number"==typeof t)for(o=e;o<r;++o)this[o]=t;else{var s=a.isBuffer(t)?t:q(new a(t,n).toString()),u=s.length;for(o=0;o<r-e;++o)this[o+e]=s[o%u]}return this};var U=/[^+\/0-9A-Za-z-_]/g;function D(t){return t<16?"0"+t.toString(16):t.toString(16)}function q(t,e){var r;e=e||1/0;for(var n=t.length,i=null,o=[],s=0;s<n;++s){if((r=t.charCodeAt(s))>55295&&r<57344){if(!i){if(r>56319){(e-=3)>-1&&o.push(239,191,189);continue}if(s+1===n){(e-=3)>-1&&o.push(239,191,189);continue}i=r;continue}if(r<56320){(e-=3)>-1&&o.push(239,191,189),i=r;continue}r=65536+(i-55296<<10|r-56320)}else i&&(e-=3)>-1&&o.push(239,191,189);if(i=null,r<128){if((e-=1)<0)break;o.push(r)}else if(r<2048){if((e-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((e-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((e-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return o}function W(t){return n.toByteArray(function(t){if((t=function(t){return t.trim?t.trim():t.replace(/^\s+|\s+$/g,"")}(t).replace(U,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function F(t,e,r,n){for(var i=0;i<n&&!(i+r>=e.length||i>=t.length);++i)e[i+r]=t[i];return i}}).call(this,r(7))},function(t,e,r){"use strict";var n=r(8);r.o(n,"payloadId")&&r.d(e,"payloadId",(function(){return n.payloadId}));n.isNode},function(t,e,r){"use strict";r.d(e,"a",(function(){return n}));r(4),r(1);function n(){return Date.now()*Math.pow(10,3)+Math.floor(Math.random()*Math.pow(10,3))}},function(t,e,r){"use strict"},function(t,e,r){"use strict";r(17)},function(t,e,r){"use strict";r(18),r(5),r(19),r(20)},function(t,e){},function(t,e,r){"use strict";var n=r(5);n.a;n.a},function(t,e){},function(t,e,r){"use strict"},function(t,e,r){"use strict"},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getWindowMetadata=void 0;const n=r(2);e.getWindowMetadata=function(){let t,e;try{t=n.getDocumentOrThrow(),e=n.getLocationOrThrow()}catch(t){return null}function r(...e){const r=t.getElementsByTagName("meta");for(let t=0;t<r.length;t++){const n=r[t],i=["itemprop","property","name"].map(t=>n.getAttribute(t)).filter(t=>!!t&&e.includes(t));if(i.length&&i){const t=n.getAttribute("content");if(t)return t}}return""}const i=function(){let e=r("name","og:site_name","og:title","twitter:title");return e||(e=t.title),e}();return{description:r("description","og:description","twitter:description","keywords"),url:e.origin,icons:function(){const r=t.getElementsByTagName("link"),n=[];for(let t=0;t<r.length;t++){const i=r[t],o=i.getAttribute("rel");if(o&&o.toLowerCase().indexOf("icon")>-1){const t=i.getAttribute("href");if(t)if(-1===t.toLowerCase().indexOf("https:")&&-1===t.toLowerCase().indexOf("http:")&&0!==t.indexOf("//")){let r=e.protocol+"//"+e.host;if(0===t.indexOf("/"))r+=t;else{const n=e.pathname.split("/");n.pop();r+=n.join("/")+"/"+t}n.push(r)}else if(0===t.indexOf("//")){const r=e.protocol+t;n.push(r)}else n.push(t)}}return n}(),name:i}}},function(t,e,r){"use strict";(function(t){r.d(e,"a",(function(){return l}));var n=function(){for(var t=0,e=0,r=arguments.length;e<r;e++)t+=arguments[e].length;var n=Array(t),i=0;for(e=0;e<r;e++)for(var o=arguments[e],s=0,u=o.length;s<u;s++,i++)n[i]=o[s];return n},i=function(t,e,r){this.name=t,this.version=e,this.os=r,this.type="browser"},o=function(e){this.version=e,this.type="node",this.name="node",this.os=t.platform},s=function(t,e,r,n){this.name=t,this.version=e,this.os=r,this.bot=n,this.type="bot-device"},u=function(){this.type="bot",this.bot=!0,this.name="bot",this.version=null,this.os=null},a=function(){this.type="react-native",this.name="react-native",this.version=null,this.os=null},h=/(nuhk|Googlebot|Yammybot|Openbot|Slurp|MSNBot|Ask\ Jeeves\/Teoma|ia_archiver)/,c=[["aol",/AOLShield\/([0-9\._]+)/],["edge",/Edge\/([0-9\._]+)/],["edge-ios",/EdgiOS\/([0-9\._]+)/],["yandexbrowser",/YaBrowser\/([0-9\._]+)/],["kakaotalk",/KAKAOTALK\s([0-9\.]+)/],["samsung",/SamsungBrowser\/([0-9\.]+)/],["silk",/\bSilk\/([0-9._-]+)\b/],["miui",/MiuiBrowser\/([0-9\.]+)$/],["beaker",/BeakerBrowser\/([0-9\.]+)/],["edge-chromium",/EdgA?\/([0-9\.]+)/],["chromium-webview",/(?!Chrom.*OPR)wv\).*Chrom(?:e|ium)\/([0-9\.]+)(:?\s|$)/],["chrome",/(?!Chrom.*OPR)Chrom(?:e|ium)\/([0-9\.]+)(:?\s|$)/],["phantomjs",/PhantomJS\/([0-9\.]+)(:?\s|$)/],["crios",/CriOS\/([0-9\.]+)(:?\s|$)/],["firefox",/Firefox\/([0-9\.]+)(?:\s|$)/],["fxios",/FxiOS\/([0-9\.]+)/],["opera-mini",/Opera Mini.*Version\/([0-9\.]+)/],["opera",/Opera\/([0-9\.]+)(?:\s|$)/],["opera",/OPR\/([0-9\.]+)(:?\s|$)/],["ie",/Trident\/7\.0.*rv\:([0-9\.]+).*\).*Gecko$/],["ie",/MSIE\s([0-9\.]+);.*Trident\/[4-7].0/],["ie",/MSIE\s(7\.0)/],["bb10",/BB10;\sTouch.*Version\/([0-9\.]+)/],["android",/Android\s([0-9\.]+)/],["ios",/Version\/([0-9\._]+).*Mobile.*Safari.*/],["safari",/Version\/([0-9\._]+).*Safari/],["facebook",/FBAV\/([0-9\.]+)/],["instagram",/Instagram\s([0-9\.]+)/],["ios-webview",/AppleWebKit\/([0-9\.]+).*Mobile/],["ios-webview",/AppleWebKit\/([0-9\.]+).*Gecko\)$/],["searchbot",/alexa|bot|crawl(er|ing)|facebookexternalhit|feedburner|google web preview|nagios|postrank|pingdom|slurp|spider|yahoo!|yandex/]],f=[["iOS",/iP(hone|od|ad)/],["Android OS",/Android/],["BlackBerry OS",/BlackBerry|BB10/],["Windows Mobile",/IEMobile/],["Amazon OS",/Kindle/],["Windows 3.11",/Win16/],["Windows 95",/(Windows 95)|(Win95)|(Windows_95)/],["Windows 98",/(Windows 98)|(Win98)/],["Windows 2000",/(Windows NT 5.0)|(Windows 2000)/],["Windows XP",/(Windows NT 5.1)|(Windows XP)/],["Windows Server 2003",/(Windows NT 5.2)/],["Windows Vista",/(Windows NT 6.0)/],["Windows 7",/(Windows NT 6.1)/],["Windows 8",/(Windows NT 6.2)/],["Windows 8.1",/(Windows NT 6.3)/],["Windows 10",/(Windows NT 10.0)/],["Windows ME",/Windows ME/],["Open BSD",/OpenBSD/],["Sun OS",/SunOS/],["Chrome OS",/CrOS/],["Linux",/(Linux)|(X11)/],["Mac OS",/(Mac_PowerPC)|(Macintosh)/],["QNX",/QNX/],["BeOS",/BeOS/],["OS/2",/OS\/2/]];function l(e){return e?p(e):"undefined"==typeof document&&"undefined"!=typeof navigator&&"ReactNative"===navigator.product?new a:"undefined"!=typeof navigator?p(navigator.userAgent):void 0!==t&&t.version?new o(t.version.slice(1)):null}function d(t){return""!==t&&c.reduce((function(e,r){var n=r[0],i=r[1];if(e)return e;var o=i.exec(t);return!!o&&[n,o]}),!1)}function p(t){var e=d(t);if(!e)return null;var r=e[0],o=e[1];if("searchbot"===r)return new u;var a=o[1]&&o[1].split(/[._]/).slice(0,3);a?a.length<3&&(a=n(a,function(t){for(var e=[],r=0;r<t;r++)e.push("0");return e}(3-a.length))):a=[];var c=a.join("."),l=function(t){for(var e=0,r=f.length;e<r;e++){var n=f[e],i=n[0];if(n[1].exec(t))return i}return null}(t),p=h.exec(t);return p&&p[1]?new s(r,c,l,p[1]):new i(r,c,l)}}).call(this,r(9))},function(t,e,r){(function(e){var n=r(10).strict;t.exports=function(t){if(n(t)){var r=e.from(t.buffer);return t.byteLength!==t.buffer.byteLength&&(r=r.slice(t.byteOffset,t.byteOffset+t.byteLength)),r}return e.from(t)}}).call(this,r(12).Buffer)},function(t,e,r){(function(n,i){var o;
/**
 * [js-sha3]{@link https://github.com/emn178/js-sha3}
 *
 * @version 0.8.0
 * <AUTHOR> Yi-Cyuan [<EMAIL>]
 * @copyright Chen, Yi-Cyuan 2015-2018
 * @license MIT
 */!function(){"use strict";var s="input is invalid type",u="object"==typeof window,a=u?window:{};a.JS_SHA3_NO_WINDOW&&(u=!1);var h=!u&&"object"==typeof self;!a.JS_SHA3_NO_NODE_JS&&"object"==typeof n&&n.versions&&n.versions.node?a=i:h&&(a=self);var c=!a.JS_SHA3_NO_COMMON_JS&&"object"==typeof t&&t.exports,f=r(36),l=!a.JS_SHA3_NO_ARRAY_BUFFER&&"undefined"!=typeof ArrayBuffer,d="0123456789abcdef".split(""),p=[4,1024,262144,67108864],m=[0,8,16,24],g=[1,0,32898,0,32906,**********,**********,**********,32907,0,**********,0,**********,**********,32777,**********,138,0,136,0,**********,0,**********,0,**********,0,139,**********,32905,**********,32771,**********,32770,**********,128,**********,32778,0,**********,**********,**********,**********,32896,**********,**********,0,**********,**********],v=[224,256,384,512],y=[128,256],w=["hex","buffer","arrayBuffer","array","digest"],_={128:168,256:136};!a.JS_SHA3_NO_NODE_JS&&Array.isArray||(Array.isArray=function(t){return"[object Array]"===Object.prototype.toString.call(t)}),!l||!a.JS_SHA3_NO_ARRAY_BUFFER_IS_VIEW&&ArrayBuffer.isView||(ArrayBuffer.isView=function(t){return"object"==typeof t&&t.buffer&&t.buffer.constructor===ArrayBuffer});for(var M=function(t,e,r){return function(n){return new L(t,e,t).update(n)[r]()}},b=function(t,e,r){return function(n,i){return new L(t,e,i).update(n)[r]()}},R=function(t,e,r){return function(e,n,i,o){return k["cshake"+t].update(e,n,i,o)[r]()}},S=function(t,e,r){return function(e,n,i,o){return k["kmac"+t].update(e,n,i,o)[r]()}},E=function(t,e,r,n){for(var i=0;i<w.length;++i){var o=w[i];t[o]=e(r,n,o)}return t},O=function(t,e){var r=M(t,e,"hex");return r.create=function(){return new L(t,e,t)},r.update=function(t){return r.create().update(t)},E(r,M,t,e)},I=[{name:"keccak",padding:[1,256,65536,16777216],bits:v,createMethod:O},{name:"sha3",padding:[6,1536,393216,100663296],bits:v,createMethod:O},{name:"shake",padding:[31,7936,2031616,520093696],bits:y,createMethod:function(t,e){var r=b(t,e,"hex");return r.create=function(r){return new L(t,e,r)},r.update=function(t,e){return r.create(e).update(t)},E(r,b,t,e)}},{name:"cshake",padding:p,bits:y,createMethod:function(t,e){var r=_[t],n=R(t,0,"hex");return n.create=function(n,i,o){return i||o?new L(t,e,n).bytepad([i,o],r):k["shake"+t].create(n)},n.update=function(t,e,r,i){return n.create(e,r,i).update(t)},E(n,R,t,e)}},{name:"kmac",padding:p,bits:y,createMethod:function(t,e){var r=_[t],n=S(t,0,"hex");return n.create=function(n,i,o){return new j(t,e,i).bytepad(["KMAC",o],r).bytepad([n],r)},n.update=function(t,e,r,i){return n.create(t,r,i).update(e)},E(n,S,t,e)}}],k={},A=[],T=0;T<I.length;++T)for(var N=I[T],C=N.bits,x=0;x<C.length;++x){var B=N.name+"_"+C[x];if(A.push(B),k[B]=N.createMethod(C[x],N.padding),"sha3"!==N.name){var P=N.name+C[x];A.push(P),k[P]=k[B]}}function L(t,e,r){this.blocks=[],this.s=[],this.padding=e,this.outputBits=r,this.reset=!0,this.finalized=!1,this.block=0,this.start=0,this.blockCount=1600-(t<<1)>>5,this.byteCount=this.blockCount<<2,this.outputBlocks=r>>5,this.extraBytes=(31&r)>>3;for(var n=0;n<50;++n)this.s[n]=0}function j(t,e,r){L.call(this,t,e,r)}L.prototype.update=function(t){if(this.finalized)throw new Error("finalize already called");var e,r=typeof t;if("string"!==r){if("object"!==r)throw new Error(s);if(null===t)throw new Error(s);if(l&&t.constructor===ArrayBuffer)t=new Uint8Array(t);else if(!(Array.isArray(t)||l&&ArrayBuffer.isView(t)))throw new Error(s);e=!0}for(var n,i,o=this.blocks,u=this.byteCount,a=t.length,h=this.blockCount,c=0,f=this.s;c<a;){if(this.reset)for(this.reset=!1,o[0]=this.block,n=1;n<h+1;++n)o[n]=0;if(e)for(n=this.start;c<a&&n<u;++c)o[n>>2]|=t[c]<<m[3&n++];else for(n=this.start;c<a&&n<u;++c)(i=t.charCodeAt(c))<128?o[n>>2]|=i<<m[3&n++]:i<2048?(o[n>>2]|=(192|i>>6)<<m[3&n++],o[n>>2]|=(128|63&i)<<m[3&n++]):i<55296||i>=57344?(o[n>>2]|=(224|i>>12)<<m[3&n++],o[n>>2]|=(128|i>>6&63)<<m[3&n++],o[n>>2]|=(128|63&i)<<m[3&n++]):(i=65536+((1023&i)<<10|1023&t.charCodeAt(++c)),o[n>>2]|=(240|i>>18)<<m[3&n++],o[n>>2]|=(128|i>>12&63)<<m[3&n++],o[n>>2]|=(128|i>>6&63)<<m[3&n++],o[n>>2]|=(128|63&i)<<m[3&n++]);if(this.lastByteIndex=n,n>=u){for(this.start=n-u,this.block=o[h],n=0;n<h;++n)f[n]^=o[n];U(f),this.reset=!0}else this.start=n}return this},L.prototype.encode=function(t,e){var r=255&t,n=1,i=[r];for(r=255&(t>>=8);r>0;)i.unshift(r),r=255&(t>>=8),++n;return e?i.push(n):i.unshift(n),this.update(i),i.length},L.prototype.encodeString=function(t){var e,r=typeof t;if("string"!==r){if("object"!==r)throw new Error(s);if(null===t)throw new Error(s);if(l&&t.constructor===ArrayBuffer)t=new Uint8Array(t);else if(!(Array.isArray(t)||l&&ArrayBuffer.isView(t)))throw new Error(s);e=!0}var n=0,i=t.length;if(e)n=i;else for(var o=0;o<t.length;++o){var u=t.charCodeAt(o);u<128?n+=1:u<2048?n+=2:u<55296||u>=57344?n+=3:(u=65536+((1023&u)<<10|1023&t.charCodeAt(++o)),n+=4)}return n+=this.encode(8*n),this.update(t),n},L.prototype.bytepad=function(t,e){for(var r=this.encode(e),n=0;n<t.length;++n)r+=this.encodeString(t[n]);var i=e-r%e,o=[];return o.length=i,this.update(o),this},L.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var t=this.blocks,e=this.lastByteIndex,r=this.blockCount,n=this.s;if(t[e>>2]|=this.padding[3&e],this.lastByteIndex===this.byteCount)for(t[0]=t[r],e=1;e<r+1;++e)t[e]=0;for(t[r-1]|=**********,e=0;e<r;++e)n[e]^=t[e];U(n)}},L.prototype.toString=L.prototype.hex=function(){this.finalize();for(var t,e=this.blockCount,r=this.s,n=this.outputBlocks,i=this.extraBytes,o=0,s=0,u="";s<n;){for(o=0;o<e&&s<n;++o,++s)t=r[o],u+=d[t>>4&15]+d[15&t]+d[t>>12&15]+d[t>>8&15]+d[t>>20&15]+d[t>>16&15]+d[t>>28&15]+d[t>>24&15];s%e==0&&(U(r),o=0)}return i&&(t=r[o],u+=d[t>>4&15]+d[15&t],i>1&&(u+=d[t>>12&15]+d[t>>8&15]),i>2&&(u+=d[t>>20&15]+d[t>>16&15])),u},L.prototype.arrayBuffer=function(){this.finalize();var t,e=this.blockCount,r=this.s,n=this.outputBlocks,i=this.extraBytes,o=0,s=0,u=this.outputBits>>3;t=i?new ArrayBuffer(n+1<<2):new ArrayBuffer(u);for(var a=new Uint32Array(t);s<n;){for(o=0;o<e&&s<n;++o,++s)a[s]=r[o];s%e==0&&U(r)}return i&&(a[o]=r[o],t=t.slice(0,u)),t},L.prototype.buffer=L.prototype.arrayBuffer,L.prototype.digest=L.prototype.array=function(){this.finalize();for(var t,e,r=this.blockCount,n=this.s,i=this.outputBlocks,o=this.extraBytes,s=0,u=0,a=[];u<i;){for(s=0;s<r&&u<i;++s,++u)t=u<<2,e=n[s],a[t]=255&e,a[t+1]=e>>8&255,a[t+2]=e>>16&255,a[t+3]=e>>24&255;u%r==0&&U(n)}return o&&(t=u<<2,e=n[s],a[t]=255&e,o>1&&(a[t+1]=e>>8&255),o>2&&(a[t+2]=e>>16&255)),a},j.prototype=new L,j.prototype.finalize=function(){return this.encode(this.outputBits,!0),L.prototype.finalize.call(this)};var U=function(t){var e,r,n,i,o,s,u,a,h,c,f,l,d,p,m,v,y,w,_,M,b,R,S,E,O,I,k,A,T,N,C,x,B,P,L,j,U,D,q,W,F,Y,J,$,z,H,V,Z,Q,G,K,X,tt,et,rt,nt,it,ot,st,ut,at,ht,ct;for(n=0;n<48;n+=2)i=t[0]^t[10]^t[20]^t[30]^t[40],o=t[1]^t[11]^t[21]^t[31]^t[41],s=t[2]^t[12]^t[22]^t[32]^t[42],u=t[3]^t[13]^t[23]^t[33]^t[43],a=t[4]^t[14]^t[24]^t[34]^t[44],h=t[5]^t[15]^t[25]^t[35]^t[45],c=t[6]^t[16]^t[26]^t[36]^t[46],f=t[7]^t[17]^t[27]^t[37]^t[47],e=(l=t[8]^t[18]^t[28]^t[38]^t[48])^(s<<1|u>>>31),r=(d=t[9]^t[19]^t[29]^t[39]^t[49])^(u<<1|s>>>31),t[0]^=e,t[1]^=r,t[10]^=e,t[11]^=r,t[20]^=e,t[21]^=r,t[30]^=e,t[31]^=r,t[40]^=e,t[41]^=r,e=i^(a<<1|h>>>31),r=o^(h<<1|a>>>31),t[2]^=e,t[3]^=r,t[12]^=e,t[13]^=r,t[22]^=e,t[23]^=r,t[32]^=e,t[33]^=r,t[42]^=e,t[43]^=r,e=s^(c<<1|f>>>31),r=u^(f<<1|c>>>31),t[4]^=e,t[5]^=r,t[14]^=e,t[15]^=r,t[24]^=e,t[25]^=r,t[34]^=e,t[35]^=r,t[44]^=e,t[45]^=r,e=a^(l<<1|d>>>31),r=h^(d<<1|l>>>31),t[6]^=e,t[7]^=r,t[16]^=e,t[17]^=r,t[26]^=e,t[27]^=r,t[36]^=e,t[37]^=r,t[46]^=e,t[47]^=r,e=c^(i<<1|o>>>31),r=f^(o<<1|i>>>31),t[8]^=e,t[9]^=r,t[18]^=e,t[19]^=r,t[28]^=e,t[29]^=r,t[38]^=e,t[39]^=r,t[48]^=e,t[49]^=r,p=t[0],m=t[1],H=t[11]<<4|t[10]>>>28,V=t[10]<<4|t[11]>>>28,A=t[20]<<3|t[21]>>>29,T=t[21]<<3|t[20]>>>29,ut=t[31]<<9|t[30]>>>23,at=t[30]<<9|t[31]>>>23,Y=t[40]<<18|t[41]>>>14,J=t[41]<<18|t[40]>>>14,P=t[2]<<1|t[3]>>>31,L=t[3]<<1|t[2]>>>31,v=t[13]<<12|t[12]>>>20,y=t[12]<<12|t[13]>>>20,Z=t[22]<<10|t[23]>>>22,Q=t[23]<<10|t[22]>>>22,N=t[33]<<13|t[32]>>>19,C=t[32]<<13|t[33]>>>19,ht=t[42]<<2|t[43]>>>30,ct=t[43]<<2|t[42]>>>30,et=t[5]<<30|t[4]>>>2,rt=t[4]<<30|t[5]>>>2,j=t[14]<<6|t[15]>>>26,U=t[15]<<6|t[14]>>>26,w=t[25]<<11|t[24]>>>21,_=t[24]<<11|t[25]>>>21,G=t[34]<<15|t[35]>>>17,K=t[35]<<15|t[34]>>>17,x=t[45]<<29|t[44]>>>3,B=t[44]<<29|t[45]>>>3,E=t[6]<<28|t[7]>>>4,O=t[7]<<28|t[6]>>>4,nt=t[17]<<23|t[16]>>>9,it=t[16]<<23|t[17]>>>9,D=t[26]<<25|t[27]>>>7,q=t[27]<<25|t[26]>>>7,M=t[36]<<21|t[37]>>>11,b=t[37]<<21|t[36]>>>11,X=t[47]<<24|t[46]>>>8,tt=t[46]<<24|t[47]>>>8,$=t[8]<<27|t[9]>>>5,z=t[9]<<27|t[8]>>>5,I=t[18]<<20|t[19]>>>12,k=t[19]<<20|t[18]>>>12,ot=t[29]<<7|t[28]>>>25,st=t[28]<<7|t[29]>>>25,W=t[38]<<8|t[39]>>>24,F=t[39]<<8|t[38]>>>24,R=t[48]<<14|t[49]>>>18,S=t[49]<<14|t[48]>>>18,t[0]=p^~v&w,t[1]=m^~y&_,t[10]=E^~I&A,t[11]=O^~k&T,t[20]=P^~j&D,t[21]=L^~U&q,t[30]=$^~H&Z,t[31]=z^~V&Q,t[40]=et^~nt&ot,t[41]=rt^~it&st,t[2]=v^~w&M,t[3]=y^~_&b,t[12]=I^~A&N,t[13]=k^~T&C,t[22]=j^~D&W,t[23]=U^~q&F,t[32]=H^~Z&G,t[33]=V^~Q&K,t[42]=nt^~ot&ut,t[43]=it^~st&at,t[4]=w^~M&R,t[5]=_^~b&S,t[14]=A^~N&x,t[15]=T^~C&B,t[24]=D^~W&Y,t[25]=q^~F&J,t[34]=Z^~G&X,t[35]=Q^~K&tt,t[44]=ot^~ut&ht,t[45]=st^~at&ct,t[6]=M^~R&p,t[7]=b^~S&m,t[16]=N^~x&E,t[17]=C^~B&O,t[26]=W^~Y&P,t[27]=F^~J&L,t[36]=G^~X&$,t[37]=K^~tt&z,t[46]=ut^~ht&et,t[47]=at^~ct&rt,t[8]=R^~p&v,t[9]=S^~m&y,t[18]=x^~E&I,t[19]=B^~O&k,t[28]=Y^~P&j,t[29]=J^~L&U,t[38]=X^~$&H,t[39]=tt^~z&V,t[48]=ht^~et&nt,t[49]=ct^~rt&it,t[0]^=g[n],t[1]^=g[n+1]};if(c)t.exports=k;else{for(T=0;T<A.length;++T)a[A[T]]=k[A[T]];f&&(void 0===(o=function(){return k}.call(e,r,e,t))||(t.exports=o))}}()}).call(this,r(9),r(7))},function(t,e,r){"use strict";r(1),r(4);var n=r(13);r.o(n,"payloadId")&&r.d(e,"payloadId",(function(){return n.payloadId}));var i=r(14);r.d(e,"payloadId",(function(){return i.a}));r(15),r(16),r(21),r(22)},function(t,e,r){"use strict";e.a=class{constructor(){this._eventEmitters=[],"undefined"!=typeof window&&void 0!==window.addEventListener&&(window.addEventListener("online",()=>this.trigger("online")),window.addEventListener("offline",()=>this.trigger("offline")))}on(t,e){this._eventEmitters.push({event:t,callback:e})}trigger(t){let e=[];t&&(e=this._eventEmitters.filter(e=>e.event===t)),e.forEach(t=>{t.callback()})}}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});const n=r(30),i=r(3),o=n.__importDefault(r(42)),s=r(44),u=n.__importDefault(r(45)),a=n.__importDefault(r(46)),h=r(47);e.default=class{constructor(t){if(this.protocol="wc",this.version=1,this._bridge="",this._key=null,this._clientId="",this._clientMeta=null,this._peerId="",this._peerMeta=null,this._handshakeId=0,this._handshakeTopic="",this._connected=!1,this._accounts=[],this._chainId=0,this._networkId=0,this._rpcUrl="",this._eventManager=new u.default,this._clientMeta=(0,i.getClientMeta)()||t.connectorOpts.clientMeta||null,this._cryptoLib=t.cryptoLib,this._sessionStorage=t.sessionStorage||new a.default(t.connectorOpts.storageId),this._qrcodeModal=t.connectorOpts.qrcodeModal,this._qrcodeModalOptions=t.connectorOpts.qrcodeModalOptions,this._signingMethods=[...i.signingMethods,...t.connectorOpts.signingMethods||[]],!t.connectorOpts.bridge&&!t.connectorOpts.uri&&!t.connectorOpts.session)throw new Error(s.ERROR_MISSING_REQUIRED);t.connectorOpts.bridge&&(this.bridge=(0,h.getBridgeUrl)(t.connectorOpts.bridge)),t.connectorOpts.uri&&(this.uri=t.connectorOpts.uri);const e=t.connectorOpts.session||this._getStorageSession();e&&(this.session=e),this.handshakeId&&this._subscribeToSessionResponse(this.handshakeId,"Session request rejected"),this._transport=t.transport||new o.default({protocol:this.protocol,version:this.version,url:this.bridge,subscriptions:[this.clientId]}),this._subscribeToInternalEvents(),this._initTransport(),t.connectorOpts.uri&&this._subscribeToSessionRequest(),t.pushServerOpts&&this._registerPushServer(t.pushServerOpts)}set bridge(t){t&&(this._bridge=t)}get bridge(){return this._bridge}set key(t){if(!t)return;const e=(0,i.convertHexToArrayBuffer)(t);this._key=e}get key(){if(this._key){return(0,i.convertArrayBufferToHex)(this._key,!0)}return""}set clientId(t){t&&(this._clientId=t)}get clientId(){let t=this._clientId;return t||(t=this._clientId=(0,i.uuid)()),this._clientId}set peerId(t){t&&(this._peerId=t)}get peerId(){return this._peerId}set clientMeta(t){}get clientMeta(){let t=this._clientMeta;return t||(t=this._clientMeta=(0,i.getClientMeta)()),t}set peerMeta(t){this._peerMeta=t}get peerMeta(){return this._peerMeta}set handshakeTopic(t){t&&(this._handshakeTopic=t)}get handshakeTopic(){return this._handshakeTopic}set handshakeId(t){t&&(this._handshakeId=t)}get handshakeId(){return this._handshakeId}get uri(){return this._formatUri()}set uri(t){if(!t)return;const{handshakeTopic:e,bridge:r,key:n}=this._parseUri(t);this.handshakeTopic=e,this.bridge=r,this.key=n}set chainId(t){this._chainId=t}get chainId(){return this._chainId}set networkId(t){this._networkId=t}get networkId(){return this._networkId}set accounts(t){this._accounts=t}get accounts(){return this._accounts}set rpcUrl(t){this._rpcUrl=t}get rpcUrl(){return this._rpcUrl}set connected(t){}get connected(){return this._connected}set pending(t){}get pending(){return!!this._handshakeTopic}get session(){return{connected:this.connected,accounts:this.accounts,chainId:this.chainId,bridge:this.bridge,key:this.key,clientId:this.clientId,clientMeta:this.clientMeta,peerId:this.peerId,peerMeta:this.peerMeta,handshakeId:this.handshakeId,handshakeTopic:this.handshakeTopic}}set session(t){t&&(this._connected=t.connected,this.accounts=t.accounts,this.chainId=t.chainId,this.bridge=t.bridge,this.key=t.key,this.clientId=t.clientId,this.clientMeta=t.clientMeta,this.peerId=t.peerId,this.peerMeta=t.peerMeta,this.handshakeId=t.handshakeId,this.handshakeTopic=t.handshakeTopic)}on(t,e){const r={event:t,callback:e};this._eventManager.subscribe(r)}off(t){this._eventManager.unsubscribe(t)}createInstantRequest(t){return n.__awaiter(this,void 0,void 0,(function*(){this._key=yield this._generateKey();const e=this._formatRequest({method:"wc_instantRequest",params:[{peerId:this.clientId,peerMeta:this.clientMeta,request:this._formatRequest(t)}]});this.handshakeId=e.id,this.handshakeTopic=(0,i.uuid)(),this._eventManager.trigger({event:"display_uri",params:[this.uri]}),this.on("modal_closed",()=>{throw new Error(s.ERROR_QRCODE_MODAL_USER_CLOSED)});const r=()=>{this.killSession()};try{const t=yield this._sendCallRequest(e);return t&&r(),t}catch(t){throw r(),t}}))}connect(t){return n.__awaiter(this,void 0,void 0,(function*(){if(!this._qrcodeModal)throw new Error(s.ERROR_QRCODE_MODAL_NOT_PROVIDED);return this.connected?{chainId:this.chainId,accounts:this.accounts}:(yield this.createSession(t),new Promise((t,e)=>n.__awaiter(this,void 0,void 0,(function*(){this.on("modal_closed",()=>e(new Error(s.ERROR_QRCODE_MODAL_USER_CLOSED))),this.on("connect",(r,n)=>{if(r)return e(r);t(n.params[0])})}))))}))}createSession(t){return n.__awaiter(this,void 0,void 0,(function*(){if(this._connected)throw new Error(s.ERROR_SESSION_CONNECTED);if(this.pending)return;this._key=yield this._generateKey();const e=this._formatRequest({method:"wc_sessionRequest",params:[{peerId:this.clientId,peerMeta:this.clientMeta,chainId:t&&t.chainId?t.chainId:null}]});this.handshakeId=e.id,this.handshakeTopic=(0,i.uuid)(),this._sendSessionRequest(e,"Session update rejected",{topic:this.handshakeTopic}),this._eventManager.trigger({event:"display_uri",params:[this.uri]})}))}approveSession(t){if(this._connected)throw new Error(s.ERROR_SESSION_CONNECTED);this.chainId=t.chainId,this.accounts=t.accounts,this.networkId=t.networkId||0,this.rpcUrl=t.rpcUrl||"";const e={approved:!0,chainId:this.chainId,networkId:this.networkId,accounts:this.accounts,rpcUrl:this.rpcUrl,peerId:this.clientId,peerMeta:this.clientMeta},r={id:this.handshakeId,jsonrpc:"2.0",result:e};this._sendResponse(r),this._connected=!0,this._setStorageSession(),this._eventManager.trigger({event:"connect",params:[{peerId:this.peerId,peerMeta:this.peerMeta,chainId:this.chainId,accounts:this.accounts}]})}rejectSession(t){if(this._connected)throw new Error(s.ERROR_SESSION_CONNECTED);const e=t&&t.message?t.message:s.ERROR_SESSION_REJECTED,r=this._formatResponse({id:this.handshakeId,error:{message:e}});this._sendResponse(r),this._connected=!1,this._eventManager.trigger({event:"disconnect",params:[{message:e}]}),this._removeStorageSession()}updateSession(t){if(!this._connected)throw new Error(s.ERROR_SESSION_DISCONNECTED);this.chainId=t.chainId,this.accounts=t.accounts,this.networkId=t.networkId||0,this.rpcUrl=t.rpcUrl||"";const e={approved:!0,chainId:this.chainId,networkId:this.networkId,accounts:this.accounts,rpcUrl:this.rpcUrl},r=this._formatRequest({method:"wc_sessionUpdate",params:[e]});this._sendSessionRequest(r,"Session update rejected"),this._eventManager.trigger({event:"session_update",params:[{chainId:this.chainId,accounts:this.accounts}]}),this._manageStorageSession()}killSession(t){return n.__awaiter(this,void 0,void 0,(function*(){const e=t?t.message:"Session Disconnected",r=this._formatRequest({method:"wc_sessionUpdate",params:[{approved:!1,chainId:null,networkId:null,accounts:null}]});yield this._sendRequest(r),this._handleSessionDisconnect(e)}))}sendTransaction(t){return n.__awaiter(this,void 0,void 0,(function*(){if(!this._connected)throw new Error(s.ERROR_SESSION_DISCONNECTED);const e=(0,i.parseTransactionData)(t),r=this._formatRequest({method:"eth_sendTransaction",params:[e]});return yield this._sendCallRequest(r)}))}signTransaction(t){return n.__awaiter(this,void 0,void 0,(function*(){if(!this._connected)throw new Error(s.ERROR_SESSION_DISCONNECTED);const e=(0,i.parseTransactionData)(t),r=this._formatRequest({method:"eth_signTransaction",params:[e]});return yield this._sendCallRequest(r)}))}signMessage(t){return n.__awaiter(this,void 0,void 0,(function*(){if(!this._connected)throw new Error(s.ERROR_SESSION_DISCONNECTED);const e=this._formatRequest({method:"eth_sign",params:t});return yield this._sendCallRequest(e)}))}signPersonalMessage(t){return n.__awaiter(this,void 0,void 0,(function*(){if(!this._connected)throw new Error(s.ERROR_SESSION_DISCONNECTED);t=(0,i.parsePersonalSign)(t);const e=this._formatRequest({method:"personal_sign",params:t});return yield this._sendCallRequest(e)}))}signTypedData(t){return n.__awaiter(this,void 0,void 0,(function*(){if(!this._connected)throw new Error(s.ERROR_SESSION_DISCONNECTED);const e=this._formatRequest({method:"eth_signTypedData",params:t});return yield this._sendCallRequest(e)}))}updateChain(t){return n.__awaiter(this,void 0,void 0,(function*(){if(!this._connected)throw new Error("Session currently disconnected");const e=this._formatRequest({method:"wallet_updateChain",params:[t]});return yield this._sendCallRequest(e)}))}unsafeSend(t,e){return this._sendRequest(t,e),this._eventManager.trigger({event:"call_request_sent",params:[{request:t,options:e}]}),new Promise((e,r)=>{this._subscribeToResponse(t.id,(t,n)=>{if(t)r(t);else{if(!n)throw new Error(s.ERROR_MISSING_JSON_RPC);e(n)}})})}sendCustomRequest(t,e){return n.__awaiter(this,void 0,void 0,(function*(){if(!this._connected)throw new Error(s.ERROR_SESSION_DISCONNECTED);switch(t.method){case"eth_accounts":return this.accounts;case"eth_chainId":return(0,i.convertNumberToHex)(this.chainId);case"eth_sendTransaction":case"eth_signTransaction":t.params&&(t.params[0]=(0,i.parseTransactionData)(t.params[0]));break;case"personal_sign":t.params&&(t.params=(0,i.parsePersonalSign)(t.params))}const r=this._formatRequest(t);return yield this._sendCallRequest(r,e)}))}approveRequest(t){if(!(0,i.isJsonRpcResponseSuccess)(t))throw new Error(s.ERROR_MISSING_RESULT);{const e=this._formatResponse(t);this._sendResponse(e)}}rejectRequest(t){if(!(0,i.isJsonRpcResponseError)(t))throw new Error(s.ERROR_MISSING_ERROR);{const e=this._formatResponse(t);this._sendResponse(e)}}transportClose(){this._transport.close()}_sendRequest(t,e){return n.__awaiter(this,void 0,void 0,(function*(){const r=this._formatRequest(t),n=yield this._encrypt(r),o=void 0!==(null==e?void 0:e.topic)?e.topic:this.peerId,s=JSON.stringify(n),u=void 0!==(null==e?void 0:e.forcePushNotification)?!e.forcePushNotification:(0,i.isSilentPayload)(r);this._transport.send(s,o,u)}))}_sendResponse(t){return n.__awaiter(this,void 0,void 0,(function*(){const e=yield this._encrypt(t),r=this.peerId,n=JSON.stringify(e);this._transport.send(n,r,!0)}))}_sendSessionRequest(t,e,r){return n.__awaiter(this,void 0,void 0,(function*(){this._sendRequest(t,r),this._subscribeToSessionResponse(t.id,e)}))}_sendCallRequest(t,e){return this._sendRequest(t,e),this._eventManager.trigger({event:"call_request_sent",params:[{request:t,options:e}]}),this._subscribeToCallResponse(t.id)}_formatRequest(t){if(void 0===t.method)throw new Error(s.ERROR_MISSING_METHOD);return{id:void 0===t.id?(0,i.payloadId)():t.id,jsonrpc:"2.0",method:t.method,params:void 0===t.params?[]:t.params}}_formatResponse(t){if(void 0===t.id)throw new Error(s.ERROR_MISSING_ID);const e={id:t.id,jsonrpc:"2.0"};if((0,i.isJsonRpcResponseError)(t)){const r=(0,i.formatRpcError)(t.error);return Object.assign(Object.assign(Object.assign({},e),t),{error:r})}if((0,i.isJsonRpcResponseSuccess)(t)){return Object.assign(Object.assign({},e),t)}throw new Error(s.ERROR_INVALID_RESPONSE)}_handleSessionDisconnect(t){const e=t||"Session Disconnected";this._connected||(this._qrcodeModal&&this._qrcodeModal.close(),(0,i.removeLocal)(i.mobileLinkChoiceKey)),this._connected&&(this._connected=!1),this._handshakeId&&(this._handshakeId=0),this._handshakeTopic&&(this._handshakeTopic=""),this._peerId&&(this._peerId=""),this._eventManager.trigger({event:"disconnect",params:[{message:e}]}),this._removeStorageSession(),this.transportClose()}_handleSessionResponse(t,e){e&&e.approved?(this._connected?(e.chainId&&(this.chainId=e.chainId),e.accounts&&(this.accounts=e.accounts),this._eventManager.trigger({event:"session_update",params:[{chainId:this.chainId,accounts:this.accounts}]})):(this._connected=!0,e.chainId&&(this.chainId=e.chainId),e.accounts&&(this.accounts=e.accounts),e.peerId&&!this.peerId&&(this.peerId=e.peerId),e.peerMeta&&!this.peerMeta&&(this.peerMeta=e.peerMeta),this._eventManager.trigger({event:"connect",params:[{peerId:this.peerId,peerMeta:this.peerMeta,chainId:this.chainId,accounts:this.accounts}]})),this._manageStorageSession()):this._handleSessionDisconnect(t)}_handleIncomingMessages(t){return n.__awaiter(this,void 0,void 0,(function*(){if(![this.clientId,this.handshakeTopic].includes(t.topic))return;let e;try{e=JSON.parse(t.payload)}catch(t){return}const r=yield this._decrypt(e);r&&this._eventManager.trigger(r)}))}_subscribeToSessionRequest(){this._transport.subscribe(this.handshakeTopic)}_subscribeToResponse(t,e){this.on("response:"+t,e)}_subscribeToSessionResponse(t,e){this._subscribeToResponse(t,(t,r)=>{t?this._handleSessionResponse(t.message):(0,i.isJsonRpcResponseSuccess)(r)?this._handleSessionResponse(e,r.result):r.error&&r.error.message?this._handleSessionResponse(r.error.message):this._handleSessionResponse(e)})}_subscribeToCallResponse(t){return new Promise((e,r)=>{this._subscribeToResponse(t,(t,n)=>{t?r(t):(0,i.isJsonRpcResponseSuccess)(n)?e(n.result):n.error&&n.error.message?r(n.error):r(new Error(s.ERROR_INVALID_RESPONSE))})})}_subscribeToInternalEvents(){this.on("display_uri",()=>{this._qrcodeModal&&this._qrcodeModal.open(this.uri,()=>{this._eventManager.trigger({event:"modal_closed",params:[]})},this._qrcodeModalOptions)}),this.on("connect",()=>{this._qrcodeModal&&this._qrcodeModal.close()}),this.on("call_request_sent",(t,e)=>{const{request:r}=e.params[0];if((0,i.isMobile)()&&this._signingMethods.includes(r.method)){const t=(0,i.getLocal)(i.mobileLinkChoiceKey);t&&(window.location.href=t.href)}}),this.on("wc_sessionRequest",(t,e)=>{t&&this._eventManager.trigger({event:"error",params:[{code:"SESSION_REQUEST_ERROR",message:t.toString()}]}),this.handshakeId=e.id,this.peerId=e.params[0].peerId,this.peerMeta=e.params[0].peerMeta;const r=Object.assign(Object.assign({},e),{method:"session_request"});this._eventManager.trigger(r)}),this.on("wc_sessionUpdate",(t,e)=>{t&&this._handleSessionResponse(t.message),this._handleSessionResponse("Session disconnected",e.params[0])})}_initTransport(){this._transport.on("message",t=>this._handleIncomingMessages(t)),this._transport.on("open",()=>this._eventManager.trigger({event:"transport_open",params:[]})),this._transport.on("close",()=>this._eventManager.trigger({event:"transport_close",params:[]})),this._transport.on("error",()=>this._eventManager.trigger({event:"transport_error",params:["Websocket connection failed"]})),this._transport.open()}_formatUri(){return`${this.protocol}:${this.handshakeTopic}@${this.version}?bridge=${encodeURIComponent(this.bridge)}&key=${this.key}`}_parseUri(t){const e=(0,i.parseWalletConnectUri)(t);if(e.protocol===this.protocol){if(!e.handshakeTopic)throw Error("Invalid or missing handshakeTopic parameter value");const t=e.handshakeTopic;if(!e.bridge)throw Error("Invalid or missing bridge url parameter value");const r=decodeURIComponent(e.bridge);if(!e.key)throw Error("Invalid or missing key parameter value");return{handshakeTopic:t,bridge:r,key:e.key}}throw new Error(s.ERROR_INVALID_URI)}_generateKey(){return n.__awaiter(this,void 0,void 0,(function*(){if(this._cryptoLib){return yield this._cryptoLib.generateKey()}return null}))}_encrypt(t){return n.__awaiter(this,void 0,void 0,(function*(){const e=this._key;if(this._cryptoLib&&e){return yield this._cryptoLib.encrypt(t,e)}return null}))}_decrypt(t){return n.__awaiter(this,void 0,void 0,(function*(){const e=this._key;if(this._cryptoLib&&e){return yield this._cryptoLib.decrypt(t,e)}return null}))}_getStorageSession(){let t=null;return this._sessionStorage&&(t=this._sessionStorage.getSession()),t}_setStorageSession(){this._sessionStorage&&this._sessionStorage.setSession(this.session)}_removeStorageSession(){this._sessionStorage&&this._sessionStorage.removeSession()}_manageStorageSession(){this._connected?this._setStorageSession():this._removeStorageSession()}_registerPushServer(t){if(!t.url||"string"!=typeof t.url)throw Error("Invalid or missing pushServerOpts.url parameter value");if(!t.type||"string"!=typeof t.type)throw Error("Invalid or missing pushServerOpts.type parameter value");if(!t.token||"string"!=typeof t.token)throw Error("Invalid or missing pushServerOpts.token parameter value");const e={bridge:this.bridge,topic:this.clientId,type:t.type,token:t.token,peerName:"",language:t.language||""};this.on("connect",(r,i)=>n.__awaiter(this,void 0,void 0,(function*(){if(r)throw r;if(t.peerMeta){const t=i.params[0].peerMeta.name;e.peerName=t}try{const r=yield fetch(t.url+"/new",{method:"POST",headers:{Accept:"application/json","Content-Type":"application/json"},body:JSON.stringify(e)});if(!(yield r.json()).success)throw Error("Failed to register in Push Server")}catch(t){throw Error("Failed to register in Push Server")}})))}}},function(t,e,r){"use strict";r.r(e),r.d(e,"__extends",(function(){return i})),r.d(e,"__assign",(function(){return o})),r.d(e,"__rest",(function(){return s})),r.d(e,"__decorate",(function(){return u})),r.d(e,"__param",(function(){return a})),r.d(e,"__metadata",(function(){return h})),r.d(e,"__awaiter",(function(){return c})),r.d(e,"__generator",(function(){return f})),r.d(e,"__exportStar",(function(){return l})),r.d(e,"__values",(function(){return d})),r.d(e,"__read",(function(){return p})),r.d(e,"__spread",(function(){return m})),r.d(e,"__await",(function(){return g})),r.d(e,"__asyncGenerator",(function(){return v})),r.d(e,"__asyncDelegator",(function(){return y})),r.d(e,"__asyncValues",(function(){return w})),r.d(e,"__makeTemplateObject",(function(){return _})),r.d(e,"__importStar",(function(){return M})),r.d(e,"__importDefault",(function(){return b}));
/*! *****************************************************************************
Copyright (c) Microsoft Corporation. All rights reserved.
Licensed under the Apache License, Version 2.0 (the "License"); you may not use
this file except in compliance with the License. You may obtain a copy of the
License at http://www.apache.org/licenses/LICENSE-2.0

THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
MERCHANTABLITY OR NON-INFRINGEMENT.

See the Apache Version 2.0 License for specific language governing permissions
and limitations under the License.
***************************************************************************** */
var n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)};function i(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}var o=function(){return(o=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var i in e=arguments[r])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)};function s(t,e){var r={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(r[n]=t[n]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(n=Object.getOwnPropertySymbols(t);i<n.length;i++)e.indexOf(n[i])<0&&(r[n[i]]=t[n[i]])}return r}function u(t,e,r,n){var i,o=arguments.length,s=o<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var u=t.length-1;u>=0;u--)(i=t[u])&&(s=(o<3?i(s):o>3?i(e,r,s):i(e,r))||s);return o>3&&s&&Object.defineProperty(e,r,s),s}function a(t,e){return function(r,n){e(r,n,t)}}function h(t,e){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(t,e)}function c(t,e,r,n){return new(r||(r=Promise))((function(i,o){function s(t){try{a(n.next(t))}catch(t){o(t)}}function u(t){try{a(n.throw(t))}catch(t){o(t)}}function a(t){t.done?i(t.value):new r((function(e){e(t.value)})).then(s,u)}a((n=n.apply(t,e||[])).next())}))}function f(t,e){var r,n,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function u(o){return function(u){return function(o){if(r)throw new TypeError("Generator is already executing.");for(;s;)try{if(r=1,n&&(i=2&o[0]?n.return:o[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,o[1])).done)return i;switch(n=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,n=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(!(i=s.trys,(i=i.length>0&&i[i.length-1])||6!==o[0]&&2!==o[0])){s=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){s.label=o[1];break}if(6===o[0]&&s.label<i[1]){s.label=i[1],i=o;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(o);break}i[2]&&s.ops.pop(),s.trys.pop();continue}o=e.call(t,s)}catch(t){o=[6,t],n=0}finally{r=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,u])}}}function l(t,e){for(var r in t)e.hasOwnProperty(r)||(e[r]=t[r])}function d(t){var e="function"==typeof Symbol&&t[Symbol.iterator],r=0;return e?e.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}}function p(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,i,o=r.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(n=o.next()).done;)s.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return s}function m(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(p(arguments[e]));return t}function g(t){return this instanceof g?(this.v=t,this):new g(t)}function v(t,e,r){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n,i=r.apply(t,e||[]),o=[];return n={},s("next"),s("throw"),s("return"),n[Symbol.asyncIterator]=function(){return this},n;function s(t){i[t]&&(n[t]=function(e){return new Promise((function(r,n){o.push([t,e,r,n])>1||u(t,e)}))})}function u(t,e){try{(r=i[t](e)).value instanceof g?Promise.resolve(r.value.v).then(a,h):c(o[0][2],r)}catch(t){c(o[0][3],t)}var r}function a(t){u("next",t)}function h(t){u("throw",t)}function c(t,e){t(e),o.shift(),o.length&&u(o[0][0],o[0][1])}}function y(t){var e,r;return e={},n("next"),n("throw",(function(t){throw t})),n("return"),e[Symbol.iterator]=function(){return this},e;function n(n,i){e[n]=t[n]?function(e){return(r=!r)?{value:g(t[n](e)),done:"return"===n}:i?i(e):e}:i}}function w(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e,r=t[Symbol.asyncIterator];return r?r.call(t):(t=d(t),e={},n("next"),n("throw"),n("return"),e[Symbol.asyncIterator]=function(){return this},e);function n(r){e[r]=t[r]&&function(e){return new Promise((function(n,i){(function(t,e,r,n){Promise.resolve(n).then((function(e){t({value:e,done:r})}),e)})(n,i,(e=t[r](e)).done,e.value)}))}}}function _(t,e){return Object.defineProperty?Object.defineProperty(t,"raw",{value:e}):t.raw=e,t}function M(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var r in t)Object.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e.default=t,e}function b(t){return t&&t.__esModule?t:{default:t}}},function(t,e){t.exports=function(t){return t.webpackPolyfill||(t.deprecate=function(){},t.paths=[],t.children||(t.children=[]),Object.defineProperty(t,"loaded",{enumerable:!0,get:function(){return t.l}}),Object.defineProperty(t,"id",{enumerable:!0,get:function(){return t.i}}),t.webpackPolyfill=1),t}},function(t,e){},function(t,e,r){"use strict";e.byteLength=function(t){var e=h(t),r=e[0],n=e[1];return 3*(r+n)/4-n},e.toByteArray=function(t){var e,r,n=h(t),s=n[0],u=n[1],a=new o(function(t,e,r){return 3*(e+r)/4-r}(0,s,u)),c=0,f=u>0?s-4:s;for(r=0;r<f;r+=4)e=i[t.charCodeAt(r)]<<18|i[t.charCodeAt(r+1)]<<12|i[t.charCodeAt(r+2)]<<6|i[t.charCodeAt(r+3)],a[c++]=e>>16&255,a[c++]=e>>8&255,a[c++]=255&e;2===u&&(e=i[t.charCodeAt(r)]<<2|i[t.charCodeAt(r+1)]>>4,a[c++]=255&e);1===u&&(e=i[t.charCodeAt(r)]<<10|i[t.charCodeAt(r+1)]<<4|i[t.charCodeAt(r+2)]>>2,a[c++]=e>>8&255,a[c++]=255&e);return a},e.fromByteArray=function(t){for(var e,r=t.length,i=r%3,o=[],s=0,u=r-i;s<u;s+=16383)o.push(c(t,s,s+16383>u?u:s+16383));1===i?(e=t[r-1],o.push(n[e>>2]+n[e<<4&63]+"==")):2===i&&(e=(t[r-2]<<8)+t[r-1],o.push(n[e>>10]+n[e>>4&63]+n[e<<2&63]+"="));return o.join("")};for(var n=[],i=[],o="undefined"!=typeof Uint8Array?Uint8Array:Array,s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",u=0,a=s.length;u<a;++u)n[u]=s[u],i[s.charCodeAt(u)]=u;function h(t){var e=t.length;if(e%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var r=t.indexOf("=");return-1===r&&(r=e),[r,r===e?0:4-r%4]}function c(t,e,r){for(var i,o,s=[],u=e;u<r;u+=3)i=(t[u]<<16&16711680)+(t[u+1]<<8&65280)+(255&t[u+2]),s.push(n[(o=i)>>18&63]+n[o>>12&63]+n[o>>6&63]+n[63&o]);return s.join("")}i["-".charCodeAt(0)]=62,i["_".charCodeAt(0)]=63},function(t,e){
/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */
e.read=function(t,e,r,n,i){var o,s,u=8*i-n-1,a=(1<<u)-1,h=a>>1,c=-7,f=r?i-1:0,l=r?-1:1,d=t[e+f];for(f+=l,o=d&(1<<-c)-1,d>>=-c,c+=u;c>0;o=256*o+t[e+f],f+=l,c-=8);for(s=o&(1<<-c)-1,o>>=-c,c+=n;c>0;s=256*s+t[e+f],f+=l,c-=8);if(0===o)o=1-h;else{if(o===a)return s?NaN:1/0*(d?-1:1);s+=Math.pow(2,n),o-=h}return(d?-1:1)*s*Math.pow(2,o-n)},e.write=function(t,e,r,n,i,o){var s,u,a,h=8*o-i-1,c=(1<<h)-1,f=c>>1,l=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,d=n?0:o-1,p=n?1:-1,m=e<0||0===e&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(u=isNaN(e)?1:0,s=c):(s=Math.floor(Math.log(e)/Math.LN2),e*(a=Math.pow(2,-s))<1&&(s--,a*=2),(e+=s+f>=1?l/a:l*Math.pow(2,1-f))*a>=2&&(s++,a/=2),s+f>=c?(u=0,s=c):s+f>=1?(u=(e*a-1)*Math.pow(2,i),s+=f):(u=e*Math.pow(2,f-1)*Math.pow(2,i),s=0));i>=8;t[r+d]=255&u,d+=p,u/=256,i-=8);for(s=s<<i|u,h+=i;h>0;t[r+d]=255&s,d+=p,s/=256,h-=8);t[r+d-p]|=128*m}},function(t,e){var r={}.toString;t.exports=Array.isArray||function(t){return"[object Array]"==r.call(t)}},function(t,e){(function(e){t.exports=e}).call(this,{})},function(t,e,r){"use strict";(function(t){function r(){return(null==t?void 0:t.crypto)||(null==t?void 0:t.msCrypto)||{}}function n(){const t=r();return t.subtle||t.webkitSubtle}Object.defineProperty(e,"__esModule",{value:!0}),e.isBrowserCryptoAvailable=e.getSubtleCrypto=e.getBrowerCrypto=void 0,e.getBrowerCrypto=r,e.getSubtleCrypto=n,e.isBrowserCryptoAvailable=function(){return!!r()&&!!n()}}).call(this,r(7))},function(t,e,r){"use strict";(function(t){function r(){return"undefined"==typeof document&&"undefined"!=typeof navigator&&"ReactNative"===navigator.product}function n(){return void 0!==t&&void 0!==t.versions&&void 0!==t.versions.node}Object.defineProperty(e,"__esModule",{value:!0}),e.isBrowser=e.isNode=e.isReactNative=void 0,e.isReactNative=r,e.isNode=n,e.isBrowser=function(){return!r()&&!n()}}).call(this,r(9))},function(t,e,r){"use strict";t.exports=t=>encodeURIComponent(t).replace(/[!'()*]/g,t=>"%"+t.charCodeAt(0).toString(16).toUpperCase())},function(t,e,r){"use strict";var n=new RegExp("%[a-f0-9]{2}","gi"),i=new RegExp("(%[a-f0-9]{2})+","gi");function o(t,e){try{return decodeURIComponent(t.join(""))}catch(t){}if(1===t.length)return t;e=e||1;var r=t.slice(0,e),n=t.slice(e);return Array.prototype.concat.call([],o(r),o(n))}function s(t){try{return decodeURIComponent(t)}catch(i){for(var e=t.match(n),r=1;r<e.length;r++)e=(t=o(e,r).join("")).match(n);return t}}t.exports=function(t){if("string"!=typeof t)throw new TypeError("Expected `encodedURI` to be of type `string`, got `"+typeof t+"`");try{return t=t.replace(/\+/g," "),decodeURIComponent(t)}catch(e){return function(t){for(var e={"%FE%FF":"��","%FF%FE":"��"},r=i.exec(t);r;){try{e[r[0]]=decodeURIComponent(r[0])}catch(t){var n=s(r[0]);n!==r[0]&&(e[r[0]]=n)}r=i.exec(t)}e["%C2"]="�";for(var o=Object.keys(e),u=0;u<o.length;u++){var a=o[u];t=t.replace(new RegExp(a,"g"),e[a])}return t}(t)}}},function(t,e,r){"use strict";t.exports=(t,e)=>{if("string"!=typeof t||"string"!=typeof e)throw new TypeError("Expected the arguments to be of type `string`");if(""===e)return[t];const r=t.indexOf(e);return-1===r?[t]:[t.slice(0,r),t.slice(r+e.length)]}},function(t,e,r){"use strict";r.r(e),function(t){var n=r(3),i=r(28);const o=void 0!==t.WebSocket?t.WebSocket:r(43);e.default=class{constructor(t){if(this.opts=t,this._queue=[],this._events=[],this._subscriptions=[],this._protocol=t.protocol,this._version=t.version,this._url="",this._netMonitor=null,this._socket=null,this._nextSocket=null,this._subscriptions=t.subscriptions||[],this._netMonitor=t.netMonitor||new i.a,!t.url||"string"!=typeof t.url)throw new Error("Missing or invalid WebSocket url");this._url=t.url,this._netMonitor.on("online",()=>this._socketCreate())}set readyState(t){}get readyState(){return this._socket?this._socket.readyState:-1}set connecting(t){}get connecting(){return 0===this.readyState}set connected(t){}get connected(){return 1===this.readyState}set closing(t){}get closing(){return 2===this.readyState}set closed(t){}get closed(){return 3===this.readyState}open(){this._socketCreate()}close(){this._socketClose()}send(t,e,r){if(!e||"string"!=typeof e)throw new Error("Missing or invalid topic field");this._socketSend({topic:e,type:"pub",payload:t,silent:!!r})}subscribe(t){this._socketSend({topic:t,type:"sub",payload:"",silent:!0})}on(t,e){this._events.push({event:t,callback:e})}_socketCreate(){if(this._nextSocket)return;const t=function(t,e,r){var i,o;const s=(t.startsWith("https")?t.replace("https","wss"):t.startsWith("http")?t.replace("http","ws"):t).split("?"),u=Object(n.isBrowser)()?{protocol:e,version:r,env:"browser",host:(null===(i=Object(n.getLocation)())||void 0===i?void 0:i.host)||""}:{protocol:e,version:r,env:(null===(o=Object(n.detectEnv)())||void 0===o?void 0:o.name)||""},a=Object(n.appendToQueryString)(Object(n.getQueryString)(s[1]||""),u);return s[0]+"?"+a}(this._url,this._protocol,this._version);if(this._nextSocket=new o(t),!this._nextSocket)throw new Error("Failed to create socket");this._nextSocket.onmessage=t=>this._socketReceive(t),this._nextSocket.onopen=()=>this._socketOpen(),this._nextSocket.onerror=t=>this._socketError(t),this._nextSocket.onclose=()=>{setTimeout(()=>{this._nextSocket=null,this._socketCreate()},1e3)}}_socketOpen(){this._socketClose(),this._socket=this._nextSocket,this._nextSocket=null,this._queueSubscriptions(),this._pushQueue()}_socketClose(){this._socket&&(this._socket.onclose=()=>{},this._socket.close())}_socketSend(t){const e=JSON.stringify(t);this._socket&&1===this._socket.readyState?this._socket.send(e):(this._setToQueue(t),this._socketCreate())}async _socketReceive(t){let e;try{e=JSON.parse(t.data)}catch(t){return}if(this._socketSend({topic:e.topic,type:"ack",payload:"",silent:!0}),this._socket&&1===this._socket.readyState){const t=this._events.filter(t=>"message"===t.event);t&&t.length&&t.forEach(t=>t.callback(e))}}_socketError(t){const e=this._events.filter(t=>"error"===t.event);e&&e.length&&e.forEach(e=>e.callback(t))}_queueSubscriptions(){this._subscriptions.forEach(t=>this._queue.push({topic:t,type:"sub",payload:"",silent:!0})),this._subscriptions=this.opts.subscriptions||[]}_setToQueue(t){this._queue.push(t)}_pushQueue(){this._queue.forEach(t=>this._socketSend(t)),this._queue=[]}}}.call(this,r(7))},function(t,e,r){"use strict";t.exports=function(){throw new Error("ws does not work in the browser. Browser clients must use the native WebSocket object")}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.ERROR_QRCODE_MODAL_USER_CLOSED=e.ERROR_QRCODE_MODAL_NOT_PROVIDED=e.ERROR_INVALID_URI=e.ERROR_INVALID_RESPONSE=e.ERROR_MISSING_REQUIRED=e.ERROR_MISSING_ID=e.ERROR_MISSING_METHOD=e.ERROR_MISSING_ERROR=e.ERROR_MISSING_RESULT=e.ERROR_MISSING_JSON_RPC=e.ERROR_SESSION_REJECTED=e.ERROR_SESSION_DISCONNECTED=e.ERROR_SESSION_CONNECTED=void 0,e.ERROR_SESSION_CONNECTED="Session currently connected",e.ERROR_SESSION_DISCONNECTED="Session currently disconnected",e.ERROR_SESSION_REJECTED="Session Rejected",e.ERROR_MISSING_JSON_RPC="Missing JSON RPC response",e.ERROR_MISSING_RESULT='JSON-RPC success response must include "result" field',e.ERROR_MISSING_ERROR='JSON-RPC error response must include "error" field',e.ERROR_MISSING_METHOD='JSON RPC request must have valid "method" value',e.ERROR_MISSING_ID='JSON RPC request must have valid "id" value',e.ERROR_MISSING_REQUIRED="Missing one of the required parameters: bridge / uri / session",e.ERROR_INVALID_RESPONSE="JSON RPC response format is invalid",e.ERROR_INVALID_URI="URI format is invalid",e.ERROR_QRCODE_MODAL_NOT_PROVIDED="QRCode Modal not provided",e.ERROR_QRCODE_MODAL_USER_CLOSED="User close QRCode Modal"},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});const n=r(3);e.default=class{constructor(){this._eventEmitters=[]}subscribe(t){this._eventEmitters.push(t)}unsubscribe(t){this._eventEmitters=this._eventEmitters.filter(e=>e.event!==t)}trigger(t){let e,r=[];e=(0,n.isJsonRpcRequest)(t)?t.method:(0,n.isJsonRpcResponseSuccess)(t)||(0,n.isJsonRpcResponseError)(t)?"response:"+t.id:(0,n.isInternalEvent)(t)?t.event:"",e&&(r=this._eventEmitters.filter(t=>t.event===e)),r&&r.length||(0,n.isReservedEvent)(e)||(0,n.isInternalEvent)(e)||(r=this._eventEmitters.filter(t=>"call_request"===t.event)),r.forEach(e=>{if((0,n.isJsonRpcResponseError)(t)){const r=new Error(t.error.message);e.callback(r,null)}else e.callback(null,t)})}}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});const n=r(3);e.default=class{constructor(t="walletconnect"){this.storageId=t}getSession(){let t=null;const e=(0,n.getLocal)(this.storageId);return e&&(0,n.isWalletConnectSession)(e)&&(t=e),t}setSession(t){return(0,n.setLocal)(this.storageId,t),t}removeSession(){(0,n.removeLocal)(this.storageId)}}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getBridgeUrl=e.shouldSelectRandomly=e.selectRandomBridgeUrl=e.randomBridgeIndex=e.extractRootDomain=e.extractHostname=void 0;const n="abcdefghijklmnopqrstuvwxyz0123456789".split("").map(t=>`https://${t}.bridge.walletconnect.org`);function i(t){let e=t.indexOf("//")>-1?t.split("/")[2]:t.split("/")[0];return e=e.split(":")[0],e=e.split("?")[0],e}function o(t){return i(t).split(".").slice(-2).join(".")}function s(){return Math.floor(Math.random()*n.length)}function u(){return n[s()]}function a(t){return"walletconnect.org"===o(t)}e.extractHostname=i,e.extractRootDomain=o,e.randomBridgeIndex=s,e.selectRandomBridgeUrl=u,e.shouldSelectRandomly=a,e.getBridgeUrl=function(t){return a(t)?u():t}}])}));