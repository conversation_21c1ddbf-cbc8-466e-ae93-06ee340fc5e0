{"name": "web3-provider-engine", "version": "16.0.1", "description": "A JavaScript library for composing Ethereum provider objects using middleware modules", "repository": "https://github.com/MetaMask/web3-provider-engine", "main": "index.js", "scripts": {"test": "node test/index.js && yarn lint", "prepublishOnly": "yarn build && yarn bundle", "build": "babel zero.js index.js -d dist/es5 && babel subproviders -d dist/es5/subproviders && babel util -d dist/es5/util", "bundle": "mkdir -p ./dist && yarn bundle-engine && yarn bundle-zero", "bundle-zero": "browserify -s ZeroClientProvider -e zero.js -t [ babelify --presets [ @babel/preset-env ] ] > dist/ZeroClientProvider.js", "bundle-engine": "browserify -s ProviderEngine -e index.js -t [ babelify --presets [ @babel/preset-env ] ] > dist/ProviderEngine.js", "lint": "eslint --quiet --ignore-path .gitignore ."}, "files": ["*.js", "dist", "subproviders", "util"], "license": "MIT", "resolutions": {"ganache-core/**/elliptic": "^6.5.2"}, "dependencies": {"async": "^2.5.0", "backoff": "^2.5.0", "clone": "^2.0.0", "cross-fetch": "^2.1.0", "eth-block-tracker": "^4.4.2", "eth-json-rpc-filters": "^4.2.1", "eth-json-rpc-infura": "^5.1.0", "eth-json-rpc-middleware": "^6.0.0", "eth-rpc-errors": "^3.0.0", "eth-sig-util": "^1.4.2", "ethereumjs-block": "^1.2.2", "ethereumjs-tx": "^1.2.0", "ethereumjs-util": "^5.1.5", "ethereumjs-vm": "^2.3.4", "json-stable-stringify": "^1.0.1", "promise-to-callback": "^1.0.0", "readable-stream": "^2.2.9", "request": "^2.85.0", "semaphore": "^1.0.3", "ws": "^5.1.1", "xhr": "^2.2.0", "xtend": "^4.0.1"}, "devDependencies": {"@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/preset-env": "^7.5.5", "babelify": "^10.0.0", "browserify": "^16.5.0", "eslint": "^6.2.0", "ethjs": "^0.3.6", "ganache-core": "^2.7.0", "tape": "^4.4.0"}, "browser": {"request": false, "ws": false}}