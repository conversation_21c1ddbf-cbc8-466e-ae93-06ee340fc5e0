{"version": 3, "file": "reactive-controller.js", "sourceRoot": "", "sources": ["../src/reactive-controller.ts"], "names": [], "mappings": "AAAA;;;;GAIG", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/**\n * An object that can host Reactive Controllers and call their lifecycle\n * callbacks.\n */\nexport interface ReactiveControllerHost {\n  /**\n   * Adds a controller to the host, which sets up the controller's lifecycle\n   * methods to be called with the host's lifecycle.\n   */\n  addController(controller: ReactiveController): void;\n\n  /**\n   * Removes a controller from the host.\n   */\n  removeController(controller: ReactiveController): void;\n\n  /**\n   * Requests a host update which is processed asynchronously. The update can\n   * be waited on via the `updateComplete` property.\n   */\n  requestUpdate(): void;\n\n  /**\n   * Returns a Promise that resolves when the host has completed updating.\n   * The Promise value is a boolean that is `true` if the element completed the\n   * update without triggering another update. The Promise result is `false` if\n   * a property was set inside `updated()`. If the Promise is rejected, an\n   * exception was thrown during the update.\n   *\n   * @return A promise of a boolean that indicates if the update resolved\n   *     without triggering another update.\n   */\n  readonly updateComplete: Promise<boolean>;\n}\n\n/**\n * A Reactive Controller is an object that enables sub-component code\n * organization and reuse by aggregating the state, behavior, and lifecycle\n * hooks related to a single feature.\n *\n * Controllers are added to a host component, or other object that implements\n * the `ReactiveControllerHost` interface, via the `addController()` method.\n * They can hook their host components's lifecycle by implementing one or more\n * of the lifecycle callbacks, or initiate an update of the host component by\n * calling `requestUpdate()` on the host.\n */\nexport interface ReactiveController {\n  /**\n   * Called when the host is connected to the component tree. For custom\n   * element hosts, this corresponds to the `connectedCallback()` lifecycle,\n   * which is only called when the component is connected to the document.\n   */\n  hostConnected?(): void;\n\n  /**\n   * Called when the host is disconnected from the component tree. For custom\n   * element hosts, this corresponds to the `disconnectedCallback()` lifecycle,\n   * which is called the host or an ancestor component is disconnected from the\n   * document.\n   */\n  hostDisconnected?(): void;\n\n  /**\n   * Called during the client-side host update, just before the host calls\n   * its own update.\n   *\n   * Code in `update()` can depend on the DOM as it is not called in\n   * server-side rendering.\n   */\n  hostUpdate?(): void;\n\n  /**\n   * Called after a host update, just before the host calls firstUpdated and\n   * updated. It is not called in server-side rendering.\n   *\n   */\n  hostUpdated?(): void;\n}\n"]}