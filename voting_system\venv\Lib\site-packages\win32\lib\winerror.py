"""Error related constants for win32

Generated from winerror.h and cderror.h"""

import warnings


# Private so h2py ignored it
def __HRESULT_FROM_WIN32(x: int) -> int:
    return x if x <= 0 else ((x & 0x0000FFFF) | (FACILITY_WIN32 << 16) | -2147483648)


def __HRESULT_FROM_SETUPAPI(x: int):
    APPLICATION_ERROR_MASK = 0x20000000  # From winnt.h
    ERROR_SEVERITY_ERROR = -1073741824  # From winnt.h
    return (
        ((x & 0x0000FFFF) | (FACILITY_SETUPAPI << 16) | -2147483648)
        if (
            (x & (APPLICATION_ERROR_MASK | ERROR_SEVERITY_ERROR))
            == (APPLICATION_ERROR_MASK | ERROR_SEVERITY_ERROR)
        )
        else HRESULT_FROM_WIN32(x)
    )


# Was previously found in this module, but no longer seems to exists anywhere
def __getattr__(name: str) -> int:
    if attr := {
        "ERROR_INSTALL_SERVICE": 1601,
        "ERROR_BAD_DATABASE_VERSION": 1613,
        "win16_E_NOTIMPL": -2147483647,
        "win16_E_OUTOFMEMORY": -2147483646,
        "win16_E_INVALIDARG": -2147483645,
        "win16_E_NOINTERFACE": -2147483644,
        "win16_E_POINTER": -2147483643,
        "win16_E_HANDLE": -2147483642,
        "win16_E_ABORT": -2147483641,
        "win16_E_FAIL": -2147483640,
        "win16_E_ACCESSDENIED": -2147483639,
        "CERTDB_E_JET_ERROR": -2146873344,
    }.get(name):
        warnings.warn(
            DeprecationWarning(
                f"Constant '{name}' is no longer part of Windows' SDK and may be removed eventually. "
                + f"If you believe this is incorrect or are still using '{name}', "
                + "please raise an issue at https://github.com/mhammond/pywin32/issues"
            ),
            stacklevel=2,
        )
        return attr
    else:
        raise AttributeError(f"module {__name__!r} has no attribute {name!r}")


# Everything below is autogenerated
# Then manually removed all `_HRESULT_TYPEDEF_` and `_NDIS_ERROR_TYPEDEF_` since they're private AND calling them is a waste of resources
# Fixed the ASSERT to an assert
# Fixed redefined names with a different value due to a condition in winerror.h
# ! NO OTHER MANUAL CHANGES BELOW !

# Generated by h2py from C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\winerror.h
FACILITY_NULL = 0
FACILITY_RPC = 1
FACILITY_DISPATCH = 2
FACILITY_STORAGE = 3
FACILITY_ITF = 4
FACILITY_WIN32 = 7
FACILITY_WINDOWS = 8
FACILITY_SSPI = 9
FACILITY_SECURITY = 9
FACILITY_CONTROL = 10
FACILITY_CERT = 11
FACILITY_INTERNET = 12
FACILITY_MEDIASERVER = 13
FACILITY_MSMQ = 14
FACILITY_SETUPAPI = 15
FACILITY_SCARD = 16
FACILITY_COMPLUS = 17
FACILITY_AAF = 18
FACILITY_URT = 19
FACILITY_ACS = 20
FACILITY_DPLAY = 21
FACILITY_UMI = 22
FACILITY_SXS = 23
FACILITY_WINDOWS_CE = 24
FACILITY_HTTP = 25
FACILITY_USERMODE_COMMONLOG = 26
FACILITY_WER = 27
FACILITY_USERMODE_FILTER_MANAGER = 31
FACILITY_BACKGROUNDCOPY = 32
FACILITY_CONFIGURATION = 33
FACILITY_WIA = 33
FACILITY_STATE_MANAGEMENT = 34
FACILITY_METADIRECTORY = 35
FACILITY_WINDOWSUPDATE = 36
FACILITY_DIRECTORYSERVICE = 37
FACILITY_GRAPHICS = 38
FACILITY_SHELL = 39
FACILITY_NAP = 39
FACILITY_TPM_SERVICES = 40
FACILITY_TPM_SOFTWARE = 41
FACILITY_UI = 42
FACILITY_XAML = 43
FACILITY_ACTION_QUEUE = 44
FACILITY_PLA = 48
FACILITY_WINDOWS_SETUP = 48
FACILITY_FVE = 49
FACILITY_FWP = 50
FACILITY_WINRM = 51
FACILITY_NDIS = 52
FACILITY_USERMODE_HYPERVISOR = 53
FACILITY_CMI = 54
FACILITY_USERMODE_VIRTUALIZATION = 55
FACILITY_USERMODE_VOLMGR = 56
FACILITY_BCD = 57
FACILITY_USERMODE_VHD = 58
FACILITY_USERMODE_HNS = 59
FACILITY_SDIAG = 60
FACILITY_WEBSERVICES = 61
FACILITY_WINPE = 61
FACILITY_WPN = 62
FACILITY_WINDOWS_STORE = 63
FACILITY_INPUT = 64
FACILITY_QUIC = 65
FACILITY_EAP = 66
FACILITY_IORING = 70
FACILITY_WINDOWS_DEFENDER = 80
FACILITY_OPC = 81
FACILITY_XPS = 82
FACILITY_MBN = 84
FACILITY_POWERSHELL = 84
FACILITY_RAS = 83
FACILITY_P2P_INT = 98
FACILITY_P2P = 99
FACILITY_DAF = 100
FACILITY_BLUETOOTH_ATT = 101
FACILITY_AUDIO = 102
FACILITY_STATEREPOSITORY = 103
FACILITY_VISUALCPP = 109
FACILITY_SCRIPT = 112
FACILITY_PARSE = 113
FACILITY_BLB = 120
FACILITY_BLB_CLI = 121
FACILITY_WSBAPP = 122
FACILITY_BLBUI = 128
FACILITY_USN = 129
FACILITY_USERMODE_VOLSNAP = 130
FACILITY_TIERING = 131
FACILITY_WSB_ONLINE = 133
FACILITY_ONLINE_ID = 134
FACILITY_DEVICE_UPDATE_AGENT = 135
FACILITY_DRVSERVICING = 136
FACILITY_DLS = 153
FACILITY_DELIVERY_OPTIMIZATION = 208
FACILITY_USERMODE_SPACES = 231
FACILITY_USER_MODE_SECURITY_CORE = 232
FACILITY_USERMODE_LICENSING = 234
FACILITY_SOS = 160
FACILITY_OCP_UPDATE_AGENT = 173
FACILITY_DEBUGGERS = 176
FACILITY_SPP = 256
FACILITY_RESTORE = 256
FACILITY_DMSERVER = 256
FACILITY_DEPLOYMENT_SERVICES_SERVER = 257
FACILITY_DEPLOYMENT_SERVICES_IMAGING = 258
FACILITY_DEPLOYMENT_SERVICES_MANAGEMENT = 259
FACILITY_DEPLOYMENT_SERVICES_UTIL = 260
FACILITY_DEPLOYMENT_SERVICES_BINLSVC = 261
FACILITY_DEPLOYMENT_SERVICES_PXE = 263
FACILITY_DEPLOYMENT_SERVICES_TFTP = 264
FACILITY_DEPLOYMENT_SERVICES_TRANSPORT_MANAGEMENT = 272
FACILITY_DEPLOYMENT_SERVICES_DRIVER_PROVISIONING = 278
FACILITY_DEPLOYMENT_SERVICES_MULTICAST_SERVER = 289
FACILITY_DEPLOYMENT_SERVICES_MULTICAST_CLIENT = 290
FACILITY_DEPLOYMENT_SERVICES_CONTENT_PROVIDER = 293
FACILITY_HSP_SERVICES = 296
FACILITY_HSP_SOFTWARE = 297
FACILITY_LINGUISTIC_SERVICES = 305
FACILITY_AUDIOSTREAMING = 1094
FACILITY_TTD = 1490
FACILITY_ACCELERATOR = 1536
FACILITY_WMAAECMA = 1996
FACILITY_DIRECTMUSIC = 2168
FACILITY_DIRECT3D10 = 2169
FACILITY_DXGI = 2170
FACILITY_DXGI_DDI = 2171
FACILITY_DIRECT3D11 = 2172
FACILITY_DIRECT3D11_DEBUG = 2173
FACILITY_DIRECT3D12 = 2174
FACILITY_DIRECT3D12_DEBUG = 2175
FACILITY_DXCORE = 2176
FACILITY_PRESENTATION = 2177
FACILITY_LEAP = 2184
FACILITY_AUDCLNT = 2185
FACILITY_WINCODEC_DWRITE_DWM = 2200
FACILITY_WINML = 2192
FACILITY_DIRECT2D = 2201
FACILITY_DEFRAG = 2304
FACILITY_USERMODE_SDBUS = 2305
FACILITY_JSCRIPT = 2306
FACILITY_PIDGENX = 2561
FACILITY_EAS = 85
FACILITY_WEB = 885
FACILITY_WEB_SOCKET = 886
FACILITY_MOBILE = 1793
FACILITY_SQLITE = 1967
FACILITY_SERVICE_FABRIC = 1968
FACILITY_UTC = 1989
FACILITY_WEP = 2049
FACILITY_SYNCENGINE = 2050
FACILITY_XBOX = 2339
FACILITY_GAME = 2340
FACILITY_PIX = 2748
ERROR_SUCCESS = 0
NO_ERROR = 0
SEC_E_OK = 0x00000000
ERROR_INVALID_FUNCTION = 1
ERROR_FILE_NOT_FOUND = 2
ERROR_PATH_NOT_FOUND = 3
ERROR_TOO_MANY_OPEN_FILES = 4
ERROR_ACCESS_DENIED = 5
ERROR_INVALID_HANDLE = 6
ERROR_ARENA_TRASHED = 7
ERROR_NOT_ENOUGH_MEMORY = 8
ERROR_INVALID_BLOCK = 9
ERROR_BAD_ENVIRONMENT = 10
ERROR_BAD_FORMAT = 11
ERROR_INVALID_ACCESS = 12
ERROR_INVALID_DATA = 13
ERROR_OUTOFMEMORY = 14
ERROR_INVALID_DRIVE = 15
ERROR_CURRENT_DIRECTORY = 16
ERROR_NOT_SAME_DEVICE = 17
ERROR_NO_MORE_FILES = 18
ERROR_WRITE_PROTECT = 19
ERROR_BAD_UNIT = 20
ERROR_NOT_READY = 21
ERROR_BAD_COMMAND = 22
ERROR_CRC = 23
ERROR_BAD_LENGTH = 24
ERROR_SEEK = 25
ERROR_NOT_DOS_DISK = 26
ERROR_SECTOR_NOT_FOUND = 27
ERROR_OUT_OF_PAPER = 28
ERROR_WRITE_FAULT = 29
ERROR_READ_FAULT = 30
ERROR_GEN_FAILURE = 31
ERROR_SHARING_VIOLATION = 32
ERROR_LOCK_VIOLATION = 33
ERROR_WRONG_DISK = 34
ERROR_SHARING_BUFFER_EXCEEDED = 36
ERROR_HANDLE_EOF = 38
ERROR_HANDLE_DISK_FULL = 39
ERROR_NOT_SUPPORTED = 50
ERROR_REM_NOT_LIST = 51
ERROR_DUP_NAME = 52
ERROR_BAD_NETPATH = 53
ERROR_NETWORK_BUSY = 54
ERROR_DEV_NOT_EXIST = 55
ERROR_TOO_MANY_CMDS = 56
ERROR_ADAP_HDW_ERR = 57
ERROR_BAD_NET_RESP = 58
ERROR_UNEXP_NET_ERR = 59
ERROR_BAD_REM_ADAP = 60
ERROR_PRINTQ_FULL = 61
ERROR_NO_SPOOL_SPACE = 62
ERROR_PRINT_CANCELLED = 63
ERROR_NETNAME_DELETED = 64
ERROR_NETWORK_ACCESS_DENIED = 65
ERROR_BAD_DEV_TYPE = 66
ERROR_BAD_NET_NAME = 67
ERROR_TOO_MANY_NAMES = 68
ERROR_TOO_MANY_SESS = 69
ERROR_SHARING_PAUSED = 70
ERROR_REQ_NOT_ACCEP = 71
ERROR_REDIR_PAUSED = 72
ERROR_FILE_EXISTS = 80
ERROR_CANNOT_MAKE = 82
ERROR_FAIL_I24 = 83
ERROR_OUT_OF_STRUCTURES = 84
ERROR_ALREADY_ASSIGNED = 85
ERROR_INVALID_PASSWORD = 86
ERROR_INVALID_PARAMETER = 87
ERROR_NET_WRITE_FAULT = 88
ERROR_NO_PROC_SLOTS = 89
ERROR_TOO_MANY_SEMAPHORES = 100
ERROR_EXCL_SEM_ALREADY_OWNED = 101
ERROR_SEM_IS_SET = 102
ERROR_TOO_MANY_SEM_REQUESTS = 103
ERROR_INVALID_AT_INTERRUPT_TIME = 104
ERROR_SEM_OWNER_DIED = 105
ERROR_SEM_USER_LIMIT = 106
ERROR_DISK_CHANGE = 107
ERROR_DRIVE_LOCKED = 108
ERROR_BROKEN_PIPE = 109
ERROR_OPEN_FAILED = 110
ERROR_BUFFER_OVERFLOW = 111
ERROR_DISK_FULL = 112
ERROR_NO_MORE_SEARCH_HANDLES = 113
ERROR_INVALID_TARGET_HANDLE = 114
ERROR_INVALID_CATEGORY = 117
ERROR_INVALID_VERIFY_SWITCH = 118
ERROR_BAD_DRIVER_LEVEL = 119
ERROR_CALL_NOT_IMPLEMENTED = 120
ERROR_SEM_TIMEOUT = 121
ERROR_INSUFFICIENT_BUFFER = 122
ERROR_INVALID_NAME = 123
ERROR_INVALID_LEVEL = 124
ERROR_NO_VOLUME_LABEL = 125
ERROR_MOD_NOT_FOUND = 126
ERROR_PROC_NOT_FOUND = 127
ERROR_WAIT_NO_CHILDREN = 128
ERROR_CHILD_NOT_COMPLETE = 129
ERROR_DIRECT_ACCESS_HANDLE = 130
ERROR_NEGATIVE_SEEK = 131
ERROR_SEEK_ON_DEVICE = 132
ERROR_IS_JOIN_TARGET = 133
ERROR_IS_JOINED = 134
ERROR_IS_SUBSTED = 135
ERROR_NOT_JOINED = 136
ERROR_NOT_SUBSTED = 137
ERROR_JOIN_TO_JOIN = 138
ERROR_SUBST_TO_SUBST = 139
ERROR_JOIN_TO_SUBST = 140
ERROR_SUBST_TO_JOIN = 141
ERROR_BUSY_DRIVE = 142
ERROR_SAME_DRIVE = 143
ERROR_DIR_NOT_ROOT = 144
ERROR_DIR_NOT_EMPTY = 145
ERROR_IS_SUBST_PATH = 146
ERROR_IS_JOIN_PATH = 147
ERROR_PATH_BUSY = 148
ERROR_IS_SUBST_TARGET = 149
ERROR_SYSTEM_TRACE = 150
ERROR_INVALID_EVENT_COUNT = 151
ERROR_TOO_MANY_MUXWAITERS = 152
ERROR_INVALID_LIST_FORMAT = 153
ERROR_LABEL_TOO_LONG = 154
ERROR_TOO_MANY_TCBS = 155
ERROR_SIGNAL_REFUSED = 156
ERROR_DISCARDED = 157
ERROR_NOT_LOCKED = 158
ERROR_BAD_THREADID_ADDR = 159
ERROR_BAD_ARGUMENTS = 160
ERROR_BAD_PATHNAME = 161
ERROR_SIGNAL_PENDING = 162
ERROR_MAX_THRDS_REACHED = 164
ERROR_LOCK_FAILED = 167
ERROR_BUSY = 170
ERROR_DEVICE_SUPPORT_IN_PROGRESS = 171
ERROR_CANCEL_VIOLATION = 173
ERROR_ATOMIC_LOCKS_NOT_SUPPORTED = 174
ERROR_INVALID_SEGMENT_NUMBER = 180
ERROR_INVALID_ORDINAL = 182
ERROR_ALREADY_EXISTS = 183
ERROR_INVALID_FLAG_NUMBER = 186
ERROR_SEM_NOT_FOUND = 187
ERROR_INVALID_STARTING_CODESEG = 188
ERROR_INVALID_STACKSEG = 189
ERROR_INVALID_MODULETYPE = 190
ERROR_INVALID_EXE_SIGNATURE = 191
ERROR_EXE_MARKED_INVALID = 192
ERROR_BAD_EXE_FORMAT = 193
ERROR_ITERATED_DATA_EXCEEDS_64k = 194
ERROR_INVALID_MINALLOCSIZE = 195
ERROR_DYNLINK_FROM_INVALID_RING = 196
ERROR_IOPL_NOT_ENABLED = 197
ERROR_INVALID_SEGDPL = 198
ERROR_AUTODATASEG_EXCEEDS_64k = 199
ERROR_RING2SEG_MUST_BE_MOVABLE = 200
ERROR_RELOC_CHAIN_XEEDS_SEGLIM = 201
ERROR_INFLOOP_IN_RELOC_CHAIN = 202
ERROR_ENVVAR_NOT_FOUND = 203
ERROR_NO_SIGNAL_SENT = 205
ERROR_FILENAME_EXCED_RANGE = 206
ERROR_RING2_STACK_IN_USE = 207
ERROR_META_EXPANSION_TOO_LONG = 208
ERROR_INVALID_SIGNAL_NUMBER = 209
ERROR_THREAD_1_INACTIVE = 210
ERROR_LOCKED = 212
ERROR_TOO_MANY_MODULES = 214
ERROR_NESTING_NOT_ALLOWED = 215
ERROR_EXE_MACHINE_TYPE_MISMATCH = 216
ERROR_EXE_CANNOT_MODIFY_SIGNED_BINARY = 217
ERROR_EXE_CANNOT_MODIFY_STRONG_SIGNED_BINARY = 218
ERROR_FILE_CHECKED_OUT = 220
ERROR_CHECKOUT_REQUIRED = 221
ERROR_BAD_FILE_TYPE = 222
ERROR_FILE_TOO_LARGE = 223
ERROR_FORMS_AUTH_REQUIRED = 224
ERROR_VIRUS_INFECTED = 225
ERROR_VIRUS_DELETED = 226
ERROR_PIPE_LOCAL = 229
ERROR_BAD_PIPE = 230
ERROR_PIPE_BUSY = 231
ERROR_NO_DATA = 232
ERROR_PIPE_NOT_CONNECTED = 233
ERROR_MORE_DATA = 234
ERROR_NO_WORK_DONE = 235
ERROR_VC_DISCONNECTED = 240
ERROR_INVALID_EA_NAME = 254
ERROR_EA_LIST_INCONSISTENT = 255
WAIT_TIMEOUT = 258
ERROR_NO_MORE_ITEMS = 259
ERROR_CANNOT_COPY = 266
ERROR_DIRECTORY = 267
ERROR_EAS_DIDNT_FIT = 275
ERROR_EA_FILE_CORRUPT = 276
ERROR_EA_TABLE_FULL = 277
ERROR_INVALID_EA_HANDLE = 278
ERROR_EAS_NOT_SUPPORTED = 282
ERROR_NOT_OWNER = 288
ERROR_TOO_MANY_POSTS = 298
ERROR_PARTIAL_COPY = 299
ERROR_OPLOCK_NOT_GRANTED = 300
ERROR_INVALID_OPLOCK_PROTOCOL = 301
ERROR_DISK_TOO_FRAGMENTED = 302
ERROR_DELETE_PENDING = 303
ERROR_INCOMPATIBLE_WITH_GLOBAL_SHORT_NAME_REGISTRY_SETTING = 304
ERROR_SHORT_NAMES_NOT_ENABLED_ON_VOLUME = 305
ERROR_SECURITY_STREAM_IS_INCONSISTENT = 306
ERROR_INVALID_LOCK_RANGE = 307
ERROR_IMAGE_SUBSYSTEM_NOT_PRESENT = 308
ERROR_NOTIFICATION_GUID_ALREADY_DEFINED = 309
ERROR_INVALID_EXCEPTION_HANDLER = 310
ERROR_DUPLICATE_PRIVILEGES = 311
ERROR_NO_RANGES_PROCESSED = 312
ERROR_NOT_ALLOWED_ON_SYSTEM_FILE = 313
ERROR_DISK_RESOURCES_EXHAUSTED = 314
ERROR_INVALID_TOKEN = 315
ERROR_DEVICE_FEATURE_NOT_SUPPORTED = 316
ERROR_MR_MID_NOT_FOUND = 317
ERROR_SCOPE_NOT_FOUND = 318
ERROR_UNDEFINED_SCOPE = 319
ERROR_INVALID_CAP = 320
ERROR_DEVICE_UNREACHABLE = 321
ERROR_DEVICE_NO_RESOURCES = 322
ERROR_DATA_CHECKSUM_ERROR = 323
ERROR_INTERMIXED_KERNEL_EA_OPERATION = 324
ERROR_FILE_LEVEL_TRIM_NOT_SUPPORTED = 326
ERROR_OFFSET_ALIGNMENT_VIOLATION = 327
ERROR_INVALID_FIELD_IN_PARAMETER_LIST = 328
ERROR_OPERATION_IN_PROGRESS = 329
ERROR_BAD_DEVICE_PATH = 330
ERROR_TOO_MANY_DESCRIPTORS = 331
ERROR_SCRUB_DATA_DISABLED = 332
ERROR_NOT_REDUNDANT_STORAGE = 333
ERROR_RESIDENT_FILE_NOT_SUPPORTED = 334
ERROR_COMPRESSED_FILE_NOT_SUPPORTED = 335
ERROR_DIRECTORY_NOT_SUPPORTED = 336
ERROR_NOT_READ_FROM_COPY = 337
ERROR_FT_WRITE_FAILURE = 338
ERROR_FT_DI_SCAN_REQUIRED = 339
ERROR_INVALID_KERNEL_INFO_VERSION = 340
ERROR_INVALID_PEP_INFO_VERSION = 341
ERROR_OBJECT_NOT_EXTERNALLY_BACKED = 342
ERROR_EXTERNAL_BACKING_PROVIDER_UNKNOWN = 343
ERROR_COMPRESSION_NOT_BENEFICIAL = 344
ERROR_STORAGE_TOPOLOGY_ID_MISMATCH = 345
ERROR_BLOCKED_BY_PARENTAL_CONTROLS = 346
ERROR_BLOCK_TOO_MANY_REFERENCES = 347
ERROR_MARKED_TO_DISALLOW_WRITES = 348
ERROR_ENCLAVE_FAILURE = 349
ERROR_FAIL_NOACTION_REBOOT = 350
ERROR_FAIL_SHUTDOWN = 351
ERROR_FAIL_RESTART = 352
ERROR_MAX_SESSIONS_REACHED = 353
ERROR_NETWORK_ACCESS_DENIED_EDP = 354
ERROR_DEVICE_HINT_NAME_BUFFER_TOO_SMALL = 355
ERROR_EDP_POLICY_DENIES_OPERATION = 356
ERROR_EDP_DPL_POLICY_CANT_BE_SATISFIED = 357
ERROR_CLOUD_FILE_SYNC_ROOT_METADATA_CORRUPT = 358
ERROR_DEVICE_IN_MAINTENANCE = 359
ERROR_NOT_SUPPORTED_ON_DAX = 360
ERROR_DAX_MAPPING_EXISTS = 361
ERROR_CLOUD_FILE_PROVIDER_NOT_RUNNING = 362
ERROR_CLOUD_FILE_METADATA_CORRUPT = 363
ERROR_CLOUD_FILE_METADATA_TOO_LARGE = 364
ERROR_CLOUD_FILE_PROPERTY_BLOB_TOO_LARGE = 365
ERROR_CLOUD_FILE_PROPERTY_BLOB_CHECKSUM_MISMATCH = 366
ERROR_CHILD_PROCESS_BLOCKED = 367
ERROR_STORAGE_LOST_DATA_PERSISTENCE = 368
ERROR_FILE_SYSTEM_VIRTUALIZATION_UNAVAILABLE = 369
ERROR_FILE_SYSTEM_VIRTUALIZATION_METADATA_CORRUPT = 370
ERROR_FILE_SYSTEM_VIRTUALIZATION_BUSY = 371
ERROR_FILE_SYSTEM_VIRTUALIZATION_PROVIDER_UNKNOWN = 372
ERROR_GDI_HANDLE_LEAK = 373
ERROR_CLOUD_FILE_TOO_MANY_PROPERTY_BLOBS = 374
ERROR_CLOUD_FILE_PROPERTY_VERSION_NOT_SUPPORTED = 375
ERROR_NOT_A_CLOUD_FILE = 376
ERROR_CLOUD_FILE_NOT_IN_SYNC = 377
ERROR_CLOUD_FILE_ALREADY_CONNECTED = 378
ERROR_CLOUD_FILE_NOT_SUPPORTED = 379
ERROR_CLOUD_FILE_INVALID_REQUEST = 380
ERROR_CLOUD_FILE_READ_ONLY_VOLUME = 381
ERROR_CLOUD_FILE_CONNECTED_PROVIDER_ONLY = 382
ERROR_CLOUD_FILE_VALIDATION_FAILED = 383
ERROR_SMB1_NOT_AVAILABLE = 384
ERROR_FILE_SYSTEM_VIRTUALIZATION_INVALID_OPERATION = 385
ERROR_CLOUD_FILE_AUTHENTICATION_FAILED = 386
ERROR_CLOUD_FILE_INSUFFICIENT_RESOURCES = 387
ERROR_CLOUD_FILE_NETWORK_UNAVAILABLE = 388
ERROR_CLOUD_FILE_UNSUCCESSFUL = 389
ERROR_CLOUD_FILE_NOT_UNDER_SYNC_ROOT = 390
ERROR_CLOUD_FILE_IN_USE = 391
ERROR_CLOUD_FILE_PINNED = 392
ERROR_CLOUD_FILE_REQUEST_ABORTED = 393
ERROR_CLOUD_FILE_PROPERTY_CORRUPT = 394
ERROR_CLOUD_FILE_ACCESS_DENIED = 395
ERROR_CLOUD_FILE_INCOMPATIBLE_HARDLINKS = 396
ERROR_CLOUD_FILE_PROPERTY_LOCK_CONFLICT = 397
ERROR_CLOUD_FILE_REQUEST_CANCELED = 398
ERROR_EXTERNAL_SYSKEY_NOT_SUPPORTED = 399
ERROR_THREAD_MODE_ALREADY_BACKGROUND = 400
ERROR_THREAD_MODE_NOT_BACKGROUND = 401
ERROR_PROCESS_MODE_ALREADY_BACKGROUND = 402
ERROR_PROCESS_MODE_NOT_BACKGROUND = 403
ERROR_CLOUD_FILE_PROVIDER_TERMINATED = 404
ERROR_NOT_A_CLOUD_SYNC_ROOT = 405
ERROR_FILE_PROTECTED_UNDER_DPL = 406
ERROR_VOLUME_NOT_CLUSTER_ALIGNED = 407
ERROR_NO_PHYSICALLY_ALIGNED_FREE_SPACE_FOUND = 408
ERROR_APPX_FILE_NOT_ENCRYPTED = 409
ERROR_RWRAW_ENCRYPTED_FILE_NOT_ENCRYPTED = 410
ERROR_RWRAW_ENCRYPTED_INVALID_EDATAINFO_FILEOFFSET = 411
ERROR_RWRAW_ENCRYPTED_INVALID_EDATAINFO_FILERANGE = 412
ERROR_RWRAW_ENCRYPTED_INVALID_EDATAINFO_PARAMETER = 413
ERROR_LINUX_SUBSYSTEM_NOT_PRESENT = 414
ERROR_FT_READ_FAILURE = 415
ERROR_STORAGE_RESERVE_ID_INVALID = 416
ERROR_STORAGE_RESERVE_DOES_NOT_EXIST = 417
ERROR_STORAGE_RESERVE_ALREADY_EXISTS = 418
ERROR_STORAGE_RESERVE_NOT_EMPTY = 419
ERROR_NOT_A_DAX_VOLUME = 420
ERROR_NOT_DAX_MAPPABLE = 421
ERROR_TIME_SENSITIVE_THREAD = 422
ERROR_DPL_NOT_SUPPORTED_FOR_USER = 423
ERROR_CASE_DIFFERING_NAMES_IN_DIR = 424
ERROR_FILE_NOT_SUPPORTED = 425
ERROR_CLOUD_FILE_REQUEST_TIMEOUT = 426
ERROR_NO_TASK_QUEUE = 427
ERROR_SRC_SRV_DLL_LOAD_FAILED = 428
ERROR_NOT_SUPPORTED_WITH_BTT = 429
ERROR_ENCRYPTION_DISABLED = 430
ERROR_ENCRYPTING_METADATA_DISALLOWED = 431
ERROR_CANT_CLEAR_ENCRYPTION_FLAG = 432
ERROR_NO_SUCH_DEVICE = 433
ERROR_CLOUD_FILE_DEHYDRATION_DISALLOWED = 434
ERROR_FILE_SNAP_IN_PROGRESS = 435
ERROR_FILE_SNAP_USER_SECTION_NOT_SUPPORTED = 436
ERROR_FILE_SNAP_MODIFY_NOT_SUPPORTED = 437
ERROR_FILE_SNAP_IO_NOT_COORDINATED = 438
ERROR_FILE_SNAP_UNEXPECTED_ERROR = 439
ERROR_FILE_SNAP_INVALID_PARAMETER = 440
ERROR_UNSATISFIED_DEPENDENCIES = 441
ERROR_CASE_SENSITIVE_PATH = 442
ERROR_UNEXPECTED_NTCACHEMANAGER_ERROR = 443
ERROR_LINUX_SUBSYSTEM_UPDATE_REQUIRED = 444
ERROR_DLP_POLICY_WARNS_AGAINST_OPERATION = 445
ERROR_DLP_POLICY_DENIES_OPERATION = 446
ERROR_SECURITY_DENIES_OPERATION = 447
ERROR_UNTRUSTED_MOUNT_POINT = 448
ERROR_DLP_POLICY_SILENTLY_FAIL = 449
ERROR_CAPAUTHZ_NOT_DEVUNLOCKED = 450
ERROR_CAPAUTHZ_CHANGE_TYPE = 451
ERROR_CAPAUTHZ_NOT_PROVISIONED = 452
ERROR_CAPAUTHZ_NOT_AUTHORIZED = 453
ERROR_CAPAUTHZ_NO_POLICY = 454
ERROR_CAPAUTHZ_DB_CORRUPTED = 455
ERROR_CAPAUTHZ_SCCD_INVALID_CATALOG = 456
ERROR_CAPAUTHZ_SCCD_NO_AUTH_ENTITY = 457
ERROR_CAPAUTHZ_SCCD_PARSE_ERROR = 458
ERROR_CAPAUTHZ_SCCD_DEV_MODE_REQUIRED = 459
ERROR_CAPAUTHZ_SCCD_NO_CAPABILITY_MATCH = 460
ERROR_CIMFS_IMAGE_CORRUPT = 470
ERROR_CIMFS_IMAGE_VERSION_NOT_SUPPORTED = 471
ERROR_STORAGE_STACK_ACCESS_DENIED = 472
ERROR_INSUFFICIENT_VIRTUAL_ADDR_RESOURCES = 473
ERROR_INDEX_OUT_OF_BOUNDS = 474
ERROR_CLOUD_FILE_US_MESSAGE_TIMEOUT = 475
ERROR_NOT_A_DEV_VOLUME = 476
ERROR_FS_GUID_MISMATCH = 477
ERROR_CANT_ATTACH_TO_DEV_VOLUME = 478
ERROR_INVALID_CONFIG_VALUE = 479
ERROR_PNP_QUERY_REMOVE_DEVICE_TIMEOUT = 480
ERROR_PNP_QUERY_REMOVE_RELATED_DEVICE_TIMEOUT = 481
ERROR_PNP_QUERY_REMOVE_UNRELATED_DEVICE_TIMEOUT = 482
ERROR_DEVICE_HARDWARE_ERROR = 483
ERROR_INVALID_ADDRESS = 487
ERROR_HAS_SYSTEM_CRITICAL_FILES = 488
ERROR_ENCRYPTED_FILE_NOT_SUPPORTED = 489
ERROR_SPARSE_FILE_NOT_SUPPORTED = 490
ERROR_PAGEFILE_NOT_SUPPORTED = 491
ERROR_VOLUME_NOT_SUPPORTED = 492
ERROR_NOT_SUPPORTED_WITH_BYPASSIO = 493
ERROR_NO_BYPASSIO_DRIVER_SUPPORT = 494
ERROR_NOT_SUPPORTED_WITH_ENCRYPTION = 495
ERROR_NOT_SUPPORTED_WITH_COMPRESSION = 496
ERROR_NOT_SUPPORTED_WITH_REPLICATION = 497
ERROR_NOT_SUPPORTED_WITH_DEDUPLICATION = 498
ERROR_NOT_SUPPORTED_WITH_AUDITING = 499
ERROR_USER_PROFILE_LOAD = 500
ERROR_SESSION_KEY_TOO_SHORT = 501
ERROR_ACCESS_DENIED_APPDATA = 502
ERROR_NOT_SUPPORTED_WITH_MONITORING = 503
ERROR_NOT_SUPPORTED_WITH_SNAPSHOT = 504
ERROR_NOT_SUPPORTED_WITH_VIRTUALIZATION = 505
ERROR_BYPASSIO_FLT_NOT_SUPPORTED = 506
ERROR_DEVICE_RESET_REQUIRED = 507
ERROR_VOLUME_WRITE_ACCESS_DENIED = 508
ERROR_NOT_SUPPORTED_WITH_CACHED_HANDLE = 509
ERROR_FS_METADATA_INCONSISTENT = 510
ERROR_BLOCK_WEAK_REFERENCE_INVALID = 511
ERROR_BLOCK_SOURCE_WEAK_REFERENCE_INVALID = 512
ERROR_BLOCK_TARGET_WEAK_REFERENCE_INVALID = 513
ERROR_BLOCK_SHARED = 514
ERROR_VOLUME_UPGRADE_NOT_NEEDED = 515
ERROR_VOLUME_UPGRADE_PENDING = 516
ERROR_VOLUME_UPGRADE_DISABLED = 517
ERROR_VOLUME_UPGRADE_DISABLED_TILL_OS_DOWNGRADE_EXPIRED = 518
ERROR_ARITHMETIC_OVERFLOW = 534
ERROR_PIPE_CONNECTED = 535
ERROR_PIPE_LISTENING = 536
ERROR_VERIFIER_STOP = 537
ERROR_ABIOS_ERROR = 538
ERROR_WX86_WARNING = 539
ERROR_WX86_ERROR = 540
ERROR_TIMER_NOT_CANCELED = 541
ERROR_UNWIND = 542
ERROR_BAD_STACK = 543
ERROR_INVALID_UNWIND_TARGET = 544
ERROR_INVALID_PORT_ATTRIBUTES = 545
ERROR_PORT_MESSAGE_TOO_LONG = 546
ERROR_INVALID_QUOTA_LOWER = 547
ERROR_DEVICE_ALREADY_ATTACHED = 548
ERROR_INSTRUCTION_MISALIGNMENT = 549
ERROR_PROFILING_NOT_STARTED = 550
ERROR_PROFILING_NOT_STOPPED = 551
ERROR_COULD_NOT_INTERPRET = 552
ERROR_PROFILING_AT_LIMIT = 553
ERROR_CANT_WAIT = 554
ERROR_CANT_TERMINATE_SELF = 555
ERROR_UNEXPECTED_MM_CREATE_ERR = 556
ERROR_UNEXPECTED_MM_MAP_ERROR = 557
ERROR_UNEXPECTED_MM_EXTEND_ERR = 558
ERROR_BAD_FUNCTION_TABLE = 559
ERROR_NO_GUID_TRANSLATION = 560
ERROR_INVALID_LDT_SIZE = 561
ERROR_INVALID_LDT_OFFSET = 563
ERROR_INVALID_LDT_DESCRIPTOR = 564
ERROR_TOO_MANY_THREADS = 565
ERROR_THREAD_NOT_IN_PROCESS = 566
ERROR_PAGEFILE_QUOTA_EXCEEDED = 567
ERROR_LOGON_SERVER_CONFLICT = 568
ERROR_SYNCHRONIZATION_REQUIRED = 569
ERROR_NET_OPEN_FAILED = 570
ERROR_IO_PRIVILEGE_FAILED = 571
ERROR_CONTROL_C_EXIT = 572
ERROR_MISSING_SYSTEMFILE = 573
ERROR_UNHANDLED_EXCEPTION = 574
ERROR_APP_INIT_FAILURE = 575
ERROR_PAGEFILE_CREATE_FAILED = 576
ERROR_INVALID_IMAGE_HASH = 577
ERROR_NO_PAGEFILE = 578
ERROR_ILLEGAL_FLOAT_CONTEXT = 579
ERROR_NO_EVENT_PAIR = 580
ERROR_DOMAIN_CTRLR_CONFIG_ERROR = 581
ERROR_ILLEGAL_CHARACTER = 582
ERROR_UNDEFINED_CHARACTER = 583
ERROR_FLOPPY_VOLUME = 584
ERROR_BIOS_FAILED_TO_CONNECT_INTERRUPT = 585
ERROR_BACKUP_CONTROLLER = 586
ERROR_MUTANT_LIMIT_EXCEEDED = 587
ERROR_FS_DRIVER_REQUIRED = 588
ERROR_CANNOT_LOAD_REGISTRY_FILE = 589
ERROR_DEBUG_ATTACH_FAILED = 590
ERROR_SYSTEM_PROCESS_TERMINATED = 591
ERROR_DATA_NOT_ACCEPTED = 592
ERROR_VDM_HARD_ERROR = 593
ERROR_DRIVER_CANCEL_TIMEOUT = 594
ERROR_REPLY_MESSAGE_MISMATCH = 595
ERROR_LOST_WRITEBEHIND_DATA = 596
ERROR_CLIENT_SERVER_PARAMETERS_INVALID = 597
ERROR_NOT_TINY_STREAM = 598
ERROR_STACK_OVERFLOW_READ = 599
ERROR_CONVERT_TO_LARGE = 600
ERROR_FOUND_OUT_OF_SCOPE = 601
ERROR_ALLOCATE_BUCKET = 602
ERROR_MARSHALL_OVERFLOW = 603
ERROR_INVALID_VARIANT = 604
ERROR_BAD_COMPRESSION_BUFFER = 605
ERROR_AUDIT_FAILED = 606
ERROR_TIMER_RESOLUTION_NOT_SET = 607
ERROR_INSUFFICIENT_LOGON_INFO = 608
ERROR_BAD_DLL_ENTRYPOINT = 609
ERROR_BAD_SERVICE_ENTRYPOINT = 610
ERROR_IP_ADDRESS_CONFLICT1 = 611
ERROR_IP_ADDRESS_CONFLICT2 = 612
ERROR_REGISTRY_QUOTA_LIMIT = 613
ERROR_NO_CALLBACK_ACTIVE = 614
ERROR_PWD_TOO_SHORT = 615
ERROR_PWD_TOO_RECENT = 616
ERROR_PWD_HISTORY_CONFLICT = 617
ERROR_UNSUPPORTED_COMPRESSION = 618
ERROR_INVALID_HW_PROFILE = 619
ERROR_INVALID_PLUGPLAY_DEVICE_PATH = 620
ERROR_QUOTA_LIST_INCONSISTENT = 621
ERROR_EVALUATION_EXPIRATION = 622
ERROR_ILLEGAL_DLL_RELOCATION = 623
ERROR_DLL_INIT_FAILED_LOGOFF = 624
ERROR_VALIDATE_CONTINUE = 625
ERROR_NO_MORE_MATCHES = 626
ERROR_RANGE_LIST_CONFLICT = 627
ERROR_SERVER_SID_MISMATCH = 628
ERROR_CANT_ENABLE_DENY_ONLY = 629
ERROR_FLOAT_MULTIPLE_FAULTS = 630
ERROR_FLOAT_MULTIPLE_TRAPS = 631
ERROR_NOINTERFACE = 632
ERROR_DRIVER_FAILED_SLEEP = 633
ERROR_CORRUPT_SYSTEM_FILE = 634
ERROR_COMMITMENT_MINIMUM = 635
ERROR_PNP_RESTART_ENUMERATION = 636
ERROR_SYSTEM_IMAGE_BAD_SIGNATURE = 637
ERROR_PNP_REBOOT_REQUIRED = 638
ERROR_INSUFFICIENT_POWER = 639
ERROR_MULTIPLE_FAULT_VIOLATION = 640
ERROR_SYSTEM_SHUTDOWN = 641
ERROR_PORT_NOT_SET = 642
ERROR_DS_VERSION_CHECK_FAILURE = 643
ERROR_RANGE_NOT_FOUND = 644
ERROR_NOT_SAFE_MODE_DRIVER = 646
ERROR_FAILED_DRIVER_ENTRY = 647
ERROR_DEVICE_ENUMERATION_ERROR = 648
ERROR_MOUNT_POINT_NOT_RESOLVED = 649
ERROR_INVALID_DEVICE_OBJECT_PARAMETER = 650
ERROR_MCA_OCCURED = 651
ERROR_DRIVER_DATABASE_ERROR = 652
ERROR_SYSTEM_HIVE_TOO_LARGE = 653
ERROR_DRIVER_FAILED_PRIOR_UNLOAD = 654
ERROR_VOLSNAP_PREPARE_HIBERNATE = 655
ERROR_HIBERNATION_FAILURE = 656
ERROR_PWD_TOO_LONG = 657
ERROR_FILE_SYSTEM_LIMITATION = 665
ERROR_ASSERTION_FAILURE = 668
ERROR_ACPI_ERROR = 669
ERROR_WOW_ASSERTION = 670
ERROR_PNP_BAD_MPS_TABLE = 671
ERROR_PNP_TRANSLATION_FAILED = 672
ERROR_PNP_IRQ_TRANSLATION_FAILED = 673
ERROR_PNP_INVALID_ID = 674
ERROR_WAKE_SYSTEM_DEBUGGER = 675
ERROR_HANDLES_CLOSED = 676
ERROR_EXTRANEOUS_INFORMATION = 677
ERROR_RXACT_COMMIT_NECESSARY = 678
ERROR_MEDIA_CHECK = 679
ERROR_GUID_SUBSTITUTION_MADE = 680
ERROR_STOPPED_ON_SYMLINK = 681
ERROR_LONGJUMP = 682
ERROR_PLUGPLAY_QUERY_VETOED = 683
ERROR_UNWIND_CONSOLIDATE = 684
ERROR_REGISTRY_HIVE_RECOVERED = 685
ERROR_DLL_MIGHT_BE_INSECURE = 686
ERROR_DLL_MIGHT_BE_INCOMPATIBLE = 687
ERROR_DBG_EXCEPTION_NOT_HANDLED = 688
ERROR_DBG_REPLY_LATER = 689
ERROR_DBG_UNABLE_TO_PROVIDE_HANDLE = 690
ERROR_DBG_TERMINATE_THREAD = 691
ERROR_DBG_TERMINATE_PROCESS = 692
ERROR_DBG_CONTROL_C = 693
ERROR_DBG_PRINTEXCEPTION_C = 694
ERROR_DBG_RIPEXCEPTION = 695
ERROR_DBG_CONTROL_BREAK = 696
ERROR_DBG_COMMAND_EXCEPTION = 697
ERROR_OBJECT_NAME_EXISTS = 698
ERROR_THREAD_WAS_SUSPENDED = 699
ERROR_IMAGE_NOT_AT_BASE = 700
ERROR_RXACT_STATE_CREATED = 701
ERROR_SEGMENT_NOTIFICATION = 702
ERROR_BAD_CURRENT_DIRECTORY = 703
ERROR_FT_READ_RECOVERY_FROM_BACKUP = 704
ERROR_FT_WRITE_RECOVERY = 705
ERROR_IMAGE_MACHINE_TYPE_MISMATCH = 706
ERROR_RECEIVE_PARTIAL = 707
ERROR_RECEIVE_EXPEDITED = 708
ERROR_RECEIVE_PARTIAL_EXPEDITED = 709
ERROR_EVENT_DONE = 710
ERROR_EVENT_PENDING = 711
ERROR_CHECKING_FILE_SYSTEM = 712
ERROR_FATAL_APP_EXIT = 713
ERROR_PREDEFINED_HANDLE = 714
ERROR_WAS_UNLOCKED = 715
ERROR_SERVICE_NOTIFICATION = 716
ERROR_WAS_LOCKED = 717
ERROR_LOG_HARD_ERROR = 718
ERROR_ALREADY_WIN32 = 719
ERROR_IMAGE_MACHINE_TYPE_MISMATCH_EXE = 720
ERROR_NO_YIELD_PERFORMED = 721
ERROR_TIMER_RESUME_IGNORED = 722
ERROR_ARBITRATION_UNHANDLED = 723
ERROR_CARDBUS_NOT_SUPPORTED = 724
ERROR_MP_PROCESSOR_MISMATCH = 725
ERROR_HIBERNATED = 726
ERROR_RESUME_HIBERNATION = 727
ERROR_FIRMWARE_UPDATED = 728
ERROR_DRIVERS_LEAKING_LOCKED_PAGES = 729
ERROR_WAKE_SYSTEM = 730
ERROR_WAIT_1 = 731
ERROR_WAIT_2 = 732
ERROR_WAIT_3 = 733
ERROR_WAIT_63 = 734
ERROR_ABANDONED_WAIT_0 = 735
ERROR_ABANDONED_WAIT_63 = 736
ERROR_USER_APC = 737
ERROR_KERNEL_APC = 738
ERROR_ALERTED = 739
ERROR_ELEVATION_REQUIRED = 740
ERROR_REPARSE = 741
ERROR_OPLOCK_BREAK_IN_PROGRESS = 742
ERROR_VOLUME_MOUNTED = 743
ERROR_RXACT_COMMITTED = 744
ERROR_NOTIFY_CLEANUP = 745
ERROR_PRIMARY_TRANSPORT_CONNECT_FAILED = 746
ERROR_PAGE_FAULT_TRANSITION = 747
ERROR_PAGE_FAULT_DEMAND_ZERO = 748
ERROR_PAGE_FAULT_COPY_ON_WRITE = 749
ERROR_PAGE_FAULT_GUARD_PAGE = 750
ERROR_PAGE_FAULT_PAGING_FILE = 751
ERROR_CACHE_PAGE_LOCKED = 752
ERROR_CRASH_DUMP = 753
ERROR_BUFFER_ALL_ZEROS = 754
ERROR_REPARSE_OBJECT = 755
ERROR_RESOURCE_REQUIREMENTS_CHANGED = 756
ERROR_TRANSLATION_COMPLETE = 757
ERROR_NOTHING_TO_TERMINATE = 758
ERROR_PROCESS_NOT_IN_JOB = 759
ERROR_PROCESS_IN_JOB = 760
ERROR_VOLSNAP_HIBERNATE_READY = 761
ERROR_FSFILTER_OP_COMPLETED_SUCCESSFULLY = 762
ERROR_INTERRUPT_VECTOR_ALREADY_CONNECTED = 763
ERROR_INTERRUPT_STILL_CONNECTED = 764
ERROR_WAIT_FOR_OPLOCK = 765
ERROR_DBG_EXCEPTION_HANDLED = 766
ERROR_DBG_CONTINUE = 767
ERROR_CALLBACK_POP_STACK = 768
ERROR_COMPRESSION_DISABLED = 769
ERROR_CANTFETCHBACKWARDS = 770
ERROR_CANTSCROLLBACKWARDS = 771
ERROR_ROWSNOTRELEASED = 772
ERROR_BAD_ACCESSOR_FLAGS = 773
ERROR_ERRORS_ENCOUNTERED = 774
ERROR_NOT_CAPABLE = 775
ERROR_REQUEST_OUT_OF_SEQUENCE = 776
ERROR_VERSION_PARSE_ERROR = 777
ERROR_BADSTARTPOSITION = 778
ERROR_MEMORY_HARDWARE = 779
ERROR_DISK_REPAIR_DISABLED = 780
ERROR_INSUFFICIENT_RESOURCE_FOR_SPECIFIED_SHARED_SECTION_SIZE = 781
ERROR_SYSTEM_POWERSTATE_TRANSITION = 782
ERROR_SYSTEM_POWERSTATE_COMPLEX_TRANSITION = 783
ERROR_MCA_EXCEPTION = 784
ERROR_ACCESS_AUDIT_BY_POLICY = 785
ERROR_ACCESS_DISABLED_NO_SAFER_UI_BY_POLICY = 786
ERROR_ABANDON_HIBERFILE = 787
ERROR_LOST_WRITEBEHIND_DATA_NETWORK_DISCONNECTED = 788
ERROR_LOST_WRITEBEHIND_DATA_NETWORK_SERVER_ERROR = 789
ERROR_LOST_WRITEBEHIND_DATA_LOCAL_DISK_ERROR = 790
ERROR_BAD_MCFG_TABLE = 791
ERROR_DISK_REPAIR_REDIRECTED = 792
ERROR_DISK_REPAIR_UNSUCCESSFUL = 793
ERROR_CORRUPT_LOG_OVERFULL = 794
ERROR_CORRUPT_LOG_CORRUPTED = 795
ERROR_CORRUPT_LOG_UNAVAILABLE = 796
ERROR_CORRUPT_LOG_DELETED_FULL = 797
ERROR_CORRUPT_LOG_CLEARED = 798
ERROR_ORPHAN_NAME_EXHAUSTED = 799
ERROR_OPLOCK_SWITCHED_TO_NEW_HANDLE = 800
ERROR_CANNOT_GRANT_REQUESTED_OPLOCK = 801
ERROR_CANNOT_BREAK_OPLOCK = 802
ERROR_OPLOCK_HANDLE_CLOSED = 803
ERROR_NO_ACE_CONDITION = 804
ERROR_INVALID_ACE_CONDITION = 805
ERROR_FILE_HANDLE_REVOKED = 806
ERROR_IMAGE_AT_DIFFERENT_BASE = 807
ERROR_ENCRYPTED_IO_NOT_POSSIBLE = 808
ERROR_FILE_METADATA_OPTIMIZATION_IN_PROGRESS = 809
ERROR_QUOTA_ACTIVITY = 810
ERROR_HANDLE_REVOKED = 811
ERROR_CALLBACK_INVOKE_INLINE = 812
ERROR_CPU_SET_INVALID = 813
ERROR_ENCLAVE_NOT_TERMINATED = 814
ERROR_ENCLAVE_VIOLATION = 815
ERROR_SERVER_TRANSPORT_CONFLICT = 816
ERROR_CERTIFICATE_VALIDATION_PREFERENCE_CONFLICT = 817
ERROR_FT_READ_FROM_COPY_FAILURE = 818
ERROR_SECTION_DIRECT_MAP_ONLY = 819
ERROR_EA_ACCESS_DENIED = 994
ERROR_OPERATION_ABORTED = 995
ERROR_IO_INCOMPLETE = 996
ERROR_IO_PENDING = 997
ERROR_NOACCESS = 998
ERROR_SWAPERROR = 999
ERROR_STACK_OVERFLOW = 1001
ERROR_INVALID_MESSAGE = 1002
ERROR_CAN_NOT_COMPLETE = 1003
ERROR_INVALID_FLAGS = 1004
ERROR_UNRECOGNIZED_VOLUME = 1005
ERROR_FILE_INVALID = 1006
ERROR_FULLSCREEN_MODE = 1007
ERROR_NO_TOKEN = 1008
ERROR_BADDB = 1009
ERROR_BADKEY = 1010
ERROR_CANTOPEN = 1011
ERROR_CANTREAD = 1012
ERROR_CANTWRITE = 1013
ERROR_REGISTRY_RECOVERED = 1014
ERROR_REGISTRY_CORRUPT = 1015
ERROR_REGISTRY_IO_FAILED = 1016
ERROR_NOT_REGISTRY_FILE = 1017
ERROR_KEY_DELETED = 1018
ERROR_NO_LOG_SPACE = 1019
ERROR_KEY_HAS_CHILDREN = 1020
ERROR_CHILD_MUST_BE_VOLATILE = 1021
ERROR_NOTIFY_ENUM_DIR = 1022
ERROR_DEPENDENT_SERVICES_RUNNING = 1051
ERROR_INVALID_SERVICE_CONTROL = 1052
ERROR_SERVICE_REQUEST_TIMEOUT = 1053
ERROR_SERVICE_NO_THREAD = 1054
ERROR_SERVICE_DATABASE_LOCKED = 1055
ERROR_SERVICE_ALREADY_RUNNING = 1056
ERROR_INVALID_SERVICE_ACCOUNT = 1057
ERROR_SERVICE_DISABLED = 1058
ERROR_CIRCULAR_DEPENDENCY = 1059
ERROR_SERVICE_DOES_NOT_EXIST = 1060
ERROR_SERVICE_CANNOT_ACCEPT_CTRL = 1061
ERROR_SERVICE_NOT_ACTIVE = 1062
ERROR_FAILED_SERVICE_CONTROLLER_CONNECT = 1063
ERROR_EXCEPTION_IN_SERVICE = 1064
ERROR_DATABASE_DOES_NOT_EXIST = 1065
ERROR_SERVICE_SPECIFIC_ERROR = 1066
ERROR_PROCESS_ABORTED = 1067
ERROR_SERVICE_DEPENDENCY_FAIL = 1068
ERROR_SERVICE_LOGON_FAILED = 1069
ERROR_SERVICE_START_HANG = 1070
ERROR_INVALID_SERVICE_LOCK = 1071
ERROR_SERVICE_MARKED_FOR_DELETE = 1072
ERROR_SERVICE_EXISTS = 1073
ERROR_ALREADY_RUNNING_LKG = 1074
ERROR_SERVICE_DEPENDENCY_DELETED = 1075
ERROR_BOOT_ALREADY_ACCEPTED = 1076
ERROR_SERVICE_NEVER_STARTED = 1077
ERROR_DUPLICATE_SERVICE_NAME = 1078
ERROR_DIFFERENT_SERVICE_ACCOUNT = 1079
ERROR_CANNOT_DETECT_DRIVER_FAILURE = 1080
ERROR_CANNOT_DETECT_PROCESS_ABORT = 1081
ERROR_NO_RECOVERY_PROGRAM = 1082
ERROR_SERVICE_NOT_IN_EXE = 1083
ERROR_NOT_SAFEBOOT_SERVICE = 1084
ERROR_END_OF_MEDIA = 1100
ERROR_FILEMARK_DETECTED = 1101
ERROR_BEGINNING_OF_MEDIA = 1102
ERROR_SETMARK_DETECTED = 1103
ERROR_NO_DATA_DETECTED = 1104
ERROR_PARTITION_FAILURE = 1105
ERROR_INVALID_BLOCK_LENGTH = 1106
ERROR_DEVICE_NOT_PARTITIONED = 1107
ERROR_UNABLE_TO_LOCK_MEDIA = 1108
ERROR_UNABLE_TO_UNLOAD_MEDIA = 1109
ERROR_MEDIA_CHANGED = 1110
ERROR_BUS_RESET = 1111
ERROR_NO_MEDIA_IN_DRIVE = 1112
ERROR_NO_UNICODE_TRANSLATION = 1113
ERROR_DLL_INIT_FAILED = 1114
ERROR_SHUTDOWN_IN_PROGRESS = 1115
ERROR_NO_SHUTDOWN_IN_PROGRESS = 1116
ERROR_IO_DEVICE = 1117
ERROR_SERIAL_NO_DEVICE = 1118
ERROR_IRQ_BUSY = 1119
ERROR_MORE_WRITES = 1120
ERROR_COUNTER_TIMEOUT = 1121
ERROR_FLOPPY_ID_MARK_NOT_FOUND = 1122
ERROR_FLOPPY_WRONG_CYLINDER = 1123
ERROR_FLOPPY_UNKNOWN_ERROR = 1124
ERROR_FLOPPY_BAD_REGISTERS = 1125
ERROR_DISK_RECALIBRATE_FAILED = 1126
ERROR_DISK_OPERATION_FAILED = 1127
ERROR_DISK_RESET_FAILED = 1128
ERROR_EOM_OVERFLOW = 1129
ERROR_NOT_ENOUGH_SERVER_MEMORY = 1130
ERROR_POSSIBLE_DEADLOCK = 1131
ERROR_MAPPED_ALIGNMENT = 1132
ERROR_SET_POWER_STATE_VETOED = 1140
ERROR_SET_POWER_STATE_FAILED = 1141
ERROR_TOO_MANY_LINKS = 1142
ERROR_OLD_WIN_VERSION = 1150
ERROR_APP_WRONG_OS = 1151
ERROR_SINGLE_INSTANCE_APP = 1152
ERROR_RMODE_APP = 1153
ERROR_INVALID_DLL = 1154
ERROR_NO_ASSOCIATION = 1155
ERROR_DDE_FAIL = 1156
ERROR_DLL_NOT_FOUND = 1157
ERROR_NO_MORE_USER_HANDLES = 1158
ERROR_MESSAGE_SYNC_ONLY = 1159
ERROR_SOURCE_ELEMENT_EMPTY = 1160
ERROR_DESTINATION_ELEMENT_FULL = 1161
ERROR_ILLEGAL_ELEMENT_ADDRESS = 1162
ERROR_MAGAZINE_NOT_PRESENT = 1163
ERROR_DEVICE_REINITIALIZATION_NEEDED = 1164
ERROR_DEVICE_REQUIRES_CLEANING = 1165
ERROR_DEVICE_DOOR_OPEN = 1166
ERROR_DEVICE_NOT_CONNECTED = 1167
ERROR_NOT_FOUND = 1168
ERROR_NO_MATCH = 1169
ERROR_SET_NOT_FOUND = 1170
ERROR_POINT_NOT_FOUND = 1171
ERROR_NO_TRACKING_SERVICE = 1172
ERROR_NO_VOLUME_ID = 1173
ERROR_UNABLE_TO_REMOVE_REPLACED = 1175
ERROR_UNABLE_TO_MOVE_REPLACEMENT = 1176
ERROR_UNABLE_TO_MOVE_REPLACEMENT_2 = 1177
ERROR_JOURNAL_DELETE_IN_PROGRESS = 1178
ERROR_JOURNAL_NOT_ACTIVE = 1179
ERROR_POTENTIAL_FILE_FOUND = 1180
ERROR_JOURNAL_ENTRY_DELETED = 1181
ERROR_PARTITION_TERMINATING = 1184
ERROR_SHUTDOWN_IS_SCHEDULED = 1190
ERROR_SHUTDOWN_USERS_LOGGED_ON = 1191
ERROR_SHUTDOWN_DISKS_NOT_IN_MAINTENANCE_MODE = 1192
ERROR_BAD_DEVICE = 1200
ERROR_CONNECTION_UNAVAIL = 1201
ERROR_DEVICE_ALREADY_REMEMBERED = 1202
ERROR_NO_NET_OR_BAD_PATH = 1203
ERROR_BAD_PROVIDER = 1204
ERROR_CANNOT_OPEN_PROFILE = 1205
ERROR_BAD_PROFILE = 1206
ERROR_NOT_CONTAINER = 1207
ERROR_EXTENDED_ERROR = 1208
ERROR_INVALID_GROUPNAME = 1209
ERROR_INVALID_COMPUTERNAME = 1210
ERROR_INVALID_EVENTNAME = 1211
ERROR_INVALID_DOMAINNAME = 1212
ERROR_INVALID_SERVICENAME = 1213
ERROR_INVALID_NETNAME = 1214
ERROR_INVALID_SHARENAME = 1215
ERROR_INVALID_PASSWORDNAME = 1216
ERROR_INVALID_MESSAGENAME = 1217
ERROR_INVALID_MESSAGEDEST = 1218
ERROR_SESSION_CREDENTIAL_CONFLICT = 1219
ERROR_REMOTE_SESSION_LIMIT_EXCEEDED = 1220
ERROR_DUP_DOMAINNAME = 1221
ERROR_NO_NETWORK = 1222
ERROR_CANCELLED = 1223
ERROR_USER_MAPPED_FILE = 1224
ERROR_CONNECTION_REFUSED = 1225
ERROR_GRACEFUL_DISCONNECT = 1226
ERROR_ADDRESS_ALREADY_ASSOCIATED = 1227
ERROR_ADDRESS_NOT_ASSOCIATED = 1228
ERROR_CONNECTION_INVALID = 1229
ERROR_CONNECTION_ACTIVE = 1230
ERROR_NETWORK_UNREACHABLE = 1231
ERROR_HOST_UNREACHABLE = 1232
ERROR_PROTOCOL_UNREACHABLE = 1233
ERROR_PORT_UNREACHABLE = 1234
ERROR_REQUEST_ABORTED = 1235
ERROR_CONNECTION_ABORTED = 1236
ERROR_RETRY = 1237
ERROR_CONNECTION_COUNT_LIMIT = 1238
ERROR_LOGIN_TIME_RESTRICTION = 1239
ERROR_LOGIN_WKSTA_RESTRICTION = 1240
ERROR_INCORRECT_ADDRESS = 1241
ERROR_ALREADY_REGISTERED = 1242
ERROR_SERVICE_NOT_FOUND = 1243
ERROR_NOT_AUTHENTICATED = 1244
ERROR_NOT_LOGGED_ON = 1245
ERROR_CONTINUE = 1246
ERROR_ALREADY_INITIALIZED = 1247
ERROR_NO_MORE_DEVICES = 1248
ERROR_NO_SUCH_SITE = 1249
ERROR_DOMAIN_CONTROLLER_EXISTS = 1250
ERROR_ONLY_IF_CONNECTED = 1251
ERROR_OVERRIDE_NOCHANGES = 1252
ERROR_BAD_USER_PROFILE = 1253
ERROR_NOT_SUPPORTED_ON_SBS = 1254
ERROR_SERVER_SHUTDOWN_IN_PROGRESS = 1255
ERROR_HOST_DOWN = 1256
ERROR_NON_ACCOUNT_SID = 1257
ERROR_NON_DOMAIN_SID = 1258
ERROR_APPHELP_BLOCK = 1259
ERROR_ACCESS_DISABLED_BY_POLICY = 1260
ERROR_REG_NAT_CONSUMPTION = 1261
ERROR_CSCSHARE_OFFLINE = 1262
ERROR_PKINIT_FAILURE = 1263
ERROR_SMARTCARD_SUBSYSTEM_FAILURE = 1264
ERROR_DOWNGRADE_DETECTED = 1265
ERROR_MACHINE_LOCKED = 1271
ERROR_SMB_GUEST_LOGON_BLOCKED = 1272
ERROR_CALLBACK_SUPPLIED_INVALID_DATA = 1273
ERROR_SYNC_FOREGROUND_REFRESH_REQUIRED = 1274
ERROR_DRIVER_BLOCKED = 1275
ERROR_INVALID_IMPORT_OF_NON_DLL = 1276
ERROR_ACCESS_DISABLED_WEBBLADE = 1277
ERROR_ACCESS_DISABLED_WEBBLADE_TAMPER = 1278
ERROR_RECOVERY_FAILURE = 1279
ERROR_ALREADY_FIBER = 1280
ERROR_ALREADY_THREAD = 1281
ERROR_STACK_BUFFER_OVERRUN = 1282
ERROR_PARAMETER_QUOTA_EXCEEDED = 1283
ERROR_DEBUGGER_INACTIVE = 1284
ERROR_DELAY_LOAD_FAILED = 1285
ERROR_VDM_DISALLOWED = 1286
ERROR_UNIDENTIFIED_ERROR = 1287
ERROR_INVALID_CRUNTIME_PARAMETER = 1288
ERROR_BEYOND_VDL = 1289
ERROR_INCOMPATIBLE_SERVICE_SID_TYPE = 1290
ERROR_DRIVER_PROCESS_TERMINATED = 1291
ERROR_IMPLEMENTATION_LIMIT = 1292
ERROR_PROCESS_IS_PROTECTED = 1293
ERROR_SERVICE_NOTIFY_CLIENT_LAGGING = 1294
ERROR_DISK_QUOTA_EXCEEDED = 1295
ERROR_CONTENT_BLOCKED = 1296
ERROR_INCOMPATIBLE_SERVICE_PRIVILEGE = 1297
ERROR_APP_HANG = 1298
ERROR_INVALID_LABEL = 1299
ERROR_NOT_ALL_ASSIGNED = 1300
ERROR_SOME_NOT_MAPPED = 1301
ERROR_NO_QUOTAS_FOR_ACCOUNT = 1302
ERROR_LOCAL_USER_SESSION_KEY = 1303
ERROR_NULL_LM_PASSWORD = 1304
ERROR_UNKNOWN_REVISION = 1305
ERROR_REVISION_MISMATCH = 1306
ERROR_INVALID_OWNER = 1307
ERROR_INVALID_PRIMARY_GROUP = 1308
ERROR_NO_IMPERSONATION_TOKEN = 1309
ERROR_CANT_DISABLE_MANDATORY = 1310
ERROR_NO_LOGON_SERVERS = 1311
ERROR_NO_SUCH_LOGON_SESSION = 1312
ERROR_NO_SUCH_PRIVILEGE = 1313
ERROR_PRIVILEGE_NOT_HELD = 1314
ERROR_INVALID_ACCOUNT_NAME = 1315
ERROR_USER_EXISTS = 1316
ERROR_NO_SUCH_USER = 1317
ERROR_GROUP_EXISTS = 1318
ERROR_NO_SUCH_GROUP = 1319
ERROR_MEMBER_IN_GROUP = 1320
ERROR_MEMBER_NOT_IN_GROUP = 1321
ERROR_LAST_ADMIN = 1322
ERROR_WRONG_PASSWORD = 1323
ERROR_ILL_FORMED_PASSWORD = 1324
ERROR_PASSWORD_RESTRICTION = 1325
ERROR_LOGON_FAILURE = 1326
ERROR_ACCOUNT_RESTRICTION = 1327
ERROR_INVALID_LOGON_HOURS = 1328
ERROR_INVALID_WORKSTATION = 1329
ERROR_PASSWORD_EXPIRED = 1330
ERROR_ACCOUNT_DISABLED = 1331
ERROR_NONE_MAPPED = 1332
ERROR_TOO_MANY_LUIDS_REQUESTED = 1333
ERROR_LUIDS_EXHAUSTED = 1334
ERROR_INVALID_SUB_AUTHORITY = 1335
ERROR_INVALID_ACL = 1336
ERROR_INVALID_SID = 1337
ERROR_INVALID_SECURITY_DESCR = 1338
ERROR_BAD_INHERITANCE_ACL = 1340
ERROR_SERVER_DISABLED = 1341
ERROR_SERVER_NOT_DISABLED = 1342
ERROR_INVALID_ID_AUTHORITY = 1343
ERROR_ALLOTTED_SPACE_EXCEEDED = 1344
ERROR_INVALID_GROUP_ATTRIBUTES = 1345
ERROR_BAD_IMPERSONATION_LEVEL = 1346
ERROR_CANT_OPEN_ANONYMOUS = 1347
ERROR_BAD_VALIDATION_CLASS = 1348
ERROR_BAD_TOKEN_TYPE = 1349
ERROR_NO_SECURITY_ON_OBJECT = 1350
ERROR_CANT_ACCESS_DOMAIN_INFO = 1351
ERROR_INVALID_SERVER_STATE = 1352
ERROR_INVALID_DOMAIN_STATE = 1353
ERROR_INVALID_DOMAIN_ROLE = 1354
ERROR_NO_SUCH_DOMAIN = 1355
ERROR_DOMAIN_EXISTS = 1356
ERROR_DOMAIN_LIMIT_EXCEEDED = 1357
ERROR_INTERNAL_DB_CORRUPTION = 1358
ERROR_INTERNAL_ERROR = 1359
ERROR_GENERIC_NOT_MAPPED = 1360
ERROR_BAD_DESCRIPTOR_FORMAT = 1361
ERROR_NOT_LOGON_PROCESS = 1362
ERROR_LOGON_SESSION_EXISTS = 1363
ERROR_NO_SUCH_PACKAGE = 1364
ERROR_BAD_LOGON_SESSION_STATE = 1365
ERROR_LOGON_SESSION_COLLISION = 1366
ERROR_INVALID_LOGON_TYPE = 1367
ERROR_CANNOT_IMPERSONATE = 1368
ERROR_RXACT_INVALID_STATE = 1369
ERROR_RXACT_COMMIT_FAILURE = 1370
ERROR_SPECIAL_ACCOUNT = 1371
ERROR_SPECIAL_GROUP = 1372
ERROR_SPECIAL_USER = 1373
ERROR_MEMBERS_PRIMARY_GROUP = 1374
ERROR_TOKEN_ALREADY_IN_USE = 1375
ERROR_NO_SUCH_ALIAS = 1376
ERROR_MEMBER_NOT_IN_ALIAS = 1377
ERROR_MEMBER_IN_ALIAS = 1378
ERROR_ALIAS_EXISTS = 1379
ERROR_LOGON_NOT_GRANTED = 1380
ERROR_TOO_MANY_SECRETS = 1381
ERROR_SECRET_TOO_LONG = 1382
ERROR_INTERNAL_DB_ERROR = 1383
ERROR_TOO_MANY_CONTEXT_IDS = 1384
ERROR_LOGON_TYPE_NOT_GRANTED = 1385
ERROR_NT_CROSS_ENCRYPTION_REQUIRED = 1386
ERROR_NO_SUCH_MEMBER = 1387
ERROR_INVALID_MEMBER = 1388
ERROR_TOO_MANY_SIDS = 1389
ERROR_LM_CROSS_ENCRYPTION_REQUIRED = 1390
ERROR_NO_INHERITANCE = 1391
ERROR_FILE_CORRUPT = 1392
ERROR_DISK_CORRUPT = 1393
ERROR_NO_USER_SESSION_KEY = 1394
ERROR_LICENSE_QUOTA_EXCEEDED = 1395
ERROR_WRONG_TARGET_NAME = 1396
ERROR_MUTUAL_AUTH_FAILED = 1397
ERROR_TIME_SKEW = 1398
ERROR_CURRENT_DOMAIN_NOT_ALLOWED = 1399
ERROR_INVALID_WINDOW_HANDLE = 1400
ERROR_INVALID_MENU_HANDLE = 1401
ERROR_INVALID_CURSOR_HANDLE = 1402
ERROR_INVALID_ACCEL_HANDLE = 1403
ERROR_INVALID_HOOK_HANDLE = 1404
ERROR_INVALID_DWP_HANDLE = 1405
ERROR_TLW_WITH_WSCHILD = 1406
ERROR_CANNOT_FIND_WND_CLASS = 1407
ERROR_WINDOW_OF_OTHER_THREAD = 1408
ERROR_HOTKEY_ALREADY_REGISTERED = 1409
ERROR_CLASS_ALREADY_EXISTS = 1410
ERROR_CLASS_DOES_NOT_EXIST = 1411
ERROR_CLASS_HAS_WINDOWS = 1412
ERROR_INVALID_INDEX = 1413
ERROR_INVALID_ICON_HANDLE = 1414
ERROR_PRIVATE_DIALOG_INDEX = 1415
ERROR_LISTBOX_ID_NOT_FOUND = 1416
ERROR_NO_WILDCARD_CHARACTERS = 1417
ERROR_CLIPBOARD_NOT_OPEN = 1418
ERROR_HOTKEY_NOT_REGISTERED = 1419
ERROR_WINDOW_NOT_DIALOG = 1420
ERROR_CONTROL_ID_NOT_FOUND = 1421
ERROR_INVALID_COMBOBOX_MESSAGE = 1422
ERROR_WINDOW_NOT_COMBOBOX = 1423
ERROR_INVALID_EDIT_HEIGHT = 1424
ERROR_DC_NOT_FOUND = 1425
ERROR_INVALID_HOOK_FILTER = 1426
ERROR_INVALID_FILTER_PROC = 1427
ERROR_HOOK_NEEDS_HMOD = 1428
ERROR_GLOBAL_ONLY_HOOK = 1429
ERROR_JOURNAL_HOOK_SET = 1430
ERROR_HOOK_NOT_INSTALLED = 1431
ERROR_INVALID_LB_MESSAGE = 1432
ERROR_SETCOUNT_ON_BAD_LB = 1433
ERROR_LB_WITHOUT_TABSTOPS = 1434
ERROR_DESTROY_OBJECT_OF_OTHER_THREAD = 1435
ERROR_CHILD_WINDOW_MENU = 1436
ERROR_NO_SYSTEM_MENU = 1437
ERROR_INVALID_MSGBOX_STYLE = 1438
ERROR_INVALID_SPI_VALUE = 1439
ERROR_SCREEN_ALREADY_LOCKED = 1440
ERROR_HWNDS_HAVE_DIFF_PARENT = 1441
ERROR_NOT_CHILD_WINDOW = 1442
ERROR_INVALID_GW_COMMAND = 1443
ERROR_INVALID_THREAD_ID = 1444
ERROR_NON_MDICHILD_WINDOW = 1445
ERROR_POPUP_ALREADY_ACTIVE = 1446
ERROR_NO_SCROLLBARS = 1447
ERROR_INVALID_SCROLLBAR_RANGE = 1448
ERROR_INVALID_SHOWWIN_COMMAND = 1449
ERROR_NO_SYSTEM_RESOURCES = 1450
ERROR_NONPAGED_SYSTEM_RESOURCES = 1451
ERROR_PAGED_SYSTEM_RESOURCES = 1452
ERROR_WORKING_SET_QUOTA = 1453
ERROR_PAGEFILE_QUOTA = 1454
ERROR_COMMITMENT_LIMIT = 1455
ERROR_MENU_ITEM_NOT_FOUND = 1456
ERROR_INVALID_KEYBOARD_HANDLE = 1457
ERROR_HOOK_TYPE_NOT_ALLOWED = 1458
ERROR_REQUIRES_INTERACTIVE_WINDOWSTATION = 1459
ERROR_TIMEOUT = 1460
ERROR_INVALID_MONITOR_HANDLE = 1461
ERROR_INCORRECT_SIZE = 1462
ERROR_SYMLINK_CLASS_DISABLED = 1463
ERROR_SYMLINK_NOT_SUPPORTED = 1464
ERROR_XML_PARSE_ERROR = 1465
ERROR_XMLDSIG_ERROR = 1466
ERROR_RESTART_APPLICATION = 1467
ERROR_WRONG_COMPARTMENT = 1468
ERROR_AUTHIP_FAILURE = 1469
ERROR_NO_NVRAM_RESOURCES = 1470
ERROR_NOT_GUI_PROCESS = 1471
ERROR_EVENTLOG_FILE_CORRUPT = 1500
ERROR_EVENTLOG_CANT_START = 1501
ERROR_LOG_FILE_FULL = 1502
ERROR_EVENTLOG_FILE_CHANGED = 1503
ERROR_CONTAINER_ASSIGNED = 1504
ERROR_JOB_NO_CONTAINER = 1505
ERROR_INVALID_TASK_NAME = 1550
ERROR_INVALID_TASK_INDEX = 1551
ERROR_THREAD_ALREADY_IN_TASK = 1552
ERROR_INSTALL_SERVICE_FAILURE = 1601
ERROR_INSTALL_USEREXIT = 1602
ERROR_INSTALL_FAILURE = 1603
ERROR_INSTALL_SUSPEND = 1604
ERROR_UNKNOWN_PRODUCT = 1605
ERROR_UNKNOWN_FEATURE = 1606
ERROR_UNKNOWN_COMPONENT = 1607
ERROR_UNKNOWN_PROPERTY = 1608
ERROR_INVALID_HANDLE_STATE = 1609
ERROR_BAD_CONFIGURATION = 1610
ERROR_INDEX_ABSENT = 1611
ERROR_INSTALL_SOURCE_ABSENT = 1612
ERROR_INSTALL_PACKAGE_VERSION = 1613
ERROR_PRODUCT_UNINSTALLED = 1614
ERROR_BAD_QUERY_SYNTAX = 1615
ERROR_INVALID_FIELD = 1616
ERROR_DEVICE_REMOVED = 1617
ERROR_INSTALL_ALREADY_RUNNING = 1618
ERROR_INSTALL_PACKAGE_OPEN_FAILED = 1619
ERROR_INSTALL_PACKAGE_INVALID = 1620
ERROR_INSTALL_UI_FAILURE = 1621
ERROR_INSTALL_LOG_FAILURE = 1622
ERROR_INSTALL_LANGUAGE_UNSUPPORTED = 1623
ERROR_INSTALL_TRANSFORM_FAILURE = 1624
ERROR_INSTALL_PACKAGE_REJECTED = 1625
ERROR_FUNCTION_NOT_CALLED = 1626
ERROR_FUNCTION_FAILED = 1627
ERROR_INVALID_TABLE = 1628
ERROR_DATATYPE_MISMATCH = 1629
ERROR_UNSUPPORTED_TYPE = 1630
ERROR_CREATE_FAILED = 1631
ERROR_INSTALL_TEMP_UNWRITABLE = 1632
ERROR_INSTALL_PLATFORM_UNSUPPORTED = 1633
ERROR_INSTALL_NOTUSED = 1634
ERROR_PATCH_PACKAGE_OPEN_FAILED = 1635
ERROR_PATCH_PACKAGE_INVALID = 1636
ERROR_PATCH_PACKAGE_UNSUPPORTED = 1637
ERROR_PRODUCT_VERSION = 1638
ERROR_INVALID_COMMAND_LINE = 1639
ERROR_INSTALL_REMOTE_DISALLOWED = 1640
ERROR_SUCCESS_REBOOT_INITIATED = 1641
ERROR_PATCH_TARGET_NOT_FOUND = 1642
ERROR_PATCH_PACKAGE_REJECTED = 1643
ERROR_INSTALL_TRANSFORM_REJECTED = 1644
ERROR_INSTALL_REMOTE_PROHIBITED = 1645
ERROR_PATCH_REMOVAL_UNSUPPORTED = 1646
ERROR_UNKNOWN_PATCH = 1647
ERROR_PATCH_NO_SEQUENCE = 1648
ERROR_PATCH_REMOVAL_DISALLOWED = 1649
ERROR_INVALID_PATCH_XML = 1650
ERROR_PATCH_MANAGED_ADVERTISED_PRODUCT = 1651
ERROR_INSTALL_SERVICE_SAFEBOOT = 1652
ERROR_FAIL_FAST_EXCEPTION = 1653
ERROR_INSTALL_REJECTED = 1654
ERROR_DYNAMIC_CODE_BLOCKED = 1655
ERROR_NOT_SAME_OBJECT = 1656
ERROR_STRICT_CFG_VIOLATION = 1657
ERROR_SET_CONTEXT_DENIED = 1660
ERROR_CROSS_PARTITION_VIOLATION = 1661
ERROR_RETURN_ADDRESS_HIJACK_ATTEMPT = 1662
RPC_S_INVALID_STRING_BINDING = 1700
RPC_S_WRONG_KIND_OF_BINDING = 1701
RPC_S_INVALID_BINDING = 1702
RPC_S_PROTSEQ_NOT_SUPPORTED = 1703
RPC_S_INVALID_RPC_PROTSEQ = 1704
RPC_S_INVALID_STRING_UUID = 1705
RPC_S_INVALID_ENDPOINT_FORMAT = 1706
RPC_S_INVALID_NET_ADDR = 1707
RPC_S_NO_ENDPOINT_FOUND = 1708
RPC_S_INVALID_TIMEOUT = 1709
RPC_S_OBJECT_NOT_FOUND = 1710
RPC_S_ALREADY_REGISTERED = 1711
RPC_S_TYPE_ALREADY_REGISTERED = 1712
RPC_S_ALREADY_LISTENING = 1713
RPC_S_NO_PROTSEQS_REGISTERED = 1714
RPC_S_NOT_LISTENING = 1715
RPC_S_UNKNOWN_MGR_TYPE = 1716
RPC_S_UNKNOWN_IF = 1717
RPC_S_NO_BINDINGS = 1718
RPC_S_NO_PROTSEQS = 1719
RPC_S_CANT_CREATE_ENDPOINT = 1720
RPC_S_OUT_OF_RESOURCES = 1721
RPC_S_SERVER_UNAVAILABLE = 1722
RPC_S_SERVER_TOO_BUSY = 1723
RPC_S_INVALID_NETWORK_OPTIONS = 1724
RPC_S_NO_CALL_ACTIVE = 1725
RPC_S_CALL_FAILED = 1726
RPC_S_CALL_FAILED_DNE = 1727
RPC_S_PROTOCOL_ERROR = 1728
RPC_S_PROXY_ACCESS_DENIED = 1729
RPC_S_UNSUPPORTED_TRANS_SYN = 1730
RPC_S_UNSUPPORTED_TYPE = 1732
RPC_S_INVALID_TAG = 1733
RPC_S_INVALID_BOUND = 1734
RPC_S_NO_ENTRY_NAME = 1735
RPC_S_INVALID_NAME_SYNTAX = 1736
RPC_S_UNSUPPORTED_NAME_SYNTAX = 1737
RPC_S_UUID_NO_ADDRESS = 1739
RPC_S_DUPLICATE_ENDPOINT = 1740
RPC_S_UNKNOWN_AUTHN_TYPE = 1741
RPC_S_MAX_CALLS_TOO_SMALL = 1742
RPC_S_STRING_TOO_LONG = 1743
RPC_S_PROTSEQ_NOT_FOUND = 1744
RPC_S_PROCNUM_OUT_OF_RANGE = 1745
RPC_S_BINDING_HAS_NO_AUTH = 1746
RPC_S_UNKNOWN_AUTHN_SERVICE = 1747
RPC_S_UNKNOWN_AUTHN_LEVEL = 1748
RPC_S_INVALID_AUTH_IDENTITY = 1749
RPC_S_UNKNOWN_AUTHZ_SERVICE = 1750
EPT_S_INVALID_ENTRY = 1751
EPT_S_CANT_PERFORM_OP = 1752
EPT_S_NOT_REGISTERED = 1753
RPC_S_NOTHING_TO_EXPORT = 1754
RPC_S_INCOMPLETE_NAME = 1755
RPC_S_INVALID_VERS_OPTION = 1756
RPC_S_NO_MORE_MEMBERS = 1757
RPC_S_NOT_ALL_OBJS_UNEXPORTED = 1758
RPC_S_INTERFACE_NOT_FOUND = 1759
RPC_S_ENTRY_ALREADY_EXISTS = 1760
RPC_S_ENTRY_NOT_FOUND = 1761
RPC_S_NAME_SERVICE_UNAVAILABLE = 1762
RPC_S_INVALID_NAF_ID = 1763
RPC_S_CANNOT_SUPPORT = 1764
RPC_S_NO_CONTEXT_AVAILABLE = 1765
RPC_S_INTERNAL_ERROR = 1766
RPC_S_ZERO_DIVIDE = 1767
RPC_S_ADDRESS_ERROR = 1768
RPC_S_FP_DIV_ZERO = 1769
RPC_S_FP_UNDERFLOW = 1770
RPC_S_FP_OVERFLOW = 1771
RPC_X_NO_MORE_ENTRIES = 1772
RPC_X_SS_CHAR_TRANS_OPEN_FAIL = 1773
RPC_X_SS_CHAR_TRANS_SHORT_FILE = 1774
RPC_X_SS_IN_NULL_CONTEXT = 1775
RPC_X_SS_CONTEXT_DAMAGED = 1777
RPC_X_SS_HANDLES_MISMATCH = 1778
RPC_X_SS_CANNOT_GET_CALL_HANDLE = 1779
RPC_X_NULL_REF_POINTER = 1780
RPC_X_ENUM_VALUE_OUT_OF_RANGE = 1781
RPC_X_BYTE_COUNT_TOO_SMALL = 1782
RPC_X_BAD_STUB_DATA = 1783
ERROR_INVALID_USER_BUFFER = 1784
ERROR_UNRECOGNIZED_MEDIA = 1785
ERROR_NO_TRUST_LSA_SECRET = 1786
ERROR_NO_TRUST_SAM_ACCOUNT = 1787
ERROR_TRUSTED_DOMAIN_FAILURE = 1788
ERROR_TRUSTED_RELATIONSHIP_FAILURE = 1789
ERROR_TRUST_FAILURE = 1790
RPC_S_CALL_IN_PROGRESS = 1791
ERROR_NETLOGON_NOT_STARTED = 1792
ERROR_ACCOUNT_EXPIRED = 1793
ERROR_REDIRECTOR_HAS_OPEN_HANDLES = 1794
ERROR_PRINTER_DRIVER_ALREADY_INSTALLED = 1795
ERROR_UNKNOWN_PORT = 1796
ERROR_UNKNOWN_PRINTER_DRIVER = 1797
ERROR_UNKNOWN_PRINTPROCESSOR = 1798
ERROR_INVALID_SEPARATOR_FILE = 1799
ERROR_INVALID_PRIORITY = 1800
ERROR_INVALID_PRINTER_NAME = 1801
ERROR_PRINTER_ALREADY_EXISTS = 1802
ERROR_INVALID_PRINTER_COMMAND = 1803
ERROR_INVALID_DATATYPE = 1804
ERROR_INVALID_ENVIRONMENT = 1805
RPC_S_NO_MORE_BINDINGS = 1806
ERROR_NOLOGON_INTERDOMAIN_TRUST_ACCOUNT = 1807
ERROR_NOLOGON_WORKSTATION_TRUST_ACCOUNT = 1808
ERROR_NOLOGON_SERVER_TRUST_ACCOUNT = 1809
ERROR_DOMAIN_TRUST_INCONSISTENT = 1810
ERROR_SERVER_HAS_OPEN_HANDLES = 1811
ERROR_RESOURCE_DATA_NOT_FOUND = 1812
ERROR_RESOURCE_TYPE_NOT_FOUND = 1813
ERROR_RESOURCE_NAME_NOT_FOUND = 1814
ERROR_RESOURCE_LANG_NOT_FOUND = 1815
ERROR_NOT_ENOUGH_QUOTA = 1816
RPC_S_NO_INTERFACES = 1817
RPC_S_CALL_CANCELLED = 1818
RPC_S_BINDING_INCOMPLETE = 1819
RPC_S_COMM_FAILURE = 1820
RPC_S_UNSUPPORTED_AUTHN_LEVEL = 1821
RPC_S_NO_PRINC_NAME = 1822
RPC_S_NOT_RPC_ERROR = 1823
RPC_S_UUID_LOCAL_ONLY = 1824
RPC_S_SEC_PKG_ERROR = 1825
RPC_S_NOT_CANCELLED = 1826
RPC_X_INVALID_ES_ACTION = 1827
RPC_X_WRONG_ES_VERSION = 1828
RPC_X_WRONG_STUB_VERSION = 1829
RPC_X_INVALID_PIPE_OBJECT = 1830
RPC_X_WRONG_PIPE_ORDER = 1831
RPC_X_WRONG_PIPE_VERSION = 1832
RPC_S_COOKIE_AUTH_FAILED = 1833
RPC_S_DO_NOT_DISTURB = 1834
RPC_S_SYSTEM_HANDLE_COUNT_EXCEEDED = 1835
RPC_S_SYSTEM_HANDLE_TYPE_MISMATCH = 1836
RPC_S_GROUP_MEMBER_NOT_FOUND = 1898
EPT_S_CANT_CREATE = 1899
RPC_S_INVALID_OBJECT = 1900
ERROR_INVALID_TIME = 1901
ERROR_INVALID_FORM_NAME = 1902
ERROR_INVALID_FORM_SIZE = 1903
ERROR_ALREADY_WAITING = 1904
ERROR_PRINTER_DELETED = 1905
ERROR_INVALID_PRINTER_STATE = 1906
ERROR_PASSWORD_MUST_CHANGE = 1907
ERROR_DOMAIN_CONTROLLER_NOT_FOUND = 1908
ERROR_ACCOUNT_LOCKED_OUT = 1909
OR_INVALID_OXID = 1910
OR_INVALID_OID = 1911
OR_INVALID_SET = 1912
RPC_S_SEND_INCOMPLETE = 1913
RPC_S_INVALID_ASYNC_HANDLE = 1914
RPC_S_INVALID_ASYNC_CALL = 1915
RPC_X_PIPE_CLOSED = 1916
RPC_X_PIPE_DISCIPLINE_ERROR = 1917
RPC_X_PIPE_EMPTY = 1918
ERROR_NO_SITENAME = 1919
ERROR_CANT_ACCESS_FILE = 1920
ERROR_CANT_RESOLVE_FILENAME = 1921
RPC_S_ENTRY_TYPE_MISMATCH = 1922
RPC_S_NOT_ALL_OBJS_EXPORTED = 1923
RPC_S_INTERFACE_NOT_EXPORTED = 1924
RPC_S_PROFILE_NOT_ADDED = 1925
RPC_S_PRF_ELT_NOT_ADDED = 1926
RPC_S_PRF_ELT_NOT_REMOVED = 1927
RPC_S_GRP_ELT_NOT_ADDED = 1928
RPC_S_GRP_ELT_NOT_REMOVED = 1929
ERROR_KM_DRIVER_BLOCKED = 1930
ERROR_CONTEXT_EXPIRED = 1931
ERROR_PER_USER_TRUST_QUOTA_EXCEEDED = 1932
ERROR_ALL_USER_TRUST_QUOTA_EXCEEDED = 1933
ERROR_USER_DELETE_TRUST_QUOTA_EXCEEDED = 1934
ERROR_AUTHENTICATION_FIREWALL_FAILED = 1935
ERROR_REMOTE_PRINT_CONNECTIONS_BLOCKED = 1936
ERROR_NTLM_BLOCKED = 1937
ERROR_PASSWORD_CHANGE_REQUIRED = 1938
ERROR_LOST_MODE_LOGON_RESTRICTION = 1939
ERROR_INVALID_PIXEL_FORMAT = 2000
ERROR_BAD_DRIVER = 2001
ERROR_INVALID_WINDOW_STYLE = 2002
ERROR_METAFILE_NOT_SUPPORTED = 2003
ERROR_TRANSFORM_NOT_SUPPORTED = 2004
ERROR_CLIPPING_NOT_SUPPORTED = 2005
ERROR_INVALID_CMM = 2010
ERROR_INVALID_PROFILE = 2011
ERROR_TAG_NOT_FOUND = 2012
ERROR_TAG_NOT_PRESENT = 2013
ERROR_DUPLICATE_TAG = 2014
ERROR_PROFILE_NOT_ASSOCIATED_WITH_DEVICE = 2015
ERROR_PROFILE_NOT_FOUND = 2016
ERROR_INVALID_COLORSPACE = 2017
ERROR_ICM_NOT_ENABLED = 2018
ERROR_DELETING_ICM_XFORM = 2019
ERROR_INVALID_TRANSFORM = 2020
ERROR_COLORSPACE_MISMATCH = 2021
ERROR_INVALID_COLORINDEX = 2022
ERROR_PROFILE_DOES_NOT_MATCH_DEVICE = 2023
ERROR_CONNECTED_OTHER_PASSWORD = 2108
ERROR_CONNECTED_OTHER_PASSWORD_DEFAULT = 2109
ERROR_BAD_USERNAME = 2202
ERROR_NOT_CONNECTED = 2250
ERROR_OPEN_FILES = 2401
ERROR_ACTIVE_CONNECTIONS = 2402
ERROR_DEVICE_IN_USE = 2404
ERROR_UNKNOWN_PRINT_MONITOR = 3000
ERROR_PRINTER_DRIVER_IN_USE = 3001
ERROR_SPOOL_FILE_NOT_FOUND = 3002
ERROR_SPL_NO_STARTDOC = 3003
ERROR_SPL_NO_ADDJOB = 3004
ERROR_PRINT_PROCESSOR_ALREADY_INSTALLED = 3005
ERROR_PRINT_MONITOR_ALREADY_INSTALLED = 3006
ERROR_INVALID_PRINT_MONITOR = 3007
ERROR_PRINT_MONITOR_IN_USE = 3008
ERROR_PRINTER_HAS_JOBS_QUEUED = 3009
ERROR_SUCCESS_REBOOT_REQUIRED = 3010
ERROR_SUCCESS_RESTART_REQUIRED = 3011
ERROR_PRINTER_NOT_FOUND = 3012
ERROR_PRINTER_DRIVER_WARNED = 3013
ERROR_PRINTER_DRIVER_BLOCKED = 3014
ERROR_PRINTER_DRIVER_PACKAGE_IN_USE = 3015
ERROR_CORE_DRIVER_PACKAGE_NOT_FOUND = 3016
ERROR_FAIL_REBOOT_REQUIRED = 3017
ERROR_FAIL_REBOOT_INITIATED = 3018
ERROR_PRINTER_DRIVER_DOWNLOAD_NEEDED = 3019
ERROR_PRINT_JOB_RESTART_REQUIRED = 3020
ERROR_INVALID_PRINTER_DRIVER_MANIFEST = 3021
ERROR_PRINTER_NOT_SHAREABLE = 3022
ERROR_SERVER_SERVICE_CALL_REQUIRES_SMB1 = 3023
ERROR_NETWORK_AUTHENTICATION_PROMPT_CANCELED = 3024
ERROR_REQUEST_PAUSED = 3050
ERROR_APPEXEC_CONDITION_NOT_SATISFIED = 3060
ERROR_APPEXEC_HANDLE_INVALIDATED = 3061
ERROR_APPEXEC_INVALID_HOST_GENERATION = 3062
ERROR_APPEXEC_UNEXPECTED_PROCESS_REGISTRATION = 3063
ERROR_APPEXEC_INVALID_HOST_STATE = 3064
ERROR_APPEXEC_NO_DONOR = 3065
ERROR_APPEXEC_HOST_ID_MISMATCH = 3066
ERROR_APPEXEC_UNKNOWN_USER = 3067
ERROR_APPEXEC_APP_COMPAT_BLOCK = 3068
ERROR_APPEXEC_CALLER_WAIT_TIMEOUT = 3069
ERROR_APPEXEC_CALLER_WAIT_TIMEOUT_TERMINATION = 3070
ERROR_APPEXEC_CALLER_WAIT_TIMEOUT_LICENSING = 3071
ERROR_APPEXEC_CALLER_WAIT_TIMEOUT_RESOURCES = 3072
ERROR_VRF_VOLATILE_CFG_AND_IO_ENABLED = 3080
ERROR_VRF_VOLATILE_NOT_STOPPABLE = 3081
ERROR_VRF_VOLATILE_SAFE_MODE = 3082
ERROR_VRF_VOLATILE_NOT_RUNNABLE_SYSTEM = 3083
ERROR_VRF_VOLATILE_NOT_SUPPORTED_RULECLASS = 3084
ERROR_VRF_VOLATILE_PROTECTED_DRIVER = 3085
ERROR_VRF_VOLATILE_NMI_REGISTERED = 3086
ERROR_VRF_VOLATILE_SETTINGS_CONFLICT = 3087
ERROR_DIF_IOCALLBACK_NOT_REPLACED = 3190
ERROR_DIF_LIVEDUMP_LIMIT_EXCEEDED = 3191
ERROR_DIF_VOLATILE_SECTION_NOT_LOCKED = 3192
ERROR_DIF_VOLATILE_DRIVER_HOTPATCHED = 3193
ERROR_DIF_VOLATILE_INVALID_INFO = 3194
ERROR_DIF_VOLATILE_DRIVER_IS_NOT_RUNNING = 3195
ERROR_DIF_VOLATILE_PLUGIN_IS_NOT_RUNNING = 3196
ERROR_DIF_VOLATILE_PLUGIN_CHANGE_NOT_ALLOWED = 3197
ERROR_DIF_VOLATILE_NOT_ALLOWED = 3198
ERROR_DIF_BINDING_API_NOT_FOUND = 3199
ERROR_IO_REISSUE_AS_CACHED = 3950
ERROR_WINS_INTERNAL = 4000
ERROR_CAN_NOT_DEL_LOCAL_WINS = 4001
ERROR_STATIC_INIT = 4002
ERROR_INC_BACKUP = 4003
ERROR_FULL_BACKUP = 4004
ERROR_REC_NON_EXISTENT = 4005
ERROR_RPL_NOT_ALLOWED = 4006
PEERDIST_ERROR_CONTENTINFO_VERSION_UNSUPPORTED = 4050
PEERDIST_ERROR_CANNOT_PARSE_CONTENTINFO = 4051
PEERDIST_ERROR_MISSING_DATA = 4052
PEERDIST_ERROR_NO_MORE = 4053
PEERDIST_ERROR_NOT_INITIALIZED = 4054
PEERDIST_ERROR_ALREADY_INITIALIZED = 4055
PEERDIST_ERROR_SHUTDOWN_IN_PROGRESS = 4056
PEERDIST_ERROR_INVALIDATED = 4057
PEERDIST_ERROR_ALREADY_EXISTS = 4058
PEERDIST_ERROR_OPERATION_NOTFOUND = 4059
PEERDIST_ERROR_ALREADY_COMPLETED = 4060
PEERDIST_ERROR_OUT_OF_BOUNDS = 4061
PEERDIST_ERROR_VERSION_UNSUPPORTED = 4062
PEERDIST_ERROR_INVALID_CONFIGURATION = 4063
PEERDIST_ERROR_NOT_LICENSED = 4064
PEERDIST_ERROR_SERVICE_UNAVAILABLE = 4065
PEERDIST_ERROR_TRUST_FAILURE = 4066
ERROR_DHCP_ADDRESS_CONFLICT = 4100
ERROR_WMI_GUID_NOT_FOUND = 4200
ERROR_WMI_INSTANCE_NOT_FOUND = 4201
ERROR_WMI_ITEMID_NOT_FOUND = 4202
ERROR_WMI_TRY_AGAIN = 4203
ERROR_WMI_DP_NOT_FOUND = 4204
ERROR_WMI_UNRESOLVED_INSTANCE_REF = 4205
ERROR_WMI_ALREADY_ENABLED = 4206
ERROR_WMI_GUID_DISCONNECTED = 4207
ERROR_WMI_SERVER_UNAVAILABLE = 4208
ERROR_WMI_DP_FAILED = 4209
ERROR_WMI_INVALID_MOF = 4210
ERROR_WMI_INVALID_REGINFO = 4211
ERROR_WMI_ALREADY_DISABLED = 4212
ERROR_WMI_READ_ONLY = 4213
ERROR_WMI_SET_FAILURE = 4214
ERROR_NOT_APPCONTAINER = 4250
ERROR_APPCONTAINER_REQUIRED = 4251
ERROR_NOT_SUPPORTED_IN_APPCONTAINER = 4252
ERROR_INVALID_PACKAGE_SID_LENGTH = 4253
ERROR_INVALID_MEDIA = 4300
ERROR_INVALID_LIBRARY = 4301
ERROR_INVALID_MEDIA_POOL = 4302
ERROR_DRIVE_MEDIA_MISMATCH = 4303
ERROR_MEDIA_OFFLINE = 4304
ERROR_LIBRARY_OFFLINE = 4305
ERROR_EMPTY = 4306
ERROR_NOT_EMPTY = 4307
ERROR_MEDIA_UNAVAILABLE = 4308
ERROR_RESOURCE_DISABLED = 4309
ERROR_INVALID_CLEANER = 4310
ERROR_UNABLE_TO_CLEAN = 4311
ERROR_OBJECT_NOT_FOUND = 4312
ERROR_DATABASE_FAILURE = 4313
ERROR_DATABASE_FULL = 4314
ERROR_MEDIA_INCOMPATIBLE = 4315
ERROR_RESOURCE_NOT_PRESENT = 4316
ERROR_INVALID_OPERATION = 4317
ERROR_MEDIA_NOT_AVAILABLE = 4318
ERROR_DEVICE_NOT_AVAILABLE = 4319
ERROR_REQUEST_REFUSED = 4320
ERROR_INVALID_DRIVE_OBJECT = 4321
ERROR_LIBRARY_FULL = 4322
ERROR_MEDIUM_NOT_ACCESSIBLE = 4323
ERROR_UNABLE_TO_LOAD_MEDIUM = 4324
ERROR_UNABLE_TO_INVENTORY_DRIVE = 4325
ERROR_UNABLE_TO_INVENTORY_SLOT = 4326
ERROR_UNABLE_TO_INVENTORY_TRANSPORT = 4327
ERROR_TRANSPORT_FULL = 4328
ERROR_CONTROLLING_IEPORT = 4329
ERROR_UNABLE_TO_EJECT_MOUNTED_MEDIA = 4330
ERROR_CLEANER_SLOT_SET = 4331
ERROR_CLEANER_SLOT_NOT_SET = 4332
ERROR_CLEANER_CARTRIDGE_SPENT = 4333
ERROR_UNEXPECTED_OMID = 4334
ERROR_CANT_DELETE_LAST_ITEM = 4335
ERROR_MESSAGE_EXCEEDS_MAX_SIZE = 4336
ERROR_VOLUME_CONTAINS_SYS_FILES = 4337
ERROR_INDIGENOUS_TYPE = 4338
ERROR_NO_SUPPORTING_DRIVES = 4339
ERROR_CLEANER_CARTRIDGE_INSTALLED = 4340
ERROR_IEPORT_FULL = 4341
ERROR_FILE_OFFLINE = 4350
ERROR_REMOTE_STORAGE_NOT_ACTIVE = 4351
ERROR_REMOTE_STORAGE_MEDIA_ERROR = 4352
ERROR_NOT_A_REPARSE_POINT = 4390
ERROR_REPARSE_ATTRIBUTE_CONFLICT = 4391
ERROR_INVALID_REPARSE_DATA = 4392
ERROR_REPARSE_TAG_INVALID = 4393
ERROR_REPARSE_TAG_MISMATCH = 4394
ERROR_REPARSE_POINT_ENCOUNTERED = 4395
ERROR_APP_DATA_NOT_FOUND = 4400
ERROR_APP_DATA_EXPIRED = 4401
ERROR_APP_DATA_CORRUPT = 4402
ERROR_APP_DATA_LIMIT_EXCEEDED = 4403
ERROR_APP_DATA_REBOOT_REQUIRED = 4404
ERROR_SECUREBOOT_ROLLBACK_DETECTED = 4420
ERROR_SECUREBOOT_POLICY_VIOLATION = 4421
ERROR_SECUREBOOT_INVALID_POLICY = 4422
ERROR_SECUREBOOT_POLICY_PUBLISHER_NOT_FOUND = 4423
ERROR_SECUREBOOT_POLICY_NOT_SIGNED = 4424
ERROR_SECUREBOOT_NOT_ENABLED = 4425
ERROR_SECUREBOOT_FILE_REPLACED = 4426
ERROR_SECUREBOOT_POLICY_NOT_AUTHORIZED = 4427
ERROR_SECUREBOOT_POLICY_UNKNOWN = 4428
ERROR_SECUREBOOT_POLICY_MISSING_ANTIROLLBACKVERSION = 4429
ERROR_SECUREBOOT_PLATFORM_ID_MISMATCH = 4430
ERROR_SECUREBOOT_POLICY_ROLLBACK_DETECTED = 4431
ERROR_SECUREBOOT_POLICY_UPGRADE_MISMATCH = 4432
ERROR_SECUREBOOT_REQUIRED_POLICY_FILE_MISSING = 4433
ERROR_SECUREBOOT_NOT_BASE_POLICY = 4434
ERROR_SECUREBOOT_NOT_SUPPLEMENTAL_POLICY = 4435
ERROR_OFFLOAD_READ_FLT_NOT_SUPPORTED = 4440
ERROR_OFFLOAD_WRITE_FLT_NOT_SUPPORTED = 4441
ERROR_OFFLOAD_READ_FILE_NOT_SUPPORTED = 4442
ERROR_OFFLOAD_WRITE_FILE_NOT_SUPPORTED = 4443
ERROR_ALREADY_HAS_STREAM_ID = 4444
ERROR_SMR_GARBAGE_COLLECTION_REQUIRED = 4445
ERROR_WOF_WIM_HEADER_CORRUPT = 4446
ERROR_WOF_WIM_RESOURCE_TABLE_CORRUPT = 4447
ERROR_WOF_FILE_RESOURCE_TABLE_CORRUPT = 4448
ERROR_OBJECT_IS_IMMUTABLE = 4449
ERROR_VOLUME_NOT_SIS_ENABLED = 4500
ERROR_SYSTEM_INTEGRITY_ROLLBACK_DETECTED = 4550
ERROR_SYSTEM_INTEGRITY_POLICY_VIOLATION = 4551
ERROR_SYSTEM_INTEGRITY_INVALID_POLICY = 4552
ERROR_SYSTEM_INTEGRITY_POLICY_NOT_SIGNED = 4553
ERROR_SYSTEM_INTEGRITY_TOO_MANY_POLICIES = 4554
ERROR_SYSTEM_INTEGRITY_SUPPLEMENTAL_POLICY_NOT_AUTHORIZED = 4555
ERROR_SYSTEM_INTEGRITY_REPUTATION_MALICIOUS = 4556
ERROR_SYSTEM_INTEGRITY_REPUTATION_PUA = 4557
ERROR_SYSTEM_INTEGRITY_REPUTATION_DANGEROUS_EXT = 4558
ERROR_SYSTEM_INTEGRITY_REPUTATION_OFFLINE = 4559
ERROR_VSM_NOT_INITIALIZED = 4560
ERROR_VSM_DMA_PROTECTION_NOT_IN_USE = 4561
ERROR_PLATFORM_MANIFEST_NOT_AUTHORIZED = 4570
ERROR_PLATFORM_MANIFEST_INVALID = 4571
ERROR_PLATFORM_MANIFEST_FILE_NOT_AUTHORIZED = 4572
ERROR_PLATFORM_MANIFEST_CATALOG_NOT_AUTHORIZED = 4573
ERROR_PLATFORM_MANIFEST_BINARY_ID_NOT_FOUND = 4574
ERROR_PLATFORM_MANIFEST_NOT_ACTIVE = 4575
ERROR_PLATFORM_MANIFEST_NOT_SIGNED = 4576
ERROR_SYSTEM_INTEGRITY_REPUTATION_UNFRIENDLY_FILE = 4580
ERROR_SYSTEM_INTEGRITY_REPUTATION_UNATTAINABLE = 4581
ERROR_SYSTEM_INTEGRITY_REPUTATION_EXPLICIT_DENY_FILE = 4582
ERROR_DEPENDENT_RESOURCE_EXISTS = 5001
ERROR_DEPENDENCY_NOT_FOUND = 5002
ERROR_DEPENDENCY_ALREADY_EXISTS = 5003
ERROR_RESOURCE_NOT_ONLINE = 5004
ERROR_HOST_NODE_NOT_AVAILABLE = 5005
ERROR_RESOURCE_NOT_AVAILABLE = 5006
ERROR_RESOURCE_NOT_FOUND = 5007
ERROR_SHUTDOWN_CLUSTER = 5008
ERROR_CANT_EVICT_ACTIVE_NODE = 5009
ERROR_OBJECT_ALREADY_EXISTS = 5010
ERROR_OBJECT_IN_LIST = 5011
ERROR_GROUP_NOT_AVAILABLE = 5012
ERROR_GROUP_NOT_FOUND = 5013
ERROR_GROUP_NOT_ONLINE = 5014
ERROR_HOST_NODE_NOT_RESOURCE_OWNER = 5015
ERROR_HOST_NODE_NOT_GROUP_OWNER = 5016
ERROR_RESMON_CREATE_FAILED = 5017
ERROR_RESMON_ONLINE_FAILED = 5018
ERROR_RESOURCE_ONLINE = 5019
ERROR_QUORUM_RESOURCE = 5020
ERROR_NOT_QUORUM_CAPABLE = 5021
ERROR_CLUSTER_SHUTTING_DOWN = 5022
ERROR_INVALID_STATE = 5023
ERROR_RESOURCE_PROPERTIES_STORED = 5024
ERROR_NOT_QUORUM_CLASS = 5025
ERROR_CORE_RESOURCE = 5026
ERROR_QUORUM_RESOURCE_ONLINE_FAILED = 5027
ERROR_QUORUMLOG_OPEN_FAILED = 5028
ERROR_CLUSTERLOG_CORRUPT = 5029
ERROR_CLUSTERLOG_RECORD_EXCEEDS_MAXSIZE = 5030
ERROR_CLUSTERLOG_EXCEEDS_MAXSIZE = 5031
ERROR_CLUSTERLOG_CHKPOINT_NOT_FOUND = 5032
ERROR_CLUSTERLOG_NOT_ENOUGH_SPACE = 5033
ERROR_QUORUM_OWNER_ALIVE = 5034
ERROR_NETWORK_NOT_AVAILABLE = 5035
ERROR_NODE_NOT_AVAILABLE = 5036
ERROR_ALL_NODES_NOT_AVAILABLE = 5037
ERROR_RESOURCE_FAILED = 5038
ERROR_CLUSTER_INVALID_NODE = 5039
ERROR_CLUSTER_NODE_EXISTS = 5040
ERROR_CLUSTER_JOIN_IN_PROGRESS = 5041
ERROR_CLUSTER_NODE_NOT_FOUND = 5042
ERROR_CLUSTER_LOCAL_NODE_NOT_FOUND = 5043
ERROR_CLUSTER_NETWORK_EXISTS = 5044
ERROR_CLUSTER_NETWORK_NOT_FOUND = 5045
ERROR_CLUSTER_NETINTERFACE_EXISTS = 5046
ERROR_CLUSTER_NETINTERFACE_NOT_FOUND = 5047
ERROR_CLUSTER_INVALID_REQUEST = 5048
ERROR_CLUSTER_INVALID_NETWORK_PROVIDER = 5049
ERROR_CLUSTER_NODE_DOWN = 5050
ERROR_CLUSTER_NODE_UNREACHABLE = 5051
ERROR_CLUSTER_NODE_NOT_MEMBER = 5052
ERROR_CLUSTER_JOIN_NOT_IN_PROGRESS = 5053
ERROR_CLUSTER_INVALID_NETWORK = 5054
ERROR_CLUSTER_NODE_UP = 5056
ERROR_CLUSTER_IPADDR_IN_USE = 5057
ERROR_CLUSTER_NODE_NOT_PAUSED = 5058
ERROR_CLUSTER_NO_SECURITY_CONTEXT = 5059
ERROR_CLUSTER_NETWORK_NOT_INTERNAL = 5060
ERROR_CLUSTER_NODE_ALREADY_UP = 5061
ERROR_CLUSTER_NODE_ALREADY_DOWN = 5062
ERROR_CLUSTER_NETWORK_ALREADY_ONLINE = 5063
ERROR_CLUSTER_NETWORK_ALREADY_OFFLINE = 5064
ERROR_CLUSTER_NODE_ALREADY_MEMBER = 5065
ERROR_CLUSTER_LAST_INTERNAL_NETWORK = 5066
ERROR_CLUSTER_NETWORK_HAS_DEPENDENTS = 5067
ERROR_INVALID_OPERATION_ON_QUORUM = 5068
ERROR_DEPENDENCY_NOT_ALLOWED = 5069
ERROR_CLUSTER_NODE_PAUSED = 5070
ERROR_NODE_CANT_HOST_RESOURCE = 5071
ERROR_CLUSTER_NODE_NOT_READY = 5072
ERROR_CLUSTER_NODE_SHUTTING_DOWN = 5073
ERROR_CLUSTER_JOIN_ABORTED = 5074
ERROR_CLUSTER_INCOMPATIBLE_VERSIONS = 5075
ERROR_CLUSTER_MAXNUM_OF_RESOURCES_EXCEEDED = 5076
ERROR_CLUSTER_SYSTEM_CONFIG_CHANGED = 5077
ERROR_CLUSTER_RESOURCE_TYPE_NOT_FOUND = 5078
ERROR_CLUSTER_RESTYPE_NOT_SUPPORTED = 5079
ERROR_CLUSTER_RESNAME_NOT_FOUND = 5080
ERROR_CLUSTER_NO_RPC_PACKAGES_REGISTERED = 5081
ERROR_CLUSTER_OWNER_NOT_IN_PREFLIST = 5082
ERROR_CLUSTER_DATABASE_SEQMISMATCH = 5083
ERROR_RESMON_INVALID_STATE = 5084
ERROR_CLUSTER_GUM_NOT_LOCKER = 5085
ERROR_QUORUM_DISK_NOT_FOUND = 5086
ERROR_DATABASE_BACKUP_CORRUPT = 5087
ERROR_CLUSTER_NODE_ALREADY_HAS_DFS_ROOT = 5088
ERROR_RESOURCE_PROPERTY_UNCHANGEABLE = 5089
ERROR_NO_ADMIN_ACCESS_POINT = 5090
ERROR_CLUSTER_MEMBERSHIP_INVALID_STATE = 5890
ERROR_CLUSTER_QUORUMLOG_NOT_FOUND = 5891
ERROR_CLUSTER_MEMBERSHIP_HALT = 5892
ERROR_CLUSTER_INSTANCE_ID_MISMATCH = 5893
ERROR_CLUSTER_NETWORK_NOT_FOUND_FOR_IP = 5894
ERROR_CLUSTER_PROPERTY_DATA_TYPE_MISMATCH = 5895
ERROR_CLUSTER_EVICT_WITHOUT_CLEANUP = 5896
ERROR_CLUSTER_PARAMETER_MISMATCH = 5897
ERROR_NODE_CANNOT_BE_CLUSTERED = 5898
ERROR_CLUSTER_WRONG_OS_VERSION = 5899
ERROR_CLUSTER_CANT_CREATE_DUP_CLUSTER_NAME = 5900
ERROR_CLUSCFG_ALREADY_COMMITTED = 5901
ERROR_CLUSCFG_ROLLBACK_FAILED = 5902
ERROR_CLUSCFG_SYSTEM_DISK_DRIVE_LETTER_CONFLICT = 5903
ERROR_CLUSTER_OLD_VERSION = 5904
ERROR_CLUSTER_MISMATCHED_COMPUTER_ACCT_NAME = 5905
ERROR_CLUSTER_NO_NET_ADAPTERS = 5906
ERROR_CLUSTER_POISONED = 5907
ERROR_CLUSTER_GROUP_MOVING = 5908
ERROR_CLUSTER_RESOURCE_TYPE_BUSY = 5909
ERROR_RESOURCE_CALL_TIMED_OUT = 5910
ERROR_INVALID_CLUSTER_IPV6_ADDRESS = 5911
ERROR_CLUSTER_INTERNAL_INVALID_FUNCTION = 5912
ERROR_CLUSTER_PARAMETER_OUT_OF_BOUNDS = 5913
ERROR_CLUSTER_PARTIAL_SEND = 5914
ERROR_CLUSTER_REGISTRY_INVALID_FUNCTION = 5915
ERROR_CLUSTER_INVALID_STRING_TERMINATION = 5916
ERROR_CLUSTER_INVALID_STRING_FORMAT = 5917
ERROR_CLUSTER_DATABASE_TRANSACTION_IN_PROGRESS = 5918
ERROR_CLUSTER_DATABASE_TRANSACTION_NOT_IN_PROGRESS = 5919
ERROR_CLUSTER_NULL_DATA = 5920
ERROR_CLUSTER_PARTIAL_READ = 5921
ERROR_CLUSTER_PARTIAL_WRITE = 5922
ERROR_CLUSTER_CANT_DESERIALIZE_DATA = 5923
ERROR_DEPENDENT_RESOURCE_PROPERTY_CONFLICT = 5924
ERROR_CLUSTER_NO_QUORUM = 5925
ERROR_CLUSTER_INVALID_IPV6_NETWORK = 5926
ERROR_CLUSTER_INVALID_IPV6_TUNNEL_NETWORK = 5927
ERROR_QUORUM_NOT_ALLOWED_IN_THIS_GROUP = 5928
ERROR_DEPENDENCY_TREE_TOO_COMPLEX = 5929
ERROR_EXCEPTION_IN_RESOURCE_CALL = 5930
ERROR_CLUSTER_RHS_FAILED_INITIALIZATION = 5931
ERROR_CLUSTER_NOT_INSTALLED = 5932
ERROR_CLUSTER_RESOURCES_MUST_BE_ONLINE_ON_THE_SAME_NODE = 5933
ERROR_CLUSTER_MAX_NODES_IN_CLUSTER = 5934
ERROR_CLUSTER_TOO_MANY_NODES = 5935
ERROR_CLUSTER_OBJECT_ALREADY_USED = 5936
ERROR_NONCORE_GROUPS_FOUND = 5937
ERROR_FILE_SHARE_RESOURCE_CONFLICT = 5938
ERROR_CLUSTER_EVICT_INVALID_REQUEST = 5939
ERROR_CLUSTER_SINGLETON_RESOURCE = 5940
ERROR_CLUSTER_GROUP_SINGLETON_RESOURCE = 5941
ERROR_CLUSTER_RESOURCE_PROVIDER_FAILED = 5942
ERROR_CLUSTER_RESOURCE_CONFIGURATION_ERROR = 5943
ERROR_CLUSTER_GROUP_BUSY = 5944
ERROR_CLUSTER_NOT_SHARED_VOLUME = 5945
ERROR_CLUSTER_INVALID_SECURITY_DESCRIPTOR = 5946
ERROR_CLUSTER_SHARED_VOLUMES_IN_USE = 5947
ERROR_CLUSTER_USE_SHARED_VOLUMES_API = 5948
ERROR_CLUSTER_BACKUP_IN_PROGRESS = 5949
ERROR_NON_CSV_PATH = 5950
ERROR_CSV_VOLUME_NOT_LOCAL = 5951
ERROR_CLUSTER_WATCHDOG_TERMINATING = 5952
ERROR_CLUSTER_RESOURCE_VETOED_MOVE_INCOMPATIBLE_NODES = 5953
ERROR_CLUSTER_INVALID_NODE_WEIGHT = 5954
ERROR_CLUSTER_RESOURCE_VETOED_CALL = 5955
ERROR_RESMON_SYSTEM_RESOURCES_LACKING = 5956
ERROR_CLUSTER_RESOURCE_VETOED_MOVE_NOT_ENOUGH_RESOURCES_ON_DESTINATION = 5957
ERROR_CLUSTER_RESOURCE_VETOED_MOVE_NOT_ENOUGH_RESOURCES_ON_SOURCE = 5958
ERROR_CLUSTER_GROUP_QUEUED = 5959
ERROR_CLUSTER_RESOURCE_LOCKED_STATUS = 5960
ERROR_CLUSTER_SHARED_VOLUME_FAILOVER_NOT_ALLOWED = 5961
ERROR_CLUSTER_NODE_DRAIN_IN_PROGRESS = 5962
ERROR_CLUSTER_DISK_NOT_CONNECTED = 5963
ERROR_DISK_NOT_CSV_CAPABLE = 5964
ERROR_RESOURCE_NOT_IN_AVAILABLE_STORAGE = 5965
ERROR_CLUSTER_SHARED_VOLUME_REDIRECTED = 5966
ERROR_CLUSTER_SHARED_VOLUME_NOT_REDIRECTED = 5967
ERROR_CLUSTER_CANNOT_RETURN_PROPERTIES = 5968
ERROR_CLUSTER_RESOURCE_CONTAINS_UNSUPPORTED_DIFF_AREA_FOR_SHARED_VOLUMES = 5969
ERROR_CLUSTER_RESOURCE_IS_IN_MAINTENANCE_MODE = 5970
ERROR_CLUSTER_AFFINITY_CONFLICT = 5971
ERROR_CLUSTER_RESOURCE_IS_REPLICA_VIRTUAL_MACHINE = 5972
ERROR_CLUSTER_UPGRADE_INCOMPATIBLE_VERSIONS = 5973
ERROR_CLUSTER_UPGRADE_FIX_QUORUM_NOT_SUPPORTED = 5974
ERROR_CLUSTER_UPGRADE_RESTART_REQUIRED = 5975
ERROR_CLUSTER_UPGRADE_IN_PROGRESS = 5976
ERROR_CLUSTER_UPGRADE_INCOMPLETE = 5977
ERROR_CLUSTER_NODE_IN_GRACE_PERIOD = 5978
ERROR_CLUSTER_CSV_IO_PAUSE_TIMEOUT = 5979
ERROR_NODE_NOT_ACTIVE_CLUSTER_MEMBER = 5980
ERROR_CLUSTER_RESOURCE_NOT_MONITORED = 5981
ERROR_CLUSTER_RESOURCE_DOES_NOT_SUPPORT_UNMONITORED = 5982
ERROR_CLUSTER_RESOURCE_IS_REPLICATED = 5983
ERROR_CLUSTER_NODE_ISOLATED = 5984
ERROR_CLUSTER_NODE_QUARANTINED = 5985
ERROR_CLUSTER_DATABASE_UPDATE_CONDITION_FAILED = 5986
ERROR_CLUSTER_SPACE_DEGRADED = 5987
ERROR_CLUSTER_TOKEN_DELEGATION_NOT_SUPPORTED = 5988
ERROR_CLUSTER_CSV_INVALID_HANDLE = 5989
ERROR_CLUSTER_CSV_SUPPORTED_ONLY_ON_COORDINATOR = 5990
ERROR_GROUPSET_NOT_AVAILABLE = 5991
ERROR_GROUPSET_NOT_FOUND = 5992
ERROR_GROUPSET_CANT_PROVIDE = 5993
ERROR_CLUSTER_FAULT_DOMAIN_PARENT_NOT_FOUND = 5994
ERROR_CLUSTER_FAULT_DOMAIN_INVALID_HIERARCHY = 5995
ERROR_CLUSTER_FAULT_DOMAIN_FAILED_S2D_VALIDATION = 5996
ERROR_CLUSTER_FAULT_DOMAIN_S2D_CONNECTIVITY_LOSS = 5997
ERROR_CLUSTER_INVALID_INFRASTRUCTURE_FILESERVER_NAME = 5998
ERROR_CLUSTERSET_MANAGEMENT_CLUSTER_UNREACHABLE = 5999
ERROR_ENCRYPTION_FAILED = 6000
ERROR_DECRYPTION_FAILED = 6001
ERROR_FILE_ENCRYPTED = 6002
ERROR_NO_RECOVERY_POLICY = 6003
ERROR_NO_EFS = 6004
ERROR_WRONG_EFS = 6005
ERROR_NO_USER_KEYS = 6006
ERROR_FILE_NOT_ENCRYPTED = 6007
ERROR_NOT_EXPORT_FORMAT = 6008
ERROR_FILE_READ_ONLY = 6009
ERROR_DIR_EFS_DISALLOWED = 6010
ERROR_EFS_SERVER_NOT_TRUSTED = 6011
ERROR_BAD_RECOVERY_POLICY = 6012
ERROR_EFS_ALG_BLOB_TOO_BIG = 6013
ERROR_VOLUME_NOT_SUPPORT_EFS = 6014
ERROR_EFS_DISABLED = 6015
ERROR_EFS_VERSION_NOT_SUPPORT = 6016
ERROR_CS_ENCRYPTION_INVALID_SERVER_RESPONSE = 6017
ERROR_CS_ENCRYPTION_UNSUPPORTED_SERVER = 6018
ERROR_CS_ENCRYPTION_EXISTING_ENCRYPTED_FILE = 6019
ERROR_CS_ENCRYPTION_NEW_ENCRYPTED_FILE = 6020
ERROR_CS_ENCRYPTION_FILE_NOT_CSE = 6021
ERROR_ENCRYPTION_POLICY_DENIES_OPERATION = 6022
ERROR_WIP_ENCRYPTION_FAILED = 6023
ERROR_NO_BROWSER_SERVERS_FOUND = 6118
SCHED_E_SERVICE_NOT_LOCALSYSTEM = 6200
ERROR_CLUSTER_OBJECT_IS_CLUSTER_SET_VM = 6250
ERROR_LOG_SECTOR_INVALID = 6600
ERROR_LOG_SECTOR_PARITY_INVALID = 6601
ERROR_LOG_SECTOR_REMAPPED = 6602
ERROR_LOG_BLOCK_INCOMPLETE = 6603
ERROR_LOG_INVALID_RANGE = 6604
ERROR_LOG_BLOCKS_EXHAUSTED = 6605
ERROR_LOG_READ_CONTEXT_INVALID = 6606
ERROR_LOG_RESTART_INVALID = 6607
ERROR_LOG_BLOCK_VERSION = 6608
ERROR_LOG_BLOCK_INVALID = 6609
ERROR_LOG_READ_MODE_INVALID = 6610
ERROR_LOG_NO_RESTART = 6611
ERROR_LOG_METADATA_CORRUPT = 6612
ERROR_LOG_METADATA_INVALID = 6613
ERROR_LOG_METADATA_INCONSISTENT = 6614
ERROR_LOG_RESERVATION_INVALID = 6615
ERROR_LOG_CANT_DELETE = 6616
ERROR_LOG_CONTAINER_LIMIT_EXCEEDED = 6617
ERROR_LOG_START_OF_LOG = 6618
ERROR_LOG_POLICY_ALREADY_INSTALLED = 6619
ERROR_LOG_POLICY_NOT_INSTALLED = 6620
ERROR_LOG_POLICY_INVALID = 6621
ERROR_LOG_POLICY_CONFLICT = 6622
ERROR_LOG_PINNED_ARCHIVE_TAIL = 6623
ERROR_LOG_RECORD_NONEXISTENT = 6624
ERROR_LOG_RECORDS_RESERVED_INVALID = 6625
ERROR_LOG_SPACE_RESERVED_INVALID = 6626
ERROR_LOG_TAIL_INVALID = 6627
ERROR_LOG_FULL = 6628
ERROR_COULD_NOT_RESIZE_LOG = 6629
ERROR_LOG_MULTIPLEXED = 6630
ERROR_LOG_DEDICATED = 6631
ERROR_LOG_ARCHIVE_NOT_IN_PROGRESS = 6632
ERROR_LOG_ARCHIVE_IN_PROGRESS = 6633
ERROR_LOG_EPHEMERAL = 6634
ERROR_LOG_NOT_ENOUGH_CONTAINERS = 6635
ERROR_LOG_CLIENT_ALREADY_REGISTERED = 6636
ERROR_LOG_CLIENT_NOT_REGISTERED = 6637
ERROR_LOG_FULL_HANDLER_IN_PROGRESS = 6638
ERROR_LOG_CONTAINER_READ_FAILED = 6639
ERROR_LOG_CONTAINER_WRITE_FAILED = 6640
ERROR_LOG_CONTAINER_OPEN_FAILED = 6641
ERROR_LOG_CONTAINER_STATE_INVALID = 6642
ERROR_LOG_STATE_INVALID = 6643
ERROR_LOG_PINNED = 6644
ERROR_LOG_METADATA_FLUSH_FAILED = 6645
ERROR_LOG_INCONSISTENT_SECURITY = 6646
ERROR_LOG_APPENDED_FLUSH_FAILED = 6647
ERROR_LOG_PINNED_RESERVATION = 6648
ERROR_INVALID_TRANSACTION = 6700
ERROR_TRANSACTION_NOT_ACTIVE = 6701
ERROR_TRANSACTION_REQUEST_NOT_VALID = 6702
ERROR_TRANSACTION_NOT_REQUESTED = 6703
ERROR_TRANSACTION_ALREADY_ABORTED = 6704
ERROR_TRANSACTION_ALREADY_COMMITTED = 6705
ERROR_TM_INITIALIZATION_FAILED = 6706
ERROR_RESOURCEMANAGER_READ_ONLY = 6707
ERROR_TRANSACTION_NOT_JOINED = 6708
ERROR_TRANSACTION_SUPERIOR_EXISTS = 6709
ERROR_CRM_PROTOCOL_ALREADY_EXISTS = 6710
ERROR_TRANSACTION_PROPAGATION_FAILED = 6711
ERROR_CRM_PROTOCOL_NOT_FOUND = 6712
ERROR_TRANSACTION_INVALID_MARSHALL_BUFFER = 6713
ERROR_CURRENT_TRANSACTION_NOT_VALID = 6714
ERROR_TRANSACTION_NOT_FOUND = 6715
ERROR_RESOURCEMANAGER_NOT_FOUND = 6716
ERROR_ENLISTMENT_NOT_FOUND = 6717
ERROR_TRANSACTIONMANAGER_NOT_FOUND = 6718
ERROR_TRANSACTIONMANAGER_NOT_ONLINE = 6719
ERROR_TRANSACTIONMANAGER_RECOVERY_NAME_COLLISION = 6720
ERROR_TRANSACTION_NOT_ROOT = 6721
ERROR_TRANSACTION_OBJECT_EXPIRED = 6722
ERROR_TRANSACTION_RESPONSE_NOT_ENLISTED = 6723
ERROR_TRANSACTION_RECORD_TOO_LONG = 6724
ERROR_IMPLICIT_TRANSACTION_NOT_SUPPORTED = 6725
ERROR_TRANSACTION_INTEGRITY_VIOLATED = 6726
ERROR_TRANSACTIONMANAGER_IDENTITY_MISMATCH = 6727
ERROR_RM_CANNOT_BE_FROZEN_FOR_SNAPSHOT = 6728
ERROR_TRANSACTION_MUST_WRITETHROUGH = 6729
ERROR_TRANSACTION_NO_SUPERIOR = 6730
ERROR_HEURISTIC_DAMAGE_POSSIBLE = 6731
ERROR_TRANSACTIONAL_CONFLICT = 6800
ERROR_RM_NOT_ACTIVE = 6801
ERROR_RM_METADATA_CORRUPT = 6802
ERROR_DIRECTORY_NOT_RM = 6803
ERROR_TRANSACTIONS_UNSUPPORTED_REMOTE = 6805
ERROR_LOG_RESIZE_INVALID_SIZE = 6806
ERROR_OBJECT_NO_LONGER_EXISTS = 6807
ERROR_STREAM_MINIVERSION_NOT_FOUND = 6808
ERROR_STREAM_MINIVERSION_NOT_VALID = 6809
ERROR_MINIVERSION_INACCESSIBLE_FROM_SPECIFIED_TRANSACTION = 6810
ERROR_CANT_OPEN_MINIVERSION_WITH_MODIFY_INTENT = 6811
ERROR_CANT_CREATE_MORE_STREAM_MINIVERSIONS = 6812
ERROR_REMOTE_FILE_VERSION_MISMATCH = 6814
ERROR_HANDLE_NO_LONGER_VALID = 6815
ERROR_NO_TXF_METADATA = 6816
ERROR_LOG_CORRUPTION_DETECTED = 6817
ERROR_CANT_RECOVER_WITH_HANDLE_OPEN = 6818
ERROR_RM_DISCONNECTED = 6819
ERROR_ENLISTMENT_NOT_SUPERIOR = 6820
ERROR_RECOVERY_NOT_NEEDED = 6821
ERROR_RM_ALREADY_STARTED = 6822
ERROR_FILE_IDENTITY_NOT_PERSISTENT = 6823
ERROR_CANT_BREAK_TRANSACTIONAL_DEPENDENCY = 6824
ERROR_CANT_CROSS_RM_BOUNDARY = 6825
ERROR_TXF_DIR_NOT_EMPTY = 6826
ERROR_INDOUBT_TRANSACTIONS_EXIST = 6827
ERROR_TM_VOLATILE = 6828
ERROR_ROLLBACK_TIMER_EXPIRED = 6829
ERROR_TXF_ATTRIBUTE_CORRUPT = 6830
ERROR_EFS_NOT_ALLOWED_IN_TRANSACTION = 6831
ERROR_TRANSACTIONAL_OPEN_NOT_ALLOWED = 6832
ERROR_LOG_GROWTH_FAILED = 6833
ERROR_TRANSACTED_MAPPING_UNSUPPORTED_REMOTE = 6834
ERROR_TXF_METADATA_ALREADY_PRESENT = 6835
ERROR_TRANSACTION_SCOPE_CALLBACKS_NOT_SET = 6836
ERROR_TRANSACTION_REQUIRED_PROMOTION = 6837
ERROR_CANNOT_EXECUTE_FILE_IN_TRANSACTION = 6838
ERROR_TRANSACTIONS_NOT_FROZEN = 6839
ERROR_TRANSACTION_FREEZE_IN_PROGRESS = 6840
ERROR_NOT_SNAPSHOT_VOLUME = 6841
ERROR_NO_SAVEPOINT_WITH_OPEN_FILES = 6842
ERROR_DATA_LOST_REPAIR = 6843
ERROR_SPARSE_NOT_ALLOWED_IN_TRANSACTION = 6844
ERROR_TM_IDENTITY_MISMATCH = 6845
ERROR_FLOATED_SECTION = 6846
ERROR_CANNOT_ACCEPT_TRANSACTED_WORK = 6847
ERROR_CANNOT_ABORT_TRANSACTIONS = 6848
ERROR_BAD_CLUSTERS = 6849
ERROR_COMPRESSION_NOT_ALLOWED_IN_TRANSACTION = 6850
ERROR_VOLUME_DIRTY = 6851
ERROR_NO_LINK_TRACKING_IN_TRANSACTION = 6852
ERROR_OPERATION_NOT_SUPPORTED_IN_TRANSACTION = 6853
ERROR_EXPIRED_HANDLE = 6854
ERROR_TRANSACTION_NOT_ENLISTED = 6855
ERROR_CTX_WINSTATION_NAME_INVALID = 7001
ERROR_CTX_INVALID_PD = 7002
ERROR_CTX_PD_NOT_FOUND = 7003
ERROR_CTX_WD_NOT_FOUND = 7004
ERROR_CTX_CANNOT_MAKE_EVENTLOG_ENTRY = 7005
ERROR_CTX_SERVICE_NAME_COLLISION = 7006
ERROR_CTX_CLOSE_PENDING = 7007
ERROR_CTX_NO_OUTBUF = 7008
ERROR_CTX_MODEM_INF_NOT_FOUND = 7009
ERROR_CTX_INVALID_MODEMNAME = 7010
ERROR_CTX_MODEM_RESPONSE_ERROR = 7011
ERROR_CTX_MODEM_RESPONSE_TIMEOUT = 7012
ERROR_CTX_MODEM_RESPONSE_NO_CARRIER = 7013
ERROR_CTX_MODEM_RESPONSE_NO_DIALTONE = 7014
ERROR_CTX_MODEM_RESPONSE_BUSY = 7015
ERROR_CTX_MODEM_RESPONSE_VOICE = 7016
ERROR_CTX_TD_ERROR = 7017
ERROR_CTX_WINSTATION_NOT_FOUND = 7022
ERROR_CTX_WINSTATION_ALREADY_EXISTS = 7023
ERROR_CTX_WINSTATION_BUSY = 7024
ERROR_CTX_BAD_VIDEO_MODE = 7025
ERROR_CTX_GRAPHICS_INVALID = 7035
ERROR_CTX_LOGON_DISABLED = 7037
ERROR_CTX_NOT_CONSOLE = 7038
ERROR_CTX_CLIENT_QUERY_TIMEOUT = 7040
ERROR_CTX_CONSOLE_DISCONNECT = 7041
ERROR_CTX_CONSOLE_CONNECT = 7042
ERROR_CTX_SHADOW_DENIED = 7044
ERROR_CTX_WINSTATION_ACCESS_DENIED = 7045
ERROR_CTX_INVALID_WD = 7049
ERROR_CTX_SHADOW_INVALID = 7050
ERROR_CTX_SHADOW_DISABLED = 7051
ERROR_CTX_CLIENT_LICENSE_IN_USE = 7052
ERROR_CTX_CLIENT_LICENSE_NOT_SET = 7053
ERROR_CTX_LICENSE_NOT_AVAILABLE = 7054
ERROR_CTX_LICENSE_CLIENT_INVALID = 7055
ERROR_CTX_LICENSE_EXPIRED = 7056
ERROR_CTX_SHADOW_NOT_RUNNING = 7057
ERROR_CTX_SHADOW_ENDED_BY_MODE_CHANGE = 7058
ERROR_ACTIVATION_COUNT_EXCEEDED = 7059
ERROR_CTX_WINSTATIONS_DISABLED = 7060
ERROR_CTX_ENCRYPTION_LEVEL_REQUIRED = 7061
ERROR_CTX_SESSION_IN_USE = 7062
ERROR_CTX_NO_FORCE_LOGOFF = 7063
ERROR_CTX_ACCOUNT_RESTRICTION = 7064
ERROR_RDP_PROTOCOL_ERROR = 7065
ERROR_CTX_CDM_CONNECT = 7066
ERROR_CTX_CDM_DISCONNECT = 7067
ERROR_CTX_SECURITY_LAYER_ERROR = 7068
ERROR_TS_INCOMPATIBLE_SESSIONS = 7069
ERROR_TS_VIDEO_SUBSYSTEM_ERROR = 7070
FRS_ERR_INVALID_API_SEQUENCE = 8001
FRS_ERR_STARTING_SERVICE = 8002
FRS_ERR_STOPPING_SERVICE = 8003
FRS_ERR_INTERNAL_API = 8004
FRS_ERR_INTERNAL = 8005
FRS_ERR_SERVICE_COMM = 8006
FRS_ERR_INSUFFICIENT_PRIV = 8007
FRS_ERR_AUTHENTICATION = 8008
FRS_ERR_PARENT_INSUFFICIENT_PRIV = 8009
FRS_ERR_PARENT_AUTHENTICATION = 8010
FRS_ERR_CHILD_TO_PARENT_COMM = 8011
FRS_ERR_PARENT_TO_CHILD_COMM = 8012
FRS_ERR_SYSVOL_POPULATE = 8013
FRS_ERR_SYSVOL_POPULATE_TIMEOUT = 8014
FRS_ERR_SYSVOL_IS_BUSY = 8015
FRS_ERR_SYSVOL_DEMOTE = 8016
FRS_ERR_INVALID_SERVICE_PARAMETER = 8017
DS_S_SUCCESS = NO_ERROR
ERROR_DS_NOT_INSTALLED = 8200
ERROR_DS_MEMBERSHIP_EVALUATED_LOCALLY = 8201
ERROR_DS_NO_ATTRIBUTE_OR_VALUE = 8202
ERROR_DS_INVALID_ATTRIBUTE_SYNTAX = 8203
ERROR_DS_ATTRIBUTE_TYPE_UNDEFINED = 8204
ERROR_DS_ATTRIBUTE_OR_VALUE_EXISTS = 8205
ERROR_DS_BUSY = 8206
ERROR_DS_UNAVAILABLE = 8207
ERROR_DS_NO_RIDS_ALLOCATED = 8208
ERROR_DS_NO_MORE_RIDS = 8209
ERROR_DS_INCORRECT_ROLE_OWNER = 8210
ERROR_DS_RIDMGR_INIT_ERROR = 8211
ERROR_DS_OBJ_CLASS_VIOLATION = 8212
ERROR_DS_CANT_ON_NON_LEAF = 8213
ERROR_DS_CANT_ON_RDN = 8214
ERROR_DS_CANT_MOD_OBJ_CLASS = 8215
ERROR_DS_CROSS_DOM_MOVE_ERROR = 8216
ERROR_DS_GC_NOT_AVAILABLE = 8217
ERROR_SHARED_POLICY = 8218
ERROR_POLICY_OBJECT_NOT_FOUND = 8219
ERROR_POLICY_ONLY_IN_DS = 8220
ERROR_PROMOTION_ACTIVE = 8221
ERROR_NO_PROMOTION_ACTIVE = 8222
ERROR_DS_OPERATIONS_ERROR = 8224
ERROR_DS_PROTOCOL_ERROR = 8225
ERROR_DS_TIMELIMIT_EXCEEDED = 8226
ERROR_DS_SIZELIMIT_EXCEEDED = 8227
ERROR_DS_ADMIN_LIMIT_EXCEEDED = 8228
ERROR_DS_COMPARE_FALSE = 8229
ERROR_DS_COMPARE_TRUE = 8230
ERROR_DS_AUTH_METHOD_NOT_SUPPORTED = 8231
ERROR_DS_STRONG_AUTH_REQUIRED = 8232
ERROR_DS_INAPPROPRIATE_AUTH = 8233
ERROR_DS_AUTH_UNKNOWN = 8234
ERROR_DS_REFERRAL = 8235
ERROR_DS_UNAVAILABLE_CRIT_EXTENSION = 8236
ERROR_DS_CONFIDENTIALITY_REQUIRED = 8237
ERROR_DS_INAPPROPRIATE_MATCHING = 8238
ERROR_DS_CONSTRAINT_VIOLATION = 8239
ERROR_DS_NO_SUCH_OBJECT = 8240
ERROR_DS_ALIAS_PROBLEM = 8241
ERROR_DS_INVALID_DN_SYNTAX = 8242
ERROR_DS_IS_LEAF = 8243
ERROR_DS_ALIAS_DEREF_PROBLEM = 8244
ERROR_DS_UNWILLING_TO_PERFORM = 8245
ERROR_DS_LOOP_DETECT = 8246
ERROR_DS_NAMING_VIOLATION = 8247
ERROR_DS_OBJECT_RESULTS_TOO_LARGE = 8248
ERROR_DS_AFFECTS_MULTIPLE_DSAS = 8249
ERROR_DS_SERVER_DOWN = 8250
ERROR_DS_LOCAL_ERROR = 8251
ERROR_DS_ENCODING_ERROR = 8252
ERROR_DS_DECODING_ERROR = 8253
ERROR_DS_FILTER_UNKNOWN = 8254
ERROR_DS_PARAM_ERROR = 8255
ERROR_DS_NOT_SUPPORTED = 8256
ERROR_DS_NO_RESULTS_RETURNED = 8257
ERROR_DS_CONTROL_NOT_FOUND = 8258
ERROR_DS_CLIENT_LOOP = 8259
ERROR_DS_REFERRAL_LIMIT_EXCEEDED = 8260
ERROR_DS_SORT_CONTROL_MISSING = 8261
ERROR_DS_OFFSET_RANGE_ERROR = 8262
ERROR_DS_RIDMGR_DISABLED = 8263
ERROR_DS_ROOT_MUST_BE_NC = 8301
ERROR_DS_ADD_REPLICA_INHIBITED = 8302
ERROR_DS_ATT_NOT_DEF_IN_SCHEMA = 8303
ERROR_DS_MAX_OBJ_SIZE_EXCEEDED = 8304
ERROR_DS_OBJ_STRING_NAME_EXISTS = 8305
ERROR_DS_NO_RDN_DEFINED_IN_SCHEMA = 8306
ERROR_DS_RDN_DOESNT_MATCH_SCHEMA = 8307
ERROR_DS_NO_REQUESTED_ATTS_FOUND = 8308
ERROR_DS_USER_BUFFER_TO_SMALL = 8309
ERROR_DS_ATT_IS_NOT_ON_OBJ = 8310
ERROR_DS_ILLEGAL_MOD_OPERATION = 8311
ERROR_DS_OBJ_TOO_LARGE = 8312
ERROR_DS_BAD_INSTANCE_TYPE = 8313
ERROR_DS_MASTERDSA_REQUIRED = 8314
ERROR_DS_OBJECT_CLASS_REQUIRED = 8315
ERROR_DS_MISSING_REQUIRED_ATT = 8316
ERROR_DS_ATT_NOT_DEF_FOR_CLASS = 8317
ERROR_DS_ATT_ALREADY_EXISTS = 8318
ERROR_DS_CANT_ADD_ATT_VALUES = 8320
ERROR_DS_SINGLE_VALUE_CONSTRAINT = 8321
ERROR_DS_RANGE_CONSTRAINT = 8322
ERROR_DS_ATT_VAL_ALREADY_EXISTS = 8323
ERROR_DS_CANT_REM_MISSING_ATT = 8324
ERROR_DS_CANT_REM_MISSING_ATT_VAL = 8325
ERROR_DS_ROOT_CANT_BE_SUBREF = 8326
ERROR_DS_NO_CHAINING = 8327
ERROR_DS_NO_CHAINED_EVAL = 8328
ERROR_DS_NO_PARENT_OBJECT = 8329
ERROR_DS_PARENT_IS_AN_ALIAS = 8330
ERROR_DS_CANT_MIX_MASTER_AND_REPS = 8331
ERROR_DS_CHILDREN_EXIST = 8332
ERROR_DS_OBJ_NOT_FOUND = 8333
ERROR_DS_ALIASED_OBJ_MISSING = 8334
ERROR_DS_BAD_NAME_SYNTAX = 8335
ERROR_DS_ALIAS_POINTS_TO_ALIAS = 8336
ERROR_DS_CANT_DEREF_ALIAS = 8337
ERROR_DS_OUT_OF_SCOPE = 8338
ERROR_DS_OBJECT_BEING_REMOVED = 8339
ERROR_DS_CANT_DELETE_DSA_OBJ = 8340
ERROR_DS_GENERIC_ERROR = 8341
ERROR_DS_DSA_MUST_BE_INT_MASTER = 8342
ERROR_DS_CLASS_NOT_DSA = 8343
ERROR_DS_INSUFF_ACCESS_RIGHTS = 8344
ERROR_DS_ILLEGAL_SUPERIOR = 8345
ERROR_DS_ATTRIBUTE_OWNED_BY_SAM = 8346
ERROR_DS_NAME_TOO_MANY_PARTS = 8347
ERROR_DS_NAME_TOO_LONG = 8348
ERROR_DS_NAME_VALUE_TOO_LONG = 8349
ERROR_DS_NAME_UNPARSEABLE = 8350
ERROR_DS_NAME_TYPE_UNKNOWN = 8351
ERROR_DS_NOT_AN_OBJECT = 8352
ERROR_DS_SEC_DESC_TOO_SHORT = 8353
ERROR_DS_SEC_DESC_INVALID = 8354
ERROR_DS_NO_DELETED_NAME = 8355
ERROR_DS_SUBREF_MUST_HAVE_PARENT = 8356
ERROR_DS_NCNAME_MUST_BE_NC = 8357
ERROR_DS_CANT_ADD_SYSTEM_ONLY = 8358
ERROR_DS_CLASS_MUST_BE_CONCRETE = 8359
ERROR_DS_INVALID_DMD = 8360
ERROR_DS_OBJ_GUID_EXISTS = 8361
ERROR_DS_NOT_ON_BACKLINK = 8362
ERROR_DS_NO_CROSSREF_FOR_NC = 8363
ERROR_DS_SHUTTING_DOWN = 8364
ERROR_DS_UNKNOWN_OPERATION = 8365
ERROR_DS_INVALID_ROLE_OWNER = 8366
ERROR_DS_COULDNT_CONTACT_FSMO = 8367
ERROR_DS_CROSS_NC_DN_RENAME = 8368
ERROR_DS_CANT_MOD_SYSTEM_ONLY = 8369
ERROR_DS_REPLICATOR_ONLY = 8370
ERROR_DS_OBJ_CLASS_NOT_DEFINED = 8371
ERROR_DS_OBJ_CLASS_NOT_SUBCLASS = 8372
ERROR_DS_NAME_REFERENCE_INVALID = 8373
ERROR_DS_CROSS_REF_EXISTS = 8374
ERROR_DS_CANT_DEL_MASTER_CROSSREF = 8375
ERROR_DS_SUBTREE_NOTIFY_NOT_NC_HEAD = 8376
ERROR_DS_NOTIFY_FILTER_TOO_COMPLEX = 8377
ERROR_DS_DUP_RDN = 8378
ERROR_DS_DUP_OID = 8379
ERROR_DS_DUP_MAPI_ID = 8380
ERROR_DS_DUP_SCHEMA_ID_GUID = 8381
ERROR_DS_DUP_LDAP_DISPLAY_NAME = 8382
ERROR_DS_SEMANTIC_ATT_TEST = 8383
ERROR_DS_SYNTAX_MISMATCH = 8384
ERROR_DS_EXISTS_IN_MUST_HAVE = 8385
ERROR_DS_EXISTS_IN_MAY_HAVE = 8386
ERROR_DS_NONEXISTENT_MAY_HAVE = 8387
ERROR_DS_NONEXISTENT_MUST_HAVE = 8388
ERROR_DS_AUX_CLS_TEST_FAIL = 8389
ERROR_DS_NONEXISTENT_POSS_SUP = 8390
ERROR_DS_SUB_CLS_TEST_FAIL = 8391
ERROR_DS_BAD_RDN_ATT_ID_SYNTAX = 8392
ERROR_DS_EXISTS_IN_AUX_CLS = 8393
ERROR_DS_EXISTS_IN_SUB_CLS = 8394
ERROR_DS_EXISTS_IN_POSS_SUP = 8395
ERROR_DS_RECALCSCHEMA_FAILED = 8396
ERROR_DS_TREE_DELETE_NOT_FINISHED = 8397
ERROR_DS_CANT_DELETE = 8398
ERROR_DS_ATT_SCHEMA_REQ_ID = 8399
ERROR_DS_BAD_ATT_SCHEMA_SYNTAX = 8400
ERROR_DS_CANT_CACHE_ATT = 8401
ERROR_DS_CANT_CACHE_CLASS = 8402
ERROR_DS_CANT_REMOVE_ATT_CACHE = 8403
ERROR_DS_CANT_REMOVE_CLASS_CACHE = 8404
ERROR_DS_CANT_RETRIEVE_DN = 8405
ERROR_DS_MISSING_SUPREF = 8406
ERROR_DS_CANT_RETRIEVE_INSTANCE = 8407
ERROR_DS_CODE_INCONSISTENCY = 8408
ERROR_DS_DATABASE_ERROR = 8409
ERROR_DS_GOVERNSID_MISSING = 8410
ERROR_DS_MISSING_EXPECTED_ATT = 8411
ERROR_DS_NCNAME_MISSING_CR_REF = 8412
ERROR_DS_SECURITY_CHECKING_ERROR = 8413
ERROR_DS_SCHEMA_NOT_LOADED = 8414
ERROR_DS_SCHEMA_ALLOC_FAILED = 8415
ERROR_DS_ATT_SCHEMA_REQ_SYNTAX = 8416
ERROR_DS_GCVERIFY_ERROR = 8417
ERROR_DS_DRA_SCHEMA_MISMATCH = 8418
ERROR_DS_CANT_FIND_DSA_OBJ = 8419
ERROR_DS_CANT_FIND_EXPECTED_NC = 8420
ERROR_DS_CANT_FIND_NC_IN_CACHE = 8421
ERROR_DS_CANT_RETRIEVE_CHILD = 8422
ERROR_DS_SECURITY_ILLEGAL_MODIFY = 8423
ERROR_DS_CANT_REPLACE_HIDDEN_REC = 8424
ERROR_DS_BAD_HIERARCHY_FILE = 8425
ERROR_DS_BUILD_HIERARCHY_TABLE_FAILED = 8426
ERROR_DS_CONFIG_PARAM_MISSING = 8427
ERROR_DS_COUNTING_AB_INDICES_FAILED = 8428
ERROR_DS_HIERARCHY_TABLE_MALLOC_FAILED = 8429
ERROR_DS_INTERNAL_FAILURE = 8430
ERROR_DS_UNKNOWN_ERROR = 8431
ERROR_DS_ROOT_REQUIRES_CLASS_TOP = 8432
ERROR_DS_REFUSING_FSMO_ROLES = 8433
ERROR_DS_MISSING_FSMO_SETTINGS = 8434
ERROR_DS_UNABLE_TO_SURRENDER_ROLES = 8435
ERROR_DS_DRA_GENERIC = 8436
ERROR_DS_DRA_INVALID_PARAMETER = 8437
ERROR_DS_DRA_BUSY = 8438
ERROR_DS_DRA_BAD_DN = 8439
ERROR_DS_DRA_BAD_NC = 8440
ERROR_DS_DRA_DN_EXISTS = 8441
ERROR_DS_DRA_INTERNAL_ERROR = 8442
ERROR_DS_DRA_INCONSISTENT_DIT = 8443
ERROR_DS_DRA_CONNECTION_FAILED = 8444
ERROR_DS_DRA_BAD_INSTANCE_TYPE = 8445
ERROR_DS_DRA_OUT_OF_MEM = 8446
ERROR_DS_DRA_MAIL_PROBLEM = 8447
ERROR_DS_DRA_REF_ALREADY_EXISTS = 8448
ERROR_DS_DRA_REF_NOT_FOUND = 8449
ERROR_DS_DRA_OBJ_IS_REP_SOURCE = 8450
ERROR_DS_DRA_DB_ERROR = 8451
ERROR_DS_DRA_NO_REPLICA = 8452
ERROR_DS_DRA_ACCESS_DENIED = 8453
ERROR_DS_DRA_NOT_SUPPORTED = 8454
ERROR_DS_DRA_RPC_CANCELLED = 8455
ERROR_DS_DRA_SOURCE_DISABLED = 8456
ERROR_DS_DRA_SINK_DISABLED = 8457
ERROR_DS_DRA_NAME_COLLISION = 8458
ERROR_DS_DRA_SOURCE_REINSTALLED = 8459
ERROR_DS_DRA_MISSING_PARENT = 8460
ERROR_DS_DRA_PREEMPTED = 8461
ERROR_DS_DRA_ABANDON_SYNC = 8462
ERROR_DS_DRA_SHUTDOWN = 8463
ERROR_DS_DRA_INCOMPATIBLE_PARTIAL_SET = 8464
ERROR_DS_DRA_SOURCE_IS_PARTIAL_REPLICA = 8465
ERROR_DS_DRA_EXTN_CONNECTION_FAILED = 8466
ERROR_DS_INSTALL_SCHEMA_MISMATCH = 8467
ERROR_DS_DUP_LINK_ID = 8468
ERROR_DS_NAME_ERROR_RESOLVING = 8469
ERROR_DS_NAME_ERROR_NOT_FOUND = 8470
ERROR_DS_NAME_ERROR_NOT_UNIQUE = 8471
ERROR_DS_NAME_ERROR_NO_MAPPING = 8472
ERROR_DS_NAME_ERROR_DOMAIN_ONLY = 8473
ERROR_DS_NAME_ERROR_NO_SYNTACTICAL_MAPPING = 8474
ERROR_DS_CONSTRUCTED_ATT_MOD = 8475
ERROR_DS_WRONG_OM_OBJ_CLASS = 8476
ERROR_DS_DRA_REPL_PENDING = 8477
ERROR_DS_DS_REQUIRED = 8478
ERROR_DS_INVALID_LDAP_DISPLAY_NAME = 8479
ERROR_DS_NON_BASE_SEARCH = 8480
ERROR_DS_CANT_RETRIEVE_ATTS = 8481
ERROR_DS_BACKLINK_WITHOUT_LINK = 8482
ERROR_DS_EPOCH_MISMATCH = 8483
ERROR_DS_SRC_NAME_MISMATCH = 8484
ERROR_DS_SRC_AND_DST_NC_IDENTICAL = 8485
ERROR_DS_DST_NC_MISMATCH = 8486
ERROR_DS_NOT_AUTHORITIVE_FOR_DST_NC = 8487
ERROR_DS_SRC_GUID_MISMATCH = 8488
ERROR_DS_CANT_MOVE_DELETED_OBJECT = 8489
ERROR_DS_PDC_OPERATION_IN_PROGRESS = 8490
ERROR_DS_CROSS_DOMAIN_CLEANUP_REQD = 8491
ERROR_DS_ILLEGAL_XDOM_MOVE_OPERATION = 8492
ERROR_DS_CANT_WITH_ACCT_GROUP_MEMBERSHPS = 8493
ERROR_DS_NC_MUST_HAVE_NC_PARENT = 8494
ERROR_DS_CR_IMPOSSIBLE_TO_VALIDATE = 8495
ERROR_DS_DST_DOMAIN_NOT_NATIVE = 8496
ERROR_DS_MISSING_INFRASTRUCTURE_CONTAINER = 8497
ERROR_DS_CANT_MOVE_ACCOUNT_GROUP = 8498
ERROR_DS_CANT_MOVE_RESOURCE_GROUP = 8499
ERROR_DS_INVALID_SEARCH_FLAG = 8500
ERROR_DS_NO_TREE_DELETE_ABOVE_NC = 8501
ERROR_DS_COULDNT_LOCK_TREE_FOR_DELETE = 8502
ERROR_DS_COULDNT_IDENTIFY_OBJECTS_FOR_TREE_DELETE = 8503
ERROR_DS_SAM_INIT_FAILURE = 8504
ERROR_DS_SENSITIVE_GROUP_VIOLATION = 8505
ERROR_DS_CANT_MOD_PRIMARYGROUPID = 8506
ERROR_DS_ILLEGAL_BASE_SCHEMA_MOD = 8507
ERROR_DS_NONSAFE_SCHEMA_CHANGE = 8508
ERROR_DS_SCHEMA_UPDATE_DISALLOWED = 8509
ERROR_DS_CANT_CREATE_UNDER_SCHEMA = 8510
ERROR_DS_INSTALL_NO_SRC_SCH_VERSION = 8511
ERROR_DS_INSTALL_NO_SCH_VERSION_IN_INIFILE = 8512
ERROR_DS_INVALID_GROUP_TYPE = 8513
ERROR_DS_NO_NEST_GLOBALGROUP_IN_MIXEDDOMAIN = 8514
ERROR_DS_NO_NEST_LOCALGROUP_IN_MIXEDDOMAIN = 8515
ERROR_DS_GLOBAL_CANT_HAVE_LOCAL_MEMBER = 8516
ERROR_DS_GLOBAL_CANT_HAVE_UNIVERSAL_MEMBER = 8517
ERROR_DS_UNIVERSAL_CANT_HAVE_LOCAL_MEMBER = 8518
ERROR_DS_GLOBAL_CANT_HAVE_CROSSDOMAIN_MEMBER = 8519
ERROR_DS_LOCAL_CANT_HAVE_CROSSDOMAIN_LOCAL_MEMBER = 8520
ERROR_DS_HAVE_PRIMARY_MEMBERS = 8521
ERROR_DS_STRING_SD_CONVERSION_FAILED = 8522
ERROR_DS_NAMING_MASTER_GC = 8523
ERROR_DS_DNS_LOOKUP_FAILURE = 8524
ERROR_DS_COULDNT_UPDATE_SPNS = 8525
ERROR_DS_CANT_RETRIEVE_SD = 8526
ERROR_DS_KEY_NOT_UNIQUE = 8527
ERROR_DS_WRONG_LINKED_ATT_SYNTAX = 8528
ERROR_DS_SAM_NEED_BOOTKEY_PASSWORD = 8529
ERROR_DS_SAM_NEED_BOOTKEY_FLOPPY = 8530
ERROR_DS_CANT_START = 8531
ERROR_DS_INIT_FAILURE = 8532
ERROR_DS_NO_PKT_PRIVACY_ON_CONNECTION = 8533
ERROR_DS_SOURCE_DOMAIN_IN_FOREST = 8534
ERROR_DS_DESTINATION_DOMAIN_NOT_IN_FOREST = 8535
ERROR_DS_DESTINATION_AUDITING_NOT_ENABLED = 8536
ERROR_DS_CANT_FIND_DC_FOR_SRC_DOMAIN = 8537
ERROR_DS_SRC_OBJ_NOT_GROUP_OR_USER = 8538
ERROR_DS_SRC_SID_EXISTS_IN_FOREST = 8539
ERROR_DS_SRC_AND_DST_OBJECT_CLASS_MISMATCH = 8540
ERROR_SAM_INIT_FAILURE = 8541
ERROR_DS_DRA_SCHEMA_INFO_SHIP = 8542
ERROR_DS_DRA_SCHEMA_CONFLICT = 8543
ERROR_DS_DRA_EARLIER_SCHEMA_CONFLICT = 8544
ERROR_DS_DRA_OBJ_NC_MISMATCH = 8545
ERROR_DS_NC_STILL_HAS_DSAS = 8546
ERROR_DS_GC_REQUIRED = 8547
ERROR_DS_LOCAL_MEMBER_OF_LOCAL_ONLY = 8548
ERROR_DS_NO_FPO_IN_UNIVERSAL_GROUPS = 8549
ERROR_DS_CANT_ADD_TO_GC = 8550
ERROR_DS_NO_CHECKPOINT_WITH_PDC = 8551
ERROR_DS_SOURCE_AUDITING_NOT_ENABLED = 8552
ERROR_DS_CANT_CREATE_IN_NONDOMAIN_NC = 8553
ERROR_DS_INVALID_NAME_FOR_SPN = 8554
ERROR_DS_FILTER_USES_CONTRUCTED_ATTRS = 8555
ERROR_DS_UNICODEPWD_NOT_IN_QUOTES = 8556
ERROR_DS_MACHINE_ACCOUNT_QUOTA_EXCEEDED = 8557
ERROR_DS_MUST_BE_RUN_ON_DST_DC = 8558
ERROR_DS_SRC_DC_MUST_BE_SP4_OR_GREATER = 8559
ERROR_DS_CANT_TREE_DELETE_CRITICAL_OBJ = 8560
ERROR_DS_INIT_FAILURE_CONSOLE = 8561
ERROR_DS_SAM_INIT_FAILURE_CONSOLE = 8562
ERROR_DS_FOREST_VERSION_TOO_HIGH = 8563
ERROR_DS_DOMAIN_VERSION_TOO_HIGH = 8564
ERROR_DS_FOREST_VERSION_TOO_LOW = 8565
ERROR_DS_DOMAIN_VERSION_TOO_LOW = 8566
ERROR_DS_INCOMPATIBLE_VERSION = 8567
ERROR_DS_LOW_DSA_VERSION = 8568
ERROR_DS_NO_BEHAVIOR_VERSION_IN_MIXEDDOMAIN = 8569
ERROR_DS_NOT_SUPPORTED_SORT_ORDER = 8570
ERROR_DS_NAME_NOT_UNIQUE = 8571
ERROR_DS_MACHINE_ACCOUNT_CREATED_PRENT4 = 8572
ERROR_DS_OUT_OF_VERSION_STORE = 8573
ERROR_DS_INCOMPATIBLE_CONTROLS_USED = 8574
ERROR_DS_NO_REF_DOMAIN = 8575
ERROR_DS_RESERVED_LINK_ID = 8576
ERROR_DS_LINK_ID_NOT_AVAILABLE = 8577
ERROR_DS_AG_CANT_HAVE_UNIVERSAL_MEMBER = 8578
ERROR_DS_MODIFYDN_DISALLOWED_BY_INSTANCE_TYPE = 8579
ERROR_DS_NO_OBJECT_MOVE_IN_SCHEMA_NC = 8580
ERROR_DS_MODIFYDN_DISALLOWED_BY_FLAG = 8581
ERROR_DS_MODIFYDN_WRONG_GRANDPARENT = 8582
ERROR_DS_NAME_ERROR_TRUST_REFERRAL = 8583
ERROR_NOT_SUPPORTED_ON_STANDARD_SERVER = 8584
ERROR_DS_CANT_ACCESS_REMOTE_PART_OF_AD = 8585
ERROR_DS_CR_IMPOSSIBLE_TO_VALIDATE_V2 = 8586
ERROR_DS_THREAD_LIMIT_EXCEEDED = 8587
ERROR_DS_NOT_CLOSEST = 8588
ERROR_DS_CANT_DERIVE_SPN_WITHOUT_SERVER_REF = 8589
ERROR_DS_SINGLE_USER_MODE_FAILED = 8590
ERROR_DS_NTDSCRIPT_SYNTAX_ERROR = 8591
ERROR_DS_NTDSCRIPT_PROCESS_ERROR = 8592
ERROR_DS_DIFFERENT_REPL_EPOCHS = 8593
ERROR_DS_DRS_EXTENSIONS_CHANGED = 8594
ERROR_DS_REPLICA_SET_CHANGE_NOT_ALLOWED_ON_DISABLED_CR = 8595
ERROR_DS_NO_MSDS_INTID = 8596
ERROR_DS_DUP_MSDS_INTID = 8597
ERROR_DS_EXISTS_IN_RDNATTID = 8598
ERROR_DS_AUTHORIZATION_FAILED = 8599
ERROR_DS_INVALID_SCRIPT = 8600
ERROR_DS_REMOTE_CROSSREF_OP_FAILED = 8601
ERROR_DS_CROSS_REF_BUSY = 8602
ERROR_DS_CANT_DERIVE_SPN_FOR_DELETED_DOMAIN = 8603
ERROR_DS_CANT_DEMOTE_WITH_WRITEABLE_NC = 8604
ERROR_DS_DUPLICATE_ID_FOUND = 8605
ERROR_DS_INSUFFICIENT_ATTR_TO_CREATE_OBJECT = 8606
ERROR_DS_GROUP_CONVERSION_ERROR = 8607
ERROR_DS_CANT_MOVE_APP_BASIC_GROUP = 8608
ERROR_DS_CANT_MOVE_APP_QUERY_GROUP = 8609
ERROR_DS_ROLE_NOT_VERIFIED = 8610
ERROR_DS_WKO_CONTAINER_CANNOT_BE_SPECIAL = 8611
ERROR_DS_DOMAIN_RENAME_IN_PROGRESS = 8612
ERROR_DS_EXISTING_AD_CHILD_NC = 8613
ERROR_DS_REPL_LIFETIME_EXCEEDED = 8614
ERROR_DS_DISALLOWED_IN_SYSTEM_CONTAINER = 8615
ERROR_DS_LDAP_SEND_QUEUE_FULL = 8616
ERROR_DS_DRA_OUT_SCHEDULE_WINDOW = 8617
ERROR_DS_POLICY_NOT_KNOWN = 8618
ERROR_NO_SITE_SETTINGS_OBJECT = 8619
ERROR_NO_SECRETS = 8620
ERROR_NO_WRITABLE_DC_FOUND = 8621
ERROR_DS_NO_SERVER_OBJECT = 8622
ERROR_DS_NO_NTDSA_OBJECT = 8623
ERROR_DS_NON_ASQ_SEARCH = 8624
ERROR_DS_AUDIT_FAILURE = 8625
ERROR_DS_INVALID_SEARCH_FLAG_SUBTREE = 8626
ERROR_DS_INVALID_SEARCH_FLAG_TUPLE = 8627
ERROR_DS_HIERARCHY_TABLE_TOO_DEEP = 8628
ERROR_DS_DRA_CORRUPT_UTD_VECTOR = 8629
ERROR_DS_DRA_SECRETS_DENIED = 8630
ERROR_DS_RESERVED_MAPI_ID = 8631
ERROR_DS_MAPI_ID_NOT_AVAILABLE = 8632
ERROR_DS_DRA_MISSING_KRBTGT_SECRET = 8633
ERROR_DS_DOMAIN_NAME_EXISTS_IN_FOREST = 8634
ERROR_DS_FLAT_NAME_EXISTS_IN_FOREST = 8635
ERROR_INVALID_USER_PRINCIPAL_NAME = 8636
ERROR_DS_OID_MAPPED_GROUP_CANT_HAVE_MEMBERS = 8637
ERROR_DS_OID_NOT_FOUND = 8638
ERROR_DS_DRA_RECYCLED_TARGET = 8639
ERROR_DS_DISALLOWED_NC_REDIRECT = 8640
ERROR_DS_HIGH_ADLDS_FFL = 8641
ERROR_DS_HIGH_DSA_VERSION = 8642
ERROR_DS_LOW_ADLDS_FFL = 8643
ERROR_DOMAIN_SID_SAME_AS_LOCAL_WORKSTATION = 8644
ERROR_DS_UNDELETE_SAM_VALIDATION_FAILED = 8645
ERROR_INCORRECT_ACCOUNT_TYPE = 8646
ERROR_DS_SPN_VALUE_NOT_UNIQUE_IN_FOREST = 8647
ERROR_DS_UPN_VALUE_NOT_UNIQUE_IN_FOREST = 8648
ERROR_DS_MISSING_FOREST_TRUST = 8649
ERROR_DS_VALUE_KEY_NOT_UNIQUE = 8650
ERROR_WEAK_WHFBKEY_BLOCKED = 8651
ERROR_DS_PER_ATTRIBUTE_AUTHZ_FAILED_DURING_ADD = 8652
ERROR_LOCAL_POLICY_MODIFICATION_NOT_SUPPORTED = 8653
ERROR_POLICY_CONTROLLED_ACCOUNT = 8654
ERROR_LAPS_LEGACY_SCHEMA_MISSING = 8655
ERROR_LAPS_SCHEMA_MISSING = 8656
ERROR_LAPS_ENCRYPTION_REQUIRES_2016_DFL = 8657
DNS_ERROR_RESPONSE_CODES_BASE = 9000
DNS_ERROR_RCODE_NO_ERROR = NO_ERROR
DNS_ERROR_MASK = 0x00002328
DNS_ERROR_RCODE_FORMAT_ERROR = 9001
DNS_ERROR_RCODE_SERVER_FAILURE = 9002
DNS_ERROR_RCODE_NAME_ERROR = 9003
DNS_ERROR_RCODE_NOT_IMPLEMENTED = 9004
DNS_ERROR_RCODE_REFUSED = 9005
DNS_ERROR_RCODE_YXDOMAIN = 9006
DNS_ERROR_RCODE_YXRRSET = 9007
DNS_ERROR_RCODE_NXRRSET = 9008
DNS_ERROR_RCODE_NOTAUTH = 9009
DNS_ERROR_RCODE_NOTZONE = 9010
DNS_ERROR_RCODE_BADSIG = 9016
DNS_ERROR_RCODE_BADKEY = 9017
DNS_ERROR_RCODE_BADTIME = 9018
DNS_ERROR_RCODE_LAST = DNS_ERROR_RCODE_BADTIME
DNS_ERROR_DNSSEC_BASE = 9100
DNS_ERROR_KEYMASTER_REQUIRED = 9101
DNS_ERROR_NOT_ALLOWED_ON_SIGNED_ZONE = 9102
DNS_ERROR_NSEC3_INCOMPATIBLE_WITH_RSA_SHA1 = 9103
DNS_ERROR_NOT_ENOUGH_SIGNING_KEY_DESCRIPTORS = 9104
DNS_ERROR_UNSUPPORTED_ALGORITHM = 9105
DNS_ERROR_INVALID_KEY_SIZE = 9106
DNS_ERROR_SIGNING_KEY_NOT_ACCESSIBLE = 9107
DNS_ERROR_KSP_DOES_NOT_SUPPORT_PROTECTION = 9108
DNS_ERROR_UNEXPECTED_DATA_PROTECTION_ERROR = 9109
DNS_ERROR_UNEXPECTED_CNG_ERROR = 9110
DNS_ERROR_UNKNOWN_SIGNING_PARAMETER_VERSION = 9111
DNS_ERROR_KSP_NOT_ACCESSIBLE = 9112
DNS_ERROR_TOO_MANY_SKDS = 9113
DNS_ERROR_INVALID_ROLLOVER_PERIOD = 9114
DNS_ERROR_INVALID_INITIAL_ROLLOVER_OFFSET = 9115
DNS_ERROR_ROLLOVER_IN_PROGRESS = 9116
DNS_ERROR_STANDBY_KEY_NOT_PRESENT = 9117
DNS_ERROR_NOT_ALLOWED_ON_ZSK = 9118
DNS_ERROR_NOT_ALLOWED_ON_ACTIVE_SKD = 9119
DNS_ERROR_ROLLOVER_ALREADY_QUEUED = 9120
DNS_ERROR_NOT_ALLOWED_ON_UNSIGNED_ZONE = 9121
DNS_ERROR_BAD_KEYMASTER = 9122
DNS_ERROR_INVALID_SIGNATURE_VALIDITY_PERIOD = 9123
DNS_ERROR_INVALID_NSEC3_ITERATION_COUNT = 9124
DNS_ERROR_DNSSEC_IS_DISABLED = 9125
DNS_ERROR_INVALID_XML = 9126
DNS_ERROR_NO_VALID_TRUST_ANCHORS = 9127
DNS_ERROR_ROLLOVER_NOT_POKEABLE = 9128
DNS_ERROR_NSEC3_NAME_COLLISION = 9129
DNS_ERROR_NSEC_INCOMPATIBLE_WITH_NSEC3_RSA_SHA1 = 9130
DNS_ERROR_PACKET_FMT_BASE = 9500
DNS_INFO_NO_RECORDS = 9501
DNS_ERROR_BAD_PACKET = 9502
DNS_ERROR_NO_PACKET = 9503
DNS_ERROR_RCODE = 9504
DNS_ERROR_UNSECURE_PACKET = 9505
DNS_STATUS_PACKET_UNSECURE = DNS_ERROR_UNSECURE_PACKET
DNS_REQUEST_PENDING = 9506
DNS_ERROR_NO_MEMORY = ERROR_OUTOFMEMORY
DNS_ERROR_INVALID_NAME = ERROR_INVALID_NAME
DNS_ERROR_INVALID_DATA = ERROR_INVALID_DATA
DNS_ERROR_GENERAL_API_BASE = 9550
DNS_ERROR_INVALID_TYPE = 9551
DNS_ERROR_INVALID_IP_ADDRESS = 9552
DNS_ERROR_INVALID_PROPERTY = 9553
DNS_ERROR_TRY_AGAIN_LATER = 9554
DNS_ERROR_NOT_UNIQUE = 9555
DNS_ERROR_NON_RFC_NAME = 9556
DNS_STATUS_FQDN = 9557
DNS_STATUS_DOTTED_NAME = 9558
DNS_STATUS_SINGLE_PART_NAME = 9559
DNS_ERROR_INVALID_NAME_CHAR = 9560
DNS_ERROR_NUMERIC_NAME = 9561
DNS_ERROR_NOT_ALLOWED_ON_ROOT_SERVER = 9562
DNS_ERROR_NOT_ALLOWED_UNDER_DELEGATION = 9563
DNS_ERROR_CANNOT_FIND_ROOT_HINTS = 9564
DNS_ERROR_INCONSISTENT_ROOT_HINTS = 9565
DNS_ERROR_DWORD_VALUE_TOO_SMALL = 9566
DNS_ERROR_DWORD_VALUE_TOO_LARGE = 9567
DNS_ERROR_BACKGROUND_LOADING = 9568
DNS_ERROR_NOT_ALLOWED_ON_RODC = 9569
DNS_ERROR_NOT_ALLOWED_UNDER_DNAME = 9570
DNS_ERROR_DELEGATION_REQUIRED = 9571
DNS_ERROR_INVALID_POLICY_TABLE = 9572
DNS_ERROR_ADDRESS_REQUIRED = 9573
DNS_ERROR_ZONE_BASE = 9600
DNS_ERROR_ZONE_DOES_NOT_EXIST = 9601
DNS_ERROR_NO_ZONE_INFO = 9602
DNS_ERROR_INVALID_ZONE_OPERATION = 9603
DNS_ERROR_ZONE_CONFIGURATION_ERROR = 9604
DNS_ERROR_ZONE_HAS_NO_SOA_RECORD = 9605
DNS_ERROR_ZONE_HAS_NO_NS_RECORDS = 9606
DNS_ERROR_ZONE_LOCKED = 9607
DNS_ERROR_ZONE_CREATION_FAILED = 9608
DNS_ERROR_ZONE_ALREADY_EXISTS = 9609
DNS_ERROR_AUTOZONE_ALREADY_EXISTS = 9610
DNS_ERROR_INVALID_ZONE_TYPE = 9611
DNS_ERROR_SECONDARY_REQUIRES_MASTER_IP = 9612
DNS_ERROR_ZONE_NOT_SECONDARY = 9613
DNS_ERROR_NEED_SECONDARY_ADDRESSES = 9614
DNS_ERROR_WINS_INIT_FAILED = 9615
DNS_ERROR_NEED_WINS_SERVERS = 9616
DNS_ERROR_NBSTAT_INIT_FAILED = 9617
DNS_ERROR_SOA_DELETE_INVALID = 9618
DNS_ERROR_FORWARDER_ALREADY_EXISTS = 9619
DNS_ERROR_ZONE_REQUIRES_MASTER_IP = 9620
DNS_ERROR_ZONE_IS_SHUTDOWN = 9621
DNS_ERROR_ZONE_LOCKED_FOR_SIGNING = 9622
DNS_ERROR_DATAFILE_BASE = 9650
DNS_ERROR_PRIMARY_REQUIRES_DATAFILE = 9651
DNS_ERROR_INVALID_DATAFILE_NAME = 9652
DNS_ERROR_DATAFILE_OPEN_FAILURE = 9653
DNS_ERROR_FILE_WRITEBACK_FAILED = 9654
DNS_ERROR_DATAFILE_PARSING = 9655
DNS_ERROR_DATABASE_BASE = 9700
DNS_ERROR_RECORD_DOES_NOT_EXIST = 9701
DNS_ERROR_RECORD_FORMAT = 9702
DNS_ERROR_NODE_CREATION_FAILED = 9703
DNS_ERROR_UNKNOWN_RECORD_TYPE = 9704
DNS_ERROR_RECORD_TIMED_OUT = 9705
DNS_ERROR_NAME_NOT_IN_ZONE = 9706
DNS_ERROR_CNAME_LOOP = 9707
DNS_ERROR_NODE_IS_CNAME = 9708
DNS_ERROR_CNAME_COLLISION = 9709
DNS_ERROR_RECORD_ONLY_AT_ZONE_ROOT = 9710
DNS_ERROR_RECORD_ALREADY_EXISTS = 9711
DNS_ERROR_SECONDARY_DATA = 9712
DNS_ERROR_NO_CREATE_CACHE_DATA = 9713
DNS_ERROR_NAME_DOES_NOT_EXIST = 9714
DNS_WARNING_PTR_CREATE_FAILED = 9715
DNS_WARNING_DOMAIN_UNDELETED = 9716
DNS_ERROR_DS_UNAVAILABLE = 9717
DNS_ERROR_DS_ZONE_ALREADY_EXISTS = 9718
DNS_ERROR_NO_BOOTFILE_IF_DS_ZONE = 9719
DNS_ERROR_NODE_IS_DNAME = 9720
DNS_ERROR_DNAME_COLLISION = 9721
DNS_ERROR_ALIAS_LOOP = 9722
DNS_ERROR_OPERATION_BASE = 9750
DNS_INFO_AXFR_COMPLETE = 9751
DNS_ERROR_AXFR = 9752
DNS_INFO_ADDED_LOCAL_WINS = 9753
DNS_ERROR_SECURE_BASE = 9800
DNS_STATUS_CONTINUE_NEEDED = 9801
DNS_ERROR_SETUP_BASE = 9850
DNS_ERROR_NO_TCPIP = 9851
DNS_ERROR_NO_DNS_SERVERS = 9852
DNS_ERROR_DP_BASE = 9900
DNS_ERROR_DP_DOES_NOT_EXIST = 9901
DNS_ERROR_DP_ALREADY_EXISTS = 9902
DNS_ERROR_DP_NOT_ENLISTED = 9903
DNS_ERROR_DP_ALREADY_ENLISTED = 9904
DNS_ERROR_DP_NOT_AVAILABLE = 9905
DNS_ERROR_DP_FSMO_ERROR = 9906
DNS_ERROR_RRL_NOT_ENABLED = 9911
DNS_ERROR_RRL_INVALID_WINDOW_SIZE = 9912
DNS_ERROR_RRL_INVALID_IPV4_PREFIX = 9913
DNS_ERROR_RRL_INVALID_IPV6_PREFIX = 9914
DNS_ERROR_RRL_INVALID_TC_RATE = 9915
DNS_ERROR_RRL_INVALID_LEAK_RATE = 9916
DNS_ERROR_RRL_LEAK_RATE_LESSTHAN_TC_RATE = 9917
DNS_ERROR_VIRTUALIZATION_INSTANCE_ALREADY_EXISTS = 9921
DNS_ERROR_VIRTUALIZATION_INSTANCE_DOES_NOT_EXIST = 9922
DNS_ERROR_VIRTUALIZATION_TREE_LOCKED = 9923
DNS_ERROR_INVAILD_VIRTUALIZATION_INSTANCE_NAME = 9924
DNS_ERROR_DEFAULT_VIRTUALIZATION_INSTANCE = 9925
DNS_ERROR_ZONESCOPE_ALREADY_EXISTS = 9951
DNS_ERROR_ZONESCOPE_DOES_NOT_EXIST = 9952
DNS_ERROR_DEFAULT_ZONESCOPE = 9953
DNS_ERROR_INVALID_ZONESCOPE_NAME = 9954
DNS_ERROR_NOT_ALLOWED_WITH_ZONESCOPES = 9955
DNS_ERROR_LOAD_ZONESCOPE_FAILED = 9956
DNS_ERROR_ZONESCOPE_FILE_WRITEBACK_FAILED = 9957
DNS_ERROR_INVALID_SCOPE_NAME = 9958
DNS_ERROR_SCOPE_DOES_NOT_EXIST = 9959
DNS_ERROR_DEFAULT_SCOPE = 9960
DNS_ERROR_INVALID_SCOPE_OPERATION = 9961
DNS_ERROR_SCOPE_LOCKED = 9962
DNS_ERROR_SCOPE_ALREADY_EXISTS = 9963
DNS_ERROR_POLICY_ALREADY_EXISTS = 9971
DNS_ERROR_POLICY_DOES_NOT_EXIST = 9972
DNS_ERROR_POLICY_INVALID_CRITERIA = 9973
DNS_ERROR_POLICY_INVALID_SETTINGS = 9974
DNS_ERROR_CLIENT_SUBNET_IS_ACCESSED = 9975
DNS_ERROR_CLIENT_SUBNET_DOES_NOT_EXIST = 9976
DNS_ERROR_CLIENT_SUBNET_ALREADY_EXISTS = 9977
DNS_ERROR_SUBNET_DOES_NOT_EXIST = 9978
DNS_ERROR_SUBNET_ALREADY_EXISTS = 9979
DNS_ERROR_POLICY_LOCKED = 9980
DNS_ERROR_POLICY_INVALID_WEIGHT = 9981
DNS_ERROR_POLICY_INVALID_NAME = 9982
DNS_ERROR_POLICY_MISSING_CRITERIA = 9983
DNS_ERROR_INVALID_CLIENT_SUBNET_NAME = 9984
DNS_ERROR_POLICY_PROCESSING_ORDER_INVALID = 9985
DNS_ERROR_POLICY_SCOPE_MISSING = 9986
DNS_ERROR_POLICY_SCOPE_NOT_ALLOWED = 9987
DNS_ERROR_SERVERSCOPE_IS_REFERENCED = 9988
DNS_ERROR_ZONESCOPE_IS_REFERENCED = 9989
DNS_ERROR_POLICY_INVALID_CRITERIA_CLIENT_SUBNET = 9990
DNS_ERROR_POLICY_INVALID_CRITERIA_TRANSPORT_PROTOCOL = 9991
DNS_ERROR_POLICY_INVALID_CRITERIA_NETWORK_PROTOCOL = 9992
DNS_ERROR_POLICY_INVALID_CRITERIA_INTERFACE = 9993
DNS_ERROR_POLICY_INVALID_CRITERIA_FQDN = 9994
DNS_ERROR_POLICY_INVALID_CRITERIA_QUERY_TYPE = 9995
DNS_ERROR_POLICY_INVALID_CRITERIA_TIME_OF_DAY = 9996
WSABASEERR = 10000
WSAEINTR = 10004
WSAEBADF = 10009
WSAEACCES = 10013
WSAEFAULT = 10014
WSAEINVAL = 10022
WSAEMFILE = 10024
WSAEWOULDBLOCK = 10035
WSAEINPROGRESS = 10036
WSAEALREADY = 10037
WSAENOTSOCK = 10038
WSAEDESTADDRREQ = 10039
WSAEMSGSIZE = 10040
WSAEPROTOTYPE = 10041
WSAENOPROTOOPT = 10042
WSAEPROTONOSUPPORT = 10043
WSAESOCKTNOSUPPORT = 10044
WSAEOPNOTSUPP = 10045
WSAEPFNOSUPPORT = 10046
WSAEAFNOSUPPORT = 10047
WSAEADDRINUSE = 10048
WSAEADDRNOTAVAIL = 10049
WSAENETDOWN = 10050
WSAENETUNREACH = 10051
WSAENETRESET = 10052
WSAECONNABORTED = 10053
WSAECONNRESET = 10054
WSAENOBUFS = 10055
WSAEISCONN = 10056
WSAENOTCONN = 10057
WSAESHUTDOWN = 10058
WSAETOOMANYREFS = 10059
WSAETIMEDOUT = 10060
WSAECONNREFUSED = 10061
WSAELOOP = 10062
WSAENAMETOOLONG = 10063
WSAEHOSTDOWN = 10064
WSAEHOSTUNREACH = 10065
WSAENOTEMPTY = 10066
WSAEPROCLIM = 10067
WSAEUSERS = 10068
WSAEDQUOT = 10069
WSAESTALE = 10070
WSAEREMOTE = 10071
WSASYSNOTREADY = 10091
WSAVERNOTSUPPORTED = 10092
WSANOTINITIALISED = 10093
WSAEDISCON = 10101
WSAENOMORE = 10102
WSAECANCELLED = 10103
WSAEINVALIDPROCTABLE = 10104
WSAEINVALIDPROVIDER = 10105
WSAEPROVIDERFAILEDINIT = 10106
WSASYSCALLFAILURE = 10107
WSASERVICE_NOT_FOUND = 10108
WSATYPE_NOT_FOUND = 10109
WSA_E_NO_MORE = 10110
WSA_E_CANCELLED = 10111
WSAEREFUSED = 10112
WSAHOST_NOT_FOUND = 11001
WSATRY_AGAIN = 11002
WSANO_RECOVERY = 11003
WSANO_DATA = 11004
WSA_QOS_RECEIVERS = 11005
WSA_QOS_SENDERS = 11006
WSA_QOS_NO_SENDERS = 11007
WSA_QOS_NO_RECEIVERS = 11008
WSA_QOS_REQUEST_CONFIRMED = 11009
WSA_QOS_ADMISSION_FAILURE = 11010
WSA_QOS_POLICY_FAILURE = 11011
WSA_QOS_BAD_STYLE = 11012
WSA_QOS_BAD_OBJECT = 11013
WSA_QOS_TRAFFIC_CTRL_ERROR = 11014
WSA_QOS_GENERIC_ERROR = 11015
WSA_QOS_ESERVICETYPE = 11016
WSA_QOS_EFLOWSPEC = 11017
WSA_QOS_EPROVSPECBUF = 11018
WSA_QOS_EFILTERSTYLE = 11019
WSA_QOS_EFILTERTYPE = 11020
WSA_QOS_EFILTERCOUNT = 11021
WSA_QOS_EOBJLENGTH = 11022
WSA_QOS_EFLOWCOUNT = 11023
WSA_QOS_EUNKOWNPSOBJ = 11024
WSA_QOS_EPOLICYOBJ = 11025
WSA_QOS_EFLOWDESC = 11026
WSA_QOS_EPSFLOWSPEC = 11027
WSA_QOS_EPSFILTERSPEC = 11028
WSA_QOS_ESDMODEOBJ = 11029
WSA_QOS_ESHAPERATEOBJ = 11030
WSA_QOS_RESERVED_PETYPE = 11031
WSA_SECURE_HOST_NOT_FOUND = 11032
WSA_IPSEC_NAME_POLICY_ERROR = 11033
ERROR_IPSEC_QM_POLICY_EXISTS = 13000
ERROR_IPSEC_QM_POLICY_NOT_FOUND = 13001
ERROR_IPSEC_QM_POLICY_IN_USE = 13002
ERROR_IPSEC_MM_POLICY_EXISTS = 13003
ERROR_IPSEC_MM_POLICY_NOT_FOUND = 13004
ERROR_IPSEC_MM_POLICY_IN_USE = 13005
ERROR_IPSEC_MM_FILTER_EXISTS = 13006
ERROR_IPSEC_MM_FILTER_NOT_FOUND = 13007
ERROR_IPSEC_TRANSPORT_FILTER_EXISTS = 13008
ERROR_IPSEC_TRANSPORT_FILTER_NOT_FOUND = 13009
ERROR_IPSEC_MM_AUTH_EXISTS = 13010
ERROR_IPSEC_MM_AUTH_NOT_FOUND = 13011
ERROR_IPSEC_MM_AUTH_IN_USE = 13012
ERROR_IPSEC_DEFAULT_MM_POLICY_NOT_FOUND = 13013
ERROR_IPSEC_DEFAULT_MM_AUTH_NOT_FOUND = 13014
ERROR_IPSEC_DEFAULT_QM_POLICY_NOT_FOUND = 13015
ERROR_IPSEC_TUNNEL_FILTER_EXISTS = 13016
ERROR_IPSEC_TUNNEL_FILTER_NOT_FOUND = 13017
ERROR_IPSEC_MM_FILTER_PENDING_DELETION = 13018
ERROR_IPSEC_TRANSPORT_FILTER_PENDING_DELETION = 13019
ERROR_IPSEC_TUNNEL_FILTER_PENDING_DELETION = 13020
ERROR_IPSEC_MM_POLICY_PENDING_DELETION = 13021
ERROR_IPSEC_MM_AUTH_PENDING_DELETION = 13022
ERROR_IPSEC_QM_POLICY_PENDING_DELETION = 13023
WARNING_IPSEC_MM_POLICY_PRUNED = 13024
WARNING_IPSEC_QM_POLICY_PRUNED = 13025
ERROR_IPSEC_IKE_NEG_STATUS_BEGIN = 13800
ERROR_IPSEC_IKE_AUTH_FAIL = 13801
ERROR_IPSEC_IKE_ATTRIB_FAIL = 13802
ERROR_IPSEC_IKE_NEGOTIATION_PENDING = 13803
ERROR_IPSEC_IKE_GENERAL_PROCESSING_ERROR = 13804
ERROR_IPSEC_IKE_TIMED_OUT = 13805
ERROR_IPSEC_IKE_NO_CERT = 13806
ERROR_IPSEC_IKE_SA_DELETED = 13807
ERROR_IPSEC_IKE_SA_REAPED = 13808
ERROR_IPSEC_IKE_MM_ACQUIRE_DROP = 13809
ERROR_IPSEC_IKE_QM_ACQUIRE_DROP = 13810
ERROR_IPSEC_IKE_QUEUE_DROP_MM = 13811
ERROR_IPSEC_IKE_QUEUE_DROP_NO_MM = 13812
ERROR_IPSEC_IKE_DROP_NO_RESPONSE = 13813
ERROR_IPSEC_IKE_MM_DELAY_DROP = 13814
ERROR_IPSEC_IKE_QM_DELAY_DROP = 13815
ERROR_IPSEC_IKE_ERROR = 13816
ERROR_IPSEC_IKE_CRL_FAILED = 13817
ERROR_IPSEC_IKE_INVALID_KEY_USAGE = 13818
ERROR_IPSEC_IKE_INVALID_CERT_TYPE = 13819
ERROR_IPSEC_IKE_NO_PRIVATE_KEY = 13820
ERROR_IPSEC_IKE_SIMULTANEOUS_REKEY = 13821
ERROR_IPSEC_IKE_DH_FAIL = 13822
ERROR_IPSEC_IKE_CRITICAL_PAYLOAD_NOT_RECOGNIZED = 13823
ERROR_IPSEC_IKE_INVALID_HEADER = 13824
ERROR_IPSEC_IKE_NO_POLICY = 13825
ERROR_IPSEC_IKE_INVALID_SIGNATURE = 13826
ERROR_IPSEC_IKE_KERBEROS_ERROR = 13827
ERROR_IPSEC_IKE_NO_PUBLIC_KEY = 13828
ERROR_IPSEC_IKE_PROCESS_ERR = 13829
ERROR_IPSEC_IKE_PROCESS_ERR_SA = 13830
ERROR_IPSEC_IKE_PROCESS_ERR_PROP = 13831
ERROR_IPSEC_IKE_PROCESS_ERR_TRANS = 13832
ERROR_IPSEC_IKE_PROCESS_ERR_KE = 13833
ERROR_IPSEC_IKE_PROCESS_ERR_ID = 13834
ERROR_IPSEC_IKE_PROCESS_ERR_CERT = 13835
ERROR_IPSEC_IKE_PROCESS_ERR_CERT_REQ = 13836
ERROR_IPSEC_IKE_PROCESS_ERR_HASH = 13837
ERROR_IPSEC_IKE_PROCESS_ERR_SIG = 13838
ERROR_IPSEC_IKE_PROCESS_ERR_NONCE = 13839
ERROR_IPSEC_IKE_PROCESS_ERR_NOTIFY = 13840
ERROR_IPSEC_IKE_PROCESS_ERR_DELETE = 13841
ERROR_IPSEC_IKE_PROCESS_ERR_VENDOR = 13842
ERROR_IPSEC_IKE_INVALID_PAYLOAD = 13843
ERROR_IPSEC_IKE_LOAD_SOFT_SA = 13844
ERROR_IPSEC_IKE_SOFT_SA_TORN_DOWN = 13845
ERROR_IPSEC_IKE_INVALID_COOKIE = 13846
ERROR_IPSEC_IKE_NO_PEER_CERT = 13847
ERROR_IPSEC_IKE_PEER_CRL_FAILED = 13848
ERROR_IPSEC_IKE_POLICY_CHANGE = 13849
ERROR_IPSEC_IKE_NO_MM_POLICY = 13850
ERROR_IPSEC_IKE_NOTCBPRIV = 13851
ERROR_IPSEC_IKE_SECLOADFAIL = 13852
ERROR_IPSEC_IKE_FAILSSPINIT = 13853
ERROR_IPSEC_IKE_FAILQUERYSSP = 13854
ERROR_IPSEC_IKE_SRVACQFAIL = 13855
ERROR_IPSEC_IKE_SRVQUERYCRED = 13856
ERROR_IPSEC_IKE_GETSPIFAIL = 13857
ERROR_IPSEC_IKE_INVALID_FILTER = 13858
ERROR_IPSEC_IKE_OUT_OF_MEMORY = 13859
ERROR_IPSEC_IKE_ADD_UPDATE_KEY_FAILED = 13860
ERROR_IPSEC_IKE_INVALID_POLICY = 13861
ERROR_IPSEC_IKE_UNKNOWN_DOI = 13862
ERROR_IPSEC_IKE_INVALID_SITUATION = 13863
ERROR_IPSEC_IKE_DH_FAILURE = 13864
ERROR_IPSEC_IKE_INVALID_GROUP = 13865
ERROR_IPSEC_IKE_ENCRYPT = 13866
ERROR_IPSEC_IKE_DECRYPT = 13867
ERROR_IPSEC_IKE_POLICY_MATCH = 13868
ERROR_IPSEC_IKE_UNSUPPORTED_ID = 13869
ERROR_IPSEC_IKE_INVALID_HASH = 13870
ERROR_IPSEC_IKE_INVALID_HASH_ALG = 13871
ERROR_IPSEC_IKE_INVALID_HASH_SIZE = 13872
ERROR_IPSEC_IKE_INVALID_ENCRYPT_ALG = 13873
ERROR_IPSEC_IKE_INVALID_AUTH_ALG = 13874
ERROR_IPSEC_IKE_INVALID_SIG = 13875
ERROR_IPSEC_IKE_LOAD_FAILED = 13876
ERROR_IPSEC_IKE_RPC_DELETE = 13877
ERROR_IPSEC_IKE_BENIGN_REINIT = 13878
ERROR_IPSEC_IKE_INVALID_RESPONDER_LIFETIME_NOTIFY = 13879
ERROR_IPSEC_IKE_INVALID_MAJOR_VERSION = 13880
ERROR_IPSEC_IKE_INVALID_CERT_KEYLEN = 13881
ERROR_IPSEC_IKE_MM_LIMIT = 13882
ERROR_IPSEC_IKE_NEGOTIATION_DISABLED = 13883
ERROR_IPSEC_IKE_QM_LIMIT = 13884
ERROR_IPSEC_IKE_MM_EXPIRED = 13885
ERROR_IPSEC_IKE_PEER_MM_ASSUMED_INVALID = 13886
ERROR_IPSEC_IKE_CERT_CHAIN_POLICY_MISMATCH = 13887
ERROR_IPSEC_IKE_UNEXPECTED_MESSAGE_ID = 13888
ERROR_IPSEC_IKE_INVALID_AUTH_PAYLOAD = 13889
ERROR_IPSEC_IKE_DOS_COOKIE_SENT = 13890
ERROR_IPSEC_IKE_SHUTTING_DOWN = 13891
ERROR_IPSEC_IKE_CGA_AUTH_FAILED = 13892
ERROR_IPSEC_IKE_PROCESS_ERR_NATOA = 13893
ERROR_IPSEC_IKE_INVALID_MM_FOR_QM = 13894
ERROR_IPSEC_IKE_QM_EXPIRED = 13895
ERROR_IPSEC_IKE_TOO_MANY_FILTERS = 13896
ERROR_IPSEC_IKE_NEG_STATUS_END = 13897
ERROR_IPSEC_IKE_KILL_DUMMY_NAP_TUNNEL = 13898
ERROR_IPSEC_IKE_INNER_IP_ASSIGNMENT_FAILURE = 13899
ERROR_IPSEC_IKE_REQUIRE_CP_PAYLOAD_MISSING = 13900
ERROR_IPSEC_KEY_MODULE_IMPERSONATION_NEGOTIATION_PENDING = 13901
ERROR_IPSEC_IKE_COEXISTENCE_SUPPRESS = 13902
ERROR_IPSEC_IKE_RATELIMIT_DROP = 13903
ERROR_IPSEC_IKE_PEER_DOESNT_SUPPORT_MOBIKE = 13904
ERROR_IPSEC_IKE_AUTHORIZATION_FAILURE = 13905
ERROR_IPSEC_IKE_STRONG_CRED_AUTHORIZATION_FAILURE = 13906
ERROR_IPSEC_IKE_AUTHORIZATION_FAILURE_WITH_OPTIONAL_RETRY = 13907
ERROR_IPSEC_IKE_STRONG_CRED_AUTHORIZATION_AND_CERTMAP_FAILURE = 13908
ERROR_IPSEC_IKE_NEG_STATUS_EXTENDED_END = 13909
ERROR_IPSEC_BAD_SPI = 13910
ERROR_IPSEC_SA_LIFETIME_EXPIRED = 13911
ERROR_IPSEC_WRONG_SA = 13912
ERROR_IPSEC_REPLAY_CHECK_FAILED = 13913
ERROR_IPSEC_INVALID_PACKET = 13914
ERROR_IPSEC_INTEGRITY_CHECK_FAILED = 13915
ERROR_IPSEC_CLEAR_TEXT_DROP = 13916
ERROR_IPSEC_AUTH_FIREWALL_DROP = 13917
ERROR_IPSEC_THROTTLE_DROP = 13918
ERROR_IPSEC_DOSP_BLOCK = 13925
ERROR_IPSEC_DOSP_RECEIVED_MULTICAST = 13926
ERROR_IPSEC_DOSP_INVALID_PACKET = 13927
ERROR_IPSEC_DOSP_STATE_LOOKUP_FAILED = 13928
ERROR_IPSEC_DOSP_MAX_ENTRIES = 13929
ERROR_IPSEC_DOSP_KEYMOD_NOT_ALLOWED = 13930
ERROR_IPSEC_DOSP_NOT_INSTALLED = 13931
ERROR_IPSEC_DOSP_MAX_PER_IP_RATELIMIT_QUEUES = 13932
ERROR_SXS_SECTION_NOT_FOUND = 14000
ERROR_SXS_CANT_GEN_ACTCTX = 14001
ERROR_SXS_INVALID_ACTCTXDATA_FORMAT = 14002
ERROR_SXS_ASSEMBLY_NOT_FOUND = 14003
ERROR_SXS_MANIFEST_FORMAT_ERROR = 14004
ERROR_SXS_MANIFEST_PARSE_ERROR = 14005
ERROR_SXS_ACTIVATION_CONTEXT_DISABLED = 14006
ERROR_SXS_KEY_NOT_FOUND = 14007
ERROR_SXS_VERSION_CONFLICT = 14008
ERROR_SXS_WRONG_SECTION_TYPE = 14009
ERROR_SXS_THREAD_QUERIES_DISABLED = 14010
ERROR_SXS_PROCESS_DEFAULT_ALREADY_SET = 14011
ERROR_SXS_UNKNOWN_ENCODING_GROUP = 14012
ERROR_SXS_UNKNOWN_ENCODING = 14013
ERROR_SXS_INVALID_XML_NAMESPACE_URI = 14014
ERROR_SXS_ROOT_MANIFEST_DEPENDENCY_NOT_INSTALLED = 14015
ERROR_SXS_LEAF_MANIFEST_DEPENDENCY_NOT_INSTALLED = 14016
ERROR_SXS_INVALID_ASSEMBLY_IDENTITY_ATTRIBUTE = 14017
ERROR_SXS_MANIFEST_MISSING_REQUIRED_DEFAULT_NAMESPACE = 14018
ERROR_SXS_MANIFEST_INVALID_REQUIRED_DEFAULT_NAMESPACE = 14019
ERROR_SXS_PRIVATE_MANIFEST_CROSS_PATH_WITH_REPARSE_POINT = 14020
ERROR_SXS_DUPLICATE_DLL_NAME = 14021
ERROR_SXS_DUPLICATE_WINDOWCLASS_NAME = 14022
ERROR_SXS_DUPLICATE_CLSID = 14023
ERROR_SXS_DUPLICATE_IID = 14024
ERROR_SXS_DUPLICATE_TLBID = 14025
ERROR_SXS_DUPLICATE_PROGID = 14026
ERROR_SXS_DUPLICATE_ASSEMBLY_NAME = 14027
ERROR_SXS_FILE_HASH_MISMATCH = 14028
ERROR_SXS_POLICY_PARSE_ERROR = 14029
ERROR_SXS_XML_E_MISSINGQUOTE = 14030
ERROR_SXS_XML_E_COMMENTSYNTAX = 14031
ERROR_SXS_XML_E_BADSTARTNAMECHAR = 14032
ERROR_SXS_XML_E_BADNAMECHAR = 14033
ERROR_SXS_XML_E_BADCHARINSTRING = 14034
ERROR_SXS_XML_E_XMLDECLSYNTAX = 14035
ERROR_SXS_XML_E_BADCHARDATA = 14036
ERROR_SXS_XML_E_MISSINGWHITESPACE = 14037
ERROR_SXS_XML_E_EXPECTINGTAGEND = 14038
ERROR_SXS_XML_E_MISSINGSEMICOLON = 14039
ERROR_SXS_XML_E_UNBALANCEDPAREN = 14040
ERROR_SXS_XML_E_INTERNALERROR = 14041
ERROR_SXS_XML_E_UNEXPECTED_WHITESPACE = 14042
ERROR_SXS_XML_E_INCOMPLETE_ENCODING = 14043
ERROR_SXS_XML_E_MISSING_PAREN = 14044
ERROR_SXS_XML_E_EXPECTINGCLOSEQUOTE = 14045
ERROR_SXS_XML_E_MULTIPLE_COLONS = 14046
ERROR_SXS_XML_E_INVALID_DECIMAL = 14047
ERROR_SXS_XML_E_INVALID_HEXIDECIMAL = 14048
ERROR_SXS_XML_E_INVALID_UNICODE = 14049
ERROR_SXS_XML_E_WHITESPACEORQUESTIONMARK = 14050
ERROR_SXS_XML_E_UNEXPECTEDENDTAG = 14051
ERROR_SXS_XML_E_UNCLOSEDTAG = 14052
ERROR_SXS_XML_E_DUPLICATEATTRIBUTE = 14053
ERROR_SXS_XML_E_MULTIPLEROOTS = 14054
ERROR_SXS_XML_E_INVALIDATROOTLEVEL = 14055
ERROR_SXS_XML_E_BADXMLDECL = 14056
ERROR_SXS_XML_E_MISSINGROOT = 14057
ERROR_SXS_XML_E_UNEXPECTEDEOF = 14058
ERROR_SXS_XML_E_BADPEREFINSUBSET = 14059
ERROR_SXS_XML_E_UNCLOSEDSTARTTAG = 14060
ERROR_SXS_XML_E_UNCLOSEDENDTAG = 14061
ERROR_SXS_XML_E_UNCLOSEDSTRING = 14062
ERROR_SXS_XML_E_UNCLOSEDCOMMENT = 14063
ERROR_SXS_XML_E_UNCLOSEDDECL = 14064
ERROR_SXS_XML_E_UNCLOSEDCDATA = 14065
ERROR_SXS_XML_E_RESERVEDNAMESPACE = 14066
ERROR_SXS_XML_E_INVALIDENCODING = 14067
ERROR_SXS_XML_E_INVALIDSWITCH = 14068
ERROR_SXS_XML_E_BADXMLCASE = 14069
ERROR_SXS_XML_E_INVALID_STANDALONE = 14070
ERROR_SXS_XML_E_UNEXPECTED_STANDALONE = 14071
ERROR_SXS_XML_E_INVALID_VERSION = 14072
ERROR_SXS_XML_E_MISSINGEQUALS = 14073
ERROR_SXS_PROTECTION_RECOVERY_FAILED = 14074
ERROR_SXS_PROTECTION_PUBLIC_KEY_TOO_SHORT = 14075
ERROR_SXS_PROTECTION_CATALOG_NOT_VALID = 14076
ERROR_SXS_UNTRANSLATABLE_HRESULT = 14077
ERROR_SXS_PROTECTION_CATALOG_FILE_MISSING = 14078
ERROR_SXS_MISSING_ASSEMBLY_IDENTITY_ATTRIBUTE = 14079
ERROR_SXS_INVALID_ASSEMBLY_IDENTITY_ATTRIBUTE_NAME = 14080
ERROR_SXS_ASSEMBLY_MISSING = 14081
ERROR_SXS_CORRUPT_ACTIVATION_STACK = 14082
ERROR_SXS_CORRUPTION = 14083
ERROR_SXS_EARLY_DEACTIVATION = 14084
ERROR_SXS_INVALID_DEACTIVATION = 14085
ERROR_SXS_MULTIPLE_DEACTIVATION = 14086
ERROR_SXS_PROCESS_TERMINATION_REQUESTED = 14087
ERROR_SXS_RELEASE_ACTIVATION_CONTEXT = 14088
ERROR_SXS_SYSTEM_DEFAULT_ACTIVATION_CONTEXT_EMPTY = 14089
ERROR_SXS_INVALID_IDENTITY_ATTRIBUTE_VALUE = 14090
ERROR_SXS_INVALID_IDENTITY_ATTRIBUTE_NAME = 14091
ERROR_SXS_IDENTITY_DUPLICATE_ATTRIBUTE = 14092
ERROR_SXS_IDENTITY_PARSE_ERROR = 14093
ERROR_MALFORMED_SUBSTITUTION_STRING = 14094
ERROR_SXS_INCORRECT_PUBLIC_KEY_TOKEN = 14095
ERROR_UNMAPPED_SUBSTITUTION_STRING = 14096
ERROR_SXS_ASSEMBLY_NOT_LOCKED = 14097
ERROR_SXS_COMPONENT_STORE_CORRUPT = 14098
ERROR_ADVANCED_INSTALLER_FAILED = 14099
ERROR_XML_ENCODING_MISMATCH = 14100
ERROR_SXS_MANIFEST_IDENTITY_SAME_BUT_CONTENTS_DIFFERENT = 14101
ERROR_SXS_IDENTITIES_DIFFERENT = 14102
ERROR_SXS_ASSEMBLY_IS_NOT_A_DEPLOYMENT = 14103
ERROR_SXS_FILE_NOT_PART_OF_ASSEMBLY = 14104
ERROR_SXS_MANIFEST_TOO_BIG = 14105
ERROR_SXS_SETTING_NOT_REGISTERED = 14106
ERROR_SXS_TRANSACTION_CLOSURE_INCOMPLETE = 14107
ERROR_SMI_PRIMITIVE_INSTALLER_FAILED = 14108
ERROR_GENERIC_COMMAND_FAILED = 14109
ERROR_SXS_FILE_HASH_MISSING = 14110
ERROR_SXS_DUPLICATE_ACTIVATABLE_CLASS = 14111
ERROR_EVT_INVALID_CHANNEL_PATH = 15000
ERROR_EVT_INVALID_QUERY = 15001
ERROR_EVT_PUBLISHER_METADATA_NOT_FOUND = 15002
ERROR_EVT_EVENT_TEMPLATE_NOT_FOUND = 15003
ERROR_EVT_INVALID_PUBLISHER_NAME = 15004
ERROR_EVT_INVALID_EVENT_DATA = 15005
ERROR_EVT_CHANNEL_NOT_FOUND = 15007
ERROR_EVT_MALFORMED_XML_TEXT = 15008
ERROR_EVT_SUBSCRIPTION_TO_DIRECT_CHANNEL = 15009
ERROR_EVT_CONFIGURATION_ERROR = 15010
ERROR_EVT_QUERY_RESULT_STALE = 15011
ERROR_EVT_QUERY_RESULT_INVALID_POSITION = 15012
ERROR_EVT_NON_VALIDATING_MSXML = 15013
ERROR_EVT_FILTER_ALREADYSCOPED = 15014
ERROR_EVT_FILTER_NOTELTSET = 15015
ERROR_EVT_FILTER_INVARG = 15016
ERROR_EVT_FILTER_INVTEST = 15017
ERROR_EVT_FILTER_INVTYPE = 15018
ERROR_EVT_FILTER_PARSEERR = 15019
ERROR_EVT_FILTER_UNSUPPORTEDOP = 15020
ERROR_EVT_FILTER_UNEXPECTEDTOKEN = 15021
ERROR_EVT_INVALID_OPERATION_OVER_ENABLED_DIRECT_CHANNEL = 15022
ERROR_EVT_INVALID_CHANNEL_PROPERTY_VALUE = 15023
ERROR_EVT_INVALID_PUBLISHER_PROPERTY_VALUE = 15024
ERROR_EVT_CHANNEL_CANNOT_ACTIVATE = 15025
ERROR_EVT_FILTER_TOO_COMPLEX = 15026
ERROR_EVT_MESSAGE_NOT_FOUND = 15027
ERROR_EVT_MESSAGE_ID_NOT_FOUND = 15028
ERROR_EVT_UNRESOLVED_VALUE_INSERT = 15029
ERROR_EVT_UNRESOLVED_PARAMETER_INSERT = 15030
ERROR_EVT_MAX_INSERTS_REACHED = 15031
ERROR_EVT_EVENT_DEFINITION_NOT_FOUND = 15032
ERROR_EVT_MESSAGE_LOCALE_NOT_FOUND = 15033
ERROR_EVT_VERSION_TOO_OLD = 15034
ERROR_EVT_VERSION_TOO_NEW = 15035
ERROR_EVT_CANNOT_OPEN_CHANNEL_OF_QUERY = 15036
ERROR_EVT_PUBLISHER_DISABLED = 15037
ERROR_EVT_FILTER_OUT_OF_RANGE = 15038
ERROR_EC_SUBSCRIPTION_CANNOT_ACTIVATE = 15080
ERROR_EC_LOG_DISABLED = 15081
ERROR_EC_CIRCULAR_FORWARDING = 15082
ERROR_EC_CREDSTORE_FULL = 15083
ERROR_EC_CRED_NOT_FOUND = 15084
ERROR_EC_NO_ACTIVE_CHANNEL = 15085
ERROR_MUI_FILE_NOT_FOUND = 15100
ERROR_MUI_INVALID_FILE = 15101
ERROR_MUI_INVALID_RC_CONFIG = 15102
ERROR_MUI_INVALID_LOCALE_NAME = 15103
ERROR_MUI_INVALID_ULTIMATEFALLBACK_NAME = 15104
ERROR_MUI_FILE_NOT_LOADED = 15105
ERROR_RESOURCE_ENUM_USER_STOP = 15106
ERROR_MUI_INTLSETTINGS_UILANG_NOT_INSTALLED = 15107
ERROR_MUI_INTLSETTINGS_INVALID_LOCALE_NAME = 15108
ERROR_MRM_RUNTIME_NO_DEFAULT_OR_NEUTRAL_RESOURCE = 15110
ERROR_MRM_INVALID_PRICONFIG = 15111
ERROR_MRM_INVALID_FILE_TYPE = 15112
ERROR_MRM_UNKNOWN_QUALIFIER = 15113
ERROR_MRM_INVALID_QUALIFIER_VALUE = 15114
ERROR_MRM_NO_CANDIDATE = 15115
ERROR_MRM_NO_MATCH_OR_DEFAULT_CANDIDATE = 15116
ERROR_MRM_RESOURCE_TYPE_MISMATCH = 15117
ERROR_MRM_DUPLICATE_MAP_NAME = 15118
ERROR_MRM_DUPLICATE_ENTRY = 15119
ERROR_MRM_INVALID_RESOURCE_IDENTIFIER = 15120
ERROR_MRM_FILEPATH_TOO_LONG = 15121
ERROR_MRM_UNSUPPORTED_DIRECTORY_TYPE = 15122
ERROR_MRM_INVALID_PRI_FILE = 15126
ERROR_MRM_NAMED_RESOURCE_NOT_FOUND = 15127
ERROR_MRM_MAP_NOT_FOUND = 15135
ERROR_MRM_UNSUPPORTED_PROFILE_TYPE = 15136
ERROR_MRM_INVALID_QUALIFIER_OPERATOR = 15137
ERROR_MRM_INDETERMINATE_QUALIFIER_VALUE = 15138
ERROR_MRM_AUTOMERGE_ENABLED = 15139
ERROR_MRM_TOO_MANY_RESOURCES = 15140
ERROR_MRM_UNSUPPORTED_FILE_TYPE_FOR_MERGE = 15141
ERROR_MRM_UNSUPPORTED_FILE_TYPE_FOR_LOAD_UNLOAD_PRI_FILE = 15142
ERROR_MRM_NO_CURRENT_VIEW_ON_THREAD = 15143
ERROR_DIFFERENT_PROFILE_RESOURCE_MANAGER_EXIST = 15144
ERROR_OPERATION_NOT_ALLOWED_FROM_SYSTEM_COMPONENT = 15145
ERROR_MRM_DIRECT_REF_TO_NON_DEFAULT_RESOURCE = 15146
ERROR_MRM_GENERATION_COUNT_MISMATCH = 15147
ERROR_PRI_MERGE_VERSION_MISMATCH = 15148
ERROR_PRI_MERGE_MISSING_SCHEMA = 15149
ERROR_PRI_MERGE_LOAD_FILE_FAILED = 15150
ERROR_PRI_MERGE_ADD_FILE_FAILED = 15151
ERROR_PRI_MERGE_WRITE_FILE_FAILED = 15152
ERROR_PRI_MERGE_MULTIPLE_PACKAGE_FAMILIES_NOT_ALLOWED = 15153
ERROR_PRI_MERGE_MULTIPLE_MAIN_PACKAGES_NOT_ALLOWED = 15154
ERROR_PRI_MERGE_BUNDLE_PACKAGES_NOT_ALLOWED = 15155
ERROR_PRI_MERGE_MAIN_PACKAGE_REQUIRED = 15156
ERROR_PRI_MERGE_RESOURCE_PACKAGE_REQUIRED = 15157
ERROR_PRI_MERGE_INVALID_FILE_NAME = 15158
ERROR_MRM_PACKAGE_NOT_FOUND = 15159
ERROR_MRM_MISSING_DEFAULT_LANGUAGE = 15160
ERROR_MRM_SCOPE_ITEM_CONFLICT = 15161
ERROR_MCA_INVALID_CAPABILITIES_STRING = 15200
ERROR_MCA_INVALID_VCP_VERSION = 15201
ERROR_MCA_MONITOR_VIOLATES_MCCS_SPECIFICATION = 15202
ERROR_MCA_MCCS_VERSION_MISMATCH = 15203
ERROR_MCA_UNSUPPORTED_MCCS_VERSION = 15204
ERROR_MCA_INTERNAL_ERROR = 15205
ERROR_MCA_INVALID_TECHNOLOGY_TYPE_RETURNED = 15206
ERROR_MCA_UNSUPPORTED_COLOR_TEMPERATURE = 15207
ERROR_AMBIGUOUS_SYSTEM_DEVICE = 15250
ERROR_SYSTEM_DEVICE_NOT_FOUND = 15299
ERROR_HASH_NOT_SUPPORTED = 15300
ERROR_HASH_NOT_PRESENT = 15301
ERROR_SECONDARY_IC_PROVIDER_NOT_REGISTERED = 15321
ERROR_GPIO_CLIENT_INFORMATION_INVALID = 15322
ERROR_GPIO_VERSION_NOT_SUPPORTED = 15323
ERROR_GPIO_INVALID_REGISTRATION_PACKET = 15324
ERROR_GPIO_OPERATION_DENIED = 15325
ERROR_GPIO_INCOMPATIBLE_CONNECT_MODE = 15326
ERROR_GPIO_INTERRUPT_ALREADY_UNMASKED = 15327
ERROR_CANNOT_SWITCH_RUNLEVEL = 15400
ERROR_INVALID_RUNLEVEL_SETTING = 15401
ERROR_RUNLEVEL_SWITCH_TIMEOUT = 15402
ERROR_RUNLEVEL_SWITCH_AGENT_TIMEOUT = 15403
ERROR_RUNLEVEL_SWITCH_IN_PROGRESS = 15404
ERROR_SERVICES_FAILED_AUTOSTART = 15405
ERROR_COM_TASK_STOP_PENDING = 15501
ERROR_INSTALL_OPEN_PACKAGE_FAILED = 15600
ERROR_INSTALL_PACKAGE_NOT_FOUND = 15601
ERROR_INSTALL_INVALID_PACKAGE = 15602
ERROR_INSTALL_RESOLVE_DEPENDENCY_FAILED = 15603
ERROR_INSTALL_OUT_OF_DISK_SPACE = 15604
ERROR_INSTALL_NETWORK_FAILURE = 15605
ERROR_INSTALL_REGISTRATION_FAILURE = 15606
ERROR_INSTALL_DEREGISTRATION_FAILURE = 15607
ERROR_INSTALL_CANCEL = 15608
ERROR_INSTALL_FAILED = 15609
ERROR_REMOVE_FAILED = 15610
ERROR_PACKAGE_ALREADY_EXISTS = 15611
ERROR_NEEDS_REMEDIATION = 15612
ERROR_INSTALL_PREREQUISITE_FAILED = 15613
ERROR_PACKAGE_REPOSITORY_CORRUPTED = 15614
ERROR_INSTALL_POLICY_FAILURE = 15615
ERROR_PACKAGE_UPDATING = 15616
ERROR_DEPLOYMENT_BLOCKED_BY_POLICY = 15617
ERROR_PACKAGES_IN_USE = 15618
ERROR_RECOVERY_FILE_CORRUPT = 15619
ERROR_INVALID_STAGED_SIGNATURE = 15620
ERROR_DELETING_EXISTING_APPLICATIONDATA_STORE_FAILED = 15621
ERROR_INSTALL_PACKAGE_DOWNGRADE = 15622
ERROR_SYSTEM_NEEDS_REMEDIATION = 15623
ERROR_APPX_INTEGRITY_FAILURE_CLR_NGEN = 15624
ERROR_RESILIENCY_FILE_CORRUPT = 15625
ERROR_INSTALL_FIREWALL_SERVICE_NOT_RUNNING = 15626
ERROR_PACKAGE_MOVE_FAILED = 15627
ERROR_INSTALL_VOLUME_NOT_EMPTY = 15628
ERROR_INSTALL_VOLUME_OFFLINE = 15629
ERROR_INSTALL_VOLUME_CORRUPT = 15630
ERROR_NEEDS_REGISTRATION = 15631
ERROR_INSTALL_WRONG_PROCESSOR_ARCHITECTURE = 15632
ERROR_DEV_SIDELOAD_LIMIT_EXCEEDED = 15633
ERROR_INSTALL_OPTIONAL_PACKAGE_REQUIRES_MAIN_PACKAGE = 15634
ERROR_PACKAGE_NOT_SUPPORTED_ON_FILESYSTEM = 15635
ERROR_PACKAGE_MOVE_BLOCKED_BY_STREAMING = 15636
ERROR_INSTALL_OPTIONAL_PACKAGE_APPLICATIONID_NOT_UNIQUE = 15637
ERROR_PACKAGE_STAGING_ONHOLD = 15638
ERROR_INSTALL_INVALID_RELATED_SET_UPDATE = 15639
ERROR_INSTALL_OPTIONAL_PACKAGE_REQUIRES_MAIN_PACKAGE_FULLTRUST_CAPABILITY = 15640
ERROR_DEPLOYMENT_BLOCKED_BY_USER_LOG_OFF = 15641
ERROR_PROVISION_OPTIONAL_PACKAGE_REQUIRES_MAIN_PACKAGE_PROVISIONED = 15642
ERROR_PACKAGES_REPUTATION_CHECK_FAILED = 15643
ERROR_PACKAGES_REPUTATION_CHECK_TIMEDOUT = 15644
ERROR_DEPLOYMENT_OPTION_NOT_SUPPORTED = 15645
ERROR_APPINSTALLER_ACTIVATION_BLOCKED = 15646
ERROR_REGISTRATION_FROM_REMOTE_DRIVE_NOT_SUPPORTED = 15647
ERROR_APPX_RAW_DATA_WRITE_FAILED = 15648
ERROR_DEPLOYMENT_BLOCKED_BY_VOLUME_POLICY_PACKAGE = 15649
ERROR_DEPLOYMENT_BLOCKED_BY_VOLUME_POLICY_MACHINE = 15650
ERROR_DEPLOYMENT_BLOCKED_BY_PROFILE_POLICY = 15651
ERROR_DEPLOYMENT_FAILED_CONFLICTING_MUTABLE_PACKAGE_DIRECTORY = 15652
ERROR_SINGLETON_RESOURCE_INSTALLED_IN_ACTIVE_USER = 15653
ERROR_DIFFERENT_VERSION_OF_PACKAGED_SERVICE_INSTALLED = 15654
ERROR_SERVICE_EXISTS_AS_NON_PACKAGED_SERVICE = 15655
ERROR_PACKAGED_SERVICE_REQUIRES_ADMIN_PRIVILEGES = 15656
ERROR_REDIRECTION_TO_DEFAULT_ACCOUNT_NOT_ALLOWED = 15657
ERROR_PACKAGE_LACKS_CAPABILITY_TO_DEPLOY_ON_HOST = 15658
ERROR_UNSIGNED_PACKAGE_INVALID_CONTENT = 15659
ERROR_UNSIGNED_PACKAGE_INVALID_PUBLISHER_NAMESPACE = 15660
ERROR_SIGNED_PACKAGE_INVALID_PUBLISHER_NAMESPACE = 15661
ERROR_PACKAGE_EXTERNAL_LOCATION_NOT_ALLOWED = 15662
ERROR_INSTALL_FULLTRUST_HOSTRUNTIME_REQUIRES_MAIN_PACKAGE_FULLTRUST_CAPABILITY = 15663
ERROR_PACKAGE_LACKS_CAPABILITY_FOR_MANDATORY_STARTUPTASKS = 15664
ERROR_INSTALL_RESOLVE_HOSTRUNTIME_DEPENDENCY_FAILED = 15665
ERROR_MACHINE_SCOPE_NOT_ALLOWED = 15666
ERROR_CLASSIC_COMPAT_MODE_NOT_ALLOWED = 15667
ERROR_STAGEFROMUPDATEAGENT_PACKAGE_NOT_APPLICABLE = 15668
ERROR_PACKAGE_NOT_REGISTERED_FOR_USER = 15669
ERROR_PACKAGE_NAME_MISMATCH = 15670
ERROR_APPINSTALLER_URI_IN_USE = 15671
ERROR_APPINSTALLER_IS_MANAGED_BY_SYSTEM = 15672
APPMODEL_ERROR_NO_PACKAGE = 15700
APPMODEL_ERROR_PACKAGE_RUNTIME_CORRUPT = 15701
APPMODEL_ERROR_PACKAGE_IDENTITY_CORRUPT = 15702
APPMODEL_ERROR_NO_APPLICATION = 15703
APPMODEL_ERROR_DYNAMIC_PROPERTY_READ_FAILED = 15704
APPMODEL_ERROR_DYNAMIC_PROPERTY_INVALID = 15705
APPMODEL_ERROR_PACKAGE_NOT_AVAILABLE = 15706
APPMODEL_ERROR_NO_MUTABLE_DIRECTORY = 15707
ERROR_STATE_LOAD_STORE_FAILED = 15800
ERROR_STATE_GET_VERSION_FAILED = 15801
ERROR_STATE_SET_VERSION_FAILED = 15802
ERROR_STATE_STRUCTURED_RESET_FAILED = 15803
ERROR_STATE_OPEN_CONTAINER_FAILED = 15804
ERROR_STATE_CREATE_CONTAINER_FAILED = 15805
ERROR_STATE_DELETE_CONTAINER_FAILED = 15806
ERROR_STATE_READ_SETTING_FAILED = 15807
ERROR_STATE_WRITE_SETTING_FAILED = 15808
ERROR_STATE_DELETE_SETTING_FAILED = 15809
ERROR_STATE_QUERY_SETTING_FAILED = 15810
ERROR_STATE_READ_COMPOSITE_SETTING_FAILED = 15811
ERROR_STATE_WRITE_COMPOSITE_SETTING_FAILED = 15812
ERROR_STATE_ENUMERATE_CONTAINER_FAILED = 15813
ERROR_STATE_ENUMERATE_SETTINGS_FAILED = 15814
ERROR_STATE_COMPOSITE_SETTING_VALUE_SIZE_LIMIT_EXCEEDED = 15815
ERROR_STATE_SETTING_VALUE_SIZE_LIMIT_EXCEEDED = 15816
ERROR_STATE_SETTING_NAME_SIZE_LIMIT_EXCEEDED = 15817
ERROR_STATE_CONTAINER_NAME_SIZE_LIMIT_EXCEEDED = 15818
ERROR_API_UNAVAILABLE = 15841
STORE_ERROR_UNLICENSED = 15861
STORE_ERROR_UNLICENSED_USER = 15862
STORE_ERROR_PENDING_COM_TRANSACTION = 15863
STORE_ERROR_LICENSE_REVOKED = 15864
SEVERITY_SUCCESS = 0
SEVERITY_ERROR = 1


def SUCCEEDED(hr):
    return (hr) >= 0


def FAILED(hr):
    return (hr) < 0


def HRESULT_CODE(hr):
    return (hr) & 0xFFFF


def SCODE_CODE(sc):
    return (sc) & 0xFFFF


def HRESULT_FACILITY(hr):
    return ((hr) >> 16) & 0x1FFF


def SCODE_FACILITY(sc):
    return ((sc) >> 16) & 0x1FFF


def HRESULT_SEVERITY(hr):
    return ((hr) >> 31) & 0x1


def SCODE_SEVERITY(sc):
    return ((sc) >> 31) & 0x1


FACILITY_NT_BIT = 0x10000000


def HRESULT_FROM_WIN32(x):
    return __HRESULT_FROM_WIN32(x)


def HRESULT_FROM_NT(x):
    return (x) | FACILITY_NT_BIT


def GetScode(hr):
    return hr


def ResultFromScode(sc):
    return sc


NOERROR = 0
E_UNEXPECTED = -**********
E_NOTIMPL = -**********
E_OUTOFMEMORY = -**********
E_INVALIDARG = -**********
E_NOINTERFACE = -**********
E_POINTER = -**********
E_HANDLE = -**********
E_ABORT = -**********
E_FAIL = -**********
E_ACCESSDENIED = -**********
E_PENDING = -**********
E_BOUNDS = -2147483637
E_CHANGED_STATE = -2147483636
E_ILLEGAL_STATE_CHANGE = -2147483635
E_ILLEGAL_METHOD_CALL = -2147483634
RO_E_METADATA_NAME_NOT_FOUND = -2147483633
RO_E_METADATA_NAME_IS_NAMESPACE = -2147483632
RO_E_METADATA_INVALID_TYPE_FORMAT = -2147483631
RO_E_INVALID_METADATA_FILE = -2147483630
RO_E_CLOSED = -2147483629
RO_E_EXCLUSIVE_WRITE = -2147483628
RO_E_CHANGE_NOTIFICATION_IN_PROGRESS = -2147483627
RO_E_ERROR_STRING_NOT_FOUND = -2147483626
E_STRING_NOT_NULL_TERMINATED = -2147483625
E_ILLEGAL_DELEGATE_ASSIGNMENT = -2147483624
E_ASYNC_OPERATION_NOT_STARTED = -2147483623
E_APPLICATION_EXITING = -2147483622
E_APPLICATION_VIEW_EXITING = -2147483621
RO_E_MUST_BE_AGILE = -2147483620
RO_E_UNSUPPORTED_FROM_MTA = -2147483619
RO_E_COMMITTED = -2147483618
RO_E_BLOCKED_CROSS_ASTA_CALL = -2147483617
RO_E_CANNOT_ACTIVATE_FULL_TRUST_SERVER = -2147483616
RO_E_CANNOT_ACTIVATE_UNIVERSAL_APPLICATION_SERVER = -2147483615
CO_E_INIT_TLS = -2147467258
CO_E_INIT_SHARED_ALLOCATOR = -2147467257
CO_E_INIT_MEMORY_ALLOCATOR = -2147467256
CO_E_INIT_CLASS_CACHE = -2147467255
CO_E_INIT_RPC_CHANNEL = -2147467254
CO_E_INIT_TLS_SET_CHANNEL_CONTROL = -2147467253
CO_E_INIT_TLS_CHANNEL_CONTROL = -2147467252
CO_E_INIT_UNACCEPTED_USER_ALLOCATOR = -2147467251
CO_E_INIT_SCM_MUTEX_EXISTS = -2147467250
CO_E_INIT_SCM_FILE_MAPPING_EXISTS = -2147467249
CO_E_INIT_SCM_MAP_VIEW_OF_FILE = -2147467248
CO_E_INIT_SCM_EXEC_FAILURE = -2147467247
CO_E_INIT_ONLY_SINGLE_THREADED = -2147467246
CO_E_CANT_REMOTE = -2147467245
CO_E_BAD_SERVER_NAME = -2147467244
CO_E_WRONG_SERVER_IDENTITY = -2147467243
CO_E_OLE1DDE_DISABLED = -2147467242
CO_E_RUNAS_SYNTAX = -2147467241
CO_E_CREATEPROCESS_FAILURE = -2147467240
CO_E_RUNAS_CREATEPROCESS_FAILURE = -2147467239
CO_E_RUNAS_LOGON_FAILURE = -2147467238
CO_E_LAUNCH_PERMSSION_DENIED = -2147467237
CO_E_START_SERVICE_FAILURE = -2147467236
CO_E_REMOTE_COMMUNICATION_FAILURE = -2147467235
CO_E_SERVER_START_TIMEOUT = -2147467234
CO_E_CLSREG_INCONSISTENT = -2147467233
CO_E_IIDREG_INCONSISTENT = -2147467232
CO_E_NOT_SUPPORTED = -2147467231
CO_E_RELOAD_DLL = -2147467230
CO_E_MSI_ERROR = -2147467229
CO_E_ATTEMPT_TO_CREATE_OUTSIDE_CLIENT_CONTEXT = -2147467228
CO_E_SERVER_PAUSED = -2147467227
CO_E_SERVER_NOT_PAUSED = -2147467226
CO_E_CLASS_DISABLED = -2147467225
CO_E_CLRNOTAVAILABLE = -2147467224
CO_E_ASYNC_WORK_REJECTED = -2147467223
CO_E_SERVER_INIT_TIMEOUT = -2147467222
CO_E_NO_SECCTX_IN_ACTIVATE = -2147467221
CO_E_TRACKER_CONFIG = -2147467216
CO_E_THREADPOOL_CONFIG = -2147467215
CO_E_SXS_CONFIG = -2147467214
CO_E_MALFORMED_SPN = -2147467213
CO_E_UNREVOKED_REGISTRATION_ON_APARTMENT_SHUTDOWN = -2147467212
CO_E_PREMATURE_STUB_RUNDOWN = -2147467211
S_OK = 0
S_FALSE = 1
OLE_E_FIRST = -2147221504
OLE_E_LAST = -2147221249
OLE_S_FIRST = 0x00040000
OLE_S_LAST = 0x000400FF
OLE_E_OLEVERB = -2147221504
OLE_E_ADVF = -2147221503
OLE_E_ENUM_NOMORE = -2147221502
OLE_E_ADVISENOTSUPPORTED = -2147221501
OLE_E_NOCONNECTION = -2147221500
OLE_E_NOTRUNNING = -2147221499
OLE_E_NOCACHE = -2147221498
OLE_E_BLANK = -2147221497
OLE_E_CLASSDIFF = -2147221496
OLE_E_CANT_GETMONIKER = -2147221495
OLE_E_CANT_BINDTOSOURCE = -2147221494
OLE_E_STATIC = -2147221493
OLE_E_PROMPTSAVECANCELLED = -2147221492
OLE_E_INVALIDRECT = -2147221491
OLE_E_WRONGCOMPOBJ = -2147221490
OLE_E_INVALIDHWND = -2147221489
OLE_E_NOT_INPLACEACTIVE = -2147221488
OLE_E_CANTCONVERT = -2147221487
OLE_E_NOSTORAGE = -2147221486
DV_E_FORMATETC = -2147221404
DV_E_DVTARGETDEVICE = -2147221403
DV_E_STGMEDIUM = -2147221402
DV_E_STATDATA = -2147221401
DV_E_LINDEX = -2147221400
DV_E_TYMED = -2147221399
DV_E_CLIPFORMAT = -2147221398
DV_E_DVASPECT = -2147221397
DV_E_DVTARGETDEVICE_SIZE = -2147221396
DV_E_NOIVIEWOBJECT = -2147221395
DRAGDROP_E_FIRST = -**********
DRAGDROP_E_LAST = -**********
DRAGDROP_S_FIRST = 0x00040100
DRAGDROP_S_LAST = 0x0004010F
DRAGDROP_E_NOTREGISTERED = -**********
DRAGDROP_E_ALREADYREGISTERED = -**********
DRAGDROP_E_INVALIDHWND = -**********
DRAGDROP_E_CONCURRENT_DRAG_ATTEMPTED = -**********
CLASSFACTORY_E_FIRST = -**********
CLASSFACTORY_E_LAST = -**********
CLASSFACTORY_S_FIRST = 0x00040110
CLASSFACTORY_S_LAST = 0x0004011F
CLASS_E_NOAGGREGATION = -**********
CLASS_E_CLASSNOTAVAILABLE = -**********
CLASS_E_NOTLICENSED = -**********
MARSHAL_E_FIRST = -**********
MARSHAL_E_LAST = -**********
MARSHAL_S_FIRST = 0x00040120
MARSHAL_S_LAST = 0x0004012F
DATA_E_FIRST = -**********
DATA_E_LAST = -**********
DATA_S_FIRST = 0x00040130
DATA_S_LAST = 0x0004013F
VIEW_E_FIRST = -**********
VIEW_E_LAST = -**********
VIEW_S_FIRST = 0x00040140
VIEW_S_LAST = 0x0004014F
VIEW_E_DRAW = -**********
REGDB_E_FIRST = -**********
REGDB_E_LAST = -**********
REGDB_S_FIRST = 0x00040150
REGDB_S_LAST = 0x0004015F
REGDB_E_READREGDB = -**********
REGDB_E_WRITEREGDB = -**********
REGDB_E_KEYMISSING = -**********
REGDB_E_INVALIDVALUE = -**********
REGDB_E_CLASSNOTREG = -**********
REGDB_E_IIDNOTREG = -**********
REGDB_E_BADTHREADINGMODEL = -**********
REGDB_E_PACKAGEPOLICYVIOLATION = -**********
CAT_E_FIRST = -**********
CAT_E_LAST = -**********
CAT_E_CATIDNOEXIST = -**********
CAT_E_NODESCRIPTION = -**********
CS_E_FIRST = -**********
CS_E_LAST = -**********
CS_E_PACKAGE_NOTFOUND = -**********
CS_E_NOT_DELETABLE = -**********
CS_E_CLASS_NOTFOUND = -2147221146
CS_E_INVALID_VERSION = -2147221145
CS_E_NO_CLASSSTORE = -2147221144
CS_E_OBJECT_NOTFOUND = -2147221143
CS_E_OBJECT_ALREADY_EXISTS = -2147221142
CS_E_INVALID_PATH = -2147221141
CS_E_NETWORK_ERROR = -2147221140
CS_E_ADMIN_LIMIT_EXCEEDED = -2147221139
CS_E_SCHEMA_MISMATCH = -2147221138
CS_E_INTERNAL_ERROR = -**********
CACHE_E_FIRST = -2147221136
CACHE_E_LAST = -2147221121
CACHE_S_FIRST = 0x00040170
CACHE_S_LAST = 0x0004017F
CACHE_E_NOCACHE_UPDATED = -2147221136
OLEOBJ_E_FIRST = -2147221120
OLEOBJ_E_LAST = -2147221105
OLEOBJ_S_FIRST = 0x00040180
OLEOBJ_S_LAST = 0x0004018F
OLEOBJ_E_NOVERBS = -2147221120
OLEOBJ_E_INVALIDVERB = -2147221119
CLIENTSITE_E_FIRST = -2147221104
CLIENTSITE_E_LAST = -2147221089
CLIENTSITE_S_FIRST = 0x00040190
CLIENTSITE_S_LAST = 0x0004019F
INPLACE_E_NOTUNDOABLE = -2147221088
INPLACE_E_NOTOOLSPACE = -2147221087
INPLACE_E_FIRST = -2147221088
INPLACE_E_LAST = -2147221073
INPLACE_S_FIRST = 0x000401A0
INPLACE_S_LAST = 0x000401AF
ENUM_E_FIRST = -2147221072
ENUM_E_LAST = -2147221057
ENUM_S_FIRST = 0x000401B0
ENUM_S_LAST = 0x000401BF
CONVERT10_E_FIRST = -2147221056
CONVERT10_E_LAST = -2147221041
CONVERT10_S_FIRST = 0x000401C0
CONVERT10_S_LAST = 0x000401CF
CONVERT10_E_OLESTREAM_GET = -2147221056
CONVERT10_E_OLESTREAM_PUT = -2147221055
CONVERT10_E_OLESTREAM_FMT = -2147221054
CONVERT10_E_OLESTREAM_BITMAP_TO_DIB = -2147221053
CONVERT10_E_STG_FMT = -2147221052
CONVERT10_E_STG_NO_STD_STREAM = -2147221051
CONVERT10_E_STG_DIB_TO_BITMAP = -2147221050
CONVERT10_E_OLELINK_DISABLED = -2147221049
CLIPBRD_E_FIRST = -2147221040
CLIPBRD_E_LAST = -2147221025
CLIPBRD_S_FIRST = 0x000401D0
CLIPBRD_S_LAST = 0x000401DF
CLIPBRD_E_CANT_OPEN = -2147221040
CLIPBRD_E_CANT_EMPTY = -2147221039
CLIPBRD_E_CANT_SET = -2147221038
CLIPBRD_E_BAD_DATA = -2147221037
CLIPBRD_E_CANT_CLOSE = -2147221036
MK_E_FIRST = -2147221024
MK_E_LAST = -2147221009
MK_S_FIRST = 0x000401E0
MK_S_LAST = 0x000401EF
MK_E_CONNECTMANUALLY = -2147221024
MK_E_EXCEEDEDDEADLINE = -2147221023
MK_E_NEEDGENERIC = -2147221022
MK_E_UNAVAILABLE = -2147221021
MK_E_SYNTAX = -2147221020
MK_E_NOOBJECT = -2147221019
MK_E_INVALIDEXTENSION = -2147221018
MK_E_INTERMEDIATEINTERFACENOTSUPPORTED = -2147221017
MK_E_NOTBINDABLE = -2147221016
MK_E_NOTBOUND = -2147221015
MK_E_CANTOPENFILE = -2147221014
MK_E_MUSTBOTHERUSER = -2147221013
MK_E_NOINVERSE = -2147221012
MK_E_NOSTORAGE = -2147221011
MK_E_NOPREFIX = -2147221010
MK_E_ENUMERATION_FAILED = -2147221009
CO_E_FIRST = -2147221008
CO_E_LAST = -2147220993
CO_S_FIRST = 0x000401F0
CO_S_LAST = 0x000401FF
CO_E_NOTINITIALIZED = -2147221008
CO_E_ALREADYINITIALIZED = -2147221007
CO_E_CANTDETERMINECLASS = -2147221006
CO_E_CLASSSTRING = -2147221005
CO_E_IIDSTRING = -2147221004
CO_E_APPNOTFOUND = -2147221003
CO_E_APPSINGLEUSE = -2147221002
CO_E_ERRORINAPP = -2147221001
CO_E_DLLNOTFOUND = -2147221000
CO_E_ERRORINDLL = -2147220999
CO_E_WRONGOSFORAPP = -2147220998
CO_E_OBJNOTREG = -2147220997
CO_E_OBJISREG = -2147220996
CO_E_OBJNOTCONNECTED = -2147220995
CO_E_APPDIDNTREG = -2147220994
CO_E_RELEASED = -2147220993
EVENT_E_FIRST = -2147220992
EVENT_E_LAST = -2147220961
EVENT_S_FIRST = 0x00040200
EVENT_S_LAST = 0x0004021F
EVENT_S_SOME_SUBSCRIBERS_FAILED = 0x00040200
EVENT_E_ALL_SUBSCRIBERS_FAILED = -2147220991
EVENT_S_NOSUBSCRIBERS = 0x00040202
EVENT_E_QUERYSYNTAX = -2147220989
EVENT_E_QUERYFIELD = -2147220988
EVENT_E_INTERNALEXCEPTION = -2147220987
EVENT_E_INTERNALERROR = -2147220986
EVENT_E_INVALID_PER_USER_SID = -2147220985
EVENT_E_USER_EXCEPTION = -2147220984
EVENT_E_TOO_MANY_METHODS = -2147220983
EVENT_E_MISSING_EVENTCLASS = -2147220982
EVENT_E_NOT_ALL_REMOVED = -2147220981
EVENT_E_COMPLUS_NOT_INSTALLED = -2147220980
EVENT_E_CANT_MODIFY_OR_DELETE_UNCONFIGURED_OBJECT = -2147220979
EVENT_E_CANT_MODIFY_OR_DELETE_CONFIGURED_OBJECT = -2147220978
EVENT_E_INVALID_EVENT_CLASS_PARTITION = -2147220977
EVENT_E_PER_USER_SID_NOT_LOGGED_ON = -2147220976
TPC_E_INVALID_PROPERTY = -2147220927
TPC_E_NO_DEFAULT_TABLET = -2147220974
TPC_E_UNKNOWN_PROPERTY = -2147220965
TPC_E_INVALID_INPUT_RECT = -2147220967
TPC_E_INVALID_STROKE = -2147220958
TPC_E_INITIALIZE_FAIL = -2147220957
TPC_E_NOT_RELEVANT = -2147220942
TPC_E_INVALID_PACKET_DESCRIPTION = -2147220941
TPC_E_RECOGNIZER_NOT_REGISTERED = -2147220939
TPC_E_INVALID_RIGHTS = -2147220938
TPC_E_OUT_OF_ORDER_CALL = -2147220937
TPC_E_QUEUE_FULL = -2147220936
TPC_E_INVALID_CONFIGURATION = -2147220935
TPC_E_INVALID_DATA_FROM_RECOGNIZER = -2147220934
TPC_S_TRUNCATED = 0x00040252
TPC_S_INTERRUPTED = 0x00040253
TPC_S_NO_DATA_TO_PROCESS = 0x00040254
XACT_E_FIRST = -2147168256
XACT_E_LAST = -2147168213
XACT_S_FIRST = 0x0004D000
XACT_S_LAST = 0x0004D010
XACT_E_ALREADYOTHERSINGLEPHASE = -2147168256
XACT_E_CANTRETAIN = -2147168255
XACT_E_COMMITFAILED = -2147168254
XACT_E_COMMITPREVENTED = -2147168253
XACT_E_HEURISTICABORT = -2147168252
XACT_E_HEURISTICCOMMIT = -2147168251
XACT_E_HEURISTICDAMAGE = -2147168250
XACT_E_HEURISTICDANGER = -2147168249
XACT_E_ISOLATIONLEVEL = -2147168248
XACT_E_NOASYNC = -2147168247
XACT_E_NOENLIST = -2147168246
XACT_E_NOISORETAIN = -2147168245
XACT_E_NORESOURCE = -2147168244
XACT_E_NOTCURRENT = -2147168243
XACT_E_NOTRANSACTION = -2147168242
XACT_E_NOTSUPPORTED = -2147168241
XACT_E_UNKNOWNRMGRID = -2147168240
XACT_E_WRONGSTATE = -2147168239
XACT_E_WRONGUOW = -2147168238
XACT_E_XTIONEXISTS = -2147168237
XACT_E_NOIMPORTOBJECT = -2147168236
XACT_E_INVALIDCOOKIE = -2147168235
XACT_E_INDOUBT = -2147168234
XACT_E_NOTIMEOUT = -2147168233
XACT_E_ALREADYINPROGRESS = -2147168232
XACT_E_ABORTED = -2147168231
XACT_E_LOGFULL = -2147168230
XACT_E_TMNOTAVAILABLE = -2147168229
XACT_E_CONNECTION_DOWN = -2147168228
XACT_E_CONNECTION_DENIED = -2147168227
XACT_E_REENLISTTIMEOUT = -2147168226
XACT_E_TIP_CONNECT_FAILED = -2147168225
XACT_E_TIP_PROTOCOL_ERROR = -2147168224
XACT_E_TIP_PULL_FAILED = -2147168223
XACT_E_DEST_TMNOTAVAILABLE = -2147168222
XACT_E_TIP_DISABLED = -2147168221
XACT_E_NETWORK_TX_DISABLED = -2147168220
XACT_E_PARTNER_NETWORK_TX_DISABLED = -2147168219
XACT_E_XA_TX_DISABLED = -2147168218
XACT_E_UNABLE_TO_READ_DTC_CONFIG = -2147168217
XACT_E_UNABLE_TO_LOAD_DTC_PROXY = -2147168216
XACT_E_ABORTING = -2147168215
XACT_E_PUSH_COMM_FAILURE = -2147168214
XACT_E_PULL_COMM_FAILURE = -2147168213
XACT_E_LU_TX_DISABLED = -2147168212
XACT_E_CLERKNOTFOUND = -2147168128
XACT_E_CLERKEXISTS = -2147168127
XACT_E_RECOVERYINPROGRESS = -2147168126
XACT_E_TRANSACTIONCLOSED = -2147168125
XACT_E_INVALIDLSN = -2147168124
XACT_E_REPLAYREQUEST = -2147168123
XACT_S_ASYNC = 0x0004D000
XACT_S_DEFECT = 0x0004D001
XACT_S_READONLY = 0x0004D002
XACT_S_SOMENORETAIN = 0x0004D003
XACT_S_OKINFORM = 0x0004D004
XACT_S_MADECHANGESCONTENT = 0x0004D005
XACT_S_MADECHANGESINFORM = 0x0004D006
XACT_S_ALLNORETAIN = 0x0004D007
XACT_S_ABORTING = 0x0004D008
XACT_S_SINGLEPHASE = 0x0004D009
XACT_S_LOCALLY_OK = 0x0004D00A
XACT_S_LASTRESOURCEMANAGER = 0x0004D010
CONTEXT_E_FIRST = -2147164160
CONTEXT_E_LAST = -2147164113
CONTEXT_S_FIRST = 0x0004E000
CONTEXT_S_LAST = 0x0004E02F
CONTEXT_E_ABORTED = -2147164158
CONTEXT_E_ABORTING = -2147164157
CONTEXT_E_NOCONTEXT = -2147164156
CONTEXT_E_WOULD_DEADLOCK = -2147164155
CONTEXT_E_SYNCH_TIMEOUT = -2147164154
CONTEXT_E_OLDREF = -2147164153
CONTEXT_E_ROLENOTFOUND = -2147164148
CONTEXT_E_TMNOTAVAILABLE = -2147164145
CO_E_ACTIVATIONFAILED = -2147164127
CO_E_ACTIVATIONFAILED_EVENTLOGGED = -2147164126
CO_E_ACTIVATIONFAILED_CATALOGERROR = -2147164125
CO_E_ACTIVATIONFAILED_TIMEOUT = -2147164124
CO_E_INITIALIZATIONFAILED = -2147164123
CONTEXT_E_NOJIT = -2147164122
CONTEXT_E_NOTRANSACTION = -2147164121
CO_E_THREADINGMODEL_CHANGED = -2147164120
CO_E_NOIISINTRINSICS = -2147164119
CO_E_NOCOOKIES = -2147164118
CO_E_DBERROR = -2147164117
CO_E_NOTPOOLED = -2147164116
CO_E_NOTCONSTRUCTED = -2147164115
CO_E_NOSYNCHRONIZATION = -2147164114
CO_E_ISOLEVELMISMATCH = -2147164113
CO_E_CALL_OUT_OF_TX_SCOPE_NOT_ALLOWED = -2147164112
CO_E_EXIT_TRANSACTION_SCOPE_NOT_CALLED = -2147164111
OLE_S_USEREG = 0x00040000
OLE_S_STATIC = 0x00040001
OLE_S_MAC_CLIPFORMAT = 0x00040002
DRAGDROP_S_DROP = 0x00040100
DRAGDROP_S_CANCEL = 0x00040101
DRAGDROP_S_USEDEFAULTCURSORS = 0x00040102
DATA_S_SAMEFORMATETC = 0x00040130
VIEW_S_ALREADY_FROZEN = 0x00040140
CACHE_S_FORMATETC_NOTSUPPORTED = 0x00040170
CACHE_S_SAMECACHE = 0x00040171
CACHE_S_SOMECACHES_NOTUPDATED = 0x00040172
OLEOBJ_S_INVALIDVERB = 0x00040180
OLEOBJ_S_CANNOT_DOVERB_NOW = 0x00040181
OLEOBJ_S_INVALIDHWND = 0x00040182
INPLACE_S_TRUNCATED = 0x000401A0
CONVERT10_S_NO_PRESENTATION = 0x000401C0
MK_S_REDUCED_TO_SELF = 0x000401E2
MK_S_ME = 0x000401E4
MK_S_HIM = 0x000401E5
MK_S_US = 0x000401E6
MK_S_MONIKERALREADYREGISTERED = 0x000401E7
SCHED_S_TASK_READY = 0x00041300
SCHED_S_TASK_RUNNING = 0x00041301
SCHED_S_TASK_DISABLED = 0x00041302
SCHED_S_TASK_HAS_NOT_RUN = 0x00041303
SCHED_S_TASK_NO_MORE_RUNS = 0x00041304
SCHED_S_TASK_NOT_SCHEDULED = 0x00041305
SCHED_S_TASK_TERMINATED = 0x00041306
SCHED_S_TASK_NO_VALID_TRIGGERS = 0x00041307
SCHED_S_EVENT_TRIGGER = 0x00041308
SCHED_E_TRIGGER_NOT_FOUND = -**********
SCHED_E_TASK_NOT_READY = -**********
SCHED_E_TASK_NOT_RUNNING = -**********
SCHED_E_SERVICE_NOT_INSTALLED = -**********
SCHED_E_CANNOT_OPEN_TASK = -**********
SCHED_E_INVALID_TASK = -**********
SCHED_E_ACCOUNT_INFORMATION_NOT_SET = -**********
SCHED_E_ACCOUNT_NAME_NOT_FOUND = -**********
SCHED_E_ACCOUNT_DBASE_CORRUPT = -**********
SCHED_E_NO_SECURITY_SERVICES = -**********
SCHED_E_UNKNOWN_OBJECT_VERSION = -**********
SCHED_E_UNSUPPORTED_ACCOUNT_OPTION = -**********
SCHED_E_SERVICE_NOT_RUNNING = -**********
SCHED_E_UNEXPECTEDNODE = -**********
SCHED_E_NAMESPACE = -**********
SCHED_E_INVALIDVALUE = -**********
SCHED_E_MISSINGNODE = -**********
SCHED_E_MALFORMEDXML = -**********
SCHED_S_SOME_TRIGGERS_FAILED = 0x0004131B
SCHED_S_BATCH_LOGON_PROBLEM = 0x0004131C
SCHED_E_TOO_MANY_NODES = -**********
SCHED_E_PAST_END_BOUNDARY = -**********
SCHED_E_ALREADY_RUNNING = -**********
SCHED_E_USER_NOT_LOGGED_ON = -**********
SCHED_E_INVALID_TASK_HASH = -**********
SCHED_E_SERVICE_NOT_AVAILABLE = -**********
SCHED_E_SERVICE_TOO_BUSY = -**********
SCHED_E_TASK_ATTEMPTED = -**********
SCHED_S_TASK_QUEUED = 0x00041325
SCHED_E_TASK_DISABLED = -**********
SCHED_E_TASK_NOT_V1_COMPAT = -**********
SCHED_E_START_ON_DEMAND = -**********
SCHED_E_TASK_NOT_UBPM_COMPAT = -**********
SCHED_E_DEPRECATED_FEATURE_USED = -**********
CO_E_CLASS_CREATE_FAILED = -**********
CO_E_SCM_ERROR = -**********
CO_E_SCM_RPC_FAILURE = -**********
CO_E_BAD_PATH = -2146959356
CO_E_SERVER_EXEC_FAILURE = -2146959355
CO_E_OBJSRV_RPC_FAILURE = -2146959354
MK_E_NO_NORMALIZED = -2146959353
CO_E_SERVER_STOPPING = -2146959352
MEM_E_INVALID_ROOT = -2146959351
MEM_E_INVALID_LINK = -2146959344
MEM_E_INVALID_SIZE = -2146959343
CO_S_NOTALLINTERFACES = 0x00080012
CO_S_MACHINENAMENOTFOUND = 0x00080013
CO_E_MISSING_DISPLAYNAME = -2146959339
CO_E_RUNAS_VALUE_MUST_BE_AAA = -2146959338
CO_E_ELEVATION_DISABLED = -2146959337
APPX_E_PACKAGING_INTERNAL = -2146958848
APPX_E_INTERLEAVING_NOT_ALLOWED = -2146958847
APPX_E_RELATIONSHIPS_NOT_ALLOWED = -2146958846
APPX_E_MISSING_REQUIRED_FILE = -2146958845
APPX_E_INVALID_MANIFEST = -2146958844
APPX_E_INVALID_BLOCKMAP = -2146958843
APPX_E_CORRUPT_CONTENT = -2146958842
APPX_E_BLOCK_HASH_INVALID = -2146958841
APPX_E_REQUESTED_RANGE_TOO_LARGE = -2146958840
APPX_E_INVALID_SIP_CLIENT_DATA = -2146958839
APPX_E_INVALID_KEY_INFO = -2146958838
APPX_E_INVALID_CONTENTGROUPMAP = -2146958837
APPX_E_INVALID_APPINSTALLER = -2146958836
APPX_E_DELTA_BASELINE_VERSION_MISMATCH = -2146958835
APPX_E_DELTA_PACKAGE_MISSING_FILE = -2146958834
APPX_E_INVALID_DELTA_PACKAGE = -2146958833
APPX_E_DELTA_APPENDED_PACKAGE_NOT_ALLOWED = -2146958832
APPX_E_INVALID_PACKAGING_LAYOUT = -2146958831
APPX_E_INVALID_PACKAGESIGNCONFIG = -2146958830
APPX_E_RESOURCESPRI_NOT_ALLOWED = -2146958829
APPX_E_FILE_COMPRESSION_MISMATCH = -2146958828
APPX_E_INVALID_PAYLOAD_PACKAGE_EXTENSION = -2146958827
APPX_E_INVALID_ENCRYPTION_EXCLUSION_FILE_LIST = -2146958826
APPX_E_INVALID_PACKAGE_FOLDER_ACLS = -2146958825
APPX_E_INVALID_PUBLISHER_BRIDGING = -2146958824
APPX_E_DIGEST_MISMATCH = -2146958823
BT_E_SPURIOUS_ACTIVATION = -2146958592
DISP_E_UNKNOWNINTERFACE = -2147352575
DISP_E_MEMBERNOTFOUND = -2147352573
DISP_E_PARAMNOTFOUND = -2147352572
DISP_E_TYPEMISMATCH = -2147352571
DISP_E_UNKNOWNNAME = -2147352570
DISP_E_NONAMEDARGS = -2147352569
DISP_E_BADVARTYPE = -2147352568
DISP_E_EXCEPTION = -2147352567
DISP_E_OVERFLOW = -2147352566
DISP_E_BADINDEX = -2147352565
DISP_E_UNKNOWNLCID = -2147352564
DISP_E_ARRAYISLOCKED = -2147352563
DISP_E_BADPARAMCOUNT = -2147352562
DISP_E_PARAMNOTOPTIONAL = -2147352561
DISP_E_BADCALLEE = -2147352560
DISP_E_NOTACOLLECTION = -2147352559
DISP_E_DIVBYZERO = -2147352558
DISP_E_BUFFERTOOSMALL = -2147352557
TYPE_E_BUFFERTOOSMALL = -2147319786
TYPE_E_FIELDNOTFOUND = -2147319785
TYPE_E_INVDATAREAD = -2147319784
TYPE_E_UNSUPFORMAT = -2147319783
TYPE_E_REGISTRYACCESS = -2147319780
TYPE_E_LIBNOTREGISTERED = -2147319779
TYPE_E_UNDEFINEDTYPE = -2147319769
TYPE_E_QUALIFIEDNAMEDISALLOWED = -2147319768
TYPE_E_INVALIDSTATE = -2147319767
TYPE_E_WRONGTYPEKIND = -2147319766
TYPE_E_ELEMENTNOTFOUND = -2147319765
TYPE_E_AMBIGUOUSNAME = -2147319764
TYPE_E_NAMECONFLICT = -2147319763
TYPE_E_UNKNOWNLCID = -2147319762
TYPE_E_DLLFUNCTIONNOTFOUND = -2147319761
TYPE_E_BADMODULEKIND = -2147317571
TYPE_E_SIZETOOBIG = -2147317563
TYPE_E_DUPLICATEID = -2147317562
TYPE_E_INVALIDID = -2147317553
TYPE_E_TYPEMISMATCH = -2147316576
TYPE_E_OUTOFBOUNDS = -2147316575
TYPE_E_IOERROR = -2147316574
TYPE_E_CANTCREATETMPFILE = -2147316573
TYPE_E_CANTLOADLIBRARY = -2147312566
TYPE_E_INCONSISTENTPROPFUNCS = -2147312509
TYPE_E_CIRCULARTYPE = -2147312508
STG_E_INVALIDFUNCTION = -2147287039
STG_E_FILENOTFOUND = -2147287038
STG_E_PATHNOTFOUND = -2147287037
STG_E_TOOMANYOPENFILES = -2147287036
STG_E_ACCESSDENIED = -2147287035
STG_E_INVALIDHANDLE = -2147287034
STG_E_INSUFFICIENTMEMORY = -2147287032
STG_E_INVALIDPOINTER = -2147287031
STG_E_NOMOREFILES = -2147287022
STG_E_DISKISWRITEPROTECTED = -2147287021
STG_E_SEEKERROR = -2147287015
STG_E_WRITEFAULT = -2147287011
STG_E_READFAULT = -2147287010
STG_E_SHAREVIOLATION = -2147287008
STG_E_LOCKVIOLATION = -2147287007
STG_E_FILEALREADYEXISTS = -2147286960
STG_E_INVALIDPARAMETER = -2147286953
STG_E_MEDIUMFULL = -2147286928
STG_E_PROPSETMISMATCHED = -2147286800
STG_E_ABNORMALAPIEXIT = -2147286790
STG_E_INVALIDHEADER = -2147286789
STG_E_INVALIDNAME = -2147286788
STG_E_UNKNOWN = -2147286787
STG_E_UNIMPLEMENTEDFUNCTION = -2147286786
STG_E_INVALIDFLAG = -2147286785
STG_E_INUSE = -2147286784
STG_E_NOTCURRENT = -2147286783
STG_E_REVERTED = -2147286782
STG_E_CANTSAVE = -2147286781
STG_E_OLDFORMAT = -2147286780
STG_E_OLDDLL = -2147286779
STG_E_SHAREREQUIRED = -2147286778
STG_E_NOTFILEBASEDSTORAGE = -2147286777
STG_E_EXTANTMARSHALLINGS = -2147286776
STG_E_DOCFILECORRUPT = -2147286775
STG_E_BADBASEADDRESS = -2147286768
STG_E_DOCFILETOOLARGE = -2147286767
STG_E_NOTSIMPLEFORMAT = -2147286766
STG_E_INCOMPLETE = -2147286527
STG_E_TERMINATED = -2147286526
STG_S_CONVERTED = 0x00030200
STG_S_BLOCK = 0x00030201
STG_S_RETRYNOW = 0x00030202
STG_S_MONITORING = 0x00030203
STG_S_MULTIPLEOPENS = 0x00030204
STG_S_CONSOLIDATIONFAILED = 0x00030205
STG_S_CANNOTCONSOLIDATE = 0x00030206
STG_S_POWER_CYCLE_REQUIRED = 0x00030207
STG_E_FIRMWARE_SLOT_INVALID = -2147286520
STG_E_FIRMWARE_IMAGE_INVALID = -2147286519
STG_E_DEVICE_UNRESPONSIVE = -2147286518
STG_E_STATUS_COPY_PROTECTION_FAILURE = -2147286267
STG_E_CSS_AUTHENTICATION_FAILURE = -2147286266
STG_E_CSS_KEY_NOT_PRESENT = -2147286265
STG_E_CSS_KEY_NOT_ESTABLISHED = -2147286264
STG_E_CSS_SCRAMBLED_SECTOR = -2147286263
STG_E_CSS_REGION_MISMATCH = -2147286262
STG_E_RESETS_EXHAUSTED = -2147286261
RPC_E_CALL_REJECTED = -2147418111
RPC_E_CALL_CANCELED = -2147418110
RPC_E_CANTPOST_INSENDCALL = -2147418109
RPC_E_CANTCALLOUT_INASYNCCALL = -2147418108
RPC_E_CANTCALLOUT_INEXTERNALCALL = -2147418107
RPC_E_CONNECTION_TERMINATED = -2147418106
RPC_E_SERVER_DIED = -2147418105
RPC_E_CLIENT_DIED = -2147418104
RPC_E_INVALID_DATAPACKET = -2147418103
RPC_E_CANTTRANSMIT_CALL = -2147418102
RPC_E_CLIENT_CANTMARSHAL_DATA = -2147418101
RPC_E_CLIENT_CANTUNMARSHAL_DATA = -2147418100
RPC_E_SERVER_CANTMARSHAL_DATA = -2147418099
RPC_E_SERVER_CANTUNMARSHAL_DATA = -2147418098
RPC_E_INVALID_DATA = -2147418097
RPC_E_INVALID_PARAMETER = -2147418096
RPC_E_CANTCALLOUT_AGAIN = -2147418095
RPC_E_SERVER_DIED_DNE = -2147418094
RPC_E_SYS_CALL_FAILED = -2147417856
RPC_E_OUT_OF_RESOURCES = -2147417855
RPC_E_ATTEMPTED_MULTITHREAD = -2147417854
RPC_E_NOT_REGISTERED = -2147417853
RPC_E_FAULT = -2147417852
RPC_E_SERVERFAULT = -2147417851
RPC_E_CHANGED_MODE = -2147417850
RPC_E_INVALIDMETHOD = -2147417849
RPC_E_DISCONNECTED = -2147417848
RPC_E_RETRY = -2147417847
RPC_E_SERVERCALL_RETRYLATER = -2147417846
RPC_E_SERVERCALL_REJECTED = -2147417845
RPC_E_INVALID_CALLDATA = -2147417844
RPC_E_CANTCALLOUT_ININPUTSYNCCALL = -2147417843
RPC_E_WRONG_THREAD = -2147417842
RPC_E_THREAD_NOT_INIT = -2147417841
RPC_E_VERSION_MISMATCH = -2147417840
RPC_E_INVALID_HEADER = -2147417839
RPC_E_INVALID_EXTENSION = -2147417838
RPC_E_INVALID_IPID = -2147417837
RPC_E_INVALID_OBJECT = -2147417836
RPC_S_CALLPENDING = -2147417835
RPC_S_WAITONTIMER = -2147417834
RPC_E_CALL_COMPLETE = -2147417833
RPC_E_UNSECURE_CALL = -2147417832
RPC_E_TOO_LATE = -2147417831
RPC_E_NO_GOOD_SECURITY_PACKAGES = -2147417830
RPC_E_ACCESS_DENIED = -2147417829
RPC_E_REMOTE_DISABLED = -2147417828
RPC_E_INVALID_OBJREF = -2147417827
RPC_E_NO_CONTEXT = -2147417826
RPC_E_TIMEOUT = -2147417825
RPC_E_NO_SYNC = -2147417824
RPC_E_FULLSIC_REQUIRED = -2147417823
RPC_E_INVALID_STD_NAME = -2147417822
CO_E_FAILEDTOIMPERSONATE = -2147417821
CO_E_FAILEDTOGETSECCTX = -2147417820
CO_E_FAILEDTOOPENTHREADTOKEN = -2147417819
CO_E_FAILEDTOGETTOKENINFO = -2147417818
CO_E_TRUSTEEDOESNTMATCHCLIENT = -2147417817
CO_E_FAILEDTOQUERYCLIENTBLANKET = -2147417816
CO_E_FAILEDTOSETDACL = -2147417815
CO_E_ACCESSCHECKFAILED = -2147417814
CO_E_NETACCESSAPIFAILED = -2147417813
CO_E_WRONGTRUSTEENAMESYNTAX = -2147417812
CO_E_INVALIDSID = -2147417811
CO_E_CONVERSIONFAILED = -2147417810
CO_E_NOMATCHINGSIDFOUND = -2147417809
CO_E_LOOKUPACCSIDFAILED = -2147417808
CO_E_NOMATCHINGNAMEFOUND = -2147417807
CO_E_LOOKUPACCNAMEFAILED = -2147417806
CO_E_SETSERLHNDLFAILED = -2147417805
CO_E_FAILEDTOGETWINDIR = -2147417804
CO_E_PATHTOOLONG = -2147417803
CO_E_FAILEDTOGENUUID = -2147417802
CO_E_FAILEDTOCREATEFILE = -2147417801
CO_E_FAILEDTOCLOSEHANDLE = -2147417800
CO_E_EXCEEDSYSACLLIMIT = -2147417799
CO_E_ACESINWRONGORDER = -2147417798
CO_E_INCOMPATIBLESTREAMVERSION = -2147417797
CO_E_FAILEDTOOPENPROCESSTOKEN = -2147417796
CO_E_DECODEFAILED = -2147417795
CO_E_ACNOTINITIALIZED = -2147417793
CO_E_CANCEL_DISABLED = -2147417792
RPC_E_UNEXPECTED = -2147352577
ERROR_AUDITING_DISABLED = -1073151999
ERROR_ALL_SIDS_FILTERED = -1073151998
ERROR_BIZRULES_NOT_ENABLED = -1073151997
NTE_BAD_UID = -**********
NTE_BAD_HASH = -**********
NTE_BAD_KEY = -**********
NTE_BAD_LEN = -**********
NTE_BAD_DATA = -**********
NTE_BAD_SIGNATURE = -**********
NTE_BAD_VER = -**********
NTE_BAD_ALGID = -**********
NTE_BAD_FLAGS = -**********
NTE_BAD_TYPE = -**********
NTE_BAD_KEY_STATE = -**********
NTE_BAD_HASH_STATE = -**********
NTE_NO_KEY = -**********
NTE_NO_MEMORY = -**********
NTE_EXISTS = -**********
NTE_PERM = -**********
NTE_NOT_FOUND = -**********
NTE_DOUBLE_ENCRYPT = -**********
NTE_BAD_PROVIDER = -**********
NTE_BAD_PROV_TYPE = -**********
NTE_BAD_PUBLIC_KEY = -**********
NTE_BAD_KEYSET = -**********
NTE_PROV_TYPE_NOT_DEF = -**********
NTE_PROV_TYPE_ENTRY_BAD = -**********
NTE_KEYSET_NOT_DEF = -**********
NTE_KEYSET_ENTRY_BAD = -**********
NTE_PROV_TYPE_NO_MATCH = -**********
NTE_SIGNATURE_FILE_BAD = -**********
NTE_PROVIDER_DLL_FAIL = -**********
NTE_PROV_DLL_NOT_FOUND = -**********
NTE_BAD_KEYSET_PARAM = -**********
NTE_FAIL = -**********
NTE_SYS_ERR = -**********
NTE_SILENT_CONTEXT = -**********
NTE_TOKEN_KEYSET_STORAGE_FULL = -**********
NTE_TEMPORARY_PROFILE = -**********
NTE_FIXEDPARAMETER = -**********
NTE_INVALID_HANDLE = -**********
NTE_INVALID_PARAMETER = -**********
NTE_BUFFER_TOO_SMALL = -**********
NTE_NOT_SUPPORTED = -**********
NTE_NO_MORE_ITEMS = -**********
NTE_BUFFERS_OVERLAP = -**********
NTE_DECRYPTION_FAILURE = -**********
NTE_INTERNAL_ERROR = -**********
NTE_UI_REQUIRED = -**********
NTE_HMAC_NOT_SUPPORTED = -**********
NTE_DEVICE_NOT_READY = -**********
NTE_AUTHENTICATION_IGNORED = -**********
NTE_VALIDATION_FAILED = -**********
NTE_INCORRECT_PASSWORD = -**********
NTE_ENCRYPTION_FAILURE = -**********
NTE_DEVICE_NOT_FOUND = -**********
NTE_USER_CANCELLED = -**********
NTE_PASSWORD_CHANGE_REQUIRED = -**********
NTE_NOT_ACTIVE_CONSOLE = -**********
SEC_E_INSUFFICIENT_MEMORY = -2146893056
SEC_E_INVALID_HANDLE = -2146893055
SEC_E_UNSUPPORTED_FUNCTION = -2146893054
SEC_E_TARGET_UNKNOWN = -2146893053
SEC_E_INTERNAL_ERROR = -2146893052
SEC_E_SECPKG_NOT_FOUND = -2146893051
SEC_E_NOT_OWNER = -2146893050
SEC_E_CANNOT_INSTALL = -2146893049
SEC_E_INVALID_TOKEN = -2146893048
SEC_E_CANNOT_PACK = -2146893047
SEC_E_QOP_NOT_SUPPORTED = -2146893046
SEC_E_NO_IMPERSONATION = -2146893045
SEC_E_LOGON_DENIED = -2146893044
SEC_E_UNKNOWN_CREDENTIALS = -2146893043
SEC_E_NO_CREDENTIALS = -2146893042
SEC_E_MESSAGE_ALTERED = -2146893041
SEC_E_OUT_OF_SEQUENCE = -2146893040
SEC_E_NO_AUTHENTICATING_AUTHORITY = -2146893039
SEC_I_CONTINUE_NEEDED = 0x00090312
SEC_I_COMPLETE_NEEDED = 0x00090313
SEC_I_COMPLETE_AND_CONTINUE = 0x00090314
SEC_I_LOCAL_LOGON = 0x00090315
SEC_I_GENERIC_EXTENSION_RECEIVED = 0x00090316
SEC_E_BAD_PKGID = -2146893034
SEC_E_CONTEXT_EXPIRED = -2146893033
SEC_I_CONTEXT_EXPIRED = 0x00090317
SEC_E_INCOMPLETE_MESSAGE = -2146893032
SEC_E_INCOMPLETE_CREDENTIALS = -2146893024
SEC_E_BUFFER_TOO_SMALL = -2146893023
SEC_I_INCOMPLETE_CREDENTIALS = 0x00090320
SEC_I_RENEGOTIATE = 0x00090321
SEC_E_WRONG_PRINCIPAL = -2146893022
SEC_I_NO_LSA_CONTEXT = 0x00090323
SEC_E_TIME_SKEW = -2146893020
SEC_E_UNTRUSTED_ROOT = -2146893019
SEC_E_ILLEGAL_MESSAGE = -2146893018
SEC_E_CERT_UNKNOWN = -2146893017
SEC_E_CERT_EXPIRED = -2146893016
SEC_E_ENCRYPT_FAILURE = -2146893015
SEC_E_DECRYPT_FAILURE = -2146893008
SEC_E_ALGORITHM_MISMATCH = -2146893007
SEC_E_SECURITY_QOS_FAILED = -2146893006
SEC_E_UNFINISHED_CONTEXT_DELETED = -2146893005
SEC_E_NO_TGT_REPLY = -2146893004
SEC_E_NO_IP_ADDRESSES = -2146893003
SEC_E_WRONG_CREDENTIAL_HANDLE = -2146893002
SEC_E_CRYPTO_SYSTEM_INVALID = -2146893001
SEC_E_MAX_REFERRALS_EXCEEDED = -2146893000
SEC_E_MUST_BE_KDC = -**********
SEC_E_STRONG_CRYPTO_NOT_SUPPORTED = -**********
SEC_E_TOO_MANY_PRINCIPALS = -**********
SEC_E_NO_PA_DATA = -**********
SEC_E_PKINIT_NAME_MISMATCH = -**********
SEC_E_SMARTCARD_LOGON_REQUIRED = -**********
SEC_E_SHUTDOWN_IN_PROGRESS = -**********
SEC_E_KDC_INVALID_REQUEST = -**********
SEC_E_KDC_UNABLE_TO_REFER = -**********
SEC_E_KDC_UNKNOWN_ETYPE = -**********
SEC_E_UNSUPPORTED_PREAUTH = -**********
SEC_E_DELEGATION_REQUIRED = -**********
SEC_E_BAD_BINDINGS = -**********
SEC_E_MULTIPLE_ACCOUNTS = -**********
SEC_E_NO_KERB_KEY = -**********
SEC_E_CERT_WRONG_USAGE = -**********
SEC_E_DOWNGRADE_DETECTED = -**********
SEC_E_SMARTCARD_CERT_REVOKED = -**********
SEC_E_ISSUING_CA_UNTRUSTED = -**********
SEC_E_REVOCATION_OFFLINE_C = -**********
SEC_E_PKINIT_CLIENT_FAILURE = -**********
SEC_E_SMARTCARD_CERT_EXPIRED = -**********
SEC_E_NO_S4U_PROT_SUPPORT = -**********
SEC_E_CROSSREALM_DELEGATION_FAILURE = -**********
SEC_E_REVOCATION_OFFLINE_KDC = -**********
SEC_E_ISSUING_CA_UNTRUSTED_KDC = -**********
SEC_E_KDC_CERT_EXPIRED = -**********
SEC_E_KDC_CERT_REVOKED = -**********
SEC_I_SIGNATURE_NEEDED = 0x0009035C
SEC_E_INVALID_PARAMETER = -**********
SEC_E_DELEGATION_POLICY = -**********
SEC_E_POLICY_NLTM_ONLY = -**********
SEC_I_NO_RENEGOTIATION = 0x00090360
SEC_E_NO_CONTEXT = -**********
SEC_E_PKU2U_CERT_FAILURE = -**********
SEC_E_MUTUAL_AUTH_FAILED = -**********
SEC_I_MESSAGE_FRAGMENT = 0x00090364
SEC_E_ONLY_HTTPS_ALLOWED = -**********
SEC_I_CONTINUE_NEEDED_MESSAGE_OK = 0x00090366
SEC_E_APPLICATION_PROTOCOL_MISMATCH = -2146892953
SEC_I_ASYNC_CALL_PENDING = 0x00090368
SEC_E_INVALID_UPN_NAME = -2146892951
SEC_E_EXT_BUFFER_TOO_SMALL = -2146892950
SEC_E_INSUFFICIENT_BUFFERS = -2146892949
SEC_E_NO_SPM = SEC_E_INTERNAL_ERROR
SEC_E_NOT_SUPPORTED = SEC_E_UNSUPPORTED_FUNCTION
CRYPT_E_MSG_ERROR = -2146889727
CRYPT_E_UNKNOWN_ALGO = -2146889726
CRYPT_E_OID_FORMAT = -2146889725
CRYPT_E_INVALID_MSG_TYPE = -2146889724
CRYPT_E_UNEXPECTED_ENCODING = -2146889723
CRYPT_E_AUTH_ATTR_MISSING = -2146889722
CRYPT_E_HASH_VALUE = -2146889721
CRYPT_E_INVALID_INDEX = -2146889720
CRYPT_E_ALREADY_DECRYPTED = -2146889719
CRYPT_E_NOT_DECRYPTED = -2146889718
CRYPT_E_RECIPIENT_NOT_FOUND = -**********
CRYPT_E_CONTROL_TYPE = -**********
CRYPT_E_ISSUER_SERIALNUMBER = -**********
CRYPT_E_SIGNER_NOT_FOUND = -**********
CRYPT_E_ATTRIBUTES_MISSING = -**********
CRYPT_E_STREAM_MSG_NOT_READY = -**********
CRYPT_E_STREAM_INSUFFICIENT_DATA = -**********
CRYPT_I_NEW_PROTECTION_REQUIRED = 0x00091012
CRYPT_E_BAD_LEN = -**********
CRYPT_E_BAD_ENCODE = -**********
CRYPT_E_FILE_ERROR = -**********
CRYPT_E_NOT_FOUND = -**********
CRYPT_E_EXISTS = -**********
CRYPT_E_NO_PROVIDER = -**********
CRYPT_E_SELF_SIGNED = -**********
CRYPT_E_DELETED_PREV = -**********
CRYPT_E_NO_MATCH = -**********
CRYPT_E_UNEXPECTED_MSG_TYPE = -**********
CRYPT_E_NO_KEY_PROPERTY = -**********
CRYPT_E_NO_DECRYPT_CERT = -**********
CRYPT_E_BAD_MSG = -**********
CRYPT_E_NO_SIGNER = -**********
CRYPT_E_PENDING_CLOSE = -**********
CRYPT_E_REVOKED = -**********
CRYPT_E_NO_REVOCATION_DLL = -**********
CRYPT_E_NO_REVOCATION_CHECK = -**********
CRYPT_E_REVOCATION_OFFLINE = -**********
CRYPT_E_NOT_IN_REVOCATION_DATABASE = -**********
CRYPT_E_INVALID_NUMERIC_STRING = -**********
CRYPT_E_INVALID_PRINTABLE_STRING = -**********
CRYPT_E_INVALID_IA5_STRING = -**********
CRYPT_E_INVALID_X500_STRING = -**********
CRYPT_E_NOT_CHAR_STRING = -**********
CRYPT_E_FILERESIZED = -**********
CRYPT_E_SECURITY_SETTINGS = -**********
CRYPT_E_NO_VERIFY_USAGE_DLL = -**********
CRYPT_E_NO_VERIFY_USAGE_CHECK = -**********
CRYPT_E_VERIFY_USAGE_OFFLINE = -**********
CRYPT_E_NOT_IN_CTL = -**********
CRYPT_E_NO_TRUSTED_SIGNER = -2146885589
CRYPT_E_MISSING_PUBKEY_PARA = -2146885588
CRYPT_E_OBJECT_LOCATOR_OBJECT_NOT_FOUND = -2146885587
CRYPT_E_OSS_ERROR = -2146881536
OSS_MORE_BUF = -2146881535
OSS_NEGATIVE_UINTEGER = -2146881534
OSS_PDU_RANGE = -2146881533
OSS_MORE_INPUT = -2146881532
OSS_DATA_ERROR = -2146881531
OSS_BAD_ARG = -2146881530
OSS_BAD_VERSION = -2146881529
OSS_OUT_MEMORY = -2146881528
OSS_PDU_MISMATCH = -2146881527
OSS_LIMITED = -2146881526
OSS_BAD_PTR = -2146881525
OSS_BAD_TIME = -2146881524
OSS_INDEFINITE_NOT_SUPPORTED = -2146881523
OSS_MEM_ERROR = -2146881522
OSS_BAD_TABLE = -2146881521
OSS_TOO_LONG = -2146881520
OSS_CONSTRAINT_VIOLATED = -2146881519
OSS_FATAL_ERROR = -2146881518
OSS_ACCESS_SERIALIZATION_ERROR = -2146881517
OSS_NULL_TBL = -2146881516
OSS_NULL_FCN = -2146881515
OSS_BAD_ENCRULES = -2146881514
OSS_UNAVAIL_ENCRULES = -2146881513
OSS_CANT_OPEN_TRACE_WINDOW = -2146881512
OSS_UNIMPLEMENTED = -2146881511
OSS_OID_DLL_NOT_LINKED = -2146881510
OSS_CANT_OPEN_TRACE_FILE = -2146881509
OSS_TRACE_FILE_ALREADY_OPEN = -2146881508
OSS_TABLE_MISMATCH = -2146881507
OSS_TYPE_NOT_SUPPORTED = -2146881506
OSS_REAL_DLL_NOT_LINKED = -2146881505
OSS_REAL_CODE_NOT_LINKED = -2146881504
OSS_OUT_OF_RANGE = -2146881503
OSS_COPIER_DLL_NOT_LINKED = -2146881502
OSS_CONSTRAINT_DLL_NOT_LINKED = -2146881501
OSS_COMPARATOR_DLL_NOT_LINKED = -2146881500
OSS_COMPARATOR_CODE_NOT_LINKED = -2146881499
OSS_MEM_MGR_DLL_NOT_LINKED = -2146881498
OSS_PDV_DLL_NOT_LINKED = -2146881497
OSS_PDV_CODE_NOT_LINKED = -2146881496
OSS_API_DLL_NOT_LINKED = -2146881495
OSS_BERDER_DLL_NOT_LINKED = -2146881494
OSS_PER_DLL_NOT_LINKED = -2146881493
OSS_OPEN_TYPE_ERROR = -2146881492
OSS_MUTEX_NOT_CREATED = -2146881491
OSS_CANT_CLOSE_TRACE_FILE = -2146881490
CRYPT_E_ASN1_ERROR = -2146881280
CRYPT_E_ASN1_INTERNAL = -2146881279
CRYPT_E_ASN1_EOD = -2146881278
CRYPT_E_ASN1_CORRUPT = -2146881277
CRYPT_E_ASN1_LARGE = -2146881276
CRYPT_E_ASN1_CONSTRAINT = -2146881275
CRYPT_E_ASN1_MEMORY = -2146881274
CRYPT_E_ASN1_OVERFLOW = -2146881273
CRYPT_E_ASN1_BADPDU = -2146881272
CRYPT_E_ASN1_BADARGS = -2146881271
CRYPT_E_ASN1_BADREAL = -2146881270
CRYPT_E_ASN1_BADTAG = -2146881269
CRYPT_E_ASN1_CHOICE = -2146881268
CRYPT_E_ASN1_RULE = -2146881267
CRYPT_E_ASN1_UTF8 = -2146881266
CRYPT_E_ASN1_PDU_TYPE = -2146881229
CRYPT_E_ASN1_NYI = -2146881228
CRYPT_E_ASN1_EXTENDED = -2146881023
CRYPT_E_ASN1_NOEOD = -2146881022
CERTSRV_E_BAD_REQUESTSUBJECT = -2146877439
CERTSRV_E_NO_REQUEST = -2146877438
CERTSRV_E_BAD_REQUESTSTATUS = -2146877437
CERTSRV_E_PROPERTY_EMPTY = -2146877436
CERTSRV_E_INVALID_CA_CERTIFICATE = -2146877435
CERTSRV_E_SERVER_SUSPENDED = -2146877434
CERTSRV_E_ENCODING_LENGTH = -2146877433
CERTSRV_E_ROLECONFLICT = -2146877432
CERTSRV_E_RESTRICTEDOFFICER = -2146877431
CERTSRV_E_KEY_ARCHIVAL_NOT_CONFIGURED = -2146877430
CERTSRV_E_NO_VALID_KRA = -2146877429
CERTSRV_E_BAD_REQUEST_KEY_ARCHIVAL = -2146877428
CERTSRV_E_NO_CAADMIN_DEFINED = -2146877427
CERTSRV_E_BAD_RENEWAL_CERT_ATTRIBUTE = -2146877426
CERTSRV_E_NO_DB_SESSIONS = -2146877425
CERTSRV_E_ALIGNMENT_FAULT = -2146877424
CERTSRV_E_ENROLL_DENIED = -2146877423
CERTSRV_E_TEMPLATE_DENIED = -2146877422
CERTSRV_E_DOWNLEVEL_DC_SSL_OR_UPGRADE = -2146877421
CERTSRV_E_ADMIN_DENIED_REQUEST = -2146877420
CERTSRV_E_NO_POLICY_SERVER = -2146877419
CERTSRV_E_WEAK_SIGNATURE_OR_KEY = -2146877418
CERTSRV_E_KEY_ATTESTATION_NOT_SUPPORTED = -2146877417
CERTSRV_E_ENCRYPTION_CERT_REQUIRED = -2146877416
CERTSRV_E_UNSUPPORTED_CERT_TYPE = -2146875392
CERTSRV_E_NO_CERT_TYPE = -2146875391
CERTSRV_E_TEMPLATE_CONFLICT = -2146875390
CERTSRV_E_SUBJECT_ALT_NAME_REQUIRED = -2146875389
CERTSRV_E_ARCHIVED_KEY_REQUIRED = -2146875388
CERTSRV_E_SMIME_REQUIRED = -2146875387
CERTSRV_E_BAD_RENEWAL_SUBJECT = -2146875386
CERTSRV_E_BAD_TEMPLATE_VERSION = -2146875385
CERTSRV_E_TEMPLATE_POLICY_REQUIRED = -2146875384
CERTSRV_E_SIGNATURE_POLICY_REQUIRED = -2146875383
CERTSRV_E_SIGNATURE_COUNT = -2146875382
CERTSRV_E_SIGNATURE_REJECTED = -2146875381
CERTSRV_E_ISSUANCE_POLICY_REQUIRED = -2146875380
CERTSRV_E_SUBJECT_UPN_REQUIRED = -2146875379
CERTSRV_E_SUBJECT_DIRECTORY_GUID_REQUIRED = -2146875378
CERTSRV_E_SUBJECT_DNS_REQUIRED = -2146875377
CERTSRV_E_ARCHIVED_KEY_UNEXPECTED = -2146875376
CERTSRV_E_KEY_LENGTH = -2146875375
CERTSRV_E_SUBJECT_EMAIL_REQUIRED = -2146875374
CERTSRV_E_UNKNOWN_CERT_TYPE = -2146875373
CERTSRV_E_CERT_TYPE_OVERLAP = -2146875372
CERTSRV_E_TOO_MANY_SIGNATURES = -2146875371
CERTSRV_E_RENEWAL_BAD_PUBLIC_KEY = -2146875370
CERTSRV_E_INVALID_EK = -2146875369
CERTSRV_E_INVALID_IDBINDING = -2146875368
CERTSRV_E_INVALID_ATTESTATION = -2146875367
CERTSRV_E_KEY_ATTESTATION = -2146875366
CERTSRV_E_CORRUPT_KEY_ATTESTATION = -2146875365
CERTSRV_E_EXPIRED_CHALLENGE = -2146875364
CERTSRV_E_INVALID_RESPONSE = -2146875363
CERTSRV_E_INVALID_REQUESTID = -2146875362
CERTSRV_E_REQUEST_PRECERTIFICATE_MISMATCH = -2146875361
CERTSRV_E_PENDING_CLIENT_RESPONSE = -2146875360
CERTSRV_E_SEC_EXT_DIRECTORY_SID_REQUIRED = -2146875359
XENROLL_E_KEY_NOT_EXPORTABLE = -2146873344
XENROLL_E_CANNOT_ADD_ROOT_CERT = -2146873343
XENROLL_E_RESPONSE_KA_HASH_NOT_FOUND = -2146873342
XENROLL_E_RESPONSE_UNEXPECTED_KA_HASH = -2146873341
XENROLL_E_RESPONSE_KA_HASH_MISMATCH = -2146873340
XENROLL_E_KEYSPEC_SMIME_MISMATCH = -2146873339
TRUST_E_SYSTEM_ERROR = -2146869247
TRUST_E_NO_SIGNER_CERT = -2146869246
TRUST_E_COUNTER_SIGNER = -2146869245
TRUST_E_CERT_SIGNATURE = -2146869244
TRUST_E_TIME_STAMP = -2146869243
TRUST_E_BAD_DIGEST = -2146869232
TRUST_E_MALFORMED_SIGNATURE = -2146869231
TRUST_E_BASIC_CONSTRAINTS = -2146869223
TRUST_E_FINANCIAL_CRITERIA = -2146869218
MSSIPOTF_E_OUTOFMEMRANGE = -2146865151
MSSIPOTF_E_CANTGETOBJECT = -2146865150
MSSIPOTF_E_NOHEADTABLE = -2146865149
MSSIPOTF_E_BAD_MAGICNUMBER = -2146865148
MSSIPOTF_E_BAD_OFFSET_TABLE = -2146865147
MSSIPOTF_E_TABLE_TAGORDER = -2146865146
MSSIPOTF_E_TABLE_LONGWORD = -2146865145
MSSIPOTF_E_BAD_FIRST_TABLE_PLACEMENT = -2146865144
MSSIPOTF_E_TABLES_OVERLAP = -2146865143
MSSIPOTF_E_TABLE_PADBYTES = -2146865142
MSSIPOTF_E_FILETOOSMALL = -**********
MSSIPOTF_E_TABLE_CHECKSUM = -**********
MSSIPOTF_E_FILE_CHECKSUM = -**********
MSSIPOTF_E_FAILED_POLICY = -**********
MSSIPOTF_E_FAILED_HINTS_CHECK = -**********
MSSIPOTF_E_NOT_OPENTYPE = -**********
MSSIPOTF_E_FILE = -**********
MSSIPOTF_E_CRYPT = -**********
MSSIPOTF_E_BADVERSION = -**********
MSSIPOTF_E_DSIG_STRUCTURE = -**********
MSSIPOTF_E_PCONST_CHECK = -**********
MSSIPOTF_E_STRUCTURE = -**********
ERROR_CRED_REQUIRES_CONFIRMATION = -**********
NTE_OP_OK = 0
TRUST_E_PROVIDER_UNKNOWN = -**********
TRUST_E_ACTION_UNKNOWN = -**********
TRUST_E_SUBJECT_FORM_UNKNOWN = -**********
TRUST_E_SUBJECT_NOT_TRUSTED = -**********
DIGSIG_E_ENCODE = -**********
DIGSIG_E_DECODE = -**********
DIGSIG_E_EXTENSIBILITY = -**********
DIGSIG_E_CRYPTO = -**********
PERSIST_E_SIZEDEFINITE = -**********
PERSIST_E_SIZEINDEFINITE = -**********
PERSIST_E_NOTSELFSIZING = -**********
TRUST_E_NOSIGNATURE = -**********
CERT_E_EXPIRED = -**********
CERT_E_VALIDITYPERIODNESTING = -**********
CERT_E_ROLE = -**********
CERT_E_PATHLENCONST = -**********
CERT_E_CRITICAL = -**********
CERT_E_PURPOSE = -**********
CERT_E_ISSUERCHAINING = -**********
CERT_E_MALFORMED = -**********
CERT_E_UNTRUSTEDROOT = -**********
CERT_E_CHAINING = -**********
TRUST_E_FAIL = -**********
CERT_E_REVOKED = -**********
CERT_E_UNTRUSTEDTESTROOT = -**********
CERT_E_REVOCATION_FAILURE = -**********
CERT_E_CN_NO_MATCH = -**********
CERT_E_WRONG_USAGE = -**********
TRUST_E_EXPLICIT_DISTRUST = -**********
CERT_E_UNTRUSTEDCA = -2146762478
CERT_E_INVALID_POLICY = -2146762477
CERT_E_INVALID_NAME = -2146762476


def HRESULT_FROM_SETUPAPI(x):
    return __HRESULT_FROM_SETUPAPI(x)


SPAPI_E_EXPECTED_SECTION_NAME = -2146500608
SPAPI_E_BAD_SECTION_NAME_LINE = -2146500607
SPAPI_E_SECTION_NAME_TOO_LONG = -2146500606
SPAPI_E_GENERAL_SYNTAX = -2146500605
SPAPI_E_WRONG_INF_STYLE = -2146500352
SPAPI_E_SECTION_NOT_FOUND = -2146500351
SPAPI_E_LINE_NOT_FOUND = -2146500350
SPAPI_E_NO_BACKUP = -2146500349
SPAPI_E_NO_ASSOCIATED_CLASS = -2146500096
SPAPI_E_CLASS_MISMATCH = -2146500095
SPAPI_E_DUPLICATE_FOUND = -2146500094
SPAPI_E_NO_DRIVER_SELECTED = -2146500093
SPAPI_E_KEY_DOES_NOT_EXIST = -2146500092
SPAPI_E_INVALID_DEVINST_NAME = -2146500091
SPAPI_E_INVALID_CLASS = -2146500090
SPAPI_E_DEVINST_ALREADY_EXISTS = -2146500089
SPAPI_E_DEVINFO_NOT_REGISTERED = -2146500088
SPAPI_E_INVALID_REG_PROPERTY = -2146500087
SPAPI_E_NO_INF = -2146500086
SPAPI_E_NO_SUCH_DEVINST = -2146500085
SPAPI_E_CANT_LOAD_CLASS_ICON = -2146500084
SPAPI_E_INVALID_CLASS_INSTALLER = -2146500083
SPAPI_E_DI_DO_DEFAULT = -2146500082
SPAPI_E_DI_NOFILECOPY = -2146500081
SPAPI_E_INVALID_HWPROFILE = -2146500080
SPAPI_E_NO_DEVICE_SELECTED = -2146500079
SPAPI_E_DEVINFO_LIST_LOCKED = -2146500078
SPAPI_E_DEVINFO_DATA_LOCKED = -2146500077
SPAPI_E_DI_BAD_PATH = -2146500076
SPAPI_E_NO_CLASSINSTALL_PARAMS = -2146500075
SPAPI_E_FILEQUEUE_LOCKED = -2146500074
SPAPI_E_BAD_SERVICE_INSTALLSECT = -2146500073
SPAPI_E_NO_CLASS_DRIVER_LIST = -2146500072
SPAPI_E_NO_ASSOCIATED_SERVICE = -**********
SPAPI_E_NO_DEFAULT_DEVICE_INTERFACE = -**********
SPAPI_E_DEVICE_INTERFACE_ACTIVE = -**********
SPAPI_E_DEVICE_INTERFACE_REMOVED = -**********
SPAPI_E_BAD_INTERFACE_INSTALLSECT = -**********
SPAPI_E_NO_SUCH_INTERFACE_CLASS = -**********
SPAPI_E_INVALID_REFERENCE_STRING = -**********
SPAPI_E_INVALID_MACHINENAME = -**********
SPAPI_E_REMOTE_COMM_FAILURE = -**********
SPAPI_E_MACHINE_UNAVAILABLE = -**********
SPAPI_E_NO_CONFIGMGR_SERVICES = -**********
SPAPI_E_INVALID_PROPPAGE_PROVIDER = -**********
SPAPI_E_NO_SUCH_DEVICE_INTERFACE = -**********
SPAPI_E_DI_POSTPROCESSING_REQUIRED = -**********
SPAPI_E_INVALID_COINSTALLER = -**********
SPAPI_E_NO_COMPAT_DRIVERS = -**********
SPAPI_E_NO_DEVICE_ICON = -**********
SPAPI_E_INVALID_INF_LOGCONFIG = -**********
SPAPI_E_DI_DONT_INSTALL = -**********
SPAPI_E_INVALID_FILTER_DRIVER = -**********
SPAPI_E_NON_WINDOWS_NT_DRIVER = -**********
SPAPI_E_NON_WINDOWS_DRIVER = -**********
SPAPI_E_NO_CATALOG_FOR_OEM_INF = -**********
SPAPI_E_DEVINSTALL_QUEUE_NONNATIVE = -**********
SPAPI_E_NOT_DISABLEABLE = -**********
SPAPI_E_CANT_REMOVE_DEVINST = -**********
SPAPI_E_INVALID_TARGET = -**********
SPAPI_E_DRIVER_NONNATIVE = -**********
SPAPI_E_IN_WOW64 = -**********
SPAPI_E_SET_SYSTEM_RESTORE_POINT = -**********
SPAPI_E_INCORRECTLY_COPIED_INF = -**********
SPAPI_E_SCE_DISABLED = -**********
SPAPI_E_UNKNOWN_EXCEPTION = -**********
SPAPI_E_PNP_REGISTRY_ERROR = -**********
SPAPI_E_REMOTE_REQUEST_UNSUPPORTED = -**********
SPAPI_E_NOT_AN_INSTALLED_OEM_INF = -2146500036
SPAPI_E_INF_IN_USE_BY_DEVICES = -2146500035
SPAPI_E_DI_FUNCTION_OBSOLETE = -2146500034
SPAPI_E_NO_AUTHENTICODE_CATALOG = -2146500033
SPAPI_E_AUTHENTICODE_DISALLOWED = -2146500032
SPAPI_E_AUTHENTICODE_TRUSTED_PUBLISHER = -2146500031
SPAPI_E_AUTHENTICODE_TRUST_NOT_ESTABLISHED = -2146500030
SPAPI_E_AUTHENTICODE_PUBLISHER_NOT_TRUSTED = -2146500029
SPAPI_E_SIGNATURE_OSATTRIBUTE_MISMATCH = -2146500028
SPAPI_E_ONLY_VALIDATE_VIA_AUTHENTICODE = -2146500027
SPAPI_E_DEVICE_INSTALLER_NOT_READY = -2146500026
SPAPI_E_DRIVER_STORE_ADD_FAILED = -2146500025
SPAPI_E_DEVICE_INSTALL_BLOCKED = -2146500024
SPAPI_E_DRIVER_INSTALL_BLOCKED = -2146500023
SPAPI_E_WRONG_INF_TYPE = -2146500022
SPAPI_E_FILE_HASH_NOT_IN_CATALOG = -2146500021
SPAPI_E_DRIVER_STORE_DELETE_FAILED = -2146500020
SPAPI_E_UNRECOVERABLE_STACK_OVERFLOW = -2146499840
SPAPI_E_ERROR_NOT_INSTALLED = -2146496512
SCARD_S_SUCCESS = NO_ERROR
SCARD_F_INTERNAL_ERROR = -2146435071
SCARD_E_CANCELLED = -2146435070
SCARD_E_INVALID_HANDLE = -2146435069
SCARD_E_INVALID_PARAMETER = -2146435068
SCARD_E_INVALID_TARGET = -2146435067
SCARD_E_NO_MEMORY = -2146435066
SCARD_F_WAITED_TOO_LONG = -2146435065
SCARD_E_INSUFFICIENT_BUFFER = -2146435064
SCARD_E_UNKNOWN_READER = -2146435063
SCARD_E_TIMEOUT = -2146435062
SCARD_E_SHARING_VIOLATION = -2146435061
SCARD_E_NO_SMARTCARD = -2146435060
SCARD_E_UNKNOWN_CARD = -2146435059
SCARD_E_CANT_DISPOSE = -2146435058
SCARD_E_PROTO_MISMATCH = -2146435057
SCARD_E_NOT_READY = -2146435056
SCARD_E_INVALID_VALUE = -2146435055
SCARD_E_SYSTEM_CANCELLED = -2146435054
SCARD_F_COMM_ERROR = -2146435053
SCARD_F_UNKNOWN_ERROR = -2146435052
SCARD_E_INVALID_ATR = -2146435051
SCARD_E_NOT_TRANSACTED = -2146435050
SCARD_E_READER_UNAVAILABLE = -2146435049
SCARD_P_SHUTDOWN = -2146435048
SCARD_E_PCI_TOO_SMALL = -2146435047
SCARD_E_READER_UNSUPPORTED = -2146435046
SCARD_E_DUPLICATE_READER = -2146435045
SCARD_E_CARD_UNSUPPORTED = -2146435044
SCARD_E_NO_SERVICE = -2146435043
SCARD_E_SERVICE_STOPPED = -2146435042
SCARD_E_UNEXPECTED = -2146435041
SCARD_E_ICC_INSTALLATION = -2146435040
SCARD_E_ICC_CREATEORDER = -2146435039
SCARD_E_UNSUPPORTED_FEATURE = -2146435038
SCARD_E_DIR_NOT_FOUND = -2146435037
SCARD_E_FILE_NOT_FOUND = -2146435036
SCARD_E_NO_DIR = -2146435035
SCARD_E_NO_FILE = -2146435034
SCARD_E_NO_ACCESS = -2146435033
SCARD_E_WRITE_TOO_MANY = -2146435032
SCARD_E_BAD_SEEK = -2146435031
SCARD_E_INVALID_CHV = -2146435030
SCARD_E_UNKNOWN_RES_MNG = -2146435029
SCARD_E_NO_SUCH_CERTIFICATE = -2146435028
SCARD_E_CERTIFICATE_UNAVAILABLE = -2146435027
SCARD_E_NO_READERS_AVAILABLE = -2146435026
SCARD_E_COMM_DATA_LOST = -2146435025
SCARD_E_NO_KEY_CONTAINER = -2146435024
SCARD_E_SERVER_TOO_BUSY = -2146435023
SCARD_E_PIN_CACHE_EXPIRED = -2146435022
SCARD_E_NO_PIN_CACHE = -2146435021
SCARD_E_READ_ONLY_CARD = -2146435020
SCARD_W_UNSUPPORTED_CARD = -2146434971
SCARD_W_UNRESPONSIVE_CARD = -2146434970
SCARD_W_UNPOWERED_CARD = -2146434969
SCARD_W_RESET_CARD = -2146434968
SCARD_W_REMOVED_CARD = -2146434967
SCARD_W_SECURITY_VIOLATION = -2146434966
SCARD_W_WRONG_CHV = -2146434965
SCARD_W_CHV_BLOCKED = -2146434964
SCARD_W_EOF = -2146434963
SCARD_W_CANCELLED_BY_USER = -2146434962
SCARD_W_CARD_NOT_AUTHENTICATED = -2146434961
SCARD_W_CACHE_ITEM_NOT_FOUND = -2146434960
SCARD_W_CACHE_ITEM_STALE = -2146434959
SCARD_W_CACHE_ITEM_TOO_BIG = -2146434958
COMADMIN_E_OBJECTERRORS = -2146368511
COMADMIN_E_OBJECTINVALID = -2146368510
COMADMIN_E_KEYMISSING = -2146368509
COMADMIN_E_ALREADYINSTALLED = -2146368508
COMADMIN_E_APP_FILE_WRITEFAIL = -2146368505
COMADMIN_E_APP_FILE_READFAIL = -2146368504
COMADMIN_E_APP_FILE_VERSION = -2146368503
COMADMIN_E_BADPATH = -2146368502
COMADMIN_E_APPLICATIONEXISTS = -2146368501
COMADMIN_E_ROLEEXISTS = -2146368500
COMADMIN_E_CANTCOPYFILE = -2146368499
COMADMIN_E_NOUSER = -2146368497
COMADMIN_E_INVALIDUSERIDS = -2146368496
COMADMIN_E_NOREGISTRYCLSID = -2146368495
COMADMIN_E_BADREGISTRYPROGID = -2146368494
COMADMIN_E_AUTHENTICATIONLEVEL = -2146368493
COMADMIN_E_USERPASSWDNOTVALID = -2146368492
COMADMIN_E_CLSIDORIIDMISMATCH = -2146368488
COMADMIN_E_REMOTEINTERFACE = -2146368487
COMADMIN_E_DLLREGISTERSERVER = -2146368486
COMADMIN_E_NOSERVERSHARE = -2146368485
COMADMIN_E_DLLLOADFAILED = -2146368483
COMADMIN_E_BADREGISTRYLIBID = -2146368482
COMADMIN_E_APPDIRNOTFOUND = -2146368481
COMADMIN_E_REGISTRARFAILED = -2146368477
COMADMIN_E_COMPFILE_DOESNOTEXIST = -2146368476
COMADMIN_E_COMPFILE_LOADDLLFAIL = -2146368475
COMADMIN_E_COMPFILE_GETCLASSOBJ = -2146368474
COMADMIN_E_COMPFILE_CLASSNOTAVAIL = -2146368473
COMADMIN_E_COMPFILE_BADTLB = -2146368472
COMADMIN_E_COMPFILE_NOTINSTALLABLE = -2146368471
COMADMIN_E_NOTCHANGEABLE = -2146368470
COMADMIN_E_NOTDELETEABLE = -2146368469
COMADMIN_E_SESSION = -2146368468
COMADMIN_E_COMP_MOVE_LOCKED = -2146368467
COMADMIN_E_COMP_MOVE_BAD_DEST = -2146368466
COMADMIN_E_REGISTERTLB = -2146368464
COMADMIN_E_SYSTEMAPP = -2146368461
COMADMIN_E_COMPFILE_NOREGISTRAR = -2146368460
COMADMIN_E_COREQCOMPINSTALLED = -2146368459
COMADMIN_E_SERVICENOTINSTALLED = -2146368458
COMADMIN_E_PROPERTYSAVEFAILED = -2146368457
COMADMIN_E_OBJECTEXISTS = -2146368456
COMADMIN_E_COMPONENTEXISTS = -2146368455
COMADMIN_E_REGFILE_CORRUPT = -2146368453
COMADMIN_E_PROPERTY_OVERFLOW = -2146368452
COMADMIN_E_NOTINREGISTRY = -2146368450
COMADMIN_E_OBJECTNOTPOOLABLE = -2146368449
COMADMIN_E_APPLID_MATCHES_CLSID = -2146368442
COMADMIN_E_ROLE_DOES_NOT_EXIST = -2146368441
COMADMIN_E_START_APP_NEEDS_COMPONENTS = -2146368440
COMADMIN_E_REQUIRES_DIFFERENT_PLATFORM = -2146368439
COMADMIN_E_CAN_NOT_EXPORT_APP_PROXY = -2146368438
COMADMIN_E_CAN_NOT_START_APP = -2146368437
COMADMIN_E_CAN_NOT_EXPORT_SYS_APP = -2146368436
COMADMIN_E_CANT_SUBSCRIBE_TO_COMPONENT = -2146368435
COMADMIN_E_EVENTCLASS_CANT_BE_SUBSCRIBER = -2146368434
COMADMIN_E_LIB_APP_PROXY_INCOMPATIBLE = -2146368433
COMADMIN_E_BASE_PARTITION_ONLY = -2146368432
COMADMIN_E_START_APP_DISABLED = -2146368431
COMADMIN_E_CAT_DUPLICATE_PARTITION_NAME = -2146368425
COMADMIN_E_CAT_INVALID_PARTITION_NAME = -2146368424
COMADMIN_E_CAT_PARTITION_IN_USE = -2146368423
COMADMIN_E_FILE_PARTITION_DUPLICATE_FILES = -2146368422
COMADMIN_E_CAT_IMPORTED_COMPONENTS_NOT_ALLOWED = -2146368421
COMADMIN_E_AMBIGUOUS_APPLICATION_NAME = -2146368420
COMADMIN_E_AMBIGUOUS_PARTITION_NAME = -2146368419
COMADMIN_E_REGDB_NOTINITIALIZED = -2146368398
COMADMIN_E_REGDB_NOTOPEN = -2146368397
COMADMIN_E_REGDB_SYSTEMERR = -2146368396
COMADMIN_E_REGDB_ALREADYRUNNING = -2146368395
COMADMIN_E_MIG_VERSIONNOTSUPPORTED = -2146368384
COMADMIN_E_MIG_SCHEMANOTFOUND = -2146368383
COMADMIN_E_CAT_BITNESSMISMATCH = -2146368382
COMADMIN_E_CAT_UNACCEPTABLEBITNESS = -2146368381
COMADMIN_E_CAT_WRONGAPPBITNESS = -2146368380
COMADMIN_E_CAT_PAUSE_RESUME_NOT_SUPPORTED = -2146368379
COMADMIN_E_CAT_SERVERFAULT = -2146368378
COMQC_E_APPLICATION_NOT_QUEUED = -2146368000
COMQC_E_NO_QUEUEABLE_INTERFACES = -2146367999
COMQC_E_QUEUING_SERVICE_NOT_AVAILABLE = -2146367998
COMQC_E_NO_IPERSISTSTREAM = -2146367997
COMQC_E_BAD_MESSAGE = -2146367996
COMQC_E_UNAUTHENTICATED = -2146367995
COMQC_E_UNTRUSTED_ENQUEUER = -2146367994
MSDTC_E_DUPLICATE_RESOURCE = -2146367743
COMADMIN_E_OBJECT_PARENT_MISSING = -2146367480
COMADMIN_E_OBJECT_DOES_NOT_EXIST = -2146367479
COMADMIN_E_APP_NOT_RUNNING = -2146367478
COMADMIN_E_INVALID_PARTITION = -2146367477
COMADMIN_E_SVCAPP_NOT_POOLABLE_OR_RECYCLABLE = -2146367475
COMADMIN_E_USER_IN_SET = -2146367474
COMADMIN_E_CANTRECYCLELIBRARYAPPS = -2146367473
COMADMIN_E_CANTRECYCLESERVICEAPPS = -2146367471
COMADMIN_E_PROCESSALREADYRECYCLED = -2146367470
COMADMIN_E_PAUSEDPROCESSMAYNOTBERECYCLED = -2146367469
COMADMIN_E_CANTMAKEINPROCSERVICE = -2146367468
COMADMIN_E_PROGIDINUSEBYCLSID = -2146367467
COMADMIN_E_DEFAULT_PARTITION_NOT_IN_SET = -2146367466
COMADMIN_E_RECYCLEDPROCESSMAYNOTBEPAUSED = -2146367465
COMADMIN_E_PARTITION_ACCESSDENIED = -2146367464
COMADMIN_E_PARTITION_MSI_ONLY = -2146367463
COMADMIN_E_LEGACYCOMPS_NOT_ALLOWED_IN_1_0_FORMAT = -2146367462
COMADMIN_E_LEGACYCOMPS_NOT_ALLOWED_IN_NONBASE_PARTITIONS = -2146367461
COMADMIN_E_COMP_MOVE_SOURCE = -2146367460
COMADMIN_E_COMP_MOVE_DEST = -2146367459
COMADMIN_E_COMP_MOVE_PRIVATE = -2146367458
COMADMIN_E_BASEPARTITION_REQUIRED_IN_SET = -2146367457
COMADMIN_E_CANNOT_ALIAS_EVENTCLASS = -2146367456
COMADMIN_E_PRIVATE_ACCESSDENIED = -2146367455
COMADMIN_E_SAFERINVALID = -2146367454
COMADMIN_E_REGISTRY_ACCESSDENIED = -2146367453
COMADMIN_E_PARTITIONS_DISABLED = -2146367452
MENROLL_E_DEVICE_MESSAGE_FORMAT_ERROR = -2145910783
MENROLL_E_DEVICE_AUTHENTICATION_ERROR = -2145910782
MENROLL_E_DEVICE_AUTHORIZATION_ERROR = -2145910781
MENROLL_E_DEVICE_CERTIFICATEREQUEST_ERROR = -2145910780
MENROLL_E_DEVICE_CONFIGMGRSERVER_ERROR = -2145910779
MENROLL_E_DEVICE_INTERNALSERVICE_ERROR = -2145910778
MENROLL_E_DEVICE_INVALIDSECURITY_ERROR = -2145910777
MENROLL_E_DEVICE_UNKNOWN_ERROR = -2145910776
MENROLL_E_ENROLLMENT_IN_PROGRESS = -2145910775
MENROLL_E_DEVICE_ALREADY_ENROLLED = -**********
MENROLL_E_DISCOVERY_SEC_CERT_DATE_INVALID = -**********
MENROLL_E_PASSWORD_NEEDED = -**********
MENROLL_E_WAB_ERROR = -**********
MENROLL_E_CONNECTIVITY = -**********
MENROLL_S_ENROLLMENT_SUSPENDED = 0x00180011
MENROLL_E_INVALIDSSLCERT = -**********
MENROLL_E_DEVICECAPREACHED = -**********
MENROLL_E_DEVICENOTSUPPORTED = -**********
MENROLL_E_NOT_SUPPORTED = -**********
MENROLL_E_NOTELIGIBLETORENEW = -**********
MENROLL_E_INMAINTENANCE = -**********
MENROLL_E_USER_LICENSE = -**********
MENROLL_E_ENROLLMENTDATAINVALID = -**********
MENROLL_E_INSECUREREDIRECT = -**********
MENROLL_E_PLATFORM_WRONG_STATE = -**********
MENROLL_E_PLATFORM_LICENSE_ERROR = -**********
MENROLL_E_PLATFORM_UNKNOWN_ERROR = -**********
MENROLL_E_PROV_CSP_CERTSTORE = -**********
MENROLL_E_PROV_CSP_W7 = -**********
MENROLL_E_PROV_CSP_DMCLIENT = -**********
MENROLL_E_PROV_CSP_PFW = -**********
MENROLL_E_PROV_CSP_MISC = -**********
MENROLL_E_PROV_UNKNOWN = -**********
MENROLL_E_PROV_SSLCERTNOTFOUND = -**********
MENROLL_E_PROV_CSP_APPMGMT = -**********
MENROLL_E_DEVICE_MANAGEMENT_BLOCKED = -**********
MENROLL_E_CERTPOLICY_PRIVATEKEYCREATION_FAILED = -**********
MENROLL_E_CERTAUTH_FAILED_TO_FIND_CERT = -**********
MENROLL_E_EMPTY_MESSAGE = -**********
MENROLL_E_USER_CANCELLED = -**********
MENROLL_E_MDM_NOT_CONFIGURED = -**********
MENROLL_E_CUSTOMSERVERERROR = -**********
WER_S_REPORT_DEBUG = 0x001B0000
WER_S_REPORT_UPLOADED = 0x001B0001
WER_S_REPORT_QUEUED = 0x001B0002
WER_S_DISABLED = 0x001B0003
WER_S_SUSPENDED_UPLOAD = 0x001B0004
WER_S_DISABLED_QUEUE = 0x001B0005
WER_S_DISABLED_ARCHIVE = 0x001B0006
WER_S_REPORT_ASYNC = 0x001B0007
WER_S_IGNORE_ASSERT_INSTANCE = 0x001B0008
WER_S_IGNORE_ALL_ASSERTS = 0x001B0009
WER_S_ASSERT_CONTINUE = 0x001B000A
WER_S_THROTTLED = 0x001B000B
WER_S_REPORT_UPLOADED_CAB = 0x001B000C
WER_E_CRASH_FAILURE = -2145681408
WER_E_CANCELED = -2145681407
WER_E_NETWORK_FAILURE = -2145681406
WER_E_NOT_INITIALIZED = -2145681405
WER_E_ALREADY_REPORTING = -2145681404
WER_E_DUMP_THROTTLED = -2145681403
WER_E_INSUFFICIENT_CONSENT = -2145681402
WER_E_TOO_HEAVY = -2145681401


def FILTER_HRESULT_FROM_FLT_NTSTATUS(x):
    assert (x & 0xFFF0000) == 0x001C0000
    return ((x) & (-**********)) | (FACILITY_USERMODE_FILTER_MANAGER << 16)


ERROR_FLT_IO_COMPLETE = 0x001F0001
ERROR_FLT_NO_HANDLER_DEFINED = -2145452031
ERROR_FLT_CONTEXT_ALREADY_DEFINED = -2145452030
ERROR_FLT_INVALID_ASYNCHRONOUS_REQUEST = -2145452029
ERROR_FLT_DISALLOW_FAST_IO = -2145452028
ERROR_FLT_INVALID_NAME_REQUEST = -2145452027
ERROR_FLT_NOT_SAFE_TO_POST_OPERATION = -2145452026
ERROR_FLT_NOT_INITIALIZED = -2145452025
ERROR_FLT_FILTER_NOT_READY = -2145452024
ERROR_FLT_POST_OPERATION_CLEANUP = -2145452023
ERROR_FLT_INTERNAL_ERROR = -2145452022
ERROR_FLT_DELETING_OBJECT = -2145452021
ERROR_FLT_MUST_BE_NONPAGED_POOL = -2145452020
ERROR_FLT_DUPLICATE_ENTRY = -2145452019
ERROR_FLT_CBDQ_DISABLED = -2145452018
ERROR_FLT_DO_NOT_ATTACH = -2145452017
ERROR_FLT_DO_NOT_DETACH = -2145452016
ERROR_FLT_INSTANCE_ALTITUDE_COLLISION = -2145452015
ERROR_FLT_INSTANCE_NAME_COLLISION = -2145452014
ERROR_FLT_FILTER_NOT_FOUND = -2145452013
ERROR_FLT_VOLUME_NOT_FOUND = -2145452012
ERROR_FLT_INSTANCE_NOT_FOUND = -2145452011
ERROR_FLT_CONTEXT_ALLOCATION_NOT_FOUND = -2145452010
ERROR_FLT_INVALID_CONTEXT_REGISTRATION = -2145452009
ERROR_FLT_NAME_CACHE_MISS = -2145452008
ERROR_FLT_NO_DEVICE_OBJECT = -2145452007
ERROR_FLT_VOLUME_ALREADY_MOUNTED = -2145452006
ERROR_FLT_ALREADY_ENLISTED = -2145452005
ERROR_FLT_CONTEXT_ALREADY_LINKED = -2145452004
ERROR_FLT_NO_WAITER_FOR_REPLY = -2145452000
ERROR_FLT_REGISTRATION_BUSY = -2145451997
ERROR_FLT_WCOS_NOT_SUPPORTED = -2145451996
ERROR_HUNG_DISPLAY_DRIVER_THREAD = -2144993279
DWM_E_COMPOSITIONDISABLED = -2144980991
DWM_E_REMOTING_NOT_SUPPORTED = -2144980990
DWM_E_NO_REDIRECTION_SURFACE_AVAILABLE = -2144980989
DWM_E_NOT_QUEUING_PRESENTS = -2144980988
DWM_E_ADAPTER_NOT_FOUND = -2144980987
DWM_S_GDI_REDIRECTION_SURFACE = 0x00263005
DWM_E_TEXTURE_TOO_LARGE = -2144980985
DWM_S_GDI_REDIRECTION_SURFACE_BLT_VIA_GDI = 0x00263008
ERROR_MONITOR_NO_DESCRIPTOR = 0x00261001
ERROR_MONITOR_UNKNOWN_DESCRIPTOR_FORMAT = 0x00261002
ERROR_MONITOR_INVALID_DESCRIPTOR_CHECKSUM = -1071247357
ERROR_MONITOR_INVALID_STANDARD_TIMING_BLOCK = -1071247356
ERROR_MONITOR_WMI_DATABLOCK_REGISTRATION_FAILED = -1071247355
ERROR_MONITOR_INVALID_SERIAL_NUMBER_MONDSC_BLOCK = -1071247354
ERROR_MONITOR_INVALID_USER_FRIENDLY_MONDSC_BLOCK = -1071247353
ERROR_MONITOR_NO_MORE_DESCRIPTOR_DATA = -1071247352
ERROR_MONITOR_INVALID_DETAILED_TIMING_BLOCK = -1071247351
ERROR_MONITOR_INVALID_MANUFACTURE_DATE = -1071247350
ERROR_GRAPHICS_NOT_EXCLUSIVE_MODE_OWNER = -1071243264
ERROR_GRAPHICS_INSUFFICIENT_DMA_BUFFER = -1071243263
ERROR_GRAPHICS_INVALID_DISPLAY_ADAPTER = -1071243262
ERROR_GRAPHICS_ADAPTER_WAS_RESET = -1071243261
ERROR_GRAPHICS_INVALID_DRIVER_MODEL = -1071243260
ERROR_GRAPHICS_PRESENT_MODE_CHANGED = -1071243259
ERROR_GRAPHICS_PRESENT_OCCLUDED = -1071243258
ERROR_GRAPHICS_PRESENT_DENIED = -1071243257
ERROR_GRAPHICS_CANNOTCOLORCONVERT = -1071243256
ERROR_GRAPHICS_DRIVER_MISMATCH = -1071243255
ERROR_GRAPHICS_PARTIAL_DATA_POPULATED = 0x4026200A
ERROR_GRAPHICS_PRESENT_REDIRECTION_DISABLED = -1071243253
ERROR_GRAPHICS_PRESENT_UNOCCLUDED = -1071243252
ERROR_GRAPHICS_WINDOWDC_NOT_AVAILABLE = -1071243251
ERROR_GRAPHICS_WINDOWLESS_PRESENT_DISABLED = -1071243250
ERROR_GRAPHICS_PRESENT_INVALID_WINDOW = -1071243249
ERROR_GRAPHICS_PRESENT_BUFFER_NOT_BOUND = -**********
ERROR_GRAPHICS_VAIL_STATE_CHANGED = -**********
ERROR_GRAPHICS_INDIRECT_DISPLAY_ABANDON_SWAPCHAIN = -**********
ERROR_GRAPHICS_INDIRECT_DISPLAY_DEVICE_STOPPED = -**********
ERROR_GRAPHICS_VAIL_FAILED_TO_SEND_CREATE_SUPERWETINK_MESSAGE = -**********
ERROR_GRAPHICS_VAIL_FAILED_TO_SEND_DESTROY_SUPERWETINK_MESSAGE = -**********
ERROR_GRAPHICS_VAIL_FAILED_TO_SEND_COMPOSITION_WINDOW_DPI_MESSAGE = -**********
ERROR_GRAPHICS_LINK_CONFIGURATION_IN_PROGRESS = -**********
ERROR_GRAPHICS_MPO_ALLOCATION_UNPINNED = -**********
ERROR_GRAPHICS_NO_VIDEO_MEMORY = -**********
ERROR_GRAPHICS_CANT_LOCK_MEMORY = -**********
ERROR_GRAPHICS_ALLOCATION_BUSY = -**********
ERROR_GRAPHICS_TOO_MANY_REFERENCES = -**********
ERROR_GRAPHICS_TRY_AGAIN_LATER = -**********
ERROR_GRAPHICS_TRY_AGAIN_NOW = -**********
ERROR_GRAPHICS_ALLOCATION_INVALID = -**********
ERROR_GRAPHICS_UNSWIZZLING_APERTURE_UNAVAILABLE = -**********
ERROR_GRAPHICS_UNSWIZZLING_APERTURE_UNSUPPORTED = -**********
ERROR_GRAPHICS_CANT_EVICT_PINNED_ALLOCATION = -**********
ERROR_GRAPHICS_INVALID_ALLOCATION_USAGE = -**********
ERROR_GRAPHICS_CANT_RENDER_LOCKED_ALLOCATION = -**********
ERROR_GRAPHICS_ALLOCATION_CLOSED = -**********
ERROR_GRAPHICS_INVALID_ALLOCATION_INSTANCE = -**********
ERROR_GRAPHICS_INVALID_ALLOCATION_HANDLE = -**********
ERROR_GRAPHICS_WRONG_ALLOCATION_DEVICE = -**********
ERROR_GRAPHICS_ALLOCATION_CONTENT_LOST = -**********
ERROR_GRAPHICS_GPU_EXCEPTION_ON_DEVICE = -**********
ERROR_GRAPHICS_SKIP_ALLOCATION_PREPARATION = 0x40262201
ERROR_GRAPHICS_INVALID_VIDPN_TOPOLOGY = -1071242496
ERROR_GRAPHICS_VIDPN_TOPOLOGY_NOT_SUPPORTED = -1071242495
ERROR_GRAPHICS_VIDPN_TOPOLOGY_CURRENTLY_NOT_SUPPORTED = -1071242494
ERROR_GRAPHICS_INVALID_VIDPN = -1071242493
ERROR_GRAPHICS_INVALID_VIDEO_PRESENT_SOURCE = -1071242492
ERROR_GRAPHICS_INVALID_VIDEO_PRESENT_TARGET = -1071242491
ERROR_GRAPHICS_VIDPN_MODALITY_NOT_SUPPORTED = -1071242490
ERROR_GRAPHICS_MODE_NOT_PINNED = 0x00262307
ERROR_GRAPHICS_INVALID_VIDPN_SOURCEMODESET = -1071242488
ERROR_GRAPHICS_INVALID_VIDPN_TARGETMODESET = -1071242487
ERROR_GRAPHICS_INVALID_FREQUENCY = -1071242486
ERROR_GRAPHICS_INVALID_ACTIVE_REGION = -1071242485
ERROR_GRAPHICS_INVALID_TOTAL_REGION = -1071242484
ERROR_GRAPHICS_INVALID_VIDEO_PRESENT_SOURCE_MODE = -1071242480
ERROR_GRAPHICS_INVALID_VIDEO_PRESENT_TARGET_MODE = -1071242479
ERROR_GRAPHICS_PINNED_MODE_MUST_REMAIN_IN_SET = -1071242478
ERROR_GRAPHICS_PATH_ALREADY_IN_TOPOLOGY = -1071242477
ERROR_GRAPHICS_MODE_ALREADY_IN_MODESET = -1071242476
ERROR_GRAPHICS_INVALID_VIDEOPRESENTSOURCESET = -1071242475
ERROR_GRAPHICS_INVALID_VIDEOPRESENTTARGETSET = -1071242474
ERROR_GRAPHICS_SOURCE_ALREADY_IN_SET = -1071242473
ERROR_GRAPHICS_TARGET_ALREADY_IN_SET = -1071242472
ERROR_GRAPHICS_INVALID_VIDPN_PRESENT_PATH = -1071242471
ERROR_GRAPHICS_NO_RECOMMENDED_VIDPN_TOPOLOGY = -1071242470
ERROR_GRAPHICS_INVALID_MONITOR_FREQUENCYRANGESET = -1071242469
ERROR_GRAPHICS_INVALID_MONITOR_FREQUENCYRANGE = -1071242468
ERROR_GRAPHICS_FREQUENCYRANGE_NOT_IN_SET = -1071242467
ERROR_GRAPHICS_NO_PREFERRED_MODE = 0x0026231E
ERROR_GRAPHICS_FREQUENCYRANGE_ALREADY_IN_SET = -1071242465
ERROR_GRAPHICS_STALE_MODESET = -1071242464
ERROR_GRAPHICS_INVALID_MONITOR_SOURCEMODESET = -1071242463
ERROR_GRAPHICS_INVALID_MONITOR_SOURCE_MODE = -1071242462
ERROR_GRAPHICS_NO_RECOMMENDED_FUNCTIONAL_VIDPN = -1071242461
ERROR_GRAPHICS_MODE_ID_MUST_BE_UNIQUE = -1071242460
ERROR_GRAPHICS_EMPTY_ADAPTER_MONITOR_MODE_SUPPORT_INTERSECTION = -1071242459
ERROR_GRAPHICS_VIDEO_PRESENT_TARGETS_LESS_THAN_SOURCES = -1071242458
ERROR_GRAPHICS_PATH_NOT_IN_TOPOLOGY = -1071242457
ERROR_GRAPHICS_ADAPTER_MUST_HAVE_AT_LEAST_ONE_SOURCE = -1071242456
ERROR_GRAPHICS_ADAPTER_MUST_HAVE_AT_LEAST_ONE_TARGET = -1071242455
ERROR_GRAPHICS_INVALID_MONITORDESCRIPTORSET = -1071242454
ERROR_GRAPHICS_INVALID_MONITORDESCRIPTOR = -1071242453
ERROR_GRAPHICS_MONITORDESCRIPTOR_NOT_IN_SET = -1071242452
ERROR_GRAPHICS_MONITORDESCRIPTOR_ALREADY_IN_SET = -1071242451
ERROR_GRAPHICS_MONITORDESCRIPTOR_ID_MUST_BE_UNIQUE = -1071242450
ERROR_GRAPHICS_INVALID_VIDPN_TARGET_SUBSET_TYPE = -1071242449
ERROR_GRAPHICS_RESOURCES_NOT_RELATED = -1071242448
ERROR_GRAPHICS_SOURCE_ID_MUST_BE_UNIQUE = -1071242447
ERROR_GRAPHICS_TARGET_ID_MUST_BE_UNIQUE = -1071242446
ERROR_GRAPHICS_NO_AVAILABLE_VIDPN_TARGET = -1071242445
ERROR_GRAPHICS_MONITOR_COULD_NOT_BE_ASSOCIATED_WITH_ADAPTER = -1071242444
ERROR_GRAPHICS_NO_VIDPNMGR = -1071242443
ERROR_GRAPHICS_NO_ACTIVE_VIDPN = -1071242442
ERROR_GRAPHICS_STALE_VIDPN_TOPOLOGY = -1071242441
ERROR_GRAPHICS_MONITOR_NOT_CONNECTED = -1071242440
ERROR_GRAPHICS_SOURCE_NOT_IN_TOPOLOGY = -1071242439
ERROR_GRAPHICS_INVALID_PRIMARYSURFACE_SIZE = -1071242438
ERROR_GRAPHICS_INVALID_VISIBLEREGION_SIZE = -1071242437
ERROR_GRAPHICS_INVALID_STRIDE = -1071242436
ERROR_GRAPHICS_INVALID_PIXELFORMAT = -1071242435
ERROR_GRAPHICS_INVALID_COLORBASIS = -1071242434
ERROR_GRAPHICS_INVALID_PIXELVALUEACCESSMODE = -1071242433
ERROR_GRAPHICS_TARGET_NOT_IN_TOPOLOGY = -1071242432
ERROR_GRAPHICS_NO_DISPLAY_MODE_MANAGEMENT_SUPPORT = -1071242431
ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE = -1071242430
ERROR_GRAPHICS_CANT_ACCESS_ACTIVE_VIDPN = -1071242429
ERROR_GRAPHICS_INVALID_PATH_IMPORTANCE_ORDINAL = -1071242428
ERROR_GRAPHICS_INVALID_PATH_CONTENT_GEOMETRY_TRANSFORMATION = -1071242427
ERROR_GRAPHICS_PATH_CONTENT_GEOMETRY_TRANSFORMATION_NOT_SUPPORTED = -1071242426
ERROR_GRAPHICS_INVALID_GAMMA_RAMP = -1071242425
ERROR_GRAPHICS_GAMMA_RAMP_NOT_SUPPORTED = -1071242424
ERROR_GRAPHICS_MULTISAMPLING_NOT_SUPPORTED = -1071242423
ERROR_GRAPHICS_MODE_NOT_IN_MODESET = -1071242422
ERROR_GRAPHICS_DATASET_IS_EMPTY = 0x0026234B
ERROR_GRAPHICS_NO_MORE_ELEMENTS_IN_DATASET = 0x0026234C
ERROR_GRAPHICS_INVALID_VIDPN_TOPOLOGY_RECOMMENDATION_REASON = -1071242419
ERROR_GRAPHICS_INVALID_PATH_CONTENT_TYPE = -1071242418
ERROR_GRAPHICS_INVALID_COPYPROTECTION_TYPE = -1071242417
ERROR_GRAPHICS_UNASSIGNED_MODESET_ALREADY_EXISTS = -1071242416
ERROR_GRAPHICS_PATH_CONTENT_GEOMETRY_TRANSFORMATION_NOT_PINNED = 0x00262351
ERROR_GRAPHICS_INVALID_SCANLINE_ORDERING = -1071242414
ERROR_GRAPHICS_TOPOLOGY_CHANGES_NOT_ALLOWED = -1071242413
ERROR_GRAPHICS_NO_AVAILABLE_IMPORTANCE_ORDINALS = -1071242412
ERROR_GRAPHICS_INCOMPATIBLE_PRIVATE_FORMAT = -1071242411
ERROR_GRAPHICS_INVALID_MODE_PRUNING_ALGORITHM = -1071242410
ERROR_GRAPHICS_INVALID_MONITOR_CAPABILITY_ORIGIN = -1071242409
ERROR_GRAPHICS_INVALID_MONITOR_FREQUENCYRANGE_CONSTRAINT = -1071242408
ERROR_GRAPHICS_MAX_NUM_PATHS_REACHED = -1071242407
ERROR_GRAPHICS_CANCEL_VIDPN_TOPOLOGY_AUGMENTATION = -1071242406
ERROR_GRAPHICS_INVALID_CLIENT_TYPE = -1071242405
ERROR_GRAPHICS_CLIENTVIDPN_NOT_SET = -1071242404
ERROR_GRAPHICS_SPECIFIED_CHILD_ALREADY_CONNECTED = -1071242240
ERROR_GRAPHICS_CHILD_DESCRIPTOR_NOT_SUPPORTED = -1071242239
ERROR_GRAPHICS_UNKNOWN_CHILD_STATUS = 0x4026242F
ERROR_GRAPHICS_NOT_A_LINKED_ADAPTER = -1071242192
ERROR_GRAPHICS_LEADLINK_NOT_ENUMERATED = -1071242191
ERROR_GRAPHICS_CHAINLINKS_NOT_ENUMERATED = -1071242190
ERROR_GRAPHICS_ADAPTER_CHAIN_NOT_READY = -1071242189
ERROR_GRAPHICS_CHAINLINKS_NOT_STARTED = -1071242188
ERROR_GRAPHICS_CHAINLINKS_NOT_POWERED_ON = -1071242187
ERROR_GRAPHICS_INCONSISTENT_DEVICE_LINK_STATE = -1071242186
ERROR_GRAPHICS_LEADLINK_START_DEFERRED = 0x40262437
ERROR_GRAPHICS_NOT_POST_DEVICE_DRIVER = -1071242184
ERROR_GRAPHICS_POLLING_TOO_FREQUENTLY = 0x40262439
ERROR_GRAPHICS_START_DEFERRED = 0x4026243A
ERROR_GRAPHICS_ADAPTER_ACCESS_NOT_EXCLUDED = -1071242181
ERROR_GRAPHICS_DEPENDABLE_CHILD_STATUS = 0x4026243C
ERROR_GRAPHICS_OPM_NOT_SUPPORTED = -1071241984
ERROR_GRAPHICS_COPP_NOT_SUPPORTED = -1071241983
ERROR_GRAPHICS_UAB_NOT_SUPPORTED = -1071241982
ERROR_GRAPHICS_OPM_INVALID_ENCRYPTED_PARAMETERS = -1071241981
ERROR_GRAPHICS_OPM_NO_VIDEO_OUTPUTS_EXIST = -1071241979
ERROR_GRAPHICS_OPM_INTERNAL_ERROR = -1071241973
ERROR_GRAPHICS_OPM_INVALID_HANDLE = -1071241972
ERROR_GRAPHICS_PVP_INVALID_CERTIFICATE_LENGTH = -1071241970
ERROR_GRAPHICS_OPM_SPANNING_MODE_ENABLED = -1071241969
ERROR_GRAPHICS_OPM_THEATER_MODE_ENABLED = -1071241968
ERROR_GRAPHICS_PVP_HFS_FAILED = -1071241967
ERROR_GRAPHICS_OPM_INVALID_SRM = -1071241966
ERROR_GRAPHICS_OPM_OUTPUT_DOES_NOT_SUPPORT_HDCP = -1071241965
ERROR_GRAPHICS_OPM_OUTPUT_DOES_NOT_SUPPORT_ACP = -1071241964
ERROR_GRAPHICS_OPM_OUTPUT_DOES_NOT_SUPPORT_CGMSA = -1071241963
ERROR_GRAPHICS_OPM_HDCP_SRM_NEVER_SET = -1071241962
ERROR_GRAPHICS_OPM_RESOLUTION_TOO_HIGH = -1071241961
ERROR_GRAPHICS_OPM_ALL_HDCP_HARDWARE_ALREADY_IN_USE = -1071241960
ERROR_GRAPHICS_OPM_VIDEO_OUTPUT_NO_LONGER_EXISTS = -1071241958
ERROR_GRAPHICS_OPM_SESSION_TYPE_CHANGE_IN_PROGRESS = -1071241957
ERROR_GRAPHICS_OPM_VIDEO_OUTPUT_DOES_NOT_HAVE_COPP_SEMANTICS = -1071241956
ERROR_GRAPHICS_OPM_INVALID_INFORMATION_REQUEST = -1071241955
ERROR_GRAPHICS_OPM_DRIVER_INTERNAL_ERROR = -1071241954
ERROR_GRAPHICS_OPM_VIDEO_OUTPUT_DOES_NOT_HAVE_OPM_SEMANTICS = -1071241953
ERROR_GRAPHICS_OPM_SIGNALING_NOT_SUPPORTED = -1071241952
ERROR_GRAPHICS_OPM_INVALID_CONFIGURATION_REQUEST = -1071241951
ERROR_GRAPHICS_I2C_NOT_SUPPORTED = -1071241856
ERROR_GRAPHICS_I2C_DEVICE_DOES_NOT_EXIST = -1071241855
ERROR_GRAPHICS_I2C_ERROR_TRANSMITTING_DATA = -1071241854
ERROR_GRAPHICS_I2C_ERROR_RECEIVING_DATA = -1071241853
ERROR_GRAPHICS_DDCCI_VCP_NOT_SUPPORTED = -1071241852
ERROR_GRAPHICS_DDCCI_INVALID_DATA = -1071241851
ERROR_GRAPHICS_DDCCI_MONITOR_RETURNED_INVALID_TIMING_STATUS_BYTE = -1071241850
ERROR_GRAPHICS_MCA_INVALID_CAPABILITIES_STRING = -1071241849
ERROR_GRAPHICS_MCA_INTERNAL_ERROR = -1071241848
ERROR_GRAPHICS_DDCCI_INVALID_MESSAGE_COMMAND = -1071241847
ERROR_GRAPHICS_DDCCI_INVALID_MESSAGE_LENGTH = -1071241846
ERROR_GRAPHICS_DDCCI_INVALID_MESSAGE_CHECKSUM = -1071241845
ERROR_GRAPHICS_INVALID_PHYSICAL_MONITOR_HANDLE = -1071241844
ERROR_GRAPHICS_MONITOR_NO_LONGER_EXISTS = -1071241843
ERROR_GRAPHICS_DDCCI_CURRENT_CURRENT_VALUE_GREATER_THAN_MAXIMUM_VALUE = -1071241768
ERROR_GRAPHICS_MCA_INVALID_VCP_VERSION = -1071241767
ERROR_GRAPHICS_MCA_MONITOR_VIOLATES_MCCS_SPECIFICATION = -1071241766
ERROR_GRAPHICS_MCA_MCCS_VERSION_MISMATCH = -1071241765
ERROR_GRAPHICS_MCA_UNSUPPORTED_MCCS_VERSION = -1071241764
ERROR_GRAPHICS_MCA_INVALID_TECHNOLOGY_TYPE_RETURNED = -1071241762
ERROR_GRAPHICS_MCA_UNSUPPORTED_COLOR_TEMPERATURE = -1071241761
ERROR_GRAPHICS_ONLY_CONSOLE_SESSION_SUPPORTED = -1071241760
ERROR_GRAPHICS_NO_DISPLAY_DEVICE_CORRESPONDS_TO_NAME = -1071241759
ERROR_GRAPHICS_DISPLAY_DEVICE_NOT_ATTACHED_TO_DESKTOP = -1071241758
ERROR_GRAPHICS_MIRRORING_DEVICES_NOT_SUPPORTED = -1071241757
ERROR_GRAPHICS_INVALID_POINTER = -1071241756
ERROR_GRAPHICS_NO_MONITORS_CORRESPOND_TO_DISPLAY_DEVICE = -1071241755
ERROR_GRAPHICS_PARAMETER_ARRAY_TOO_SMALL = -1071241754
ERROR_GRAPHICS_INTERNAL_ERROR = -1071241753
ERROR_GRAPHICS_SESSION_TYPE_CHANGE_IN_PROGRESS = -1071249944
NAP_E_INVALID_PACKET = -2144927743
NAP_E_MISSING_SOH = -2144927742
NAP_E_CONFLICTING_ID = -2144927741
NAP_E_NO_CACHED_SOH = -2144927740
NAP_E_STILL_BOUND = -2144927739
NAP_E_NOT_REGISTERED = -2144927738
NAP_E_NOT_INITIALIZED = -2144927737
NAP_E_MISMATCHED_ID = -2144927736
NAP_E_NOT_PENDING = -2144927735
NAP_E_ID_NOT_FOUND = -2144927734
NAP_E_MAXSIZE_TOO_SMALL = -2144927733
NAP_E_SERVICE_NOT_RUNNING = -2144927732
NAP_S_CERT_ALREADY_PRESENT = 0x0027000D
NAP_E_ENTITY_DISABLED = -2144927730
NAP_E_NETSH_GROUPPOLICY_ERROR = -2144927729
NAP_E_TOO_MANY_CALLS = -2144927728
NAP_E_SHV_CONFIG_EXISTED = -2144927727
NAP_E_SHV_CONFIG_NOT_FOUND = -2144927726
NAP_E_SHV_TIMEOUT = -2144927725
TPM_E_ERROR_MASK = -2144862208
TPM_E_AUTHFAIL = -2144862207
TPM_E_BADINDEX = -2144862206
TPM_E_BAD_PARAMETER = -2144862205
TPM_E_AUDITFAILURE = -2144862204
TPM_E_CLEAR_DISABLED = -2144862203
TPM_E_DEACTIVATED = -2144862202
TPM_E_DISABLED = -2144862201
TPM_E_DISABLED_CMD = -2144862200
TPM_E_FAIL = -2144862199
TPM_E_BAD_ORDINAL = -2144862198
TPM_E_INSTALL_DISABLED = -2144862197
TPM_E_INVALID_KEYHANDLE = -2144862196
TPM_E_KEYNOTFOUND = -2144862195
TPM_E_INAPPROPRIATE_ENC = -2144862194
TPM_E_MIGRATEFAIL = -2144862193
TPM_E_INVALID_PCR_INFO = -2144862192
TPM_E_NOSPACE = -2144862191
TPM_E_NOSRK = -2144862190
TPM_E_NOTSEALED_BLOB = -2144862189
TPM_E_OWNER_SET = -2144862188
TPM_E_RESOURCES = -2144862187
TPM_E_SHORTRANDOM = -2144862186
TPM_E_SIZE = -2144862185
TPM_E_WRONGPCRVAL = -2144862184
TPM_E_BAD_PARAM_SIZE = -2144862183
TPM_E_SHA_THREAD = -2144862182
TPM_E_SHA_ERROR = -2144862181
TPM_E_FAILEDSELFTEST = -2144862180
TPM_E_AUTH2FAIL = -2144862179
TPM_E_BADTAG = -2144862178
TPM_E_IOERROR = -2144862177
TPM_E_ENCRYPT_ERROR = -2144862176
TPM_E_DECRYPT_ERROR = -2144862175
TPM_E_INVALID_AUTHHANDLE = -2144862174
TPM_E_NO_ENDORSEMENT = -2144862173
TPM_E_INVALID_KEYUSAGE = -2144862172
TPM_E_WRONG_ENTITYTYPE = -2144862171
TPM_E_INVALID_POSTINIT = -2144862170
TPM_E_INAPPROPRIATE_SIG = -2144862169
TPM_E_BAD_KEY_PROPERTY = -2144862168
TPM_E_BAD_MIGRATION = -2144862167
TPM_E_BAD_SCHEME = -2144862166
TPM_E_BAD_DATASIZE = -2144862165
TPM_E_BAD_MODE = -2144862164
TPM_E_BAD_PRESENCE = -2144862163
TPM_E_BAD_VERSION = -2144862162
TPM_E_NO_WRAP_TRANSPORT = -2144862161
TPM_E_AUDITFAIL_UNSUCCESSFUL = -2144862160
TPM_E_AUDITFAIL_SUCCESSFUL = -2144862159
TPM_E_NOTRESETABLE = -2144862158
TPM_E_NOTLOCAL = -2144862157
TPM_E_BAD_TYPE = -2144862156
TPM_E_INVALID_RESOURCE = -2144862155
TPM_E_NOTFIPS = -2144862154
TPM_E_INVALID_FAMILY = -2144862153
TPM_E_NO_NV_PERMISSION = -2144862152
TPM_E_REQUIRES_SIGN = -2144862151
TPM_E_KEY_NOTSUPPORTED = -2144862150
TPM_E_AUTH_CONFLICT = -2144862149
TPM_E_AREA_LOCKED = -2144862148
TPM_E_BAD_LOCALITY = -2144862147
TPM_E_READ_ONLY = -2144862146
TPM_E_PER_NOWRITE = -2144862145
TPM_E_FAMILYCOUNT = -2144862144
TPM_E_WRITE_LOCKED = -2144862143
TPM_E_BAD_ATTRIBUTES = -2144862142
TPM_E_INVALID_STRUCTURE = -2144862141
TPM_E_KEY_OWNER_CONTROL = -2144862140
TPM_E_BAD_COUNTER = -2144862139
TPM_E_NOT_FULLWRITE = -2144862138
TPM_E_CONTEXT_GAP = -2144862137
TPM_E_MAXNVWRITES = -2144862136
TPM_E_NOOPERATOR = -2144862135
TPM_E_RESOURCEMISSING = -2144862134
TPM_E_DELEGATE_LOCK = -2144862133
TPM_E_DELEGATE_FAMILY = -2144862132
TPM_E_DELEGATE_ADMIN = -2144862131
TPM_E_TRANSPORT_NOTEXCLUSIVE = -2144862130
TPM_E_OWNER_CONTROL = -2144862129
TPM_E_DAA_RESOURCES = -2144862128
TPM_E_DAA_INPUT_DATA0 = -2144862127
TPM_E_DAA_INPUT_DATA1 = -2144862126
TPM_E_DAA_ISSUER_SETTINGS = -2144862125
TPM_E_DAA_TPM_SETTINGS = -2144862124
TPM_E_DAA_STAGE = -2144862123
TPM_E_DAA_ISSUER_VALIDITY = -2144862122
TPM_E_DAA_WRONG_W = -2144862121
TPM_E_BAD_HANDLE = -2144862120
TPM_E_BAD_DELEGATE = -2144862119
TPM_E_BADCONTEXT = -2144862118
TPM_E_TOOMANYCONTEXTS = -2144862117
TPM_E_MA_TICKET_SIGNATURE = -2144862116
TPM_E_MA_DESTINATION = -2144862115
TPM_E_MA_SOURCE = -2144862114
TPM_E_MA_AUTHORITY = -2144862113
TPM_E_PERMANENTEK = -2144862111
TPM_E_BAD_SIGNATURE = -2144862110
TPM_E_NOCONTEXTSPACE = -2144862109
TPM_20_E_ASYMMETRIC = -2144862079
TPM_20_E_ATTRIBUTES = -2144862078
TPM_20_E_HASH = -2144862077
TPM_20_E_VALUE = -2144862076
TPM_20_E_HIERARCHY = -2144862075
TPM_20_E_KEY_SIZE = -2144862073
TPM_20_E_MGF = -2144862072
TPM_20_E_MODE = -2144862071
TPM_20_E_TYPE = -2144862070
TPM_20_E_HANDLE = -2144862069
TPM_20_E_KDF = -2144862068
TPM_20_E_RANGE = -2144862067
TPM_20_E_AUTH_FAIL = -2144862066
TPM_20_E_NONCE = -2144862065
TPM_20_E_PP = -2144862064
TPM_20_E_SCHEME = -2144862062
TPM_20_E_SIZE = -2144862059
TPM_20_E_SYMMETRIC = -2144862058
TPM_20_E_TAG = -2144862057
TPM_20_E_SELECTOR = -2144862056
TPM_20_E_INSUFFICIENT = -2144862054
TPM_20_E_SIGNATURE = -2144862053
TPM_20_E_KEY = -2144862052
TPM_20_E_POLICY_FAIL = -2144862051
TPM_20_E_INTEGRITY = -2144862049
TPM_20_E_TICKET = -2144862048
TPM_20_E_RESERVED_BITS = -2144862047
TPM_20_E_BAD_AUTH = -2144862046
TPM_20_E_EXPIRED = -2144862045
TPM_20_E_POLICY_CC = -2144862044
TPM_20_E_BINDING = -2144862043
TPM_20_E_CURVE = -2144862042
TPM_20_E_ECC_POINT = -2144862041
TPM_20_E_INITIALIZE = -2144861952
TPM_20_E_FAILURE = -2144861951
TPM_20_E_SEQUENCE = -2144861949
TPM_20_E_PRIVATE = -2144861941
TPM_20_E_HMAC = -2144861927
TPM_20_E_DISABLED = -2144861920
TPM_20_E_EXCLUSIVE = -2144861919
TPM_20_E_ECC_CURVE = -2144861917
TPM_20_E_AUTH_TYPE = -2144861916
TPM_20_E_AUTH_MISSING = -2144861915
TPM_20_E_POLICY = -2144861914
TPM_20_E_PCR = -2144861913
TPM_20_E_PCR_CHANGED = -2144861912
TPM_20_E_UPGRADE = -2144861907
TPM_20_E_TOO_MANY_CONTEXTS = -2144861906
TPM_20_E_AUTH_UNAVAILABLE = -2144861905
TPM_20_E_REBOOT = -2144861904
TPM_20_E_UNBALANCED = -2144861903
TPM_20_E_COMMAND_SIZE = -2144861886
TPM_20_E_COMMAND_CODE = -2144861885
TPM_20_E_AUTHSIZE = -2144861884
TPM_20_E_AUTH_CONTEXT = -2144861883
TPM_20_E_NV_RANGE = -2144861882
TPM_20_E_NV_SIZE = -2144861881
TPM_20_E_NV_LOCKED = -2144861880
TPM_20_E_NV_AUTHORIZATION = -2144861879
TPM_20_E_NV_UNINITIALIZED = -2144861878
TPM_20_E_NV_SPACE = -2144861877
TPM_20_E_NV_DEFINED = -2144861876
TPM_20_E_BAD_CONTEXT = -2144861872
TPM_20_E_CPHASH = -2144861871
TPM_20_E_PARENT = -2144861870
TPM_20_E_NEEDS_TEST = -2144861869
TPM_20_E_NO_RESULT = -2144861868
TPM_20_E_SENSITIVE = -2144861867
TPM_E_COMMAND_BLOCKED = -2144861184
TPM_E_INVALID_HANDLE = -2144861183
TPM_E_DUPLICATE_VHANDLE = -2144861182
TPM_E_EMBEDDED_COMMAND_BLOCKED = -2144861181
TPM_E_EMBEDDED_COMMAND_UNSUPPORTED = -2144861180
TPM_E_RETRY = -2144860160
TPM_E_NEEDS_SELFTEST = -2144860159
TPM_E_DOING_SELFTEST = -2144860158
TPM_E_DEFEND_LOCK_RUNNING = -2144860157
TPM_20_E_CONTEXT_GAP = -2144859903
TPM_20_E_OBJECT_MEMORY = -2144859902
TPM_20_E_SESSION_MEMORY = -2144859901
TPM_20_E_MEMORY = -2144859900
TPM_20_E_SESSION_HANDLES = -2144859899
TPM_20_E_OBJECT_HANDLES = -2144859898
TPM_20_E_LOCALITY = -2144859897
TPM_20_E_YIELDED = -2144859896
TPM_20_E_CANCELED = -2144859895
TPM_20_E_TESTING = -2144859894
TPM_20_E_NV_RATE = -2144859872
TPM_20_E_LOCKOUT = -2144859871
TPM_20_E_RETRY = -2144859870
TPM_20_E_NV_UNAVAILABLE = -2144859869
TBS_E_INTERNAL_ERROR = -2144845823
TBS_E_BAD_PARAMETER = -2144845822
TBS_E_INVALID_OUTPUT_POINTER = -2144845821
TBS_E_INVALID_CONTEXT = -2144845820
TBS_E_INSUFFICIENT_BUFFER = -2144845819
TBS_E_IOERROR = -2144845818
TBS_E_INVALID_CONTEXT_PARAM = -2144845817
TBS_E_SERVICE_NOT_RUNNING = -2144845816
TBS_E_TOO_MANY_TBS_CONTEXTS = -2144845815
TBS_E_TOO_MANY_RESOURCES = -2144845814
TBS_E_SERVICE_START_PENDING = -2144845813
TBS_E_PPI_NOT_SUPPORTED = -2144845812
TBS_E_COMMAND_CANCELED = -2144845811
TBS_E_BUFFER_TOO_LARGE = -2144845810
TBS_E_TPM_NOT_FOUND = -2144845809
TBS_E_SERVICE_DISABLED = -2144845808
TBS_E_NO_EVENT_LOG = -2144845807
TBS_E_ACCESS_DENIED = -2144845806
TBS_E_PROVISIONING_NOT_ALLOWED = -2144845805
TBS_E_PPI_FUNCTION_UNSUPPORTED = -2144845804
TBS_E_OWNERAUTH_NOT_FOUND = -2144845803
TBS_E_PROVISIONING_INCOMPLETE = -2144845802
TPMAPI_E_INVALID_STATE = -2144796416
TPMAPI_E_NOT_ENOUGH_DATA = -2144796415
TPMAPI_E_TOO_MUCH_DATA = -2144796414
TPMAPI_E_INVALID_OUTPUT_POINTER = -2144796413
TPMAPI_E_INVALID_PARAMETER = -2144796412
TPMAPI_E_OUT_OF_MEMORY = -2144796411
TPMAPI_E_BUFFER_TOO_SMALL = -2144796410
TPMAPI_E_INTERNAL_ERROR = -2144796409
TPMAPI_E_ACCESS_DENIED = -2144796408
TPMAPI_E_AUTHORIZATION_FAILED = -2144796407
TPMAPI_E_INVALID_CONTEXT_HANDLE = -2144796406
TPMAPI_E_TBS_COMMUNICATION_ERROR = -2144796405
TPMAPI_E_TPM_COMMAND_ERROR = -2144796404
TPMAPI_E_MESSAGE_TOO_LARGE = -2144796403
TPMAPI_E_INVALID_ENCODING = -2144796402
TPMAPI_E_INVALID_KEY_SIZE = -2144796401
TPMAPI_E_ENCRYPTION_FAILED = -2144796400
TPMAPI_E_INVALID_KEY_PARAMS = -2144796399
TPMAPI_E_INVALID_MIGRATION_AUTHORIZATION_BLOB = -2144796398
TPMAPI_E_INVALID_PCR_INDEX = -2144796397
TPMAPI_E_INVALID_DELEGATE_BLOB = -2144796396
TPMAPI_E_INVALID_CONTEXT_PARAMS = -2144796395
TPMAPI_E_INVALID_KEY_BLOB = -2144796394
TPMAPI_E_INVALID_PCR_DATA = -2144796393
TPMAPI_E_INVALID_OWNER_AUTH = -2144796392
TPMAPI_E_FIPS_RNG_CHECK_FAILED = -2144796391
TPMAPI_E_EMPTY_TCG_LOG = -2144796390
TPMAPI_E_INVALID_TCG_LOG_ENTRY = -2144796389
TPMAPI_E_TCG_SEPARATOR_ABSENT = -2144796388
TPMAPI_E_TCG_INVALID_DIGEST_ENTRY = -2144796387
TPMAPI_E_POLICY_DENIES_OPERATION = -2144796386
TPMAPI_E_NV_BITS_NOT_DEFINED = -2144796385
TPMAPI_E_NV_BITS_NOT_READY = -2144796384
TPMAPI_E_SEALING_KEY_NOT_AVAILABLE = -2144796383
TPMAPI_E_NO_AUTHORIZATION_CHAIN_FOUND = -2144796382
TPMAPI_E_SVN_COUNTER_NOT_AVAILABLE = -2144796381
TPMAPI_E_OWNER_AUTH_NOT_NULL = -2144796380
TPMAPI_E_ENDORSEMENT_AUTH_NOT_NULL = -2144796379
TPMAPI_E_AUTHORIZATION_REVOKED = -2144796378
TPMAPI_E_MALFORMED_AUTHORIZATION_KEY = -2144796377
TPMAPI_E_AUTHORIZING_KEY_NOT_SUPPORTED = -2144796376
TPMAPI_E_INVALID_AUTHORIZATION_SIGNATURE = -2144796375
TPMAPI_E_MALFORMED_AUTHORIZATION_POLICY = -2144796374
TPMAPI_E_MALFORMED_AUTHORIZATION_OTHER = -2144796373
TPMAPI_E_SEALING_KEY_CHANGED = -2144796372
TPMAPI_E_INVALID_TPM_VERSION = -2144796371
TPMAPI_E_INVALID_POLICYAUTH_BLOB_TYPE = -2144796370
TBSIMP_E_BUFFER_TOO_SMALL = -2144796160
TBSIMP_E_CLEANUP_FAILED = -2144796159
TBSIMP_E_INVALID_CONTEXT_HANDLE = -2144796158
TBSIMP_E_INVALID_CONTEXT_PARAM = -2144796157
TBSIMP_E_TPM_ERROR = -2144796156
TBSIMP_E_HASH_BAD_KEY = -2144796155
TBSIMP_E_DUPLICATE_VHANDLE = -2144796154
TBSIMP_E_INVALID_OUTPUT_POINTER = -2144796153
TBSIMP_E_INVALID_PARAMETER = -2144796152
TBSIMP_E_RPC_INIT_FAILED = -2144796151
TBSIMP_E_SCHEDULER_NOT_RUNNING = -2144796150
TBSIMP_E_COMMAND_CANCELED = -2144796149
TBSIMP_E_OUT_OF_MEMORY = -2144796148
TBSIMP_E_LIST_NO_MORE_ITEMS = -2144796147
TBSIMP_E_LIST_NOT_FOUND = -2144796146
TBSIMP_E_NOT_ENOUGH_SPACE = -2144796145
TBSIMP_E_NOT_ENOUGH_TPM_CONTEXTS = -2144796144
TBSIMP_E_COMMAND_FAILED = -2144796143
TBSIMP_E_UNKNOWN_ORDINAL = -2144796142
TBSIMP_E_RESOURCE_EXPIRED = -2144796141
TBSIMP_E_INVALID_RESOURCE = -2144796140
TBSIMP_E_NOTHING_TO_UNLOAD = -2144796139
TBSIMP_E_HASH_TABLE_FULL = -2144796138
TBSIMP_E_TOO_MANY_TBS_CONTEXTS = -2144796137
TBSIMP_E_TOO_MANY_RESOURCES = -2144796136
TBSIMP_E_PPI_NOT_SUPPORTED = -2144796135
TBSIMP_E_TPM_INCOMPATIBLE = -2144796134
TBSIMP_E_NO_EVENT_LOG = -2144796133
TPM_E_PPI_ACPI_FAILURE = -2144795904
TPM_E_PPI_USER_ABORT = -2144795903
TPM_E_PPI_BIOS_FAILURE = -2144795902
TPM_E_PPI_NOT_SUPPORTED = -2144795901
TPM_E_PPI_BLOCKED_IN_BIOS = -2144795900
TPM_E_PCP_ERROR_MASK = -2144795648
TPM_E_PCP_DEVICE_NOT_READY = -2144795647
TPM_E_PCP_INVALID_HANDLE = -2144795646
TPM_E_PCP_INVALID_PARAMETER = -2144795645
TPM_E_PCP_FLAG_NOT_SUPPORTED = -2144795644
TPM_E_PCP_NOT_SUPPORTED = -2144795643
TPM_E_PCP_BUFFER_TOO_SMALL = -2144795642
TPM_E_PCP_INTERNAL_ERROR = -2144795641
TPM_E_PCP_AUTHENTICATION_FAILED = -2144795640
TPM_E_PCP_AUTHENTICATION_IGNORED = -2144795639
TPM_E_PCP_POLICY_NOT_FOUND = -2144795638
TPM_E_PCP_PROFILE_NOT_FOUND = -2144795637
TPM_E_PCP_VALIDATION_FAILED = -2144795636
TPM_E_PCP_WRONG_PARENT = -2144795634
TPM_E_KEY_NOT_LOADED = -2144795633
TPM_E_NO_KEY_CERTIFICATION = -2144795632
TPM_E_KEY_NOT_FINALIZED = -2144795631
TPM_E_ATTESTATION_CHALLENGE_NOT_SET = -2144795630
TPM_E_NOT_PCR_BOUND = -2144795629
TPM_E_KEY_ALREADY_FINALIZED = -2144795628
TPM_E_KEY_USAGE_POLICY_NOT_SUPPORTED = -2144795627
TPM_E_KEY_USAGE_POLICY_INVALID = -2144795626
TPM_E_SOFT_KEY_ERROR = -2144795625
TPM_E_KEY_NOT_AUTHENTICATED = -2144795624
TPM_E_PCP_KEY_NOT_AIK = -2144795623
TPM_E_KEY_NOT_SIGNING_KEY = -2144795622
TPM_E_LOCKED_OUT = -2144795621
TPM_E_CLAIM_TYPE_NOT_SUPPORTED = -2144795620
TPM_E_VERSION_NOT_SUPPORTED = -2144795619
TPM_E_BUFFER_LENGTH_MISMATCH = -2144795618
TPM_E_PCP_IFX_RSA_KEY_CREATION_BLOCKED = -2144795617
TPM_E_PCP_TICKET_MISSING = -2144795616
TPM_E_PCP_RAW_POLICY_NOT_SUPPORTED = -2144795615
TPM_E_PCP_KEY_HANDLE_INVALIDATED = -2144795614
TPM_E_PCP_UNSUPPORTED_PSS_SALT = 0x40290423
TPM_E_PCP_PLATFORM_CLAIM_MAY_BE_OUTDATED = 0x40290424
TPM_E_PCP_PLATFORM_CLAIM_OUTDATED = 0x40290425
TPM_E_PCP_PLATFORM_CLAIM_REBOOT = 0x40290426
TPM_E_ZERO_EXHAUST_ENABLED = -2144795392
TPM_E_PROVISIONING_INCOMPLETE = -2144795136
TPM_E_INVALID_OWNER_AUTH = -2144795135
TPM_E_TOO_MUCH_DATA = -2144795134
TPM_E_TPM_GENERATED_EPS = -2144795133
PLA_E_DCS_NOT_FOUND = -2144337918
PLA_E_DCS_IN_USE = -2144337750
PLA_E_TOO_MANY_FOLDERS = -2144337851
PLA_E_NO_MIN_DISK = -2144337808
PLA_E_DCS_ALREADY_EXISTS = -2144337737
PLA_S_PROPERTY_IGNORED = 0x00300100
PLA_E_PROPERTY_CONFLICT = -2144337663
PLA_E_DCS_SINGLETON_REQUIRED = -2144337662
PLA_E_CREDENTIALS_REQUIRED = -2144337661
PLA_E_DCS_NOT_RUNNING = -2144337660
PLA_E_CONFLICT_INCL_EXCL_API = -2144337659
PLA_E_NETWORK_EXE_NOT_VALID = -2144337658
PLA_E_EXE_ALREADY_CONFIGURED = -2144337657
PLA_E_EXE_PATH_NOT_VALID = -2144337656
PLA_E_DC_ALREADY_EXISTS = -2144337655
PLA_E_DCS_START_WAIT_TIMEOUT = -2144337654
PLA_E_DC_START_WAIT_TIMEOUT = -2144337653
PLA_E_REPORT_WAIT_TIMEOUT = -2144337652
PLA_E_NO_DUPLICATES = -2144337651
PLA_E_EXE_FULL_PATH_REQUIRED = -2144337650
PLA_E_INVALID_SESSION_NAME = -2144337649
PLA_E_PLA_CHANNEL_NOT_ENABLED = -2144337648
PLA_E_TASKSCHED_CHANNEL_NOT_ENABLED = -2144337647
PLA_E_RULES_MANAGER_FAILED = -2144337646
PLA_E_CABAPI_FAILURE = -2144337645
FVE_E_LOCKED_VOLUME = -2144272384
FVE_E_NOT_ENCRYPTED = -2144272383
FVE_E_NO_TPM_BIOS = -2144272382
FVE_E_NO_MBR_METRIC = -2144272381
FVE_E_NO_BOOTSECTOR_METRIC = -2144272380
FVE_E_NO_BOOTMGR_METRIC = -2144272379
FVE_E_WRONG_BOOTMGR = -2144272378
FVE_E_SECURE_KEY_REQUIRED = -2144272377
FVE_E_NOT_ACTIVATED = -2144272376
FVE_E_ACTION_NOT_ALLOWED = -2144272375
FVE_E_AD_SCHEMA_NOT_INSTALLED = -2144272374
FVE_E_AD_INVALID_DATATYPE = -2144272373
FVE_E_AD_INVALID_DATASIZE = -2144272372
FVE_E_AD_NO_VALUES = -2144272371
FVE_E_AD_ATTR_NOT_SET = -2144272370
FVE_E_AD_GUID_NOT_FOUND = -2144272369
FVE_E_BAD_INFORMATION = -2144272368
FVE_E_TOO_SMALL = -2144272367
FVE_E_SYSTEM_VOLUME = -2144272366
FVE_E_FAILED_WRONG_FS = -2144272365
FVE_E_BAD_PARTITION_SIZE = -2144272364
FVE_E_NOT_SUPPORTED = -2144272363
FVE_E_BAD_DATA = -2144272362
FVE_E_VOLUME_NOT_BOUND = -2144272361
FVE_E_TPM_NOT_OWNED = -2144272360
FVE_E_NOT_DATA_VOLUME = -2144272359
FVE_E_AD_INSUFFICIENT_BUFFER = -2144272358
FVE_E_CONV_READ = -2144272357
FVE_E_CONV_WRITE = -2144272356
FVE_E_KEY_REQUIRED = -2144272355
FVE_E_CLUSTERING_NOT_SUPPORTED = -2144272354
FVE_E_VOLUME_BOUND_ALREADY = -2144272353
FVE_E_OS_NOT_PROTECTED = -2144272352
FVE_E_PROTECTION_DISABLED = -2144272351
FVE_E_RECOVERY_KEY_REQUIRED = -2144272350
FVE_E_FOREIGN_VOLUME = -2144272349
FVE_E_OVERLAPPED_UPDATE = -2144272348
FVE_E_TPM_SRK_AUTH_NOT_ZERO = -2144272347
FVE_E_FAILED_SECTOR_SIZE = -2144272346
FVE_E_FAILED_AUTHENTICATION = -2144272345
FVE_E_NOT_OS_VOLUME = -2144272344
FVE_E_AUTOUNLOCK_ENABLED = -2144272343
FVE_E_WRONG_BOOTSECTOR = -2144272342
FVE_E_WRONG_SYSTEM_FS = -2144272341
FVE_E_POLICY_PASSWORD_REQUIRED = -2144272340
FVE_E_CANNOT_SET_FVEK_ENCRYPTED = -2144272339
FVE_E_CANNOT_ENCRYPT_NO_KEY = -2144272338
FVE_E_BOOTABLE_CDDVD = -2144272336
FVE_E_PROTECTOR_EXISTS = -2144272335
FVE_E_RELATIVE_PATH = -2144272334
FVE_E_PROTECTOR_NOT_FOUND = -2144272333
FVE_E_INVALID_KEY_FORMAT = -2144272332
FVE_E_INVALID_PASSWORD_FORMAT = -2144272331
FVE_E_FIPS_RNG_CHECK_FAILED = -2144272330
FVE_E_FIPS_PREVENTS_RECOVERY_PASSWORD = -2144272329
FVE_E_FIPS_PREVENTS_EXTERNAL_KEY_EXPORT = -2144272328
FVE_E_NOT_DECRYPTED = -2144272327
FVE_E_INVALID_PROTECTOR_TYPE = -2144272326
FVE_E_NO_PROTECTORS_TO_TEST = -2144272325
FVE_E_KEYFILE_NOT_FOUND = -**********
FVE_E_KEYFILE_INVALID = -**********
FVE_E_KEYFILE_NO_VMK = -**********
FVE_E_TPM_DISABLED = -**********
FVE_E_NOT_ALLOWED_IN_SAFE_MODE = -**********
FVE_E_TPM_INVALID_PCR = -**********
FVE_E_TPM_NO_VMK = -**********
FVE_E_PIN_INVALID = -**********
FVE_E_AUTH_INVALID_APPLICATION = -**********
FVE_E_AUTH_INVALID_CONFIG = -**********
FVE_E_FIPS_DISABLE_PROTECTION_NOT_ALLOWED = -**********
FVE_E_FS_NOT_EXTENDED = -**********
FVE_E_FIRMWARE_TYPE_NOT_SUPPORTED = -**********
FVE_E_NO_LICENSE = -**********
FVE_E_NOT_ON_STACK = -**********
FVE_E_FS_MOUNTED = -**********
FVE_E_TOKEN_NOT_IMPERSONATED = -**********
FVE_E_DRY_RUN_FAILED = -**********
FVE_E_REBOOT_REQUIRED = -**********
FVE_E_DEBUGGER_ENABLED = -**********
FVE_E_RAW_ACCESS = -**********
FVE_E_RAW_BLOCKED = -**********
FVE_E_BCD_APPLICATIONS_PATH_INCORRECT = -**********
FVE_E_NOT_ALLOWED_IN_VERSION = -**********
FVE_E_NO_AUTOUNLOCK_MASTER_KEY = -**********
FVE_E_MOR_FAILED = -**********
FVE_E_HIDDEN_VOLUME = -**********
FVE_E_TRANSIENT_STATE = -**********
FVE_E_PUBKEY_NOT_ALLOWED = -**********
FVE_E_VOLUME_HANDLE_OPEN = -**********
FVE_E_NO_FEATURE_LICENSE = -**********
FVE_E_INVALID_STARTUP_OPTIONS = -**********
FVE_E_POLICY_RECOVERY_PASSWORD_NOT_ALLOWED = -**********
FVE_E_POLICY_RECOVERY_PASSWORD_REQUIRED = -**********
FVE_E_POLICY_RECOVERY_KEY_NOT_ALLOWED = -**********
FVE_E_POLICY_RECOVERY_KEY_REQUIRED = -**********
FVE_E_POLICY_STARTUP_PIN_NOT_ALLOWED = -**********
FVE_E_POLICY_STARTUP_PIN_REQUIRED = -**********
FVE_E_POLICY_STARTUP_KEY_NOT_ALLOWED = -**********
FVE_E_POLICY_STARTUP_KEY_REQUIRED = -**********
FVE_E_POLICY_STARTUP_PIN_KEY_NOT_ALLOWED = -**********
FVE_E_POLICY_STARTUP_PIN_KEY_REQUIRED = -**********
FVE_E_POLICY_STARTUP_TPM_NOT_ALLOWED = -**********
FVE_E_POLICY_STARTUP_TPM_REQUIRED = -**********
FVE_E_POLICY_INVALID_PIN_LENGTH = -**********
FVE_E_KEY_PROTECTOR_NOT_SUPPORTED = -**********
FVE_E_POLICY_PASSPHRASE_NOT_ALLOWED = -**********
FVE_E_POLICY_PASSPHRASE_REQUIRED = -**********
FVE_E_FIPS_PREVENTS_PASSPHRASE = -**********
FVE_E_OS_VOLUME_PASSPHRASE_NOT_ALLOWED = -**********
FVE_E_INVALID_BITLOCKER_OID = -2144272274
FVE_E_VOLUME_TOO_SMALL = -2144272273
FVE_E_DV_NOT_SUPPORTED_ON_FS = -2144272272
FVE_E_DV_NOT_ALLOWED_BY_GP = -2144272271
FVE_E_POLICY_USER_CERTIFICATE_NOT_ALLOWED = -2144272270
FVE_E_POLICY_USER_CERTIFICATE_REQUIRED = -2144272269
FVE_E_POLICY_USER_CERT_MUST_BE_HW = -2144272268
FVE_E_POLICY_USER_CONFIGURE_FDV_AUTOUNLOCK_NOT_ALLOWED = -2144272267
FVE_E_POLICY_USER_CONFIGURE_RDV_AUTOUNLOCK_NOT_ALLOWED = -2144272266
FVE_E_POLICY_USER_CONFIGURE_RDV_NOT_ALLOWED = -2144272265
FVE_E_POLICY_USER_ENABLE_RDV_NOT_ALLOWED = -2144272264
FVE_E_POLICY_USER_DISABLE_RDV_NOT_ALLOWED = -2144272263
FVE_E_POLICY_INVALID_PASSPHRASE_LENGTH = -2144272256
FVE_E_POLICY_PASSPHRASE_TOO_SIMPLE = -2144272255
FVE_E_RECOVERY_PARTITION = -2144272254
FVE_E_POLICY_CONFLICT_FDV_RK_OFF_AUK_ON = -2144272253
FVE_E_POLICY_CONFLICT_RDV_RK_OFF_AUK_ON = -2144272252
FVE_E_NON_BITLOCKER_OID = -2144272251
FVE_E_POLICY_PROHIBITS_SELFSIGNED = -2144272250
FVE_E_POLICY_CONFLICT_RO_AND_STARTUP_KEY_REQUIRED = -2144272249
FVE_E_CONV_RECOVERY_FAILED = -2144272248
FVE_E_VIRTUALIZED_SPACE_TOO_BIG = -2144272247
FVE_E_POLICY_CONFLICT_OSV_RP_OFF_ADB_ON = -2144272240
FVE_E_POLICY_CONFLICT_FDV_RP_OFF_ADB_ON = -2144272239
FVE_E_POLICY_CONFLICT_RDV_RP_OFF_ADB_ON = -2144272238
FVE_E_NON_BITLOCKER_KU = -2144272237
FVE_E_PRIVATEKEY_AUTH_FAILED = -2144272236
FVE_E_REMOVAL_OF_DRA_FAILED = -2144272235
FVE_E_OPERATION_NOT_SUPPORTED_ON_VISTA_VOLUME = -2144272234
FVE_E_CANT_LOCK_AUTOUNLOCK_ENABLED_VOLUME = -2144272233
FVE_E_FIPS_HASH_KDF_NOT_ALLOWED = -2144272232
FVE_E_ENH_PIN_INVALID = -2144272231
FVE_E_INVALID_PIN_CHARS = -2144272230
FVE_E_INVALID_DATUM_TYPE = -2144272229
FVE_E_EFI_ONLY = -2144272228
FVE_E_MULTIPLE_NKP_CERTS = -2144272227
FVE_E_REMOVAL_OF_NKP_FAILED = -2144272226
FVE_E_INVALID_NKP_CERT = -2144272225
FVE_E_NO_EXISTING_PIN = -2144272224
FVE_E_PROTECTOR_CHANGE_PIN_MISMATCH = -2144272223
FVE_E_PIN_PROTECTOR_CHANGE_BY_STD_USER_DISALLOWED = -2144272222
FVE_E_PROTECTOR_CHANGE_MAX_PIN_CHANGE_ATTEMPTS_REACHED = -2144272221
FVE_E_POLICY_PASSPHRASE_REQUIRES_ASCII = -2144272220
FVE_E_FULL_ENCRYPTION_NOT_ALLOWED_ON_TP_STORAGE = -2144272219
FVE_E_WIPE_NOT_ALLOWED_ON_TP_STORAGE = -2144272218
FVE_E_KEY_LENGTH_NOT_SUPPORTED_BY_EDRIVE = -2144272217
FVE_E_NO_EXISTING_PASSPHRASE = -2144272216
FVE_E_PROTECTOR_CHANGE_PASSPHRASE_MISMATCH = -2144272215
FVE_E_PASSPHRASE_TOO_LONG = -2144272214
FVE_E_NO_PASSPHRASE_WITH_TPM = -2144272213
FVE_E_NO_TPM_WITH_PASSPHRASE = -2144272212
FVE_E_NOT_ALLOWED_ON_CSV_STACK = -2144272211
FVE_E_NOT_ALLOWED_ON_CLUSTER = -2144272210
FVE_E_EDRIVE_NO_FAILOVER_TO_SW = -2144272209
FVE_E_EDRIVE_BAND_IN_USE = -2144272208
FVE_E_EDRIVE_DISALLOWED_BY_GP = -2144272207
FVE_E_EDRIVE_INCOMPATIBLE_VOLUME = -2144272206
FVE_E_NOT_ALLOWED_TO_UPGRADE_WHILE_CONVERTING = -2144272205
FVE_E_EDRIVE_DV_NOT_SUPPORTED = -2144272204
FVE_E_NO_PREBOOT_KEYBOARD_DETECTED = -2144272203
FVE_E_NO_PREBOOT_KEYBOARD_OR_WINRE_DETECTED = -2144272202
FVE_E_POLICY_REQUIRES_STARTUP_PIN_ON_TOUCH_DEVICE = -2144272201
FVE_E_POLICY_REQUIRES_RECOVERY_PASSWORD_ON_TOUCH_DEVICE = -**********
FVE_E_WIPE_CANCEL_NOT_APPLICABLE = -**********
FVE_E_SECUREBOOT_DISABLED = -**********
FVE_E_SECUREBOOT_CONFIGURATION_INVALID = -**********
FVE_E_EDRIVE_DRY_RUN_FAILED = -**********
FVE_E_SHADOW_COPY_PRESENT = -**********
FVE_E_POLICY_INVALID_ENHANCED_BCD_SETTINGS = -**********
FVE_E_EDRIVE_INCOMPATIBLE_FIRMWARE = -**********
FVE_E_PROTECTOR_CHANGE_MAX_PASSPHRASE_CHANGE_ATTEMPTS_REACHED = -**********
FVE_E_PASSPHRASE_PROTECTOR_CHANGE_BY_STD_USER_DISALLOWED = -**********
FVE_E_LIVEID_ACCOUNT_SUSPENDED = -**********
FVE_E_LIVEID_ACCOUNT_BLOCKED = -**********
FVE_E_NOT_PROVISIONED_ON_ALL_VOLUMES = -**********
FVE_E_DE_FIXED_DATA_NOT_SUPPORTED = -**********
FVE_E_DE_HARDWARE_NOT_COMPLIANT = -**********
FVE_E_DE_WINRE_NOT_CONFIGURED = -**********
FVE_E_DE_PROTECTION_SUSPENDED = -**********
FVE_E_DE_OS_VOLUME_NOT_PROTECTED = -**********
FVE_E_DE_DEVICE_LOCKEDOUT = -**********
FVE_E_DE_PROTECTION_NOT_YET_ENABLED = -**********
FVE_E_INVALID_PIN_CHARS_DETAILED = -**********
FVE_E_DEVICE_LOCKOUT_COUNTER_UNAVAILABLE = -**********
FVE_E_DEVICELOCKOUT_COUNTER_MISMATCH = -**********
FVE_E_BUFFER_TOO_LARGE = -**********
FVE_E_NO_SUCH_CAPABILITY_ON_TARGET = -**********
FVE_E_DE_PREVENTED_FOR_OS = -**********
FVE_E_DE_VOLUME_OPTED_OUT = -**********
FVE_E_DE_VOLUME_NOT_SUPPORTED = -**********
FVE_E_EOW_NOT_SUPPORTED_IN_VERSION = -**********
FVE_E_ADBACKUP_NOT_ENABLED = -**********
FVE_E_VOLUME_EXTEND_PREVENTS_EOW_DECRYPT = -**********
FVE_E_NOT_DE_VOLUME = -**********
FVE_E_PROTECTION_CANNOT_BE_DISABLED = -**********
FVE_E_OSV_KSR_NOT_ALLOWED = -2144272167
FVE_E_AD_BACKUP_REQUIRED_POLICY_NOT_SET_OS_DRIVE = -2144272166
FVE_E_AD_BACKUP_REQUIRED_POLICY_NOT_SET_FIXED_DRIVE = -2144272165
FVE_E_AD_BACKUP_REQUIRED_POLICY_NOT_SET_REMOVABLE_DRIVE = -2144272164
FVE_E_KEY_ROTATION_NOT_SUPPORTED = -2144272163
FVE_E_EXECUTE_REQUEST_SENT_TOO_SOON = -2144272162
FVE_E_KEY_ROTATION_NOT_ENABLED = -2144272161
FVE_E_DEVICE_NOT_JOINED = -2144272160
FVE_E_AAD_ENDPOINT_BUSY = -2144272159
FVE_E_INVALID_NBP_CERT = -2144272158
FVE_E_EDRIVE_BAND_ENUMERATION_FAILED = -2144272157
FVE_E_POLICY_ON_RDV_EXCLUSION_LIST = -2144272156
FVE_E_PREDICTED_TPM_PROTECTOR_NOT_SUPPORTED = -**********
FVE_E_SETUP_TPM_CALLBACK_NOT_SUPPORTED = -**********
FVE_E_TPM_CONTEXT_SETUP_NOT_SUPPORTED = -**********
FVE_E_UPDATE_INVALID_CONFIG = -**********
FVE_E_AAD_SERVER_FAIL_RETRY_AFTER = -**********
FVE_E_AAD_SERVER_FAIL_BACKOFF = -**********
FVE_E_DATASET_FULL = -**********
FVE_E_METADATA_FULL = -**********
FWP_E_CALLOUT_NOT_FOUND = -**********
FWP_E_CONDITION_NOT_FOUND = -**********
FWP_E_FILTER_NOT_FOUND = -**********
FWP_E_LAYER_NOT_FOUND = -**********
FWP_E_PROVIDER_NOT_FOUND = -**********
FWP_E_PROVIDER_CONTEXT_NOT_FOUND = -**********
FWP_E_SUBLAYER_NOT_FOUND = -**********
FWP_E_NOT_FOUND = -**********
FWP_E_ALREADY_EXISTS = -**********
FWP_E_IN_USE = -**********
FWP_E_DYNAMIC_SESSION_IN_PROGRESS = -**********
FWP_E_WRONG_SESSION = -**********
FWP_E_NO_TXN_IN_PROGRESS = -**********
FWP_E_TXN_IN_PROGRESS = -**********
FWP_E_TXN_ABORTED = -**********
FWP_E_SESSION_ABORTED = -**********
FWP_E_INCOMPATIBLE_TXN = -**********
FWP_E_TIMEOUT = -**********
FWP_E_NET_EVENTS_DISABLED = -**********
FWP_E_INCOMPATIBLE_LAYER = -**********
FWP_E_KM_CLIENTS_ONLY = -**********
FWP_E_LIFETIME_MISMATCH = -**********
FWP_E_BUILTIN_OBJECT = -**********
FWP_E_TOO_MANY_CALLOUTS = -**********
FWP_E_NOTIFICATION_DROPPED = -**********
FWP_E_TRAFFIC_MISMATCH = -**********
FWP_E_INCOMPATIBLE_SA_STATE = -**********
FWP_E_NULL_POINTER = -**********
FWP_E_INVALID_ENUMERATOR = -**********
FWP_E_INVALID_FLAGS = -**********
FWP_E_INVALID_NET_MASK = -**********
FWP_E_INVALID_RANGE = -**********
FWP_E_INVALID_INTERVAL = -2144206815
FWP_E_ZERO_LENGTH_ARRAY = -2144206814
FWP_E_NULL_DISPLAY_NAME = -2144206813
FWP_E_INVALID_ACTION_TYPE = -2144206812
FWP_E_INVALID_WEIGHT = -2144206811
FWP_E_MATCH_TYPE_MISMATCH = -2144206810
FWP_E_TYPE_MISMATCH = -2144206809
FWP_E_OUT_OF_BOUNDS = -**********
FWP_E_RESERVED = -**********
FWP_E_DUPLICATE_CONDITION = -**********
FWP_E_DUPLICATE_KEYMOD = -**********
FWP_E_ACTION_INCOMPATIBLE_WITH_LAYER = -**********
FWP_E_ACTION_INCOMPATIBLE_WITH_SUBLAYER = -**********
FWP_E_CONTEXT_INCOMPATIBLE_WITH_LAYER = -**********
FWP_E_CONTEXT_INCOMPATIBLE_WITH_CALLOUT = -**********
FWP_E_INCOMPATIBLE_AUTH_METHOD = -**********
FWP_E_INCOMPATIBLE_DH_GROUP = -**********
FWP_E_EM_NOT_SUPPORTED = -**********
FWP_E_NEVER_MATCH = -**********
FWP_E_PROVIDER_CONTEXT_MISMATCH = -**********
FWP_E_INVALID_PARAMETER = -**********
FWP_E_TOO_MANY_SUBLAYERS = -**********
FWP_E_CALLOUT_NOTIFICATION_FAILED = -**********
FWP_E_INVALID_AUTH_TRANSFORM = -**********
FWP_E_INVALID_CIPHER_TRANSFORM = -**********
FWP_E_INCOMPATIBLE_CIPHER_TRANSFORM = -**********
FWP_E_INVALID_TRANSFORM_COMBINATION = -**********
FWP_E_DUPLICATE_AUTH_METHOD = -**********
FWP_E_INVALID_TUNNEL_ENDPOINT = -**********
FWP_E_L2_DRIVER_NOT_READY = -**********
FWP_E_KEY_DICTATOR_ALREADY_REGISTERED = -**********
FWP_E_KEY_DICTATION_INVALID_KEYING_MATERIAL = -**********
FWP_E_CONNECTIONS_DISABLED = -**********
FWP_E_INVALID_DNS_NAME = -**********
FWP_E_STILL_ON = -**********
FWP_E_IKEEXT_NOT_RUNNING = -**********
FWP_E_DROP_NOICMP = -**********
WS_S_ASYNC = 0x003D0000
WS_S_END = 0x003D0001
WS_E_INVALID_FORMAT = -**********
WS_E_OBJECT_FAULTED = -**********
WS_E_NUMERIC_OVERFLOW = -**********
WS_E_INVALID_OPERATION = -**********
WS_E_OPERATION_ABORTED = -**********
WS_E_ENDPOINT_ACCESS_DENIED = -2143485947
WS_E_OPERATION_TIMED_OUT = -2143485946
WS_E_OPERATION_ABANDONED = -2143485945
WS_E_QUOTA_EXCEEDED = -2143485944
WS_E_NO_TRANSLATION_AVAILABLE = -2143485943
WS_E_SECURITY_VERIFICATION_FAILURE = -2143485942
WS_E_ADDRESS_IN_USE = -2143485941
WS_E_ADDRESS_NOT_AVAILABLE = -2143485940
WS_E_ENDPOINT_NOT_FOUND = -2143485939
WS_E_ENDPOINT_NOT_AVAILABLE = -2143485938
WS_E_ENDPOINT_FAILURE = -2143485937
WS_E_ENDPOINT_UNREACHABLE = -2143485936
WS_E_ENDPOINT_ACTION_NOT_SUPPORTED = -2143485935
WS_E_ENDPOINT_TOO_BUSY = -2143485934
WS_E_ENDPOINT_FAULT_RECEIVED = -2143485933
WS_E_ENDPOINT_DISCONNECTED = -2143485932
WS_E_PROXY_FAILURE = -2143485931
WS_E_PROXY_ACCESS_DENIED = -2143485930
WS_E_NOT_SUPPORTED = -2143485929
WS_E_PROXY_REQUIRES_BASIC_AUTH = -2143485928
WS_E_PROXY_REQUIRES_DIGEST_AUTH = -2143485927
WS_E_PROXY_REQUIRES_NTLM_AUTH = -2143485926
WS_E_PROXY_REQUIRES_NEGOTIATE_AUTH = -2143485925
WS_E_SERVER_REQUIRES_BASIC_AUTH = -2143485924
WS_E_SERVER_REQUIRES_DIGEST_AUTH = -2143485923
WS_E_SERVER_REQUIRES_NTLM_AUTH = -2143485922
WS_E_SERVER_REQUIRES_NEGOTIATE_AUTH = -2143485921
WS_E_INVALID_ENDPOINT_URL = -2143485920
WS_E_OTHER = -2143485919
WS_E_SECURITY_TOKEN_EXPIRED = -2143485918
WS_E_SECURITY_SYSTEM_FAILURE = -2143485917


ERROR_NDIS_INTERFACE_CLOSING = -2144075774
ERROR_NDIS_BAD_VERSION = -2144075772
ERROR_NDIS_BAD_CHARACTERISTICS = -2144075771
ERROR_NDIS_ADAPTER_NOT_FOUND = -2144075770
ERROR_NDIS_OPEN_FAILED = -2144075769
ERROR_NDIS_DEVICE_FAILED = -2144075768
ERROR_NDIS_MULTICAST_FULL = -2144075767
ERROR_NDIS_MULTICAST_EXISTS = -2144075766
ERROR_NDIS_MULTICAST_NOT_FOUND = -2144075765
ERROR_NDIS_REQUEST_ABORTED = -2144075764
ERROR_NDIS_RESET_IN_PROGRESS = -2144075763
ERROR_NDIS_NOT_SUPPORTED = -2144075589
ERROR_NDIS_INVALID_PACKET = -2144075761
ERROR_NDIS_ADAPTER_NOT_READY = -2144075759
ERROR_NDIS_INVALID_LENGTH = -2144075756
ERROR_NDIS_INVALID_DATA = -2144075755
ERROR_NDIS_BUFFER_TOO_SHORT = -2144075754
ERROR_NDIS_INVALID_OID = -2144075753
ERROR_NDIS_ADAPTER_REMOVED = -2144075752
ERROR_NDIS_UNSUPPORTED_MEDIA = -2144075751
ERROR_NDIS_GROUP_ADDRESS_IN_USE = -2144075750
ERROR_NDIS_FILE_NOT_FOUND = -2144075749
ERROR_NDIS_ERROR_READING_FILE = -2144075748
ERROR_NDIS_ALREADY_MAPPED = -2144075747
ERROR_NDIS_RESOURCE_CONFLICT = -2144075746
ERROR_NDIS_MEDIA_DISCONNECTED = -2144075745
ERROR_NDIS_INVALID_ADDRESS = -2144075742
ERROR_NDIS_INVALID_DEVICE_REQUEST = -2144075760
ERROR_NDIS_PAUSED = -2144075734
ERROR_NDIS_INTERFACE_NOT_FOUND = -2144075733
ERROR_NDIS_UNSUPPORTED_REVISION = -2144075732
ERROR_NDIS_INVALID_PORT = -2144075731
ERROR_NDIS_INVALID_PORT_STATE = -2144075730
ERROR_NDIS_LOW_POWER_STATE = -2144075729
ERROR_NDIS_REINIT_REQUIRED = -2144075728
ERROR_NDIS_NO_QUEUES = -2144075727
ERROR_NDIS_DOT11_AUTO_CONFIG_ENABLED = -2144067584
ERROR_NDIS_DOT11_MEDIA_IN_USE = -2144067583
ERROR_NDIS_DOT11_POWER_STATE_INVALID = -2144067582
ERROR_NDIS_PM_WOL_PATTERN_LIST_FULL = -2144067581
ERROR_NDIS_PM_PROTOCOL_OFFLOAD_LIST_FULL = -2144067580
ERROR_NDIS_DOT11_AP_CHANNEL_CURRENTLY_NOT_AVAILABLE = -2144067579
ERROR_NDIS_DOT11_AP_BAND_CURRENTLY_NOT_AVAILABLE = -2144067578
ERROR_NDIS_DOT11_AP_CHANNEL_NOT_ALLOWED = -2144067577
ERROR_NDIS_DOT11_AP_BAND_NOT_ALLOWED = -2144067576
ERROR_NDIS_INDICATION_REQUIRED = 0x00340001
ERROR_NDIS_OFFLOAD_POLICY = -1070329841
ERROR_NDIS_OFFLOAD_CONNECTION_REJECTED = -1070329838
ERROR_NDIS_OFFLOAD_PATH_REJECTED = -1070329837
ERROR_HV_INVALID_HYPERCALL_CODE = -1070268414
ERROR_HV_INVALID_HYPERCALL_INPUT = -1070268413
ERROR_HV_INVALID_ALIGNMENT = -1070268412
ERROR_HV_INVALID_PARAMETER = -1070268411
ERROR_HV_ACCESS_DENIED = -1070268410
ERROR_HV_INVALID_PARTITION_STATE = -1070268409
ERROR_HV_OPERATION_DENIED = -1070268408
ERROR_HV_UNKNOWN_PROPERTY = -1070268407
ERROR_HV_PROPERTY_VALUE_OUT_OF_RANGE = -1070268406
ERROR_HV_INSUFFICIENT_MEMORY = -1070268405
ERROR_HV_PARTITION_TOO_DEEP = -1070268404
ERROR_HV_INVALID_PARTITION_ID = -1070268403
ERROR_HV_INVALID_VP_INDEX = -1070268402
ERROR_HV_INVALID_PORT_ID = -1070268399
ERROR_HV_INVALID_CONNECTION_ID = -1070268398
ERROR_HV_INSUFFICIENT_BUFFERS = -1070268397
ERROR_HV_NOT_ACKNOWLEDGED = -1070268396
ERROR_HV_INVALID_VP_STATE = -1070268395
ERROR_HV_ACKNOWLEDGED = -1070268394
ERROR_HV_INVALID_SAVE_RESTORE_STATE = -1070268393
ERROR_HV_INVALID_SYNIC_STATE = -1070268392
ERROR_HV_OBJECT_IN_USE = -1070268391
ERROR_HV_INVALID_PROXIMITY_DOMAIN_INFO = -1070268390
ERROR_HV_NO_DATA = -1070268389
ERROR_HV_INACTIVE = -1070268388
ERROR_HV_NO_RESOURCES = -1070268387
ERROR_HV_FEATURE_UNAVAILABLE = -1070268386
ERROR_HV_INSUFFICIENT_BUFFER = -1070268365
ERROR_HV_INSUFFICIENT_DEVICE_DOMAINS = -1070268360
ERROR_HV_CPUID_FEATURE_VALIDATION = -1070268356
ERROR_HV_CPUID_XSAVE_FEATURE_VALIDATION = -1070268355
ERROR_HV_PROCESSOR_STARTUP_TIMEOUT = -1070268354
ERROR_HV_SMX_ENABLED = -1070268353
ERROR_HV_INVALID_LP_INDEX = -1070268351
ERROR_HV_INVALID_REGISTER_VALUE = -1070268336
ERROR_HV_INVALID_VTL_STATE = -1070268335
ERROR_HV_NX_NOT_DETECTED = -1070268331
ERROR_HV_INVALID_DEVICE_ID = -1070268329
ERROR_HV_INVALID_DEVICE_STATE = -1070268328
ERROR_HV_PENDING_PAGE_REQUESTS = 0x00350059
ERROR_HV_PAGE_REQUEST_INVALID = -1070268320
ERROR_HV_INVALID_CPU_GROUP_ID = -1070268305
ERROR_HV_INVALID_CPU_GROUP_STATE = -1070268304
ERROR_HV_OPERATION_FAILED = -1070268303
ERROR_HV_NOT_ALLOWED_WITH_NESTED_VIRT_ACTIVE = -1070268302
ERROR_HV_INSUFFICIENT_ROOT_MEMORY = -1070268301
ERROR_HV_EVENT_BUFFER_ALREADY_FREED = -1070268300
ERROR_HV_INSUFFICIENT_CONTIGUOUS_MEMORY = -1070268299
ERROR_HV_DEVICE_NOT_IN_DOMAIN = -1070268298
ERROR_HV_NESTED_VM_EXIT = -1070268297
ERROR_HV_MSR_ACCESS_FAILED = -1070268288
ERROR_HV_INSUFFICIENT_MEMORY_MIRRORING = -1070268287
ERROR_HV_INSUFFICIENT_CONTIGUOUS_MEMORY_MIRRORING = -1070268286
ERROR_HV_INSUFFICIENT_CONTIGUOUS_ROOT_MEMORY = -1070268285
ERROR_HV_INSUFFICIENT_ROOT_MEMORY_MIRRORING = -1070268284
ERROR_HV_INSUFFICIENT_CONTIGUOUS_ROOT_MEMORY_MIRRORING = -1070268283
ERROR_HV_NOT_PRESENT = -1070264320
ERROR_VID_DUPLICATE_HANDLER = -1070137343
ERROR_VID_TOO_MANY_HANDLERS = -1070137342
ERROR_VID_QUEUE_FULL = -1070137341
ERROR_VID_HANDLER_NOT_PRESENT = -1070137340
ERROR_VID_INVALID_OBJECT_NAME = -1070137339
ERROR_VID_PARTITION_NAME_TOO_LONG = -1070137338
ERROR_VID_MESSAGE_QUEUE_NAME_TOO_LONG = -1070137337
ERROR_VID_PARTITION_ALREADY_EXISTS = -1070137336
ERROR_VID_PARTITION_DOES_NOT_EXIST = -1070137335
ERROR_VID_PARTITION_NAME_NOT_FOUND = -1070137334
ERROR_VID_MESSAGE_QUEUE_ALREADY_EXISTS = -1070137333
ERROR_VID_EXCEEDED_MBP_ENTRY_MAP_LIMIT = -1070137332
ERROR_VID_MB_STILL_REFERENCED = -1070137331
ERROR_VID_CHILD_GPA_PAGE_SET_CORRUPTED = -1070137330
ERROR_VID_INVALID_NUMA_SETTINGS = -1070137329
ERROR_VID_INVALID_NUMA_NODE_INDEX = -1070137328
ERROR_VID_NOTIFICATION_QUEUE_ALREADY_ASSOCIATED = -1070137327
ERROR_VID_INVALID_MEMORY_BLOCK_HANDLE = -1070137326
ERROR_VID_PAGE_RANGE_OVERFLOW = -1070137325
ERROR_VID_INVALID_MESSAGE_QUEUE_HANDLE = -1070137324
ERROR_VID_INVALID_GPA_RANGE_HANDLE = -1070137323
ERROR_VID_NO_MEMORY_BLOCK_NOTIFICATION_QUEUE = -1070137322
ERROR_VID_MEMORY_BLOCK_LOCK_COUNT_EXCEEDED = -1070137321
ERROR_VID_INVALID_PPM_HANDLE = -1070137320
ERROR_VID_MBPS_ARE_LOCKED = -1070137319
ERROR_VID_MESSAGE_QUEUE_CLOSED = -1070137318
ERROR_VID_VIRTUAL_PROCESSOR_LIMIT_EXCEEDED = -1070137317
ERROR_VID_STOP_PENDING = -1070137316
ERROR_VID_INVALID_PROCESSOR_STATE = -1070137315
ERROR_VID_EXCEEDED_KM_CONTEXT_COUNT_LIMIT = -1070137314
ERROR_VID_KM_INTERFACE_ALREADY_INITIALIZED = -1070137313
ERROR_VID_MB_PROPERTY_ALREADY_SET_RESET = -1070137312
ERROR_VID_MMIO_RANGE_DESTROYED = -1070137311
ERROR_VID_INVALID_CHILD_GPA_PAGE_SET = -1070137310
ERROR_VID_RESERVE_PAGE_SET_IS_BEING_USED = -1070137309
ERROR_VID_RESERVE_PAGE_SET_TOO_SMALL = -1070137308
ERROR_VID_MBP_ALREADY_LOCKED_USING_RESERVED_PAGE = -1070137307
ERROR_VID_MBP_COUNT_EXCEEDED_LIMIT = -1070137306
ERROR_VID_SAVED_STATE_CORRUPT = -1070137305
ERROR_VID_SAVED_STATE_UNRECOGNIZED_ITEM = -1070137304
ERROR_VID_SAVED_STATE_INCOMPATIBLE = -1070137303
ERROR_VID_VTL_ACCESS_DENIED = -1070137302
ERROR_VID_INSUFFICIENT_RESOURCES_RESERVE = -1070137301
ERROR_VID_INSUFFICIENT_RESOURCES_PHYSICAL_BUFFER = -1070137300
ERROR_VID_INSUFFICIENT_RESOURCES_HV_DEPOSIT = -1070137299
ERROR_VID_MEMORY_TYPE_NOT_SUPPORTED = -1070137298
ERROR_VID_INSUFFICIENT_RESOURCES_WITHDRAW = -1070137297
ERROR_VID_PROCESS_ALREADY_SET = -1070137296
ERROR_VMCOMPUTE_TERMINATED_DURING_START = -1070137088
ERROR_VMCOMPUTE_IMAGE_MISMATCH = -1070137087
ERROR_VMCOMPUTE_HYPERV_NOT_INSTALLED = -1070137086
ERROR_VMCOMPUTE_OPERATION_PENDING = -1070137085
ERROR_VMCOMPUTE_TOO_MANY_NOTIFICATIONS = -1070137084
ERROR_VMCOMPUTE_INVALID_STATE = -1070137083
ERROR_VMCOMPUTE_UNEXPECTED_EXIT = -1070137082
ERROR_VMCOMPUTE_TERMINATED = -1070137081
ERROR_VMCOMPUTE_CONNECT_FAILED = -1070137080
ERROR_VMCOMPUTE_TIMEOUT = -1070137079
ERROR_VMCOMPUTE_CONNECTION_CLOSED = -1070137078
ERROR_VMCOMPUTE_UNKNOWN_MESSAGE = -1070137077
ERROR_VMCOMPUTE_UNSUPPORTED_PROTOCOL_VERSION = -1070137076
ERROR_VMCOMPUTE_INVALID_JSON = -1070137075
ERROR_VMCOMPUTE_SYSTEM_NOT_FOUND = -1070137074
ERROR_VMCOMPUTE_SYSTEM_ALREADY_EXISTS = -1070137073
ERROR_VMCOMPUTE_SYSTEM_ALREADY_STOPPED = -1070137072
ERROR_VMCOMPUTE_PROTOCOL_ERROR = -1070137071
ERROR_VMCOMPUTE_INVALID_LAYER = -1070137070
ERROR_VMCOMPUTE_WINDOWS_INSIDER_REQUIRED = -1070137069
HCS_E_TERMINATED_DURING_START = -2143878912
HCS_E_IMAGE_MISMATCH = -2143878911
HCS_E_HYPERV_NOT_INSTALLED = -2143878910
HCS_E_INVALID_STATE = -2143878907
HCS_E_UNEXPECTED_EXIT = -2143878906
HCS_E_TERMINATED = -2143878905
HCS_E_CONNECT_FAILED = -2143878904
HCS_E_CONNECTION_TIMEOUT = -2143878903
HCS_E_CONNECTION_CLOSED = -2143878902
HCS_E_UNKNOWN_MESSAGE = -2143878901
HCS_E_UNSUPPORTED_PROTOCOL_VERSION = -2143878900
HCS_E_INVALID_JSON = -2143878899
HCS_E_SYSTEM_NOT_FOUND = -2143878898
HCS_E_SYSTEM_ALREADY_EXISTS = -2143878897
HCS_E_SYSTEM_ALREADY_STOPPED = -2143878896
HCS_E_PROTOCOL_ERROR = -2143878895
HCS_E_INVALID_LAYER = -2143878894
HCS_E_WINDOWS_INSIDER_REQUIRED = -2143878893
HCS_E_SERVICE_NOT_AVAILABLE = -2143878892
HCS_E_OPERATION_NOT_STARTED = -2143878891
HCS_E_OPERATION_ALREADY_STARTED = -2143878890
HCS_E_OPERATION_PENDING = -2143878889
HCS_E_OPERATION_TIMEOUT = -2143878888
HCS_E_OPERATION_SYSTEM_CALLBACK_ALREADY_SET = -2143878887
HCS_E_OPERATION_RESULT_ALLOCATION_FAILED = -2143878886
HCS_E_ACCESS_DENIED = -2143878885
HCS_E_GUEST_CRITICAL_ERROR = -2143878884
HCS_E_PROCESS_INFO_NOT_AVAILABLE = -2143878883
HCS_E_SERVICE_DISCONNECT = -2143878882
HCS_E_PROCESS_ALREADY_STOPPED = -2143878881
HCS_E_SYSTEM_NOT_CONFIGURED_FOR_OPERATION = -2143878880
HCS_E_OPERATION_ALREADY_CANCELLED = -2143878879
ERROR_VNET_VIRTUAL_SWITCH_NAME_NOT_FOUND = -1070136832
ERROR_VID_REMOTE_NODE_PARENT_GPA_PAGES_USED = -2143879167
WHV_E_UNKNOWN_CAPABILITY = -2143878400
WHV_E_INSUFFICIENT_BUFFER = -2143878399
WHV_E_UNKNOWN_PROPERTY = -2143878398
WHV_E_UNSUPPORTED_HYPERVISOR_CONFIG = -2143878397
WHV_E_INVALID_PARTITION_CONFIG = -2143878396
WHV_E_GPA_RANGE_NOT_FOUND = -2143878395
WHV_E_VP_ALREADY_EXISTS = -2143878394
WHV_E_VP_DOES_NOT_EXIST = -2143878393
WHV_E_INVALID_VP_STATE = -2143878392
WHV_E_INVALID_VP_REGISTER_NAME = -2143878391
WHV_E_UNSUPPORTED_PROCESSOR_CONFIG = -2143878384
ERROR_VSMB_SAVED_STATE_FILE_NOT_FOUND = -1070136320
ERROR_VSMB_SAVED_STATE_CORRUPT = -1070136319
VM_SAVED_STATE_DUMP_E_PARTITION_STATE_NOT_FOUND = -1070136064
VM_SAVED_STATE_DUMP_E_GUEST_MEMORY_NOT_FOUND = -1070136063
VM_SAVED_STATE_DUMP_E_NO_VP_FOUND_IN_PARTITION_STATE = -1070136062
VM_SAVED_STATE_DUMP_E_NESTED_VIRTUALIZATION_NOT_SUPPORTED = -1070136061
VM_SAVED_STATE_DUMP_E_WINDOWS_KERNEL_IMAGE_NOT_FOUND = -1070136060
VM_SAVED_STATE_DUMP_E_VA_NOT_MAPPED = -1070136059
VM_SAVED_STATE_DUMP_E_INVALID_VP_STATE = -1070136058
VM_SAVED_STATE_DUMP_E_VP_VTL_NOT_ENABLED = -1070136055
ERROR_DM_OPERATION_LIMIT_EXCEEDED = -1070135808
ERROR_VOLMGR_INCOMPLETE_REGENERATION = -2143813631
ERROR_VOLMGR_INCOMPLETE_DISK_MIGRATION = -2143813630
ERROR_VOLMGR_DATABASE_FULL = -1070071807
ERROR_VOLMGR_DISK_CONFIGURATION_CORRUPTED = -1070071806
ERROR_VOLMGR_DISK_CONFIGURATION_NOT_IN_SYNC = -1070071805
ERROR_VOLMGR_PACK_CONFIG_UPDATE_FAILED = -1070071804
ERROR_VOLMGR_DISK_CONTAINS_NON_SIMPLE_VOLUME = -1070071803
ERROR_VOLMGR_DISK_DUPLICATE = -1070071802
ERROR_VOLMGR_DISK_DYNAMIC = -1070071801
ERROR_VOLMGR_DISK_ID_INVALID = -1070071800
ERROR_VOLMGR_DISK_INVALID = -1070071799
ERROR_VOLMGR_DISK_LAST_VOTER = -1070071798
ERROR_VOLMGR_DISK_LAYOUT_INVALID = -1070071797
ERROR_VOLMGR_DISK_LAYOUT_NON_BASIC_BETWEEN_BASIC_PARTITIONS = -1070071796
ERROR_VOLMGR_DISK_LAYOUT_NOT_CYLINDER_ALIGNED = -1070071795
ERROR_VOLMGR_DISK_LAYOUT_PARTITIONS_TOO_SMALL = -1070071794
ERROR_VOLMGR_DISK_LAYOUT_PRIMARY_BETWEEN_LOGICAL_PARTITIONS = -1070071793
ERROR_VOLMGR_DISK_LAYOUT_TOO_MANY_PARTITIONS = -1070071792
ERROR_VOLMGR_DISK_MISSING = -1070071791
ERROR_VOLMGR_DISK_NOT_EMPTY = -1070071790
ERROR_VOLMGR_DISK_NOT_ENOUGH_SPACE = -1070071789
ERROR_VOLMGR_DISK_REVECTORING_FAILED = -1070071788
ERROR_VOLMGR_DISK_SECTOR_SIZE_INVALID = -1070071787
ERROR_VOLMGR_DISK_SET_NOT_CONTAINED = -1070071786
ERROR_VOLMGR_DISK_USED_BY_MULTIPLE_MEMBERS = -1070071785
ERROR_VOLMGR_DISK_USED_BY_MULTIPLE_PLEXES = -1070071784
ERROR_VOLMGR_DYNAMIC_DISK_NOT_SUPPORTED = -1070071783
ERROR_VOLMGR_EXTENT_ALREADY_USED = -1070071782
ERROR_VOLMGR_EXTENT_NOT_CONTIGUOUS = -1070071781
ERROR_VOLMGR_EXTENT_NOT_IN_PUBLIC_REGION = -1070071780
ERROR_VOLMGR_EXTENT_NOT_SECTOR_ALIGNED = -1070071779
ERROR_VOLMGR_EXTENT_OVERLAPS_EBR_PARTITION = -1070071778
ERROR_VOLMGR_EXTENT_VOLUME_LENGTHS_DO_NOT_MATCH = -1070071777
ERROR_VOLMGR_FAULT_TOLERANT_NOT_SUPPORTED = -1070071776
ERROR_VOLMGR_INTERLEAVE_LENGTH_INVALID = -1070071775
ERROR_VOLMGR_MAXIMUM_REGISTERED_USERS = -1070071774
ERROR_VOLMGR_MEMBER_IN_SYNC = -1070071773
ERROR_VOLMGR_MEMBER_INDEX_DUPLICATE = -1070071772
ERROR_VOLMGR_MEMBER_INDEX_INVALID = -1070071771
ERROR_VOLMGR_MEMBER_MISSING = -1070071770
ERROR_VOLMGR_MEMBER_NOT_DETACHED = -1070071769
ERROR_VOLMGR_MEMBER_REGENERATING = -1070071768
ERROR_VOLMGR_ALL_DISKS_FAILED = -1070071767
ERROR_VOLMGR_NO_REGISTERED_USERS = -1070071766
ERROR_VOLMGR_NO_SUCH_USER = -1070071765
ERROR_VOLMGR_NOTIFICATION_RESET = -1070071764
ERROR_VOLMGR_NUMBER_OF_MEMBERS_INVALID = -1070071763
ERROR_VOLMGR_NUMBER_OF_PLEXES_INVALID = -1070071762
ERROR_VOLMGR_PACK_DUPLICATE = -1070071761
ERROR_VOLMGR_PACK_ID_INVALID = -1070071760
ERROR_VOLMGR_PACK_INVALID = -1070071759
ERROR_VOLMGR_PACK_NAME_INVALID = -1070071758
ERROR_VOLMGR_PACK_OFFLINE = -1070071757
ERROR_VOLMGR_PACK_HAS_QUORUM = -1070071756
ERROR_VOLMGR_PACK_WITHOUT_QUORUM = -1070071755
ERROR_VOLMGR_PARTITION_STYLE_INVALID = -1070071754
ERROR_VOLMGR_PARTITION_UPDATE_FAILED = -1070071753
ERROR_VOLMGR_PLEX_IN_SYNC = -1070071752
ERROR_VOLMGR_PLEX_INDEX_DUPLICATE = -1070071751
ERROR_VOLMGR_PLEX_INDEX_INVALID = -1070071750
ERROR_VOLMGR_PLEX_LAST_ACTIVE = -1070071749
ERROR_VOLMGR_PLEX_MISSING = -1070071748
ERROR_VOLMGR_PLEX_REGENERATING = -1070071747
ERROR_VOLMGR_PLEX_TYPE_INVALID = -1070071746
ERROR_VOLMGR_PLEX_NOT_RAID5 = -1070071745
ERROR_VOLMGR_PLEX_NOT_SIMPLE = -1070071744
ERROR_VOLMGR_STRUCTURE_SIZE_INVALID = -1070071743
ERROR_VOLMGR_TOO_MANY_NOTIFICATION_REQUESTS = -1070071742
ERROR_VOLMGR_TRANSACTION_IN_PROGRESS = -1070071741
ERROR_VOLMGR_UNEXPECTED_DISK_LAYOUT_CHANGE = -1070071740
ERROR_VOLMGR_VOLUME_CONTAINS_MISSING_DISK = -1070071739
ERROR_VOLMGR_VOLUME_ID_INVALID = -1070071738
ERROR_VOLMGR_VOLUME_LENGTH_INVALID = -1070071737
ERROR_VOLMGR_VOLUME_LENGTH_NOT_SECTOR_SIZE_MULTIPLE = -1070071736
ERROR_VOLMGR_VOLUME_NOT_MIRRORED = -1070071735
ERROR_VOLMGR_VOLUME_NOT_RETAINED = -1070071734
ERROR_VOLMGR_VOLUME_OFFLINE = -1070071733
ERROR_VOLMGR_VOLUME_RETAINED = -1070071732
ERROR_VOLMGR_NUMBER_OF_EXTENTS_INVALID = -1070071731
ERROR_VOLMGR_DIFFERENT_SECTOR_SIZE = -1070071730
ERROR_VOLMGR_BAD_BOOT_DISK = -1070071729
ERROR_VOLMGR_PACK_CONFIG_OFFLINE = -1070071728
ERROR_VOLMGR_PACK_CONFIG_ONLINE = -1070071727
ERROR_VOLMGR_NOT_PRIMARY_PACK = -1070071726
ERROR_VOLMGR_PACK_LOG_UPDATE_FAILED = -1070071725
ERROR_VOLMGR_NUMBER_OF_DISKS_IN_PLEX_INVALID = -1070071724
ERROR_VOLMGR_NUMBER_OF_DISKS_IN_MEMBER_INVALID = -1070071723
ERROR_VOLMGR_VOLUME_MIRRORED = -1070071722
ERROR_VOLMGR_PLEX_NOT_SIMPLE_SPANNED = -1070071721
ERROR_VOLMGR_NO_VALID_LOG_COPIES = -1070071720
ERROR_VOLMGR_PRIMARY_PACK_PRESENT = -1070071719
ERROR_VOLMGR_NUMBER_OF_DISKS_INVALID = -1070071718
ERROR_VOLMGR_MIRROR_NOT_SUPPORTED = -1070071717
ERROR_VOLMGR_RAID5_NOT_SUPPORTED = -1070071716
ERROR_BCD_NOT_ALL_ENTRIES_IMPORTED = -2143748095
ERROR_BCD_TOO_MANY_ELEMENTS = -1070006270
ERROR_BCD_NOT_ALL_ENTRIES_SYNCHRONIZED = -2143748093
ERROR_VHD_DRIVE_FOOTER_MISSING = -1069940735
ERROR_VHD_DRIVE_FOOTER_CHECKSUM_MISMATCH = -1069940734
ERROR_VHD_DRIVE_FOOTER_CORRUPT = -1069940733
ERROR_VHD_FORMAT_UNKNOWN = -1069940732
ERROR_VHD_FORMAT_UNSUPPORTED_VERSION = -1069940731
ERROR_VHD_SPARSE_HEADER_CHECKSUM_MISMATCH = -1069940730
ERROR_VHD_SPARSE_HEADER_UNSUPPORTED_VERSION = -1069940729
ERROR_VHD_SPARSE_HEADER_CORRUPT = -1069940728
ERROR_VHD_BLOCK_ALLOCATION_FAILURE = -**********
ERROR_VHD_BLOCK_ALLOCATION_TABLE_CORRUPT = -**********
ERROR_VHD_INVALID_BLOCK_SIZE = -**********
ERROR_VHD_BITMAP_MISMATCH = -**********
ERROR_VHD_PARENT_VHD_NOT_FOUND = -**********
ERROR_VHD_CHILD_PARENT_ID_MISMATCH = -**********
ERROR_VHD_CHILD_PARENT_TIMESTAMP_MISMATCH = -**********
ERROR_VHD_METADATA_READ_FAILURE = -**********
ERROR_VHD_METADATA_WRITE_FAILURE = -**********
ERROR_VHD_INVALID_SIZE = -**********
ERROR_VHD_INVALID_FILE_SIZE = -**********
ERROR_VIRTDISK_PROVIDER_NOT_FOUND = -**********
ERROR_VIRTDISK_NOT_VIRTUAL_DISK = -**********
ERROR_VHD_PARENT_VHD_ACCESS_DENIED = -**********
ERROR_VHD_CHILD_PARENT_SIZE_MISMATCH = -**********
ERROR_VHD_DIFFERENCING_CHAIN_CYCLE_DETECTED = -**********
ERROR_VHD_DIFFERENCING_CHAIN_ERROR_IN_PARENT = -**********
ERROR_VIRTUAL_DISK_LIMITATION = -**********
ERROR_VHD_INVALID_TYPE = -**********
ERROR_VHD_INVALID_STATE = -**********
ERROR_VIRTDISK_UNSUPPORTED_DISK_SECTOR_SIZE = -**********
ERROR_VIRTDISK_DISK_ALREADY_OWNED = -**********
ERROR_VIRTDISK_DISK_ONLINE_AND_WRITABLE = -**********
ERROR_CTLOG_TRACKING_NOT_INITIALIZED = -**********
ERROR_CTLOG_LOGFILE_SIZE_EXCEEDED_MAXSIZE = -**********
ERROR_CTLOG_VHD_CHANGED_OFFLINE = -**********
ERROR_CTLOG_INVALID_TRACKING_STATE = -**********
ERROR_CTLOG_INCONSISTENT_TRACKING_FILE = -**********
ERROR_VHD_RESIZE_WOULD_TRUNCATE_DATA = -**********
ERROR_VHD_COULD_NOT_COMPUTE_MINIMUM_VIRTUAL_SIZE = -**********
ERROR_VHD_ALREADY_AT_OR_BELOW_MINIMUM_VIRTUAL_SIZE = -1069940697
ERROR_VHD_METADATA_FULL = -1069940696
ERROR_VHD_INVALID_CHANGE_TRACKING_ID = -1069940695
ERROR_VHD_CHANGE_TRACKING_DISABLED = -1069940694
ERROR_VHD_MISSING_CHANGE_TRACKING_INFORMATION = -1069940688
ERROR_VHD_UNEXPECTED_ID = -1069940684
ERROR_QUERY_STORAGE_ERROR = -2143682559
HCN_E_NETWORK_NOT_FOUND = -2143617023
HCN_E_ENDPOINT_NOT_FOUND = -2143617022
HCN_E_LAYER_NOT_FOUND = -2143617021
HCN_E_SWITCH_NOT_FOUND = -2143617020
HCN_E_SUBNET_NOT_FOUND = -2143617019
HCN_E_ADAPTER_NOT_FOUND = -2143617018
HCN_E_PORT_NOT_FOUND = -2143617017
HCN_E_POLICY_NOT_FOUND = -2143617016
HCN_E_VFP_PORTSETTING_NOT_FOUND = -2143617015
HCN_E_INVALID_NETWORK = -2143617014
HCN_E_INVALID_NETWORK_TYPE = -2143617013
HCN_E_INVALID_ENDPOINT = -2143617012
HCN_E_INVALID_POLICY = -2143617011
HCN_E_INVALID_POLICY_TYPE = -2143617010
HCN_E_INVALID_REMOTE_ENDPOINT_OPERATION = -2143617009
HCN_E_NETWORK_ALREADY_EXISTS = -2143617008
HCN_E_LAYER_ALREADY_EXISTS = -2143617007
HCN_E_POLICY_ALREADY_EXISTS = -2143617006
HCN_E_PORT_ALREADY_EXISTS = -2143617005
HCN_E_ENDPOINT_ALREADY_ATTACHED = -2143617004
HCN_E_REQUEST_UNSUPPORTED = -2143617003
HCN_E_MAPPING_NOT_SUPPORTED = -2143617002
HCN_E_DEGRADED_OPERATION = -2143617001
HCN_E_SHARED_SWITCH_MODIFICATION = -2143617000
HCN_E_GUID_CONVERSION_FAILURE = -2143616999
HCN_E_REGKEY_FAILURE = -2143616998
HCN_E_INVALID_JSON = -2143616997
HCN_E_INVALID_JSON_REFERENCE = -2143616996
HCN_E_ENDPOINT_SHARING_DISABLED = -2143616995
HCN_E_INVALID_IP = -2143616994
HCN_E_SWITCH_EXTENSION_NOT_FOUND = -2143616993
HCN_E_MANAGER_STOPPED = -2143616992
GCN_E_MODULE_NOT_FOUND = -2143616991
GCN_E_NO_REQUEST_HANDLERS = -2143616990
GCN_E_REQUEST_UNSUPPORTED = -2143616989
GCN_E_RUNTIMEKEYS_FAILED = -2143616988
GCN_E_NETADAPTER_TIMEOUT = -2143616987
GCN_E_NETADAPTER_NOT_FOUND = -2143616986
GCN_E_NETCOMPARTMENT_NOT_FOUND = -2143616985
GCN_E_NETINTERFACE_NOT_FOUND = -2143616984
GCN_E_DEFAULTNAMESPACE_EXISTS = -2143616983
HCN_E_ICS_DISABLED = -2143616982
HCN_E_ENDPOINT_NAMESPACE_ALREADY_EXISTS = -2143616981
HCN_E_ENTITY_HAS_REFERENCES = -2143616980
HCN_E_INVALID_INTERNAL_PORT = -2143616979
HCN_E_NAMESPACE_ATTACH_FAILED = -2143616978
HCN_E_ADDR_INVALID_OR_RESERVED = -2143616977
HCN_E_INVALID_PREFIX = -2143616976
HCN_E_OBJECT_USED_AFTER_UNLOAD = -2143616975
HCN_E_INVALID_SUBNET = -2143616974
HCN_E_INVALID_IP_SUBNET = -2143616973
HCN_E_ENDPOINT_NOT_ATTACHED = -2143616972
HCN_E_ENDPOINT_NOT_LOCAL = -2143616971
HCN_INTERFACEPARAMETERS_ALREADY_APPLIED = -2143616970
HCN_E_VFP_NOT_ALLOWED = -2143616969
SDIAG_E_CANCELLED = -2143551232
SDIAG_E_SCRIPT = -2143551231
SDIAG_E_POWERSHELL = -2143551230
SDIAG_E_MANAGEDHOST = -2143551229
SDIAG_E_NOVERIFIER = -2143551228
SDIAG_S_CANNOTRUN = 0x003C0105
SDIAG_E_DISABLED = -2143551226
SDIAG_E_TRUST = -2143551225
SDIAG_E_CANNOTRUN = -2143551224
SDIAG_E_VERSION = -2143551223
SDIAG_E_RESOURCE = -2143551222
SDIAG_E_ROOTCAUSE = -2143551221
WPN_E_CHANNEL_CLOSED = -2143420160
WPN_E_CHANNEL_REQUEST_NOT_COMPLETE = -2143420159
WPN_E_INVALID_APP = -2143420158
WPN_E_OUTSTANDING_CHANNEL_REQUEST = -2143420157
WPN_E_DUPLICATE_CHANNEL = -2143420156
WPN_E_PLATFORM_UNAVAILABLE = -2143420155
WPN_E_NOTIFICATION_POSTED = -2143420154
WPN_E_NOTIFICATION_HIDDEN = -2143420153
WPN_E_NOTIFICATION_NOT_POSTED = -2143420152
WPN_E_CLOUD_DISABLED = -2143420151
WPN_E_CLOUD_INCAPABLE = -2143420144
WPN_E_CLOUD_AUTH_UNAVAILABLE = -2143420134
WPN_E_CLOUD_SERVICE_UNAVAILABLE = -2143420133
WPN_E_FAILED_LOCK_SCREEN_UPDATE_INTIALIZATION = -2143420132
WPN_E_NOTIFICATION_DISABLED = -2143420143
WPN_E_NOTIFICATION_INCAPABLE = -2143420142
WPN_E_INTERNET_INCAPABLE = -2143420141
WPN_E_NOTIFICATION_TYPE_DISABLED = -2143420140
WPN_E_NOTIFICATION_SIZE = -2143420139
WPN_E_TAG_SIZE = -2143420138
WPN_E_ACCESS_DENIED = -2143420137
WPN_E_DUPLICATE_REGISTRATION = -2143420136
WPN_E_PUSH_NOTIFICATION_INCAPABLE = -2143420135
WPN_E_DEV_ID_SIZE = -2143420128
WPN_E_TAG_ALPHANUMERIC = -2143420118
WPN_E_INVALID_HTTP_STATUS_CODE = -2143420117
WPN_E_OUT_OF_SESSION = -2143419904
WPN_E_POWER_SAVE = -2143419903
WPN_E_IMAGE_NOT_FOUND_IN_CACHE = -2143419902
WPN_E_ALL_URL_NOT_COMPLETED = -2143419901
WPN_E_INVALID_CLOUD_IMAGE = -2143419900
WPN_E_NOTIFICATION_ID_MATCHED = -**********
WPN_E_CALLBACK_ALREADY_REGISTERED = -**********
WPN_E_TOAST_NOTIFICATION_DROPPED = -**********
WPN_E_STORAGE_LOCKED = -**********
WPN_E_GROUP_SIZE = -**********
WPN_E_GROUP_ALPHANUMERIC = -**********
WPN_E_CLOUD_DISABLED_FOR_APP = -**********
E_MBN_CONTEXT_NOT_ACTIVATED = -**********
E_MBN_BAD_SIM = -**********
E_MBN_DATA_CLASS_NOT_AVAILABLE = -**********
E_MBN_INVALID_ACCESS_STRING = -**********
E_MBN_MAX_ACTIVATED_CONTEXTS = -**********
E_MBN_PACKET_SVC_DETACHED = -**********
E_MBN_PROVIDER_NOT_VISIBLE = -**********
E_MBN_RADIO_POWER_OFF = -**********
E_MBN_SERVICE_NOT_ACTIVATED = -**********
E_MBN_SIM_NOT_INSERTED = -**********
E_MBN_VOICE_CALL_IN_PROGRESS = -**********
E_MBN_INVALID_CACHE = -**********
E_MBN_NOT_REGISTERED = -**********
E_MBN_PROVIDERS_NOT_FOUND = -**********
E_MBN_PIN_NOT_SUPPORTED = -**********
E_MBN_PIN_REQUIRED = -**********
E_MBN_PIN_DISABLED = -**********
E_MBN_FAILURE = -**********
E_MBN_INVALID_PROFILE = -**********
E_MBN_DEFAULT_PROFILE_EXIST = -**********
E_MBN_SMS_ENCODING_NOT_SUPPORTED = -**********
E_MBN_SMS_FILTER_NOT_SUPPORTED = -**********
E_MBN_SMS_INVALID_MEMORY_INDEX = -**********
E_MBN_SMS_LANG_NOT_SUPPORTED = -**********
E_MBN_SMS_MEMORY_FAILURE = -**********
E_MBN_SMS_NETWORK_TIMEOUT = -**********
E_MBN_SMS_UNKNOWN_SMSC_ADDRESS = -**********
E_MBN_SMS_FORMAT_NOT_SUPPORTED = -**********
E_MBN_SMS_OPERATION_NOT_ALLOWED = -**********
E_MBN_SMS_MEMORY_FULL = -**********
PEER_E_IPV6_NOT_INSTALLED = -**********
PEER_E_NOT_INITIALIZED = -**********
PEER_E_CANNOT_START_SERVICE = -**********
PEER_E_NOT_LICENSED = -**********
PEER_E_INVALID_GRAPH = -**********
PEER_E_DBNAME_CHANGED = -**********
PEER_E_DUPLICATE_GRAPH = -**********
PEER_E_GRAPH_NOT_READY = -**********
PEER_E_GRAPH_SHUTTING_DOWN = -**********
PEER_E_GRAPH_IN_USE = -**********
PEER_E_INVALID_DATABASE = -**********
PEER_E_TOO_MANY_ATTRIBUTES = -**********
PEER_E_CONNECTION_NOT_FOUND = -**********
PEER_E_CONNECT_SELF = -**********
PEER_E_ALREADY_LISTENING = -**********
PEER_E_NODE_NOT_FOUND = -**********
PEER_E_CONNECTION_FAILED = -**********
PEER_E_CONNECTION_NOT_AUTHENTICATED = -**********
PEER_E_CONNECTION_REFUSED = -**********
PEER_E_CLASSIFIER_TOO_LONG = -**********
PEER_E_TOO_MANY_IDENTITIES = -**********
PEER_E_NO_KEY_ACCESS = -**********
PEER_E_GROUPS_EXIST = -**********
PEER_E_RECORD_NOT_FOUND = -**********
PEER_E_DATABASE_ACCESSDENIED = -**********
PEER_E_DBINITIALIZATION_FAILED = -**********
PEER_E_MAX_RECORD_SIZE_EXCEEDED = -**********
PEER_E_DATABASE_ALREADY_PRESENT = -**********
PEER_E_DATABASE_NOT_PRESENT = -2140994810
PEER_E_IDENTITY_NOT_FOUND = -2140994559
PEER_E_EVENT_HANDLE_NOT_FOUND = -2140994303
PEER_E_INVALID_SEARCH = -2140994047
PEER_E_INVALID_ATTRIBUTES = -2140994046
PEER_E_INVITATION_NOT_TRUSTED = -2140993791
PEER_E_CHAIN_TOO_LONG = -2140993789
PEER_E_INVALID_TIME_PERIOD = -2140993787
PEER_E_CIRCULAR_CHAIN_DETECTED = -2140993786
PEER_E_CERT_STORE_CORRUPTED = -2140993535
PEER_E_NO_CLOUD = -2140991487
PEER_E_CLOUD_NAME_AMBIGUOUS = -2140991483
PEER_E_INVALID_RECORD = -2140987376
PEER_E_NOT_AUTHORIZED = -2140987360
PEER_E_PASSWORD_DOES_NOT_MEET_POLICY = -2140987359
PEER_E_DEFERRED_VALIDATION = -2140987344
PEER_E_INVALID_GROUP_PROPERTIES = -2140987328
PEER_E_INVALID_PEER_NAME = -2140987312
PEER_E_INVALID_CLASSIFIER = -2140987296
PEER_E_INVALID_FRIENDLY_NAME = -2140987280
PEER_E_INVALID_ROLE_PROPERTY = -2140987279
PEER_E_INVALID_CLASSIFIER_PROPERTY = -2140987278
PEER_E_INVALID_RECORD_EXPIRATION = -2140987264
PEER_E_INVALID_CREDENTIAL_INFO = -2140987263
PEER_E_INVALID_CREDENTIAL = -2140987262
PEER_E_INVALID_RECORD_SIZE = -2140987261
PEER_E_UNSUPPORTED_VERSION = -2140987248
PEER_E_GROUP_NOT_READY = -2140987247
PEER_E_GROUP_IN_USE = -2140987246
PEER_E_INVALID_GROUP = -2140987245
PEER_E_NO_MEMBERS_FOUND = -2140987244
PEER_E_NO_MEMBER_CONNECTIONS = -2140987243
PEER_E_UNABLE_TO_LISTEN = -2140987242
PEER_E_IDENTITY_DELETED = -2140987232
PEER_E_SERVICE_NOT_AVAILABLE = -2140987231
PEER_E_CONTACT_NOT_FOUND = -2140971007
PEER_S_GRAPH_DATA_CREATED = 0x00630001
PEER_S_NO_EVENT_DATA = 0x00630002
PEER_S_ALREADY_CONNECTED = 0x00632000
PEER_S_SUBSCRIPTION_EXISTS = 0x00636000
PEER_S_NO_CONNECTIVITY = 0x00630005
PEER_S_ALREADY_A_MEMBER = 0x00630006
PEER_E_CANNOT_CONVERT_PEER_NAME = -2140979199
PEER_E_INVALID_PEER_HOST_NAME = -2140979198
PEER_E_NO_MORE = -2140979197
PEER_E_PNRP_DUPLICATE_PEER_NAME = -2140979195
PEER_E_INVITE_CANCELLED = -2140966912
PEER_E_INVITE_RESPONSE_NOT_AVAILABLE = -2140966911
PEER_E_NOT_SIGNED_IN = -2140966909
PEER_E_PRIVACY_DECLINED = -2140966908
PEER_E_TIMEOUT = -2140966907
PEER_E_INVALID_ADDRESS = -2140966905
PEER_E_FW_EXCEPTION_DISABLED = -2140966904
PEER_E_FW_BLOCKED_BY_POLICY = -2140966903
PEER_E_FW_BLOCKED_BY_SHIELDS_UP = -2140966902
PEER_E_FW_DECLINED = -2140966901
UI_E_CREATE_FAILED = -2144731135
UI_E_SHUTDOWN_CALLED = -2144731134
UI_E_ILLEGAL_REENTRANCY = -2144731133
UI_E_OBJECT_SEALED = -2144731132
UI_E_VALUE_NOT_SET = -2144731131
UI_E_VALUE_NOT_DETERMINED = -2144731130
UI_E_INVALID_OUTPUT = -2144731129
UI_E_BOOLEAN_EXPECTED = -2144731128
UI_E_DIFFERENT_OWNER = -2144731127
UI_E_AMBIGUOUS_MATCH = -2144731126
UI_E_FP_OVERFLOW = -2144731125
UI_E_WRONG_THREAD = -2144731124
UI_E_STORYBOARD_ACTIVE = -2144730879
UI_E_STORYBOARD_NOT_PLAYING = -2144730878
UI_E_START_KEYFRAME_AFTER_END = -2144730877
UI_E_END_KEYFRAME_NOT_DETERMINED = -2144730876
UI_E_LOOPS_OVERLAP = -2144730875
UI_E_TRANSITION_ALREADY_USED = -2144730874
UI_E_TRANSITION_NOT_IN_STORYBOARD = -2144730873
UI_E_TRANSITION_ECLIPSED = -2144730872
UI_E_TIME_BEFORE_LAST_UPDATE = -2144730871
UI_E_TIMER_CLIENT_ALREADY_CONNECTED = -2144730870
UI_E_INVALID_DIMENSION = -2144730869
UI_E_PRIMITIVE_OUT_OF_BOUNDS = -2144730868
UI_E_WINDOW_CLOSED = -2144730623
E_BLUETOOTH_ATT_INVALID_HANDLE = -2140864511
E_BLUETOOTH_ATT_READ_NOT_PERMITTED = -2140864510
E_BLUETOOTH_ATT_WRITE_NOT_PERMITTED = -2140864509
E_BLUETOOTH_ATT_INVALID_PDU = -2140864508
E_BLUETOOTH_ATT_INSUFFICIENT_AUTHENTICATION = -2140864507
E_BLUETOOTH_ATT_REQUEST_NOT_SUPPORTED = -2140864506
E_BLUETOOTH_ATT_INVALID_OFFSET = -2140864505
E_BLUETOOTH_ATT_INSUFFICIENT_AUTHORIZATION = -2140864504
E_BLUETOOTH_ATT_PREPARE_QUEUE_FULL = -2140864503
E_BLUETOOTH_ATT_ATTRIBUTE_NOT_FOUND = -2140864502
E_BLUETOOTH_ATT_ATTRIBUTE_NOT_LONG = -2140864501
E_BLUETOOTH_ATT_INSUFFICIENT_ENCRYPTION_KEY_SIZE = -2140864500
E_BLUETOOTH_ATT_INVALID_ATTRIBUTE_VALUE_LENGTH = -2140864499
E_BLUETOOTH_ATT_UNLIKELY = -2140864498
E_BLUETOOTH_ATT_INSUFFICIENT_ENCRYPTION = -2140864497
E_BLUETOOTH_ATT_UNSUPPORTED_GROUP_TYPE = -2140864496
E_BLUETOOTH_ATT_INSUFFICIENT_RESOURCES = -2140864495
E_BLUETOOTH_ATT_UNKNOWN_ERROR = -2140860416
E_AUDIO_ENGINE_NODE_NOT_FOUND = -2140798975
E_HDAUDIO_EMPTY_CONNECTION_LIST = -2140798974
E_HDAUDIO_CONNECTION_LIST_NOT_SUPPORTED = -2140798973
E_HDAUDIO_NO_LOGICAL_DEVICES_CREATED = -2140798972
E_HDAUDIO_NULL_LINKED_LIST_ENTRY = -2140798971
STATEREPOSITORY_E_CONCURRENCY_LOCKING_FAILURE = -2140733439
STATEREPOSITORY_E_STATEMENT_INPROGRESS = -2140733438
STATEREPOSITORY_E_CONFIGURATION_INVALID = -2140733437
STATEREPOSITORY_E_UNKNOWN_SCHEMA_VERSION = -2140733436
STATEREPOSITORY_ERROR_DICTIONARY_CORRUPTED = -2140733435
STATEREPOSITORY_E_BLOCKED = -2140733434
STATEREPOSITORY_E_BUSY_RETRY = -2140733433
STATEREPOSITORY_E_BUSY_RECOVERY_RETRY = -2140733432
STATEREPOSITORY_E_LOCKED_RETRY = -2140733431
STATEREPOSITORY_E_LOCKED_SHAREDCACHE_RETRY = -2140733430
STATEREPOSITORY_E_TRANSACTION_REQUIRED = -2140733429
STATEREPOSITORY_E_BUSY_TIMEOUT_EXCEEDED = -2140733428
STATEREPOSITORY_E_BUSY_RECOVERY_TIMEOUT_EXCEEDED = -2140733427
STATEREPOSITORY_E_LOCKED_TIMEOUT_EXCEEDED = -2140733426
STATEREPOSITORY_E_LOCKED_SHAREDCACHE_TIMEOUT_EXCEEDED = -2140733425
STATEREPOSITORY_E_SERVICE_STOP_IN_PROGRESS = -2140733424
STATEREPOSTORY_E_NESTED_TRANSACTION_NOT_SUPPORTED = -2140733423
STATEREPOSITORY_ERROR_CACHE_CORRUPTED = -2140733422
STATEREPOSITORY_TRANSACTION_CALLER_ID_CHANGED = 0x00670013
STATEREPOSITORY_TRANSACTION_IN_PROGRESS = -2140733420
STATEREPOSITORY_E_CACHE_NOT_INIITALIZED = -2140733419
STATEREPOSITORY_E_DEPENDENCY_NOT_RESOLVED = -2140733418
ERROR_SPACES_POOL_WAS_DELETED = 0x00E70001
ERROR_SPACES_FAULT_DOMAIN_TYPE_INVALID = -2132344831
ERROR_SPACES_INTERNAL_ERROR = -2132344830
ERROR_SPACES_RESILIENCY_TYPE_INVALID = -2132344829
ERROR_SPACES_DRIVE_SECTOR_SIZE_INVALID = -2132344828
ERROR_SPACES_DRIVE_REDUNDANCY_INVALID = -2132344826
ERROR_SPACES_NUMBER_OF_DATA_COPIES_INVALID = -2132344825
ERROR_SPACES_PARITY_LAYOUT_INVALID = -2132344824
ERROR_SPACES_INTERLEAVE_LENGTH_INVALID = -2132344823
ERROR_SPACES_NUMBER_OF_COLUMNS_INVALID = -2132344822
ERROR_SPACES_NOT_ENOUGH_DRIVES = -2132344821
ERROR_SPACES_EXTENDED_ERROR = -2132344820
ERROR_SPACES_PROVISIONING_TYPE_INVALID = -2132344819
ERROR_SPACES_ALLOCATION_SIZE_INVALID = -2132344818
ERROR_SPACES_ENCLOSURE_AWARE_INVALID = -2132344817
ERROR_SPACES_WRITE_CACHE_SIZE_INVALID = -2132344816
ERROR_SPACES_NUMBER_OF_GROUPS_INVALID = -2132344815
ERROR_SPACES_DRIVE_OPERATIONAL_STATE_INVALID = -2132344814
ERROR_SPACES_ENTRY_INCOMPLETE = -2132344813
ERROR_SPACES_ENTRY_INVALID = -2132344812
ERROR_SPACES_UPDATE_COLUMN_STATE = -2132344811
ERROR_SPACES_MAP_REQUIRED = -2132344810
ERROR_SPACES_UNSUPPORTED_VERSION = -2132344809
ERROR_SPACES_CORRUPT_METADATA = -2132344808
ERROR_SPACES_DRT_FULL = -2132344807
ERROR_SPACES_INCONSISTENCY = -2132344806
ERROR_SPACES_LOG_NOT_READY = -2132344805
ERROR_SPACES_NO_REDUNDANCY = -2132344804
ERROR_SPACES_DRIVE_NOT_READY = -2132344803
ERROR_SPACES_DRIVE_SPLIT = -2132344802
ERROR_SPACES_DRIVE_LOST_DATA = -2132344801
ERROR_SPACES_MARK_DIRTY = -2132344800
ERROR_SPACES_FLUSH_METADATA = -2132344795
ERROR_SPACES_CACHE_FULL = -2132344794
ERROR_SPACES_REPAIR_IN_PROGRESS = -2132344793
ERROR_VOLSNAP_BOOTFILE_NOT_VALID = -2138963967
ERROR_VOLSNAP_ACTIVATION_TIMEOUT = -2138963966
ERROR_VOLSNAP_NO_BYPASSIO_WITH_SNAPSHOT = -2138963965
ERROR_TIERING_NOT_SUPPORTED_ON_VOLUME = -**********
ERROR_TIERING_VOLUME_DISMOUNT_IN_PROGRESS = -**********
ERROR_TIERING_STORAGE_TIER_NOT_FOUND = -**********
ERROR_TIERING_INVALID_FILE_ID = -**********
ERROR_TIERING_WRONG_CLUSTER_NODE = -**********
ERROR_TIERING_ALREADY_PROCESSING = -**********
ERROR_TIERING_CANNOT_PIN_OBJECT = -**********
ERROR_TIERING_FILE_IS_NOT_PINNED = -**********
ERROR_NOT_A_TIERED_VOLUME = -**********
ERROR_ATTRIBUTE_NOT_PRESENT = -**********
ERROR_SECCORE_INVALID_COMMAND = -**********
ERROR_NO_APPLICABLE_APP_LICENSES_FOUND = -**********
ERROR_CLIP_LICENSE_NOT_FOUND = -**********
ERROR_CLIP_DEVICE_LICENSE_MISSING = -**********
ERROR_CLIP_LICENSE_INVALID_SIGNATURE = -**********
ERROR_CLIP_KEYHOLDER_LICENSE_MISSING_OR_INVALID = -**********
ERROR_CLIP_LICENSE_EXPIRED = -**********
ERROR_CLIP_LICENSE_SIGNED_BY_UNKNOWN_SOURCE = -**********
ERROR_CLIP_LICENSE_NOT_SIGNED = -**********
ERROR_CLIP_LICENSE_HARDWARE_ID_OUT_OF_TOLERANCE = -**********
ERROR_CLIP_LICENSE_DEVICE_ID_MISMATCH = -**********
DXGI_STATUS_OCCLUDED = 0x087A0001
DXGI_STATUS_CLIPPED = 0x087A0002
DXGI_STATUS_NO_REDIRECTION = 0x087A0004
DXGI_STATUS_NO_DESKTOP_ACCESS = 0x087A0005
DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE = 0x087A0006
DXGI_STATUS_MODE_CHANGED = 0x087A0007
DXGI_STATUS_MODE_CHANGE_IN_PROGRESS = 0x087A0008
DXGI_ERROR_INVALID_CALL = -**********
DXGI_ERROR_NOT_FOUND = -**********
DXGI_ERROR_MORE_DATA = -**********
DXGI_ERROR_UNSUPPORTED = -**********
DXGI_ERROR_DEVICE_REMOVED = -**********
DXGI_ERROR_DEVICE_HUNG = -**********
DXGI_ERROR_DEVICE_RESET = -**********
DXGI_ERROR_WAS_STILL_DRAWING = -**********
DXGI_ERROR_FRAME_STATISTICS_DISJOINT = -**********
DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE = -**********
DXGI_ERROR_DRIVER_INTERNAL_ERROR = -**********
DXGI_ERROR_NONEXCLUSIVE = -**********
DXGI_ERROR_NOT_CURRENTLY_AVAILABLE = -**********
DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED = -**********
DXGI_ERROR_REMOTE_OUTOFMEMORY = -**********
DXGI_ERROR_ACCESS_LOST = -2005270490
DXGI_ERROR_WAIT_TIMEOUT = -2005270489
DXGI_ERROR_SESSION_DISCONNECTED = -2005270488
DXGI_ERROR_RESTRICT_TO_OUTPUT_STALE = -2005270487
DXGI_ERROR_CANNOT_PROTECT_CONTENT = -2005270486
DXGI_ERROR_ACCESS_DENIED = -2005270485
DXGI_ERROR_NAME_ALREADY_EXISTS = -2005270484
DXGI_ERROR_SDK_COMPONENT_MISSING = -2005270483
DXGI_ERROR_NOT_CURRENT = -2005270482
DXGI_ERROR_HW_PROTECTION_OUTOFMEMORY = -2005270480
DXGI_ERROR_DYNAMIC_CODE_POLICY_VIOLATION = -**********
DXGI_ERROR_NON_COMPOSITED_UI = -**********
DXCORE_ERROR_EVENT_NOT_UNREGISTERED = -**********
PRESENTATION_ERROR_LOST = -**********
DXGI_STATUS_UNOCCLUDED = 0x087A0009
DXGI_STATUS_DDA_WAS_STILL_DRAWING = 0x087A000A
DXGI_ERROR_MODE_CHANGE_IN_PROGRESS = -**********
DXGI_STATUS_PRESENT_REQUIRED = 0x087A002F
DXGI_ERROR_CACHE_CORRUPT = -**********
DXGI_ERROR_CACHE_FULL = -**********
DXGI_ERROR_CACHE_HASH_COLLISION = -**********
DXGI_ERROR_ALREADY_EXISTS = -**********
DXGI_ERROR_MPO_UNPINNED = -**********
DXGI_DDI_ERR_WASSTILLDRAWING = -**********
DXGI_DDI_ERR_UNSUPPORTED = -**********
DXGI_DDI_ERR_NONEXCLUSIVE = -**********
D3D10_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS = -**********
D3D10_ERROR_FILE_NOT_FOUND = -**********
D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS = -**********
D3D11_ERROR_FILE_NOT_FOUND = -**********
D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS = -**********
D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD = -**********
D3D12_ERROR_ADAPTER_NOT_FOUND = -**********
D3D12_ERROR_DRIVER_VERSION_MISMATCH = -**********
D3D12_ERROR_INVALID_REDIST = -**********
D2DERR_WRONG_STATE = -**********
D2DERR_NOT_INITIALIZED = -**********
D2DERR_UNSUPPORTED_OPERATION = -**********
D2DERR_SCANNER_FAILED = -**********
D2DERR_SCREEN_ACCESS_DENIED = -**********
D2DERR_DISPLAY_STATE_INVALID = -**********
D2DERR_ZERO_VECTOR = -**********
D2DERR_INTERNAL_ERROR = -**********
D2DERR_DISPLAY_FORMAT_NOT_SUPPORTED = -**********
D2DERR_INVALID_CALL = -**********
D2DERR_NO_HARDWARE_DEVICE = -2003238901
D2DERR_RECREATE_TARGET = -2003238900
D2DERR_TOO_MANY_SHADER_ELEMENTS = -2003238899
D2DERR_SHADER_COMPILE_FAILED = -2003238898
D2DERR_MAX_TEXTURE_SIZE_EXCEEDED = -2003238897
D2DERR_UNSUPPORTED_VERSION = -2003238896
D2DERR_BAD_NUMBER = -2003238895
D2DERR_WRONG_FACTORY = -2003238894
D2DERR_LAYER_ALREADY_IN_USE = -2003238893
D2DERR_POP_CALL_DID_NOT_MATCH_PUSH = -2003238892
D2DERR_WRONG_RESOURCE_DOMAIN = -2003238891
D2DERR_PUSH_POP_UNBALANCED = -2003238890
D2DERR_RENDER_TARGET_HAS_LAYER_OR_CLIPRECT = -2003238889
D2DERR_INCOMPATIBLE_BRUSH_TYPES = -2003238888
D2DERR_WIN32_ERROR = -2003238887
D2DERR_TARGET_NOT_GDI_COMPATIBLE = -2003238886
D2DERR_TEXT_EFFECT_IS_WRONG_TYPE = -2003238885
D2DERR_TEXT_RENDERER_NOT_RELEASED = -2003238884
D2DERR_EXCEEDS_MAX_BITMAP_SIZE = -2003238883
D2DERR_INVALID_GRAPH_CONFIGURATION = -2003238882
D2DERR_INVALID_INTERNAL_GRAPH_CONFIGURATION = -2003238881
D2DERR_CYCLIC_GRAPH = -2003238880
D2DERR_BITMAP_CANNOT_DRAW = -2003238879
D2DERR_OUTSTANDING_BITMAP_REFERENCES = -2003238878
D2DERR_ORIGINAL_TARGET_NOT_BOUND = -2003238877
D2DERR_INVALID_TARGET = -2003238876
D2DERR_BITMAP_BOUND_AS_TARGET = -2003238875
D2DERR_INSUFFICIENT_DEVICE_CAPABILITIES = -2003238874
D2DERR_INTERMEDIATE_TOO_LARGE = -2003238873
D2DERR_EFFECT_IS_NOT_REGISTERED = -2003238872
D2DERR_INVALID_PROPERTY = -2003238871
D2DERR_NO_SUBPROPERTIES = -2003238870
D2DERR_PRINT_JOB_CLOSED = -2003238869
D2DERR_PRINT_FORMAT_NOT_SUPPORTED = -2003238868
D2DERR_TOO_MANY_TRANSFORM_INPUTS = -2003238867
D2DERR_INVALID_GLYPH_IMAGE = -2003238866
DWRITE_E_FILEFORMAT = -2003283968
DWRITE_E_UNEXPECTED = -2003283967
DWRITE_E_NOFONT = -2003283966
DWRITE_E_FILENOTFOUND = -2003283965
DWRITE_E_FILEACCESS = -2003283964
DWRITE_E_FONTCOLLECTIONOBSOLETE = -2003283963
DWRITE_E_ALREADYREGISTERED = -2003283962
DWRITE_E_CACHEFORMAT = -2003283961
DWRITE_E_CACHEVERSION = -2003283960
DWRITE_E_UNSUPPORTEDOPERATION = -2003283959
DWRITE_E_TEXTRENDERERINCOMPATIBLE = -2003283958
DWRITE_E_FLOWDIRECTIONCONFLICTS = -2003283957
DWRITE_E_NOCOLOR = -2003283956
DWRITE_E_REMOTEFONT = -2003283955
DWRITE_E_DOWNLOADCANCELLED = -2003283954
DWRITE_E_DOWNLOADFAILED = -2003283953
DWRITE_E_TOOMANYDOWNLOADS = -2003283952
WINCODEC_ERR_WRONGSTATE = -2003292412
WINCODEC_ERR_VALUEOUTOFRANGE = -2003292411
WINCODEC_ERR_UNKNOWNIMAGEFORMAT = -2003292409
WINCODEC_ERR_UNSUPPORTEDVERSION = -2003292405
WINCODEC_ERR_NOTINITIALIZED = -2003292404
WINCODEC_ERR_ALREADYLOCKED = -2003292403
WINCODEC_ERR_PROPERTYNOTFOUND = -2003292352
WINCODEC_ERR_PROPERTYNOTSUPPORTED = -2003292351
WINCODEC_ERR_PROPERTYSIZE = -2003292350
WINCODEC_ERR_CODECPRESENT = -2003292349
WINCODEC_ERR_CODECNOTHUMBNAIL = -2003292348
WINCODEC_ERR_PALETTEUNAVAILABLE = -2003292347
WINCODEC_ERR_CODECTOOMANYSCANLINES = -2003292346
WINCODEC_ERR_INTERNALERROR = -2003292344
WINCODEC_ERR_SOURCERECTDOESNOTMATCHDIMENSIONS = -2003292343
WINCODEC_ERR_COMPONENTNOTFOUND = -2003292336
WINCODEC_ERR_IMAGESIZEOUTOFRANGE = -2003292335
WINCODEC_ERR_TOOMUCHMETADATA = -2003292334
WINCODEC_ERR_BADIMAGE = -2003292320
WINCODEC_ERR_BADHEADER = -2003292319
WINCODEC_ERR_FRAMEMISSING = -2003292318
WINCODEC_ERR_BADMETADATAHEADER = -2003292317
WINCODEC_ERR_BADSTREAMDATA = -2003292304
WINCODEC_ERR_STREAMWRITE = -2003292303
WINCODEC_ERR_STREAMREAD = -2003292302
WINCODEC_ERR_STREAMNOTAVAILABLE = -2003292301
WINCODEC_ERR_UNSUPPORTEDPIXELFORMAT = -2003292288
WINCODEC_ERR_UNSUPPORTEDOPERATION = -2003292287
WINCODEC_ERR_INVALIDREGISTRATION = -2003292278
WINCODEC_ERR_COMPONENTINITIALIZEFAILURE = -2003292277
WINCODEC_ERR_INSUFFICIENTBUFFER = -2003292276
WINCODEC_ERR_DUPLICATEMETADATAPRESENT = -2003292275
WINCODEC_ERR_PROPERTYUNEXPECTEDTYPE = -2003292274
WINCODEC_ERR_UNEXPECTEDSIZE = -2003292273
WINCODEC_ERR_INVALIDQUERYREQUEST = -2003292272
WINCODEC_ERR_UNEXPECTEDMETADATATYPE = -2003292271
WINCODEC_ERR_REQUESTONLYVALIDATMETADATAROOT = -2003292270
WINCODEC_ERR_INVALIDQUERYCHARACTER = -2003292269
WINCODEC_ERR_WIN32ERROR = -2003292268
WINCODEC_ERR_INVALIDPROGRESSIVELEVEL = -2003292267
WINCODEC_ERR_INVALIDJPEGSCANINDEX = -2003292266
MILERR_OBJECTBUSY = -2003304447
MILERR_INSUFFICIENTBUFFER = -2003304446
MILERR_WIN32ERROR = -2003304445
MILERR_SCANNER_FAILED = -2003304444
MILERR_SCREENACCESSDENIED = -2003304443
MILERR_DISPLAYSTATEINVALID = -2003304442
MILERR_NONINVERTIBLEMATRIX = -2003304441
MILERR_ZEROVECTOR = -2003304440
MILERR_TERMINATED = -2003304439
MILERR_BADNUMBER = -2003304438
MILERR_INTERNALERROR = -2003304320
MILERR_DISPLAYFORMATNOTSUPPORTED = -2003304316
MILERR_INVALIDCALL = -2003304315
MILERR_ALREADYLOCKED = -2003304314
MILERR_NOTLOCKED = -2003304313
MILERR_DEVICECANNOTRENDERTEXT = -2003304312
MILERR_GLYPHBITMAPMISSED = -2003304311
MILERR_MALFORMEDGLYPHCACHE = -2003304310
MILERR_GENERIC_IGNORE = -2003304309
MILERR_MALFORMED_GUIDELINE_DATA = -2003304308
MILERR_NO_HARDWARE_DEVICE = -2003304307
MILERR_NEED_RECREATE_AND_PRESENT = -2003304306
MILERR_ALREADY_INITIALIZED = -2003304305
MILERR_MISMATCHED_SIZE = -2003304304
MILERR_NO_REDIRECTION_SURFACE_AVAILABLE = -2003304303
MILERR_REMOTING_NOT_SUPPORTED = -2003304302
MILERR_QUEUED_PRESENT_NOT_SUPPORTED = -2003304301
MILERR_NOT_QUEUING_PRESENTS = -2003304300
MILERR_NO_REDIRECTION_SURFACE_RETRY_LATER = -2003304299
MILERR_TOOMANYSHADERELEMNTS = -2003304298
MILERR_MROW_READLOCK_FAILED = -2003304297
MILERR_MROW_UPDATE_FAILED = -2003304296
MILERR_SHADER_COMPILE_FAILED = -2003304295
MILERR_MAX_TEXTURE_SIZE_EXCEEDED = -2003304294
MILERR_QPC_TIME_WENT_BACKWARD = -2003304293
MILERR_DXGI_ENUMERATION_OUT_OF_SYNC = -2003304291
MILERR_ADAPTER_NOT_FOUND = -2003304290
MILERR_COLORSPACE_NOT_SUPPORTED = -2003304289
MILERR_PREFILTER_NOT_SUPPORTED = -2003304288
MILERR_DISPLAYID_ACCESS_DENIED = -2003304287
UCEERR_INVALIDPACKETHEADER = -2003303424
UCEERR_UNKNOWNPACKET = -2003303423
UCEERR_ILLEGALPACKET = -2003303422
UCEERR_MALFORMEDPACKET = -2003303421
UCEERR_ILLEGALHANDLE = -2003303420
UCEERR_HANDLELOOKUPFAILED = -2003303419
UCEERR_RENDERTHREADFAILURE = -2003303418
UCEERR_CTXSTACKFRSTTARGETNULL = -2003303417
UCEERR_CONNECTIONIDLOOKUPFAILED = -2003303416
UCEERR_BLOCKSFULL = -2003303415
UCEERR_MEMORYFAILURE = -2003303414
UCEERR_PACKETRECORDOUTOFRANGE = -2003303413
UCEERR_ILLEGALRECORDTYPE = -2003303412
UCEERR_OUTOFHANDLES = -2003303411
UCEERR_UNCHANGABLE_UPDATE_ATTEMPTED = -2003303410
UCEERR_NO_MULTIPLE_WORKER_THREADS = -2003303409
UCEERR_REMOTINGNOTSUPPORTED = -2003303408
UCEERR_MISSINGENDCOMMAND = -2003303407
UCEERR_MISSINGBEGINCOMMAND = -2003303406
UCEERR_CHANNELSYNCTIMEDOUT = -2003303405
UCEERR_CHANNELSYNCABANDONED = -2003303404
UCEERR_UNSUPPORTEDTRANSPORTVERSION = -2003303403
UCEERR_TRANSPORTUNAVAILABLE = -2003303402
UCEERR_FEEDBACK_UNSUPPORTED = -2003303401
UCEERR_COMMANDTRANSPORTDENIED = -2003303400
UCEERR_GRAPHICSSTREAMUNAVAILABLE = -2003303399
UCEERR_GRAPHICSSTREAMALREADYOPEN = -2003303392
UCEERR_TRANSPORTDISCONNECTED = -2003303391
UCEERR_TRANSPORTOVERLOADED = -2003303390
UCEERR_PARTITION_ZOMBIED = -2003303389
MILAVERR_NOCLOCK = -2003303168
MILAVERR_NOMEDIATYPE = -2003303167
MILAVERR_NOVIDEOMIXER = -2003303166
MILAVERR_NOVIDEOPRESENTER = -2003303165
MILAVERR_NOREADYFRAMES = -2003303164
MILAVERR_MODULENOTLOADED = -2003303163
MILAVERR_WMPFACTORYNOTREGISTERED = -2003303162
MILAVERR_INVALIDWMPVERSION = -2003303161
MILAVERR_INSUFFICIENTVIDEORESOURCES = -2003303160
MILAVERR_VIDEOACCELERATIONNOTAVAILABLE = -2003303159
MILAVERR_REQUESTEDTEXTURETOOBIG = -2003303158
MILAVERR_SEEKFAILED = -2003303157
MILAVERR_UNEXPECTEDWMPFAILURE = -2003303156
MILAVERR_MEDIAPLAYERCLOSED = -2003303155
MILAVERR_UNKNOWNHARDWAREERROR = -2003303154
MILEFFECTSERR_UNKNOWNPROPERTY = -2003302898
MILEFFECTSERR_EFFECTNOTPARTOFGROUP = -2003302897
MILEFFECTSERR_NOINPUTSOURCEATTACHED = -2003302896
MILEFFECTSERR_CONNECTORNOTCONNECTED = -2003302895
MILEFFECTSERR_CONNECTORNOTASSOCIATEDWITHEFFECT = -2003302894
MILEFFECTSERR_RESERVED = -2003302893
MILEFFECTSERR_CYCLEDETECTED = -2003302892
MILEFFECTSERR_EFFECTINMORETHANONEGRAPH = -2003302891
MILEFFECTSERR_EFFECTALREADYINAGRAPH = -2003302890
MILEFFECTSERR_EFFECTHASNOCHILDREN = -2003302889
MILEFFECTSERR_ALREADYATTACHEDTOLISTENER = -2003302888
MILEFFECTSERR_NOTAFFINETRANSFORM = -2003302887
MILEFFECTSERR_EMPTYBOUNDS = -2003302886
MILEFFECTSERR_OUTPUTSIZETOOLARGE = -**********
DWMERR_STATE_TRANSITION_FAILED = -**********
DWMERR_THEME_FAILED = -**********
DWMERR_CATASTROPHIC_FAILURE = -**********
DCOMPOSITION_ERROR_WINDOW_ALREADY_COMPOSED = -**********
DCOMPOSITION_ERROR_SURFACE_BEING_RENDERED = -**********
DCOMPOSITION_ERROR_SURFACE_NOT_BEING_RENDERED = -**********
ONL_E_INVALID_AUTHENTICATION_TARGET = -**********
ONL_E_ACCESS_DENIED_BY_TOU = -**********
ONL_E_INVALID_APPLICATION = -**********
ONL_E_PASSWORD_UPDATE_REQUIRED = -**********
ONL_E_ACCOUNT_UPDATE_REQUIRED = -**********
ONL_E_FORCESIGNIN = -**********
ONL_E_ACCOUNT_LOCKED = -**********
ONL_E_PARENTAL_CONSENT_REQUIRED = -**********
ONL_E_EMAIL_VERIFICATION_REQUIRED = -**********
ONL_E_ACCOUNT_SUSPENDED_COMPROIMISE = -**********
ONL_E_ACCOUNT_SUSPENDED_ABUSE = -**********
ONL_E_ACTION_REQUIRED = -**********
ONL_CONNECTION_COUNT_LIMIT = -**********
ONL_E_CONNECTED_ACCOUNT_CAN_NOT_SIGNOUT = -**********
ONL_E_USER_AUTHENTICATION_REQUIRED = -**********
ONL_E_REQUEST_THROTTLED = -**********
FA_E_MAX_PERSISTED_ITEMS_REACHED = -**********
FA_E_HOMEGROUP_NOT_AVAILABLE = -**********
E_MONITOR_RESOLUTION_TOO_LOW = -**********
E_ELEVATED_ACTIVATION_NOT_SUPPORTED = -**********
E_UAC_DISABLED = -**********
E_FULL_ADMIN_NOT_SUPPORTED = -**********
E_APPLICATION_NOT_REGISTERED = -**********
E_MULTIPLE_EXTENSIONS_FOR_APPLICATION = -**********
E_MULTIPLE_PACKAGES_FOR_FAMILY = -**********
E_APPLICATION_MANAGER_NOT_RUNNING = -**********
S_STORE_LAUNCHED_FOR_REMEDIATION = 0x00270258
S_APPLICATION_ACTIVATION_ERROR_HANDLED_BY_DIALOG = 0x00270259
E_APPLICATION_ACTIVATION_TIMED_OUT = -**********
E_APPLICATION_ACTIVATION_EXEC_FAILURE = -**********
E_APPLICATION_TEMPORARY_LICENSE_ERROR = -**********
E_APPLICATION_TRIAL_LICENSE_EXPIRED = -**********
E_SKYDRIVE_ROOT_TARGET_FILE_SYSTEM_NOT_SUPPORTED = -**********
E_SKYDRIVE_ROOT_TARGET_OVERLAP = -**********
E_SKYDRIVE_ROOT_TARGET_CANNOT_INDEX = -**********
E_SKYDRIVE_FILE_NOT_UPLOADED = -**********
E_SKYDRIVE_UPDATE_AVAILABILITY_FAIL = -**********
E_SKYDRIVE_ROOT_TARGET_VOLUME_ROOT_NOT_SUPPORTED = -**********
E_SYNCENGINE_FILE_SIZE_OVER_LIMIT = -**********
E_SYNCENGINE_FILE_SIZE_EXCEEDS_REMAINING_QUOTA = -**********
E_SYNCENGINE_UNSUPPORTED_FILE_NAME = -**********
E_SYNCENGINE_FOLDER_ITEM_COUNT_LIMIT_EXCEEDED = -**********
E_SYNCENGINE_FILE_SYNC_PARTNER_ERROR = -**********
E_SYNCENGINE_SYNC_PAUSED_BY_SERVICE = -**********
E_SYNCENGINE_FILE_IDENTIFIER_UNKNOWN = -**********
E_SYNCENGINE_SERVICE_AUTHENTICATION_FAILED = -**********
E_SYNCENGINE_UNKNOWN_SERVICE_ERROR = -**********
E_SYNCENGINE_SERVICE_RETURNED_UNEXPECTED_SIZE = -**********
E_SYNCENGINE_REQUEST_BLOCKED_BY_SERVICE = -**********
E_SYNCENGINE_REQUEST_BLOCKED_DUE_TO_CLIENT_ERROR = -**********
E_SYNCENGINE_FOLDER_INACCESSIBLE = -2013081599
E_SYNCENGINE_UNSUPPORTED_FOLDER_NAME = -2013081598
E_SYNCENGINE_UNSUPPORTED_MARKET = -2013081597
E_SYNCENGINE_PATH_LENGTH_LIMIT_EXCEEDED = -2013081596
E_SYNCENGINE_REMOTE_PATH_LENGTH_LIMIT_EXCEEDED = -2013081595
E_SYNCENGINE_CLIENT_UPDATE_NEEDED = -2013081594
E_SYNCENGINE_PROXY_AUTHENTICATION_REQUIRED = -2013081593
E_SYNCENGINE_STORAGE_SERVICE_PROVISIONING_FAILED = -2013081592
E_SYNCENGINE_UNSUPPORTED_REPARSE_POINT = -2013081591
E_SYNCENGINE_STORAGE_SERVICE_BLOCKED = -2013081590
E_SYNCENGINE_FOLDER_IN_REDIRECTION = -2013081589
EAS_E_POLICY_NOT_MANAGED_BY_OS = -2141913087
EAS_E_POLICY_COMPLIANT_WITH_ACTIONS = -2141913086
EAS_E_REQUESTED_POLICY_NOT_ENFORCEABLE = -2141913085
EAS_E_CURRENT_USER_HAS_BLANK_PASSWORD = -2141913084
EAS_E_REQUESTED_POLICY_PASSWORD_EXPIRATION_INCOMPATIBLE = -2141913083
EAS_E_USER_CANNOT_CHANGE_PASSWORD = -2141913082
EAS_E_ADMINS_HAVE_BLANK_PASSWORD = -2141913081
EAS_E_ADMINS_CANNOT_CHANGE_PASSWORD = -2141913080
EAS_E_LOCAL_CONTROLLED_USERS_CANNOT_CHANGE_PASSWORD = -2141913079
EAS_E_PASSWORD_POLICY_NOT_ENFORCEABLE_FOR_CONNECTED_ADMINS = -2141913078
EAS_E_CONNECTED_ADMINS_NEED_TO_CHANGE_PASSWORD = -2141913077
EAS_E_PASSWORD_POLICY_NOT_ENFORCEABLE_FOR_CURRENT_CONNECTED_USER = -2141913076
EAS_E_CURRENT_CONNECTED_USER_NEED_TO_CHANGE_PASSWORD = -2141913075
WEB_E_UNSUPPORTED_FORMAT = -2089484287
WEB_E_INVALID_XML = -2089484286
WEB_E_MISSING_REQUIRED_ELEMENT = -2089484285
WEB_E_MISSING_REQUIRED_ATTRIBUTE = -2089484284
WEB_E_UNEXPECTED_CONTENT = -2089484283
WEB_E_RESOURCE_TOO_LARGE = -2089484282
WEB_E_INVALID_JSON_STRING = -2089484281
WEB_E_INVALID_JSON_NUMBER = -2089484280
WEB_E_JSON_VALUE_NOT_FOUND = -2089484279
HTTP_E_STATUS_UNEXPECTED = -2145845247
HTTP_E_STATUS_UNEXPECTED_REDIRECTION = -2145845245
HTTP_E_STATUS_UNEXPECTED_CLIENT_ERROR = -2145845244
HTTP_E_STATUS_UNEXPECTED_SERVER_ERROR = -2145845243
HTTP_E_STATUS_AMBIGUOUS = -2145844948
HTTP_E_STATUS_MOVED = -2145844947
HTTP_E_STATUS_REDIRECT = -2145844946
HTTP_E_STATUS_REDIRECT_METHOD = -2145844945
HTTP_E_STATUS_NOT_MODIFIED = -2145844944
HTTP_E_STATUS_USE_PROXY = -2145844943
HTTP_E_STATUS_REDIRECT_KEEP_VERB = -2145844941
HTTP_E_STATUS_BAD_REQUEST = -2145844848
HTTP_E_STATUS_DENIED = -2145844847
HTTP_E_STATUS_PAYMENT_REQ = -2145844846
HTTP_E_STATUS_FORBIDDEN = -2145844845
HTTP_E_STATUS_NOT_FOUND = -2145844844
HTTP_E_STATUS_BAD_METHOD = -2145844843
HTTP_E_STATUS_NONE_ACCEPTABLE = -2145844842
HTTP_E_STATUS_PROXY_AUTH_REQ = -2145844841
HTTP_E_STATUS_REQUEST_TIMEOUT = -2145844840
HTTP_E_STATUS_CONFLICT = -2145844839
HTTP_E_STATUS_GONE = -2145844838
HTTP_E_STATUS_LENGTH_REQUIRED = -2145844837
HTTP_E_STATUS_PRECOND_FAILED = -2145844836
HTTP_E_STATUS_REQUEST_TOO_LARGE = -2145844835
HTTP_E_STATUS_URI_TOO_LONG = -2145844834
HTTP_E_STATUS_UNSUPPORTED_MEDIA = -2145844833
HTTP_E_STATUS_RANGE_NOT_SATISFIABLE = -2145844832
HTTP_E_STATUS_EXPECTATION_FAILED = -2145844831
HTTP_E_STATUS_SERVER_ERROR = -2145844748
HTTP_E_STATUS_NOT_SUPPORTED = -2145844747
HTTP_E_STATUS_BAD_GATEWAY = -2145844746
HTTP_E_STATUS_SERVICE_UNAVAIL = -2145844745
HTTP_E_STATUS_GATEWAY_TIMEOUT = -2145844744
HTTP_E_STATUS_VERSION_NOT_SUP = -2145844743
E_INVALID_PROTOCOL_OPERATION = -2089418751
E_INVALID_PROTOCOL_FORMAT = -2089418750
E_PROTOCOL_EXTENSIONS_NOT_SUPPORTED = -2089418749
E_SUBPROTOCOL_NOT_SUPPORTED = -2089418748
E_PROTOCOL_VERSION_NOT_SUPPORTED = -2089418747
INPUT_E_OUT_OF_ORDER = -2143289344
INPUT_E_REENTRANCY = -2143289343
INPUT_E_MULTIMODAL = -2143289342
INPUT_E_PACKET = -2143289341
INPUT_E_FRAME = -2143289340
INPUT_E_HISTORY = -2143289339
INPUT_E_DEVICE_INFO = -2143289338
INPUT_E_TRANSFORM = -2143289337
INPUT_E_DEVICE_PROPERTY = -2143289336
INET_E_INVALID_URL = -2146697214
INET_E_NO_SESSION = -2146697213
INET_E_CANNOT_CONNECT = -2146697212
INET_E_RESOURCE_NOT_FOUND = -2146697211
INET_E_OBJECT_NOT_FOUND = -2146697210
INET_E_DATA_NOT_AVAILABLE = -2146697209
INET_E_DOWNLOAD_FAILURE = -2146697208
INET_E_AUTHENTICATION_REQUIRED = -2146697207
INET_E_NO_VALID_MEDIA = -2146697206
INET_E_CONNECTION_TIMEOUT = -2146697205
INET_E_INVALID_REQUEST = -2146697204
INET_E_UNKNOWN_PROTOCOL = -2146697203
INET_E_SECURITY_PROBLEM = -2146697202
INET_E_CANNOT_LOAD_DATA = -2146697201
INET_E_CANNOT_INSTANTIATE_OBJECT = -2146697200
INET_E_INVALID_CERTIFICATE = -2146697191
INET_E_REDIRECT_FAILED = -2146697196
INET_E_REDIRECT_TO_DIR = -2146697195
ERROR_DBG_CREATE_PROCESS_FAILURE_LOCKDOWN = -2135949311
ERROR_DBG_ATTACH_PROCESS_FAILURE_LOCKDOWN = -**********
ERROR_DBG_CONNECT_SERVER_FAILURE_LOCKDOWN = -**********
ERROR_DBG_START_SERVER_FAILURE_LOCKDOWN = -**********
HSP_E_ERROR_MASK = -**********
HSP_E_INTERNAL_ERROR = -**********
HSP_BS_ERROR_MASK = -**********
HSP_BS_INTERNAL_ERROR = -**********
HSP_DRV_ERROR_MASK = -**********
HSP_DRV_INTERNAL_ERROR = -**********
HSP_BASE_ERROR_MASK = -**********
HSP_BASE_INTERNAL_ERROR = -**********
HSP_KSP_ERROR_MASK = -**********
HSP_KSP_DEVICE_NOT_READY = -**********
HSP_KSP_INVALID_PROVIDER_HANDLE = -**********
HSP_KSP_INVALID_KEY_HANDLE = -**********
HSP_KSP_INVALID_PARAMETER = -**********
HSP_KSP_BUFFER_TOO_SMALL = -**********
HSP_KSP_NOT_SUPPORTED = -**********
HSP_KSP_INVALID_DATA = -**********
HSP_KSP_INVALID_FLAGS = -**********
HSP_KSP_ALGORITHM_NOT_SUPPORTED = -**********
HSP_KSP_KEY_ALREADY_FINALIZED = -**********
HSP_KSP_KEY_NOT_FINALIZED = -**********
HSP_KSP_INVALID_KEY_TYPE = -**********
HSP_KSP_NO_MEMORY = -**********
HSP_KSP_PARAMETER_NOT_SET = -**********
HSP_KSP_KEY_EXISTS = -**********
HSP_KSP_KEY_MISSING = -**********
HSP_KSP_KEY_LOAD_FAIL = -**********
HSP_KSP_NO_MORE_ITEMS = -**********
HSP_KSP_INTERNAL_ERROR = -**********
ERROR_IO_PREEMPTED = -**********
JSCRIPT_E_CANTEXECUTE = -**********
WEP_E_NOT_PROVISIONED_ON_ALL_VOLUMES = -**********
WEP_E_FIXED_DATA_NOT_SUPPORTED = -**********
WEP_E_HARDWARE_NOT_COMPLIANT = -**********
WEP_E_LOCK_NOT_CONFIGURED = -**********
WEP_E_PROTECTION_SUSPENDED = -**********
WEP_E_NO_LICENSE = -**********
WEP_E_OS_NOT_PROTECTED = -**********
WEP_E_UNEXPECTED_FAIL = -**********
WEP_E_BUFFER_TOO_LARGE = -**********
ERROR_SVHDX_ERROR_STORED = -**********
ERROR_SVHDX_ERROR_NOT_AVAILABLE = -**********
ERROR_SVHDX_UNIT_ATTENTION_AVAILABLE = -**********
ERROR_SVHDX_UNIT_ATTENTION_CAPACITY_DATA_CHANGED = -**********
ERROR_SVHDX_UNIT_ATTENTION_RESERVATIONS_PREEMPTED = -**********
ERROR_SVHDX_UNIT_ATTENTION_RESERVATIONS_RELEASED = -**********
ERROR_SVHDX_UNIT_ATTENTION_REGISTRATIONS_PREEMPTED = -**********
ERROR_SVHDX_UNIT_ATTENTION_OPERATING_DEFINITION_CHANGED = -**********
ERROR_SVHDX_RESERVATION_CONFLICT = -**********
ERROR_SVHDX_WRONG_FILE_TYPE = -**********
ERROR_SVHDX_VERSION_MISMATCH = -**********
ERROR_VHD_SHARED = -**********
ERROR_SVHDX_NO_INITIATOR = -**********
ERROR_VHDSET_BACKING_STORAGE_NOT_FOUND = -**********
ERROR_SMB_NO_PREAUTH_INTEGRITY_HASH_OVERLAP = -**********
ERROR_SMB_BAD_CLUSTER_DIALECT = -**********
ERROR_SMB_NO_SIGNING_ALGORITHM_OVERLAP = -1067646974
WININET_E_OUT_OF_HANDLES = -2147012895
WININET_E_TIMEOUT = -2147012894
WININET_E_EXTENDED_ERROR = -2147012893
WININET_E_INTERNAL_ERROR = -2147012892
WININET_E_INVALID_URL = -2147012891
WININET_E_UNRECOGNIZED_SCHEME = -2147012890
WININET_E_NAME_NOT_RESOLVED = -2147012889
WININET_E_PROTOCOL_NOT_FOUND = -2147012888
WININET_E_INVALID_OPTION = -2147012887
WININET_E_BAD_OPTION_LENGTH = -2147012886
WININET_E_OPTION_NOT_SETTABLE = -2147012885
WININET_E_SHUTDOWN = -2147012884
WININET_E_INCORRECT_USER_NAME = -2147012883
WININET_E_INCORRECT_PASSWORD = -2147012882
WININET_E_LOGIN_FAILURE = -2147012881
WININET_E_INVALID_OPERATION = -2147012880
WININET_E_OPERATION_CANCELLED = -2147012879
WININET_E_INCORRECT_HANDLE_TYPE = -2147012878
WININET_E_INCORRECT_HANDLE_STATE = -2147012877
WININET_E_NOT_PROXY_REQUEST = -2147012876
WININET_E_REGISTRY_VALUE_NOT_FOUND = -2147012875
WININET_E_BAD_REGISTRY_PARAMETER = -2147012874
WININET_E_NO_DIRECT_ACCESS = -2147012873
WININET_E_NO_CONTEXT = -2147012872
WININET_E_NO_CALLBACK = -2147012871
WININET_E_REQUEST_PENDING = -2147012870
WININET_E_INCORRECT_FORMAT = -2147012869
WININET_E_ITEM_NOT_FOUND = -2147012868
WININET_E_CANNOT_CONNECT = -2147012867
WININET_E_CONNECTION_ABORTED = -2147012866
WININET_E_CONNECTION_RESET = -2147012865
WININET_E_FORCE_RETRY = -2147012864
WININET_E_INVALID_PROXY_REQUEST = -2147012863
WININET_E_NEED_UI = -2147012862
WININET_E_HANDLE_EXISTS = -2147012860
WININET_E_SEC_CERT_DATE_INVALID = -2147012859
WININET_E_SEC_CERT_CN_INVALID = -2147012858
WININET_E_HTTP_TO_HTTPS_ON_REDIR = -2147012857
WININET_E_HTTPS_TO_HTTP_ON_REDIR = -2147012856
WININET_E_MIXED_SECURITY = -2147012855
WININET_E_CHG_POST_IS_NON_SECURE = -2147012854
WININET_E_POST_IS_NON_SECURE = -2147012853
WININET_E_CLIENT_AUTH_CERT_NEEDED = -2147012852
WININET_E_INVALID_CA = -2147012851
WININET_E_CLIENT_AUTH_NOT_SETUP = -2147012850
WININET_E_ASYNC_THREAD_FAILED = -2147012849
WININET_E_REDIRECT_SCHEME_CHANGE = -2147012848
WININET_E_DIALOG_PENDING = -2147012847
WININET_E_RETRY_DIALOG = -2147012846
WININET_E_NO_NEW_CONTAINERS = -2147012845
WININET_E_HTTPS_HTTP_SUBMIT_REDIR = -2147012844
WININET_E_SEC_CERT_ERRORS = -2147012841
WININET_E_SEC_CERT_REV_FAILED = -2147012839
WININET_E_HEADER_NOT_FOUND = -2147012746
WININET_E_DOWNLEVEL_SERVER = -2147012745
WININET_E_INVALID_SERVER_RESPONSE = -2147012744
WININET_E_INVALID_HEADER = -2147012743
WININET_E_INVALID_QUERY_REQUEST = -2147012742
WININET_E_HEADER_ALREADY_EXISTS = -2147012741
WININET_E_REDIRECT_FAILED = -2147012740
WININET_E_SECURITY_CHANNEL_ERROR = -2147012739
WININET_E_UNABLE_TO_CACHE_FILE = -2147012738
WININET_E_TCPIP_NOT_INSTALLED = -2147012737
WININET_E_DISCONNECTED = -2147012733
WININET_E_SERVER_UNREACHABLE = -2147012732
WININET_E_PROXY_SERVER_UNREACHABLE = -2147012731
WININET_E_BAD_AUTO_PROXY_SCRIPT = -2147012730
WININET_E_UNABLE_TO_DOWNLOAD_SCRIPT = -2147012729
WININET_E_SEC_INVALID_CERT = -2147012727
WININET_E_SEC_CERT_REVOKED = -2147012726
WININET_E_FAILED_DUETOSECURITYCHECK = -2147012725
WININET_E_NOT_INITIALIZED = -2147012724
WININET_E_LOGIN_FAILURE_DISPLAY_ENTITY_BODY = -2147012722
WININET_E_DECODING_FAILED = -2147012721
WININET_E_NOT_REDIRECTED = -2147012736
WININET_E_COOKIE_NEEDS_CONFIRMATION = -2147012735
WININET_E_COOKIE_DECLINED = -2147012734
WININET_E_REDIRECT_NEEDS_CONFIRMATION = -2147012728
SQLITE_E_ERROR = -2018574335
SQLITE_E_INTERNAL = -2018574334
SQLITE_E_PERM = -2018574333
SQLITE_E_ABORT = -2018574332
SQLITE_E_BUSY = -2018574331
SQLITE_E_LOCKED = -2018574330
SQLITE_E_NOMEM = -2018574329
SQLITE_E_READONLY = -2018574328
SQLITE_E_INTERRUPT = -2018574327
SQLITE_E_IOERR = -2018574326
SQLITE_E_CORRUPT = -2018574325
SQLITE_E_NOTFOUND = -2018574324
SQLITE_E_FULL = -2018574323
SQLITE_E_CANTOPEN = -2018574322
SQLITE_E_PROTOCOL = -2018574321
SQLITE_E_EMPTY = -2018574320
SQLITE_E_SCHEMA = -2018574319
SQLITE_E_TOOBIG = -2018574318
SQLITE_E_CONSTRAINT = -2018574317
SQLITE_E_MISMATCH = -2018574316
SQLITE_E_MISUSE = -2018574315
SQLITE_E_NOLFS = -2018574314
SQLITE_E_AUTH = -2018574313
SQLITE_E_FORMAT = -2018574312
SQLITE_E_RANGE = -2018574311
SQLITE_E_NOTADB = -2018574310
SQLITE_E_NOTICE = -2018574309
SQLITE_E_WARNING = -2018574308
SQLITE_E_ROW = -2018574236
SQLITE_E_DONE = -2018574235
SQLITE_E_IOERR_READ = -2018574070
SQLITE_E_IOERR_SHORT_READ = -2018573814
SQLITE_E_IOERR_WRITE = -2018573558
SQLITE_E_IOERR_FSYNC = -2018573302
SQLITE_E_IOERR_DIR_FSYNC = -2018573046
SQLITE_E_IOERR_TRUNCATE = -2018572790
SQLITE_E_IOERR_FSTAT = -2018572534
SQLITE_E_IOERR_UNLOCK = -2018572278
SQLITE_E_IOERR_RDLOCK = -2018572022
SQLITE_E_IOERR_DELETE = -2018571766
SQLITE_E_IOERR_BLOCKED = -2018571510
SQLITE_E_IOERR_NOMEM = -2018571254
SQLITE_E_IOERR_ACCESS = -2018570998
SQLITE_E_IOERR_CHECKRESERVEDLOCK = -2018570742
SQLITE_E_IOERR_LOCK = -2018570486
SQLITE_E_IOERR_CLOSE = -2018570230
SQLITE_E_IOERR_DIR_CLOSE = -2018569974
SQLITE_E_IOERR_SHMOPEN = -2018569718
SQLITE_E_IOERR_SHMSIZE = -2018569462
SQLITE_E_IOERR_SHMLOCK = -2018569206
SQLITE_E_IOERR_SHMMAP = -2018568950
SQLITE_E_IOERR_SEEK = -2018568694
SQLITE_E_IOERR_DELETE_NOENT = -2018568438
SQLITE_E_IOERR_MMAP = -2018568182
SQLITE_E_IOERR_GETTEMPPATH = -2018567926
SQLITE_E_IOERR_CONVPATH = -2018567670
SQLITE_E_IOERR_VNODE = -2018567678
SQLITE_E_IOERR_AUTH = -2018567677
SQLITE_E_LOCKED_SHAREDCACHE = -2018574074
SQLITE_E_BUSY_RECOVERY = -2018574075
SQLITE_E_BUSY_SNAPSHOT = -2018573819
SQLITE_E_CANTOPEN_NOTEMPDIR = -2018574066
SQLITE_E_CANTOPEN_ISDIR = -2018573810
SQLITE_E_CANTOPEN_FULLPATH = -2018573554
SQLITE_E_CANTOPEN_CONVPATH = -2018573298
SQLITE_E_CORRUPT_VTAB = -2018574069
SQLITE_E_READONLY_RECOVERY = -2018574072
SQLITE_E_READONLY_CANTLOCK = -2018573816
SQLITE_E_READONLY_ROLLBACK = -2018573560
SQLITE_E_READONLY_DBMOVED = -2018573304
SQLITE_E_ABORT_ROLLBACK = -2018573820
SQLITE_E_CONSTRAINT_CHECK = -2018574061
SQLITE_E_CONSTRAINT_COMMITHOOK = -2018573805
SQLITE_E_CONSTRAINT_FOREIGNKEY = -2018573549
SQLITE_E_CONSTRAINT_FUNCTION = -2018573293
SQLITE_E_CONSTRAINT_NOTNULL = -2018573037
SQLITE_E_CONSTRAINT_PRIMARYKEY = -2018572781
SQLITE_E_CONSTRAINT_TRIGGER = -2018572525
SQLITE_E_CONSTRAINT_UNIQUE = -2018572269
SQLITE_E_CONSTRAINT_VTAB = -2018572013
SQLITE_E_CONSTRAINT_ROWID = -2018571757
SQLITE_E_NOTICE_RECOVER_WAL = -2018574053
SQLITE_E_NOTICE_RECOVER_ROLLBACK = -2018573797
SQLITE_E_WARNING_AUTOINDEX = -2018574052
UTC_E_TOGGLE_TRACE_STARTED = -2017128447
UTC_E_ALTERNATIVE_TRACE_CANNOT_PREEMPT = -2017128446
UTC_E_AOT_NOT_RUNNING = -2017128445
UTC_E_SCRIPT_TYPE_INVALID = -2017128444
UTC_E_SCENARIODEF_NOT_FOUND = -2017128443
UTC_E_TRACEPROFILE_NOT_FOUND = -2017128442
UTC_E_FORWARDER_ALREADY_ENABLED = -2017128441
UTC_E_FORWARDER_ALREADY_DISABLED = -2017128440
UTC_E_EVENTLOG_ENTRY_MALFORMED = -2017128439
UTC_E_DIAGRULES_SCHEMAVERSION_MISMATCH = -2017128438
UTC_E_SCRIPT_TERMINATED = -2017128437
UTC_E_INVALID_CUSTOM_FILTER = -2017128436
UTC_E_TRACE_NOT_RUNNING = -2017128435
UTC_E_REESCALATED_TOO_QUICKLY = -2017128434
UTC_E_ESCALATION_ALREADY_RUNNING = -2017128433
UTC_E_PERFTRACK_ALREADY_TRACING = -2017128432
UTC_E_REACHED_MAX_ESCALATIONS = -2017128431
UTC_E_FORWARDER_PRODUCER_MISMATCH = -2017128430
UTC_E_INTENTIONAL_SCRIPT_FAILURE = -2017128429
UTC_E_SQM_INIT_FAILED = -2017128428
UTC_E_NO_WER_LOGGER_SUPPORTED = -2017128427
UTC_E_TRACERS_DONT_EXIST = -2017128426
UTC_E_WINRT_INIT_FAILED = -2017128425
UTC_E_SCENARIODEF_SCHEMAVERSION_MISMATCH = -2017128424
UTC_E_INVALID_FILTER = -2017128423
UTC_E_EXE_TERMINATED = -2017128422
UTC_E_ESCALATION_NOT_AUTHORIZED = -2017128421
UTC_E_SETUP_NOT_AUTHORIZED = -2017128420
UTC_E_CHILD_PROCESS_FAILED = -2017128419
UTC_E_COMMAND_LINE_NOT_AUTHORIZED = -2017128418
UTC_E_CANNOT_LOAD_SCENARIO_EDITOR_XML = -2017128417
UTC_E_ESCALATION_TIMED_OUT = -2017128416
UTC_E_SETUP_TIMED_OUT = -2017128415
UTC_E_TRIGGER_MISMATCH = -2017128414
UTC_E_TRIGGER_NOT_FOUND = -2017128413
UTC_E_SIF_NOT_SUPPORTED = -2017128412
UTC_E_DELAY_TERMINATED = -2017128411
UTC_E_DEVICE_TICKET_ERROR = -2017128410
UTC_E_TRACE_BUFFER_LIMIT_EXCEEDED = -2017128409
UTC_E_API_RESULT_UNAVAILABLE = -2017128408
UTC_E_RPC_TIMEOUT = -2017128407
UTC_E_RPC_WAIT_FAILED = -2017128406
UTC_E_API_BUSY = -2017128405
UTC_E_TRACE_MIN_DURATION_REQUIREMENT_NOT_MET = -2017128404
UTC_E_EXCLUSIVITY_NOT_AVAILABLE = -2017128403
UTC_E_GETFILE_FILE_PATH_NOT_APPROVED = -2017128402
UTC_E_ESCALATION_DIRECTORY_ALREADY_EXISTS = -2017128401
UTC_E_TIME_TRIGGER_ON_START_INVALID = -2017128400
UTC_E_TIME_TRIGGER_ONLY_VALID_ON_SINGLE_TRANSITION = -2017128399
UTC_E_TIME_TRIGGER_INVALID_TIME_RANGE = -2017128398
UTC_E_MULTIPLE_TIME_TRIGGER_ON_SINGLE_STATE = -2017128397
UTC_E_BINARY_MISSING = -2017128396
UTC_E_FAILED_TO_RESOLVE_CONTAINER_ID = -2017128394
UTC_E_UNABLE_TO_RESOLVE_SESSION = -2017128393
UTC_E_THROTTLED = -2017128392
UTC_E_UNAPPROVED_SCRIPT = -2017128391
UTC_E_SCRIPT_MISSING = -2017128390
UTC_E_SCENARIO_THROTTLED = -2017128389
UTC_E_API_NOT_SUPPORTED = -2017128388
UTC_E_GETFILE_EXTERNAL_PATH_NOT_APPROVED = -2017128387
UTC_E_TRY_GET_SCENARIO_TIMEOUT_EXCEEDED = -2017128386
UTC_E_CERT_REV_FAILED = -2017128385
UTC_E_FAILED_TO_START_NDISCAP = -2017128384
UTC_E_KERNELDUMP_LIMIT_REACHED = -2017128383
UTC_E_MISSING_AGGREGATE_EVENT_TAG = -2017128382
UTC_E_INVALID_AGGREGATION_STRUCT = -2017128381
UTC_E_ACTION_NOT_SUPPORTED_IN_DESTINATION = -2017128380
UTC_E_FILTER_MISSING_ATTRIBUTE = -2017128379
UTC_E_FILTER_INVALID_TYPE = -2017128378
UTC_E_FILTER_VARIABLE_NOT_FOUND = -2017128377
UTC_E_FILTER_FUNCTION_RESTRICTED = -2017128376
UTC_E_FILTER_VERSION_MISMATCH = -2017128375
UTC_E_FILTER_INVALID_FUNCTION = -2017128368
UTC_E_FILTER_INVALID_FUNCTION_PARAMS = -2017128367
UTC_E_FILTER_INVALID_COMMAND = -2017128366
UTC_E_FILTER_ILLEGAL_EVAL = -2017128365
UTC_E_TTTRACER_RETURNED_ERROR = -2017128364
UTC_E_AGENT_DIAGNOSTICS_TOO_LARGE = -2017128363
UTC_E_FAILED_TO_RECEIVE_AGENT_DIAGNOSTICS = -2017128362
UTC_E_SCENARIO_HAS_NO_ACTIONS = -2017128361
UTC_E_TTTRACER_STORAGE_FULL = -2017128360
UTC_E_INSUFFICIENT_SPACE_TO_START_TRACE = -2017128359
UTC_E_ESCALATION_CANCELLED_AT_SHUTDOWN = -2017128358
UTC_E_GETFILEINFOACTION_FILE_NOT_APPROVED = -2017128357
UTC_E_SETREGKEYACTION_TYPE_NOT_APPROVED = -2017128356
UTC_E_TRACE_THROTTLED = -2017128355
WINML_ERR_INVALID_DEVICE = -2003828735
WINML_ERR_INVALID_BINDING = -2003828734
WINML_ERR_VALUE_NOTFOUND = -2003828733
WINML_ERR_SIZE_MISMATCH = -2003828732
ERROR_QUIC_HANDSHAKE_FAILURE = -2143223808
ERROR_QUIC_VER_NEG_FAILURE = -2143223807
ERROR_QUIC_USER_CANCELED = -2143223806
ERROR_QUIC_INTERNAL_ERROR = -2143223805
ERROR_QUIC_PROTOCOL_VIOLATION = -2143223804
ERROR_QUIC_CONNECTION_IDLE = -2143223803
ERROR_QUIC_CONNECTION_TIMEOUT = -2143223802
ERROR_QUIC_ALPN_NEG_FAILURE = -2143223801
IORING_E_REQUIRED_FLAG_NOT_SUPPORTED = -2142896127
IORING_E_SUBMISSION_QUEUE_FULL = -2142896126
IORING_E_VERSION_NOT_SUPPORTED = -2142896125
IORING_E_SUBMISSION_QUEUE_TOO_BIG = -2142896124
IORING_E_COMPLETION_QUEUE_TOO_BIG = -2142896123
IORING_E_SUBMIT_IN_PROGRESS = -2142896122
IORING_E_CORRUPT = -2142896121
IORING_E_COMPLETION_QUEUE_TOO_FULL = -2142896120


# Generated by h2py from C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\cderr.h
CDERR_DIALOGFAILURE = 0xFFFF
CDERR_GENERALCODES = 0x0000
CDERR_STRUCTSIZE = 0x0001
CDERR_INITIALIZATION = 0x0002
CDERR_NOTEMPLATE = 0x0003
CDERR_NOHINSTANCE = 0x0004
CDERR_LOADSTRFAILURE = 0x0005
CDERR_FINDRESFAILURE = 0x0006
CDERR_LOADRESFAILURE = 0x0007
CDERR_LOCKRESFAILURE = 0x0008
CDERR_MEMALLOCFAILURE = 0x0009
CDERR_MEMLOCKFAILURE = 0x000A
CDERR_NOHOOK = 0x000B
CDERR_REGISTERMSGFAIL = 0x000C
PDERR_PRINTERCODES = 0x1000
PDERR_SETUPFAILURE = 0x1001
PDERR_PARSEFAILURE = 0x1002
PDERR_RETDEFFAILURE = 0x1003
PDERR_LOADDRVFAILURE = 0x1004
PDERR_GETDEVMODEFAIL = 0x1005
PDERR_INITFAILURE = 0x1006
PDERR_NODEVICES = 0x1007
PDERR_NODEFAULTPRN = 0x1008
PDERR_DNDMMISMATCH = 0x1009
PDERR_CREATEICFAILURE = 0x100A
PDERR_PRINTERNOTFOUND = 0x100B
PDERR_DEFAULTDIFFERENT = 0x100C
CFERR_CHOOSEFONTCODES = 0x2000
CFERR_NOFONTS = 0x2001
CFERR_MAXLESSTHANMIN = 0x2002
FNERR_FILENAMECODES = 0x3000
FNERR_SUBCLASSFAILURE = 0x3001
FNERR_INVALIDFILENAME = 0x3002
FNERR_BUFFERTOOSMALL = 0x3003
FRERR_FINDREPLACECODES = 0x4000
FRERR_BUFFERLENGTHZERO = 0x4001
CCERR_CHOOSECOLORCODES = 0x5000
