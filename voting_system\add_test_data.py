import os
import django
import datetime

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'voting_system.settings')
django.setup()

from django.contrib.auth.models import User
from voting.models import (
    County, Constituency, PollingStation, Party, ElectionType, Election, Candidate, Voter
)
from django.utils import timezone

def create_test_data():
    print("Creating test data...")
    
    # Create counties
    counties = [
        County(name="Nairobi", code="047"),
        County(name="Mombasa", code="001"),
        County(name="Kisumu", code="042"),
        County(name="Nakuru", code="032"),
        County(name="Kiambu", code="022")
    ]
    County.objects.bulk_create(counties)
    print(f"Created {len(counties)} counties")
    
    # Create constituencies for each county
    constituencies = []
    for county in County.objects.all():
        if county.name == "Nairobi":
            constituencies.extend([
                Constituency(name="Westlands", code="001", county=county),
                Constituency(name="Dagoretti North", code="002", county=county),
                Constituency(name="Langata", code="003", county=county),
                Constituency(name="Kibra", code="004", county=county)
            ])
        elif county.name == "Mombasa":
            constituencies.extend([
                Constituency(name="Nyali", code="001", county=county),
                Constituency(name="Kisauni", code="002", county=county),
                Constituency(name="Likoni", code="003", county=county)
            ])
        elif county.name == "Kisumu":
            constituencies.extend([
                Constituency(name="Kisumu Central", code="001", county=county),
                Constituency(name="Kisumu East", code="002", county=county),
                Constituency(name="Kisumu West", code="003", county=county)
            ])
        elif county.name == "Nakuru":
            constituencies.extend([
                Constituency(name="Nakuru Town East", code="001", county=county),
                Constituency(name="Nakuru Town West", code="002", county=county),
                Constituency(name="Naivasha", code="003", county=county)
            ])
        elif county.name == "Kiambu":
            constituencies.extend([
                Constituency(name="Kiambaa", code="001", county=county),
                Constituency(name="Kikuyu", code="002", county=county),
                Constituency(name="Limuru", code="003", county=county)
            ])
    
    Constituency.objects.bulk_create(constituencies)
    print(f"Created {len(constituencies)} constituencies")
    
    # Create polling stations for each constituency
    polling_stations = []
    for constituency in Constituency.objects.all():
        for i in range(1, 4):  # 3 polling stations per constituency
            polling_stations.append(
                PollingStation(
                    name=f"{constituency.name} Polling Station {i}",
                    code=f"{constituency.code}-{i:03d}",
                    constituency=constituency
                )
            )
    
    PollingStation.objects.bulk_create(polling_stations)
    print(f"Created {len(polling_stations)} polling stations")
    
    # Create political parties
    parties = [
        Party(name="Jubilee Party", symbol_description="Red and Yellow colors with a handshake"),
        Party(name="Orange Democratic Movement", symbol_description="Orange fruit"),
        Party(name="Wiper Democratic Movement", symbol_description="Umbrella"),
        Party(name="Amani National Congress", symbol_description="House"),
        Party(name="Kenya African National Union", symbol_description="Cockerel")
    ]
    Party.objects.bulk_create(parties)
    print(f"Created {len(parties)} political parties")
    
    # Create election types
    election_types = [
        ElectionType(name="Presidential", description="Election for the President of Kenya"),
        ElectionType(name="Gubernatorial", description="Election for County Governors"),
        ElectionType(name="Senatorial", description="Election for County Senators"),
        ElectionType(name="National Assembly", description="Election for Members of Parliament"),
        ElectionType(name="County Assembly", description="Election for Members of County Assembly")
    ]
    ElectionType.objects.bulk_create(election_types)
    print(f"Created {len(election_types)} election types")
    
    # Create elections
    today = timezone.now()
    elections = [
        Election(
            name="2022 Presidential Election",
            description="General election for the President of Kenya",
            election_type=ElectionType.objects.get(name="Presidential"),
            start_date=today - datetime.timedelta(days=30),
            end_date=today + datetime.timedelta(days=30),
            active=True
        ),
        Election(
            name="2022 Gubernatorial Elections",
            description="Elections for County Governors",
            election_type=ElectionType.objects.get(name="Gubernatorial"),
            start_date=today - datetime.timedelta(days=30),
            end_date=today + datetime.timedelta(days=30),
            active=True
        ),
        Election(
            name="2022 Parliamentary Elections",
            description="Elections for Members of Parliament",
            election_type=ElectionType.objects.get(name="National Assembly"),
            start_date=today - datetime.timedelta(days=30),
            end_date=today + datetime.timedelta(days=30),
            active=True
        )
    ]
    Election.objects.bulk_create(elections)
    print(f"Created {len(elections)} elections")
    
    # Create candidates for the presidential election
    presidential_election = Election.objects.get(name="2022 Presidential Election")
    candidates = [
        Candidate(
            name="John Doe",
            party=Party.objects.get(name="Jubilee Party"),
            election=presidential_election,
            bio="Experienced leader with a vision for Kenya",
            is_approved=True
        ),
        Candidate(
            name="Jane Smith",
            party=Party.objects.get(name="Orange Democratic Movement"),
            election=presidential_election,
            bio="Fighting for equality and justice",
            is_approved=True
        ),
        Candidate(
            name="Robert Johnson",
            party=Party.objects.get(name="Wiper Democratic Movement"),
            election=presidential_election,
            bio="A new direction for Kenya",
            is_approved=True
        )
    ]
    Candidate.objects.bulk_create(candidates)
    print(f"Created {len(candidates)} presidential candidates")
    
    print("Test data creation completed successfully!")

if __name__ == "__main__":
    create_test_data()
