# Generated by Django 5.2 on 2025-05-26 10:41

import django.core.validators
import django.db.models.deletion
import voting.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('voting', '0002_validnationalid'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='voter',
            name='id_picture',
            field=models.ImageField(default='', help_text="Upload a clear photo of the voter's national ID", upload_to=voting.models.voter_id_picture_path),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='voter',
            name='verification_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='voter',
            name='verified_by',
            field=models.ForeignKey(blank=True, help_text='Admin who verified this voter', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='verified_voters', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='voter',
            name='voter_photo',
            field=models.ImageField(default='', help_text='Upload a clear photo of the voter', upload_to=voting.models.voter_photo_path),
            preserve_default=False,
        ),
        migrations.CreateModel(
            name='Worker',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('employee_id', models.CharField(max_length=20, unique=True)),
                ('phone_number', models.CharField(max_length=12, validators=[django.core.validators.RegexValidator('^\\+254\\d{9}$', 'Phone number must be in format +254XXXXXXXXX')])),
                ('is_active', models.BooleanField(default=True)),
                ('date_created', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_workers', to=settings.AUTH_USER_MODEL)),
                ('station_assigned', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='voting.pollingstation')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Registration Worker',
                'verbose_name_plural': 'Registration Workers',
            },
        ),
        migrations.AddField(
            model_name='voter',
            name='registered_by',
            field=models.ForeignKey(help_text='Worker who registered this voter', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='registered_voters', to='voting.worker'),
        ),
    ]
