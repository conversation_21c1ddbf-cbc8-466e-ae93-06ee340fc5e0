System.register(["proxy-compare"],function(D){"use strict";var x,K;return{setters:[function(v){x=v.markToTrack,K=v.getUntracked}],execute:function(){D({getVersion:_,proxy:U,ref:L,snapshot:B,subscribe:q});const v=c=>typeof c=="object"&&c!==null,f=new WeakMap,m=new WeakSet,T=(c=Object.is,l=(t,w)=>new Proxy(t,w),O=t=>v(t)&&!m.has(t)&&(Array.isArray(t)||!(Symbol.iterator in t))&&!(t instanceof WeakMap)&&!(t instanceof WeakSet)&&!(t instanceof Error)&&!(t instanceof Number)&&!(t instanceof Date)&&!(t instanceof String)&&!(t instanceof RegExp)&&!(t instanceof ArrayBuffer),P=t=>{switch(t.status){case"fulfilled":return t.value;case"rejected":throw t.reason;default:throw t}},u=new WeakMap,g=(t,w,b=P)=>{const s=u.get(t);if((s==null?void 0:s[0])===w)return s[1];const a=Array.isArray(t)?[]:Object.create(Object.getPrototypeOf(t));return x(a,!0),u.set(t,[w,a]),Reflect.ownKeys(t).forEach(k=>{if(Object.getOwnPropertyDescriptor(a,k))return;const y=Reflect.get(t,k),j={value:y,enumerable:!0,configurable:!0};if(m.has(y))x(y,!1);else if(y instanceof Promise)delete j.value,j.get=()=>b(y);else if(f.has(y)){const[i,W]=f.get(y);j.value=g(i,W(),b)}Object.defineProperty(a,k,j)}),a},h=new WeakMap,d=[1,1],E=t=>{if(!v(t))throw new Error("object required");const w=h.get(t);if(w)return w;let b=d[0];const s=new Set,a=(n,e=++d[0])=>{b!==e&&(b=e,s.forEach(r=>r(n,e)))};let k=d[1];const y=(n=++d[1])=>(k!==n&&!s.size&&(k=n,i.forEach(([e])=>{const r=e[1](n);r>b&&(b=r)})),b),j=n=>(e,r)=>{const o=[...e];o[1]=[n,...o[1]],a(o,r)},i=new Map,W=(n,e)=>{if(s.size){const r=e[3](j(n));i.set(n,[e,r])}else i.set(n,[e])},V=n=>{var e;const r=i.get(n);r&&(i.delete(n),(e=r[1])==null||e.call(r))},N=n=>(s.add(n),s.size===1&&i.forEach(([e,r],o)=>{const M=e[3](j(o));i.set(o,[e,M])}),()=>{s.delete(n),s.size===0&&i.forEach(([e,r],o)=>{r&&(r(),i.set(o,[e]))})}),z=Array.isArray(t)?[]:Object.create(Object.getPrototypeOf(t)),R=l(z,{deleteProperty(n,e){const r=Reflect.get(n,e);V(e);const o=Reflect.deleteProperty(n,e);return o&&a(["delete",[e],r]),o},set(n,e,r,o){const M=Reflect.has(n,e),S=Reflect.get(n,e,o);if(M&&(c(S,r)||h.has(r)&&c(S,h.get(r))))return!0;V(e),v(r)&&(r=K(r)||r);let A=r;if(r instanceof Promise)r.then(p=>{r.status="fulfilled",r.value=p,a(["resolve",[e],p])}).catch(p=>{r.status="rejected",r.reason=p,a(["reject",[e],p])});else{!f.has(r)&&O(r)&&(A=E(r));const p=!m.has(A)&&f.get(A);p&&W(e,p)}return Reflect.set(n,e,A,o),a(["set",[e],r,S]),!0}});h.set(t,R);const C=[z,y,g,N];return f.set(R,C),Reflect.ownKeys(t).forEach(n=>{const e=Object.getOwnPropertyDescriptor(t,n);"value"in e&&(R[n]=t[n],delete e.value,delete e.writable),Object.defineProperty(z,n,e)}),R})=>[E,f,m,c,l,O,P,u,g,h,d],[F]=T();function U(c={}){return F(c)}function _(c){const l=f.get(c);return l==null?void 0:l[1]()}function q(c,l,O){const P=f.get(c);let u;const g=[],h=P[3];let d=!1;const E=h(t=>{if(g.push(t),O){l(g.splice(0));return}u||(u=Promise.resolve().then(()=>{u=void 0,d&&l(g.splice(0))}))});return d=!0,()=>{d=!1,E()}}function B(c,l){const O=f.get(c),[P,u,g]=O;return g(P,u(),l)}function L(c){return m.add(c),c}const G=D("unstable_buildProxyFunction",T)}}});
