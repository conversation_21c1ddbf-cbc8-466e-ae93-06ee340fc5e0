# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# Azat, 2023
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-09-18 11:41-0300\n"
"PO-Revision-Date: 2023-12-04 18:40+0000\n"
"Last-Translator: Azat, 2023\n"
"Language-Team: Uyghur (http://app.transifex.com/django/django/language/ug/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ug\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Humanize"
msgstr "ئىنسانلاشتۇرۇش"

#. Translators: Ordinal format for 11 (11th), 12 (12th), and 13 (13th).
msgctxt "ordinal 11, 12, 13"
msgid "{}th"
msgstr "{}جى"

#. Translators: Ordinal format when value ends with 0, e.g. 80th.
msgctxt "ordinal 0"
msgid "{}th"
msgstr "{}جى"

#. Translators: Ordinal format when value ends with 1, e.g. 81st, except 11.
msgctxt "ordinal 1"
msgid "{}st"
msgstr "{}جى"

#. Translators: Ordinal format when value ends with 2, e.g. 82nd, except 12.
msgctxt "ordinal 2"
msgid "{}nd"
msgstr "{}جى"

#. Translators: Ordinal format when value ends with 3, e.g. 83rd, except 13.
msgctxt "ordinal 3"
msgid "{}rd"
msgstr "{}جى"

#. Translators: Ordinal format when value ends with 4, e.g. 84th.
msgctxt "ordinal 4"
msgid "{}th"
msgstr "{}جى"

#. Translators: Ordinal format when value ends with 5, e.g. 85th.
msgctxt "ordinal 5"
msgid "{}th"
msgstr "{}جى"

#. Translators: Ordinal format when value ends with 6, e.g. 86th.
msgctxt "ordinal 6"
msgid "{}th"
msgstr "{}جى"

#. Translators: Ordinal format when value ends with 7, e.g. 87th.
msgctxt "ordinal 7"
msgid "{}th"
msgstr "{}جى"

#. Translators: Ordinal format when value ends with 8, e.g. 88th.
msgctxt "ordinal 8"
msgid "{}th"
msgstr "{}جى"

#. Translators: Ordinal format when value ends with 9, e.g. 89th.
msgctxt "ordinal 9"
msgid "{}th"
msgstr "{}جى"

#, python-format
msgid "%(value)s million"
msgid_plural "%(value)s million"
msgstr[0] "%(value)s مىليون"
msgstr[1] "%(value)s مىليون"

#, python-format
msgid "%(value)s billion"
msgid_plural "%(value)s billion"
msgstr[0] "%(value)s مىليارد"
msgstr[1] "%(value)s مىليارد"

#, python-format
msgid "%(value)s trillion"
msgid_plural "%(value)s trillion"
msgstr[0] "%(value)s تىرىليون"
msgstr[1] "%(value)s تىرىليون"

#, python-format
msgid "%(value)s quadrillion"
msgid_plural "%(value)s quadrillion"
msgstr[0] "%(value)s كۋادىرىللىيۇن"
msgstr[1] "%(value)s كۋادىرىللىيۇن"

#, python-format
msgid "%(value)s quintillion"
msgid_plural "%(value)s quintillion"
msgstr[0] "%(value)s كۈيىنتىللىيون"
msgstr[1] "%(value)s كۈيىنتىللىيون"

#, python-format
msgid "%(value)s sextillion"
msgid_plural "%(value)s sextillion"
msgstr[0] "%(value)s سېكستىللىيون"
msgstr[1] "%(value)s سېكستىللىيون"

#, python-format
msgid "%(value)s septillion"
msgid_plural "%(value)s septillion"
msgstr[0] "%(value)s سېكستىللىيون"
msgstr[1] "%(value)s سېكستىللىيون"

#, python-format
msgid "%(value)s octillion"
msgid_plural "%(value)s octillion"
msgstr[0] "%(value)s ئوكتىلىيون"
msgstr[1] "%(value)s ئوكتىلىيون"

#, python-format
msgid "%(value)s nonillion"
msgid_plural "%(value)s nonillion"
msgstr[0] "%(value)s نونىللىيون"
msgstr[1] "%(value)s نونىللىيون"

#, python-format
msgid "%(value)s decillion"
msgid_plural "%(value)s decillion"
msgstr[0] "%(value)s دېكىللىيون"
msgstr[1] "%(value)s دېكىللىيون"

#, python-format
msgid "%(value)s googol"
msgid_plural "%(value)s googol"
msgstr[0] "%(value)s گۇگول "
msgstr[1] "%(value)s گۇگول"

msgid "one"
msgstr "بىر"

msgid "two"
msgstr "ئىككى"

msgid "three"
msgstr "ئۈچ"

msgid "four"
msgstr "تۆت"

msgid "five"
msgstr "بەش"

msgid "six"
msgstr "ئالتە"

msgid "seven"
msgstr "يەتتە"

msgid "eight"
msgstr "سەككىز"

msgid "nine"
msgstr "توققۇز"

msgid "today"
msgstr "بۈگۈن"

msgid "tomorrow"
msgstr "ئەتە"

msgid "yesterday"
msgstr "تۈنۈگۈن"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s ago"
msgstr "%(delta)s ئىلگىرى"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour ago"
msgid_plural "%(count)s hours ago"
msgstr[0] "1 سائەت ئىلگىرى"
msgstr[1] "%(count)s سائەت ئىلگىرى"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute ago"
msgid_plural "%(count)s minutes ago"
msgstr[0] "1 مىنۇت ئىلگىرى"
msgstr[1] "%(count)s مىنۇت ئىلگىرى"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second ago"
msgid_plural "%(count)s seconds ago"
msgstr[0] "1 سېكۇنت ئىلگىرى"
msgstr[1] "%(count)s سېكۇنت ئىلگىرى"

msgid "now"
msgstr "ھازىر"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second from now"
msgid_plural "%(count)s seconds from now"
msgstr[0] "1 سېكۇنتتىن كېيىن"
msgstr[1] "%(count)s سېكۇنتتىن كېيىن"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute from now"
msgid_plural "%(count)s minutes from now"
msgstr[0] "1 مىنۇتتىن كېيىن"
msgstr[1] "%(count)s مىنۇتتىن كېيىن"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour from now"
msgid_plural "%(count)s hours from now"
msgstr[0] "1 سائەتتىن كېيىن"
msgstr[1] "%(count)s سائەتتىن كېيىن"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s from now"
msgstr "%(delta)s دىن كېيىن"

#. Translators: 'naturaltime-past' strings will be included in '%(delta)s ago'
#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d year"
msgid_plural "%(num)d years"
msgstr[0] "%(num)d يىل"
msgstr[1] "%(num)d يىل"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d month"
msgid_plural "%(num)d months"
msgstr[0] "%(num)d ئاي"
msgstr[1] "%(num)d ئاي"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d week"
msgid_plural "%(num)d weeks"
msgstr[0] "%(num)d ھەپتە"
msgstr[1] "%(num)d ھەپتە"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d day"
msgid_plural "%(num)d days"
msgstr[0] "%(num)d كۈن"
msgstr[1] "%(num)d كۈن"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d hour"
msgid_plural "%(num)d hours"
msgstr[0] "%(num)d سائەت"
msgstr[1] "%(num)d سائەت"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d minute"
msgid_plural "%(num)d minutes"
msgstr[0] "%(num)d مىنۇت"
msgstr[1] "%(num)d مىنۇت"

#. Translators: 'naturaltime-future' strings will be included in '%(delta)s
#. from now'
#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d year"
msgid_plural "%(num)d years"
msgstr[0] "%(num)d يىل"
msgstr[1] "%(num)d يىل"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d month"
msgid_plural "%(num)d months"
msgstr[0] "%(num)d ئاي"
msgstr[1] "%(num)d ئاي"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d week"
msgid_plural "%(num)d weeks"
msgstr[0] "%(num)d ھەپتە"
msgstr[1] "%(num)d ھەپتە"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d day"
msgid_plural "%(num)d days"
msgstr[0] "%(num)d كۈن"
msgstr[1] "%(num)d كۈن"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d hour"
msgid_plural "%(num)d hours"
msgstr[0] "%(num)d سائەت"
msgstr[1] "%(num)d سائەت"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d minute"
msgid_plural "%(num)d minutes"
msgstr[0] "%(num)d مىنۇت"
msgstr[1] "%(num)d مىنۇت"
