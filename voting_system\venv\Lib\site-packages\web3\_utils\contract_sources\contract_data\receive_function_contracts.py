"""
Generated by `compile_contracts.py` script.
Compiled with Solidity v0.8.19.
"""

# source: web3/_utils/contract_sources/ReceiveFunctionContracts.sol:ReceiveFunctionContract  # noqa: E501
RECEIVE_FUNCTION_CONTRACT_BYTECODE = "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"  # noqa: E501
RECEIVE_FUNCTION_CONTRACT_RUNTIME = "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"  # noqa: E501
RECEIVE_FUNCTION_CONTRACT_ABI = [
    {"stateMutability": "payable", "type": "fallback"},
    {
        "inputs": [],
        "name": "getText",
        "outputs": [{"internalType": "string", "name": "", "type": "string"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "string", "name": "new_text", "type": "string"}],
        "name": "setText",
        "outputs": [{"internalType": "string", "name": "", "type": "string"}],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {"stateMutability": "payable", "type": "receive"},
]
RECEIVE_FUNCTION_CONTRACT_DATA = {
    "bytecode": RECEIVE_FUNCTION_CONTRACT_BYTECODE,
    "bytecode_runtime": RECEIVE_FUNCTION_CONTRACT_RUNTIME,
    "abi": RECEIVE_FUNCTION_CONTRACT_ABI,
}


# source: web3/_utils/contract_sources/ReceiveFunctionContracts.sol:NoReceiveFunctionContract  # noqa: E501
NO_RECEIVE_FUNCTION_CONTRACT_BYTECODE = "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"  # noqa: E501
NO_RECEIVE_FUNCTION_CONTRACT_RUNTIME = "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"  # noqa: E501
NO_RECEIVE_FUNCTION_CONTRACT_ABI = [
    {"stateMutability": "nonpayable", "type": "fallback"},
    {
        "inputs": [],
        "name": "getText",
        "outputs": [{"internalType": "string", "name": "", "type": "string"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "string", "name": "new_text", "type": "string"}],
        "name": "setText",
        "outputs": [{"internalType": "string", "name": "", "type": "string"}],
        "stateMutability": "nonpayable",
        "type": "function",
    },
]
NO_RECEIVE_FUNCTION_CONTRACT_DATA = {
    "bytecode": NO_RECEIVE_FUNCTION_CONTRACT_BYTECODE,
    "bytecode_runtime": NO_RECEIVE_FUNCTION_CONTRACT_RUNTIME,
    "abi": NO_RECEIVE_FUNCTION_CONTRACT_ABI,
}
