{"version": 3, "file": "lit-html.js", "sources": ["../../src/lit-html.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n// IMPORTANT: these imports must be type-only\nimport type {Directive, DirectiveResult, PartInfo} from './directive.js';\n\nconst DEV_MODE = true;\nconst ENABLE_EXTRA_SECURITY_HOOKS = true;\nconst ENABLE_SHADYDOM_NOPATCH = true;\nconst NODE_MODE = false;\n// Use window for browser builds because IE11 doesn't have globalThis.\nconst global = NODE_MODE ? globalThis : window;\n\n/**\n * Contains types that are part of the unstable debug API.\n *\n * Everything in this API is not stable and may change or be removed in the future,\n * even on patch releases.\n */\n// eslint-disable-next-line @typescript-eslint/no-namespace\nexport namespace LitUnstable {\n  /**\n   * When Lit is running in dev mode and `window.emitLitDebugLogEvents` is true,\n   * we will emit 'lit-debug' events to window, with live details about the update and render\n   * lifecycle. These can be useful for writing debug tooling and visualizations.\n   *\n   * Please be aware that running with window.emitLitDebugLogEvents has performance overhead,\n   * making certain operations that are normally very cheap (like a no-op render) much slower,\n   * because we must copy data and dispatch events.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-namespace\n  export namespace DebugLog {\n    export type Entry =\n      | TemplatePrep\n      | TemplateInstantiated\n      | TemplateInstantiatedAndUpdated\n      | TemplateUpdating\n      | BeginRender\n      | EndRender\n      | CommitPartEntry\n      | SetPartValue;\n    export interface TemplatePrep {\n      kind: 'template prep';\n      template: Template;\n      strings: TemplateStringsArray;\n      clonableTemplate: HTMLTemplateElement;\n      parts: TemplatePart[];\n    }\n    export interface BeginRender {\n      kind: 'begin render';\n      id: number;\n      value: unknown;\n      container: HTMLElement | DocumentFragment;\n      options: RenderOptions | undefined;\n      part: ChildPart | undefined;\n    }\n    export interface EndRender {\n      kind: 'end render';\n      id: number;\n      value: unknown;\n      container: HTMLElement | DocumentFragment;\n      options: RenderOptions | undefined;\n      part: ChildPart;\n    }\n    export interface TemplateInstantiated {\n      kind: 'template instantiated';\n      template: Template | CompiledTemplate;\n      instance: TemplateInstance;\n      options: RenderOptions | undefined;\n      fragment: Node;\n      parts: Array<Part | undefined>;\n      values: unknown[];\n    }\n    export interface TemplateInstantiatedAndUpdated {\n      kind: 'template instantiated and updated';\n      template: Template | CompiledTemplate;\n      instance: TemplateInstance;\n      options: RenderOptions | undefined;\n      fragment: Node;\n      parts: Array<Part | undefined>;\n      values: unknown[];\n    }\n    export interface TemplateUpdating {\n      kind: 'template updating';\n      template: Template | CompiledTemplate;\n      instance: TemplateInstance;\n      options: RenderOptions | undefined;\n      parts: Array<Part | undefined>;\n      values: unknown[];\n    }\n    export interface SetPartValue {\n      kind: 'set part';\n      part: Part;\n      value: unknown;\n      valueIndex: number;\n      values: unknown[];\n      templateInstance: TemplateInstance;\n    }\n\n    export type CommitPartEntry =\n      | CommitNothingToChildEntry\n      | CommitText\n      | CommitNode\n      | CommitAttribute\n      | CommitProperty\n      | CommitBooleanAttribute\n      | CommitEventListener\n      | CommitToElementBinding;\n\n    export interface CommitNothingToChildEntry {\n      kind: 'commit nothing to child';\n      start: ChildNode;\n      end: ChildNode | null;\n      parent: Disconnectable | undefined;\n      options: RenderOptions | undefined;\n    }\n\n    export interface CommitText {\n      kind: 'commit text';\n      node: Text;\n      value: unknown;\n      options: RenderOptions | undefined;\n    }\n\n    export interface CommitNode {\n      kind: 'commit node';\n      start: Node;\n      parent: Disconnectable | undefined;\n      value: Node;\n      options: RenderOptions | undefined;\n    }\n\n    export interface CommitAttribute {\n      kind: 'commit attribute';\n      element: Element;\n      name: string;\n      value: unknown;\n      options: RenderOptions | undefined;\n    }\n\n    export interface CommitProperty {\n      kind: 'commit property';\n      element: Element;\n      name: string;\n      value: unknown;\n      options: RenderOptions | undefined;\n    }\n\n    export interface CommitBooleanAttribute {\n      kind: 'commit boolean attribute';\n      element: Element;\n      name: string;\n      value: boolean;\n      options: RenderOptions | undefined;\n    }\n\n    export interface CommitEventListener {\n      kind: 'commit event listener';\n      element: Element;\n      name: string;\n      value: unknown;\n      oldListener: unknown;\n      options: RenderOptions | undefined;\n      // True if we're removing the old event listener (e.g. because settings changed, or value is nothing)\n      removeListener: boolean;\n      // True if we're adding a new event listener (e.g. because first render, or settings changed)\n      addListener: boolean;\n    }\n\n    export interface CommitToElementBinding {\n      kind: 'commit to element binding';\n      element: Element;\n      value: unknown;\n      options: RenderOptions | undefined;\n    }\n  }\n}\n\ninterface DebugLoggingWindow {\n  // Even in dev mode, we generally don't want to emit these events, as that's\n  // another level of cost, so only emit them when DEV_MODE is true _and_ when\n  // window.emitLitDebugEvents is true.\n  emitLitDebugLogEvents?: boolean;\n}\n\n/**\n * Useful for visualizing and logging insights into what the Lit template system is doing.\n *\n * Compiled out of prod mode builds.\n */\nconst debugLogEvent = DEV_MODE\n  ? (event: LitUnstable.DebugLog.Entry) => {\n      const shouldEmit = (global as unknown as DebugLoggingWindow)\n        .emitLitDebugLogEvents;\n      if (!shouldEmit) {\n        return;\n      }\n      global.dispatchEvent(\n        new CustomEvent<LitUnstable.DebugLog.Entry>('lit-debug', {\n          detail: event,\n        })\n      );\n    }\n  : undefined;\n// Used for connecting beginRender and endRender events when there are nested\n// renders when errors are thrown preventing an endRender event from being\n// called.\nlet debugLogRenderId = 0;\n\nlet issueWarning: (code: string, warning: string) => void;\n\nif (DEV_MODE) {\n  global.litIssuedWarnings ??= new Set();\n\n  // Issue a warning, if we haven't already.\n  issueWarning = (code: string, warning: string) => {\n    warning += code\n      ? ` See https://lit.dev/msg/${code} for more information.`\n      : '';\n    if (!global.litIssuedWarnings!.has(warning)) {\n      console.warn(warning);\n      global.litIssuedWarnings!.add(warning);\n    }\n  };\n\n  issueWarning(\n    'dev-mode',\n    `Lit is in dev mode. Not recommended for production!`\n  );\n}\n\nconst wrap =\n  ENABLE_SHADYDOM_NOPATCH &&\n  global.ShadyDOM?.inUse &&\n  global.ShadyDOM?.noPatch === true\n    ? global.ShadyDOM!.wrap\n    : (node: Node) => node;\n\nconst trustedTypes = (global as unknown as Partial<Window>).trustedTypes;\n\n/**\n * Our TrustedTypePolicy for HTML which is declared using the html template\n * tag function.\n *\n * That HTML is a developer-authored constant, and is parsed with innerHTML\n * before any untrusted expressions have been mixed in. Therefor it is\n * considered safe by construction.\n */\nconst policy = trustedTypes\n  ? trustedTypes.createPolicy('lit-html', {\n      createHTML: (s) => s,\n    })\n  : undefined;\n\n/**\n * Used to sanitize any value before it is written into the DOM. This can be\n * used to implement a security policy of allowed and disallowed values in\n * order to prevent XSS attacks.\n *\n * One way of using this callback would be to check attributes and properties\n * against a list of high risk fields, and require that values written to such\n * fields be instances of a class which is safe by construction. Closure's Safe\n * HTML Types is one implementation of this technique (\n * https://github.com/google/safe-html-types/blob/master/doc/safehtml-types.md).\n * The TrustedTypes polyfill in API-only mode could also be used as a basis\n * for this technique (https://github.com/WICG/trusted-types).\n *\n * @param node The HTML node (usually either a #text node or an Element) that\n *     is being written to. Note that this is just an exemplar node, the write\n *     may take place against another instance of the same class of node.\n * @param name The name of an attribute or property (for example, 'href').\n * @param type Indicates whether the write that's about to be performed will\n *     be to a property or a node.\n * @return A function that will sanitize this class of writes.\n */\nexport type SanitizerFactory = (\n  node: Node,\n  name: string,\n  type: 'property' | 'attribute'\n) => ValueSanitizer;\n\n/**\n * A function which can sanitize values that will be written to a specific kind\n * of DOM sink.\n *\n * See SanitizerFactory.\n *\n * @param value The value to sanitize. Will be the actual value passed into\n *     the lit-html template literal, so this could be of any type.\n * @return The value to write to the DOM. Usually the same as the input value,\n *     unless sanitization is needed.\n */\nexport type ValueSanitizer = (value: unknown) => unknown;\n\nconst identityFunction: ValueSanitizer = (value: unknown) => value;\nconst noopSanitizer: SanitizerFactory = (\n  _node: Node,\n  _name: string,\n  _type: 'property' | 'attribute'\n) => identityFunction;\n\n/** Sets the global sanitizer factory. */\nconst setSanitizer = (newSanitizer: SanitizerFactory) => {\n  if (!ENABLE_EXTRA_SECURITY_HOOKS) {\n    return;\n  }\n  if (sanitizerFactoryInternal !== noopSanitizer) {\n    throw new Error(\n      `Attempted to overwrite existing lit-html security policy.` +\n        ` setSanitizeDOMValueFactory should be called at most once.`\n    );\n  }\n  sanitizerFactoryInternal = newSanitizer;\n};\n\n/**\n * Only used in internal tests, not a part of the public API.\n */\nconst _testOnlyClearSanitizerFactoryDoNotCallOrElse = () => {\n  sanitizerFactoryInternal = noopSanitizer;\n};\n\nconst createSanitizer: SanitizerFactory = (node, name, type) => {\n  return sanitizerFactoryInternal(node, name, type);\n};\n\n// Added to an attribute name to mark the attribute as bound so we can find\n// it easily.\nconst boundAttributeSuffix = '$lit$';\n\n// This marker is used in many syntactic positions in HTML, so it must be\n// a valid element name and attribute name. We don't support dynamic names (yet)\n// but this at least ensures that the parse tree is closer to the template\n// intention.\nconst marker = `lit$${String(Math.random()).slice(9)}$`;\n\n// String used to tell if a comment is a marker comment\nconst markerMatch = '?' + marker;\n\n// Text used to insert a comment marker node. We use processing instruction\n// syntax because it's slightly smaller, but parses as a comment node.\nconst nodeMarker = `<${markerMatch}>`;\n\nconst d =\n  NODE_MODE && global.document === undefined\n    ? ({\n        createTreeWalker() {\n          return {};\n        },\n      } as unknown as Document)\n    : document;\n\n// Creates a dynamic marker. We never have to search for these in the DOM.\nconst createMarker = () => d.createComment('');\n\n// https://tc39.github.io/ecma262/#sec-typeof-operator\ntype Primitive = null | undefined | boolean | number | string | symbol | bigint;\nconst isPrimitive = (value: unknown): value is Primitive =>\n  value === null || (typeof value != 'object' && typeof value != 'function');\nconst isArray = Array.isArray;\nconst isIterable = (value: unknown): value is Iterable<unknown> =>\n  isArray(value) ||\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  typeof (value as any)?.[Symbol.iterator] === 'function';\n\nconst SPACE_CHAR = `[ \\t\\n\\f\\r]`;\nconst ATTR_VALUE_CHAR = `[^ \\t\\n\\f\\r\"'\\`<>=]`;\nconst NAME_CHAR = `[^\\\\s\"'>=/]`;\n\n// These regexes represent the five parsing states that we care about in the\n// Template's HTML scanner. They match the *end* of the state they're named\n// after.\n// Depending on the match, we transition to a new state. If there's no match,\n// we stay in the same state.\n// Note that the regexes are stateful. We utilize lastIndex and sync it\n// across the multiple regexes used. In addition to the five regexes below\n// we also dynamically create a regex to find the matching end tags for raw\n// text elements.\n\n/**\n * End of text is: `<` followed by:\n *   (comment start) or (tag) or (dynamic tag binding)\n */\nconst textEndRegex = /<(?:(!--|\\/[^a-zA-Z])|(\\/?[a-zA-Z][^>\\s]*)|(\\/?$))/g;\nconst COMMENT_START = 1;\nconst TAG_NAME = 2;\nconst DYNAMIC_TAG_NAME = 3;\n\nconst commentEndRegex = /-->/g;\n/**\n * Comments not started with <!--, like </{, can be ended by a single `>`\n */\nconst comment2EndRegex = />/g;\n\n/**\n * The tagEnd regex matches the end of the \"inside an opening\" tag syntax\n * position. It either matches a `>`, an attribute-like sequence, or the end\n * of the string after a space (attribute-name position ending).\n *\n * See attributes in the HTML spec:\n * https://www.w3.org/TR/html5/syntax.html#elements-attributes\n *\n * \" \\t\\n\\f\\r\" are HTML space characters:\n * https://infra.spec.whatwg.org/#ascii-whitespace\n *\n * So an attribute is:\n *  * The name: any character except a whitespace character, (\"), ('), \">\",\n *    \"=\", or \"/\". Note: this is different from the HTML spec which also excludes control characters.\n *  * Followed by zero or more space characters\n *  * Followed by \"=\"\n *  * Followed by zero or more space characters\n *  * Followed by:\n *    * Any character except space, ('), (\"), \"<\", \">\", \"=\", (`), or\n *    * (\") then any non-(\"), or\n *    * (') then any non-(')\n */\nconst tagEndRegex = new RegExp(\n  `>|${SPACE_CHAR}(?:(${NAME_CHAR}+)(${SPACE_CHAR}*=${SPACE_CHAR}*(?:${ATTR_VALUE_CHAR}|(\"|')|))|$)`,\n  'g'\n);\nconst ENTIRE_MATCH = 0;\nconst ATTRIBUTE_NAME = 1;\nconst SPACES_AND_EQUALS = 2;\nconst QUOTE_CHAR = 3;\n\nconst singleQuoteAttrEndRegex = /'/g;\nconst doubleQuoteAttrEndRegex = /\"/g;\n/**\n * Matches the raw text elements.\n *\n * Comments are not parsed within raw text elements, so we need to search their\n * text content for marker strings.\n */\nconst rawTextElement = /^(?:script|style|textarea|title)$/i;\n\n/** TemplateResult types */\nconst HTML_RESULT = 1;\nconst SVG_RESULT = 2;\n\ntype ResultType = typeof HTML_RESULT | typeof SVG_RESULT;\n\n// TemplatePart types\n// IMPORTANT: these must match the values in PartType\nconst ATTRIBUTE_PART = 1;\nconst CHILD_PART = 2;\nconst PROPERTY_PART = 3;\nconst BOOLEAN_ATTRIBUTE_PART = 4;\nconst EVENT_PART = 5;\nconst ELEMENT_PART = 6;\nconst COMMENT_PART = 7;\n\n/**\n * The return type of the template tag functions, {@linkcode html} and\n * {@linkcode svg}.\n *\n * A `TemplateResult` object holds all the information about a template\n * expression required to render it: the template strings, expression values,\n * and type of template (html or svg).\n *\n * `TemplateResult` objects do not create any DOM on their own. To create or\n * update DOM you need to render the `TemplateResult`. See\n * [Rendering](https://lit.dev/docs/components/rendering) for more information.\n *\n */\nexport type TemplateResult<T extends ResultType = ResultType> = {\n  // This property needs to remain unminified.\n  ['_$litType$']: T;\n  strings: TemplateStringsArray;\n  values: unknown[];\n};\n\nexport type HTMLTemplateResult = TemplateResult<typeof HTML_RESULT>;\n\nexport type SVGTemplateResult = TemplateResult<typeof SVG_RESULT>;\n\nexport interface CompiledTemplateResult {\n  // This is a factory in order to make template initialization lazy\n  // and allow ShadyRenderOptions scope to be passed in.\n  // This property needs to remain unminified.\n  ['_$litType$']: CompiledTemplate;\n  values: unknown[];\n}\n\nexport interface CompiledTemplate extends Omit<Template, 'el'> {\n  // el is overridden to be optional. We initialize it on first render\n  el?: HTMLTemplateElement;\n\n  // The prepared HTML string to create a template element from.\n  // The type is a TemplateStringsArray to guarantee that the value came from\n  // source code, preventing a JSON injection attack.\n  h: TemplateStringsArray;\n}\n\n/**\n * Generates a template literal tag function that returns a TemplateResult with\n * the given result type.\n */\nconst tag =\n  <T extends ResultType>(type: T) =>\n  (strings: TemplateStringsArray, ...values: unknown[]): TemplateResult<T> => {\n    // Warn against templates octal escape sequences\n    // We do this here rather than in render so that the warning is closer to the\n    // template definition.\n    if (DEV_MODE && strings.some((s) => s === undefined)) {\n      console.warn(\n        'Some template strings are undefined.\\n' +\n          'This is probably caused by illegal octal escape sequences.'\n      );\n    }\n    return {\n      // This property needs to remain unminified.\n      ['_$litType$']: type,\n      strings,\n      values,\n    };\n  };\n\n/**\n * Interprets a template literal as an HTML template that can efficiently\n * render to and update a container.\n *\n * ```ts\n * const header = (title: string) => html`<h1>${title}</h1>`;\n * ```\n *\n * The `html` tag returns a description of the DOM to render as a value. It is\n * lazy, meaning no work is done until the template is rendered. When rendering,\n * if a template comes from the same expression as a previously rendered result,\n * it's efficiently updated instead of replaced.\n */\nexport const html = tag(HTML_RESULT);\n\n/**\n * Interprets a template literal as an SVG fragment that can efficiently\n * render to and update a container.\n *\n * ```ts\n * const rect = svg`<rect width=\"10\" height=\"10\"></rect>`;\n *\n * const myImage = html`\n *   <svg viewBox=\"0 0 10 10\" xmlns=\"http://www.w3.org/2000/svg\">\n *     ${rect}\n *   </svg>`;\n * ```\n *\n * The `svg` *tag function* should only be used for SVG fragments, or elements\n * that would be contained **inside** an `<svg>` HTML element. A common error is\n * placing an `<svg>` *element* in a template tagged with the `svg` tag\n * function. The `<svg>` element is an HTML element and should be used within a\n * template tagged with the {@linkcode html} tag function.\n *\n * In LitElement usage, it's invalid to return an SVG fragment from the\n * `render()` method, as the SVG fragment will be contained within the element's\n * shadow root and thus cannot be used within an `<svg>` HTML element.\n */\nexport const svg = tag(SVG_RESULT);\n\n/**\n * A sentinel value that signals that a value was handled by a directive and\n * should not be written to the DOM.\n */\nexport const noChange = Symbol.for('lit-noChange');\n\n/**\n * A sentinel value that signals a ChildPart to fully clear its content.\n *\n * ```ts\n * const button = html`${\n *  user.isAdmin\n *    ? html`<button>DELETE</button>`\n *    : nothing\n * }`;\n * ```\n *\n * Prefer using `nothing` over other falsy values as it provides a consistent\n * behavior between various expression binding contexts.\n *\n * In child expressions, `undefined`, `null`, `''`, and `nothing` all behave the\n * same and render no nodes. In attribute expressions, `nothing` _removes_ the\n * attribute, while `undefined` and `null` will render an empty string. In\n * property expressions `nothing` becomes `undefined`.\n */\nexport const nothing = Symbol.for('lit-nothing');\n\n/**\n * The cache of prepared templates, keyed by the tagged TemplateStringsArray\n * and _not_ accounting for the specific template tag used. This means that\n * template tags cannot be dynamic - the must statically be one of html, svg,\n * or attr. This restriction simplifies the cache lookup, which is on the hot\n * path for rendering.\n */\nconst templateCache = new WeakMap<TemplateStringsArray, Template>();\n\n/**\n * Object specifying options for controlling lit-html rendering. Note that\n * while `render` may be called multiple times on the same `container` (and\n * `renderBefore` reference node) to efficiently update the rendered content,\n * only the options passed in during the first render are respected during\n * the lifetime of renders to that unique `container` + `renderBefore`\n * combination.\n */\nexport interface RenderOptions {\n  /**\n   * An object to use as the `this` value for event listeners. It's often\n   * useful to set this to the host component rendering a template.\n   */\n  host?: object;\n  /**\n   * A DOM node before which to render content in the container.\n   */\n  renderBefore?: ChildNode | null;\n  /**\n   * Node used for cloning the template (`importNode` will be called on this\n   * node). This controls the `ownerDocument` of the rendered DOM, along with\n   * any inherited context. Defaults to the global `document`.\n   */\n  creationScope?: {importNode(node: Node, deep?: boolean): Node};\n  /**\n   * The initial connected state for the top-level part being rendered. If no\n   * `isConnected` option is set, `AsyncDirective`s will be connected by\n   * default. Set to `false` if the initial render occurs in a disconnected tree\n   * and `AsyncDirective`s should see `isConnected === false` for their initial\n   * render. The `part.setConnected()` method must be used subsequent to initial\n   * render to change the connected state of the part.\n   */\n  isConnected?: boolean;\n}\n\nconst walker = d.createTreeWalker(\n  d,\n  129 /* NodeFilter.SHOW_{ELEMENT|COMMENT} */,\n  null,\n  false\n);\n\nlet sanitizerFactoryInternal: SanitizerFactory = noopSanitizer;\n\n//\n// Classes only below here, const variable declarations only above here...\n//\n// Keeping variable declarations and classes together improves minification.\n// Interfaces and type aliases can be interleaved freely.\n//\n\n// Type for classes that have a `_directive` or `_directives[]` field, used by\n// `resolveDirective`\nexport interface DirectiveParent {\n  _$parent?: DirectiveParent;\n  _$isConnected: boolean;\n  __directive?: Directive;\n  __directives?: Array<Directive | undefined>;\n}\n\nfunction trustFromTemplateString(\n  tsa: TemplateStringsArray,\n  stringFromTSA: string\n): TrustedHTML {\n  // A security check to prevent spoofing of Lit template results.\n  // In the future, we may be able to replace this with Array.isTemplateObject,\n  // though we might need to make that check inside of the html and svg\n  // functions, because precompiled templates don't come in as\n  // TemplateStringArray objects.\n  if (!Array.isArray(tsa) || !tsa.hasOwnProperty('raw')) {\n    let message = 'invalid template strings array';\n    if (DEV_MODE) {\n      message = `\n          Internal Error: expected template strings to be an array\n          with a 'raw' field. Faking a template strings array by\n          calling html or svg like an ordinary function is effectively\n          the same as calling unsafeHtml and can lead to major security\n          issues, e.g. opening your code up to XSS attacks.\n          If you're using the html or svg tagged template functions normally\n          and still seeing this error, please file a bug at\n          https://github.com/lit/lit/issues/new?template=bug_report.md\n          and include information about your build tooling, if any.\n        `\n        .trim()\n        .replace(/\\n */g, '\\n');\n    }\n    throw new Error(message);\n  }\n  return policy !== undefined\n    ? policy.createHTML(stringFromTSA)\n    : (stringFromTSA as unknown as TrustedHTML);\n}\n\n/**\n * Returns an HTML string for the given TemplateStringsArray and result type\n * (HTML or SVG), along with the case-sensitive bound attribute names in\n * template order. The HTML contains comment markers denoting the `ChildPart`s\n * and suffixes on bound attributes denoting the `AttributeParts`.\n *\n * @param strings template strings array\n * @param type HTML or SVG\n * @return Array containing `[html, attrNames]` (array returned for terseness,\n *     to avoid object fields since this code is shared with non-minified SSR\n *     code)\n */\nconst getTemplateHtml = (\n  strings: TemplateStringsArray,\n  type: ResultType\n): [TrustedHTML, Array<string | undefined>] => {\n  // Insert makers into the template HTML to represent the position of\n  // bindings. The following code scans the template strings to determine the\n  // syntactic position of the bindings. They can be in text position, where\n  // we insert an HTML comment, attribute value position, where we insert a\n  // sentinel string and re-write the attribute name, or inside a tag where\n  // we insert the sentinel string.\n  const l = strings.length - 1;\n  // Stores the case-sensitive bound attribute names in the order of their\n  // parts. ElementParts are also reflected in this array as undefined\n  // rather than a string, to disambiguate from attribute bindings.\n  const attrNames: Array<string | undefined> = [];\n  let html = type === SVG_RESULT ? '<svg>' : '';\n\n  // When we're inside a raw text tag (not it's text content), the regex\n  // will still be tagRegex so we can find attributes, but will switch to\n  // this regex when the tag ends.\n  let rawTextEndRegex: RegExp | undefined;\n\n  // The current parsing state, represented as a reference to one of the\n  // regexes\n  let regex = textEndRegex;\n\n  for (let i = 0; i < l; i++) {\n    const s = strings[i];\n    // The index of the end of the last attribute name. When this is\n    // positive at end of a string, it means we're in an attribute value\n    // position and need to rewrite the attribute name.\n    // We also use a special value of -2 to indicate that we encountered\n    // the end of a string in attribute name position.\n    let attrNameEndIndex = -1;\n    let attrName: string | undefined;\n    let lastIndex = 0;\n    let match!: RegExpExecArray | null;\n\n    // The conditions in this loop handle the current parse state, and the\n    // assignments to the `regex` variable are the state transitions.\n    while (lastIndex < s.length) {\n      // Make sure we start searching from where we previously left off\n      regex.lastIndex = lastIndex;\n      match = regex.exec(s);\n      if (match === null) {\n        break;\n      }\n      lastIndex = regex.lastIndex;\n      if (regex === textEndRegex) {\n        if (match[COMMENT_START] === '!--') {\n          regex = commentEndRegex;\n        } else if (match[COMMENT_START] !== undefined) {\n          // We started a weird comment, like </{\n          regex = comment2EndRegex;\n        } else if (match[TAG_NAME] !== undefined) {\n          if (rawTextElement.test(match[TAG_NAME])) {\n            // Record if we encounter a raw-text element. We'll switch to\n            // this regex at the end of the tag.\n            rawTextEndRegex = new RegExp(`</${match[TAG_NAME]}`, 'g');\n          }\n          regex = tagEndRegex;\n        } else if (match[DYNAMIC_TAG_NAME] !== undefined) {\n          if (DEV_MODE) {\n            throw new Error(\n              'Bindings in tag names are not supported. Please use static templates instead. ' +\n                'See https://lit.dev/docs/templates/expressions/#static-expressions'\n            );\n          }\n          regex = tagEndRegex;\n        }\n      } else if (regex === tagEndRegex) {\n        if (match[ENTIRE_MATCH] === '>') {\n          // End of a tag. If we had started a raw-text element, use that\n          // regex\n          regex = rawTextEndRegex ?? textEndRegex;\n          // We may be ending an unquoted attribute value, so make sure we\n          // clear any pending attrNameEndIndex\n          attrNameEndIndex = -1;\n        } else if (match[ATTRIBUTE_NAME] === undefined) {\n          // Attribute name position\n          attrNameEndIndex = -2;\n        } else {\n          attrNameEndIndex = regex.lastIndex - match[SPACES_AND_EQUALS].length;\n          attrName = match[ATTRIBUTE_NAME];\n          regex =\n            match[QUOTE_CHAR] === undefined\n              ? tagEndRegex\n              : match[QUOTE_CHAR] === '\"'\n              ? doubleQuoteAttrEndRegex\n              : singleQuoteAttrEndRegex;\n        }\n      } else if (\n        regex === doubleQuoteAttrEndRegex ||\n        regex === singleQuoteAttrEndRegex\n      ) {\n        regex = tagEndRegex;\n      } else if (regex === commentEndRegex || regex === comment2EndRegex) {\n        regex = textEndRegex;\n      } else {\n        // Not one of the five state regexes, so it must be the dynamically\n        // created raw text regex and we're at the close of that element.\n        regex = tagEndRegex;\n        rawTextEndRegex = undefined;\n      }\n    }\n\n    if (DEV_MODE) {\n      // If we have a attrNameEndIndex, which indicates that we should\n      // rewrite the attribute name, assert that we're in a valid attribute\n      // position - either in a tag, or a quoted attribute value.\n      console.assert(\n        attrNameEndIndex === -1 ||\n          regex === tagEndRegex ||\n          regex === singleQuoteAttrEndRegex ||\n          regex === doubleQuoteAttrEndRegex,\n        'unexpected parse state B'\n      );\n    }\n\n    // We have four cases:\n    //  1. We're in text position, and not in a raw text element\n    //     (regex === textEndRegex): insert a comment marker.\n    //  2. We have a non-negative attrNameEndIndex which means we need to\n    //     rewrite the attribute name to add a bound attribute suffix.\n    //  3. We're at the non-first binding in a multi-binding attribute, use a\n    //     plain marker.\n    //  4. We're somewhere else inside the tag. If we're in attribute name\n    //     position (attrNameEndIndex === -2), add a sequential suffix to\n    //     generate a unique attribute name.\n\n    // Detect a binding next to self-closing tag end and insert a space to\n    // separate the marker from the tag end:\n    const end =\n      regex === tagEndRegex && strings[i + 1].startsWith('/>') ? ' ' : '';\n    html +=\n      regex === textEndRegex\n        ? s + nodeMarker\n        : attrNameEndIndex >= 0\n        ? (attrNames.push(attrName!),\n          s.slice(0, attrNameEndIndex) +\n            boundAttributeSuffix +\n            s.slice(attrNameEndIndex)) +\n          marker +\n          end\n        : s +\n          marker +\n          (attrNameEndIndex === -2 ? (attrNames.push(undefined), i) : end);\n  }\n\n  const htmlResult: string | TrustedHTML =\n    html + (strings[l] || '<?>') + (type === SVG_RESULT ? '</svg>' : '');\n\n  // Returned as an array for terseness\n  return [trustFromTemplateString(strings, htmlResult), attrNames];\n};\n\n/** @internal */\nexport type {Template};\nclass Template {\n  /** @internal */\n  el!: HTMLTemplateElement;\n\n  parts: Array<TemplatePart> = [];\n\n  constructor(\n    // This property needs to remain unminified.\n    {strings, ['_$litType$']: type}: TemplateResult,\n    options?: RenderOptions\n  ) {\n    let node: Node | null;\n    let nodeIndex = 0;\n    let attrNameIndex = 0;\n    const partCount = strings.length - 1;\n    const parts = this.parts;\n\n    // Create template element\n    const [html, attrNames] = getTemplateHtml(strings, type);\n    this.el = Template.createElement(html, options);\n    walker.currentNode = this.el.content;\n\n    // Reparent SVG nodes into template root\n    if (type === SVG_RESULT) {\n      const content = this.el.content;\n      const svgElement = content.firstChild!;\n      svgElement.remove();\n      content.append(...svgElement.childNodes);\n    }\n\n    // Walk the template to find binding markers and create TemplateParts\n    while ((node = walker.nextNode()) !== null && parts.length < partCount) {\n      if (node.nodeType === 1) {\n        if (DEV_MODE) {\n          const tag = (node as Element).localName;\n          // Warn if `textarea` includes an expression and throw if `template`\n          // does since these are not supported. We do this by checking\n          // innerHTML for anything that looks like a marker. This catches\n          // cases like bindings in textarea there markers turn into text nodes.\n          if (\n            /^(?:textarea|template)$/i!.test(tag) &&\n            (node as Element).innerHTML.includes(marker)\n          ) {\n            const m =\n              `Expressions are not supported inside \\`${tag}\\` ` +\n              `elements. See https://lit.dev/msg/expression-in-${tag} for more ` +\n              `information.`;\n            if (tag === 'template') {\n              throw new Error(m);\n            } else issueWarning('', m);\n          }\n        }\n        // TODO (justinfagnani): for attempted dynamic tag names, we don't\n        // increment the bindingIndex, and it'll be off by 1 in the element\n        // and off by two after it.\n        if ((node as Element).hasAttributes()) {\n          // We defer removing bound attributes because on IE we might not be\n          // iterating attributes in their template order, and would sometimes\n          // remove an attribute that we still need to create a part for.\n          const attrsToRemove = [];\n          for (const name of (node as Element).getAttributeNames()) {\n            // `name` is the name of the attribute we're iterating over, but not\n            // _necessarily_ the name of the attribute we will create a part\n            // for. They can be different in browsers that don't iterate on\n            // attributes in source order. In that case the attrNames array\n            // contains the attribute name we'll process next. We only need the\n            // attribute name here to know if we should process a bound attribute\n            // on this element.\n            if (\n              name.endsWith(boundAttributeSuffix) ||\n              name.startsWith(marker)\n            ) {\n              const realName = attrNames[attrNameIndex++];\n              attrsToRemove.push(name);\n              if (realName !== undefined) {\n                // Lowercase for case-sensitive SVG attributes like viewBox\n                const value = (node as Element).getAttribute(\n                  realName.toLowerCase() + boundAttributeSuffix\n                )!;\n                const statics = value.split(marker);\n                const m = /([.?@])?(.*)/.exec(realName)!;\n                parts.push({\n                  type: ATTRIBUTE_PART,\n                  index: nodeIndex,\n                  name: m[2],\n                  strings: statics,\n                  ctor:\n                    m[1] === '.'\n                      ? PropertyPart\n                      : m[1] === '?'\n                      ? BooleanAttributePart\n                      : m[1] === '@'\n                      ? EventPart\n                      : AttributePart,\n                });\n              } else {\n                parts.push({\n                  type: ELEMENT_PART,\n                  index: nodeIndex,\n                });\n              }\n            }\n          }\n          for (const name of attrsToRemove) {\n            (node as Element).removeAttribute(name);\n          }\n        }\n        // TODO (justinfagnani): benchmark the regex against testing for each\n        // of the 3 raw text element names.\n        if (rawTextElement.test((node as Element).tagName)) {\n          // For raw text elements we need to split the text content on\n          // markers, create a Text node for each segment, and create\n          // a TemplatePart for each marker.\n          const strings = (node as Element).textContent!.split(marker);\n          const lastIndex = strings.length - 1;\n          if (lastIndex > 0) {\n            (node as Element).textContent = trustedTypes\n              ? (trustedTypes.emptyScript as unknown as '')\n              : '';\n            // Generate a new text node for each literal section\n            // These nodes are also used as the markers for node parts\n            // We can't use empty text nodes as markers because they're\n            // normalized when cloning in IE (could simplify when\n            // IE is no longer supported)\n            for (let i = 0; i < lastIndex; i++) {\n              (node as Element).append(strings[i], createMarker());\n              // Walk past the marker node we just added\n              walker.nextNode();\n              parts.push({type: CHILD_PART, index: ++nodeIndex});\n            }\n            // Note because this marker is added after the walker's current\n            // node, it will be walked to in the outer loop (and ignored), so\n            // we don't need to adjust nodeIndex here\n            (node as Element).append(strings[lastIndex], createMarker());\n          }\n        }\n      } else if (node.nodeType === 8) {\n        const data = (node as Comment).data;\n        if (data === markerMatch) {\n          parts.push({type: CHILD_PART, index: nodeIndex});\n        } else {\n          let i = -1;\n          while ((i = (node as Comment).data.indexOf(marker, i + 1)) !== -1) {\n            // Comment node has a binding marker inside, make an inactive part\n            // The binding won't work, but subsequent bindings will\n            parts.push({type: COMMENT_PART, index: nodeIndex});\n            // Move to the end of the match\n            i += marker.length - 1;\n          }\n        }\n      }\n      nodeIndex++;\n    }\n    // We could set walker.currentNode to another node here to prevent a memory\n    // leak, but every time we prepare a template, we immediately render it\n    // and re-use the walker in new TemplateInstance._clone().\n    debugLogEvent?.({\n      kind: 'template prep',\n      template: this,\n      clonableTemplate: this.el,\n      parts: this.parts,\n      strings,\n    });\n  }\n\n  // Overridden via `litHtmlPolyfillSupport` to provide platform support.\n  /** @nocollapse */\n  static createElement(html: TrustedHTML, _options?: RenderOptions) {\n    const el = d.createElement('template');\n    el.innerHTML = html as unknown as string;\n    return el;\n  }\n}\n\nexport interface Disconnectable {\n  _$parent?: Disconnectable;\n  _$disconnectableChildren?: Set<Disconnectable>;\n  // Rather than hold connection state on instances, Disconnectables recursively\n  // fetch the connection state from the RootPart they are connected in via\n  // getters up the Disconnectable tree via _$parent references. This pushes the\n  // cost of tracking the isConnected state to `AsyncDirectives`, and avoids\n  // needing to pass all Disconnectables (parts, template instances, and\n  // directives) their connection state each time it changes, which would be\n  // costly for trees that have no AsyncDirectives.\n  _$isConnected: boolean;\n}\n\nfunction resolveDirective(\n  part: ChildPart | AttributePart | ElementPart,\n  value: unknown,\n  parent: DirectiveParent = part,\n  attributeIndex?: number\n): unknown {\n  // Bail early if the value is explicitly noChange. Note, this means any\n  // nested directive is still attached and is not run.\n  if (value === noChange) {\n    return value;\n  }\n  let currentDirective =\n    attributeIndex !== undefined\n      ? (parent as AttributePart).__directives?.[attributeIndex]\n      : (parent as ChildPart | ElementPart | Directive).__directive;\n  const nextDirectiveConstructor = isPrimitive(value)\n    ? undefined\n    : // This property needs to remain unminified.\n      (value as DirectiveResult)['_$litDirective$'];\n  if (currentDirective?.constructor !== nextDirectiveConstructor) {\n    // This property needs to remain unminified.\n    currentDirective?.['_$notifyDirectiveConnectionChanged']?.(false);\n    if (nextDirectiveConstructor === undefined) {\n      currentDirective = undefined;\n    } else {\n      currentDirective = new nextDirectiveConstructor(part as PartInfo);\n      currentDirective._$initialize(part, parent, attributeIndex);\n    }\n    if (attributeIndex !== undefined) {\n      ((parent as AttributePart).__directives ??= [])[attributeIndex] =\n        currentDirective;\n    } else {\n      (parent as ChildPart | Directive).__directive = currentDirective;\n    }\n  }\n  if (currentDirective !== undefined) {\n    value = resolveDirective(\n      part,\n      currentDirective._$resolve(part, (value as DirectiveResult).values),\n      currentDirective,\n      attributeIndex\n    );\n  }\n  return value;\n}\n\nexport type {TemplateInstance};\n/**\n * An updateable instance of a Template. Holds references to the Parts used to\n * update the template instance.\n */\nclass TemplateInstance implements Disconnectable {\n  _$template: Template;\n  _$parts: Array<Part | undefined> = [];\n\n  /** @internal */\n  _$parent: ChildPart;\n  /** @internal */\n  _$disconnectableChildren?: Set<Disconnectable> = undefined;\n\n  constructor(template: Template, parent: ChildPart) {\n    this._$template = template;\n    this._$parent = parent;\n  }\n\n  // Called by ChildPart parentNode getter\n  get parentNode() {\n    return this._$parent.parentNode;\n  }\n\n  // See comment in Disconnectable interface for why this is a getter\n  get _$isConnected() {\n    return this._$parent._$isConnected;\n  }\n\n  // This method is separate from the constructor because we need to return a\n  // DocumentFragment and we don't want to hold onto it with an instance field.\n  _clone(options: RenderOptions | undefined) {\n    const {\n      el: {content},\n      parts: parts,\n    } = this._$template;\n    const fragment = (options?.creationScope ?? d).importNode(content, true);\n    walker.currentNode = fragment;\n\n    let node = walker.nextNode()!;\n    let nodeIndex = 0;\n    let partIndex = 0;\n    let templatePart = parts[0];\n\n    while (templatePart !== undefined) {\n      if (nodeIndex === templatePart.index) {\n        let part: Part | undefined;\n        if (templatePart.type === CHILD_PART) {\n          part = new ChildPart(\n            node as HTMLElement,\n            node.nextSibling,\n            this,\n            options\n          );\n        } else if (templatePart.type === ATTRIBUTE_PART) {\n          part = new templatePart.ctor(\n            node as HTMLElement,\n            templatePart.name,\n            templatePart.strings,\n            this,\n            options\n          );\n        } else if (templatePart.type === ELEMENT_PART) {\n          part = new ElementPart(node as HTMLElement, this, options);\n        }\n        this._$parts.push(part);\n        templatePart = parts[++partIndex];\n      }\n      if (nodeIndex !== templatePart?.index) {\n        node = walker.nextNode()!;\n        nodeIndex++;\n      }\n    }\n    // We need to set the currentNode away from the cloned tree so that we\n    // don't hold onto the tree even if the tree is detached and should be\n    // freed.\n    walker.currentNode = d;\n    return fragment;\n  }\n\n  _update(values: Array<unknown>) {\n    let i = 0;\n    for (const part of this._$parts) {\n      if (part !== undefined) {\n        debugLogEvent?.({\n          kind: 'set part',\n          part,\n          value: values[i],\n          valueIndex: i,\n          values,\n          templateInstance: this,\n        });\n        if ((part as AttributePart).strings !== undefined) {\n          (part as AttributePart)._$setValue(values, part as AttributePart, i);\n          // The number of values the part consumes is part.strings.length - 1\n          // since values are in between template spans. We increment i by 1\n          // later in the loop, so increment it by part.strings.length - 2 here\n          i += (part as AttributePart).strings!.length - 2;\n        } else {\n          part._$setValue(values[i]);\n        }\n      }\n      i++;\n    }\n  }\n}\n\n/*\n * Parts\n */\ntype AttributeTemplatePart = {\n  readonly type: typeof ATTRIBUTE_PART;\n  readonly index: number;\n  readonly name: string;\n  readonly ctor: typeof AttributePart;\n  readonly strings: ReadonlyArray<string>;\n};\ntype ChildTemplatePart = {\n  readonly type: typeof CHILD_PART;\n  readonly index: number;\n};\ntype ElementTemplatePart = {\n  readonly type: typeof ELEMENT_PART;\n  readonly index: number;\n};\ntype CommentTemplatePart = {\n  readonly type: typeof COMMENT_PART;\n  readonly index: number;\n};\n\n/**\n * A TemplatePart represents a dynamic part in a template, before the template\n * is instantiated. When a template is instantiated Parts are created from\n * TemplateParts.\n */\ntype TemplatePart =\n  | ChildTemplatePart\n  | AttributeTemplatePart\n  | ElementTemplatePart\n  | CommentTemplatePart;\n\nexport type Part =\n  | ChildPart\n  | AttributePart\n  | PropertyPart\n  | BooleanAttributePart\n  | ElementPart\n  | EventPart;\n\nexport type {ChildPart};\nclass ChildPart implements Disconnectable {\n  readonly type = CHILD_PART;\n  readonly options: RenderOptions | undefined;\n  _$committedValue: unknown = nothing;\n  /** @internal */\n  __directive?: Directive;\n  /** @internal */\n  _$startNode: ChildNode;\n  /** @internal */\n  _$endNode: ChildNode | null;\n  private _textSanitizer: ValueSanitizer | undefined;\n  /** @internal */\n  _$parent: Disconnectable | undefined;\n  /**\n   * Connection state for RootParts only (i.e. ChildPart without _$parent\n   * returned from top-level `render`). This field is unsed otherwise. The\n   * intention would clearer if we made `RootPart` a subclass of `ChildPart`\n   * with this field (and a different _$isConnected getter), but the subclass\n   * caused a perf regression, possibly due to making call sites polymorphic.\n   * @internal\n   */\n  __isConnected: boolean;\n\n  // See comment in Disconnectable interface for why this is a getter\n  get _$isConnected() {\n    // ChildParts that are not at the root should always be created with a\n    // parent; only RootChildNode's won't, so they return the local isConnected\n    // state\n    return this._$parent?._$isConnected ?? this.__isConnected;\n  }\n\n  // The following fields will be patched onto ChildParts when required by\n  // AsyncDirective\n  /** @internal */\n  _$disconnectableChildren?: Set<Disconnectable> = undefined;\n  /** @internal */\n  _$notifyConnectionChanged?(\n    isConnected: boolean,\n    removeFromParent?: boolean,\n    from?: number\n  ): void;\n  /** @internal */\n  _$reparentDisconnectables?(parent: Disconnectable): void;\n\n  constructor(\n    startNode: ChildNode,\n    endNode: ChildNode | null,\n    parent: TemplateInstance | ChildPart | undefined,\n    options: RenderOptions | undefined\n  ) {\n    this._$startNode = startNode;\n    this._$endNode = endNode;\n    this._$parent = parent;\n    this.options = options;\n    // Note __isConnected is only ever accessed on RootParts (i.e. when there is\n    // no _$parent); the value on a non-root-part is \"don't care\", but checking\n    // for parent would be more code\n    this.__isConnected = options?.isConnected ?? true;\n    if (ENABLE_EXTRA_SECURITY_HOOKS) {\n      // Explicitly initialize for consistent class shape.\n      this._textSanitizer = undefined;\n    }\n  }\n\n  /**\n   * The parent node into which the part renders its content.\n   *\n   * A ChildPart's content consists of a range of adjacent child nodes of\n   * `.parentNode`, possibly bordered by 'marker nodes' (`.startNode` and\n   * `.endNode`).\n   *\n   * - If both `.startNode` and `.endNode` are non-null, then the part's content\n   * consists of all siblings between `.startNode` and `.endNode`, exclusively.\n   *\n   * - If `.startNode` is non-null but `.endNode` is null, then the part's\n   * content consists of all siblings following `.startNode`, up to and\n   * including the last child of `.parentNode`. If `.endNode` is non-null, then\n   * `.startNode` will always be non-null.\n   *\n   * - If both `.endNode` and `.startNode` are null, then the part's content\n   * consists of all child nodes of `.parentNode`.\n   */\n  get parentNode(): Node {\n    let parentNode: Node = wrap(this._$startNode).parentNode!;\n    const parent = this._$parent;\n    if (\n      parent !== undefined &&\n      parentNode?.nodeType === 11 /* Node.DOCUMENT_FRAGMENT */\n    ) {\n      // If the parentNode is a DocumentFragment, it may be because the DOM is\n      // still in the cloned fragment during initial render; if so, get the real\n      // parentNode the part will be committed into by asking the parent.\n      parentNode = (parent as ChildPart | TemplateInstance).parentNode;\n    }\n    return parentNode;\n  }\n\n  /**\n   * The part's leading marker node, if any. See `.parentNode` for more\n   * information.\n   */\n  get startNode(): Node | null {\n    return this._$startNode;\n  }\n\n  /**\n   * The part's trailing marker node, if any. See `.parentNode` for more\n   * information.\n   */\n  get endNode(): Node | null {\n    return this._$endNode;\n  }\n\n  _$setValue(value: unknown, directiveParent: DirectiveParent = this): void {\n    if (DEV_MODE && this.parentNode === null) {\n      throw new Error(\n        `This \\`ChildPart\\` has no \\`parentNode\\` and therefore cannot accept a value. This likely means the element containing the part was manipulated in an unsupported way outside of Lit's control such that the part's marker nodes were ejected from DOM. For example, setting the element's \\`innerHTML\\` or \\`textContent\\` can do this.`\n      );\n    }\n    value = resolveDirective(this, value, directiveParent);\n    if (isPrimitive(value)) {\n      // Non-rendering child values. It's important that these do not render\n      // empty text nodes to avoid issues with preventing default <slot>\n      // fallback content.\n      if (value === nothing || value == null || value === '') {\n        if (this._$committedValue !== nothing) {\n          debugLogEvent?.({\n            kind: 'commit nothing to child',\n            start: this._$startNode,\n            end: this._$endNode,\n            parent: this._$parent,\n            options: this.options,\n          });\n          this._$clear();\n        }\n        this._$committedValue = nothing;\n      } else if (value !== this._$committedValue && value !== noChange) {\n        this._commitText(value);\n      }\n      // This property needs to remain unminified.\n    } else if ((value as TemplateResult)['_$litType$'] !== undefined) {\n      this._commitTemplateResult(value as TemplateResult);\n    } else if ((value as Node).nodeType !== undefined) {\n      if (DEV_MODE && this.options?.host === value) {\n        this._commitText(\n          `[probable mistake: rendered a template's host in itself ` +\n            `(commonly caused by writing \\${this} in a template]`\n        );\n        console.warn(\n          `Attempted to render the template host`,\n          value,\n          `inside itself. This is almost always a mistake, and in dev mode `,\n          `we render some warning text. In production however, we'll `,\n          `render it, which will usually result in an error, and sometimes `,\n          `in the element disappearing from the DOM.`\n        );\n        return;\n      }\n      this._commitNode(value as Node);\n    } else if (isIterable(value)) {\n      this._commitIterable(value);\n    } else {\n      // Fallback, will render the string representation\n      this._commitText(value);\n    }\n  }\n\n  private _insert<T extends Node>(node: T) {\n    return wrap(wrap(this._$startNode).parentNode!).insertBefore(\n      node,\n      this._$endNode\n    );\n  }\n\n  private _commitNode(value: Node): void {\n    if (this._$committedValue !== value) {\n      this._$clear();\n      if (\n        ENABLE_EXTRA_SECURITY_HOOKS &&\n        sanitizerFactoryInternal !== noopSanitizer\n      ) {\n        const parentNodeName = this._$startNode.parentNode?.nodeName;\n        if (parentNodeName === 'STYLE' || parentNodeName === 'SCRIPT') {\n          let message = 'Forbidden';\n          if (DEV_MODE) {\n            if (parentNodeName === 'STYLE') {\n              message =\n                `Lit does not support binding inside style nodes. ` +\n                `This is a security risk, as style injection attacks can ` +\n                `exfiltrate data and spoof UIs. ` +\n                `Consider instead using css\\`...\\` literals ` +\n                `to compose styles, and make do dynamic styling with ` +\n                `css custom properties, ::parts, <slot>s, ` +\n                `and by mutating the DOM rather than stylesheets.`;\n            } else {\n              message =\n                `Lit does not support binding inside script nodes. ` +\n                `This is a security risk, as it could allow arbitrary ` +\n                `code execution.`;\n            }\n          }\n          throw new Error(message);\n        }\n      }\n      debugLogEvent?.({\n        kind: 'commit node',\n        start: this._$startNode,\n        parent: this._$parent,\n        value: value,\n        options: this.options,\n      });\n      this._$committedValue = this._insert(value);\n    }\n  }\n\n  private _commitText(value: unknown): void {\n    // If the committed value is a primitive it means we called _commitText on\n    // the previous render, and we know that this._$startNode.nextSibling is a\n    // Text node. We can now just replace the text content (.data) of the node.\n    if (\n      this._$committedValue !== nothing &&\n      isPrimitive(this._$committedValue)\n    ) {\n      const node = wrap(this._$startNode).nextSibling as Text;\n      if (ENABLE_EXTRA_SECURITY_HOOKS) {\n        if (this._textSanitizer === undefined) {\n          this._textSanitizer = createSanitizer(node, 'data', 'property');\n        }\n        value = this._textSanitizer(value);\n      }\n      debugLogEvent?.({\n        kind: 'commit text',\n        node,\n        value,\n        options: this.options,\n      });\n      (node as Text).data = value as string;\n    } else {\n      if (ENABLE_EXTRA_SECURITY_HOOKS) {\n        const textNode = d.createTextNode('');\n        this._commitNode(textNode);\n        // When setting text content, for security purposes it matters a lot\n        // what the parent is. For example, <style> and <script> need to be\n        // handled with care, while <span> does not. So first we need to put a\n        // text node into the document, then we can sanitize its content.\n        if (this._textSanitizer === undefined) {\n          this._textSanitizer = createSanitizer(textNode, 'data', 'property');\n        }\n        value = this._textSanitizer(value);\n        debugLogEvent?.({\n          kind: 'commit text',\n          node: textNode,\n          value,\n          options: this.options,\n        });\n        textNode.data = value as string;\n      } else {\n        this._commitNode(d.createTextNode(value as string));\n        debugLogEvent?.({\n          kind: 'commit text',\n          node: wrap(this._$startNode).nextSibling as Text,\n          value,\n          options: this.options,\n        });\n      }\n    }\n    this._$committedValue = value;\n  }\n\n  private _commitTemplateResult(\n    result: TemplateResult | CompiledTemplateResult\n  ): void {\n    // This property needs to remain unminified.\n    const {values, ['_$litType$']: type} = result;\n    // If $litType$ is a number, result is a plain TemplateResult and we get\n    // the template from the template cache. If not, result is a\n    // CompiledTemplateResult and _$litType$ is a CompiledTemplate and we need\n    // to create the <template> element the first time we see it.\n    const template: Template | CompiledTemplate =\n      typeof type === 'number'\n        ? this._$getTemplate(result as TemplateResult)\n        : (type.el === undefined &&\n            (type.el = Template.createElement(\n              trustFromTemplateString(type.h, type.h[0]),\n              this.options\n            )),\n          type);\n\n    if ((this._$committedValue as TemplateInstance)?._$template === template) {\n      debugLogEvent?.({\n        kind: 'template updating',\n        template,\n        instance: this._$committedValue as TemplateInstance,\n        parts: (this._$committedValue as TemplateInstance)._$parts,\n        options: this.options,\n        values,\n      });\n      (this._$committedValue as TemplateInstance)._update(values);\n    } else {\n      const instance = new TemplateInstance(template as Template, this);\n      const fragment = instance._clone(this.options);\n      debugLogEvent?.({\n        kind: 'template instantiated',\n        template,\n        instance,\n        parts: instance._$parts,\n        options: this.options,\n        fragment,\n        values,\n      });\n      instance._update(values);\n      debugLogEvent?.({\n        kind: 'template instantiated and updated',\n        template,\n        instance,\n        parts: instance._$parts,\n        options: this.options,\n        fragment,\n        values,\n      });\n      this._commitNode(fragment);\n      this._$committedValue = instance;\n    }\n  }\n\n  // Overridden via `litHtmlPolyfillSupport` to provide platform support.\n  /** @internal */\n  _$getTemplate(result: TemplateResult) {\n    let template = templateCache.get(result.strings);\n    if (template === undefined) {\n      templateCache.set(result.strings, (template = new Template(result)));\n    }\n    return template;\n  }\n\n  private _commitIterable(value: Iterable<unknown>): void {\n    // For an Iterable, we create a new InstancePart per item, then set its\n    // value to the item. This is a little bit of overhead for every item in\n    // an Iterable, but it lets us recurse easily and efficiently update Arrays\n    // of TemplateResults that will be commonly returned from expressions like:\n    // array.map((i) => html`${i}`), by reusing existing TemplateInstances.\n\n    // If value is an array, then the previous render was of an\n    // iterable and value will contain the ChildParts from the previous\n    // render. If value is not an array, clear this part and make a new\n    // array for ChildParts.\n    if (!isArray(this._$committedValue)) {\n      this._$committedValue = [];\n      this._$clear();\n    }\n\n    // Lets us keep track of how many items we stamped so we can clear leftover\n    // items from a previous render\n    const itemParts = this._$committedValue as ChildPart[];\n    let partIndex = 0;\n    let itemPart: ChildPart | undefined;\n\n    for (const item of value) {\n      if (partIndex === itemParts.length) {\n        // If no existing part, create a new one\n        // TODO (justinfagnani): test perf impact of always creating two parts\n        // instead of sharing parts between nodes\n        // https://github.com/lit/lit/issues/1266\n        itemParts.push(\n          (itemPart = new ChildPart(\n            this._insert(createMarker()),\n            this._insert(createMarker()),\n            this,\n            this.options\n          ))\n        );\n      } else {\n        // Reuse an existing part\n        itemPart = itemParts[partIndex];\n      }\n      itemPart._$setValue(item);\n      partIndex++;\n    }\n\n    if (partIndex < itemParts.length) {\n      // itemParts always have end nodes\n      this._$clear(\n        itemPart && wrap(itemPart._$endNode!).nextSibling,\n        partIndex\n      );\n      // Truncate the parts array so _value reflects the current state\n      itemParts.length = partIndex;\n    }\n  }\n\n  /**\n   * Removes the nodes contained within this Part from the DOM.\n   *\n   * @param start Start node to clear from, for clearing a subset of the part's\n   *     DOM (used when truncating iterables)\n   * @param from  When `start` is specified, the index within the iterable from\n   *     which ChildParts are being removed, used for disconnecting directives in\n   *     those Parts.\n   *\n   * @internal\n   */\n  _$clear(\n    start: ChildNode | null = wrap(this._$startNode).nextSibling,\n    from?: number\n  ) {\n    this._$notifyConnectionChanged?.(false, true, from);\n    while (start && start !== this._$endNode) {\n      const n = wrap(start!).nextSibling;\n      (wrap(start!) as Element).remove();\n      start = n;\n    }\n  }\n  /**\n   * Implementation of RootPart's `isConnected`. Note that this metod\n   * should only be called on `RootPart`s (the `ChildPart` returned from a\n   * top-level `render()` call). It has no effect on non-root ChildParts.\n   * @param isConnected Whether to set\n   * @internal\n   */\n  setConnected(isConnected: boolean) {\n    if (this._$parent === undefined) {\n      this.__isConnected = isConnected;\n      this._$notifyConnectionChanged?.(isConnected);\n    } else if (DEV_MODE) {\n      throw new Error(\n        'part.setConnected() may only be called on a ' +\n          'RootPart returned from render().'\n      );\n    }\n  }\n}\n\n/**\n * A top-level `ChildPart` returned from `render` that manages the connected\n * state of `AsyncDirective`s created throughout the tree below it.\n */\nexport interface RootPart extends ChildPart {\n  /**\n   * Sets the connection state for `AsyncDirective`s contained within this root\n   * ChildPart.\n   *\n   * lit-html does not automatically monitor the connectedness of DOM rendered;\n   * as such, it is the responsibility of the caller to `render` to ensure that\n   * `part.setConnected(false)` is called before the part object is potentially\n   * discarded, to ensure that `AsyncDirective`s have a chance to dispose of\n   * any resources being held. If a `RootPart` that was previously\n   * disconnected is subsequently re-connected (and its `AsyncDirective`s should\n   * re-connect), `setConnected(true)` should be called.\n   *\n   * @param isConnected Whether directives within this tree should be connected\n   * or not\n   */\n  setConnected(isConnected: boolean): void;\n}\n\nexport type {AttributePart};\nclass AttributePart implements Disconnectable {\n  readonly type = ATTRIBUTE_PART as\n    | typeof ATTRIBUTE_PART\n    | typeof PROPERTY_PART\n    | typeof BOOLEAN_ATTRIBUTE_PART\n    | typeof EVENT_PART;\n  readonly element: HTMLElement;\n  readonly name: string;\n  readonly options: RenderOptions | undefined;\n\n  /**\n   * If this attribute part represents an interpolation, this contains the\n   * static strings of the interpolation. For single-value, complete bindings,\n   * this is undefined.\n   */\n  readonly strings?: ReadonlyArray<string>;\n  /** @internal */\n  _$committedValue: unknown | Array<unknown> = nothing;\n  /** @internal */\n  __directives?: Array<Directive | undefined>;\n  /** @internal */\n  _$parent: Disconnectable;\n  /** @internal */\n  _$disconnectableChildren?: Set<Disconnectable> = undefined;\n\n  protected _sanitizer: ValueSanitizer | undefined;\n\n  get tagName() {\n    return this.element.tagName;\n  }\n\n  // See comment in Disconnectable interface for why this is a getter\n  get _$isConnected() {\n    return this._$parent._$isConnected;\n  }\n\n  constructor(\n    element: HTMLElement,\n    name: string,\n    strings: ReadonlyArray<string>,\n    parent: Disconnectable,\n    options: RenderOptions | undefined\n  ) {\n    this.element = element;\n    this.name = name;\n    this._$parent = parent;\n    this.options = options;\n    if (strings.length > 2 || strings[0] !== '' || strings[1] !== '') {\n      this._$committedValue = new Array(strings.length - 1).fill(new String());\n      this.strings = strings;\n    } else {\n      this._$committedValue = nothing;\n    }\n    if (ENABLE_EXTRA_SECURITY_HOOKS) {\n      this._sanitizer = undefined;\n    }\n  }\n\n  /**\n   * Sets the value of this part by resolving the value from possibly multiple\n   * values and static strings and committing it to the DOM.\n   * If this part is single-valued, `this._strings` will be undefined, and the\n   * method will be called with a single value argument. If this part is\n   * multi-value, `this._strings` will be defined, and the method is called\n   * with the value array of the part's owning TemplateInstance, and an offset\n   * into the value array from which the values should be read.\n   * This method is overloaded this way to eliminate short-lived array slices\n   * of the template instance values, and allow a fast-path for single-valued\n   * parts.\n   *\n   * @param value The part value, or an array of values for multi-valued parts\n   * @param valueIndex the index to start reading values from. `undefined` for\n   *   single-valued parts\n   * @param noCommit causes the part to not commit its value to the DOM. Used\n   *   in hydration to prime attribute parts with their first-rendered value,\n   *   but not set the attribute, and in SSR to no-op the DOM operation and\n   *   capture the value for serialization.\n   *\n   * @internal\n   */\n  _$setValue(\n    value: unknown | Array<unknown>,\n    directiveParent: DirectiveParent = this,\n    valueIndex?: number,\n    noCommit?: boolean\n  ) {\n    const strings = this.strings;\n\n    // Whether any of the values has changed, for dirty-checking\n    let change = false;\n\n    if (strings === undefined) {\n      // Single-value binding case\n      value = resolveDirective(this, value, directiveParent, 0);\n      change =\n        !isPrimitive(value) ||\n        (value !== this._$committedValue && value !== noChange);\n      if (change) {\n        this._$committedValue = value;\n      }\n    } else {\n      // Interpolation case\n      const values = value as Array<unknown>;\n      value = strings[0];\n\n      let i, v;\n      for (i = 0; i < strings.length - 1; i++) {\n        v = resolveDirective(this, values[valueIndex! + i], directiveParent, i);\n\n        if (v === noChange) {\n          // If the user-provided value is `noChange`, use the previous value\n          v = (this._$committedValue as Array<unknown>)[i];\n        }\n        change ||=\n          !isPrimitive(v) || v !== (this._$committedValue as Array<unknown>)[i];\n        if (v === nothing) {\n          value = nothing;\n        } else if (value !== nothing) {\n          value += (v ?? '') + strings[i + 1];\n        }\n        // We always record each value, even if one is `nothing`, for future\n        // change detection.\n        (this._$committedValue as Array<unknown>)[i] = v;\n      }\n    }\n    if (change && !noCommit) {\n      this._commitValue(value);\n    }\n  }\n\n  /** @internal */\n  _commitValue(value: unknown) {\n    if (value === nothing) {\n      (wrap(this.element) as Element).removeAttribute(this.name);\n    } else {\n      if (ENABLE_EXTRA_SECURITY_HOOKS) {\n        if (this._sanitizer === undefined) {\n          this._sanitizer = sanitizerFactoryInternal(\n            this.element,\n            this.name,\n            'attribute'\n          );\n        }\n        value = this._sanitizer(value ?? '');\n      }\n      debugLogEvent?.({\n        kind: 'commit attribute',\n        element: this.element,\n        name: this.name,\n        value,\n        options: this.options,\n      });\n      (wrap(this.element) as Element).setAttribute(\n        this.name,\n        (value ?? '') as string\n      );\n    }\n  }\n}\n\nexport type {PropertyPart};\nclass PropertyPart extends AttributePart {\n  override readonly type = PROPERTY_PART;\n\n  /** @internal */\n  override _commitValue(value: unknown) {\n    if (ENABLE_EXTRA_SECURITY_HOOKS) {\n      if (this._sanitizer === undefined) {\n        this._sanitizer = sanitizerFactoryInternal(\n          this.element,\n          this.name,\n          'property'\n        );\n      }\n      value = this._sanitizer(value);\n    }\n    debugLogEvent?.({\n      kind: 'commit property',\n      element: this.element,\n      name: this.name,\n      value,\n      options: this.options,\n    });\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    (this.element as any)[this.name] = value === nothing ? undefined : value;\n  }\n}\n\n// Temporary workaround for https://crbug.com/993268\n// Currently, any attribute starting with \"on\" is considered to be a\n// TrustedScript source. Such boolean attributes must be set to the equivalent\n// trusted emptyScript value.\nconst emptyStringForBooleanAttribute = trustedTypes\n  ? (trustedTypes.emptyScript as unknown as '')\n  : '';\n\nexport type {BooleanAttributePart};\nclass BooleanAttributePart extends AttributePart {\n  override readonly type = BOOLEAN_ATTRIBUTE_PART;\n\n  /** @internal */\n  override _commitValue(value: unknown) {\n    debugLogEvent?.({\n      kind: 'commit boolean attribute',\n      element: this.element,\n      name: this.name,\n      value: !!(value && value !== nothing),\n      options: this.options,\n    });\n    if (value && value !== nothing) {\n      (wrap(this.element) as Element).setAttribute(\n        this.name,\n        emptyStringForBooleanAttribute\n      );\n    } else {\n      (wrap(this.element) as Element).removeAttribute(this.name);\n    }\n  }\n}\n\ntype EventListenerWithOptions = EventListenerOrEventListenerObject &\n  Partial<AddEventListenerOptions>;\n\n/**\n * An AttributePart that manages an event listener via add/removeEventListener.\n *\n * This part works by adding itself as the event listener on an element, then\n * delegating to the value passed to it. This reduces the number of calls to\n * add/removeEventListener if the listener changes frequently, such as when an\n * inline function is used as a listener.\n *\n * Because event options are passed when adding listeners, we must take case\n * to add and remove the part as a listener when the event options change.\n */\nexport type {EventPart};\nclass EventPart extends AttributePart {\n  override readonly type = EVENT_PART;\n\n  constructor(\n    element: HTMLElement,\n    name: string,\n    strings: ReadonlyArray<string>,\n    parent: Disconnectable,\n    options: RenderOptions | undefined\n  ) {\n    super(element, name, strings, parent, options);\n\n    if (DEV_MODE && this.strings !== undefined) {\n      throw new Error(\n        `A \\`<${element.localName}>\\` has a \\`@${name}=...\\` listener with ` +\n          'invalid content. Event listeners in templates must have exactly ' +\n          'one expression and no surrounding text.'\n      );\n    }\n  }\n\n  // EventPart does not use the base _$setValue/_resolveValue implementation\n  // since the dirty checking is more complex\n  /** @internal */\n  override _$setValue(\n    newListener: unknown,\n    directiveParent: DirectiveParent = this\n  ) {\n    newListener =\n      resolveDirective(this, newListener, directiveParent, 0) ?? nothing;\n    if (newListener === noChange) {\n      return;\n    }\n    const oldListener = this._$committedValue;\n\n    // If the new value is nothing or any options change we have to remove the\n    // part as a listener.\n    const shouldRemoveListener =\n      (newListener === nothing && oldListener !== nothing) ||\n      (newListener as EventListenerWithOptions).capture !==\n        (oldListener as EventListenerWithOptions).capture ||\n      (newListener as EventListenerWithOptions).once !==\n        (oldListener as EventListenerWithOptions).once ||\n      (newListener as EventListenerWithOptions).passive !==\n        (oldListener as EventListenerWithOptions).passive;\n\n    // If the new value is not nothing and we removed the listener, we have\n    // to add the part as a listener.\n    const shouldAddListener =\n      newListener !== nothing &&\n      (oldListener === nothing || shouldRemoveListener);\n\n    debugLogEvent?.({\n      kind: 'commit event listener',\n      element: this.element,\n      name: this.name,\n      value: newListener,\n      options: this.options,\n      removeListener: shouldRemoveListener,\n      addListener: shouldAddListener,\n      oldListener,\n    });\n    if (shouldRemoveListener) {\n      this.element.removeEventListener(\n        this.name,\n        this,\n        oldListener as EventListenerWithOptions\n      );\n    }\n    if (shouldAddListener) {\n      // Beware: IE11 and Chrome 41 don't like using the listener as the\n      // options object. Figure out how to deal w/ this in IE11 - maybe\n      // patch addEventListener?\n      this.element.addEventListener(\n        this.name,\n        this,\n        newListener as EventListenerWithOptions\n      );\n    }\n    this._$committedValue = newListener;\n  }\n\n  handleEvent(event: Event) {\n    if (typeof this._$committedValue === 'function') {\n      this._$committedValue.call(this.options?.host ?? this.element, event);\n    } else {\n      (this._$committedValue as EventListenerObject).handleEvent(event);\n    }\n  }\n}\n\nexport type {ElementPart};\nclass ElementPart implements Disconnectable {\n  readonly type = ELEMENT_PART;\n\n  /** @internal */\n  __directive?: Directive;\n\n  // This is to ensure that every Part has a _$committedValue\n  _$committedValue: undefined;\n\n  /** @internal */\n  _$parent!: Disconnectable;\n\n  /** @internal */\n  _$disconnectableChildren?: Set<Disconnectable> = undefined;\n\n  options: RenderOptions | undefined;\n\n  constructor(\n    public element: Element,\n    parent: Disconnectable,\n    options: RenderOptions | undefined\n  ) {\n    this._$parent = parent;\n    this.options = options;\n  }\n\n  // See comment in Disconnectable interface for why this is a getter\n  get _$isConnected() {\n    return this._$parent._$isConnected;\n  }\n\n  _$setValue(value: unknown): void {\n    debugLogEvent?.({\n      kind: 'commit to element binding',\n      element: this.element,\n      value,\n      options: this.options,\n    });\n    resolveDirective(this, value);\n  }\n}\n\n/**\n * END USERS SHOULD NOT RELY ON THIS OBJECT.\n *\n * Private exports for use by other Lit packages, not intended for use by\n * external users.\n *\n * We currently do not make a mangled rollup build of the lit-ssr code. In order\n * to keep a number of (otherwise private) top-level exports  mangled in the\n * client side code, we export a _$LH object containing those members (or\n * helper methods for accessing private fields of those members), and then\n * re-export them for use in lit-ssr. This keeps lit-ssr agnostic to whether the\n * client-side code is being used in `dev` mode or `prod` mode.\n *\n * This has a unique name, to disambiguate it from private exports in\n * lit-element, which re-exports all of lit-html.\n *\n * @private\n */\nexport const _$LH = {\n  // Used in lit-ssr\n  _boundAttributeSuffix: boundAttributeSuffix,\n  _marker: marker,\n  _markerMatch: markerMatch,\n  _HTML_RESULT: HTML_RESULT,\n  _getTemplateHtml: getTemplateHtml,\n  // Used in tests and private-ssr-support\n  _TemplateInstance: TemplateInstance,\n  _isIterable: isIterable,\n  _resolveDirective: resolveDirective,\n  _ChildPart: ChildPart,\n  _AttributePart: AttributePart,\n  _BooleanAttributePart: BooleanAttributePart,\n  _EventPart: EventPart,\n  _PropertyPart: PropertyPart,\n  _ElementPart: ElementPart,\n};\n\n// Apply polyfills if available\nconst polyfillSupport = DEV_MODE\n  ? global.litHtmlPolyfillSupportDevMode\n  : global.litHtmlPolyfillSupport;\npolyfillSupport?.(Template, ChildPart);\n\n// IMPORTANT: do not change the property name or the assignment expression.\n// This line will be used in regexes to search for lit-html usage.\n(global.litHtmlVersions ??= []).push('2.8.0');\nif (DEV_MODE && global.litHtmlVersions.length > 1) {\n  issueWarning!(\n    'multiple-versions',\n    `Multiple versions of Lit loaded. ` +\n      `Loading multiple versions is not recommended.`\n  );\n}\n\n/**\n * Renders a value, usually a lit-html TemplateResult, to the container.\n *\n * This example renders the text \"Hello, Zoe!\" inside a paragraph tag, appending\n * it to the container `document.body`.\n *\n * ```js\n * import {html, render} from 'lit';\n *\n * const name = \"Zoe\";\n * render(html`<p>Hello, ${name}!</p>`, document.body);\n * ```\n *\n * @param value Any [renderable\n *   value](https://lit.dev/docs/templates/expressions/#child-expressions),\n *   typically a {@linkcode TemplateResult} created by evaluating a template tag\n *   like {@linkcode html} or {@linkcode svg}.\n * @param container A DOM container to render to. The first render will append\n *   the rendered value to the container, and subsequent renders will\n *   efficiently update the rendered value if the same result type was\n *   previously rendered there.\n * @param options See {@linkcode RenderOptions} for options documentation.\n * @see\n * {@link https://lit.dev/docs/libraries/standalone-templates/#rendering-lit-html-templates| Rendering Lit HTML Templates}\n */\nexport const render = (\n  value: unknown,\n  container: HTMLElement | DocumentFragment,\n  options?: RenderOptions\n): RootPart => {\n  if (DEV_MODE && container == null) {\n    // Give a clearer error message than\n    //     Uncaught TypeError: Cannot read properties of null (reading\n    //     '_$litPart$')\n    // which reads like an internal Lit error.\n    throw new TypeError(`The container to render into may not be ${container}`);\n  }\n  const renderId = DEV_MODE ? debugLogRenderId++ : 0;\n  const partOwnerNode = options?.renderBefore ?? container;\n  // This property needs to remain unminified.\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  let part: ChildPart = (partOwnerNode as any)['_$litPart$'];\n  debugLogEvent?.({\n    kind: 'begin render',\n    id: renderId,\n    value,\n    container,\n    options,\n    part,\n  });\n  if (part === undefined) {\n    const endNode = options?.renderBefore ?? null;\n    // This property needs to remain unminified.\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    (partOwnerNode as any)['_$litPart$'] = part = new ChildPart(\n      container.insertBefore(createMarker(), endNode),\n      endNode,\n      undefined,\n      options ?? {}\n    );\n  }\n  part._$setValue(value);\n  debugLogEvent?.({\n    kind: 'end render',\n    id: renderId,\n    value,\n    container,\n    options,\n    part,\n  });\n  return part as RootPart;\n};\n\nif (ENABLE_EXTRA_SECURITY_HOOKS) {\n  render.setSanitizer = setSanitizer;\n  render.createSanitizer = createSanitizer;\n  if (DEV_MODE) {\n    render._testOnlyClearSanitizerFactoryDoNotCallOrElse =\n      _testOnlyClearSanitizerFactoryDoNotCallOrElse;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;AAIG;;AASH;AACA,MAAM,MAAM,GAAe,UAAU,CAAS,CAAC;AA8K/C;;;;AAIG;AACH,MAAM,aAAa,GACf,CAAC,KAAiC,KAAI;QACpC,MAAM,UAAU,GAAI,MAAwC;AACzD,aAAA,qBAAqB,CAAC;QACzB,IAAI,CAAC,UAAU,EAAE;YACf,OAAO;AACR,SAAA;AACD,QAAA,MAAM,CAAC,aAAa,CAClB,IAAI,WAAW,CAA6B,WAAW,EAAE;AACvD,YAAA,MAAM,EAAE,KAAK;AACd,SAAA,CAAC,CACH,CAAC;KACH;IACQ,CAAC;AACd;AACA;AACA;AACA,IAAI,gBAAgB,GAAG,CAAC,CAAC;AAEzB,IAAI,YAAqD,CAAC;AAE5C;IACZ,CAAA,EAAA,GAAA,MAAM,CAAC,iBAAiB,MAAxB,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,IAAA,MAAM,CAAC,iBAAiB,GAAK,IAAI,GAAG,EAAE,CAAC,CAAA;;AAGvC,IAAA,YAAY,GAAG,CAAC,IAAY,EAAE,OAAe,KAAI;AAC/C,QAAA,OAAO,IAAI,IAAI;cACX,CAA4B,yBAAA,EAAA,IAAI,CAAwB,sBAAA,CAAA;cACxD,EAAE,CAAC;QACP,IAAI,CAAC,MAAM,CAAC,iBAAkB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;AAC3C,YAAA,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACtB,YAAA,MAAM,CAAC,iBAAkB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;AACxC,SAAA;AACH,KAAC,CAAC;AAEF,IAAA,YAAY,CACV,UAAU,EACV,CAAA,mDAAA,CAAqD,CACtD,CAAC;AACH,CAAA;AAED,MAAM,IAAI,GAKJ,CAAC,IAAU,KAAK,IAAI,CAAC;AAE3B,MAAM,YAAY,GAAI,MAAqC,CAAC,YAAY,CAAC;AAEzE;;;;;;;AAOG;AACH,MAAM,MAAM,GAAG,YAAY;AACzB,MAAE,YAAY,CAAC,YAAY,CAAC,UAAU,EAAE;AACpC,QAAA,UAAU,EAAE,CAAC,CAAC,KAAK,CAAC;KACrB,CAAC;MACF,SAAS,CAAC;AA0Cd,MAAM,gBAAgB,GAAmB,CAAC,KAAc,KAAK,KAAK,CAAC;AACnE,MAAM,aAAa,GAAqB,CACtC,KAAW,EACX,KAAa,EACb,KAA+B,KAC5B,gBAAgB,CAAC;AAEtB;AACA,MAAM,YAAY,GAAG,CAAC,YAA8B,KAAI;IAItD,IAAI,wBAAwB,KAAK,aAAa,EAAE;QAC9C,MAAM,IAAI,KAAK,CACb,CAA2D,yDAAA,CAAA;AACzD,YAAA,CAAA,0DAAA,CAA4D,CAC/D,CAAC;AACH,KAAA;IACD,wBAAwB,GAAG,YAAY,CAAC;AAC1C,CAAC,CAAC;AAEF;;AAEG;AACH,MAAM,6CAA6C,GAAG,MAAK;IACzD,wBAAwB,GAAG,aAAa,CAAC;AAC3C,CAAC,CAAC;AAEF,MAAM,eAAe,GAAqB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,KAAI;IAC7D,OAAO,wBAAwB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AACpD,CAAC,CAAC;AAEF;AACA;AACA,MAAM,oBAAoB,GAAG,OAAO,CAAC;AAErC;AACA;AACA;AACA;AACA,MAAM,MAAM,GAAG,CAAA,IAAA,EAAO,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;AAExD;AACA,MAAM,WAAW,GAAG,GAAG,GAAG,MAAM,CAAC;AAEjC;AACA;AACA,MAAM,UAAU,GAAG,CAAI,CAAA,EAAA,WAAW,GAAG,CAAC;AAEtC,MAAM,CAAC,GACQ,MAAM,CAAC,QAAQ,KAAK,SAAS;AACxC,MAAG;QACC,gBAAgB,GAAA;AACd,YAAA,OAAO,EAAE,CAAC;SACX;AACsB,KAAA;MACzB,QAAQ,CAAC;AAEf;AACA,MAAM,YAAY,GAAG,MAAM,CAAC,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;AAI/C,MAAM,WAAW,GAAG,CAAC,KAAc,KACjC,KAAK,KAAK,IAAI,KAAK,OAAO,KAAK,IAAI,QAAQ,IAAI,OAAO,KAAK,IAAI,UAAU,CAAC,CAAC;AAC7E,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;AAC9B,MAAM,UAAU,GAAG,CAAC,KAAc,KAChC,OAAO,CAAC,KAAK,CAAC;;AAEd,IAAA,QAAQ,KAAa,KAAb,IAAA,IAAA,KAAK,uBAAL,KAAK,CAAW,MAAM,CAAC,QAAQ,CAAC,CAAA,KAAK,UAAU,CAAC;AAE1D,MAAM,UAAU,GAAG,CAAA,WAAA,CAAa,CAAC;AACjC,MAAM,eAAe,GAAG,CAAA,mBAAA,CAAqB,CAAC;AAC9C,MAAM,SAAS,GAAG,CAAA,WAAA,CAAa,CAAC;AAEhC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;AAGG;AACH,MAAM,YAAY,GAAG,qDAAqD,CAAC;AAC3E,MAAM,aAAa,GAAG,CAAC,CAAC;AACxB,MAAM,QAAQ,GAAG,CAAC,CAAC;AACnB,MAAM,gBAAgB,GAAG,CAAC,CAAC;AAE3B,MAAM,eAAe,GAAG,MAAM,CAAC;AAC/B;;AAEG;AACH,MAAM,gBAAgB,GAAG,IAAI,CAAC;AAE9B;;;;;;;;;;;;;;;;;;;;;AAqBG;AACH,MAAM,WAAW,GAAG,IAAI,MAAM,CAC5B,CAAA,EAAA,EAAK,UAAU,CAAO,IAAA,EAAA,SAAS,MAAM,UAAU,CAAA,EAAA,EAAK,UAAU,CAAO,IAAA,EAAA,eAAe,cAAc,EAClG,GAAG,CACJ,CAAC;AACF,MAAM,YAAY,GAAG,CAAC,CAAC;AACvB,MAAM,cAAc,GAAG,CAAC,CAAC;AACzB,MAAM,iBAAiB,GAAG,CAAC,CAAC;AAC5B,MAAM,UAAU,GAAG,CAAC,CAAC;AAErB,MAAM,uBAAuB,GAAG,IAAI,CAAC;AACrC,MAAM,uBAAuB,GAAG,IAAI,CAAC;AACrC;;;;;AAKG;AACH,MAAM,cAAc,GAAG,oCAAoC,CAAC;AAE5D;AACA,MAAM,WAAW,GAAG,CAAC,CAAC;AACtB,MAAM,UAAU,GAAG,CAAC,CAAC;AAIrB;AACA;AACA,MAAM,cAAc,GAAG,CAAC,CAAC;AACzB,MAAM,UAAU,GAAG,CAAC,CAAC;AACrB,MAAM,aAAa,GAAG,CAAC,CAAC;AACxB,MAAM,sBAAsB,GAAG,CAAC,CAAC;AACjC,MAAM,UAAU,GAAG,CAAC,CAAC;AACrB,MAAM,YAAY,GAAG,CAAC,CAAC;AACvB,MAAM,YAAY,GAAG,CAAC,CAAC;AA4CvB;;;AAGG;AACH,MAAM,GAAG,GACP,CAAuB,IAAO,KAC9B,CAAC,OAA6B,EAAE,GAAG,MAAiB,KAAuB;;;;AAIzE,IAAA,IAAgB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,SAAS,CAAC,EAAE;QACpD,OAAO,CAAC,IAAI,CACV,wCAAwC;AACtC,YAAA,4DAA4D,CAC/D,CAAC;AACH,KAAA;IACD,OAAO;;QAEL,CAAC,YAAY,GAAG,IAAI;QACpB,OAAO;QACP,MAAM;KACP,CAAC;AACJ,CAAC,CAAC;AAEJ;;;;;;;;;;;;AAYG;MACU,IAAI,GAAG,GAAG,CAAC,WAAW,EAAE;AAErC;;;;;;;;;;;;;;;;;;;;;;AAsBG;MACU,GAAG,GAAG,GAAG,CAAC,UAAU,EAAE;AAEnC;;;AAGG;AACU,MAAA,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,cAAc,EAAE;AAEnD;;;;;;;;;;;;;;;;;;AAkBG;AACU,MAAA,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC,aAAa,EAAE;AAEjD;;;;;;AAMG;AACH,MAAM,aAAa,GAAG,IAAI,OAAO,EAAkC,CAAC;AAqCpE,MAAM,MAAM,GAAG,CAAC,CAAC,gBAAgB,CAC/B,CAAC,EACD,GAAG,0CACH,IAAI,EACJ,KAAK,CACN,CAAC;AAEF,IAAI,wBAAwB,GAAqB,aAAa,CAAC;AAkB/D,SAAS,uBAAuB,CAC9B,GAAyB,EACzB,aAAqB,EAAA;;;;;;AAOrB,IAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;QACrD,IAAI,OAAO,GAAG,gCAAgC,CAAC;AAC/C,QAAc;AACZ,YAAA,OAAO,GAAG,CAAA;;;;;;;;;;AAUP,QAAA,CAAA;AACA,iBAAA,IAAI,EAAE;AACN,iBAAA,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AAC3B,SAAA;AACD,QAAA,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;AAC1B,KAAA;IACD,OAAO,MAAM,KAAK,SAAS;AACzB,UAAE,MAAM,CAAC,UAAU,CAAC,aAAa,CAAC;UAC/B,aAAwC,CAAC;AAChD,CAAC;AAED;;;;;;;;;;;AAWG;AACH,MAAM,eAAe,GAAG,CACtB,OAA6B,EAC7B,IAAgB,KAC4B;;;;;;;AAO5C,IAAA,MAAM,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;;;;IAI7B,MAAM,SAAS,GAA8B,EAAE,CAAC;AAChD,IAAA,IAAI,IAAI,GAAG,IAAI,KAAK,UAAU,GAAG,OAAO,GAAG,EAAE,CAAC;;;;AAK9C,IAAA,IAAI,eAAmC,CAAC;;;IAIxC,IAAI,KAAK,GAAG,YAAY,CAAC;IAEzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC1B,QAAA,MAAM,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;;;;;;AAMrB,QAAA,IAAI,gBAAgB,GAAG,CAAC,CAAC,CAAC;AAC1B,QAAA,IAAI,QAA4B,CAAC;QACjC,IAAI,SAAS,GAAG,CAAC,CAAC;AAClB,QAAA,IAAI,KAA8B,CAAC;;;AAInC,QAAA,OAAO,SAAS,GAAG,CAAC,CAAC,MAAM,EAAE;;AAE3B,YAAA,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC;AAC5B,YAAA,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACtB,IAAI,KAAK,KAAK,IAAI,EAAE;gBAClB,MAAM;AACP,aAAA;AACD,YAAA,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC;YAC5B,IAAI,KAAK,KAAK,YAAY,EAAE;AAC1B,gBAAA,IAAI,KAAK,CAAC,aAAa,CAAC,KAAK,KAAK,EAAE;oBAClC,KAAK,GAAG,eAAe,CAAC;AACzB,iBAAA;AAAM,qBAAA,IAAI,KAAK,CAAC,aAAa,CAAC,KAAK,SAAS,EAAE;;oBAE7C,KAAK,GAAG,gBAAgB,CAAC;AAC1B,iBAAA;AAAM,qBAAA,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,SAAS,EAAE;oBACxC,IAAI,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE;;;AAGxC,wBAAA,eAAe,GAAG,IAAI,MAAM,CAAC,CAAK,EAAA,EAAA,KAAK,CAAC,QAAQ,CAAC,CAAA,CAAE,EAAE,GAAG,CAAC,CAAC;AAC3D,qBAAA;oBACD,KAAK,GAAG,WAAW,CAAC;AACrB,iBAAA;AAAM,qBAAA,IAAI,KAAK,CAAC,gBAAgB,CAAC,KAAK,SAAS,EAAE;AAChD,oBAAc;wBACZ,MAAM,IAAI,KAAK,CACb,gFAAgF;AAC9E,4BAAA,oEAAoE,CACvE,CAAC;AACH,qBAAA;AAEF,iBAAA;AACF,aAAA;iBAAM,IAAI,KAAK,KAAK,WAAW,EAAE;AAChC,gBAAA,IAAI,KAAK,CAAC,YAAY,CAAC,KAAK,GAAG,EAAE;;;oBAG/B,KAAK,GAAG,eAAe,KAAf,IAAA,IAAA,eAAe,cAAf,eAAe,GAAI,YAAY,CAAC;;;oBAGxC,gBAAgB,GAAG,CAAC,CAAC,CAAC;AACvB,iBAAA;AAAM,qBAAA,IAAI,KAAK,CAAC,cAAc,CAAC,KAAK,SAAS,EAAE;;oBAE9C,gBAAgB,GAAG,CAAC,CAAC,CAAC;AACvB,iBAAA;AAAM,qBAAA;oBACL,gBAAgB,GAAG,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,iBAAiB,CAAC,CAAC,MAAM,CAAC;AACrE,oBAAA,QAAQ,GAAG,KAAK,CAAC,cAAc,CAAC,CAAC;oBACjC,KAAK;AACH,wBAAA,KAAK,CAAC,UAAU,CAAC,KAAK,SAAS;AAC7B,8BAAE,WAAW;AACb,8BAAE,KAAK,CAAC,UAAU,CAAC,KAAK,GAAG;AAC3B,kCAAE,uBAAuB;kCACvB,uBAAuB,CAAC;AAC/B,iBAAA;AACF,aAAA;iBAAM,IACL,KAAK,KAAK,uBAAuB;gBACjC,KAAK,KAAK,uBAAuB,EACjC;gBACA,KAAK,GAAG,WAAW,CAAC;AACrB,aAAA;AAAM,iBAAA,IAAI,KAAK,KAAK,eAAe,IAAI,KAAK,KAAK,gBAAgB,EAAE;gBAClE,KAAK,GAAG,YAAY,CAAC;AACtB,aAAA;AAAM,iBAAA;;;gBAGL,KAAK,GAAG,WAAW,CAAC;gBACpB,eAAe,GAAG,SAAS,CAAC;AAC7B,aAAA;AACF,SAAA;AAED,QAAc;;;;AAIZ,YAAA,OAAO,CAAC,MAAM,CACZ,gBAAgB,KAAK,CAAC,CAAC;AACrB,gBAAA,KAAK,KAAK,WAAW;AACrB,gBAAA,KAAK,KAAK,uBAAuB;AACjC,gBAAA,KAAK,KAAK,uBAAuB,EACnC,0BAA0B,CAC3B,CAAC;AACH,SAAA;;;;;;;;;;;;;QAeD,MAAM,GAAG,GACP,KAAK,KAAK,WAAW,IAAI,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;QACtE,IAAI;AACF,YAAA,KAAK,KAAK,YAAY;kBAClB,CAAC,GAAG,UAAU;kBACd,gBAAgB,IAAI,CAAC;AACvB,sBAAE,CAAC,SAAS,CAAC,IAAI,CAAC,QAAS,CAAC;AAC1B,wBAAA,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,gBAAgB,CAAC;4BAC1B,oBAAoB;AACpB,4BAAA,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC;wBAC3B,MAAM;wBACN,GAAG;AACL,sBAAE,CAAC;wBACD,MAAM;yBACL,gBAAgB,KAAK,CAAC,CAAC,IAAI,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC;AACxE,KAAA;IAED,MAAM,UAAU,GACd,IAAI,IAAI,OAAO,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAC;;IAGvE,OAAO,CAAC,uBAAuB,CAAC,OAAO,EAAE,UAAU,CAAC,EAAE,SAAS,CAAC,CAAC;AACnE,CAAC,CAAC;AAIF,MAAM,QAAQ,CAAA;AAMZ,IAAA,WAAA;;IAEE,EAAC,OAAO,EAAE,CAAC,YAAY,GAAG,IAAI,EAAiB,EAC/C,OAAuB,EAAA;QALzB,IAAK,CAAA,KAAA,GAAwB,EAAE,CAAC;AAO9B,QAAA,IAAI,IAAiB,CAAC;QACtB,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,IAAI,aAAa,GAAG,CAAC,CAAC;AACtB,QAAA,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;AACrC,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;;AAGzB,QAAA,MAAM,CAAC,IAAI,EAAE,SAAS,CAAC,GAAG,eAAe,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACzD,IAAI,CAAC,EAAE,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAChD,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC;;QAGrC,IAAI,IAAI,KAAK,UAAU,EAAE;AACvB,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC;AAChC,YAAA,MAAM,UAAU,GAAG,OAAO,CAAC,UAAW,CAAC;YACvC,UAAU,CAAC,MAAM,EAAE,CAAC;YACpB,OAAO,CAAC,MAAM,CAAC,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC;AAC1C,SAAA;;AAGD,QAAA,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,QAAQ,EAAE,MAAM,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,SAAS,EAAE;AACtE,YAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,EAAE;AACvB,gBAAc;AACZ,oBAAA,MAAM,GAAG,GAAI,IAAgB,CAAC,SAAS,CAAC;;;;;AAKxC,oBAAA,IACE,0BAA2B,CAAC,IAAI,CAAC,GAAG,CAAC;AACpC,wBAAA,IAAgB,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,EAC5C;AACA,wBAAA,MAAM,CAAC,GACL,CAA0C,uCAAA,EAAA,GAAG,CAAK,GAAA,CAAA;AAClD,4BAAA,CAAA,gDAAA,EAAmD,GAAG,CAAY,UAAA,CAAA;AAClE,4BAAA,CAAA,YAAA,CAAc,CAAC;wBACjB,IAAI,GAAG,KAAK,UAAU,EAAE;AACtB,4BAAA,MAAM,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;AACpB,yBAAA;;AAAM,4BAAA,YAAY,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AAC5B,qBAAA;AACF,iBAAA;;;;AAID,gBAAA,IAAK,IAAgB,CAAC,aAAa,EAAE,EAAE;;;;oBAIrC,MAAM,aAAa,GAAG,EAAE,CAAC;AACzB,oBAAA,KAAK,MAAM,IAAI,IAAK,IAAgB,CAAC,iBAAiB,EAAE,EAAE;;;;;;;;AAQxD,wBAAA,IACE,IAAI,CAAC,QAAQ,CAAC,oBAAoB,CAAC;AACnC,4BAAA,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EACvB;AACA,4BAAA,MAAM,QAAQ,GAAG,SAAS,CAAC,aAAa,EAAE,CAAC,CAAC;AAC5C,4BAAA,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;4BACzB,IAAI,QAAQ,KAAK,SAAS,EAAE;;AAE1B,gCAAA,MAAM,KAAK,GAAI,IAAgB,CAAC,YAAY,CAC1C,QAAQ,CAAC,WAAW,EAAE,GAAG,oBAAoB,CAC7C,CAAC;gCACH,MAAM,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;gCACpC,MAAM,CAAC,GAAG,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAE,CAAC;gCACzC,KAAK,CAAC,IAAI,CAAC;AACT,oCAAA,IAAI,EAAE,cAAc;AACpB,oCAAA,KAAK,EAAE,SAAS;AAChB,oCAAA,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;AACV,oCAAA,OAAO,EAAE,OAAO;AAChB,oCAAA,IAAI,EACF,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG;AACV,0CAAE,YAAY;AACd,0CAAE,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG;AACd,8CAAE,oBAAoB;AACtB,8CAAE,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG;AACd,kDAAE,SAAS;AACX,kDAAE,aAAa;AACpB,iCAAA,CAAC,CAAC;AACJ,6BAAA;AAAM,iCAAA;gCACL,KAAK,CAAC,IAAI,CAAC;AACT,oCAAA,IAAI,EAAE,YAAY;AAClB,oCAAA,KAAK,EAAE,SAAS;AACjB,iCAAA,CAAC,CAAC;AACJ,6BAAA;AACF,yBAAA;AACF,qBAAA;AACD,oBAAA,KAAK,MAAM,IAAI,IAAI,aAAa,EAAE;AAC/B,wBAAA,IAAgB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;AACzC,qBAAA;AACF,iBAAA;;;gBAGD,IAAI,cAAc,CAAC,IAAI,CAAE,IAAgB,CAAC,OAAO,CAAC,EAAE;;;;oBAIlD,MAAM,OAAO,GAAI,IAAgB,CAAC,WAAY,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AAC7D,oBAAA,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;oBACrC,IAAI,SAAS,GAAG,CAAC,EAAE;wBAChB,IAAgB,CAAC,WAAW,GAAG,YAAY;8BACvC,YAAY,CAAC,WAA6B;8BAC3C,EAAE,CAAC;;;;;;wBAMP,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;4BACjC,IAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC;;4BAErD,MAAM,CAAC,QAAQ,EAAE,CAAC;AAClB,4BAAA,KAAK,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,EAAE,SAAS,EAAC,CAAC,CAAC;AACpD,yBAAA;;;;wBAIA,IAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC;AAC9D,qBAAA;AACF,iBAAA;AACF,aAAA;AAAM,iBAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,EAAE;AAC9B,gBAAA,MAAM,IAAI,GAAI,IAAgB,CAAC,IAAI,CAAC;gBACpC,IAAI,IAAI,KAAK,WAAW,EAAE;AACxB,oBAAA,KAAK,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,SAAS,EAAC,CAAC,CAAC;AAClD,iBAAA;AAAM,qBAAA;AACL,oBAAA,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AACX,oBAAA,OAAO,CAAC,CAAC,GAAI,IAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE;;;AAGjE,wBAAA,KAAK,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,SAAS,EAAC,CAAC,CAAC;;AAEnD,wBAAA,CAAC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;AACxB,qBAAA;AACF,iBAAA;AACF,aAAA;AACD,YAAA,SAAS,EAAE,CAAC;AACb,SAAA;;;;AAID,QAAA,aAAa,KAAb,IAAA,IAAA,aAAa,KAAb,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,aAAa,CAAG;AACd,YAAA,IAAI,EAAE,eAAe;AACrB,YAAA,QAAQ,EAAE,IAAI;YACd,gBAAgB,EAAE,IAAI,CAAC,EAAE;YACzB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,OAAO;AACR,SAAA,CAAC,CAAC;KACJ;;;AAID,IAAA,OAAO,aAAa,CAAC,IAAiB,EAAE,QAAwB,EAAA;QAC9D,MAAM,EAAE,GAAG,CAAC,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;AACvC,QAAA,EAAE,CAAC,SAAS,GAAG,IAAyB,CAAC;AACzC,QAAA,OAAO,EAAE,CAAC;KACX;AACF,CAAA;AAeD,SAAS,gBAAgB,CACvB,IAA6C,EAC7C,KAAc,EACd,MAAA,GAA0B,IAAI,EAC9B,cAAuB,EAAA;;;;;IAIvB,IAAI,KAAK,KAAK,QAAQ,EAAE;AACtB,QAAA,OAAO,KAAK,CAAC;AACd,KAAA;AACD,IAAA,IAAI,gBAAgB,GAClB,cAAc,KAAK,SAAS;AAC1B,UAAE,CAAC,EAAA,GAAA,MAAwB,CAAC,YAAY,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAG,cAAc,CAAC;AAC1D,UAAG,MAA8C,CAAC,WAAW,CAAC;AAClE,IAAA,MAAM,wBAAwB,GAAG,WAAW,CAAC,KAAK,CAAC;AACjD,UAAE,SAAS;AACX;YACG,KAAyB,CAAC,iBAAiB,CAAC,CAAC;IAClD,IAAI,CAAA,gBAAgB,KAAA,IAAA,IAAhB,gBAAgB,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAhB,gBAAgB,CAAE,WAAW,MAAK,wBAAwB,EAAE;;QAE9D,CAAA,EAAA,GAAA,gBAAgB,KAAhB,IAAA,IAAA,gBAAgB,KAAhB,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,gBAAgB,CAAG,oCAAoC,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,gBAAA,EAAG,KAAK,CAAC,CAAC;QAClE,IAAI,wBAAwB,KAAK,SAAS,EAAE;YAC1C,gBAAgB,GAAG,SAAS,CAAC;AAC9B,SAAA;AAAM,aAAA;AACL,YAAA,gBAAgB,GAAG,IAAI,wBAAwB,CAAC,IAAgB,CAAC,CAAC;YAClE,gBAAgB,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,EAAE,cAAc,CAAC,CAAC;AAC7D,SAAA;QACD,IAAI,cAAc,KAAK,SAAS,EAAE;YAChC,CAAE,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,MAAwB,EAAC,YAAY,MAAZ,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,IAAA,EAAA,CAAA,YAAY,GAAK,EAAE,CAAA,EAAE,cAAc,CAAC;AAC7D,gBAAA,gBAAgB,CAAC;AACpB,SAAA;AAAM,aAAA;AACJ,YAAA,MAAgC,CAAC,WAAW,GAAG,gBAAgB,CAAC;AAClE,SAAA;AACF,KAAA;IACD,IAAI,gBAAgB,KAAK,SAAS,EAAE;QAClC,KAAK,GAAG,gBAAgB,CACtB,IAAI,EACJ,gBAAgB,CAAC,SAAS,CAAC,IAAI,EAAG,KAAyB,CAAC,MAAM,CAAC,EACnE,gBAAgB,EAChB,cAAc,CACf,CAAC;AACH,KAAA;AACD,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAGD;;;AAGG;AACH,MAAM,gBAAgB,CAAA;IASpB,WAAY,CAAA,QAAkB,EAAE,MAAiB,EAAA;QAPjD,IAAO,CAAA,OAAA,GAA4B,EAAE,CAAC;;QAKtC,IAAwB,CAAA,wBAAA,GAAyB,SAAS,CAAC;AAGzD,QAAA,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC;AAC3B,QAAA,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC;KACxB;;AAGD,IAAA,IAAI,UAAU,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;KACjC;;AAGD,IAAA,IAAI,aAAa,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC;KACpC;;;AAID,IAAA,MAAM,CAAC,OAAkC,EAAA;;AACvC,QAAA,MAAM,EACJ,EAAE,EAAE,EAAC,OAAO,EAAC,EACb,KAAK,EAAE,KAAK,GACb,GAAG,IAAI,CAAC,UAAU,CAAC;QACpB,MAAM,QAAQ,GAAG,CAAC,CAAA,EAAA,GAAA,OAAO,KAAP,IAAA,IAAA,OAAO,uBAAP,OAAO,CAAE,aAAa,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,CAAC,EAAE,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AACzE,QAAA,MAAM,CAAC,WAAW,GAAG,QAAQ,CAAC;AAE9B,QAAA,IAAI,IAAI,GAAG,MAAM,CAAC,QAAQ,EAAG,CAAC;QAC9B,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,IAAI,SAAS,GAAG,CAAC,CAAC;AAClB,QAAA,IAAI,YAAY,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAE5B,OAAO,YAAY,KAAK,SAAS,EAAE;AACjC,YAAA,IAAI,SAAS,KAAK,YAAY,CAAC,KAAK,EAAE;AACpC,gBAAA,IAAI,IAAsB,CAAC;AAC3B,gBAAA,IAAI,YAAY,CAAC,IAAI,KAAK,UAAU,EAAE;AACpC,oBAAA,IAAI,GAAG,IAAI,SAAS,CAClB,IAAmB,EACnB,IAAI,CAAC,WAAW,EAChB,IAAI,EACJ,OAAO,CACR,CAAC;AACH,iBAAA;AAAM,qBAAA,IAAI,YAAY,CAAC,IAAI,KAAK,cAAc,EAAE;oBAC/C,IAAI,GAAG,IAAI,YAAY,CAAC,IAAI,CAC1B,IAAmB,EACnB,YAAY,CAAC,IAAI,EACjB,YAAY,CAAC,OAAO,EACpB,IAAI,EACJ,OAAO,CACR,CAAC;AACH,iBAAA;AAAM,qBAAA,IAAI,YAAY,CAAC,IAAI,KAAK,YAAY,EAAE;oBAC7C,IAAI,GAAG,IAAI,WAAW,CAAC,IAAmB,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AAC5D,iBAAA;AACD,gBAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACxB,gBAAA,YAAY,GAAG,KAAK,CAAC,EAAE,SAAS,CAAC,CAAC;AACnC,aAAA;YACD,IAAI,SAAS,MAAK,YAAY,KAAZ,IAAA,IAAA,YAAY,uBAAZ,YAAY,CAAE,KAAK,CAAA,EAAE;AACrC,gBAAA,IAAI,GAAG,MAAM,CAAC,QAAQ,EAAG,CAAC;AAC1B,gBAAA,SAAS,EAAE,CAAC;AACb,aAAA;AACF,SAAA;;;;AAID,QAAA,MAAM,CAAC,WAAW,GAAG,CAAC,CAAC;AACvB,QAAA,OAAO,QAAQ,CAAC;KACjB;AAED,IAAA,OAAO,CAAC,MAAsB,EAAA;QAC5B,IAAI,CAAC,GAAG,CAAC,CAAC;AACV,QAAA,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;YAC/B,IAAI,IAAI,KAAK,SAAS,EAAE;AACtB,gBAAA,aAAa,KAAb,IAAA,IAAA,aAAa,KAAb,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,aAAa,CAAG;AACd,oBAAA,IAAI,EAAE,UAAU;oBAChB,IAAI;AACJ,oBAAA,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;AAChB,oBAAA,UAAU,EAAE,CAAC;oBACb,MAAM;AACN,oBAAA,gBAAgB,EAAE,IAAI;AACvB,iBAAA,CAAC,CAAC;AACH,gBAAA,IAAK,IAAsB,CAAC,OAAO,KAAK,SAAS,EAAE;oBAChD,IAAsB,CAAC,UAAU,CAAC,MAAM,EAAE,IAAqB,EAAE,CAAC,CAAC,CAAC;;;;oBAIrE,CAAC,IAAK,IAAsB,CAAC,OAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;AAClD,iBAAA;AAAM,qBAAA;oBACL,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5B,iBAAA;AACF,aAAA;AACD,YAAA,CAAC,EAAE,CAAC;AACL,SAAA;KACF;AACF,CAAA;AA6CD,MAAM,SAAS,CAAA;AA4Cb,IAAA,WAAA,CACE,SAAoB,EACpB,OAAyB,EACzB,MAAgD,EAChD,OAAkC,EAAA;;QA/C3B,IAAI,CAAA,IAAA,GAAG,UAAU,CAAC;QAE3B,IAAgB,CAAA,gBAAA,GAAY,OAAO,CAAC;;;;QA+BpC,IAAwB,CAAA,wBAAA,GAAyB,SAAS,CAAC;AAgBzD,QAAA,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC;AAC7B,QAAA,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC;AACzB,QAAA,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC;AACvB,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;;;;AAIvB,QAAA,IAAI,CAAC,aAAa,GAAG,CAAA,EAAA,GAAA,OAAO,KAAA,IAAA,IAAP,OAAO,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAP,OAAO,CAAE,WAAW,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,IAAI,CAAC;AAClD,QAAiC;;AAE/B,YAAA,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;AACjC,SAAA;KACF;;AAtCD,IAAA,IAAI,aAAa,GAAA;;;;;QAIf,OAAO,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,QAAQ,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,aAAa,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,IAAI,CAAC,aAAa,CAAC;KAC3D;AAmCD;;;;;;;;;;;;;;;;;AAiBG;AACH,IAAA,IAAI,UAAU,GAAA;QACZ,IAAI,UAAU,GAAS,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,UAAW,CAAC;AAC1D,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC7B,IACE,MAAM,KAAK,SAAS;YACpB,CAAA,UAAU,KAAV,IAAA,IAAA,UAAU,KAAV,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,UAAU,CAAE,QAAQ,MAAK,EAAE,+BAC3B;;;;AAIA,YAAA,UAAU,GAAI,MAAuC,CAAC,UAAU,CAAC;AAClE,SAAA;AACD,QAAA,OAAO,UAAU,CAAC;KACnB;AAED;;;AAGG;AACH,IAAA,IAAI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,WAAW,CAAC;KACzB;AAED;;;AAGG;AACH,IAAA,IAAI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;AAED,IAAA,UAAU,CAAC,KAAc,EAAE,eAAA,GAAmC,IAAI,EAAA;;AAChE,QAAA,IAAgB,IAAI,CAAC,UAAU,KAAK,IAAI,EAAE;AACxC,YAAA,MAAM,IAAI,KAAK,CACb,CAAA,wUAAA,CAA0U,CAC3U,CAAC;AACH,SAAA;QACD,KAAK,GAAG,gBAAgB,CAAC,IAAI,EAAE,KAAK,EAAE,eAAe,CAAC,CAAC;AACvD,QAAA,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE;;;;YAItB,IAAI,KAAK,KAAK,OAAO,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK,EAAE,EAAE;AACtD,gBAAA,IAAI,IAAI,CAAC,gBAAgB,KAAK,OAAO,EAAE;AACrC,oBAAA,aAAa,KAAb,IAAA,IAAA,aAAa,KAAb,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,aAAa,CAAG;AACd,wBAAA,IAAI,EAAE,yBAAyB;wBAC/B,KAAK,EAAE,IAAI,CAAC,WAAW;wBACvB,GAAG,EAAE,IAAI,CAAC,SAAS;wBACnB,MAAM,EAAE,IAAI,CAAC,QAAQ;wBACrB,OAAO,EAAE,IAAI,CAAC,OAAO;AACtB,qBAAA,CAAC,CAAC;oBACH,IAAI,CAAC,OAAO,EAAE,CAAC;AAChB,iBAAA;AACD,gBAAA,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC;AACjC,aAAA;iBAAM,IAAI,KAAK,KAAK,IAAI,CAAC,gBAAgB,IAAI,KAAK,KAAK,QAAQ,EAAE;AAChE,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AACzB,aAAA;;AAEF,SAAA;AAAM,aAAA,IAAK,KAAwB,CAAC,YAAY,CAAC,KAAK,SAAS,EAAE;AAChE,YAAA,IAAI,CAAC,qBAAqB,CAAC,KAAuB,CAAC,CAAC;AACrD,SAAA;AAAM,aAAA,IAAK,KAAc,CAAC,QAAQ,KAAK,SAAS,EAAE;YACjD,IAAgB,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAI,MAAK,KAAK,EAAE;gBAC5C,IAAI,CAAC,WAAW,CACd,CAA0D,wDAAA,CAAA;AACxD,oBAAA,CAAA,mDAAA,CAAqD,CACxD,CAAC;AACF,gBAAA,OAAO,CAAC,IAAI,CACV,CAAA,qCAAA,CAAuC,EACvC,KAAK,EACL,CAAkE,gEAAA,CAAA,EAClE,4DAA4D,EAC5D,CAAA,gEAAA,CAAkE,EAClE,CAAA,yCAAA,CAA2C,CAC5C,CAAC;gBACF,OAAO;AACR,aAAA;AACD,YAAA,IAAI,CAAC,WAAW,CAAC,KAAa,CAAC,CAAC;AACjC,SAAA;AAAM,aAAA,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE;AAC5B,YAAA,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;AAC7B,SAAA;AAAM,aAAA;;AAEL,YAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AACzB,SAAA;KACF;AAEO,IAAA,OAAO,CAAiB,IAAO,EAAA;QACrC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,UAAW,CAAC,CAAC,YAAY,CAC1D,IAAI,EACJ,IAAI,CAAC,SAAS,CACf,CAAC;KACH;AAEO,IAAA,WAAW,CAAC,KAAW,EAAA;;AAC7B,QAAA,IAAI,IAAI,CAAC,gBAAgB,KAAK,KAAK,EAAE;YACnC,IAAI,CAAC,OAAO,EAAE,CAAC;AACf,YAAA,IAEE,wBAAwB,KAAK,aAAa,EAC1C;gBACA,MAAM,cAAc,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,WAAW,CAAC,UAAU,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,QAAQ,CAAC;AAC7D,gBAAA,IAAI,cAAc,KAAK,OAAO,IAAI,cAAc,KAAK,QAAQ,EAAE;oBAC7D,IAAI,OAAO,GAAG,WAAW,CAAC;AAC1B,oBAAc;wBACZ,IAAI,cAAc,KAAK,OAAO,EAAE;4BAC9B,OAAO;gCACL,CAAmD,iDAAA,CAAA;oCACnD,CAA0D,wDAAA,CAAA;oCAC1D,CAAiC,+BAAA,CAAA;oCACjC,CAA6C,2CAAA,CAAA;oCAC7C,CAAsD,oDAAA,CAAA;oCACtD,CAA2C,yCAAA,CAAA;AAC3C,oCAAA,CAAA,gDAAA,CAAkD,CAAC;AACtD,yBAAA;AAAM,6BAAA;4BACL,OAAO;gCACL,CAAoD,kDAAA,CAAA;oCACpD,CAAuD,qDAAA,CAAA;AACvD,oCAAA,CAAA,eAAA,CAAiB,CAAC;AACrB,yBAAA;AACF,qBAAA;AACD,oBAAA,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;AAC1B,iBAAA;AACF,aAAA;AACD,YAAA,aAAa,KAAb,IAAA,IAAA,aAAa,KAAb,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,aAAa,CAAG;AACd,gBAAA,IAAI,EAAE,aAAa;gBACnB,KAAK,EAAE,IAAI,CAAC,WAAW;gBACvB,MAAM,EAAE,IAAI,CAAC,QAAQ;AACrB,gBAAA,KAAK,EAAE,KAAK;gBACZ,OAAO,EAAE,IAAI,CAAC,OAAO;AACtB,aAAA,CAAC,CAAC;YACH,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC7C,SAAA;KACF;AAEO,IAAA,WAAW,CAAC,KAAc,EAAA;;;;AAIhC,QAAA,IACE,IAAI,CAAC,gBAAgB,KAAK,OAAO;AACjC,YAAA,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAClC;YACA,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,WAAmB,CAAC;AACxD,YAAiC;AAC/B,gBAAA,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,EAAE;oBACrC,IAAI,CAAC,cAAc,GAAG,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;AACjE,iBAAA;AACD,gBAAA,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AACpC,aAAA;AACD,YAAA,aAAa,KAAb,IAAA,IAAA,aAAa,KAAb,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,aAAa,CAAG;AACd,gBAAA,IAAI,EAAE,aAAa;gBACnB,IAAI;gBACJ,KAAK;gBACL,OAAO,EAAE,IAAI,CAAC,OAAO;AACtB,aAAA,CAAC,CAAC;AACF,YAAA,IAAa,CAAC,IAAI,GAAG,KAAe,CAAC;AACvC,SAAA;AAAM,aAAA;AACL,YAAiC;gBAC/B,MAAM,QAAQ,GAAG,CAAC,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;AACtC,gBAAA,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;;;;;AAK3B,gBAAA,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,EAAE;oBACrC,IAAI,CAAC,cAAc,GAAG,eAAe,CAAC,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;AACrE,iBAAA;AACD,gBAAA,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AACnC,gBAAA,aAAa,KAAb,IAAA,IAAA,aAAa,KAAb,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,aAAa,CAAG;AACd,oBAAA,IAAI,EAAE,aAAa;AACnB,oBAAA,IAAI,EAAE,QAAQ;oBACd,KAAK;oBACL,OAAO,EAAE,IAAI,CAAC,OAAO;AACtB,iBAAA,CAAC,CAAC;AACH,gBAAA,QAAQ,CAAC,IAAI,GAAG,KAAe,CAAC;AACjC,aAQA;AACF,SAAA;AACD,QAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;KAC/B;AAEO,IAAA,qBAAqB,CAC3B,MAA+C,EAAA;;;QAG/C,MAAM,EAAC,MAAM,EAAE,CAAC,YAAY,GAAG,IAAI,EAAC,GAAG,MAAM,CAAC;;;;;AAK9C,QAAA,MAAM,QAAQ,GACZ,OAAO,IAAI,KAAK,QAAQ;AACtB,cAAE,IAAI,CAAC,aAAa,CAAC,MAAwB,CAAC;AAC9C,eAAG,IAAI,CAAC,EAAE,KAAK,SAAS;iBACnB,IAAI,CAAC,EAAE,GAAG,QAAQ,CAAC,aAAa,CAC/B,uBAAuB,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAC1C,IAAI,CAAC,OAAO,CACb,CAAC;AACJ,gBAAA,IAAI,CAAC,CAAC;QAEZ,IAAI,CAAA,MAAC,IAAI,CAAC,gBAAqC,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,UAAU,MAAK,QAAQ,EAAE;AACxE,YAAA,aAAa,KAAb,IAAA,IAAA,aAAa,KAAb,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,aAAa,CAAG;AACd,gBAAA,IAAI,EAAE,mBAAmB;gBACzB,QAAQ;gBACR,QAAQ,EAAE,IAAI,CAAC,gBAAoC;AACnD,gBAAA,KAAK,EAAG,IAAI,CAAC,gBAAqC,CAAC,OAAO;gBAC1D,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,MAAM;AACP,aAAA,CAAC,CAAC;AACF,YAAA,IAAI,CAAC,gBAAqC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAC7D,SAAA;AAAM,aAAA;YACL,MAAM,QAAQ,GAAG,IAAI,gBAAgB,CAAC,QAAoB,EAAE,IAAI,CAAC,CAAC;YAClE,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC/C,YAAA,aAAa,KAAb,IAAA,IAAA,aAAa,KAAb,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,aAAa,CAAG;AACd,gBAAA,IAAI,EAAE,uBAAuB;gBAC7B,QAAQ;gBACR,QAAQ;gBACR,KAAK,EAAE,QAAQ,CAAC,OAAO;gBACvB,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,QAAQ;gBACR,MAAM;AACP,aAAA,CAAC,CAAC;AACH,YAAA,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACzB,YAAA,aAAa,KAAb,IAAA,IAAA,aAAa,KAAb,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,aAAa,CAAG;AACd,gBAAA,IAAI,EAAE,mCAAmC;gBACzC,QAAQ;gBACR,QAAQ;gBACR,KAAK,EAAE,QAAQ,CAAC,OAAO;gBACvB,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,QAAQ;gBACR,MAAM;AACP,aAAA,CAAC,CAAC;AACH,YAAA,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC3B,YAAA,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC;AAClC,SAAA;KACF;;;AAID,IAAA,aAAa,CAAC,MAAsB,EAAA;QAClC,IAAI,QAAQ,GAAG,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACjD,IAAI,QAAQ,KAAK,SAAS,EAAE;AAC1B,YAAA,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,GAAG,QAAQ,GAAG,IAAI,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;AACtE,SAAA;AACD,QAAA,OAAO,QAAQ,CAAC;KACjB;AAEO,IAAA,eAAe,CAAC,KAAwB,EAAA;;;;;;;;;;AAW9C,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE;AACnC,YAAA,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;YAC3B,IAAI,CAAC,OAAO,EAAE,CAAC;AAChB,SAAA;;;AAID,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,gBAA+B,CAAC;QACvD,IAAI,SAAS,GAAG,CAAC,CAAC;AAClB,QAAA,IAAI,QAA+B,CAAC;AAEpC,QAAA,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;AACxB,YAAA,IAAI,SAAS,KAAK,SAAS,CAAC,MAAM,EAAE;;;;;AAKlC,gBAAA,SAAS,CAAC,IAAI,EACX,QAAQ,GAAG,IAAI,SAAS,CACvB,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,EAC5B,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,EAC5B,IAAI,EACJ,IAAI,CAAC,OAAO,CACb,EACF,CAAC;AACH,aAAA;AAAM,iBAAA;;AAEL,gBAAA,QAAQ,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC;AACjC,aAAA;AACD,YAAA,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AAC1B,YAAA,SAAS,EAAE,CAAC;AACb,SAAA;AAED,QAAA,IAAI,SAAS,GAAG,SAAS,CAAC,MAAM,EAAE;;AAEhC,YAAA,IAAI,CAAC,OAAO,CACV,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAU,CAAC,CAAC,WAAW,EACjD,SAAS,CACV,CAAC;;AAEF,YAAA,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC;AAC9B,SAAA;KACF;AAED;;;;;;;;;;AAUG;IACH,OAAO,CACL,KAA0B,GAAA,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,WAAW,EAC5D,IAAa,EAAA;;QAEb,CAAA,EAAA,GAAA,IAAI,CAAC,yBAAyB,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,IAAA,EAAA,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AACpD,QAAA,OAAO,KAAK,IAAI,KAAK,KAAK,IAAI,CAAC,SAAS,EAAE;YACxC,MAAM,CAAC,GAAG,IAAI,CAAC,KAAM,CAAC,CAAC,WAAW,CAAC;AAClC,YAAA,IAAI,CAAC,KAAM,CAAa,CAAC,MAAM,EAAE,CAAC;YACnC,KAAK,GAAG,CAAC,CAAC;AACX,SAAA;KACF;AACD;;;;;;AAMG;AACH,IAAA,YAAY,CAAC,WAAoB,EAAA;;AAC/B,QAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE;AAC/B,YAAA,IAAI,CAAC,aAAa,GAAG,WAAW,CAAC;AACjC,YAAA,CAAA,EAAA,GAAA,IAAI,CAAC,yBAAyB,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,IAAA,EAAA,WAAW,CAAC,CAAC;AAC/C,SAAA;AAAM,aAAc;YACnB,MAAM,IAAI,KAAK,CACb,8CAA8C;AAC5C,gBAAA,kCAAkC,CACrC,CAAC;AACH,SAAA;KACF;AACF,CAAA;AA0BD,MAAM,aAAa,CAAA;IAoCjB,WACE,CAAA,OAAoB,EACpB,IAAY,EACZ,OAA8B,EAC9B,MAAsB,EACtB,OAAkC,EAAA;QAxC3B,IAAI,CAAA,IAAA,GAAG,cAIK,CAAC;;QAYtB,IAAgB,CAAA,gBAAA,GAA6B,OAAO,CAAC;;QAMrD,IAAwB,CAAA,wBAAA,GAAyB,SAAS,CAAC;AAoBzD,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AACvB,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACjB,QAAA,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC;AACvB,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AACvB,QAAA,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;AAChE,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,MAAM,EAAE,CAAC,CAAC;AACzE,YAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AACxB,SAAA;AAAM,aAAA;AACL,YAAA,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC;AACjC,SAAA;AACD,QAAiC;AAC/B,YAAA,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;AAC7B,SAAA;KACF;AA7BD,IAAA,IAAI,OAAO,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;KAC7B;;AAGD,IAAA,IAAI,aAAa,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC;KACpC;AAwBD;;;;;;;;;;;;;;;;;;;;;AAqBG;IACH,UAAU,CACR,KAA+B,EAC/B,eAAA,GAAmC,IAAI,EACvC,UAAmB,EACnB,QAAkB,EAAA;AAElB,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;;QAG7B,IAAI,MAAM,GAAG,KAAK,CAAC;QAEnB,IAAI,OAAO,KAAK,SAAS,EAAE;;YAEzB,KAAK,GAAG,gBAAgB,CAAC,IAAI,EAAE,KAAK,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC;YAC1D,MAAM;gBACJ,CAAC,WAAW,CAAC,KAAK,CAAC;qBAClB,KAAK,KAAK,IAAI,CAAC,gBAAgB,IAAI,KAAK,KAAK,QAAQ,CAAC,CAAC;AAC1D,YAAA,IAAI,MAAM,EAAE;AACV,gBAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;AAC/B,aAAA;AACF,SAAA;AAAM,aAAA;;YAEL,MAAM,MAAM,GAAG,KAAuB,CAAC;AACvC,YAAA,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAEnB,IAAI,CAAC,EAAE,CAAC,CAAC;AACT,YAAA,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AACvC,gBAAA,CAAC,GAAG,gBAAgB,CAAC,IAAI,EAAE,MAAM,CAAC,UAAW,GAAG,CAAC,CAAC,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC;gBAExE,IAAI,CAAC,KAAK,QAAQ,EAAE;;AAElB,oBAAA,CAAC,GAAI,IAAI,CAAC,gBAAmC,CAAC,CAAC,CAAC,CAAC;AAClD,iBAAA;AACD,gBAAA,MAAM,KAAN,MAAM,GACJ,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAM,IAAI,CAAC,gBAAmC,CAAC,CAAC,CAAC,CAAC,CAAA;gBACxE,IAAI,CAAC,KAAK,OAAO,EAAE;oBACjB,KAAK,GAAG,OAAO,CAAC;AACjB,iBAAA;qBAAM,IAAI,KAAK,KAAK,OAAO,EAAE;AAC5B,oBAAA,KAAK,IAAI,CAAC,CAAC,KAAD,IAAA,IAAA,CAAC,cAAD,CAAC,GAAI,EAAE,IAAI,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACrC,iBAAA;;;AAGA,gBAAA,IAAI,CAAC,gBAAmC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAClD,aAAA;AACF,SAAA;AACD,QAAA,IAAI,MAAM,IAAI,CAAC,QAAQ,EAAE;AACvB,YAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AAC1B,SAAA;KACF;;AAGD,IAAA,YAAY,CAAC,KAAc,EAAA;QACzB,IAAI,KAAK,KAAK,OAAO,EAAE;AACpB,YAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAa,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC5D,SAAA;AAAM,aAAA;AACL,YAAiC;AAC/B,gBAAA,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,EAAE;AACjC,oBAAA,IAAI,CAAC,UAAU,GAAG,wBAAwB,CACxC,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,IAAI,EACT,WAAW,CACZ,CAAC;AACH,iBAAA;AACD,gBAAA,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,KAAL,IAAA,IAAA,KAAK,KAAL,KAAA,CAAA,GAAA,KAAK,GAAI,EAAE,CAAC,CAAC;AACtC,aAAA;AACD,YAAA,aAAa,KAAb,IAAA,IAAA,aAAa,KAAb,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,aAAa,CAAG;AACd,gBAAA,IAAI,EAAE,kBAAkB;gBACxB,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,KAAK;gBACL,OAAO,EAAE,IAAI,CAAC,OAAO;AACtB,aAAA,CAAC,CAAC;YACF,IAAI,CAAC,IAAI,CAAC,OAAO,CAAa,CAAC,YAAY,CAC1C,IAAI,CAAC,IAAI,GACR,KAAK,aAAL,KAAK,KAAA,KAAA,CAAA,GAAL,KAAK,GAAI,EAAE,EACb,CAAC;AACH,SAAA;KACF;AACF,CAAA;AAGD,MAAM,YAAa,SAAQ,aAAa,CAAA;AAAxC,IAAA,WAAA,GAAA;;QACoB,IAAI,CAAA,IAAA,GAAG,aAAa,CAAC;KAwBxC;;AArBU,IAAA,YAAY,CAAC,KAAc,EAAA;AAClC,QAAiC;AAC/B,YAAA,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,EAAE;AACjC,gBAAA,IAAI,CAAC,UAAU,GAAG,wBAAwB,CACxC,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,IAAI,EACT,UAAU,CACX,CAAC;AACH,aAAA;AACD,YAAA,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;AAChC,SAAA;AACD,QAAA,aAAa,KAAb,IAAA,IAAA,aAAa,KAAb,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,aAAa,CAAG;AACd,YAAA,IAAI,EAAE,iBAAiB;YACvB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,KAAK;YACL,OAAO,EAAE,IAAI,CAAC,OAAO;AACtB,SAAA,CAAC,CAAC;;AAEF,QAAA,IAAI,CAAC,OAAe,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,KAAK,KAAK,OAAO,GAAG,SAAS,GAAG,KAAK,CAAC;KAC1E;AACF,CAAA;AAED;AACA;AACA;AACA;AACA,MAAM,8BAA8B,GAAG,YAAY;MAC9C,YAAY,CAAC,WAA6B;MAC3C,EAAE,CAAC;AAGP,MAAM,oBAAqB,SAAQ,aAAa,CAAA;AAAhD,IAAA,WAAA,GAAA;;QACoB,IAAI,CAAA,IAAA,GAAG,sBAAsB,CAAC;KAoBjD;;AAjBU,IAAA,YAAY,CAAC,KAAc,EAAA;AAClC,QAAA,aAAa,KAAb,IAAA,IAAA,aAAa,KAAb,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,aAAa,CAAG;AACd,YAAA,IAAI,EAAE,0BAA0B;YAChC,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,KAAK,EAAE,CAAC,EAAE,KAAK,IAAI,KAAK,KAAK,OAAO,CAAC;YACrC,OAAO,EAAE,IAAI,CAAC,OAAO;AACtB,SAAA,CAAC,CAAC;AACH,QAAA,IAAI,KAAK,IAAI,KAAK,KAAK,OAAO,EAAE;AAC7B,YAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAa,CAAC,YAAY,CAC1C,IAAI,CAAC,IAAI,EACT,8BAA8B,CAC/B,CAAC;AACH,SAAA;AAAM,aAAA;AACJ,YAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAa,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC5D,SAAA;KACF;AACF,CAAA;AAiBD,MAAM,SAAU,SAAQ,aAAa,CAAA;IAGnC,WACE,CAAA,OAAoB,EACpB,IAAY,EACZ,OAA8B,EAC9B,MAAsB,EACtB,OAAkC,EAAA;QAElC,KAAK,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;QAT/B,IAAI,CAAA,IAAA,GAAG,UAAU,CAAC;AAWlC,QAAA,IAAgB,IAAI,CAAC,OAAO,KAAK,SAAS,EAAE;YAC1C,MAAM,IAAI,KAAK,CACb,CAAA,KAAA,EAAQ,OAAO,CAAC,SAAS,CAAgB,aAAA,EAAA,IAAI,CAAuB,qBAAA,CAAA;gBAClE,kEAAkE;AAClE,gBAAA,yCAAyC,CAC5C,CAAC;AACH,SAAA;KACF;;;;AAKQ,IAAA,UAAU,CACjB,WAAoB,EACpB,eAAA,GAAmC,IAAI,EAAA;;QAEvC,WAAW;AACT,YAAA,CAAA,EAAA,GAAA,gBAAgB,CAAC,IAAI,EAAE,WAAW,EAAE,eAAe,EAAE,CAAC,CAAC,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,OAAO,CAAC;QACrE,IAAI,WAAW,KAAK,QAAQ,EAAE;YAC5B,OAAO;AACR,SAAA;AACD,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC;;;QAI1C,MAAM,oBAAoB,GACxB,CAAC,WAAW,KAAK,OAAO,IAAI,WAAW,KAAK,OAAO;AAClD,YAAA,WAAwC,CAAC,OAAO;AAC9C,gBAAA,WAAwC,CAAC,OAAO;AAClD,YAAA,WAAwC,CAAC,IAAI;AAC3C,gBAAA,WAAwC,CAAC,IAAI;AAC/C,YAAA,WAAwC,CAAC,OAAO;gBAC9C,WAAwC,CAAC,OAAO,CAAC;;;AAItD,QAAA,MAAM,iBAAiB,GACrB,WAAW,KAAK,OAAO;AACvB,aAAC,WAAW,KAAK,OAAO,IAAI,oBAAoB,CAAC,CAAC;AAEpD,QAAA,aAAa,KAAb,IAAA,IAAA,aAAa,KAAb,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,aAAa,CAAG;AACd,YAAA,IAAI,EAAE,uBAAuB;YAC7B,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,IAAI,EAAE,IAAI,CAAC,IAAI;AACf,YAAA,KAAK,EAAE,WAAW;YAClB,OAAO,EAAE,IAAI,CAAC,OAAO;AACrB,YAAA,cAAc,EAAE,oBAAoB;AACpC,YAAA,WAAW,EAAE,iBAAiB;YAC9B,WAAW;AACZ,SAAA,CAAC,CAAC;AACH,QAAA,IAAI,oBAAoB,EAAE;AACxB,YAAA,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAC9B,IAAI,CAAC,IAAI,EACT,IAAI,EACJ,WAAuC,CACxC,CAAC;AACH,SAAA;AACD,QAAA,IAAI,iBAAiB,EAAE;;;;AAIrB,YAAA,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAC3B,IAAI,CAAC,IAAI,EACT,IAAI,EACJ,WAAuC,CACxC,CAAC;AACH,SAAA;AACD,QAAA,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC;KACrC;AAED,IAAA,WAAW,CAAC,KAAY,EAAA;;AACtB,QAAA,IAAI,OAAO,IAAI,CAAC,gBAAgB,KAAK,UAAU,EAAE;AAC/C,YAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA,EAAA,GAAA,MAAA,IAAI,CAAC,OAAO,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAI,mCAAI,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AACvE,SAAA;AAAM,aAAA;AACJ,YAAA,IAAI,CAAC,gBAAwC,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AACnE,SAAA;KACF;AACF,CAAA;AAGD,MAAM,WAAW,CAAA;AAiBf,IAAA,WAAA,CACS,OAAgB,EACvB,MAAsB,EACtB,OAAkC,EAAA;QAF3B,IAAO,CAAA,OAAA,GAAP,OAAO,CAAS;QAjBhB,IAAI,CAAA,IAAA,GAAG,YAAY,CAAC;;QAY7B,IAAwB,CAAA,wBAAA,GAAyB,SAAS,CAAC;AASzD,QAAA,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC;AACvB,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;KACxB;;AAGD,IAAA,IAAI,aAAa,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC;KACpC;AAED,IAAA,UAAU,CAAC,KAAc,EAAA;AACvB,QAAA,aAAa,KAAb,IAAA,IAAA,aAAa,KAAb,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,aAAa,CAAG;AACd,YAAA,IAAI,EAAE,2BAA2B;YACjC,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,KAAK;YACL,OAAO,EAAE,IAAI,CAAC,OAAO;AACtB,SAAA,CAAC,CAAC;AACH,QAAA,gBAAgB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;KAC/B;AACF,CAAA;AAED;;;;;;;;;;;;;;;;;AAiBG;AACU,MAAA,IAAI,GAAG;;AAElB,IAAA,qBAAqB,EAAE,oBAAoB;AAC3C,IAAA,OAAO,EAAE,MAAM;AACf,IAAA,YAAY,EAAE,WAAW;AACzB,IAAA,YAAY,EAAE,WAAW;AACzB,IAAA,gBAAgB,EAAE,eAAe;;AAEjC,IAAA,iBAAiB,EAAE,gBAAgB;AACnC,IAAA,WAAW,EAAE,UAAU;AACvB,IAAA,iBAAiB,EAAE,gBAAgB;AACnC,IAAA,UAAU,EAAE,SAAS;AACrB,IAAA,cAAc,EAAE,aAAa;AAC7B,IAAA,qBAAqB,EAAE,oBAAoB;AAC3C,IAAA,UAAU,EAAE,SAAS;AACrB,IAAA,aAAa,EAAE,YAAY;AAC3B,IAAA,YAAY,EAAE,WAAW;EACzB;AAEF;AACA,MAAM,eAAe,GACjB,MAAM,CAAC,6BAA6B;AACtC,IAA+B,CAAC;AAClC,eAAe,KAAA,IAAA,IAAf,eAAe,KAAf,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,eAAe,CAAG,QAAQ,EAAE,SAAS,CAAC,CAAC;AAEvC;AACA;AACA,CAAA,CAAA,EAAA,GAAC,MAAM,CAAC,eAAe,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,IAAtB,MAAM,CAAC,eAAe,GAAK,EAAE,GAAE,IAAI,CAAC,OAAO,CAAC,CAAC;AAC9C,IAAgB,MAAM,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;IACjD,YAAa,CACX,mBAAmB,EACnB,CAAmC,iCAAA,CAAA;AACjC,QAAA,CAAA,6CAAA,CAA+C,CAClD,CAAC;AACH,CAAA;AAED;;;;;;;;;;;;;;;;;;;;;;;;AAwBG;AACU,MAAA,MAAM,GAAG,CACpB,KAAc,EACd,SAAyC,EACzC,OAAuB,KACX;;AACZ,IAAA,IAAgB,SAAS,IAAI,IAAI,EAAE;;;;;AAKjC,QAAA,MAAM,IAAI,SAAS,CAAC,2CAA2C,SAAS,CAAA,CAAE,CAAC,CAAC;AAC7E,KAAA;AACD,IAAA,MAAM,QAAQ,GAAc,gBAAgB,EAAE,CAAI,CAAC;AACnD,IAAA,MAAM,aAAa,GAAG,CAAA,EAAA,GAAA,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,YAAY,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,SAAS,CAAC;;;AAGzD,IAAA,IAAI,IAAI,GAAe,aAAqB,CAAC,YAAY,CAAC,CAAC;AAC3D,IAAA,aAAa,KAAb,IAAA,IAAA,aAAa,KAAb,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,aAAa,CAAG;AACd,QAAA,IAAI,EAAE,cAAc;AACpB,QAAA,EAAE,EAAE,QAAQ;QACZ,KAAK;QACL,SAAS;QACT,OAAO;QACP,IAAI;AACL,KAAA,CAAC,CAAC;IACH,IAAI,IAAI,KAAK,SAAS,EAAE;AACtB,QAAA,MAAM,OAAO,GAAG,CAAA,EAAA,GAAA,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,YAAY,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,IAAI,CAAC;;;AAG7C,QAAA,aAAqB,CAAC,YAAY,CAAC,GAAG,IAAI,GAAG,IAAI,SAAS,CACzD,SAAS,CAAC,YAAY,CAAC,YAAY,EAAE,EAAE,OAAO,CAAC,EAC/C,OAAO,EACP,SAAS,EACT,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,KAAA,CAAA,GAAA,OAAO,GAAI,EAAE,CACd,CAAC;AACH,KAAA;AACD,IAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;AACvB,IAAA,aAAa,KAAb,IAAA,IAAA,aAAa,KAAb,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,aAAa,CAAG;AACd,QAAA,IAAI,EAAE,YAAY;AAClB,QAAA,EAAE,EAAE,QAAQ;QACZ,KAAK;QACL,SAAS;QACT,OAAO;QACP,IAAI;AACL,KAAA,CAAC,CAAC;AACH,IAAA,OAAO,IAAgB,CAAC;AAC1B,EAAE;AAE+B;AAC/B,IAAA,MAAM,CAAC,YAAY,GAAG,YAAY,CAAC;AACnC,IAAA,MAAM,CAAC,eAAe,GAAG,eAAe,CAAC;AACzC,IAAc;AACZ,QAAA,MAAM,CAAC,6CAA6C;AAClD,YAAA,6CAA6C,CAAC;AACjD,KAAA;AACF;;;;"}