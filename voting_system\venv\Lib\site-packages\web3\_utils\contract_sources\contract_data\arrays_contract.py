"""
Generated by `compile_contracts.py` script.
Compiled with Solidity v0.8.19.
"""

# source: web3/_utils/contract_sources/ArraysContract.sol:ArraysContract
ARRAYS_CONTRACT_BYTECODE = "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"  # noqa: E501
ARRAYS_CONTRACT_RUNTIME = "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"  # noqa: E501
ARRAYS_CONTRACT_ABI = [
    {
        "inputs": [
            {"internalType": "bytes32[]", "name": "_bytes32Value", "type": "bytes32[]"},
            {"internalType": "bytes1[]", "name": "_byteValue", "type": "bytes1[]"},
        ],
        "stateMutability": "nonpayable",
        "type": "constructor",
    },
    {
        "inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "name": "byteConstValue",
        "outputs": [{"internalType": "bytes1", "name": "", "type": "bytes1"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "name": "byteValue",
        "outputs": [{"internalType": "bytes1", "name": "", "type": "bytes1"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "name": "bytes32ConstValue",
        "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "name": "bytes32Value",
        "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "getByteConstValue",
        "outputs": [{"internalType": "bytes1[]", "name": "", "type": "bytes1[]"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "getByteValue",
        "outputs": [{"internalType": "bytes1[]", "name": "", "type": "bytes1[]"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "getBytes32ConstValue",
        "outputs": [{"internalType": "bytes32[]", "name": "", "type": "bytes32[]"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "getBytes32Value",
        "outputs": [{"internalType": "bytes32[]", "name": "", "type": "bytes32[]"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [
            {"internalType": "bytes1[]", "name": "_byteValue", "type": "bytes1[]"}
        ],
        "name": "setByteValue",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [
            {"internalType": "bytes32[]", "name": "_bytes32Value", "type": "bytes32[]"}
        ],
        "name": "setBytes32Value",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
]
ARRAYS_CONTRACT_DATA = {
    "bytecode": ARRAYS_CONTRACT_BYTECODE,
    "bytecode_runtime": ARRAYS_CONTRACT_RUNTIME,
    "abi": ARRAYS_CONTRACT_ABI,
}
