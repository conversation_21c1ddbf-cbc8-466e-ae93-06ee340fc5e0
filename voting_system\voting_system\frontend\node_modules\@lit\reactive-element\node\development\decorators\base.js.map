{"version": 3, "file": "base.js", "sources": ["../../../src/decorators/base.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {ReactiveElement} from '../reactive-element.js';\n\n/**\n * Generates a public interface type that removes private and protected fields.\n * This allows accepting otherwise compatible versions of the type (e.g. from\n * multiple copies of the same package in `node_modules`).\n */\nexport type Interface<T> = {\n  [K in keyof T]: T[K];\n};\n\nexport type Constructor<T> = {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  new (...args: any[]): T;\n};\n\n// From the TC39 Decorators proposal\nexport interface ClassDescriptor {\n  kind: 'class';\n  elements: ClassElement[];\n  finisher?: <T>(clazz: Constructor<T>) => void | Constructor<T>;\n}\n\n// From the TC39 Decorators proposal\nexport interface ClassElement {\n  kind: 'field' | 'method';\n  key: PropertyKey;\n  placement: 'static' | 'prototype' | 'own';\n  initializer?: Function;\n  extras?: ClassElement[];\n  finisher?: <T>(clazz: Constructor<T>) => void | Constructor<T>;\n  descriptor?: PropertyDescriptor;\n}\n\nexport const legacyPrototypeMethod = (\n  descriptor: PropertyDescriptor,\n  proto: Object,\n  name: PropertyKey\n) => {\n  Object.defineProperty(proto, name, descriptor);\n};\n\nexport const standardPrototypeMethod = (\n  descriptor: PropertyDescriptor,\n  element: ClassElement\n) => ({\n  kind: 'method',\n  placement: 'prototype',\n  key: element.key,\n  descriptor,\n});\n\n/**\n * Helper for decorating a property that is compatible with both TypeScript\n * and Babel decorators. The optional `finisher` can be used to perform work on\n * the class. The optional `descriptor` should return a PropertyDescriptor\n * to install for the given property.\n *\n * @param finisher {function} Optional finisher method; receives the element\n * constructor and property key as arguments and has no return value.\n * @param descriptor {function} Optional descriptor method; receives the\n * property key as an argument and returns a property descriptor to define for\n * the given property.\n * @returns {ClassElement|void}\n */\nexport const decorateProperty =\n  ({\n    finisher,\n    descriptor,\n  }: {\n    finisher?:\n      | ((ctor: typeof ReactiveElement, property: PropertyKey) => void)\n      | null;\n    descriptor?: (property: PropertyKey) => PropertyDescriptor;\n  }) =>\n  (\n    protoOrDescriptor: Interface<ReactiveElement> | ClassElement,\n    name?: PropertyKey\n    // Note TypeScript requires the return type to be `void|any`\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  ): void | any => {\n    // TypeScript / Babel legacy mode\n    if (name !== undefined) {\n      const ctor = (protoOrDescriptor as ReactiveElement)\n        .constructor as typeof ReactiveElement;\n      if (descriptor !== undefined) {\n        Object.defineProperty(protoOrDescriptor, name, descriptor(name));\n      }\n      finisher?.(ctor, name!);\n      // Babel standard mode\n    } else {\n      // Note, the @property decorator saves `key` as `originalKey`\n      // so try to use it here.\n      const key =\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        (protoOrDescriptor as any).originalKey ??\n        (protoOrDescriptor as ClassElement).key;\n      const info: ClassElement =\n        descriptor != undefined\n          ? {\n              kind: 'method',\n              placement: 'prototype',\n              key,\n              descriptor: descriptor((protoOrDescriptor as ClassElement).key),\n            }\n          : {...(protoOrDescriptor as ClassElement), key};\n      if (finisher != undefined) {\n        info.finisher = function <ReactiveElement>(\n          ctor: Constructor<ReactiveElement>\n        ) {\n          finisher(ctor as unknown as typeof ReactiveElement, key);\n        };\n      }\n      return info;\n    }\n  };\n"], "names": [], "mappings": "AAAA;;;;AAIG;AAoCU,MAAA,qBAAqB,GAAG,CACnC,UAA8B,EAC9B,KAAa,EACb,IAAiB,KACf;IACF,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;AACjD,EAAE;AAEW,MAAA,uBAAuB,GAAG,CACrC,UAA8B,EAC9B,OAAqB,MACjB;AACJ,IAAA,IAAI,EAAE,QAAQ;AACd,IAAA,SAAS,EAAE,WAAW;IACtB,GAAG,EAAE,OAAO,CAAC,GAAG;IAChB,UAAU;AACX,CAAA,EAAE;AAEH;;;;;;;;;;;;AAYG;AACU,MAAA,gBAAgB,GAC3B,CAAC,EACC,QAAQ,EACR,UAAU,GAMX,KACD,CACE,iBAA4D,EAC5D,IAAkB;AAClB;AACA;KACc;;;IAEd,IAAI,IAAI,KAAK,SAAS,EAAE;QACtB,MAAM,IAAI,GAAI,iBAAqC;AAChD,aAAA,WAAqC,CAAC;QACzC,IAAI,UAAU,KAAK,SAAS,EAAE;AAC5B,YAAA,MAAM,CAAC,cAAc,CAAC,iBAAiB,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;AAClE,SAAA;QACD,QAAQ,KAAA,IAAA,IAAR,QAAQ,KAAR,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,QAAQ,CAAG,IAAI,EAAE,IAAK,CAAC,CAAC;;AAEzB,KAAA;AAAM,SAAA;;;AAGL,QAAA,MAAM,GAAG;;AAEP,QAAA,CAAA,EAAA,GAAC,iBAAyB,CAAC,WAAW,mCACrC,iBAAkC,CAAC,GAAG,CAAC;AAC1C,QAAA,MAAM,IAAI,GACR,UAAU,IAAI,SAAS;AACrB,cAAE;AACE,gBAAA,IAAI,EAAE,QAAQ;AACd,gBAAA,SAAS,EAAE,WAAW;gBACtB,GAAG;AACH,gBAAA,UAAU,EAAE,UAAU,CAAE,iBAAkC,CAAC,GAAG,CAAC;AAChE,aAAA;AACH,cAAE,EAAC,GAAI,iBAAkC,EAAE,GAAG,EAAC,CAAC;QACpD,IAAI,QAAQ,IAAI,SAAS,EAAE;AACzB,YAAA,IAAI,CAAC,QAAQ,GAAG,UACd,IAAkC,EAAA;AAElC,gBAAA,QAAQ,CAAC,IAAyC,EAAE,GAAG,CAAC,CAAC;AAC3D,aAAC,CAAC;AACH,SAAA;AACD,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;AACH;;;;"}