from django.core.management.base import BaseCommand
from voting.models import County, Constituency, PollingStation


class Command(BaseCommand):
    help = 'Populate test data for counties, constituencies, and polling stations'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Populating test data...'))

        # Get existing counties
        counties = County.objects.all()
        
        for county in counties:
            self.stdout.write(f'Processing county: {county.name}')
            
            # Create constituencies for each county if they don't exist
            existing_constituencies = Constituency.objects.filter(county=county).count()
            
            if existing_constituencies == 0:
                # Create sample constituencies for each county
                constituencies_data = [
                    f'{county.name} Central',
                    f'{county.name} East',
                    f'{county.name} West',
                ]
                
                for i, const_name in enumerate(constituencies_data, 1):
                    constituency, created = Constituency.objects.get_or_create(
                        name=const_name,
                        county=county,
                        defaults={'code': f'{county.code}C{i:02d}'}
                    )
                    if created:
                        self.stdout.write(f'  Created constituency: {const_name}')
                        
                        # Create polling stations for each constituency
                        polling_stations_data = [
                            f'{const_name} Primary School',
                            f'{const_name} Secondary School',
                            f'{const_name} Community Center',
                        ]
                        
                        for j, station_name in enumerate(polling_stations_data, 1):
                            polling_station, ps_created = PollingStation.objects.get_or_create(
                                name=station_name,
                                constituency=constituency,
                                defaults={
                                    'code': f'{constituency.code}P{j:02d}',
                                    'location': f'{station_name} Location'
                                }
                            )
                            if ps_created:
                                self.stdout.write(f'    Created polling station: {station_name}')
            else:
                self.stdout.write(f'  {county.name} already has {existing_constituencies} constituencies')
                
                # Check if constituencies have polling stations
                for constituency in Constituency.objects.filter(county=county):
                    existing_stations = PollingStation.objects.filter(constituency=constituency).count()
                    if existing_stations == 0:
                        # Create polling stations
                        polling_stations_data = [
                            f'{constituency.name} Primary School',
                            f'{constituency.name} Secondary School',
                            f'{constituency.name} Community Center',
                        ]
                        
                        for j, station_name in enumerate(polling_stations_data, 1):
                            polling_station, ps_created = PollingStation.objects.get_or_create(
                                name=station_name,
                                constituency=constituency,
                                defaults={
                                    'code': f'{constituency.code}P{j:02d}',
                                    'location': f'{station_name} Location'
                                }
                            )
                            if ps_created:
                                self.stdout.write(f'    Created polling station: {station_name}')

        # Print summary
        total_counties = County.objects.count()
        total_constituencies = Constituency.objects.count()
        total_polling_stations = PollingStation.objects.count()
        
        self.stdout.write(self.style.SUCCESS('\nData population completed!'))
        self.stdout.write(f'Total Counties: {total_counties}')
        self.stdout.write(f'Total Constituencies: {total_constituencies}')
        self.stdout.write(f'Total Polling Stations: {total_polling_stations}')
