export declare function hmacSha256Sign(key: Uint8<PERSON><PERSON><PERSON>, msg: Uint8Array): Promise<Uint8Array>;
export declare function hmacSha256Verify(key: Uint8Array, msg: Uint8Array, sig: Uint8Array): Promise<boolean>;
export declare function hmacSha512Sign(key: Uint8Array, msg: Uint8Array): Promise<Uint8Array>;
export declare function hmacSha512Verify(key: Uint8Array, msg: Uint8Array, sig: Uint8Array): Promise<boolean>;
//# sourceMappingURL=hmac.d.ts.map