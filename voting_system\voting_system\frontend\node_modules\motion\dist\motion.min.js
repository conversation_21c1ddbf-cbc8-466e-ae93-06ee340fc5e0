!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).Motion={})}(this,(function(t){"use strict";function e(t,e){-1===t.indexOf(e)&&t.push(e)}function n(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const i=(t,e,n)=>Math.min(Math.max(n,t),e),r={duration:.3,delay:0,endDelay:0,repeat:0,easing:"ease"},o=t=>"number"==typeof t,s=t=>Array.isArray(t)&&!o(t[0]);function a(t,e){return s(t)?t[((t,e,n)=>{const i=e-t;return((n-t)%i+i)%i+t})(0,t.length,e)]:t}const c=(t,e,n)=>-n*t+n*e+t,l=()=>{},u=t=>t,f=(t,e,n)=>e-t==0?1:(n-t)/(e-t);function h(t,e){const n=t[t.length-1];for(let i=1;i<=e;i++){const r=f(0,e,i);t.push(c(n,1,r))}}function d(t){const e=[0];return h(e,t-1),e}function g(t,e=d(t.length),n=u){const r=t.length,o=r-e.length;return o>0&&h(e,o),o=>{let s=0;for(;s<r-2&&!(o<e[s+1]);s++);let l=i(0,1,f(e[s],e[s+1],o));return l=a(n,s)(l),c(t[s],t[s+1],l)}}const p=t=>Array.isArray(t)&&o(t[0]),m=t=>"object"==typeof t&&Boolean(t.createAnimation),y=t=>"function"==typeof t,v=t=>"string"==typeof t,w=t=>1e3*t,b=t=>t/1e3;function E(t,e){return e?t*(1e3/e):0}const x=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t;function O(t,e,n,i){if(t===e&&n===i)return u;const r=e=>function(t,e,n,i,r){let o,s,a=0;do{s=e+(n-e)/2,o=x(s,i,r)-t,o>0?n=s:e=s}while(Math.abs(o)>1e-7&&++a<12);return s}(e,0,1,t,n);return t=>0===t||1===t?t:x(r(t),e,i)}const S={ease:O(.25,.1,.25,1),"ease-in":O(.42,0,1,1),"ease-in-out":O(.42,0,.58,1),"ease-out":O(0,0,.58,1)},M=/\((.*?)\)/;function T(t){if(y(t))return t;if(p(t))return O(...t);if(S[t])return S[t];if(t.startsWith("steps")){const e=M.exec(t);if(e){const t=e[1].split(",");return((t,e="end")=>n=>{const r=(n="end"===e?Math.min(n,.999):Math.max(n,.001))*t,o="end"===e?Math.floor(r):Math.ceil(r);return i(0,1,o/t)})(parseFloat(t[0]),t[1].trim())}}return u}class A{constructor(t,e=[0,1],{easing:n,duration:i=r.duration,delay:o=r.delay,endDelay:a=r.endDelay,repeat:c=r.repeat,offset:l,direction:f="normal"}={}){if(this.startTime=null,this.rate=1,this.t=0,this.cancelTimestamp=null,this.easing=u,this.duration=0,this.totalDuration=0,this.repeat=0,this.playState="idle",this.finished=new Promise(((t,e)=>{this.resolve=t,this.reject=e})),n=n||r.easing,m(n)){const t=n.createAnimation(e);n=t.easing,e=t.keyframes||e,i=t.duration||i}this.repeat=c,this.easing=s(n)?u:T(n),this.updateDuration(i);const h=g(e,l,s(n)?n.map(T):u);this.tick=e=>{var n;let i=0;i=void 0!==this.pauseTime?this.pauseTime:(e-this.startTime)*this.rate,this.t=i,i/=1e3,i=Math.max(i-o,0),"finished"===this.playState&&void 0===this.pauseTime&&(i=this.totalDuration);const r=i/this.duration;let s=Math.floor(r),c=r%1;!c&&r>=1&&(c=1),1===c&&s--;const l=s%2;("reverse"===f||"alternate"===f&&l||"alternate-reverse"===f&&!l)&&(c=1-c);const u=i>=this.totalDuration?1:Math.min(c,1),d=h(this.easing(u));t(d);void 0===this.pauseTime&&("finished"===this.playState||i>=this.totalDuration+a)?(this.playState="finished",null===(n=this.resolve)||void 0===n||n.call(this,d)):"idle"!==this.playState&&(this.frameRequestId=requestAnimationFrame(this.tick))},this.play()}play(){const t=performance.now();this.playState="running",void 0!==this.pauseTime?this.startTime=t-this.pauseTime:this.startTime||(this.startTime=t),this.cancelTimestamp=this.startTime,this.pauseTime=void 0,this.frameRequestId=requestAnimationFrame(this.tick)}pause(){this.playState="paused",this.pauseTime=this.t}finish(){this.playState="finished",this.tick(0)}stop(){var t;this.playState="idle",void 0!==this.frameRequestId&&cancelAnimationFrame(this.frameRequestId),null===(t=this.reject)||void 0===t||t.call(this,!1)}cancel(){this.stop(),this.tick(this.cancelTimestamp)}reverse(){this.rate*=-1}commitStyles(){}updateDuration(t){this.duration=t,this.totalDuration=t*(this.repeat+1)}get currentTime(){return this.t}set currentTime(t){void 0!==this.pauseTime||0===this.rate?this.pauseTime=t:this.startTime=performance.now()-t/this.rate}get playbackRate(){return this.rate}set playbackRate(t){this.rate=t}}class D{setAnimation(t){this.animation=t,null==t||t.finished.then((()=>this.clearAnimation())).catch((()=>{}))}clearAnimation(){this.animation=this.generator=void 0}}const L=new WeakMap;function k(t){return L.has(t)||L.set(t,{transforms:[],values:new Map}),L.get(t)}const W=["","X","Y","Z"],j={x:"translateX",y:"translateY",z:"translateZ"},R={syntax:"<angle>",initialValue:"0deg",toDefaultUnit:t=>t+"deg"},z={translate:{syntax:"<length-percentage>",initialValue:"0px",toDefaultUnit:t=>t+"px"},rotate:R,scale:{syntax:"<number>",initialValue:1,toDefaultUnit:u},skew:R},B=new Map,P=t=>`--motion-${t}`,V=["x","y","z"];["translate","scale","rotate","skew"].forEach((t=>{W.forEach((e=>{V.push(t+e),B.set(P(t+e),z[t])}))}));const $=(t,e)=>V.indexOf(t)-V.indexOf(e),q=new Set(V),C=t=>q.has(t),F=t=>t.sort($).reduce(H,"").trim(),H=(t,e)=>`${t} ${e}(var(${P(e)}))`,I=t=>t.startsWith("--"),U=new Set;const N=(t,e)=>document.createElement("div").animate(t,e),_={cssRegisterProperty:()=>"undefined"!=typeof CSS&&Object.hasOwnProperty.call(CSS,"registerProperty"),waapi:()=>Object.hasOwnProperty.call(Element.prototype,"animate"),partialKeyframes:()=>{try{N({opacity:[1]})}catch(t){return!1}return!0},finished:()=>Boolean(N({opacity:[0,1]},{duration:.001}).finished),linearEasing:()=>{try{N({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0}},G={},Z={};for(const t in _)Z[t]=()=>(void 0===G[t]&&(G[t]=_[t]()),G[t]);const K=(t,e)=>y(t)?Z.linearEasing()?`linear(${((t,e)=>{let n="";const i=Math.round(e/.015);for(let e=0;e<i;e++)n+=t(f(0,i-1,e))+", ";return n.substring(0,n.length-2)})(t,e)})`:r.easing:p(t)?X(t):t,X=([t,e,n,i])=>`cubic-bezier(${t}, ${e}, ${n}, ${i})`;const Y=t=>Array.isArray(t)?t:[t];function J(t){return j[t]&&(t=j[t]),C(t)?P(t):t}const Q={get:(t,e)=>{e=J(e);let n=I(e)?t.style.getPropertyValue(e):getComputedStyle(t)[e];if(!n&&0!==n){const t=B.get(e);t&&(n=t.initialValue)}return n},set:(t,e,n)=>{e=J(e),I(e)?t.style.setProperty(e,n):t.style[e]=n}};function tt(t,e=!0){if(t&&"finished"!==t.playState)try{t.stop?t.stop():(e&&t.commitStyles(),t.cancel())}catch(t){}}function et(t,e){var n;let i=(null==e?void 0:e.toDefaultUnit)||u;const r=t[t.length-1];if(v(r)){const t=(null===(n=r.match(/(-?[\d.]+)([a-z%]*)/))||void 0===n?void 0:n[2])||"";t&&(i=e=>e+t)}return i}function nt(t,n,i,a={},c){const u=window.__MOTION_DEV_TOOLS_RECORD,f=!1!==a.record&&u;let h,{duration:d=r.duration,delay:g=r.delay,endDelay:p=r.endDelay,repeat:v=r.repeat,easing:b=r.easing,persist:E=!1,direction:x,offset:O,allowWebkitAcceleration:S=!1}=a;const M=k(t),T=C(n);let A=Z.waapi();T&&((t,n)=>{j[n]&&(n=j[n]);const{transforms:i}=k(t);e(i,n),t.style.transform=F(i)})(t,n);const L=J(n),W=function(t,e){return t.has(e)||t.set(e,new D),t.get(e)}(M.values,L),R=B.get(L);return tt(W.animation,!(m(b)&&W.generator)&&!1!==a.record),()=>{const e=()=>{var e,n;return null!==(n=null!==(e=Q.get(t,L))&&void 0!==e?e:null==R?void 0:R.initialValue)&&void 0!==n?n:0};let r=function(t,e){for(let n=0;n<t.length;n++)null===t[n]&&(t[n]=n?t[n-1]:e());return t}(Y(i),e);const M=et(r,R);if(m(b)){const t=b.createAnimation(r,"opacity"!==n,e,L,W);b=t.easing,r=t.keyframes||r,d=t.duration||d}if(I(L)&&(Z.cssRegisterProperty()?function(t){if(!U.has(t)){U.add(t);try{const{syntax:e,initialValue:n}=B.has(t)?B.get(t):{};CSS.registerProperty({name:t,inherits:!1,syntax:e,initialValue:n})}catch(t){}}}(L):A=!1),T&&!Z.linearEasing()&&(y(b)||s(b)&&b.some(y))&&(A=!1),A){R&&(r=r.map((t=>o(t)?R.toDefaultUnit(t):t))),1!==r.length||Z.partialKeyframes()&&!f||r.unshift(e());const n={delay:w(g),duration:w(d),endDelay:w(p),easing:s(b)?void 0:K(b,d),direction:x,iterations:v+1,fill:"both"};h=t.animate({[L]:r,offset:O,easing:s(b)?b.map((t=>K(t,d))):void 0},n),h.finished||(h.finished=new Promise(((t,e)=>{h.onfinish=t,h.oncancel=e})));const i=r[r.length-1];h.finished.then((()=>{E||(Q.set(t,L,i),h.cancel())})).catch(l),S||(h.playbackRate=1.000001)}else if(c&&T)r=r.map((t=>"string"==typeof t?parseFloat(t):t)),1===r.length&&r.unshift(parseFloat(e())),h=new c((e=>{Q.set(t,L,M?M(e):e)}),r,Object.assign(Object.assign({},a),{duration:d,easing:b}));else{const e=r[r.length-1];Q.set(t,L,R&&o(e)?R.toDefaultUnit(e):e)}return f&&u(t,n,r,{duration:d,delay:g,easing:b,repeat:v,offset:O},"motion-one"),W.setAnimation(h),h}}const it=(t,e)=>t[e]?Object.assign(Object.assign({},t),t[e]):Object.assign({},t);function rt(t,e){var n;return"string"==typeof t?e?(null!==(n=e[t])&&void 0!==n||(e[t]=document.querySelectorAll(t)),t=e[t]):t=document.querySelectorAll(t):t instanceof Element&&(t=[t]),Array.from(t||[])}const ot=t=>t(),st=(t,e,n=r.duration)=>new Proxy({animations:t.map(ot).filter(Boolean),duration:n,options:e},at),at={get:(t,e)=>{const n=t.animations[0];switch(e){case"duration":return t.duration;case"currentTime":return b((null==n?void 0:n[e])||0);case"playbackRate":case"playState":return null==n?void 0:n[e];case"finished":return t.finished||(t.finished=Promise.all(t.animations.map(ct)).catch(l)),t.finished;case"stop":return()=>{t.animations.forEach((t=>tt(t)))};case"forEachNative":return e=>{t.animations.forEach((n=>e(n,t)))};default:return void 0===(null==n?void 0:n[e])?void 0:()=>t.animations.forEach((t=>t[e]()))}},set:(t,e,n)=>{switch(e){case"currentTime":n=w(n);case"currentTime":case"playbackRate":for(let i=0;i<t.animations.length;i++)t.animations[i][e]=n;return!0}return!1}},ct=t=>t.finished;function lt(t,e,n){return y(t)?t(e,n):t}function ut(t){return function(e,n,i={}){const r=(e=rt(e)).length,o=[];for(let s=0;s<r;s++){const a=e[s];for(const e in n){const c=it(i,e);c.delay=lt(c.delay,s,r);const l=nt(a,e,n[e],c,t);o.push(l)}}return st(o,i,i.duration)}}const ft=ut(A);function ht(t,e){var n={};for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&e.indexOf(i)<0&&(n[i]=t[i]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(i=Object.getOwnPropertySymbols(t);r<i.length;r++)e.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(t,i[r])&&(n[i[r]]=t[i[r]])}return n}function dt(t,e,n,i){var r;return o(e)?e:e.startsWith("-")||e.startsWith("+")?Math.max(0,t+parseFloat(e)):"<"===e?n:null!==(r=i.get(e))&&void 0!==r?r:t}function gt(t,e,i,r,o,s){!function(t,e,i){for(let r=0;r<t.length;r++){const o=t[r];o.at>e&&o.at<i&&(n(t,o),r--)}}(t,o,s);for(let n=0;n<e.length;n++)t.push({value:e[n],at:c(o,s,r[n]),easing:a(i,n)})}function pt(t,e){return t.at===e.at?null===t.value?1:-1:t.at-e.at}function mt(t,e){return!e.has(t)&&e.set(t,{}),e.get(t)}function yt(t,e){return e[t]||(e[t]=[]),e[t]}function vt(t,e,n){const i=Math.max(e-5,0);return E(n-t(i),e-i)}const wt=100,bt=10,Et=1;const xt=({stiffness:t=wt,damping:e=bt,mass:n=Et,from:i=0,to:r=1,velocity:o=0,restSpeed:s=2,restDistance:a=.5}={})=>{o=o?b(o):0;const c={done:!1,hasReachedTarget:!1,current:i,target:r},l=r-i,u=Math.sqrt(t/n)/1e3,f=((t=wt,e=bt,n=Et)=>e/(2*Math.sqrt(t*n)))(t,e,n);let h;if(f<1){const t=u*Math.sqrt(1-f*f);h=e=>r-Math.exp(-f*u*e)*((f*u*l-o)/t*Math.sin(t*e)+l*Math.cos(t*e))}else h=t=>r-Math.exp(-u*t)*(l+(u*l-o)*t);return t=>{c.current=h(t);const e=0===t?o:vt(h,t,c.current),n=Math.abs(e)<=s,l=Math.abs(r-c.current)<=a;var u,f,d;return c.done=n&&l,c.hasReachedTarget=(u=i,f=r,d=c.current,u<f&&d>=f||u>f&&d<=f),c}};function Ot(t){return o(t)&&!isNaN(t)}function St(t){return v(t)?parseFloat(t):t}function Mt(t){const e=new WeakMap;return(n={})=>{const i=new Map,r=(e=0,r=100,o=0,s=!1)=>{const a=`${e}-${r}-${o}-${s}`;return i.has(a)||i.set(a,t(Object.assign({from:e,to:r,velocity:o,restSpeed:s?.05:2,restDistance:s?.01:.5},n))),i.get(a)},o=(t,n)=>(e.has(t)||e.set(t,function(t,e=u){let n,i=10,r=t(0);const o=[e(r.current)];for(;!r.done&&i<1e4;)r=t(i),o.push(e(r.done?r.target:r.current)),void 0===n&&r.hasReachedTarget&&(n=i),i+=10;const s=i-10;return 1===o.length&&o.push(r.current),{keyframes:o,duration:s/1e3,overshootDuration:(null!=n?n:s)/1e3}}(t,n)),e.get(t));return{createAnimation:(t,e=!0,n,i,s)=>{let a,c,l,f=0,h=u;const d=t.length;if(e){h=et(t,i?B.get(J(i)):void 0);if(l=St(t[d-1]),d>1&&null!==t[0])c=St(t[0]);else{const t=null==s?void 0:s.generator;if(t){const{animation:e,generatorStartTime:n}=s,i=(null==e?void 0:e.startTime)||n||0,r=(null==e?void 0:e.currentTime)||performance.now()-i,o=t(r).current;c=o,f=vt((e=>t(e).current),r,o)}else n&&(c=St(n()))}}if(Ot(c)&&Ot(l)){const t=r(c,l,f,null==i?void 0:i.includes("scale"));a=Object.assign(Object.assign({},o(t,h)),{easing:"linear"}),s&&(s.generator=t,s.generatorStartTime=performance.now())}if(!a){a={easing:"ease",duration:o(r(0,100)).overshootDuration}}return a}}}}const Tt=Mt(xt),At=Mt((({from:t=0,velocity:e=0,power:n=.8,decay:i=.325,bounceDamping:r,bounceStiffness:o,changeTarget:s,min:a,max:c,restDistance:l=.5,restSpeed:u})=>{i=w(i);const f={hasReachedTarget:!1,done:!1,current:t,target:t},h=t=>void 0===a?c:void 0===c||Math.abs(a-t)<Math.abs(c-t)?a:c;let d=n*e;const g=t+d,p=void 0===s?g:s(g);f.target=p,p!==g&&(d=p-t);const m=t=>-d*Math.exp(-t/i),y=t=>p+m(t),v=t=>{const e=m(t),n=y(t);f.done=Math.abs(e)<=l,f.current=f.done?p:n};let b,E;const x=t=>{var e;(e=f.current,void 0!==a&&e<a||void 0!==c&&e>c)&&(b=t,E=xt({from:f.current,to:h(f.current),velocity:vt(y,t,f.current),damping:r,stiffness:o,restDistance:l,restSpeed:u}))};return x(0),t=>{let e=!1;return E||void 0!==b||(e=!0,v(t),x(t)),void 0!==b&&t>b?(f.hasReachedTarget=!0,E(t-b)):(f.hasReachedTarget=!1,!e&&v(t),f)}})),Dt={any:0,all:1};function Lt(t,e,{root:n,margin:i,amount:r="any"}={}){if("undefined"==typeof IntersectionObserver)return()=>{};const o=rt(t),s=new WeakMap,a=new IntersectionObserver((t=>{t.forEach((t=>{const n=s.get(t.target);if(t.isIntersecting!==Boolean(n))if(t.isIntersecting){const n=e(t);y(n)?s.set(t.target,n):a.unobserve(t.target)}else n&&(n(t),s.delete(t.target))}))}),{root:n,rootMargin:i,threshold:"number"==typeof r?r:Dt[r]});return o.forEach((t=>a.observe(t))),()=>a.disconnect()}const kt=new WeakMap;let Wt;function jt({target:t,contentRect:e,borderBoxSize:n}){var i;null===(i=kt.get(t))||void 0===i||i.forEach((i=>{i({target:t,contentSize:e,get size(){return function(t,e){if(e){const{inlineSize:t,blockSize:n}=e[0];return{width:t,height:n}}return t instanceof SVGElement&&"getBBox"in t?t.getBBox():{width:t.offsetWidth,height:t.offsetHeight}}(t,n)}})}))}function Rt(t){t.forEach(jt)}function zt(t,e){Wt||"undefined"!=typeof ResizeObserver&&(Wt=new ResizeObserver(Rt));const n=rt(t);return n.forEach((t=>{let n=kt.get(t);n||(n=new Set,kt.set(t,n)),n.add(e),null==Wt||Wt.observe(t)})),()=>{n.forEach((t=>{const n=kt.get(t);null==n||n.delete(e),(null==n?void 0:n.size)||null==Wt||Wt.unobserve(t)}))}}const Bt=new Set;let Pt;function Vt(t){return Bt.add(t),Pt||(Pt=()=>{const t={width:window.innerWidth,height:window.innerHeight},e={target:window,size:t,contentSize:t};Bt.forEach((t=>t(e)))},window.addEventListener("resize",Pt)),()=>{Bt.delete(t),!Bt.size&&Pt&&(Pt=void 0)}}function $t(t,e){return y(t)?Vt(t):zt(t,e)}const qt={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};function Ct(t,e,n,i){const r=n[e],{length:o,position:s}=qt[e],a=r.current,c=n.time;r.current=t["scroll"+s],r.scrollLength=t["scroll"+o]-t["client"+o],r.offset.length=0,r.offset[0]=0,r.offset[1]=r.scrollLength,r.progress=f(0,r.scrollLength,r.current);const l=i-c;r.velocity=l>50?0:E(r.current-a,l)}const Ft={Enter:[[0,1],[1,1]],Exit:[[0,0],[1,0]],Any:[[1,0],[0,1]],All:[[0,0],[1,1]]},Ht={start:0,center:.5,end:1};function It(t,e,n=0){let i=0;if(void 0!==Ht[t]&&(t=Ht[t]),v(t)){const e=parseFloat(t);t.endsWith("px")?i=e:t.endsWith("%")?t=e/100:t.endsWith("vw")?i=e/100*document.documentElement.clientWidth:t.endsWith("vh")?i=e/100*document.documentElement.clientHeight:t=e}return o(t)&&(i=e*t),n+i}const Ut=[0,0];function Nt(t,e,n,i){let r=Array.isArray(t)?t:Ut,s=0,a=0;return o(t)?r=[t,t]:v(t)&&(r=(t=t.trim()).includes(" ")?t.split(" "):[t,Ht[t]?t:"0"]),s=It(r[0],n,i),a=It(r[1],e),s-a}const _t={x:0,y:0};function Gt(t,e,n){let{offset:i=Ft.All}=n;const{target:r=t,axis:o="y"}=n,s="y"===o?"height":"width",a=r!==t?function(t,e){let n={x:0,y:0},i=t;for(;i&&i!==e;)if(i instanceof HTMLElement)n.x+=i.offsetLeft,n.y+=i.offsetTop,i=i.offsetParent;else if(i instanceof SVGGraphicsElement&&"getBBox"in i){const{top:t,left:e}=i.getBBox();for(n.x+=e,n.y+=t;i&&"svg"!==i.tagName;)i=i.parentNode}return n}(r,t):_t,c=r===t?{width:t.scrollWidth,height:t.scrollHeight}:{width:r.clientWidth,height:r.clientHeight},l={width:t.clientWidth,height:t.clientHeight};e[o].offset.length=0;let u=!e[o].interpolate;const f=i.length;for(let t=0;t<f;t++){const n=Nt(i[t],l[s],c[s],a[o]);u||n===e[o].interpolatorOffsets[t]||(u=!0),e[o].offset[t]=n}u&&(e[o].interpolate=g(d(f),e[o].offset),e[o].interpolatorOffsets=[...e[o].offset]),e[o].progress=e[o].interpolate(e[o].current)}function Zt(t,e,n,i={}){const r=i.axis||"y";return{measure:()=>function(t,e=t,n){if(n.x.targetOffset=0,n.y.targetOffset=0,e!==t){let i=e;for(;i&&i!=t;)n.x.targetOffset+=i.offsetLeft,n.y.targetOffset+=i.offsetTop,i=i.offsetParent}n.x.targetLength=e===t?e.scrollWidth:e.clientWidth,n.y.targetLength=e===t?e.scrollHeight:e.clientHeight,n.x.containerLength=t.clientWidth,n.y.containerLength=t.clientHeight}(t,i.target,n),update:e=>{!function(t,e,n){Ct(t,"x",e,n),Ct(t,"y",e,n),e.time=n}(t,n,e),(i.offset||i.target)&&Gt(t,n,i)},notify:y(e)?()=>e(n):Kt(e,n[r])}}function Kt(t,e){return t.pause(),t.forEachNative(((t,{easing:e})=>{var n,i;if(t.updateDuration)e||(t.easing=u),t.updateDuration(1);else{const r={duration:1e3};e||(r.easing="linear"),null===(i=null===(n=t.effect)||void 0===n?void 0:n.updateTiming)||void 0===i||i.call(n,r)}})),()=>{t.currentTime=e.progress}}const Xt=new WeakMap,Yt=new WeakMap,Jt=new WeakMap,Qt=t=>t===document.documentElement?window:t;function te(t,e){return function(t){return"object"==typeof t}(t)?t:t&&e?e[t]:void 0}let ee;function ne(){if(!ee)return;const t=ee.sort(re).map(oe);t.forEach(se),t.forEach(se),ee=void 0}function ie(t){ee?e(ee,t):(ee=[t],requestAnimationFrame(ne))}const re=(t,e)=>t.getDepth()-e.getDepth(),oe=t=>t.animateUpdates(),se=t=>t.next(),ae=(t,e)=>new CustomEvent(t,{detail:{target:e}});function ce(t,e,n){t.dispatchEvent(new CustomEvent(e,{detail:{originalEvent:n}}))}function le(t,e,n){t.dispatchEvent(new CustomEvent(e,{detail:{originalEntry:n}}))}const ue=(t,e,n)=>i=>{i.pointerType&&"mouse"!==i.pointerType||(n(),ce(t,e,i))},fe={inView:{isActive:t=>Boolean(t.inView),subscribe:(t,{enable:e,disable:n},{inViewOptions:i={}})=>{const{once:r}=i,o=ht(i,["once"]);return Lt(t,(i=>{if(e(),le(t,"viewenter",i),!r)return e=>{n(),le(t,"viewleave",e)}}),o)}},hover:{isActive:t=>Boolean(t.hover),subscribe:(t,{enable:e,disable:n})=>{const i=ue(t,"hoverstart",e),r=ue(t,"hoverend",n);return t.addEventListener("pointerenter",i),t.addEventListener("pointerleave",r),()=>{t.removeEventListener("pointerenter",i),t.removeEventListener("pointerleave",r)}}},press:{isActive:t=>Boolean(t.press),subscribe:(t,{enable:e,disable:n})=>{const i=e=>{n(),ce(t,"pressend",e),window.removeEventListener("pointerup",i)},r=n=>{e(),ce(t,"pressstart",n),window.addEventListener("pointerup",i)};return t.addEventListener("pointerdown",r),()=>{t.removeEventListener("pointerdown",r),window.removeEventListener("pointerup",i)}}}},he=["initial","animate",...Object.keys(fe),"exit"],de=new WeakMap;function ge(t){const e={},n=[];for(let i in t){const r=t[i];C(i)&&(j[i]&&(i=j[i]),n.push(i),i=P(i));let s=Array.isArray(r)?r[0]:r;const a=B.get(i);a&&(s=o(r)?a.toDefaultUnit(r):r),e[i]=s}return n.length&&(e.transform=F(n)),e}const pe=t=>`-${t.toLowerCase()}`;function me(t,e={}){return st([()=>{const n=new A(t,[0,1],e);return n.finished.catch((()=>{})),n}],e,e.duration)}t.MotionValue=D,t.ScrollOffset=Ft,t.animate=function(t,e,n){return(y(t)?me:ft)(t,e,n)},t.animateStyle=nt,t.createAnimate=ut,t.createMotionState=function(t={},e){let i,r=e?e.getDepth()+1:0;const o={initial:!0,animate:!0},s={},a={};for(const n of he)a[n]="string"==typeof t[n]?t[n]:null==e?void 0:e.getContext()[n];const c=!1===t.initial?"animate":"initial";let u=ht(te(t[c]||a[c],t.variants)||{},["transition"]);const f=Object.assign({},u),h=(t,e)=>()=>{o[t]=e,ie(g)},d=()=>{for(const e in fe){const n=fe[e].isActive(t),r=s[e];n&&!r?s[e]=fe[e].subscribe(i,{enable:h(e,!0),disable:h(e,!1)},t):!n&&r&&(r(),delete s[e])}},g={update:e=>{i&&(t=e,d(),ie(g))},setActive:(t,e)=>{i&&(o[t]=e,ie(g))},animateUpdates:function*(){var e,n;const r=u;u={};const s={};for(const i of he){if(!o[i])continue;const r=te(t[i]);if(r)for(const i in r)"transition"!==i&&(u[i]=r[i],s[i]=it(null!==(n=null!==(e=r.transition)&&void 0!==e?e:t.transition)&&void 0!==n?n:{},i))}const a=new Set([...Object.keys(u),...Object.keys(r)]),c=[];a.forEach((t=>{var e,n,o;void 0===u[t]&&(u[t]=f[t]),n=r[t],o=u[t],typeof n==typeof o&&(Array.isArray(n)&&Array.isArray(o)?function(t,e){const n=e.length;if(n!==t.length)return!1;for(let i=0;i<n;i++)if(e[i]!==t[i])return!1;return!0}(n,o):n===o)||(null!==(e=f[t])&&void 0!==e||(f[t]=Q.get(i,t)),c.push(nt(i,t,u[t],s[t],A)))})),yield;const h=c.map((t=>t())).filter(Boolean);if(!h.length)return;const d=u;i.dispatchEvent(ae("motionstart",d)),Promise.all(h.map((t=>t.finished))).then((()=>{i.dispatchEvent(ae("motioncomplete",d))})).catch(l)},getDepth:()=>r,getTarget:()=>u,getOptions:()=>t,getContext:()=>a,mount:t=>(i=t,de.set(i,g),d(),()=>{de.delete(i),function(t){ee&&n(ee,t)}(g);for(const t in s)s[t]()}),isMounted:()=>Boolean(i)};return g},t.createStyleString=function(t={}){const e=ge(t);let n="";for(const t in e)n+=t.startsWith("--")?t:t.replace(/[A-Z]/g,pe),n+=`: ${e[t]}; `;return n},t.createStyles=ge,t.getAnimationData=k,t.getStyleName=J,t.glide=At,t.inView=Lt,t.mountedStates=de,t.resize=$t,t.scroll=function(t,e={}){var{container:n=document.documentElement}=e,i=ht(e,["container"]);let r=Jt.get(n);r||(r=new Set,Jt.set(n,r));const o=Zt(n,t,{time:0,x:{current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0},y:{current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0}},i);if(r.add(o),!Xt.has(n)){const t=()=>{const t=performance.now();for(const t of r)t.measure();for(const e of r)e.update(t);for(const t of r)t.notify()};Xt.set(n,t);const e=Qt(n);window.addEventListener("resize",t,{passive:!0}),n!==document.documentElement&&Yt.set(n,$t(n,t)),e.addEventListener("scroll",t,{passive:!0})}const s=Xt.get(n),a=requestAnimationFrame(s);return()=>{var e;"function"!=typeof t&&t.stop(),cancelAnimationFrame(a);const i=Jt.get(n);if(!i)return;if(i.delete(o),i.size)return;const r=Xt.get(n);Xt.delete(n),r&&(Qt(n).removeEventListener("scroll",r),null===(e=Yt.get(n))||void 0===e||e(),window.removeEventListener("resize",r))}},t.spring=Tt,t.stagger=function(t=.1,{start:e=0,from:n=0,easing:i}={}){return(r,s)=>{const a=o(n)?n:function(t,e){if("first"===t)return 0;{const n=e-1;return"last"===t?n:n/2}}(n,s),c=Math.abs(a-r);let l=t*c;if(i){const e=s*t;l=T(i)(l/e)*e}return e+l}},t.style=Q,t.timeline=function(t,e={}){var n;const i=function(t,e={}){var{defaultOptions:n={}}=e,i=ht(e,["defaultOptions"]);const o=[],s=new Map,a={},c=new Map;let l=0,u=0,g=0;for(let e=0;e<t.length;e++){const i=t[e];if(v(i)){c.set(i,u);continue}if(!Array.isArray(i)){c.set(i.name,dt(u,i.at,l,c));continue}const[o,f,p={}]=i;void 0!==p.at&&(u=dt(u,p.at,l,c));let y=0;const w=rt(o,a),b=w.length;for(let t=0;t<b;t++){const e=mt(w[t],s);for(const i in f){const o=yt(i,e);let s=Y(f[i]);const a=it(p,i);let{duration:c=n.duration||r.duration,easing:l=n.easing||r.easing}=a;if(m(l)){"opacity"===i||s.length;const t=l.createAnimation(s,"opacity"!==i,(()=>0),i);l=t.easing,s=t.keyframes||s,c=t.duration||c}const v=lt(p.delay,t,b)||0,w=u+v,E=w+c;let{offset:x=d(s.length)}=a;1===x.length&&0===x[0]&&(x[1]=1);const O=x.length-s.length;O>0&&h(x,O),1===s.length&&s.unshift(null),gt(o,s,l,x,w,E),y=Math.max(v+c,y),g=Math.max(E,g)}}l=u,u+=y}return s.forEach(((t,e)=>{for(const s in t){const a=t[s];a.sort(pt);const c=[],l=[],u=[];for(let t=0;t<a.length;t++){const{at:e,value:n,easing:i}=a[t];c.push(n),l.push(f(0,g,e)),u.push(i||r.easing)}0!==l[0]&&(l.unshift(0),c.unshift(c[0]),u.unshift("linear")),1!==l[l.length-1]&&(l.push(1),c.push(null)),o.push([e,s,c,Object.assign(Object.assign(Object.assign({},n),{duration:g,easing:u,offset:l}),i)])}})),o}(t,e),o=i.map((t=>nt(...t,A))).filter(Boolean);return st(o,e,null===(n=i[0])||void 0===n?void 0:n[3].duration)},t.withControls=st,Object.defineProperty(t,"__esModule",{value:!0})}));
