{"name": "eth-json-rpc-filters", "version": "4.2.2", "description": "[json-rpc-engine](https://github.com/MetaMask/json-rpc-engine) middleware implementing ethereum filter methods. Backed by an [eth-block-tracker](https://github.com/MetaMask/eth-block-tracker) and web3 provider interface (`web3.currentProvider`).", "main": "index.js", "scripts": {"lint": "printf '%s\\n' 'No lint command'", "test": "node test"}, "license": "ISC", "files": ["*.js"], "dependencies": {"@metamask/safe-event-emitter": "^2.0.0", "async-mutex": "^0.2.6", "eth-json-rpc-middleware": "^6.0.0", "eth-query": "^2.1.2", "json-rpc-engine": "^6.1.0", "pify": "^5.0.0"}, "devDependencies": {"eth-block-tracker": "^4.4.1", "ethereumjs-util": "^6.1.0", "ethjs-query": "^0.3.8", "ganache-core": "^2.13.2", "tape": "^4.9.1"}, "repository": {"type": "git", "url": "git+https://github.com/MetaMask/eth-json-rpc-filters.git"}, "bugs": {"url": "https://github.com/MetaMask/eth-json-rpc-filters/issues"}, "homepage": "https://github.com/MetaMask/eth-json-rpc-filters#readme"}