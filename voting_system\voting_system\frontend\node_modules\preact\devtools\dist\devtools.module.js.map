{"version": 3, "file": "devtools.module.js", "sources": ["../src/devtools.js"], "sourcesContent": ["import { options, Fragment, Component } from 'preact';\n\nexport function initDevTools() {\n\tif (typeof window != 'undefined' && window.__PREACT_DEVTOOLS__) {\n\t\twindow.__PREACT_DEVTOOLS__.attachPreact('10.4.1', options, {\n\t\t\tFragment,\n\t\t\tComponent\n\t\t});\n\t}\n}\n"], "names": ["window", "__PREACT_DEVTOOLS__", "attachPreact", "options", "Fragment", "Component"], "mappings": "8DAGsB,oBAAVA,QAAyBA,OAAOC,qBAC1CD,OAAOC,oBAAoBC,aAAa,SAAUC,EAAS,CAC1DC,SAAAA,EACAC,UAAAA"}