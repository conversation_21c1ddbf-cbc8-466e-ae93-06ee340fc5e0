{"author": "<PERSON><PERSON> <<EMAIL>> (http://www.futurealoof.com)", "name": "forever-agent", "description": "HTTP Agent that keeps socket connections alive between keep-alive requests. Formerly part of mikeal/request, now a standalone module.", "version": "0.6.1", "license": "Apache-2.0", "repository": {"url": "https://github.com/mikeal/forever-agent"}, "main": "index.js", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "engines": {"node": "*"}}