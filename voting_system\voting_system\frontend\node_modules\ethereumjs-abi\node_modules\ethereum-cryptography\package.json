{"name": "ethereum-cryptography", "version": "0.1.3", "description": "All the cryptographic primitives used in Ethereum", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": "github:ethereum/js-ethereum-cryptography", "license": "MIT", "keywords": ["ethereum", "cryptography", "digital signature", "hash", "encryption", "prng", "keccak", "scrypt", "pbkdf2", "sha-256", "ripemd-160", "blake2b", "aes", "advanced encryption standar", "secp256k1", "ecdsa", "bip32", "hierarchical deterministic keys", "hdwallet", "hdkeys"], "main": "./index.js", "types": "./index.d.ts", "files": ["src", "vendor", "shims", "bip39", "*.js", "*.js.map", "*.d.ts", "*.d.ts.map", "pure/**/*.js", "pure/**/*.js.map", "pure/**/*.d.ts", "pure/**/*.d.ts.map"], "browser": {"./hdkey.js": "./pure/hdkey.js", "./ripemd160.js": "./pure/ripemd160.js", "./sha256.js": "./pure/sha256.js"}, "sideEffects": false, "scripts": {"prepare": "npm run build", "build": "npm-run-all build:tsc hdkey-without-crypto:build hdkey-without-crypto:copy bip39-without-wordlists:build bip39-without-wordlists:copy", "build:tsc": "tsc --project tsconfig.prod.json", "test": "npm-run-all hdkey-without-crypto:build bip39-without-wordlists:build test:node", "test:node": "mocha", "clean": "rimraf vendor test-builds pure shims hdkey-without-crypto-build bip39-without-wordlists-build bip39 '*.js' '*.js.map' '*.d.ts' '*.d.ts.map' 'src/**/*.js'", "lint": "tslint --project tsconfig.json", "lint:fix": "tslint --fix --project tsconfig.json", "browser-tests": "npm-run-all browser-tests:build browser-tests:test", "browser-tests:build": "bash -x ./scripts/build-browser-tests.sh", "browser-tests:test": "npm-run-all browser-tests:test-parcel browser-tests:test-browserify browser-tests:test-webpack browser-tests:test-rollup", "browser-tests:test-parcel": "karma start --single-run --browsers ChromeHeadless test/karma.parcel.conf.js", "browser-tests:test-browserify": "karma start --single-run --browsers ChromeHeadless test/karma.browserify.conf.js", "browser-tests:test-webpack": "karma start --single-run --browsers ChromeHeadless test/karma.webpack.conf.js", "browser-tests:test-rollup": "karma start --single-run --browsers ChromeHeadless test/karma.rollup.conf.js", "hdkey-without-crypto:build": "bash -x scripts/build-hdkey-without-crypto.sh", "hdkey-without-crypto:copy": "mkdir -p vendor pure/vendor && cp src/vendor/hdkey-without-crypto.js ./vendor && cp src/pure/vendor/hdkey-without-crypto.js ./pure/vendor", "bip39-without-wordlists:build": "bash -x scripts/build-bip39-without-wordlists.sh", "bip39-without-wordlists:copy": "mkdir -p vendor && cp src/vendor/bip39-without-wordlists.js ./vendor"}, "devDependencies": {"@types/chai": "^4.2.1", "@types/mocha": "^5.2.7", "@types/node": "^8.10.52", "browserify": "^16.5.0", "chai": "^4.2.0", "karma": "^4.3.0", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^3.1.0", "karma-mocha": "^1.3.0", "karma-mocha-reporter": "^2.2.5", "mocha": "^6.2.0", "npm-run-all": "^4.1.5", "parcel-bundler": "^1.12.3", "prettier": "1.18.2", "rimraf": "^3.0.0", "rollup": "^1.20.3", "rollup-plugin-alias": "^2.0.0", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-json": "^4.0.0", "rollup-plugin-node-builtins": "^2.1.2", "rollup-plugin-node-globals": "^1.4.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-replace": "^2.2.0", "ts-node": "^8.3.0", "tslint": "^5.19.0", "tslint-config-prettier": "^1.18.0", "tslint-plugin-prettier": "^2.0.1", "typescript": "~3.6.2", "webpack": "^4.39.3", "webpack-cli": "^3.3.8"}, "dependencies": {"@types/pbkdf2": "^3.0.0", "@types/secp256k1": "^4.0.1", "blakejs": "^1.1.0", "browserify-aes": "^1.2.0", "bs58check": "^2.1.2", "create-hash": "^1.2.0", "create-hmac": "^1.1.7", "hash.js": "^1.1.7", "keccak": "^3.0.0", "pbkdf2": "^3.0.17", "randombytes": "^2.1.0", "safe-buffer": "^5.1.2", "scrypt-js": "^3.0.0", "secp256k1": "^4.0.1", "setimmediate": "^1.0.5"}}