"""
Generated by `compile_contracts.py` script.
Compiled with Solidity v0.8.19.
"""

# source: web3/_utils/contract_sources/OffchainLookup.sol:OffchainLookup
OFFCHAIN_LOOKUP_BYTECODE = "0x608060405260405180604001604052806040518060600160405280602c815260200162001129602c91398152602001604051806060016040528060258152602001620011556025913981525060009060026200005d92919062000072565b503480156200006b57600080fd5b50620004c0565b828054828255906000526020600020908101928215620000bf579160200282015b82811115620000be578251829081620000ad9190620003d9565b509160200191906001019062000093565b5b509050620000ce9190620000d2565b5090565b5b80821115620000f65760008181620000ec9190620000fa565b50600101620000d3565b5090565b5080546200010890620001c8565b6000825580601f106200011c57506200013d565b601f0160209004906000526020600020908101906200013c919062000140565b5b50565b5b808211156200015b57600081600090555060010162000141565b5090565b600081519050919050565b7f4e487b7100000000000000000000000000000000000000000000000000000000600052604160045260246000fd5b7f4e487b7100000000000000000000000000000000000000000000000000000000600052602260045260246000fd5b60006002820490506001821680620001e157607f821691505b602082108103620001f757620001f662000199565b5b50919050565b60008190508160005260206000209050919050565b60006020601f8301049050919050565b600082821b905092915050565b600060088302620002617fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff8262000222565b6200026d868362000222565b95508019841693508086168417925050509392505050565b6000819050919050565b6000819050919050565b6000620002ba620002b4620002ae8462000285565b6200028f565b62000285565b9050919050565b6000819050919050565b620002d68362000299565b620002ee620002e582620002c1565b8484546200022f565b825550505050565b600090565b62000305620002f6565b62000312818484620002cb565b505050565b5b818110156200033a576200032e600082620002fb565b60018101905062000318565b5050565b601f82111562000389576200035381620001fd565b6200035e8462000212565b810160208510156200036e578190505b620003866200037d8562000212565b83018262000317565b50505b505050565b600082821c905092915050565b6000620003ae600019846008026200038e565b1980831691505092915050565b6000620003c983836200039b565b9150826002028217905092915050565b620003e4826200015f565b67ffffffffffffffff8111156200040057620003ff6200016a565b5b6200040c8254620001c8565b620004198282856200033e565b600060209050601f8311600181146200045157600084156200043c578287015190505b620004488582620003bb565b865550620004b8565b601f1984166200046186620001fd565b60005b828110156200048b5784890151825560018201915060208501945060208101905062000464565b86831015620004ab5784890151620004a7601f8916826200039b565b8355505b6001600288020188555050505b505050505050565b610c5980620004d06000396000f3fe608060405234801561001057600080fd5b50600436106100415760003560e01c806309a3c01b146100465780636337ed5814610064578063da96d05a14610094575b600080fd5b61004e6100c4565b60405161005b919061041b565b60405180910390f35b61007e600480360381019061007991906104b6565b610114565b60405161008b919061041b565b60405180910390f35b6100ae60048036038101906100a99190610503565b610202565b6040516100bb919061041b565b60405180910390f35b606080306000826309a3c01b60e01b846040517f556f183000000000000000000000000000000000000000000000000000000000815260040161010b9594939291906107d0565b60405180910390fd5b6060600083838101906101279190610968565b90507fd9bdd1345ca2a00d0c1413137c1b2b1d0a35e5b0e11508f3b3eff856286af0758160405160200161015b91906109f8565b60405160208183030381529060405280519060200120146101b1576040517f08c379a00000000000000000000000000000000000000000000000000000000081526004016101a890610a6c565b60405180910390fd5b306000858563da96d05a60e01b88886040517f556f18300000000000000000000000000000000000000000000000000000000081526004016101f99796959493929190610ab9565b60405180910390fd5b6060600085858101906102159190610968565b90507faed76f463930323372899e36460e078e5292aac45f645bbe567be6fca83ede108160405160200161024991906109f8565b604051602081830303815290604052805190602001201461029f576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040161029690610b97565b60405180910390fd5b600084848101906102b09190610968565b90507fd9bdd1345ca2a00d0c1413137c1b2b1d0a35e5b0e11508f3b3eff856286af075816040516020016102e491906109f8565b604051602081830303815290604052805190602001201461033a576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040161033190610c03565b60405180910390fd5b86868080601f016020809104026020016040519081016040528093929190818152602001838380828437600081840152601f19601f8201169050808301925050505050505092505050949350505050565b600081519050919050565b600082825260208201905092915050565b60005b838110156103c55780820151818401526020810190506103aa565b60008484015250505050565b6000601f19601f8301169050919050565b60006103ed8261038b565b6103f78185610396565b93506104078185602086016103a7565b610410816103d1565b840191505092915050565b6000602082019050818103600083015261043581846103e2565b905092915050565b6000604051905090565b600080fd5b600080fd5b600080fd5b600080fd5b600080fd5b60008083601f84011261047657610475610451565b5b8235905067ffffffffffffffff81111561049357610492610456565b5b6020830191508360018202830111156104af576104ae61045b565b5b9250929050565b600080602083850312156104cd576104cc610447565b5b600083013567ffffffffffffffff8111156104eb576104ea61044c565b5b6104f785828601610460565b92509250509250929050565b6000806000806040858703121561051d5761051c610447565b5b600085013567ffffffffffffffff81111561053b5761053a61044c565b5b61054787828801610460565b9450945050602085013567ffffffffffffffff81111561056a5761056961044c565b5b61057687828801610460565b925092505092959194509250565b600073ffffffffffffffffffffffffffffffffffffffff82169050919050565b60006105af82610584565b9050919050565b6105bf816105a4565b82525050565b600081549050919050565b600082825260208201905092915050565b60008190508160005260206000209050919050565b7f4e487b7100000000000000000000000000000000000000000000000000000000600052602260045260246000fd5b6000600282049050600182168061063d57607f821691505b6020821081036106505761064f6105f6565b5b50919050565b600082825260208201905092915050565b60008190508160005260206000209050919050565b6000815461068981610625565b6106938186610656565b945060018216600081146106ae57600181146106c4576106f7565b60ff1983168652811515602002860193506106f7565b6106cd85610667565b60005b838110156106ef578154818901526001820191506020810190506106d0565b808801955050505b50505092915050565b600061070c838361067c565b905092915050565b6000600182019050919050565b600061072c826105c5565b61073681856105d0565b935083602082028501610748856105e1565b8060005b85811015610783578484038952816107648582610700565b945061076f83610714565b925060208a0199505060018101905061074c565b50829750879550505050505092915050565b60007fffffffff0000000000000000000000000000000000000000000000000000000082169050919050565b6107ca81610795565b82525050565b600060a0820190506107e560008301886105b6565b81810360208301526107f78187610721565b9050818103604083015261080b81866103e2565b905061081a60608301856107c1565b818103608083015261082c81846103e2565b90509695505050505050565b600080fd5b7f4e487b7100000000000000000000000000000000000000000000000000000000600052604160045260246000fd5b610875826103d1565b810181811067ffffffffffffffff821117156108945761089361083d565b5b80604052505050565b60006108a761043d565b90506108b3828261086c565b919050565b600067ffffffffffffffff8211156108d3576108d261083d565b5b6108dc826103d1565b9050602081019050919050565b82818337600083830152505050565b600061090b610906846108b8565b61089d565b90508281526020810184848401111561092757610926610838565b5b6109328482856108e9565b509392505050565b600082601f83011261094f5761094e610451565b5b813561095f8482602086016108f8565b91505092915050565b60006020828403121561097e5761097d610447565b5b600082013567ffffffffffffffff81111561099c5761099b61044c565b5b6109a88482850161093a565b91505092915050565b600081519050919050565b600081905092915050565b60006109d2826109b1565b6109dc81856109bc565b93506109ec8185602086016103a7565b80840191505092915050565b6000610a0482846109c7565b915081905092915050565b600082825260208201905092915050565b7f7465737420646174612076616c69646174696f6e206661696c65642e00000000600082015250565b6000610a56601c83610a0f565b9150610a6182610a20565b602082019050919050565b60006020820190508181036000830152610a8581610a49565b9050919050565b6000610a988385610396565b9350610aa58385846108e9565b610aae836103d1565b840190509392505050565b600060a082019050610ace600083018a6105b6565b8181036020830152610ae08189610721565b90508181036040830152610af5818789610a8c565b9050610b0460608301866107c1565b8181036080830152610b17818486610a8c565b905098975050505050505050565b7f68747470207265717565737420726573756c742076616c69646174696f6e206660008201527f61696c65642e0000000000000000000000000000000000000000000000000000602082015250565b6000610b81602683610a0f565b9150610b8c82610b25565b604082019050919050565b60006020820190508181036000830152610bb081610b74565b9050919050565b7f6578747261446174612076616c69646174696f6e206661696c65642e00000000600082015250565b6000610bed601c83610a0f565b9150610bf882610bb7565b602082019050919050565b60006020820190508181036000830152610c1c81610be0565b905091905056fea26469706673582212207d45ce0c131520a39dcebc7261dd61227c25059a661ba1fb25e1acbd0061870164736f6c6343000813003368747470733a2f2f776562332e70792f676174657761792f7b73656e6465727d2f7b646174617d2e6a736f6e68747470733a2f2f776562332e70792f676174657761792f7b73656e6465727d2e6a736f6e"  # noqa: E501
OFFCHAIN_LOOKUP_RUNTIME = "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"  # noqa: E501
OFFCHAIN_LOOKUP_ABI = [
    {
        "inputs": [
            {"internalType": "address", "name": "sender", "type": "address"},
            {"internalType": "string[]", "name": "urls", "type": "string[]"},
            {"internalType": "bytes", "name": "callData", "type": "bytes"},
            {"internalType": "bytes4", "name": "callbackFunction", "type": "bytes4"},
            {"internalType": "bytes", "name": "extraData", "type": "bytes"},
        ],
        "name": "OffchainLookup",
        "type": "error",
    },
    {
        "inputs": [],
        "name": "continuousOffchainLookup",
        "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [
            {"internalType": "bytes", "name": "specifiedDataFromTest", "type": "bytes"}
        ],
        "name": "testOffchainLookup",
        "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [
            {"internalType": "bytes", "name": "result", "type": "bytes"},
            {"internalType": "bytes", "name": "extraData", "type": "bytes"},
        ],
        "name": "testOffchainLookupWithProof",
        "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}],
        "stateMutability": "nonpayable",
        "type": "function",
    },
]
OFFCHAIN_LOOKUP_DATA = {
    "bytecode": OFFCHAIN_LOOKUP_BYTECODE,
    "bytecode_runtime": OFFCHAIN_LOOKUP_RUNTIME,
    "abi": OFFCHAIN_LOOKUP_ABI,
}
