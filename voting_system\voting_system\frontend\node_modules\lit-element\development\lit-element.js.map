{"version": 3, "file": "lit-element.js", "sourceRoot": "", "sources": ["../src/lit-element.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAEH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAyCG;AACH,OAAO,EAAiB,eAAe,EAAC,MAAM,uBAAuB,CAAC;AACtE,OAAO,EAAC,MAAM,EAAiB,QAAQ,EAAW,MAAM,UAAU,CAAC;AACnE,cAAc,uBAAuB,CAAC;AACtC,cAAc,UAAU,CAAC;AA8BzB,+EAA+E;AAC/E,iDAAiD;AACjD,MAAM,CAAC,MAAM,eAAe,GAAG,eAAe,CAAC;AAE/C,MAAM,QAAQ,GAAG,IAAI,CAAC;AAEtB,IAAI,YAAqD,CAAC;AAE1D,IAAI,QAAQ,EAAE;IACZ,uEAAuE;IACvE,cAAc;IACd,MAAM,cAAc,GAClB,OAAC,UAAU,CAAC,iBAAiB,oCAA5B,UAAU,CAAC,iBAAiB,GAAK,IAAI,GAAG,EAAE,EAAC,CAAC;IAE/C,0CAA0C;IAC1C,YAAY,GAAG,CAAC,IAAY,EAAE,OAAe,EAAE,EAAE;QAC/C,OAAO,IAAI,4BAA4B,IAAI,wBAAwB,CAAC;QACpE,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;YAChC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACtB,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;SAC7B;IACH,CAAC,CAAC;CACH;AAED;;;;;;;;GAQG;AACH,MAAM,OAAO,UAAW,SAAQ,eAAe;IAA/C;;QAaE;;WAEG;QACM,kBAAa,GAAkB,EAAC,IAAI,EAAE,IAAI,EAAC,CAAC;QAE7C,gBAAW,GAAyB,SAAS,CAAC;IA8FxD,CAAC;IA5FC;;OAEG;IACgB,gBAAgB;;;QACjC,MAAM,UAAU,GAAG,KAAK,CAAC,gBAAgB,EAAE,CAAC;QAC5C,kEAAkE;QAClE,uEAAuE;QACvE,sEAAsE;QACtE,sEAAsE;QACtE,kBAAkB;QAClB,YAAA,IAAI,CAAC,aAAa,EAAC,YAAY,uCAAZ,YAAY,GAAK,UAAW,CAAC,UAAuB,EAAC;QACxE,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;;;;;OAMG;IACgB,MAAM,CAAC,iBAAiC;QACzD,qEAAqE;QACrE,0EAA0E;QAC1E,eAAe;QACf,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QAC5B,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YACpB,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;SACnD;QACD,KAAK,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;QAChC,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IACxE,CAAC;IAED;;;;;;;;;;;;;;;;;;;OAmBG;IACM,iBAAiB;;QACxB,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAC1B,MAAA,IAAI,CAAC,WAAW,0CAAE,YAAY,CAAC,IAAI,CAAC,CAAC;IACvC,CAAC;IAED;;;;;;;;;;;;;;;;;;OAkBG;IACM,oBAAoB;;QAC3B,KAAK,CAAC,oBAAoB,EAAE,CAAC;QAC7B,MAAA,IAAI,CAAC,WAAW,0CAAE,YAAY,CAAC,KAAK,CAAC,CAAC;IACxC,CAAC;IAED;;;;;;OAMG;IACO,MAAM;QACd,OAAO,QAAQ,CAAC;IAClB,CAAC;;AA9GD;;;;;;GAMG;AACuB,WAAC,WAAW,CAAC,GAAG,IAAI,CAAC;AAE/C,4CAA4C;AACrC,WAAC,eAAe,CAAC,GAAG,IAAI,CAAC;AAuGlC,iCAAiC;AACjC,MAAA,UAAU,CAAC,wBAAwB,2DAAG,EAAC,UAAU,EAAC,CAAC,CAAC;AAEpD,+BAA+B;AAC/B,MAAM,eAAe,GAAG,QAAQ;IAC9B,CAAC,CAAC,UAAU,CAAC,gCAAgC;IAC7C,CAAC,CAAC,UAAU,CAAC,yBAAyB,CAAC;AACzC,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAG,EAAC,UAAU,EAAC,CAAC,CAAC;AAEhC,oBAAoB;AACpB,IAAI,QAAQ,EAAE;IACZ,uDAAuD;IACvD,gEAAgE;IAChE,0CAA0C;IACzC,UAAkB,CAAC,UAAU,CAAC,GAAG;QAChC,MAAM,SAAS,GAAI,eAAuB,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/D,IAAI,CAAC,SAAS,EAAE;YACd,OAAO,KAAK,CAAC;SACd;QACD,MAAM,oBAAoB,GAAG,CAAC,GAAQ,EAAE,IAAY,EAAE,OAAO,GAAG,KAAK,EAAE,EAAE;YACvE,IAAI,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;gBAC5B,MAAM,QAAQ,GAAG,CAAC,OAAO,GAAG,KAAK,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC;qBACjE,IAAI,CAAC;gBACR,YAAY,CACV,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,EACvC,KAAK,IAAI,8BAA8B,QAAQ,OAAO;oBACpD,YAAY,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,GAAG;oBAC9C,gCAAgC,CACnC,CAAC;aACH;QACH,CAAC,CAAC;QACF,oBAAoB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QACrC,oBAAoB,CAAC,IAAI,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;QAC9C,oBAAoB,CAAE,IAA0B,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;QAC3E,OAAO,IAAI,CAAC;IACd,CAAC,CAAC;IACF,sDAAsD;CACvD;AAED;;;;;;;;;;;;;;;;;GAiBG;AACH,MAAM,CAAC,MAAM,IAAI,GAAG;IAClB,qBAAqB,EAAE,CACrB,EAAc,EACd,IAAY,EACZ,KAAoB,EACpB,EAAE;QACF,2BAA2B;QAC1B,EAAU,CAAC,qBAAqB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACjD,CAAC;IACD,2BAA2B;IAC3B,mBAAmB,EAAE,CAAC,EAAc,EAAE,EAAE,CAAE,EAAU,CAAC,mBAAmB;CACzE,CAAC;AAEF,2EAA2E;AAC3E,oEAAoE;AACpE,OAAC,UAAU,CAAC,kBAAkB,oCAA7B,UAAU,CAAC,kBAAkB,GAAK,EAAE,EAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACrD,IAAI,QAAQ,IAAI,UAAU,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE;IACxD,YAAa,CACX,mBAAmB,EACnB,6DAA6D;QAC3D,qBAAqB,CACxB,CAAC;CACH", "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/**\n * The main LitElement module, which defines the {@linkcode LitElement} base\n * class and related APIs.\n *\n *  LitElement components can define a template and a set of observed\n * properties. Changing an observed property triggers a re-render of the\n * element.\n *\n *  Import {@linkcode LitElement} and {@linkcode html} from this module to\n * create a component:\n *\n *  ```js\n * import {LitElement, html} from 'lit-element';\n *\n * class MyElement extends LitElement {\n *\n *   // Declare observed properties\n *   static get properties() {\n *     return {\n *       adjective: {}\n *     }\n *   }\n *\n *   constructor() {\n *     this.adjective = 'awesome';\n *   }\n *\n *   // Define the element's template\n *   render() {\n *     return html`<p>your ${adjective} template here</p>`;\n *   }\n * }\n *\n * customElements.define('my-element', MyElement);\n * ```\n *\n * `LitElement` extends {@linkcode ReactiveElement} and adds lit-html\n * templating. The `ReactiveElement` class is provided for users that want to\n * build their own custom element base classes that don't use lit-html.\n *\n * @packageDocumentation\n */\nimport {PropertyValues, ReactiveElement} from '@lit/reactive-element';\nimport {render, RenderOptions, noChange, RootPart} from 'lit-html';\nexport * from '@lit/reactive-element';\nexport * from 'lit-html';\n\nimport {LitUnstable} from 'lit-html';\nimport {ReactiveUnstable} from '@lit/reactive-element';\n\n/**\n * Contains types that are part of the unstable debug API.\n *\n * Everything in this API is not stable and may change or be removed in the future,\n * even on patch releases.\n */\n// eslint-disable-next-line @typescript-eslint/no-namespace\nexport namespace Unstable {\n  /**\n   * When Lit is running in dev mode and `window.emitLitDebugLogEvents` is true,\n   * we will emit 'lit-debug' events to window, with live details about the update and render\n   * lifecycle. These can be useful for writing debug tooling and visualizations.\n   *\n   * Please be aware that running with window.emitLitDebugLogEvents has performance overhead,\n   * making certain operations that are normally very cheap (like a no-op render) much slower,\n   * because we must copy data and dispatch events.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-namespace\n  export namespace DebugLog {\n    export type Entry =\n      | LitUnstable.DebugLog.Entry\n      | ReactiveUnstable.DebugLog.Entry;\n  }\n}\n\n// For backwards compatibility export ReactiveElement as UpdatingElement. Note,\n// IE transpilation requires exporting like this.\nexport const UpdatingElement = ReactiveElement;\n\nconst DEV_MODE = true;\n\nlet issueWarning: (code: string, warning: string) => void;\n\nif (DEV_MODE) {\n  // Ensure warnings are issued only 1x, even if multiple versions of Lit\n  // are loaded.\n  const issuedWarnings: Set<string | undefined> =\n    (globalThis.litIssuedWarnings ??= new Set());\n\n  // Issue a warning, if we haven't already.\n  issueWarning = (code: string, warning: string) => {\n    warning += ` See https://lit.dev/msg/${code} for more information.`;\n    if (!issuedWarnings.has(warning)) {\n      console.warn(warning);\n      issuedWarnings.add(warning);\n    }\n  };\n}\n\n/**\n * Base element class that manages element properties and attributes, and\n * renders a lit-html template.\n *\n * To define a component, subclass `LitElement` and implement a\n * `render` method to provide the component's template. Define properties\n * using the {@linkcode LitElement.properties properties} property or the\n * {@linkcode property} decorator.\n */\nexport class LitElement extends ReactiveElement {\n  /**\n   * Ensure this class is marked as `finalized` as an optimization ensuring\n   * it will not needlessly try to `finalize`.\n   *\n   * Note this property name is a string to prevent breaking Closure JS Compiler\n   * optimizations. See @lit/reactive-element for more information.\n   */\n  protected static override ['finalized'] = true;\n\n  // This property needs to remain unminified.\n  static ['_$litElement$'] = true;\n\n  /**\n   * @category rendering\n   */\n  readonly renderOptions: RenderOptions = {host: this};\n\n  private __childPart: RootPart | undefined = undefined;\n\n  /**\n   * @category rendering\n   */\n  protected override createRenderRoot() {\n    const renderRoot = super.createRenderRoot();\n    // When adoptedStyleSheets are shimmed, they are inserted into the\n    // shadowRoot by createRenderRoot. Adjust the renderBefore node so that\n    // any styles in Lit content render before adoptedStyleSheets. This is\n    // important so that adoptedStyleSheets have precedence over styles in\n    // the shadowRoot.\n    this.renderOptions.renderBefore ??= renderRoot!.firstChild as ChildNode;\n    return renderRoot;\n  }\n\n  /**\n   * Updates the element. This method reflects property values to attributes\n   * and calls `render` to render DOM via lit-html. Setting properties inside\n   * this method will *not* trigger another update.\n   * @param changedProperties Map of changed properties with old values\n   * @category updates\n   */\n  protected override update(changedProperties: PropertyValues) {\n    // Setting properties in `render` should not trigger an update. Since\n    // updates are allowed after super.update, it's important to call `render`\n    // before that.\n    const value = this.render();\n    if (!this.hasUpdated) {\n      this.renderOptions.isConnected = this.isConnected;\n    }\n    super.update(changedProperties);\n    this.__childPart = render(value, this.renderRoot, this.renderOptions);\n  }\n\n  /**\n   * Invoked when the component is added to the document's DOM.\n   *\n   * In `connectedCallback()` you should setup tasks that should only occur when\n   * the element is connected to the document. The most common of these is\n   * adding event listeners to nodes external to the element, like a keydown\n   * event handler added to the window.\n   *\n   * ```ts\n   * connectedCallback() {\n   *   super.connectedCallback();\n   *   addEventListener('keydown', this._handleKeydown);\n   * }\n   * ```\n   *\n   * Typically, anything done in `connectedCallback()` should be undone when the\n   * element is disconnected, in `disconnectedCallback()`.\n   *\n   * @category lifecycle\n   */\n  override connectedCallback() {\n    super.connectedCallback();\n    this.__childPart?.setConnected(true);\n  }\n\n  /**\n   * Invoked when the component is removed from the document's DOM.\n   *\n   * This callback is the main signal to the element that it may no longer be\n   * used. `disconnectedCallback()` should ensure that nothing is holding a\n   * reference to the element (such as event listeners added to nodes external\n   * to the element), so that it is free to be garbage collected.\n   *\n   * ```ts\n   * disconnectedCallback() {\n   *   super.disconnectedCallback();\n   *   window.removeEventListener('keydown', this._handleKeydown);\n   * }\n   * ```\n   *\n   * An element may be re-connected after being disconnected.\n   *\n   * @category lifecycle\n   */\n  override disconnectedCallback() {\n    super.disconnectedCallback();\n    this.__childPart?.setConnected(false);\n  }\n\n  /**\n   * Invoked on each update to perform rendering tasks. This method may return\n   * any value renderable by lit-html's `ChildPart` - typically a\n   * `TemplateResult`. Setting properties inside this method will *not* trigger\n   * the element to update.\n   * @category rendering\n   */\n  protected render(): unknown {\n    return noChange;\n  }\n}\n\n// Install hydration if available\nglobalThis.litElementHydrateSupport?.({LitElement});\n\n// Apply polyfills if available\nconst polyfillSupport = DEV_MODE\n  ? globalThis.litElementPolyfillSupportDevMode\n  : globalThis.litElementPolyfillSupport;\npolyfillSupport?.({LitElement});\n\n// DEV mode warnings\nif (DEV_MODE) {\n  /* eslint-disable @typescript-eslint/no-explicit-any */\n  // Note, for compatibility with closure compilation, this access\n  // needs to be as a string property index.\n  (LitElement as any)['finalize'] = function (this: typeof LitElement) {\n    const finalized = (ReactiveElement as any).finalize.call(this);\n    if (!finalized) {\n      return false;\n    }\n    const warnRemovedOrRenamed = (obj: any, name: string, renamed = false) => {\n      if (obj.hasOwnProperty(name)) {\n        const ctorName = (typeof obj === 'function' ? obj : obj.constructor)\n          .name;\n        issueWarning(\n          renamed ? 'renamed-api' : 'removed-api',\n          `\\`${name}\\` is implemented on class ${ctorName}. It ` +\n            `has been ${renamed ? 'renamed' : 'removed'} ` +\n            `in this version of LitElement.`\n        );\n      }\n    };\n    warnRemovedOrRenamed(this, 'render');\n    warnRemovedOrRenamed(this, 'getStyles', true);\n    warnRemovedOrRenamed((this as typeof LitElement).prototype, 'adoptStyles');\n    return true;\n  };\n  /* eslint-enable @typescript-eslint/no-explicit-any */\n}\n\n/**\n * END USERS SHOULD NOT RELY ON THIS OBJECT.\n *\n * Private exports for use by other Lit packages, not intended for use by\n * external users.\n *\n * We currently do not make a mangled rollup build of the lit-ssr code. In order\n * to keep a number of (otherwise private) top-level exports  mangled in the\n * client side code, we export a _$LE object containing those members (or\n * helper methods for accessing private fields of those members), and then\n * re-export them for use in lit-ssr. This keeps lit-ssr agnostic to whether the\n * client-side code is being used in `dev` mode or `prod` mode.\n *\n * This has a unique name, to disambiguate it from private exports in\n * lit-html, since this module re-exports all of lit-html.\n *\n * @private\n */\nexport const _$LE = {\n  _$attributeToProperty: (\n    el: LitElement,\n    name: string,\n    value: string | null\n  ) => {\n    // eslint-disable-next-line\n    (el as any)._$attributeToProperty(name, value);\n  },\n  // eslint-disable-next-line\n  _$changedProperties: (el: LitElement) => (el as any)._$changedProperties,\n};\n\n// IMPORTANT: do not change the property name or the assignment expression.\n// This line will be used in regexes to search for LitElement usage.\n(globalThis.litElementVersions ??= []).push('3.3.3');\nif (DEV_MODE && globalThis.litElementVersions.length > 1) {\n  issueWarning!(\n    'multiple-versions',\n    `Multiple versions of Lit loaded. Loading multiple versions ` +\n      `is not recommended.`\n  );\n}\n"]}