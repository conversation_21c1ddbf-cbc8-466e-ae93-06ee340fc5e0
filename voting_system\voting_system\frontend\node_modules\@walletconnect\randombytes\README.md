# iso-random [![npm version](https://badge.fury.io/js/%40pedrouid%2Fiso-random.svg)](https://badge.fury.io/js/%40pedrouid%2Fiso-random)

Isomorphic Library for Random Bytes

## Description

This library supports random byte generation through native NodeJS and Browser APIs when available and fallbacks to vanilla javascript are already provided.

## Usage

### RandomBytes

```typescript
import * as isoRandom from 'iso-random';

const length = 32;
const key = isoRandom.randomBytes(length);

// key.length === length
```

## React-Native Support

This library is intended for use in a Browser or NodeJS environment, however it is possible to use in a React-Native environment if NodeJS modules are polyfilled with `react-native-crypto`, read more [here](https://github.com/tradle/react-native-crypto).

## License

[MIT License](LICENSE.md)
