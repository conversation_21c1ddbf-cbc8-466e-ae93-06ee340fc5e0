import os
import django
import datetime
import random
import csv
from io import StringIO

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'voting_system.settings')
django.setup()

from voting.models import ValidNationalID
from django.utils import timezone

def generate_id_number():
    """Generate a random 8-digit ID number"""
    return ''.join([str(random.randint(0, 9)) for _ in range(8)])

def generate_name():
    """Generate a random Kenyan name"""
    first_names = [
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", 
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"
    ]
    
    last_names = [
        "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Githuku", "Muchiri", "Gichuki", "Muriuki", "Kinyua", "Nyaga",
        "Mwenda", "Murithi", "Kiarie", "Chege", "Muthoni", "Wangui", "Njoki", "Muthoni"
    ]
    
    return f"{random.choice(first_names)} {random.choice(last_names)}"

def generate_date_of_birth():
    """Generate a random date of birth for an adult (18-80 years old)"""
    today = timezone.now().date()
    age = random.randint(18, 80)
    days_offset = random.randint(0, 364)
    return today - datetime.timedelta(days=365*age + days_offset)

def add_valid_ids(num_ids=100):
    """Add a specified number of valid national IDs to the database"""
    print(f"Adding {num_ids} valid national IDs to the database...")
    
    # Check if there are already IDs in the database
    existing_count = ValidNationalID.objects.count()
    if existing_count > 0:
        print(f"There are already {existing_count} valid IDs in the database.")
        choice = input("Do you want to add more IDs? (y/n): ")
        if choice.lower() != 'y':
            print("Operation cancelled.")
            return
    
    # Generate and add IDs
    valid_ids = []
    for i in range(num_ids):
        try:
            # Generate random data
            id_number = generate_id_number()
            full_name = generate_name()
            date_of_birth = generate_date_of_birth()
            
            # Create ValidNationalID object
            valid_id, created = ValidNationalID.objects.get_or_create(
                id_number=id_number,
                defaults={
                    'full_name': full_name,
                    'date_of_birth': date_of_birth,
                    'is_active': True
                }
            )
            
            if created:
                valid_ids.append(valid_id)
                print(f"Added valid ID: {id_number} - {full_name}")
            else:
                print(f"ID {id_number} already exists, skipping...")
                
        except Exception as e:
            print(f"Error adding ID {i+1}: {str(e)}")
    
    print(f"Successfully added {len(valid_ids)} valid national IDs to the database.")
    return valid_ids

def import_ids_from_csv(csv_data):
    """Import valid national IDs from CSV data"""
    print("Importing valid national IDs from CSV data...")
    
    csv_file = StringIO(csv_data)
    reader = csv.DictReader(csv_file)
    
    valid_ids = []
    for row in reader:
        try:
            id_number = row.get('id_number')
            full_name = row.get('full_name')
            date_of_birth = row.get('date_of_birth')
            
            if not id_number:
                print("Skipping row with missing ID number")
                continue
                
            # Parse date if provided
            dob = None
            if date_of_birth:
                try:
                    dob = datetime.datetime.strptime(date_of_birth, '%Y-%m-%d').date()
                except ValueError:
                    print(f"Invalid date format for ID {id_number}, using None")
            
            # Create ValidNationalID object
            valid_id, created = ValidNationalID.objects.get_or_create(
                id_number=id_number,
                defaults={
                    'full_name': full_name or '',
                    'date_of_birth': dob,
                    'is_active': True
                }
            )
            
            if created:
                valid_ids.append(valid_id)
                print(f"Added valid ID: {id_number} - {full_name}")
            else:
                print(f"ID {id_number} already exists, skipping...")
                
        except Exception as e:
            print(f"Error importing ID: {str(e)}")
    
    print(f"Successfully imported {len(valid_ids)} valid national IDs from CSV.")
    return valid_ids

if __name__ == "__main__":
    # Add 50 random valid IDs
    add_valid_ids(50)
    
    # Example of importing from CSV (commented out)
    """
    csv_data = '''id_number,full_name,date_of_birth
    12345678,John Kamau,1985-05-15
    87654321,Mary Wanjiru,1990-10-20
    11223344,James Mwangi,1978-03-25
    '''
    import_ids_from_csv(csv_data)
    """
