import { browserSha256, browserSha512 } from "../lib/browser";
export async function sha256(msg) {
    const result = await browserSha256(msg);
    return result;
}
export async function sha512(msg) {
    const result = await browserSha512(msg);
    return result;
}
export async function ripemd160(_msg) {
    throw new Error("Not supported for Browser async methods, use sync instead!");
}
//# sourceMappingURL=sha2.js.map