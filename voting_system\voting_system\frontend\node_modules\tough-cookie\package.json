{"author": {"name": "<PERSON>", "email": "<EMAIL>", "website": "https://github.com/stash"}, "contributors": [{"name": "<PERSON>", "website": "https://github.com/apsavin"}, {"name": "<PERSON>", "website": "https://github.com/ianlivingstone"}, {"name": "<PERSON>", "website": "https://github.com/inikulin"}, {"name": "<PERSON><PERSON>", "website": "https://github.com/lalitkapoor"}, {"name": "<PERSON>", "website": "https://github.com/sambthompson"}, {"name": "<PERSON>", "website": "https://github.com/Sebmaster"}], "license": "BSD-3-<PERSON><PERSON>", "name": "tough-cookie", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "version": "2.5.0", "homepage": "https://github.com/salesforce/tough-cookie", "repository": {"type": "git", "url": "git://github.com/salesforce/tough-cookie.git"}, "bugs": {"url": "https://github.com/salesforce/tough-cookie/issues"}, "main": "./lib/cookie", "files": ["lib"], "scripts": {"version": "genversion lib/version.js && git add lib/version.js", "test": "vows test/*_test.js", "cover": "nyc --reporter=lcov --reporter=html vows test/*_test.js"}, "engines": {"node": ">=0.8"}, "devDependencies": {"async": "^1.4.2", "genversion": "^2.1.0", "nyc": "^11.6.0", "string.prototype.repeat": "^0.2.0", "vows": "^0.8.2"}, "dependencies": {"psl": "^1.1.28", "punycode": "^2.1.1"}}