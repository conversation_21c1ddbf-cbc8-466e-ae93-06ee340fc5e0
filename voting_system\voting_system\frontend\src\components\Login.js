import React, { useState } from 'react';

const Login = ({ onLoginSuccess }) => {
    const [username, setUsername] = useState('');
    const [password, setPassword] = useState('');
    const [error, setError] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [showRegister, setShowRegister] = useState(false);
    const [registerData, setRegisterData] = useState({
        username: '',
        email: '',
        password: '',
        confirmPassword: '',
        firstName: '',
        lastName: '',
    });

    const handleLogin = async (e) => {
        e.preventDefault();
        setError('');
        setIsLoading(true);

        try {
            // Use the new custom authentication endpoint
            const response = await fetch('/api/auth/login/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ username, password }),
            });

            // Check if the response is JSON
            const contentType = response.headers.get('content-type');
            let data;

            if (contentType && contentType.includes('application/json')) {
                data = await response.json();
            } else {
                // If not JSON, get the text and create an error
                const text = await response.text();
                throw new Error('Server returned an invalid response. Please try again later.');
            }

            if (!response.ok) {
                throw new Error(data.error || data.non_field_errors?.[0] || 'Invalid credentials');
            }

            // Store token and user data in localStorage
            localStorage.setItem('authToken', data.token);
            localStorage.setItem('userData', JSON.stringify({
                userId: data.user_id,
                username: data.username,
                firstName: data.first_name,
                lastName: data.last_name,
                email: data.email,
                isStaff: data.is_staff,
                isCandidate: data.is_candidate,
                voter: data.voter
            }));

            // Set Authorization header for future requests
            if (onLoginSuccess) {
                onLoginSuccess(data.token, data);
            }

            setIsLoading(false);
        } catch (error) {
            setError(error.message || 'Login failed. Please try again.');
            setIsLoading(false);
        }
    };

    const handleRegister = async (e) => {
        e.preventDefault();
        setError('');
        setIsLoading(true);

        // Validate passwords match
        if (registerData.password !== registerData.confirmPassword) {
            setError('Passwords do not match');
            setIsLoading(false);
            return;
        }

        try {
            const response = await fetch('/api/auth/register/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username: registerData.username,
                    email: registerData.email,
                    password: registerData.password,
                    first_name: registerData.firstName,
                    last_name: registerData.lastName,
                }),
            });

            // Check if the response is JSON
            const contentType = response.headers.get('content-type');
            let data;

            if (contentType && contentType.includes('application/json')) {
                data = await response.json();
            } else {
                // If not JSON, get the text and create an error
                const text = await response.text();
                throw new Error('Server returned an invalid response. Please try again later.');
            }

            if (!response.ok) {
                const errorMessage = Object.values(data).flat().join(' ');
                throw new Error(errorMessage || 'Registration failed');
            }

            // Switch to login form with success message
            setShowRegister(false);
            setUsername(registerData.username);
            setPassword('');
            setError('');
            alert('Registration successful! Please log in with your credentials.');
            setIsLoading(false);
        } catch (error) {
            setError(error.message || 'Registration failed. Please try again.');
            setIsLoading(false);
        }
    };

    const handleRegisterInputChange = (e) => {
        const { name, value } = e.target;
        setRegisterData({
            ...registerData,
            [name]: value,
        });
    };

    return (
        <div className="container mt-5">
            <div className="row justify-content-center">
                <div className="col-md-6">
                    <div className="card shadow">
                        <div className="card-header bg-primary text-white">
                            <h4 className="mb-0">{showRegister ? 'Create Account' : 'Login'}</h4>
                        </div>
                        <div className="card-body">
                            {error && (
                                <div className="alert alert-danger" role="alert">
                                    {error}
                                </div>
                            )}

                            {!showRegister ? (
                                // Login Form
                                <form onSubmit={handleLogin}>
                                    <div className="mb-3">
                                        <label htmlFor="username" className="form-label">Username</label>
                                        <input
                                            type="text"
                                            className="form-control"
                                            id="username"
                                            value={username}
                                            onChange={(e) => setUsername(e.target.value)}
                                            required
                                        />
                                    </div>
                                    <div className="mb-3">
                                        <label htmlFor="password" className="form-label">Password</label>
                                        <input
                                            type="password"
                                            className="form-control"
                                            id="password"
                                            value={password}
                                            onChange={(e) => setPassword(e.target.value)}
                                            required
                                        />
                                    </div>
                                    <div className="d-grid gap-2">
                                        <button
                                            type="submit"
                                            className="btn btn-primary"
                                            disabled={isLoading}
                                        >
                                            {isLoading ? (
                                                <>
                                                    <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                                    Logging in...
                                                </>
                                            ) : (
                                                'Login'
                                            )}
                                        </button>
                                    </div>
                                </form>
                            ) : (
                                // Registration Form
                                <form onSubmit={handleRegister}>
                                    <div className="row">
                                        <div className="col-md-6 mb-3">
                                            <label htmlFor="firstName" className="form-label">First Name</label>
                                            <input
                                                type="text"
                                                className="form-control"
                                                id="firstName"
                                                name="firstName"
                                                value={registerData.firstName}
                                                onChange={handleRegisterInputChange}
                                                required
                                            />
                                        </div>
                                        <div className="col-md-6 mb-3">
                                            <label htmlFor="lastName" className="form-label">Last Name</label>
                                            <input
                                                type="text"
                                                className="form-control"
                                                id="lastName"
                                                name="lastName"
                                                value={registerData.lastName}
                                                onChange={handleRegisterInputChange}
                                                required
                                            />
                                        </div>
                                    </div>
                                    <div className="mb-3">
                                        <label htmlFor="registerUsername" className="form-label">Username</label>
                                        <input
                                            type="text"
                                            className="form-control"
                                            id="registerUsername"
                                            name="username"
                                            value={registerData.username}
                                            onChange={handleRegisterInputChange}
                                            required
                                        />
                                    </div>
                                    <div className="mb-3">
                                        <label htmlFor="email" className="form-label">Email</label>
                                        <input
                                            type="email"
                                            className="form-control"
                                            id="email"
                                            name="email"
                                            value={registerData.email}
                                            onChange={handleRegisterInputChange}
                                            required
                                        />
                                    </div>
                                    <div className="mb-3">
                                        <label htmlFor="registerPassword" className="form-label">Password</label>
                                        <input
                                            type="password"
                                            className="form-control"
                                            id="registerPassword"
                                            name="password"
                                            value={registerData.password}
                                            onChange={handleRegisterInputChange}
                                            required
                                        />
                                    </div>
                                    <div className="mb-3">
                                        <label htmlFor="confirmPassword" className="form-label">Confirm Password</label>
                                        <input
                                            type="password"
                                            className="form-control"
                                            id="confirmPassword"
                                            name="confirmPassword"
                                            value={registerData.confirmPassword}
                                            onChange={handleRegisterInputChange}
                                            required
                                        />
                                    </div>
                                    <div className="d-grid gap-2">
                                        <button
                                            type="submit"
                                            className="btn btn-primary"
                                            disabled={isLoading}
                                        >
                                            {isLoading ? (
                                                <>
                                                    <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                                    Registering...
                                                </>
                                            ) : (
                                                'Register'
                                            )}
                                        </button>
                                    </div>
                                </form>
                            )}
                        </div>
                        <div className="card-footer text-center">
                            {!showRegister ? (
                                <p className="mb-0">
                                    Don't have an account?{' '}
                                    <button
                                        className="btn btn-link p-0"
                                        onClick={() => setShowRegister(true)}
                                    >
                                        Register
                                    </button>
                                </p>
                            ) : (
                                <p className="mb-0">
                                    Already have an account?{' '}
                                    <button
                                        className="btn btn-link p-0"
                                        onClick={() => setShowRegister(false)}
                                    >
                                        Login
                                    </button>
                                </p>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Login;
