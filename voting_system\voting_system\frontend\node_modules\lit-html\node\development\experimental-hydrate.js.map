{"version": 3, "file": "experimental-hydrate.js", "sources": ["../../src/experimental-hydrate.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport type {TemplateResult} from './lit-html.js';\n\nimport {noChange, RenderOptions, _$LH} from './lit-html.js';\nimport {AttributePartInfo, PartType} from './directive.js';\nimport {\n  isPrimitive,\n  isSingleExpression,\n  isTemplateResult,\n  isCompiledTemplateResult,\n} from './directive-helpers.js';\n\n// In the Node build, this import will be injected by Rollup:\n// import {Buffer} from 'buffer';\n\nconst NODE_MODE = false;\n\nconst {\n  _TemplateInstance: TemplateInstance,\n  _isIterable: isIterable,\n  _resolveDirective: resolveDirective,\n  _ChildPart: ChildPart,\n  _ElementPart: ElementPart,\n} = _$LH;\n\ntype ChildPart = InstanceType<typeof ChildPart>;\ntype TemplateInstance = InstanceType<typeof TemplateInstance>;\n\n/**\n * Information needed to rehydrate a single TemplateResult.\n */\ntype ChildPartState =\n  | {\n      type: 'leaf';\n      /** The ChildPart that the result is rendered to */\n      part: ChildPart;\n    }\n  | {\n      type: 'iterable';\n      /** The ChildPart that the result is rendered to */\n      part: ChildPart;\n      value: Iterable<unknown>;\n      iterator: Iterator<unknown>;\n      done: boolean;\n    }\n  | {\n      type: 'template-instance';\n      /** The ChildPart that the result is rendered to */\n      part: ChildPart;\n\n      result: TemplateResult;\n\n      /** The TemplateInstance created from the TemplateResult */\n      instance: TemplateInstance;\n\n      /**\n       * The index of the next Template part to be hydrated. This is mutable and\n       * updated as the tree walk discovers new part markers at the right level in\n       * the template instance tree.  Note there is only one Template part per\n       * attribute with (one or more) bindings.\n       */\n      templatePartIndex: number;\n\n      /**\n       * The index of the next TemplateInstance part to be hydrated. This is used\n       * to retrieve the value from the TemplateResult and initialize the\n       * TemplateInstance parts' values for dirty-checking on first render.\n       */\n      instancePartIndex: number;\n    };\n\n/**\n * hydrate() operates on a container with server-side rendered content and\n * restores the client side data structures needed for lit-html updates such as\n * TemplateInstances and Parts. After calling `hydrate`, lit-html will behave as\n * if it initially rendered the DOM, and any subsequent updates will update\n * efficiently, the same as if lit-html had rendered the DOM on the client.\n *\n * hydrate() must be called on DOM that adheres the to lit-ssr structure for\n * parts. ChildParts must be represented with both a start and end comment\n * marker, and ChildParts that contain a TemplateInstance must have the template\n * digest written into the comment data.\n *\n * Since render() encloses its output in a ChildPart, there must always be a root\n * ChildPart.\n *\n * Example (using for # ... for annotations in HTML)\n *\n * Given this input:\n *\n *   html`<div class=${x}>${y}</div>`\n *\n * The SSR DOM is:\n *\n *   <!--lit-part AEmR7W+R0Ak=-->  # Start marker for the root ChildPart created\n *                                 # by render(). Includes the digest of the\n *                                 # template\n *   <div class=\"TEST_X\">\n *     <!--lit-node 0--> # Indicates there are attribute bindings here\n *                           # The number is the depth-first index of the parent\n *                           # node in the template.\n *     <!--lit-part-->  # Start marker for the ${x} expression\n *     TEST_Y\n *     <!--/lit-part-->  # End marker for the ${x} expression\n *   </div>\n *\n *   <!--/lit-part-->  # End marker for the root ChildPart\n *\n * @param rootValue\n * @param container\n * @param userOptions\n *\n * @deprecated This has been moved to `@lit-labs/ssr-client` and will be removed\n * in a future release.\n */\nexport const hydrate = (\n  rootValue: unknown,\n  container: Element | DocumentFragment,\n  options: Partial<RenderOptions> = {}\n) => {\n  console.warn(\n    'Importing `hydrate()` from `lit-html/experimental-hydrate.js` is deprecated.' +\n      'Import from `@lit-labs/ssr-client` instead.'\n  );\n\n  // TODO(kschaaf): Do we need a helper for _$litPart$ (\"part for node\")?\n  // This property needs to remain unminified.\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  if ((container as any)['_$litPart$'] !== undefined) {\n    throw new Error('container already contains a live render');\n  }\n\n  // Since render() creates a ChildPart to render into, we'll always have\n  // exactly one root part. We need to hold a reference to it so we can set\n  // it in the parts cache.\n  let rootPart: ChildPart | undefined = undefined;\n\n  // Used for error messages\n  let rootPartMarker: Comment | undefined = undefined;\n\n  // When we are in-between ChildPart markers, this is the current ChildPart.\n  // It's needed to be able to set the ChildPart's endNode when we see a\n  // close marker\n  let currentChildPart: ChildPart | undefined = undefined;\n\n  // Used to remember parent template state as we recurse into nested\n  // templates\n  const stack: Array<ChildPartState> = [];\n\n  const walker = document.createTreeWalker(\n    container,\n    NodeFilter.SHOW_COMMENT,\n    null,\n    false\n  );\n  let marker: Comment | null;\n\n  // Walk the DOM looking for part marker comments\n  while ((marker = walker.nextNode() as Comment | null) !== null) {\n    const markerText = marker.data;\n    if (markerText.startsWith('lit-part')) {\n      if (stack.length === 0 && rootPart !== undefined) {\n        throw new Error(\n          `There must be only one root part per container. ` +\n            `Found a part marker (${marker}) when we already have a root ` +\n            `part marker (${rootPartMarker})`\n        );\n      }\n      // Create a new ChildPart and push it onto the stack\n      currentChildPart = openChildPart(rootValue, marker, stack, options);\n      rootPart ??= currentChildPart;\n      rootPartMarker ??= marker;\n    } else if (markerText.startsWith('lit-node')) {\n      // Create and hydrate attribute parts into the current ChildPart on the\n      // stack\n      createAttributeParts(marker, stack, options);\n    } else if (markerText.startsWith('/lit-part')) {\n      // Close the current ChildPart, and pop the previous one off the stack\n      if (stack.length === 1 && currentChildPart !== rootPart) {\n        throw new Error('internal error');\n      }\n      currentChildPart = closeChildPart(marker, currentChildPart, stack);\n    }\n  }\n  if (rootPart === undefined) {\n    const elementMessage =\n      container instanceof ShadowRoot\n        ? `{container.host.localName}'s shadow root`\n        : container instanceof DocumentFragment\n        ? 'DocumentFragment'\n        : container.localName;\n    console.error(\n      `There should be exactly one root part in a render container, ` +\n        `but we didn't find any in ${elementMessage}.`\n    );\n  } // This property needs to remain unminified.\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  (container as any)['_$litPart$'] = rootPart;\n};\n\nconst openChildPart = (\n  rootValue: unknown,\n  marker: Comment,\n  stack: Array<ChildPartState>,\n  options: RenderOptions\n) => {\n  let value: unknown;\n  // We know the startNode now. We'll know the endNode when we get to\n  // the matching marker and set it in closeChildPart()\n  // TODO(kschaaf): Current constructor takes both nodes\n  let part;\n  if (stack.length === 0) {\n    part = new ChildPart(marker, null, undefined, options);\n    value = rootValue;\n  } else {\n    const state = stack[stack.length - 1];\n    if (state.type === 'template-instance') {\n      part = new ChildPart(marker, null, state.instance, options);\n      state.instance._$parts.push(part);\n      value = state.result.values[state.instancePartIndex++];\n      state.templatePartIndex++;\n    } else if (state.type === 'iterable') {\n      part = new ChildPart(marker, null, state.part, options);\n      const result = state.iterator.next();\n      if (result.done) {\n        value = undefined;\n        state.done = true;\n        throw new Error('Unhandled shorter than expected iterable');\n      } else {\n        value = result.value;\n      }\n      (state.part._$committedValue as Array<ChildPart>).push(part);\n    } else {\n      // state.type === 'leaf'\n      // TODO(kschaaf): This is unexpected, and likely a result of a primitive\n      // been rendered on the client when a TemplateResult was rendered on the\n      // server; this part will be hydrated but not used. We can detect it, but\n      // we need to decide what to do in this case. Note that this part won't be\n      // retained by any parent TemplateInstance, since a primitive had been\n      // rendered in its place.\n      // https://github.com/lit/lit/issues/1434\n      // throw new Error('Hydration value mismatch: Found a TemplateInstance' +\n      //  'where a leaf value was expected');\n      part = new ChildPart(marker, null, state.part, options);\n    }\n  }\n\n  // Initialize the ChildPart state depending on the type of value and push\n  // it onto the stack. This logic closely follows the ChildPart commit()\n  // cascade order:\n  // 1. directive\n  // 2. noChange\n  // 3. primitive (note strings must be handled before iterables, since they\n  //    are iterable)\n  // 4. TemplateResult\n  // 5. Node (not yet implemented, but fallback handling is fine)\n  // 6. Iterable\n  // 7. nothing (handled in fallback)\n  // 8. Fallback for everything else\n  value = resolveDirective(part, value);\n  if (value === noChange) {\n    stack.push({part, type: 'leaf'});\n  } else if (isPrimitive(value)) {\n    stack.push({part, type: 'leaf'});\n    part._$committedValue = value;\n    // TODO(kschaaf): We can detect when a primitive is being hydrated on the\n    // client where a TemplateResult was rendered on the server, but we need to\n    // decide on a strategy for what to do next.\n    // https://github.com/lit/lit/issues/1434\n    // if (marker.data !== 'lit-part') {\n    //   throw new Error('Hydration value mismatch: Primitive found where TemplateResult expected');\n    // }\n  } else if (isTemplateResult(value)) {\n    if (isCompiledTemplateResult(value)) {\n      throw new Error('compiled templates are not supported');\n    }\n    // Check for a template result digest\n    const markerWithDigest = `lit-part ${digestForTemplateResult(value)}`;\n    if (marker.data === markerWithDigest) {\n      const template = ChildPart.prototype._$getTemplate(value);\n      const instance = new TemplateInstance(template, part);\n      stack.push({\n        type: 'template-instance',\n        instance,\n        part,\n        templatePartIndex: 0,\n        instancePartIndex: 0,\n        result: value,\n      });\n      // For TemplateResult values, we set the part value to the\n      // generated TemplateInstance\n      part._$committedValue = instance;\n    } else {\n      // TODO: if this isn't the server-rendered template, do we\n      // need to stop hydrating this subtree? Clear it? Add tests.\n      throw new Error(\n        'Hydration value mismatch: Unexpected TemplateResult rendered to part'\n      );\n    }\n  } else if (isIterable(value)) {\n    // currentChildPart.value will contain an array of ChildParts\n    stack.push({\n      part: part,\n      type: 'iterable',\n      value,\n      iterator: value[Symbol.iterator](),\n      done: false,\n    });\n    part._$committedValue = [];\n  } else {\n    // Fallback for everything else (nothing, Objects, Functions,\n    // etc.): we just initialize the part's value\n    // Note that `Node` value types are not currently supported during\n    // SSR, so that part of the cascade is missing.\n    stack.push({part: part, type: 'leaf'});\n    part._$committedValue = value == null ? '' : value;\n  }\n  return part;\n};\n\nconst closeChildPart = (\n  marker: Comment,\n  part: ChildPart | undefined,\n  stack: Array<ChildPartState>\n): ChildPart | undefined => {\n  if (part === undefined) {\n    throw new Error('unbalanced part marker');\n  }\n\n  part._$endNode = marker;\n\n  const currentState = stack.pop()!;\n\n  if (currentState.type === 'iterable') {\n    if (!currentState.iterator.next().done) {\n      throw new Error('unexpected longer than expected iterable');\n    }\n  }\n\n  if (stack.length > 0) {\n    const state = stack[stack.length - 1];\n    return state.part;\n  } else {\n    return undefined;\n  }\n};\n\nconst createAttributeParts = (\n  comment: Comment,\n  stack: Array<ChildPartState>,\n  options: RenderOptions\n) => {\n  // Get the nodeIndex from DOM. We're only using this for an integrity\n  // check right now, we might not need it.\n  const match = /lit-node (\\d+)/.exec(comment.data)!;\n  const nodeIndex = parseInt(match[1]);\n\n  // Node markers are added as a previous sibling to identify elements\n  // with attribute/property/element/event bindings or custom elements\n  // whose `defer-hydration` attribute needs to be removed\n  const node = comment.nextElementSibling;\n  if (node === null) {\n    throw new Error('could not find node for attribute parts');\n  }\n  // Remove `defer-hydration` attribute, if any\n  node.removeAttribute('defer-hydration');\n\n  const state = stack[stack.length - 1];\n  if (state.type === 'template-instance') {\n    const instance = state.instance;\n    // eslint-disable-next-line no-constant-condition\n    while (true) {\n      // If the next template part is in attribute-position on the current node,\n      // create the instance part for it and prime its state\n      const templatePart = instance._$template.parts[state.templatePartIndex];\n      if (\n        templatePart === undefined ||\n        (templatePart.type !== PartType.ATTRIBUTE &&\n          templatePart.type !== PartType.ELEMENT) ||\n        templatePart.index !== nodeIndex\n      ) {\n        break;\n      }\n\n      if (templatePart.type === PartType.ATTRIBUTE) {\n        // The instance part is created based on the constructor saved in the\n        // template part\n        const instancePart = new templatePart.ctor(\n          node as HTMLElement,\n          templatePart.name,\n          templatePart.strings,\n          state.instance,\n          options\n        );\n\n        const value = isSingleExpression(\n          instancePart as unknown as AttributePartInfo\n        )\n          ? state.result.values[state.instancePartIndex]\n          : state.result.values;\n\n        // Setting the attribute value primes committed value with the resolved\n        // directive value; we only then commit that value for event/property\n        // parts since those were not serialized, and pass `noCommit` for the\n        // others to avoid perf impact of touching the DOM unnecessarily\n        const noCommit = !(\n          instancePart.type === PartType.EVENT ||\n          instancePart.type === PartType.PROPERTY\n        );\n        instancePart._$setValue(\n          value,\n          instancePart,\n          state.instancePartIndex,\n          noCommit\n        );\n        state.instancePartIndex += templatePart.strings.length - 1;\n        instance._$parts.push(instancePart);\n      } else {\n        // templatePart.type === PartType.ELEMENT\n        const instancePart = new ElementPart(node, state.instance, options);\n        resolveDirective(\n          instancePart,\n          state.result.values[state.instancePartIndex++]\n        );\n        instance._$parts.push(instancePart);\n      }\n      state.templatePartIndex++;\n    }\n  } else {\n    throw new Error('internal error');\n  }\n};\n\n// Number of 32 bit elements to use to create template digests\nconst digestSize = 2;\n// We need to specify a digest to use across rendering environments. This is a\n// simple digest build from a DJB2-ish hash modified from:\n// https://github.com/darkskyapp/string-hash/blob/master/index.js\n// It has been changed to an array of hashes to add additional bits.\n// Goals:\n//  - Extremely low collision rate. We may not be able to detect collisions.\n//  - Extremely fast.\n//  - Extremely small code size.\n//  - Safe to include in HTML comment text or attribute value.\n//  - Easily specifiable and implementable in multiple languages.\n// We don't care about cryptographic suitability.\nexport const digestForTemplateResult = (templateResult: TemplateResult) => {\n  const hashes = new Uint32Array(digestSize).fill(5381);\n\n  for (const s of templateResult.strings) {\n    for (let i = 0; i < s.length; i++) {\n      hashes[i % digestSize] = (hashes[i % digestSize] * 33) ^ s.charCodeAt(i);\n    }\n  }\n  const str = String.fromCharCode(...new Uint8Array(hashes.buffer));\n  // Use `btoa` in browsers because it is supported universally.\n  //\n  // In Node, we are sometimes executing in an isolated VM context, which means\n  // neither `btoa` nor `Buffer` will be globally available by default (also\n  // note that `btoa` is only supported in Node 16+ anyway, and we still support\n  // Node 14). Instead of requiring users to always provide an implementation\n  // for `btoa` when they set up their VM context, we instead inject an import\n  // for `Buffer` from Node's built-in `buffer` module in our Rollup config (see\n  // note at the top of this file), and use that.\n  return NODE_MODE ? Buffer.from(str, 'binary').toString('base64') : btoa(str);\n};\n"], "names": [], "mappings": ";;;;;AAsBA,MAAM,EACJ,iBAAiB,EAAE,gBAAgB,EACnC,WAAW,EAAE,UAAU,EACvB,iBAAiB,EAAE,gBAAgB,EACnC,UAAU,EAAE,SAAS,EACrB,YAAY,EAAE,WAAW,GAC1B,GAAG,IAAI,CAAC;AAgDT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2CG;AACI,MAAM,OAAO,GAAG,CACrB,SAAkB,EAClB,SAAqC,EACrC,OAAA,GAAkC,EAAE,KAClC;IACF,OAAO,CAAC,IAAI,CACV,8EAA8E;AAC5E,QAAA,6CAA6C,CAChD,CAAC;;;;AAKF,IAAA,IAAK,SAAiB,CAAC,YAAY,CAAC,KAAK,SAAS,EAAE;AAClD,QAAA,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;AAC7D,KAAA;;;;IAKD,IAAI,QAAQ,GAA0B,SAAS,CAAC;;IAGhD,IAAI,cAAc,GAAwB,SAAS,CAAC;;;;IAKpD,IAAI,gBAAgB,GAA0B,SAAS,CAAC;;;IAIxD,MAAM,KAAK,GAA0B,EAAE,CAAC;AAExC,IAAA,MAAM,MAAM,GAAG,QAAQ,CAAC,gBAAgB,CACtC,SAAS,EACT,UAAU,CAAC,YAAY,EACvB,IAAI,EACJ,KAAK,CACN,CAAC;AACF,IAAA,IAAI,MAAsB,CAAC;;IAG3B,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,QAAQ,EAAoB,MAAM,IAAI,EAAE;AAC9D,QAAA,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC;AAC/B,QAAA,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;YACrC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,QAAQ,KAAK,SAAS,EAAE;gBAChD,MAAM,IAAI,KAAK,CACb,CAAkD,gDAAA,CAAA;AAChD,oBAAA,CAAA,qBAAA,EAAwB,MAAM,CAAgC,8BAAA,CAAA;oBAC9D,CAAgB,aAAA,EAAA,cAAc,CAAG,CAAA,CAAA,CACpC,CAAC;AACH,aAAA;;YAED,gBAAgB,GAAG,aAAa,CAAC,SAAS,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;YACpE,QAAQ,KAAA,IAAA,IAAR,QAAQ,KAAR,KAAA,CAAA,GAAA,QAAQ,IAAR,QAAQ,GAAK,gBAAgB,CAAC,CAAA;YAC9B,cAAc,KAAA,IAAA,IAAd,cAAc,KAAd,KAAA,CAAA,GAAA,cAAc,IAAd,cAAc,GAAK,MAAM,CAAC,CAAA;AAC3B,SAAA;AAAM,aAAA,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;;;AAG5C,YAAA,oBAAoB,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;AAC9C,SAAA;AAAM,aAAA,IAAI,UAAU,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE;;YAE7C,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,gBAAgB,KAAK,QAAQ,EAAE;AACvD,gBAAA,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;AACnC,aAAA;YACD,gBAAgB,GAAG,cAAc,CAAC,MAAM,EAAE,gBAAgB,EAAE,KAAK,CAAC,CAAC;AACpE,SAAA;AACF,KAAA;IACD,IAAI,QAAQ,KAAK,SAAS,EAAE;AAC1B,QAAA,MAAM,cAAc,GAClB,SAAS,YAAY,UAAU;AAC7B,cAAE,CAA0C,wCAAA,CAAA;cAC1C,SAAS,YAAY,gBAAgB;AACvC,kBAAE,kBAAkB;AACpB,kBAAE,SAAS,CAAC,SAAS,CAAC;QAC1B,OAAO,CAAC,KAAK,CACX,CAA+D,6DAAA,CAAA;YAC7D,CAA6B,0BAAA,EAAA,cAAc,CAAG,CAAA,CAAA,CACjD,CAAC;AACH,KAAA;;AAEA,IAAA,SAAiB,CAAC,YAAY,CAAC,GAAG,QAAQ,CAAC;AAC9C,EAAE;AAEF,MAAM,aAAa,GAAG,CACpB,SAAkB,EAClB,MAAe,EACf,KAA4B,EAC5B,OAAsB,KACpB;AACF,IAAA,IAAI,KAAc,CAAC;;;;AAInB,IAAA,IAAI,IAAI,CAAC;AACT,IAAA,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;AACtB,QAAA,IAAI,GAAG,IAAI,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QACvD,KAAK,GAAG,SAAS,CAAC;AACnB,KAAA;AAAM,SAAA;QACL,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACtC,QAAA,IAAI,KAAK,CAAC,IAAI,KAAK,mBAAmB,EAAE;AACtC,YAAA,IAAI,GAAG,IAAI,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAC5D,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAClC,YAAA,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC,CAAC;YACvD,KAAK,CAAC,iBAAiB,EAAE,CAAC;AAC3B,SAAA;AAAM,aAAA,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU,EAAE;AACpC,YAAA,IAAI,GAAG,IAAI,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YACxD,MAAM,MAAM,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;YACrC,IAAI,MAAM,CAAC,IAAI,EAAE;gBACf,KAAK,GAAG,SAAS,CAAC;AAClB,gBAAA,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;AAClB,gBAAA,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;AAC7D,aAAA;AAAM,iBAAA;AACL,gBAAA,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;AACtB,aAAA;YACA,KAAK,CAAC,IAAI,CAAC,gBAAqC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC9D,SAAA;AAAM,aAAA;;;;;;;;;;;AAWL,YAAA,IAAI,GAAG,IAAI,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AACzD,SAAA;AACF,KAAA;;;;;;;;;;;;;AAcD,IAAA,KAAK,GAAG,gBAAgB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACtC,IAAI,KAAK,KAAK,QAAQ,EAAE;QACtB,KAAK,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAC,CAAC,CAAC;AAClC,KAAA;AAAM,SAAA,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE;QAC7B,KAAK,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAC,CAAC,CAAC;AACjC,QAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;;;;;;;;AAQ/B,KAAA;AAAM,SAAA,IAAI,gBAAgB,CAAC,KAAK,CAAC,EAAE;AAClC,QAAA,IAAI,wBAAwB,CAAC,KAAK,CAAC,EAAE;AACnC,YAAA,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;AACzD,SAAA;;QAED,MAAM,gBAAgB,GAAG,CAAY,SAAA,EAAA,uBAAuB,CAAC,KAAK,CAAC,EAAE,CAAC;AACtE,QAAA,IAAI,MAAM,CAAC,IAAI,KAAK,gBAAgB,EAAE;YACpC,MAAM,QAAQ,GAAG,SAAS,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAC1D,MAAM,QAAQ,GAAG,IAAI,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YACtD,KAAK,CAAC,IAAI,CAAC;AACT,gBAAA,IAAI,EAAE,mBAAmB;gBACzB,QAAQ;gBACR,IAAI;AACJ,gBAAA,iBAAiB,EAAE,CAAC;AACpB,gBAAA,iBAAiB,EAAE,CAAC;AACpB,gBAAA,MAAM,EAAE,KAAK;AACd,aAAA,CAAC,CAAC;;;AAGH,YAAA,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC;AAClC,SAAA;AAAM,aAAA;;;AAGL,YAAA,MAAM,IAAI,KAAK,CACb,sEAAsE,CACvE,CAAC;AACH,SAAA;AACF,KAAA;AAAM,SAAA,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE;;QAE5B,KAAK,CAAC,IAAI,CAAC;AACT,YAAA,IAAI,EAAE,IAAI;AACV,YAAA,IAAI,EAAE,UAAU;YAChB,KAAK;AACL,YAAA,QAAQ,EAAE,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE;AAClC,YAAA,IAAI,EAAE,KAAK;AACZ,SAAA,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;AAC5B,KAAA;AAAM,SAAA;;;;;AAKL,QAAA,KAAK,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAC,CAAC,CAAC;AACvC,QAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK,IAAI,IAAI,GAAG,EAAE,GAAG,KAAK,CAAC;AACpD,KAAA;AACD,IAAA,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF,MAAM,cAAc,GAAG,CACrB,MAAe,EACf,IAA2B,EAC3B,KAA4B,KACH;IACzB,IAAI,IAAI,KAAK,SAAS,EAAE;AACtB,QAAA,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;AAC3C,KAAA;AAED,IAAA,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;AAExB,IAAA,MAAM,YAAY,GAAG,KAAK,CAAC,GAAG,EAAG,CAAC;AAElC,IAAA,IAAI,YAAY,CAAC,IAAI,KAAK,UAAU,EAAE;QACpC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE;AACtC,YAAA,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;AAC7D,SAAA;AACF,KAAA;AAED,IAAA,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;QACpB,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACtC,OAAO,KAAK,CAAC,IAAI,CAAC;AACnB,KAAA;AAAM,SAAA;AACL,QAAA,OAAO,SAAS,CAAC;AAClB,KAAA;AACH,CAAC,CAAC;AAEF,MAAM,oBAAoB,GAAG,CAC3B,OAAgB,EAChB,KAA4B,EAC5B,OAAsB,KACpB;;;IAGF,MAAM,KAAK,GAAG,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAE,CAAC;IACnD,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;;;;AAKrC,IAAA,MAAM,IAAI,GAAG,OAAO,CAAC,kBAAkB,CAAC;IACxC,IAAI,IAAI,KAAK,IAAI,EAAE;AACjB,QAAA,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;AAC5D,KAAA;;AAED,IAAA,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC;IAExC,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACtC,IAAA,IAAI,KAAK,CAAC,IAAI,KAAK,mBAAmB,EAAE;AACtC,QAAA,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;;AAEhC,QAAA,OAAO,IAAI,EAAE;;;AAGX,YAAA,MAAM,YAAY,GAAG,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;YACxE,IACE,YAAY,KAAK,SAAS;AAC1B,iBAAC,YAAY,CAAC,IAAI,KAAK,QAAQ,CAAC,SAAS;AACvC,oBAAA,YAAY,CAAC,IAAI,KAAK,QAAQ,CAAC,OAAO,CAAC;AACzC,gBAAA,YAAY,CAAC,KAAK,KAAK,SAAS,EAChC;gBACA,MAAM;AACP,aAAA;AAED,YAAA,IAAI,YAAY,CAAC,IAAI,KAAK,QAAQ,CAAC,SAAS,EAAE;;;gBAG5C,MAAM,YAAY,GAAG,IAAI,YAAY,CAAC,IAAI,CACxC,IAAmB,EACnB,YAAY,CAAC,IAAI,EACjB,YAAY,CAAC,OAAO,EACpB,KAAK,CAAC,QAAQ,EACd,OAAO,CACR,CAAC;AAEF,gBAAA,MAAM,KAAK,GAAG,kBAAkB,CAC9B,YAA4C,CAC7C;sBACG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC;AAC9C,sBAAE,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC;;;;;gBAMxB,MAAM,QAAQ,GAAG,EACf,YAAY,CAAC,IAAI,KAAK,QAAQ,CAAC,KAAK;AACpC,oBAAA,YAAY,CAAC,IAAI,KAAK,QAAQ,CAAC,QAAQ,CACxC,CAAC;AACF,gBAAA,YAAY,CAAC,UAAU,CACrB,KAAK,EACL,YAAY,EACZ,KAAK,CAAC,iBAAiB,EACvB,QAAQ,CACT,CAAC;gBACF,KAAK,CAAC,iBAAiB,IAAI,YAAY,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;AAC3D,gBAAA,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AACrC,aAAA;AAAM,iBAAA;;AAEL,gBAAA,MAAM,YAAY,GAAG,IAAI,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AACpE,gBAAA,gBAAgB,CACd,YAAY,EACZ,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC,CAC/C,CAAC;AACF,gBAAA,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AACrC,aAAA;YACD,KAAK,CAAC,iBAAiB,EAAE,CAAC;AAC3B,SAAA;AACF,KAAA;AAAM,SAAA;AACL,QAAA,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;AACnC,KAAA;AACH,CAAC,CAAC;AAEF;AACA,MAAM,UAAU,GAAG,CAAC,CAAC;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACa,MAAA,uBAAuB,GAAG,CAAC,cAA8B,KAAI;AACxE,IAAA,MAAM,MAAM,GAAG,IAAI,WAAW,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAEtD,IAAA,KAAK,MAAM,CAAC,IAAI,cAAc,CAAC,OAAO,EAAE;AACtC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACjC,MAAM,CAAC,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,UAAU,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC1E,SAAA;AACF,KAAA;AACD,IAAA,MAAM,GAAG,GAAG,MAAM,CAAC,YAAY,CAAC,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;;;;;;;;;;IAUlE,OAAmB,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAY,CAAC;AAC/E;;;;"}