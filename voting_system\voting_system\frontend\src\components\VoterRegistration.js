import React, { useState, useEffect } from 'react';

const VoterRegistration = ({ onSubmit, account }) => {
    const [formData, setFormData] = useState({
        id_number: '',
        date_of_birth: '',
        phone_number: '',
        constituency: '',
        polling_station: '',
    });
    const [counties, setCounties] = useState([]);
    const [constituencies, setConstituencies] = useState([]);
    const [pollingStations, setPollingStations] = useState([]);
    const [selectedCounty, setSelectedCounty] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState('');
    const [formErrors, setFormErrors] = useState({});
    // State for ID verification
    const [idVerificationStatus, setIdVerificationStatus] = useState({
        checked: false,
        exists: false,
        verified: false,
        valid: false,
        message: ''
    });

    // Fetch counties on component mount
    useEffect(() => {
        const fetchCounties = async () => {
            try {
                const response = await fetch('/api/counties/');

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                // Make sure data is an array
                if (Array.isArray(data)) {
                    setCounties(data);
                } else {
                    console.warn('Counties data is not an array:', data);
                    // If API returns demo data in a different format, use demo counties
                    setCounties(getDemoCounties());
                }
            } catch (error) {
                console.error('Error fetching counties:', error);
                setError('Error fetching counties. Using demo data.');
                // Use demo counties when API fails
                setCounties(getDemoCounties());
            }
        };

        fetchCounties();
    }, []);

    // Function to get demo counties data
    const getDemoCounties = () => {
        return [
            { id: 1, name: 'Nairobi' },
            { id: 2, name: 'Mombasa' },
            { id: 3, name: 'Kisumu' },
            { id: 4, name: 'Nakuru' },
            { id: 5, name: 'Kiambu' },
            { id: 6, name: 'Uasin Gishu' },
            { id: 7, name: 'Machakos' },
            { id: 8, name: 'Kajiado' }
        ];
    };

    // Fetch constituencies when county is selected
    useEffect(() => {
        if (selectedCounty) {
            const fetchConstituencies = async () => {
                try {
                    const response = await fetch(`/api/counties/${selectedCounty}/constituencies/`);

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const data = await response.json();

                    // Make sure data is an array
                    if (Array.isArray(data)) {
                        setConstituencies(data);
                    } else {
                        console.warn('Constituencies data is not an array:', data);
                        // If API returns demo data in a different format, use demo constituencies
                        setConstituencies(getDemoConstituencies(selectedCounty));
                    }
                } catch (error) {
                    console.error('Error fetching constituencies:', error);
                    setError('Error fetching constituencies. Using demo data.');
                    // Use demo constituencies when API fails
                    setConstituencies(getDemoConstituencies(selectedCounty));
                }
            };

            fetchConstituencies();
        } else {
            setConstituencies([]);
        }
    }, [selectedCounty]);

    // Function to get demo constituencies data
    const getDemoConstituencies = (countyId) => {
        const demoConstituenciesMap = {
            '1': [ // Nairobi
                { id: 1, name: 'Westlands', county: 1 },
                { id: 2, name: 'Dagoretti North', county: 1 },
                { id: 3, name: 'Langata', county: 1 },
                { id: 4, name: 'Kibra', county: 1 }
            ],
            '2': [ // Mombasa
                { id: 5, name: 'Nyali', county: 2 },
                { id: 6, name: 'Kisauni', county: 2 },
                { id: 7, name: 'Likoni', county: 2 }
            ],
            '3': [ // Kisumu
                { id: 8, name: 'Kisumu Central', county: 3 },
                { id: 9, name: 'Kisumu East', county: 3 },
                { id: 10, name: 'Kisumu West', county: 3 }
            ],
            '4': [ // Nakuru
                { id: 11, name: 'Nakuru Town East', county: 4 },
                { id: 12, name: 'Nakuru Town West', county: 4 },
                { id: 13, name: 'Naivasha', county: 4 }
            ]
        };

        return demoConstituenciesMap[countyId] || [
            { id: 100, name: 'Demo Constituency 1', county: countyId },
            { id: 101, name: 'Demo Constituency 2', county: countyId },
            { id: 102, name: 'Demo Constituency 3', county: countyId }
        ];
    };

    // Fetch polling stations when constituency is selected
    useEffect(() => {
        console.log("Constituency useEffect triggered with:", formData.constituency);

        if (formData.constituency) {
            const fetchPollingStations = async () => {
                try {
                    console.log(`Fetching polling stations for constituency ID: ${formData.constituency}`);
                    // Ensure constituency ID is a number
                    const constituencyId = typeof formData.constituency === 'number'
                        ? formData.constituency
                        : parseInt(formData.constituency, 10);

                    const response = await fetch(`/api/constituencies/${constituencyId}/polling-stations/`);

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const data = await response.json();
                    console.log("Polling stations API response:", data);

                    // Make sure data is an array
                    if (Array.isArray(data)) {
                        setPollingStations(data);
                        console.log("Set polling stations from API:", data);
                    } else {
                        console.warn('Polling stations data is not an array:', data);
                        // If API returns demo data in a different format, use demo polling stations
                        const demoStations = getDemoPollingStations(constituencyId);
                        setPollingStations(demoStations);
                        console.log("Set demo polling stations:", demoStations);
                    }
                } catch (error) {
                    console.error('Error fetching polling stations:', error);
                    setError('Error fetching polling stations. Using demo data.');
                    // Use demo polling stations when API fails
                    const demoStations = getDemoPollingStations(formData.constituency);
                    setPollingStations(demoStations);
                    console.log("Set demo polling stations after error:", demoStations);
                }
            };

            fetchPollingStations();
        } else {
            setPollingStations([]);
            console.log("Cleared polling stations because no constituency is selected");
        }
    }, [formData.constituency]);

    // Function to get demo polling stations data
    const getDemoPollingStations = (constituencyId) => {
        // Generate 3 polling stations for each constituency
        return [
            { id: parseInt(constituencyId) * 100 + 1, name: `Polling Station A - ${constituencyId}`, constituency: parseInt(constituencyId) },
            { id: parseInt(constituencyId) * 100 + 2, name: `Polling Station B - ${constituencyId}`, constituency: parseInt(constituencyId) },
            { id: parseInt(constituencyId) * 100 + 3, name: `Polling Station C - ${constituencyId}`, constituency: parseInt(constituencyId) }
        ];
    };

    // Check if ID number exists and is verified
    const checkIdNumber = async (idNumber) => {
        if (!idNumber || idNumber.length < 8) return;

        try {
            const response = await fetch('/api/check-id-number/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ id_number: idNumber }),
            });

            const data = await response.json();
            console.log('ID verification response:', data);

            setIdVerificationStatus({
                checked: true,
                exists: data.success,
                verified: data.verified,
                valid: data.valid,
                message: data.message
            });

            // If ID is already registered and verified, show error
            if (data.success && data.verified) {
                setFormErrors(prev => ({
                    ...prev,
                    id_number: 'This ID number is already registered and verified.'
                }));
            }

            // If ID is not valid (not in national database), show error
            if (!data.valid) {
                setFormErrors(prev => ({
                    ...prev,
                    id_number: 'This ID number is not registered in the national database.'
                }));
            }

            return data;
        } catch (error) {
            console.error('Error checking ID number:', error);
            setIdVerificationStatus({
                checked: true,
                exists: false,
                verified: false,
                valid: false,
                message: 'Error checking ID number. Please try again.'
            });
        }
    };

    // Handle form input changes
    const handleChange = (e) => {
        const { name, value } = e.target;
        console.log(`Field ${name} changed to: ${value}`);

        // Convert ID values to numbers for dropdown selections
        let processedValue = value;
        if ((name === 'constituency' || name === 'polling_station') && value !== '') {
            processedValue = parseInt(value, 10);
            console.log(`Converted ${name} value to number: ${processedValue}`);
        }

        setFormData(prevData => {
            const newData = {
                ...prevData,
                [name]: processedValue
            };
            console.log("Updated form data:", newData);
            return newData;
        });

        // Clear errors for this field
        if (formErrors[name]) {
            setFormErrors(prev => ({
                ...prev,
                [name]: ''
            }));
        }

        // If ID number changed and is valid length, check if it exists
        if (name === 'id_number' && value.length === 8) {
            checkIdNumber(value);
        }

        // If constituency changed, log it and clear polling station
        if (name === 'constituency') {
            console.log(`Constituency changed to: ${processedValue}`);
            // Reset polling station when constituency changes
            if (formData.polling_station) {
                setFormData(prevData => ({
                    ...prevData,
                    polling_station: ''
                }));
            }
        }

        // If polling_station changed, log it
        if (name === 'polling_station') {
            console.log(`Polling station changed to: ${processedValue}`);
        }
    };

    // Handle county selection
    const handleCountyChange = (e) => {
        const value = e.target.value;
        // Convert to number if not empty
        const countyId = value !== '' ? parseInt(value, 10) : '';
        console.log("Selected county ID:", countyId);
        setSelectedCounty(countyId);
        // Reset constituency and polling station
        setFormData(prevData => ({
            ...prevData,
            constituency: '',
            polling_station: ''
        }));
    };

    // Validate form
    const validateForm = () => {
        const errors = {};

        if (!formData.id_number) {
            errors.id_number = 'ID number is required';
        } else if (!/^\d{8}$/.test(formData.id_number)) {
            errors.id_number = 'ID number must be 8 digits';
        } else if (idVerificationStatus.checked && idVerificationStatus.verified) {
            // If ID is already registered and verified, show error
            errors.id_number = 'This ID number is already registered and verified.';
        } else if (idVerificationStatus.checked && !idVerificationStatus.valid) {
            // If ID is not valid (not in national database), show error
            errors.id_number = 'This ID number is not registered in the national database.';
        }

        if (!formData.date_of_birth) {
            errors.date_of_birth = 'Date of birth is required';
        } else {
            const dob = new Date(formData.date_of_birth);
            const today = new Date();
            const age = today.getFullYear() - dob.getFullYear();
            if (age < 18) {
                errors.date_of_birth = 'You must be at least 18 years old to register';
            }
        }

        if (!formData.phone_number) {
            errors.phone_number = 'Phone number is required';
        } else if (!/^0\d{9}$/.test(formData.phone_number)) {
            errors.phone_number = 'Phone number must be in the format 07XXXXXXXX or 01XXXXXXXX';
        }

        if (!formData.constituency) {
            errors.constituency = 'Constituency is required';
        }

        if (!formData.polling_station) {
            errors.polling_station = 'Polling station is required';
        }

        return errors;
    };

    // Handle form submission
    const handleSubmit = async (e) => {
        e.preventDefault();

        // Validate form
        const errors = validateForm();
        if (Object.keys(errors).length > 0) {
            setFormErrors(errors);
            return;
        }

        setIsLoading(true);
        setError('');

        try {
            await onSubmit(formData);
        } catch (error) {
            console.error('Error submitting form:', error);
            setError('Error submitting form. Please try again.');
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="row justify-content-center">
            <div className="col-md-8">
                <div className="card">
                    <div className="card-header bg-primary text-white">
                        <h3 className="mb-0">Voter Registration</h3>
                    </div>
                    <div className="card-body">
                        {error && (
                            <div className="alert alert-danger" role="alert">
                                {error}
                            </div>
                        )}

                        <div className="alert alert-primary mb-4">
                            <h5 className="alert-heading">Account vs. Voter Registration</h5>
                            <p>This form is for registering as a voter. Having an account allows you to use the system, but you need to register as a voter to participate in elections.</p>
                            <hr />
                            <p className="mb-0">If you don't have an account yet, please <a href="#" onClick={(e) => { e.preventDefault(); window.location.href = '/signup'; }}>create an account</a> first.</p>
                        </div>

                        <p className="lead">Please complete the form below to register as a voter.</p>

                        {account ? (
                            <div className="alert alert-info">
                                <strong>Blockchain Address:</strong> <small>{account}</small>
                                <p className="mb-0 mt-2">This address will be linked to your voter registration.</p>
                            </div>
                        ) : (
                            <div className="alert alert-warning">
                                Please connect your Ethereum wallet to register.
                            </div>
                        )}

                        <form onSubmit={handleSubmit}>
                            {/* Already Registered Voter Section */}
                            <div className="alert alert-info mb-4">
                                <h5 className="alert-heading">Already Registered?</h5>
                                <p>If you have already registered as a voter, you can directly proceed to vote.</p>
                                <hr />
                                <button
                                    type="button"
                                    className="btn btn-primary"
                                    onClick={() => window.location.href = '/'}
                                >
                                    Go to Elections
                                </button>
                            </div>

                            <div className="mb-3">
                                <label htmlFor="id_number" className="form-label">National ID Number</label>
                                <div className="input-group">
                                    <input
                                        type="text"
                                        className={`form-control ${formErrors.id_number ? 'is-invalid' : ''} ${idVerificationStatus.checked && idVerificationStatus.verified ? 'is-valid' : ''}`}
                                        id="id_number"
                                        name="id_number"
                                        value={formData.id_number}
                                        onChange={handleChange}
                                        placeholder="Enter your ID number"
                                        autoComplete="off"
                                    />
                                    <button
                                        className="btn btn-outline-secondary"
                                        type="button"
                                        onClick={() => checkIdNumber(formData.id_number)}
                                        disabled={!formData.id_number || formData.id_number.length < 8}
                                    >
                                        Verify
                                    </button>
                                </div>
                                {formErrors.id_number && (
                                    <div className="invalid-feedback d-block">{formErrors.id_number}</div>
                                )}
                                {idVerificationStatus.checked && !formErrors.id_number && (
                                    <div className={`mt-1 ${idVerificationStatus.verified ? 'text-success' : 'text-warning'}`}>
                                        {idVerificationStatus.message}
                                    </div>
                                )}
                            </div>

                            <div className="mb-3">
                                <label htmlFor="date_of_birth" className="form-label">Date of Birth</label>
                                <input
                                    type="date"
                                    className={`form-control ${formErrors.date_of_birth ? 'is-invalid' : ''}`}
                                    id="date_of_birth"
                                    name="date_of_birth"
                                    value={formData.date_of_birth}
                                    onChange={handleChange}
                                    autoComplete="off"
                                />
                                {formErrors.date_of_birth && (
                                    <div className="invalid-feedback d-block">{formErrors.date_of_birth}</div>
                                )}
                            </div>

                            <div className="mb-3">
                                <label htmlFor="phone_number" className="form-label">Phone Number</label>
                                <input
                                    type="text"
                                    className={`form-control ${formErrors.phone_number ? 'is-invalid' : ''}`}
                                    id="phone_number"
                                    name="phone_number"
                                    value={formData.phone_number}
                                    onChange={handleChange}
                                    placeholder="e.g., 0712345678"
                                    autoComplete="off"
                                />
                                {formErrors.phone_number && (
                                    <div className="invalid-feedback d-block">{formErrors.phone_number}</div>
                                )}
                            </div>

                            <div className="mb-3">
                                <label htmlFor="county" className="form-label">County</label>
                                <select
                                    className="form-select"
                                    id="county"
                                    value={selectedCounty}
                                    onChange={handleCountyChange}
                                    autoComplete="off"
                                >
                                    <option value="">Select County</option>
                                    {Array.isArray(counties) && counties.map(county => (
                                        <option key={county.id} value={county.id}>
                                            {county.name}
                                        </option>
                                    ))}
                                </select>
                            </div>

                            <div className="mb-3">
                                <label htmlFor="constituency" className="form-label">Constituency</label>
                                <select
                                    className={`form-select ${formErrors.constituency ? 'is-invalid' : ''}`}
                                    id="constituency"
                                    name="constituency"
                                    value={formData.constituency}
                                    onChange={(e) => {
                                        handleChange(e);
                                        // When constituency changes, fetch polling stations
                                        if (e.target.value) {
                                            const constituencyPollingStations = getPollingStations(e.target.value);
                                            setPollingStations(constituencyPollingStations);
                                            // Reset polling station selection
                                            setFormData(prev => ({
                                                ...prev,
                                                polling_station: ''
                                            }));
                                        } else {
                                            setPollingStations([]);
                                        }
                                    }}
                                    disabled={!selectedCounty}
                                    autoComplete="off"
                                >
                                    <option value="">Select Constituency</option>
                                    {Array.isArray(constituencies) && constituencies.map(constituency => (
                                        <option key={constituency.id} value={constituency.id}>
                                            {constituency.name}
                                        </option>
                                    ))}
                                </select>
                                {formErrors.constituency && (
                                    <div className="invalid-feedback d-block">{formErrors.constituency}</div>
                                )}
                            </div>

                            <div className="mb-3">
                                <label htmlFor="polling_station" className="form-label">Polling Station</label>
                                <select
                                    className={`form-select ${formErrors.polling_station ? 'is-invalid' : ''}`}
                                    id="polling_station"
                                    name="polling_station"
                                    value={formData.polling_station}
                                    onChange={handleChange}
                                    disabled={!formData.constituency}
                                    autoComplete="off"
                                >
                                    <option value="">Select Polling Station</option>
                                    {Array.isArray(pollingStations) && pollingStations.map(station => (
                                        <option key={station.id} value={station.id}>
                                            {station.name}
                                        </option>
                                    ))}
                                </select>
                                {formErrors.polling_station && (
                                    <div className="invalid-feedback d-block">{formErrors.polling_station}</div>
                                )}
                            </div>

                            <div className="d-grid gap-2">
                                <button
                                    type="submit"
                                    className="btn btn-primary"
                                    disabled={isLoading || !account}
                                >
                                    {isLoading ? (
                                        <>
                                            <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                            <span className="ms-2">Registering...</span>
                                        </>
                                    ) : (
                                        'Register as Voter'
                                    )}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default VoterRegistration;
