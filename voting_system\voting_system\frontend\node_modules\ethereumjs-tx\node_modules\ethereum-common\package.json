{"name": "ethereum-common", "version": "0.0.18", "description": "Resources common to all Ethereum implementations", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/ethereumjs/common.git"}, "keywords": ["ethereum"], "author": "", "license": "MIT", "bugs": {"url": "https://github.com/ethereumjs/common/issues"}, "homepage": "https://github.com/ethereumjs/common", "maintainers": [{"name": "null_radix", "email": "<EMAIL>"}]}