System.register(["react","proxy-compare","use-sync-external-store/shim","valtio/vanilla"],function(w){"use strict";var s,a,o,i,f,p,l,h,y,c;return{setters:[function(e){s=e.useRef,a=e.useCallback,o=e.useEffect,i=e.useMemo,f=e.default},function(e){p=e.isChanged,l=e.createProxy},function(e){h=e.default},function(e){y=e.subscribe,c=e.snapshot}],execute:function(){w("useSnapshot",W);const{use:e}=f,{useSyncExternalStore:v}=h,C=new WeakMap;function W(t,b){const k=b==null?void 0:b.sync,r=s(),u=s();let x=!0;const M=v(a(n=>{const E=y(t,n,k);return n(),E},[t,k]),()=>{const n=c(t,e);try{if(!x&&r.current&&u.current&&!p(r.current,n,u.current,new WeakMap))return r.current}catch{}return n},()=>c(t,e));x=!1;const S=new WeakMap;o(()=>{r.current=M,u.current=S});const g=i(()=>new WeakMap,[]);return l(M,S,g,C)}}}});
