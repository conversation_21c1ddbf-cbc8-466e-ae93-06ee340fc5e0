# Working with JavaScript Values

`node-addon-api` provides a set of classes that allow to create and manage
JavaScript object:

- [Function](function.md)
  - [FunctionReference](function_reference.md)
- [ObjectWrap](object_wrap.md)
  - [ClassPropertyDescriptor](class_property_descriptor.md)
- [Buffer](buffer.md)
- [ArrayBuffer](array_buffer.md)
- [TypedArray](typed_array.md)
  - [TypedArrayOf](typed_array_of.md)
- [DataView](dataview.md)
