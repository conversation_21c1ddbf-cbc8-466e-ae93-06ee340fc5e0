"""
Generated by `compile_contracts.py` script.
Compiled with Solidity v0.8.19.
"""

# source: web3/_utils/contract_sources/PayableTester.sol:PayableTesterContract
PAYABLE_TESTER_CONTRACT_BYTECODE = "0x608060405234801561001057600080fd5b5060ee8061001f6000396000f3fe6080604052348015600f57600080fd5b506004361060325760003560e01c8063c6803622146037578063e4cb8f5c146051575b600080fd5b603d6059565b60405160489190609f565b60405180910390f35b6057606a565b005b60008054906101000a900460ff1681565b60016000806101000a81548160ff021916908315150217905550565b60008115159050919050565b6099816086565b82525050565b600060208201905060b260008301846092565b9291505056fea2646970667358221220887e1ab76e4be9d44aa3db38f90b4f33d009a6a8925cf974837b77c38a58031564736f6c63430008130033"  # noqa: E501
PAYABLE_TESTER_CONTRACT_RUNTIME = "0x6080604052348015600f57600080fd5b506004361060325760003560e01c8063c6803622146037578063e4cb8f5c146051575b600080fd5b603d6059565b60405160489190609f565b60405180910390f35b6057606a565b005b60008054906101000a900460ff1681565b60016000806101000a81548160ff021916908315150217905550565b60008115159050919050565b6099816086565b82525050565b600060208201905060b260008301846092565b9291505056fea2646970667358221220887e1ab76e4be9d44aa3db38f90b4f33d009a6a8925cf974837b77c38a58031564736f6c63430008130033"  # noqa: E501
PAYABLE_TESTER_CONTRACT_ABI = [
    {
        "inputs": [],
        "name": "doNoValueCall",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "wasCalled",
        "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
        "stateMutability": "view",
        "type": "function",
    },
]
PAYABLE_TESTER_CONTRACT_DATA = {
    "bytecode": PAYABLE_TESTER_CONTRACT_BYTECODE,
    "bytecode_runtime": PAYABLE_TESTER_CONTRACT_RUNTIME,
    "abi": PAYABLE_TESTER_CONTRACT_ABI,
}
