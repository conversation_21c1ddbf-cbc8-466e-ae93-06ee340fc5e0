export*from"@lit/reactive-element";export*from"lit-html";export{LitElement,UpdatingElement,_$LE}from"./lit-element.js";export*from"@lit/reactive-element/decorators/base.js";export*from"@lit/reactive-element/decorators/custom-element.js";export*from"@lit/reactive-element/decorators/property.js";export*from"@lit/reactive-element/decorators/state.js";export*from"@lit/reactive-element/decorators/event-options.js";export*from"@lit/reactive-element/decorators/query.js";export*from"@lit/reactive-element/decorators/query-all.js";export*from"@lit/reactive-element/decorators/query-async.js";export*from"@lit/reactive-element/decorators/query-assigned-elements.js";export*from"@lit/reactive-element/decorators/query-assigned-nodes.js";
/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */console.warn("The main 'lit-element' module entrypoint is deprecated. Please update your imports to use the 'lit' package: 'lit' and 'lit/decorators.ts' or import from 'lit-element/lit-element.ts'. See https://lit.dev/msg/deprecated-import-path for more information.");
//# sourceMappingURL=index.js.map
