from django.contrib import admin
from django.contrib.auth.models import User
from .models import (
    County, Constituency, PollingStation, Party,
    ElectionType, Election, Voter, Candidate, Vote, ValidNationalID, Worker
)

@admin.register(County)
class CountyAdmin(admin.ModelAdmin):
    list_display = ('name', 'code')
    search_fields = ('name', 'code')

@admin.register(Constituency)
class ConstituencyAdmin(admin.ModelAdmin):
    list_display = ('name', 'county', 'code')
    list_filter = ('county',)
    search_fields = ('name', 'code')

@admin.register(PollingStation)
class PollingStationAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'constituency', 'location')
    list_filter = ('constituency__county', 'constituency')
    search_fields = ('name', 'code', 'location')

@admin.register(Party)
class PartyAdmin(admin.ModelAdmin):
    list_display = ('name', 'abbreviation', 'registration_number')
    search_fields = ('name', 'abbreviation', 'registration_number')

@admin.register(ElectionType)
class ElectionTypeAdmin(admin.ModelAdmin):
    list_display = ('name', 'description')
    search_fields = ('name', 'description')

@admin.register(Election)
class ElectionAdmin(admin.ModelAdmin):
    list_display = ('title', 'election_type', 'start_date', 'end_date', 'active')
    list_filter = ('election_type', 'active')
    search_fields = ('title', 'description')
    readonly_fields = ('contract_election_id',)

@admin.register(Worker)
class WorkerAdmin(admin.ModelAdmin):
    list_display = ('user', 'employee_id', 'phone_number', 'station_assigned', 'is_active', 'date_created')
    list_filter = ('is_active', 'station_assigned', 'date_created')
    search_fields = ('user__username', 'user__first_name', 'user__last_name', 'employee_id')
    readonly_fields = ('date_created',)

    def save_model(self, request, obj, form, change):
        if not change:  # If creating a new worker
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(Voter)
class VoterAdmin(admin.ModelAdmin):
    list_display = ('user', 'id_number', 'constituency', 'polling_station', 'registered_by', 'is_verified', 'registration_date')
    list_filter = ('is_verified', 'constituency__county', 'constituency', 'registered_by', 'registration_date')
    search_fields = ('user__username', 'user__first_name', 'user__last_name', 'id_number', 'phone_number')
    readonly_fields = ('registration_date', 'verification_date')

    fieldsets = (
        ('Personal Information', {
            'fields': ('user', 'id_number', 'date_of_birth', 'phone_number')
        }),
        ('Location', {
            'fields': ('constituency', 'polling_station')
        }),
        ('Documents', {
            'fields': ('id_picture', 'voter_photo')
        }),
        ('Registration Details', {
            'fields': ('registered_by', 'is_verified', 'registration_date', 'verification_date', 'verified_by')
        }),
    )

    def save_model(self, request, obj, form, change):
        if obj.is_verified and not obj.verification_date:
            from django.utils import timezone
            obj.verification_date = timezone.now()
            obj.verified_by = request.user
        super().save_model(request, obj, form, change)

@admin.register(Candidate)
class CandidateAdmin(admin.ModelAdmin):
    list_display = ('user', 'party', 'election', 'constituency', 'county', 'is_independent', 'is_approved', 'votes_count')
    list_filter = ('election', 'party', 'is_independent', 'is_approved', 'constituency__county')
    search_fields = ('user__username', 'user__first_name', 'user__last_name', 'bio')
    readonly_fields = ('votes_count', 'contract_candidate_id')

@admin.register(Vote)
class VoteAdmin(admin.ModelAdmin):
    list_display = ('voter', 'candidate', 'election', 'polling_station', 'timestamp')
    list_filter = ('election', 'polling_station__constituency__county', 'polling_station__constituency')
    search_fields = ('voter__user__username', 'candidate__user__username')
    readonly_fields = ('timestamp', 'transaction_hash')

@admin.register(ValidNationalID)
class ValidNationalIDAdmin(admin.ModelAdmin):
    list_display = ('id_number', 'full_name', 'date_of_birth', 'is_active', 'date_added')
    list_filter = ('is_active', 'date_added')
    search_fields = ('id_number', 'full_name')
    readonly_fields = ('date_added',)
