System.register(["@babel/helper-module-imports","@babel/types","aslemammad-vite-plugin-macro","babel-plugin-macros"],function(o){"use strict";var l,t,a,u;return{setters:[function(e){l=e},function(e){t=e},function(e){a=e},function(e){u=e}],execute:function(){o("provideValtioMacro",s);const{defineMacro:e,defineMacroProvider:b,createMacroPlugin:h}="default"in a?a.default:a,M=o("valtioMacro",e("useProxy").withSignature("<T extends object>(proxyObject: T): void").withHandler(g=>{var d,m,v,f;const{path:r,args:x}=g,P=l.addNamed(r,"useSnapshot","valtio"),n=(d=x[0])==null?void 0:d.node;if(!t.isIdentifier(n))throw new u.MacroError("no proxy object");const p=t.identifier(`valtio_macro_snap_${n.name}`);(m=r.parentPath)==null||m.replaceWith(t.variableDeclaration("const",[t.variableDeclarator(p,t.callExpression(P,[n]))]));let i=0;(f=(v=r.parentPath)==null?void 0:v.getFunctionParent())==null||f.traverse({Identifier(c){i===0&&c.node!==n&&c.node.name===n.name&&(c.node.name=p.name)},Function:{enter(){++i},exit(){--i}}})}));function s(){return b({id:"valtio/macro",exports:{"valtio/macro":{macros:[M]}}})}const y=o("default",h({}).use(s()))}}});
