{"manifest_version": "2", "version": "1.0.0", "package_name": "wallet", "sources": {"./contracts/Wallet.sol": "ipfs://QmdvZEW3AaUntDfFkcbdnYzeLAAeD4YFeixQsdmHF88T6Q"}, "contract_types": {"Wallet": {"deployment_bytecode": {"bytecode": "0x608060405260008054600160a060020a031916331790556102bf806100256000396000f3006080604052600436106100565763ffffffff7c0100000000000000000000000000000000000000000000000000000000600035041663095ea7b381146100655780632e1a7d4d146100aa578063d0679d34146100c2575b34801561006257600080fd5b50005b34801561007157600080fd5b5061009673ffffffffffffffffffffffffffffffffffffffff600435166024356100f3565b604080519115158252519081900360200190f35b3480156100b657600080fd5b5061009660043561014a565b3480156100ce57600080fd5b5061009673ffffffffffffffffffffffffffffffffffffffff60043516602435610236565b6000805473ffffffffffffffffffffffffffffffffffffffff16331461011857600080fd5b5073ffffffffffffffffffffffffffffffffffffffff8216600090815260016020819052604090912082905592915050565b3360009081526001602090815260408083205481517fa293d1e800000000000000000000000000000000000000000000000000000000815260048101919091526024810185905290517300000000000000000000000000000000000000009263a293d1e89260448082019391829003018186803b1580156101ca57600080fd5b505af41580156101de573d6000803e3d6000fd5b505050506040513d60208110156101f457600080fd5b505133600081815260016020526040808220939093559151909184156108fc02918591818181858888f19350505050151561022e57600080fd5b506001919050565b6000805473ffffffffffffffffffffffffffffffffffffffff16331461025b57600080fd5b60405173ffffffffffffffffffffffffffffffffffffffff84169083156108fc029084906000818181858888f19796505050505050505600a165627a7a723058207816bfaab53d55eff94c08cb3e5655a597fb47b8b10256bc7b52b12105f3095e0029", "link_references": [{"offsets": [442], "length": 20, "name": "SafeMathLib"}]}, "runtime_bytecode": {"bytecode": "0x6080604052600436106100565763ffffffff7c0100000000000000000000000000000000000000000000000000000000600035041663095ea7b381146100655780632e1a7d4d146100aa578063d0679d34146100c2575b34801561006257600080fd5b50005b34801561007157600080fd5b5061009673ffffffffffffffffffffffffffffffffffffffff600435166024356100f3565b604080519115158252519081900360200190f35b3480156100b657600080fd5b5061009660043561014a565b3480156100ce57600080fd5b5061009673ffffffffffffffffffffffffffffffffffffffff60043516602435610236565b6000805473ffffffffffffffffffffffffffffffffffffffff16331461011857600080fd5b5073ffffffffffffffffffffffffffffffffffffffff8216600090815260016020819052604090912082905592915050565b3360009081526001602090815260408083205481517fa293d1e800000000000000000000000000000000000000000000000000000000815260048101919091526024810185905290517300000000000000000000000000000000000000009263a293d1e89260448082019391829003018186803b1580156101ca57600080fd5b505af41580156101de573d6000803e3d6000fd5b505050506040513d60208110156101f457600080fd5b505133600081815260016020526040808220939093559151909184156108fc02918591818181858888f19350505050151561022e57600080fd5b506001919050565b6000805473ffffffffffffffffffffffffffffffffffffffff16331461025b57600080fd5b60405173ffffffffffffffffffffffffffffffffffffffff84169083156108fc029084906000818181858888f19796505050505050505600a165627a7a723058207816bfaab53d55eff94c08cb3e5655a597fb47b8b10256bc7b52b12105f3095e0029", "link_references": [{"offsets": [405], "length": 20, "name": "SafeMathLib"}]}, "abi": [{"constant": false, "inputs": [{"name": "recipient", "type": "address"}, {"name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "value", "type": "uint256"}], "name": "withdraw", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "recipient", "type": "address"}, {"name": "value", "type": "uint256"}], "name": "send", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"payable": false, "stateMutability": "nonpayable", "type": "fallback"}], "natspec": {"compiler": {"name": "solc", "version": "0.4.24+commit.e67f0147.Emscripten.clang", "settings": {"optimize": true}}, "author": "<PERSON>am <<EMAIL>>", "methods": {"approve(address,uint256)": {"details": "Sets recipient to be approved to withdraw the specified amount", "notice": "This will set the recipient to be approved to withdraw the specified amount."}, "send(address,uint256)": {"details": "Sends the recipient the specified amount", "notice": "This will send the reciepient the specified amount."}, "withdraw(uint256)": {"details": "Lets caller withdraw up to their approved amount", "notice": "This will withdraw provided value, deducting it from your total allowance."}}, "title": "Contract for holding funds in escrow between two semi trusted parties."}}}, "deployments": {"blockchain://41941023680923e0fe4d74a34bdac8141f2540e3ae90623718e47d66d1ca4a2d/block/0d2426efb30377a18129af05d887f9ba76bff85482000bd19e3630eb09a8bf39": {"Wallet": {"contract_type": "Wallet", "address": "******************************************", "transaction": "0xaf122c215282dd340d686a662d60239f43700e15127b44e68dd6a00c48b9c88a", "block": "0x0a2d01614f4dadd0b7eeeb945d5bab534825911a7769a6d2da8adb72e91b2531", "runtime_bytecode": {"link_dependencies": [{"offsets": [405], "type": "reference", "value": "safe-math-lib:SafeMathLib"}]}}}}, "build_dependencies": {"owned": "ipfs://QmbeVyFLSuEUxiXKwSsEjef6icpdTdA4kGG9BcrJXKNKUW", "safe-math-lib": "ipfs://QmWgvM8yXGyHoGWqLFXvareJsoCZVsdrpKNCLMun3RaSJm"}}