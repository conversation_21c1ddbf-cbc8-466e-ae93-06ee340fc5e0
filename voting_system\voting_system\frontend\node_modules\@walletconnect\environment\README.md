# environment [![npm version](https://badge.fury.io/js/%40pedrouid%2Fenvironment.svg)](https://badge.fury.io/js/%40pedrouid%2Fenvironment)

Environment utilities

## Description

This library provides utilties to detect environment for Browser, NodeJS and React-Native

## Usage

```typescript
import * as env from 'environment';

// true for NodeJS
env.isNode();

// true for React-Native
env.isReactNative();

// true for Browser
env.isBrowser();
```

## License

[MIT License](LICENSE.md)
