{"version": 3, "file": "query.js", "sources": ["../../src/decorators/query.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/*\n * IMPORTANT: For compatibility with <PERSON><PERSON><PERSON> and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\n\nimport {ReactiveElement} from '../reactive-element.js';\nimport {decorateProperty} from './base.js';\n\n/**\n * A property decorator that converts a class property into a getter that\n * executes a querySelector on the element's renderRoot.\n *\n * @param selector A DOMString containing one or more selectors to match.\n * @param cache An optional boolean which when true performs the DOM query only\n *     once and caches the result.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/Document/querySelector\n *\n * ```ts\n * class MyElement {\n *   @query('#first')\n *   first: HTMLDivElement;\n *\n *   render() {\n *     return html`\n *       <div id=\"first\"></div>\n *       <div id=\"second\"></div>\n *     `;\n *   }\n * }\n * ```\n * @category Decorator\n */\nexport function query(selector: string, cache?: boolean) {\n  return decorateProperty({\n    descriptor: (name: PropertyKey) => {\n      const descriptor = {\n        get(this: ReactiveElement) {\n          return this.renderRoot?.querySelector(selector) ?? null;\n        },\n        enumerable: true,\n        configurable: true,\n      };\n      if (cache) {\n        const key = typeof name === 'symbol' ? Symbol() : `__${name}`;\n        descriptor.get = function (this: ReactiveElement) {\n          if (\n            (this as unknown as {[key: string]: Element | null})[\n              key as string\n            ] === undefined\n          ) {\n            (this as unknown as {[key: string]: Element | null})[\n              key as string\n            ] = this.renderRoot?.querySelector(selector) ?? null;\n          }\n          return (this as unknown as {[key: string]: Element | null})[\n            key as string\n          ];\n        };\n      }\n      return descriptor;\n    },\n  });\n}\n"], "names": ["query", "selector", "cache", "decorateProperty", "descriptor", "name", "get", "_b", "_a", "this", "renderRoot", "querySelector", "enumerable", "configurable", "key", "Symbol", "undefined"], "mappings": ";;;;;GAyCgB,SAAAA,EAAMC,EAAkBC,GACtC,OAAOC,EAAiB,CACtBC,WAAaC,IACX,MAAMD,EAAa,CACjBE,cACE,OAAmD,QAA5CC,EAAe,QAAfC,EAAAC,KAAKC,kBAAU,IAAAF,OAAA,EAAAA,EAAEG,cAAcV,UAAa,IAAAM,EAAAA,EAAA,IACpD,EACDK,YAAY,EACZC,cAAc,GAEhB,GAAIX,EAAO,CACT,MAAMY,EAAsB,iBAATT,EAAoBU,SAAW,KAAKV,EACvDD,EAAWE,IAAM,mBAUf,YANQU,IAFLP,KACCK,KAGDL,KACCK,GAC0C,QAAxCP,EAAe,UAAfE,KAAKC,kBAAU,IAAAF,OAAA,EAAAA,EAAEG,cAAcV,UAAS,IAAAM,EAAAA,EAAI,MAE1CE,KACNK,EAEJ,CACD,CACD,OAAOV,CAAU,GAGvB"}