# This file is distributed under the same license as the Django package.
#
# Translators:
# 534b44a19bf18d20b71ecc4eb77c572f_db336e9 <f8268c65f822ec11a3a2e5d482cd7ead_175>, 2011-2012
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2012-2015,2017,2019,2023,2025
# <AUTHOR> <EMAIL>, 2020
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 11:30-0500\n"
"PO-Revision-Date: 2025-04-01 15:04-0500\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, "
"2012-2015,2017,2019,2023,2025\n"
"Language-Team: Hebrew (http://app.transifex.com/django/django/language/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: he\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % "
"1 == 0) ? 1: (n % 10 == 0 && n % 1 == 0 && n > 10) ? 2 : 3;\n"

msgid "Personal info"
msgstr "מידע אישי"

msgid "Permissions"
msgstr "הרשאות"

msgid "Important dates"
msgstr "תאריכים חשובים"

#, python-format
msgid "%(name)s object with primary key %(key)r does not exist."
msgstr "הפריט %(name)s עם המפתח הראשי %(key)r אינו קיים."

msgid "Conflicting form data submitted. Please try again."
msgstr "נשלחו נתונים סותרים בטופס. נא לנסות שוב."

msgid "Password changed successfully."
msgstr "הסיסמה שונתה בהצלחה."

msgid "Password-based authentication was disabled."
msgstr "אימות מבוסס סיסמה הושבת."

#, python-format
msgid "Change password: %s"
msgstr "שינוי סיסמה: %s"

#, python-format
msgid "Set password: %s"
msgstr "קביעת סיסמה: %s"

msgid "Authentication and Authorization"
msgstr "אימות והרשאות"

msgid "password"
msgstr "סיסמה"

msgid "last login"
msgstr "כניסה אחרונה"

msgid "Invalid password format or unknown hashing algorithm."
msgstr "תחביר סיסמה בלתי-חוקי או אלגוריתם גיבוב לא ידוע."

msgid "No password set."
msgstr "לא נקבעה סיסמה."

msgid "Reset password"
msgstr "איפוס סיסמה"

msgid "Set password"
msgstr "קביעת סיסמה"

msgid "The two password fields didn’t match."
msgstr "שני שדות הסיסמה אינם זהים."

msgid "Password"
msgstr "סיסמה"

msgid "Password confirmation"
msgstr "אימות סיסמה"

msgid "Enter the same password as before, for verification."
msgstr "יש להזין את אותה סיסמה כמו קודם, לאימות."

msgid ""
"Whether the user will be able to authenticate using a password or not. If "
"disabled, they may still be able to authenticate using other backends, such "
"as Single Sign-On or LDAP."
msgstr ""
"האם המשתמש יוכל לבצע אימות באמצעות סיסמה או לא. אם האפשרות מושבתת, ייתכן "
"שעדיין ניתן יהיה לבצע אימות באמצעות מנגנונים אחרים, כגון Single Sign-On או "
"LDAP."

msgid "Password-based authentication"
msgstr "אימות מבוסס סיסמה"

msgid "Enabled"
msgstr "מופעל"

msgid "Disabled"
msgstr "מושבת"

msgid ""
"Raw passwords are not stored, so there is no way to see the user’s password."
msgstr ""
"סיסמאות אינן נשמרות בצורתן המקורית, לכן אין אפשרות לראות את סיסמת המשתמש."

msgid ""
"Enable password-based authentication for this user by setting a password."
msgstr "אפשר אימות מבוסס סיסמה עבור משתמש זה על ידי הגדרת סיסמה."

#, python-format
msgid ""
"Please enter a correct %(username)s and password. Note that both fields may "
"be case-sensitive."
msgstr ""
"נא להזין  %(username)s וסיסמה נכונים. נא לשים לב כי שני השדות רגישים לאותיות "
"גדולות/קטנות."

msgid "This account is inactive."
msgstr "חשבון זה אינו פעיל."

msgid "Email"
msgstr "דוא\"ל"

msgid "New password"
msgstr "סיסמה חדשה"

msgid "New password confirmation"
msgstr "אימות סיסמה חדשה"

msgid "Your old password was entered incorrectly. Please enter it again."
msgstr "סיסמתך הישנה הוזנה בצורה שגויה. נא להזינה שוב."

msgid "Old password"
msgstr "סיסמה ישנה"

msgid "algorithm"
msgstr "אלגוריתם"

msgid "iterations"
msgstr "חזרות"

msgid "salt"
msgstr "salt"

msgid "hash"
msgstr "גיבוב"

msgid "variety"
msgstr "מגוון"

msgid "version"
msgstr "גרסה"

msgid "memory cost"
msgstr "עלות זכרון"

msgid "time cost"
msgstr "עלות זמן"

msgid "parallelism"
msgstr "מקבילות"

msgid "work factor"
msgstr "work factor"

msgid "checksum"
msgstr "סיכום ביקורת"

msgid "block size"
msgstr "גודל בלוק"

msgid "name"
msgstr "שם"

msgid "content type"
msgstr "סוג תוכן"

msgid "codename"
msgstr "שם קוד"

msgid "permission"
msgstr "הרשאה"

msgid "permissions"
msgstr "הרשאות"

msgid "group"
msgstr "קבוצה"

msgid "groups"
msgstr "קבוצות"

msgid "superuser status"
msgstr "סטטוס משתמש על"

msgid ""
"Designates that this user has all permissions without explicitly assigning "
"them."
msgstr "מציין שלמשתמש זה יש את כל ההרשאות ללא הצורך המפורש בהענקתן."

msgid ""
"The groups this user belongs to. A user will get all permissions granted to "
"each of their groups."
msgstr ""
"הקבוצות שמשתמש זה שייך אליהן. משתמש יקבל את כל ההרשאות המוקצות לכל אחת "
"מהקבוצות שלו/שלה."

msgid "user permissions"
msgstr "הרשאות משתמש"

msgid "Specific permissions for this user."
msgstr "הרשאות ספציפיות למשתמש זה."

msgid "username"
msgstr "שם משתמש"

msgid "Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only."
msgstr "שדה חובה. 150 תווים או פחות. אותיות, ספרות ו-@/./+/-/_ בלבד."

msgid "A user with that username already exists."
msgstr "משתמש עם שם משתמש זה קיים כבר"

msgid "first name"
msgstr "שם פרטי"

msgid "last name"
msgstr "שם משפחה"

msgid "email address"
msgstr "כתובת דוא\"ל"

msgid "staff status"
msgstr "סטטוס איש צוות"

msgid "Designates whether the user can log into this admin site."
msgstr "מציין האם המשתמש יכול להתחבר לאתר הניהול."

msgid "active"
msgstr "פעיל"

msgid ""
"Designates whether this user should be treated as active. Unselect this "
"instead of deleting accounts."
msgstr ""
"מציין האם יש להתייחס למשתמש כפעיל. יש לבטל בחירה זו במקום למחוק חשבונות "
"משתמשים."

msgid "date joined"
msgstr "תאריך הצטרפות"

msgid "user"
msgstr "משתמש"

msgid "users"
msgstr "משתמשים"

#, python-format
msgid "This password is too short. It must contain at least %d character."
msgid_plural ""
"This password is too short. It must contain at least %d characters."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#, python-format
msgid "Your password must contain at least %(min_length)d character."
msgid_plural "Your password must contain at least %(min_length)d characters."
msgstr[0] "הסיסמה שלך חייבת להכיל לפחות תו %(min_length)d."
msgstr[1] "הסיסמה שלך חייבת להכיל לפחות %(min_length)d תווים."
msgstr[2] "הסיסמה שלך חייבת להכיל לפחות %(min_length)d תווים."

#, python-format
msgid "The password is too similar to the %(verbose_name)s."
msgstr "סיסמה זו דומה מדי ל-%(verbose_name)s."

msgid "Your password can’t be too similar to your other personal information."
msgstr "הסיסמה שלך לא יכולה להיות דומה מדי למידע אישי אחר שלך."

msgid "This password is too common."
msgstr "סיסמה זו נפוצה מדי."

msgid "Your password can’t be a commonly used password."
msgstr "הסיסמה שלך לא יכולה להיות סיסמה שכיחה."

msgid "This password is entirely numeric."
msgstr "סיסמה זו מכילה רק ספרות."

msgid "Your password can’t be entirely numeric."
msgstr "הסיסמה שלך לא יכולה להכיל רק ספרות."

#, python-format
msgid "Password reset on %(site_name)s"
msgstr "החלפת הסיסמה ב-%(site_name)s"

msgid ""
"Enter a valid username. This value may contain only unaccented lowercase a-z "
"and uppercase A-Z letters, numbers, and @/./+/-/_ characters."
msgstr ""
"יש להזין שם משתמש חוקי. ערך זה יכול להכיל אותיות אנגליות, ספרות והתווים  @/./"
"+/-/_ בלבד."

msgid ""
"Enter a valid username. This value may contain only letters, numbers, and "
"@/./+/-/_ characters."
msgstr ""
"יש להזין שם משתמש חוקי. ערך זה יכול להכיל אותיות, ספרות והתווים @/./+/-/_ "
"בלבד."

msgid "Logged out"
msgstr "יצאת מהמערכת"

msgid "Password reset"
msgstr "איפוס סיסמה"

msgid "Password reset sent"
msgstr "איפוס הסיסמה נשלח."

msgid "Enter new password"
msgstr "הזנת סיסמה חדשה"

msgid "Password reset unsuccessful"
msgstr "איפוס הסיסמה נכשל"

msgid "Password reset complete"
msgstr "איפוס הסיסמה הושלם"

msgid "Password change"
msgstr "שינוי סיסמה"

msgid "Password change successful"
msgstr "הסיסמה שונתה בהצלחה"
