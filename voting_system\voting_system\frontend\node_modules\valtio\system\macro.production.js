System.register(["@babel/helper-module-imports","@babel/types","babel-plugin-macros"],function(h){"use strict";var i,n,l,c;return{setters:[function(e){i=e.addNamed},function(e){n=e},function(e){l=e.createMacro,c=e.MacroError}],execute:function(){var P=h("default",l(({references:g})=>{var s;(s=g.useProxy)==null||s.forEach(a=>{var u,d,m,p,v,f;const y=i(a,"useSnapshot","valtio"),r=(d=(u=a.parentPath)==null?void 0:u.get("arguments.0"))==null?void 0:d.node;if(!n.isIdentifier(r))throw new c("no proxy object");const b=n.identifier(`valtio_macro_snap_${r.name}`);(p=(m=a.parentPath)==null?void 0:m.parentPath)==null||p.replaceWith(n.variableDeclaration("const",[n.variableDeclarator(b,n.callExpression(y,[r]))]));let t=0;(f=(v=a.parentPath)==null?void 0:v.getFunctionParent())==null||f.traverse({Identifier(o){t===0&&o.node!==r&&o.node.name===r.name&&(o.node.name=b.name)},Function:{enter(){++t},exit(){--t}}})})},{configName:"valtio"}))}}});
