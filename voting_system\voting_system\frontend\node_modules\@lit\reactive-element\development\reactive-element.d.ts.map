{"version": 3, "file": "reactive-element.d.ts", "sourceRoot": "", "sources": ["../src/reactive-element.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH;;;;GAIG;AAEH,OAAO,EAGL,cAAc,EACd,iBAAiB,EAClB,MAAM,cAAc,CAAC;AACtB,OAAO,KAAK,EACV,kBAAkB,EAClB,sBAAsB,EACvB,MAAM,0BAA0B,CAAC;AAKlC,cAAc,cAAc,CAAC;AAC7B,YAAY,EACV,kBAAkB,EAClB,sBAAsB,GACvB,MAAM,0BAA0B,CAAC;AAiFlC;;;;;GAKG;AAEH,yBAAiB,gBAAgB,CAAC;IAChC;;;;;;;;OAQG;IAEH,UAAiB,QAAQ,CAAC;QACxB,KAAY,KAAK,GAAG,MAAM,CAAC;QAC3B,UAAiB,MAAM;YACrB,IAAI,EAAE,QAAQ,CAAC;SAChB;KACF;CACF;AAyCD;;GAEG;AACH,MAAM,WAAW,yBAAyB,CAAC,IAAI,GAAG,OAAO,EAAE,QAAQ,GAAG,OAAO;IAC3E;;;OAGG;IACH,aAAa,CAAC,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI,EAAE,IAAI,CAAC,EAAE,QAAQ,GAAG,IAAI,CAAC;IAE5D;;;;;;OAMG;IACH,WAAW,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,QAAQ,GAAG,OAAO,CAAC;CACrD;AAED,aAAK,kBAAkB,CAAC,IAAI,GAAG,OAAO,EAAE,QAAQ,GAAG,OAAO,IACtD,yBAAyB,CAAC,IAAI,CAAC,GAC/B,CAAC,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI,EAAE,IAAI,CAAC,EAAE,QAAQ,KAAK,IAAI,CAAC,CAAC;AAEtD;;GAEG;AACH,MAAM,WAAW,mBAAmB,CAAC,IAAI,GAAG,OAAO,EAAE,QAAQ,GAAG,OAAO;IACrE;;;;;;OAMG;IACH,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC;IAEzB;;;;;;OAMG;IACH,QAAQ,CAAC,SAAS,CAAC,EAAE,OAAO,GAAG,MAAM,CAAC;IAEtC;;;;OAIG;IACH,QAAQ,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAC;IAEzB;;;;;;;;;;;OAWG;IACH,QAAQ,CAAC,SAAS,CAAC,EAAE,kBAAkB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IAExD;;;;;;OAMG;IACH,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC;IAE3B;;;;OAIG;IACH,UAAU,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,GAAG,OAAO,CAAC;IAElD;;;;;;;OAOG;IACH,QAAQ,CAAC,UAAU,CAAC,EAAE,OAAO,CAAC;CAC/B;AAED;;;;GAIG;AACH,MAAM,WAAW,oBAAoB;IACnC,QAAQ,EAAE,GAAG,EAAE,MAAM,GAAG,mBAAmB,CAAC;CAC7C;AAED,aAAK,sBAAsB,GAAG,GAAG,CAAC,WAAW,EAAE,mBAAmB,CAAC,CAAC;AAIpE;;;;;;;;;;GAUG;AAQH,oBAAY,cAAc,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,SAAS,MAAM,GAClD,gBAAgB,CAAC,CAAC,CAAC,GACnB,GAAG,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;AAE9B;;GAEG;AAGH,MAAM,WAAW,gBAAgB,CAAC,CAAC,CAAE,SAAQ,GAAG,CAAC,WAAW,EAAE,OAAO,CAAC;IACpE,GAAG,CAAC,CAAC,SAAS,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,GAAG,CAAC,CAAC,SAAS,MAAM,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IAClD,GAAG,CAAC,CAAC,SAAS,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC;IACtC,MAAM,CAAC,CAAC,SAAS,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC;CAC1C;AAED,eAAO,MAAM,gBAAgB,EAAE,yBAwC9B,CAAC;AAEF,MAAM,WAAW,UAAU;IACzB,CAAC,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,GAAG,OAAO,CAAC;CACzC;AAED;;;GAGG;AACH,eAAO,MAAM,QAAQ,EAAE,UAGtB,CAAC;AAUF;;;;;GAKG;AACH,QAAA,MAAM,SAAS,cAAc,CAAC;AAE9B;;GAEG;AACH,oBAAY,WAAW,GAAG,kBAAkB,GAAG,WAAW,CAAC;AAE3D,oBAAY,WAAW,GAAG,CAAC,OAAO,EAAE,eAAe,KAAK,IAAI,CAAC;AAE7D;;;;;GAKG;AACH,8BAAsB,eASpB,SAAQ,WACR,YAAW,sBAAsB;IAGjC;;;;;;;OAOG;IACH,MAAM,CAAC,eAAe,CAAC,EAAE,WAAW,EAAE,CAAC;IAEvC;;;;;;;;;;;;;;;;OAgBG;IACH,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC,WAAW,EAAE,WAAW,KAAK,IAAI,CAAC;IAE1D;;;;;;;;;;;;;;;;OAgBG;IACH,MAAM,CAAC,cAAc,CAAC,EAAE,CAAC,WAAW,EAAE,WAAW,KAAK,IAAI,CAAC;IAE3D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAgCG;IACH,MAAM,CAAC,cAAc,CAAC,WAAW,EAAE,WAAW;IAK9C,MAAM,CAAC,aAAa,CAAC,EAAE,WAAW,EAAE,CAAC;IAQrC;;;;;OAKG;IACH,OAAO,CAAC,MAAM,CAAC,wBAAwB,CAAe;IAEtD;;OAEG;IACH,SAAS,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,UAAQ;IAEpC;;;;;OAKG;IACH,MAAM,CAAC,iBAAiB,EAAE,sBAAsB,CAAa;IAE7D;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACH,MAAM,CAAC,UAAU,EAAE,oBAAoB,CAAC;IAExC;;;;;OAKG;IACH,MAAM,CAAC,aAAa,EAAE,KAAK,CAAC,iBAAiB,CAAC,CAAM;IAEpD;;;;;;;;;;;;;;;;;;;;;;;;;OAyBG;IACH,MAAM,CAAC,MAAM,CAAC,EAAE,cAAc,CAAC;IAE/B;;;;OAIG;IACH,OAAO,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAmB;IAEzD;;;;OAIG;IACH,MAAM,KAAK,kBAAkB,aAc5B;IAED;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACH,MAAM,CAAC,cAAc,CACnB,IAAI,EAAE,WAAW,EACjB,OAAO,GAAE,mBAAgD;IAoC3D;;;;;;;;;;;;;;;;;;;;;;;;;;OA0BG;IACH,SAAS,CAAC,MAAM,CAAC,qBAAqB,CACpC,IAAI,EAAE,WAAW,EACjB,GAAG,EAAE,MAAM,GAAG,MAAM,EACpB,OAAO,EAAE,mBAAmB,GAC3B,kBAAkB,GAAG,SAAS;IAsBjC;;;;;;;;;;;;;OAaG;IACH,MAAM,CAAC,kBAAkB,CAAC,IAAI,EAAE,WAAW;IAI3C;;;;;OAKG;IACH,SAAS,CAAC,MAAM,CAAC,QAAQ;IAwDzB;;;;;;;;;OASG;IACH,MAAM,CAAC,iBAAiB,EAAE,cAAc,CAAkB;IAE1D;;;;;;;;;;;;;OAaG;IACH,SAAS,CAAC,MAAM,CAAC,cAAc,CAC7B,MAAM,CAAC,EAAE,cAAc,GACtB,KAAK,CAAC,iBAAiB,CAAC;IAiB3B;;;;OAIG;IACH,QAAQ,CAAC,UAAU,EAAG,WAAW,GAAG,UAAU,CAAC;IAE/C;;;OAGG;IACH,OAAO,CAAC,MAAM,CAAC,0BAA0B;IAczC,OAAO,CAAC,oBAAoB,CAAC,CAA6B;IAG1D,OAAO,CAAC,eAAe,CAAoB;IAE3C;;;;OAIG;IACH,eAAe,UAAS;IAExB;;;;OAIG;IACH,UAAU,UAAS;IAUnB;;OAEG;IACH,OAAO,CAAC,sBAAsB,CAAC,CAAwC;IAEvE;;OAEG;IACH,OAAO,CAAC,oBAAoB,CAA4B;IAExD;;OAEG;IACH,OAAO,CAAC,aAAa,CAAC,CAAuB;;IAO7C;;;OAGG;IACH,OAAO,CAAC,YAAY;IAcpB;;;;;;;;OAQG;IACH,aAAa,CAAC,UAAU,EAAE,kBAAkB;IAW5C;;;OAGG;IACH,gBAAgB,CAAC,UAAU,EAAE,kBAAkB;IAM/C;;;;;;;;;;;OAWG;IACH,OAAO,CAAC,wBAAwB;IAahC;;;;;;;;OAQG;IACH,SAAS,CAAC,gBAAgB,IAAI,OAAO,GAAG,UAAU;IAalD;;;;OAIG;IACH,iBAAiB;IAajB;;;;;OAKG;IACH,SAAS,CAAC,cAAc,CAAC,gBAAgB,EAAE,OAAO;IAElD;;;;;OAKG;IACH,oBAAoB;IAIpB;;;;;;;;;;;OAWG;IACH,wBAAwB,CACtB,IAAI,EAAE,MAAM,EACZ,IAAI,EAAE,MAAM,GAAG,IAAI,EACnB,KAAK,EAAE,MAAM,GAAG,IAAI;IAKtB,OAAO,CAAC,qBAAqB;IA6E7B;;;;;;;;;;;;;OAaG;IACH,aAAa,CACX,IAAI,CAAC,EAAE,WAAW,EAClB,QAAQ,CAAC,EAAE,OAAO,EAClB,OAAO,CAAC,EAAE,mBAAmB,GAC5B,IAAI;IAqCP;;OAEG;YACW,eAAe;IAuB7B;;;;;;;;;;;;;;;;OAgBG;IACH,SAAS,CAAC,cAAc,IAAI,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC;IAInD;;;;;;;;;;;;;;;;OAgBG;IACH,SAAS,CAAC,aAAa,IAAI,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC;IAkElD;;;;;;;;;;;;;;;;;;;;OAoBG;IACH,SAAS,CAAC,UAAU,CAAC,kBAAkB,EAAE,cAAc,GAAG,IAAI;IA6B9D,OAAO,CAAC,aAAa;IAKrB;;;;;;;;;;;;;;;OAeG;IACH,IAAI,cAAc,IAAI,OAAO,CAAC,OAAO,CAAC,CAErC;IAED;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACH,SAAS,CAAC,iBAAiB,IAAI,OAAO,CAAC,OAAO,CAAC;IAI/C;;;;;;;OAOG;IACH,SAAS,CAAC,YAAY,CAAC,kBAAkB,EAAE,cAAc,GAAG,OAAO;IAInE;;;;;;;;OAQG;IACH,SAAS,CAAC,MAAM,CAAC,kBAAkB,EAAE,cAAc;IAYnD;;;;;;;;;OASG;IACH,SAAS,CAAC,OAAO,CAAC,kBAAkB,EAAE,cAAc;IAEpD;;;;;;;;;;;;;;;OAeG;IACH,SAAS,CAAC,YAAY,CAAC,kBAAkB,EAAE,cAAc;CAC1D"}