"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getBridgeUrl = exports.shouldSelectRandomly = exports.selectRandomBridgeUrl = exports.randomBridgeIndex = exports.extractRootDomain = exports.extractHostname = void 0;
const domain = "walletconnect.org";
const alphanumerical = "abcdefghijklmnopqrstuvwxyz0123456789";
const bridges = alphanumerical.split("").map(char => `https://${char}.bridge.walletconnect.org`);
function extractHostname(url) {
    let hostname = url.indexOf("//") > -1 ? url.split("/")[2] : url.split("/")[0];
    hostname = hostname.split(":")[0];
    hostname = hostname.split("?")[0];
    return hostname;
}
exports.extractHostname = extractHostname;
function extractRootDomain(url) {
    return extractHostname(url)
        .split(".")
        .slice(-2)
        .join(".");
}
exports.extractRootDomain = extractRootDomain;
function randomBridgeIndex() {
    return Math.floor(Math.random() * bridges.length);
}
exports.randomBridgeIndex = randomBridgeIndex;
function selectRandomBridgeUrl() {
    return bridges[randomBridgeIndex()];
}
exports.selectRandomBridgeUrl = selectRandomBridgeUrl;
function shouldSelectRandomly(url) {
    return extractRootDomain(url) === domain;
}
exports.shouldSelectRandomly = shouldSelectRandomly;
function getBridgeUrl(url) {
    if (shouldSelectRandomly(url)) {
        return selectRandomBridgeUrl();
    }
    return url;
}
exports.getBridgeUrl = getBridgeUrl;
//# sourceMappingURL=url.js.map