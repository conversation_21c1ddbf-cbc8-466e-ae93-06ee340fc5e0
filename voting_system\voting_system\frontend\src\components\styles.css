/* General Styles */
body {
  background-color: #f8f9fa;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.container {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

/* Header Styles */
.app-header {
  background: linear-gradient(135deg, #1a237e 0%, #283593 100%);
  color: white;
  padding: 1.5rem 0;
  margin-bottom: 2rem;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.app-header h1 {
  font-weight: 700;
  margin: 0;
}

.app-header .account-info {
  background-color: rgba(255, 255, 255, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  font-size: 0.9rem;
}

/* Card Styles */
.card {
  border: none;
  border-radius: 0.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  margin-bottom: 1.5rem;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.card-header {
  font-weight: 600;
  border-bottom: none;
}

.card-footer {
  background-color: white;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

/* Election Cards */
.election-card .card-title {
  color: #1a237e;
  font-weight: 600;
}

.election-card .badge {
  padding: 0.5rem 0.75rem;
  border-radius: 1rem;
}

/* Candidate Cards */
.candidate-card {
  height: 100%;
}

.candidate-card .card-header {
  background-color: #1a237e;
  color: white;
  font-weight: 600;
}

.candidate-photo {
  height: 200px;
  object-fit: cover;
}

/* Buttons */
.btn-primary {
  background-color: #1a237e;
  border-color: #1a237e;
}

.btn-primary:hover, .btn-primary:focus {
  background-color: #283593;
  border-color: #283593;
}

.btn-outline-primary {
  color: #1a237e;
  border-color: #1a237e;
}

.btn-outline-primary:hover, .btn-outline-primary:focus {
  background-color: #1a237e;
  border-color: #1a237e;
}

/* Voting Booth */
.voting-booth {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

/* Registration Form */
.registration-form label {
  font-weight: 500;
}

.registration-form .form-control, .registration-form .form-select {
  padding: 0.75rem;
  border-radius: 0.5rem;
}

/* Kenya Flag Colors */
.kenya-flag-colors {
  background: linear-gradient(to right, #000000 25%, #ffffff 25%, #ffffff 50%, #ff0000 50%, #ff0000 75%, #006600 75%);
  height: 5px;
  margin-bottom: 1.5rem;
}

/* Loading Spinner */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

.loading-spinner {
  width: 3rem;
  height: 3rem;
  border-width: 0.25rem;
}

/* Results Table */
.results-table th {
  background-color: #f8f9fa;
}

.results-table .winner {
  background-color: rgba(40, 167, 69, 0.1);
  font-weight: 600;
}

/* Modal Overlay */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1050;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .card-deck {
    display: block;
  }

  .card {
    margin-bottom: 1.5rem;
  }
}
