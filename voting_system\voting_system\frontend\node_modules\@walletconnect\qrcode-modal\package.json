{"name": "@walletconnect/qrcode-modal", "version": "1.8.0", "description": "QR Code Modal for WalletConnect", "scripts": {"clean": "rm -rf dist", "build:svg": "node ./scripts/build-svg.js", "build:css": "node ./scripts/build-css.js", "build:pre": "run-s clean build:svg build:css", "build:umd": "webpack", "build:cjs": "microbundle --no-compress -f cjs --alias react=preact/compat,react-dom/test-utils=preact/test-utils,react-dom=preact/compat --tsconfig ./tsconfig.cjs.json", "build": "run-s build:pre build:cjs build:umd", "test": "env TS_NODE_PROJECT=\"tsconfig.cjs.json\" mocha -r ts-node/register ./test/**/*.spec.ts --exit"}, "keywords": ["wallet", "walletconnect", "ethereum", "jsonrpc", "mobile", "qrcode", "web3", "crypto", "cryptocurrency", "dapp"], "author": "WalletConnect, Inc. <walletconnect.com>", "homepage": "https://github.com/WalletConnect/walletconnect-monorepo/", "license": "Apache-2.0", "source": "src/index.ts", "main": "dist/cjs/index.js", "types": "dist/cjs/index.d.ts", "unpkg": "dist/umd/index.min.js", "files": ["dist"], "repository": {"type": "git", "url": "git+https://github.com/walletconnect/walletconnect-monorepo.git"}, "bugs": {"url": "https://github.com/walletconnect/walletconnect-monorepo/issues"}, "devDependencies": {"@babel/cli": "7.8.3", "@babel/core": "7.8.3", "@babel/node": "7.8.3", "@babel/polyfill": "7.8.3", "@babel/preset-env": "7.8.3", "@babel/preset-typescript": "7.8.3", "@babel/register": "7.8.3", "@types/chai": "4.2.14", "@types/jest": "22.2.3", "@types/mocha": "8.0.4", "@types/node": "12.12.14", "@types/qrcode": "1.3.4", "@types/react": "16.9.36", "chai": "4.2.0", "css-loader": "3.5.3", "microbundle": "0.11.0", "mocha": "8.2.1", "npm-run-all": "4.1.5", "style-loader": "1.2.0", "svg-url-loader": "5.0.0", "ts-loader": "6.2.2", "ts-node": "10.8.1", "webpack": "4.41.5", "webpack-cli": "3.3.10"}, "dependencies": {"@walletconnect/browser-utils": "^1.8.0", "@walletconnect/mobile-registry": "^1.4.0", "@walletconnect/types": "^1.8.0", "copy-to-clipboard": "^3.3.1", "preact": "10.4.1", "qrcode": "1.4.4"}, "gitHead": "165f7993c2acc907c653c02847fb02721052c6e7"}