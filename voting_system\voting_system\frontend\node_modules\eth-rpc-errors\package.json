{"name": "eth-rpc-errors", "version": "3.0.0", "description": "Ethereum RPC and Provider errors.", "license": "MIT", "author": "<PERSON> <<EMAIL>>", "scripts": {"build": "echo", "test": "node test", "test:coverage": "nyc tape test", "lint": "eslint . --ext js,json", "lint:fix": "eslint . --ext js,json --fix"}, "main": "index.js", "files": ["@types", "index.js", "src"], "dependencies": {"fast-safe-stringify": "^2.0.6"}, "devDependencies": {"@metamask/eslint-config": "^2.1.1", "eslint": "6.8.0", "eslint-plugin-import": "^2.20.2", "eslint-plugin-json": "^2.1.1", "fast-deep-equal": "^2.0.1", "nyc": "^15.0.1", "tape": "^5.0.0"}, "bugs": {"url": "https://github.com/MetaMask/eth-rpc-errors/issues"}, "homepage": "https://github.com/MetaMask/eth-rpc-errors#readme", "repository": {"type": "git", "url": "git+https://github.com/MetaMask/eth-rpc-errors.git"}, "keywords": ["rpc", "ethereum", "errors", "utility"]}