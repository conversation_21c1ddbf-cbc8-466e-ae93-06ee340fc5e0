{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../../src/errors.ts"], "names": [], "mappings": ";;;AAAa,QAAA,uBAAuB,GAAG,6BAA6B,CAAC;AACxD,QAAA,0BAA0B,GAAG,gCAAgC,CAAC;AAC9D,QAAA,sBAAsB,GAAG,kBAAkB,CAAC;AAE5C,QAAA,sBAAsB,GAAG,2BAA2B,CAAC;AACrD,QAAA,oBAAoB,GAAG,uDAAuD,CAAC;AAC/E,QAAA,mBAAmB,GAAG,oDAAoD,CAAC;AAC3E,QAAA,oBAAoB,GAAG,iDAAiD,CAAC;AACzE,QAAA,gBAAgB,GAAG,6CAA6C,CAAC;AACjE,QAAA,sBAAsB,GACjC,gEAAgE,CAAC;AAEtD,QAAA,sBAAsB,GAAG,qCAAqC,CAAC;AAC/D,QAAA,iBAAiB,GAAG,uBAAuB,CAAC;AAE5C,QAAA,+BAA+B,GAAG,2BAA2B,CAAC;AAC9D,QAAA,8BAA8B,GAAG,yBAAyB,CAAC"}