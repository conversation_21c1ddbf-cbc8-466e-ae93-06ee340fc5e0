{"version": 3, "file": "join.js", "sourceRoot": "", "sources": ["../../src/directives/join.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAuBH,MAAM,SAAS,CAAC,CAAC,IAAI,CAAO,KAA8B,EAAE,MAAS;IACnE,MAAM,UAAU,GAAG,OAAO,MAAM,KAAK,UAAU,CAAC;IAChD,IAAI,KAAK,KAAK,SAAS,EAAE;QACvB,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QACX,KAAK,MAAM,KAAK,IAAI,KAAK,EAAE;YACzB,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;gBACV,MAAM,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;aACvC;YACD,CAAC,EAAE,CAAC;YACJ,MAAM,KAAK,CAAC;SACb;KACF;AACH,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/**\n * Returns an iterable containing the values in `items` interleaved with the\n * `joiner` value.\n *\n * @example\n *\n * ```ts\n * render() {\n *   return html`\n *     ${join(items, html`<span class=\"separator\">|</span>`)}\n *   `;\n * }\n */\nexport function join<I, J>(\n  items: Iterable<I> | undefined,\n  joiner: (index: number) => J\n): Iterable<I | J>;\nexport function join<I, J>(\n  items: Iterable<I> | undefined,\n  joiner: J\n): Iterable<I | J>;\nexport function* join<I, J>(items: Iterable<I> | undefined, joiner: J) {\n  const isFunction = typeof joiner === 'function';\n  if (items !== undefined) {\n    let i = -1;\n    for (const value of items) {\n      if (i > -1) {\n        yield isFunction ? joiner(i) : joiner;\n      }\n      i++;\n      yield value;\n    }\n  }\n}\n"]}