"""
Generated by `compile_contracts.py` script.
Compiled with Solidity v0.8.19.
"""

# source: web3/_utils/contract_sources/MathContract.sol:MathContract
MATH_CONTRACT_BYTECODE = "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"  # noqa: E501
MATH_CONTRACT_RUNTIME = "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"  # noqa: E501
MATH_CONTRACT_ABI = [
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "value",
                "type": "uint256",
            }
        ],
        "name": "Increased",
        "type": "event",
    },
    {
        "inputs": [
            {"internalType": "int256", "name": "a", "type": "int256"},
            {"internalType": "int256", "name": "b", "type": "int256"},
        ],
        "name": "add",
        "outputs": [{"internalType": "int256", "name": "result", "type": "int256"}],
        "stateMutability": "payable",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "counter",
        "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "incrementCounter",
        "outputs": [{"internalType": "uint256", "name": "result", "type": "uint256"}],
        "stateMutability": "payable",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}],
        "name": "incrementCounter",
        "outputs": [{"internalType": "uint256", "name": "result", "type": "uint256"}],
        "stateMutability": "payable",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "int256", "name": "a", "type": "int256"}],
        "name": "multiply7",
        "outputs": [{"internalType": "int256", "name": "result", "type": "int256"}],
        "stateMutability": "payable",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "return13",
        "outputs": [{"internalType": "int256", "name": "result", "type": "int256"}],
        "stateMutability": "nonpayable",
        "type": "function",
    },
]
MATH_CONTRACT_DATA = {
    "bytecode": MATH_CONTRACT_BYTECODE,
    "bytecode_runtime": MATH_CONTRACT_RUNTIME,
    "abi": MATH_CONTRACT_ABI,
}
