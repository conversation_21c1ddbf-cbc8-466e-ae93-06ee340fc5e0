export interface TextMap {
    choose_preferred_wallet: string;
    connect_mobile_wallet: string;
    scan_qrcode_with_wallet: string;
    connect: string;
    qrcode: string;
    mobile: string;
    desktop: string;
    copy_to_clipboard: string;
    copied_to_clipboard: string;
    connect_with: string;
    loading: string;
    something_went_wrong: string;
    no_supported_wallets: string;
    no_wallets_found: string;
}
//# sourceMappingURL=text.d.ts.map