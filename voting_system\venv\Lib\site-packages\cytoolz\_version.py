
# This file was generated by 'versioneer.py' (0.22) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2024-12-12T22:48:53-0600",
 "dirty": false,
 "error": null,
 "full-revisionid": "008f5d83b7b066993d820c0b41c8cc6fb583fd19",
 "version": "1.0.1"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
