{"name": "rustbn.js", "version": "0.2.0", "description": "Javascript bindings for https://github.com/paritytech/bn (using asm.js)", "main": "index.js", "scripts": {"test": "tape ./tests/index.js"}, "repository": {"type": "git", "url": "git+https://github.com/ethereumjs/rustbn.js.git"}, "keywords": ["ethereum", "ecc", "bn128", "pairing"], "author": "cdetrio <<EMAIL>>", "contributors": [{"name": "holgerd77", "email": "<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "(MIT OR Apache-2.0)", "bugs": {"url": "https://github.com/ethereumjs/rustbn.js/issues"}, "homepage": "https://github.com/ethereumjs/rustbn.js#readme", "devDependencies": {"tape": "^4.8.0"}}