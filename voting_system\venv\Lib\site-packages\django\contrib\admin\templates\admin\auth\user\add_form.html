{% extends "admin/change_form.html" %}
{% load i18n static %}

{% block form_top %}
  {% if not is_popup %}
    <p>{% translate "After you’ve created a user, you’ll be able to edit more user options." %}</p>
  {% endif %}
{% endblock %}
{% block extrahead %}
  {{ block.super }}
  <link rel="stylesheet" href="{% static 'admin/css/unusable_password_field.css' %}">
{% endblock %}
{% block admin_change_form_document_ready %}
  {{ block.super }}
  <script src="{% static 'admin/js/unusable_password_field.js' %}" defer></script>
{% endblock %}
