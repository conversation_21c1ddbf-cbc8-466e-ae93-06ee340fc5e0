{"version": 3, "file": "encoding.js", "sourceRoot": "", "sources": ["../../src/encoding.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,OAAO,CAAC;AACvB,OAAO,KAAK,QAAQ,MAAM,yBAAyB,CAAC;AAIpD,MAAM,UAAU,0BAA0B,CAAC,MAAmB;IAC5D,OAAO,QAAQ,CAAC,aAAa,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;AACxD,CAAC;AAED,MAAM,UAAU,wBAAwB,CAAC,MAAmB;IAC1D,OAAO,QAAQ,CAAC,WAAW,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;AACtD,CAAC;AAED,MAAM,UAAU,uBAAuB,CAAC,MAAmB,EAAE,QAAkB;IAC7E,OAAO,QAAQ,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;AAChE,CAAC;AAED,MAAM,UAAU,0BAA0B,CAAC,MAAmB;IAC5D,OAAO,QAAQ,CAAC,aAAa,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;AACxD,CAAC;AAED,MAAM,UAAU,kBAAkB,CAAC,GAAG,IAAmB;IACvD,OAAO,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC;AACpG,CAAC;AAID,MAAM,UAAU,0BAA0B,CAAC,GAAW;IACpD,OAAO,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;AAC5C,CAAC;AAED,MAAM,UAAU,mBAAmB,CAAC,GAAW;IAC7C,OAAO,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AACpC,CAAC;AAED,MAAM,UAAU,kBAAkB,CAAC,GAAW,EAAE,QAAkB;IAChE,OAAO,QAAQ,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,CAAC;AAC9C,CAAC;AAED,MAAM,UAAU,qBAAqB,CAAC,GAAW;IAC/C,OAAO,QAAQ,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;AACtC,CAAC;AAED,MAAM,UAAU,aAAa,CAAC,GAAG,IAAc;IAC7C,OAAO,QAAQ,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC,CAAC;AACzC,CAAC;AAID,MAAM,UAAU,wBAAwB,CAAC,IAAY;IACnD,OAAO,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;AAC3C,CAAC;AAED,MAAM,UAAU,mBAAmB,CAAC,IAAY;IAC9C,OAAO,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;AACrC,CAAC;AAED,MAAM,UAAU,gBAAgB,CAAC,IAAY,EAAE,QAAkB;IAC/D,OAAO,QAAQ,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,CAAC;AAC7C,CAAC;AAED,MAAM,UAAU,mBAAmB,CAAC,IAAY;IAC9C,OAAO,IAAI,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC;AACrC,CAAC;AAID,MAAM,UAAU,kBAAkB,CAAC,GAAW;IAC5C,OAAO,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;AACnC,CAAC;AAED,MAAM,UAAU,uBAAuB,CAAC,GAAW;IACjD,OAAO,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;AACzC,CAAC;AAED,MAAM,UAAU,gBAAgB,CAAC,GAAW;IAC1C,OAAO,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;AACjC,CAAC;AAED,MAAM,UAAU,kBAAkB,CAAC,GAAW;IAC5C,OAAO,IAAI,EAAE,CAAC,QAAQ,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;AACjE,CAAC;AAID,MAAM,UAAU,qBAAqB,CAAC,GAAW;IAC/C,OAAO,QAAQ,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;AACtC,CAAC;AAED,MAAM,UAAU,0BAA0B,CAAC,GAAW;IACpD,OAAO,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;AAC5C,CAAC;AAED,MAAM,UAAU,mBAAmB,CAAC,GAAW;IAC7C,OAAO,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC;AAChC,CAAC;AAED,MAAM,UAAU,kBAAkB,CAAC,GAAoB,EAAE,QAAkB;IACzE,MAAM,GAAG,GAAG,QAAQ,CAAC,eAAe,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACrF,OAAO,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AACrD,CAAC"}