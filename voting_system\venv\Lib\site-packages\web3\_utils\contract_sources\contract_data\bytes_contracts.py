"""
Generated by `compile_contracts.py` script.
Compiled with Solidity v0.8.19.
"""

# source: web3/_utils/contract_sources/BytesContracts.sol:BytesContract
BYTES_CONTRACT_BYTECODE = "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"  # noqa: E501
BYTES_CONTRACT_RUNTIME = "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"  # noqa: E501
BYTES_CONTRACT_ABI = [
    {
        "inputs": [{"internalType": "bytes", "name": "_value", "type": "bytes"}],
        "stateMutability": "nonpayable",
        "type": "constructor",
    },
    {
        "inputs": [],
        "name": "constValue",
        "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "getValue",
        "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "bytes", "name": "_value", "type": "bytes"}],
        "name": "setValue",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
]
BYTES_CONTRACT_DATA = {
    "bytecode": BYTES_CONTRACT_BYTECODE,
    "bytecode_runtime": BYTES_CONTRACT_RUNTIME,
    "abi": BYTES_CONTRACT_ABI,
}


# source: web3/_utils/contract_sources/BytesContracts.sol:Bytes32Contract
BYTES32_CONTRACT_BYTECODE = "0x60806040527f012301230123012301230123012301230123012301230123012301230123012360005534801561003457600080fd5b5060405161025d38038061025d8339818101604052810190610056919061009e565b80600181905550506100cb565b600080fd5b6000819050919050565b61007b81610068565b811461008657600080fd5b50565b60008151905061009881610072565b92915050565b6000602082840312156100b4576100b3610063565b5b60006100c284828501610089565b91505092915050565b610183806100da6000396000f3fe608060405234801561001057600080fd5b50600436106100415760003560e01c8063209652551461004657806330de3cee1461006457806358825b1014610082575b600080fd5b61004e61009e565b60405161005b91906100d4565b60405180910390f35b61006c6100a8565b60405161007991906100d4565b60405180910390f35b61009c60048036038101906100979190610120565b6100b1565b005b6000600154905090565b60008054905090565b8060018190555050565b6000819050919050565b6100ce816100bb565b82525050565b60006020820190506100e960008301846100c5565b92915050565b600080fd5b6100fd816100bb565b811461010857600080fd5b50565b60008135905061011a816100f4565b92915050565b600060208284031215610136576101356100ef565b5b60006101448482850161010b565b9150509291505056fea2646970667358221220967501f16e2398d7682cc7112f089eeb3c3153bebb94e7a01a943c7c89a57f3a64736f6c63430008130033"  # noqa: E501
BYTES32_CONTRACT_RUNTIME = "0x608060405234801561001057600080fd5b50600436106100415760003560e01c8063209652551461004657806330de3cee1461006457806358825b1014610082575b600080fd5b61004e61009e565b60405161005b91906100d4565b60405180910390f35b61006c6100a8565b60405161007991906100d4565b60405180910390f35b61009c60048036038101906100979190610120565b6100b1565b005b6000600154905090565b60008054905090565b8060018190555050565b6000819050919050565b6100ce816100bb565b82525050565b60006020820190506100e960008301846100c5565b92915050565b600080fd5b6100fd816100bb565b811461010857600080fd5b50565b60008135905061011a816100f4565b92915050565b600060208284031215610136576101356100ef565b5b60006101448482850161010b565b9150509291505056fea2646970667358221220967501f16e2398d7682cc7112f089eeb3c3153bebb94e7a01a943c7c89a57f3a64736f6c63430008130033"  # noqa: E501
BYTES32_CONTRACT_ABI = [
    {
        "inputs": [{"internalType": "bytes32", "name": "_value", "type": "bytes32"}],
        "stateMutability": "nonpayable",
        "type": "constructor",
    },
    {
        "inputs": [],
        "name": "constValue",
        "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "getValue",
        "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "bytes32", "name": "_value", "type": "bytes32"}],
        "name": "setValue",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
]
BYTES32_CONTRACT_DATA = {
    "bytecode": BYTES32_CONTRACT_BYTECODE,
    "bytecode_runtime": BYTES32_CONTRACT_RUNTIME,
    "abi": BYTES32_CONTRACT_ABI,
}
