# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON>u <<EMAIL>>, 2015
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-10-09 17:42+0200\n"
"PO-Revision-Date: 2017-09-23 18:54+0000\n"
"Last-Translator: <PERSON><PERSON> <jann<PERSON>@leidel.info>\n"
"Language-Team: Swahili (http://www.transifex.com/django/django/language/"
"sw/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: sw\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Advanced options"
msgstr "Chaguzi za kiwango cha juu"

msgid "Flat Pages"
msgstr ""

msgid "URL"
msgstr "URL"

msgid ""
"Example: '/about/contact/'. Make sure to have leading and trailing slashes."
msgstr ""
"Mfano: '/about/contact/'. Hakikisha unaweka mkwaju mwanzoni na mwishoni."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""

msgid "URL is missing a leading slash."
msgstr "URL imekosa mkwaju mwishoni"

msgid "URL is missing a trailing slash."
msgstr "URL imekosa mkwaju mwanzoni"

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr ""

msgid "title"
msgstr "kichwa"

msgid "content"
msgstr "maudhui"

msgid "enable comments"
msgstr "ruhusu maoni"

msgid "template name"
msgstr "jina la templeti"

msgid ""
"Example: 'flatpages/contact_page.html'. If this isn't provided, the system "
"will use 'flatpages/default.html'."
msgstr ""
"Mfano: 'flatpages/contact_page.html'. Ikiwa hii haitaainishwa mfumo utatumia "
"'flatpages/default.html'."

msgid "registration required"
msgstr "usajili unahitajika"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr ""
"Ikiwa hii itatikiwa ni watumiaji waliosajiliwa tuu ndio watakaoweza kuuona "
"ukurasa huo"

msgid "sites"
msgstr ""

msgid "flat page"
msgstr ""

msgid "flat pages"
msgstr ""
