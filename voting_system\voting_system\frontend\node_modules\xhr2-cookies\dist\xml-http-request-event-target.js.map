{"version": 3, "file": "xml-http-request-event-target.js", "sourceRoot": "", "sources": ["../xml-http-request-event-target.ts"], "names": [], "mappings": ";;AAMA;IAAA;QASS,cAAS,GAAmD,EAAE,CAAC;IAiCxE,CAAC;IA/BA,oDAAgB,GAAhB,UAAiB,SAAiB,EAAE,QAAqD;QACxF,SAAS,GAAG,SAAS,CAAC,WAAW,EAAE,CAAC;QACpC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QAC5D,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,IAAI,CAAE,QAAwC,CAAC,WAAW,IAAK,QAAkC,CAAC,CAAC;IAC9H,CAAC;IACD,uDAAmB,GAAnB,UAAoB,SAAiB,EAAE,QAAqD;QAC3F,SAAS,GAAG,SAAS,CAAC,WAAW,EAAE,CAAC;QACpC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAAC,MAAM,CAAC;QAAC,CAAC;QAE3C,IAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,OAAO,CAAE,QAAwC,CAAC,WAAW,IAAK,QAAkC,CAAC,CAAC;QAC9I,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;YAAC,MAAM,CAAC;QAAC,CAAC;QAE1B,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;IAC5C,CAAC;IACD,iDAAa,GAAb,UAAc,KAAoB;QACjC,IAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;QAC3C,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,iCAAiC;QAEtD,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC/B,GAAG,CAAC,CAAiB,UAAyB,EAAzB,KAAA,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,EAAzB,cAAyB,EAAzB,IAAyB;gBAAzC,IAAI,UAAQ,SAAA;gBAChB,UAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;aAC3B;QACF,CAAC;QAED,IAAM,QAAQ,GAAG,IAAI,CAAC,OAAK,SAAW,CAAC,CAAC;QACxC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;YACd,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAC5B,CAAC;QAED,MAAM,CAAC,IAAI,CAAC;IACb,CAAC;IACF,gCAAC;AAAD,CAAC,AA1CD,IA0CC;AA1CY,8DAAyB"}