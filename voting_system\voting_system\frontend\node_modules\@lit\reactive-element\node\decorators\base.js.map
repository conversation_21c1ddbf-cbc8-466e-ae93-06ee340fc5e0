{"version": 3, "file": "base.js", "sources": ["../../src/decorators/base.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {ReactiveElement} from '../reactive-element.js';\n\n/**\n * Generates a public interface type that removes private and protected fields.\n * This allows accepting otherwise compatible versions of the type (e.g. from\n * multiple copies of the same package in `node_modules`).\n */\nexport type Interface<T> = {\n  [K in keyof T]: T[K];\n};\n\nexport type Constructor<T> = {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  new (...args: any[]): T;\n};\n\n// From the TC39 Decorators proposal\nexport interface ClassDescriptor {\n  kind: 'class';\n  elements: ClassElement[];\n  finisher?: <T>(clazz: Constructor<T>) => void | Constructor<T>;\n}\n\n// From the TC39 Decorators proposal\nexport interface ClassElement {\n  kind: 'field' | 'method';\n  key: PropertyKey;\n  placement: 'static' | 'prototype' | 'own';\n  initializer?: Function;\n  extras?: ClassElement[];\n  finisher?: <T>(clazz: Constructor<T>) => void | Constructor<T>;\n  descriptor?: PropertyDescriptor;\n}\n\nexport const legacyPrototypeMethod = (\n  descriptor: PropertyDescriptor,\n  proto: Object,\n  name: PropertyKey\n) => {\n  Object.defineProperty(proto, name, descriptor);\n};\n\nexport const standardPrototypeMethod = (\n  descriptor: PropertyDescriptor,\n  element: ClassElement\n) => ({\n  kind: 'method',\n  placement: 'prototype',\n  key: element.key,\n  descriptor,\n});\n\n/**\n * Helper for decorating a property that is compatible with both TypeScript\n * and Babel decorators. The optional `finisher` can be used to perform work on\n * the class. The optional `descriptor` should return a PropertyDescriptor\n * to install for the given property.\n *\n * @param finisher {function} Optional finisher method; receives the element\n * constructor and property key as arguments and has no return value.\n * @param descriptor {function} Optional descriptor method; receives the\n * property key as an argument and returns a property descriptor to define for\n * the given property.\n * @returns {ClassElement|void}\n */\nexport const decorateProperty =\n  ({\n    finisher,\n    descriptor,\n  }: {\n    finisher?:\n      | ((ctor: typeof ReactiveElement, property: PropertyKey) => void)\n      | null;\n    descriptor?: (property: PropertyKey) => PropertyDescriptor;\n  }) =>\n  (\n    protoOrDescriptor: Interface<ReactiveElement> | ClassElement,\n    name?: PropertyKey\n    // Note TypeScript requires the return type to be `void|any`\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  ): void | any => {\n    // TypeScript / Babel legacy mode\n    if (name !== undefined) {\n      const ctor = (protoOrDescriptor as ReactiveElement)\n        .constructor as typeof ReactiveElement;\n      if (descriptor !== undefined) {\n        Object.defineProperty(protoOrDescriptor, name, descriptor(name));\n      }\n      finisher?.(ctor, name!);\n      // Babel standard mode\n    } else {\n      // Note, the @property decorator saves `key` as `originalKey`\n      // so try to use it here.\n      const key =\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        (protoOrDescriptor as any).originalKey ??\n        (protoOrDescriptor as ClassElement).key;\n      const info: ClassElement =\n        descriptor != undefined\n          ? {\n              kind: 'method',\n              placement: 'prototype',\n              key,\n              descriptor: descriptor((protoOrDescriptor as ClassElement).key),\n            }\n          : {...(protoOrDescriptor as ClassElement), key};\n      if (finisher != undefined) {\n        info.finisher = function <ReactiveElement>(\n          ctor: Constructor<ReactiveElement>\n        ) {\n          finisher(ctor as unknown as typeof ReactiveElement, key);\n        };\n      }\n      return info;\n    }\n  };\n"], "names": ["legacyPrototypeMethod", "descriptor", "proto", "name", "Object", "defineProperty", "standardPrototypeMethod", "element", "kind", "placement", "key", "decorateProperty", "finisher", "protoOrDescriptor", "undefined", "_a", "original<PERSON>ey", "info", "ctor", "constructor"], "mappings": ";;;;;AAwCa,MAAAA,EAAwB,CACnCC,EACAC,EACAC,KAEAC,OAAOC,eAAeH,EAAOC,EAAMF,EAAW,EAGnCK,EAA0B,CACrCL,EACAM,KACI,CACJC,KAAM,SACNC,UAAW,YACXC,IAAKH,EAAQG,IACbT,eAgBWU,EACX,EACEC,WACAX,gBAOF,CACEY,EACAV,WAKA,QAAaW,IAATX,EAQG,CAGL,MAAMO,UAEJK,EAACF,EAA0BG,2BAC1BH,EAAmCH,IAChCO,EACUH,MAAdb,EACI,CACEO,KAAM,SACNC,UAAW,YACXC,MACAT,WAAYA,EAAYY,EAAmCH,MAE7D,IAAKG,EAAoCH,OAQ/C,OAPgBI,MAAZF,IACFK,EAAKL,SAAW,SACdM,GAEAN,EAASM,EAA2CR,EACtD,GAEKO,CACR,CAhCuB,CACtB,MAAMC,EAAQL,EACXM,iBACgBL,IAAfb,GACFG,OAAOC,eAAeQ,EAAmBV,EAAMF,EAAWE,IAE5DS,SAAAA,EAAWM,EAAMf,EAElB,CAwBA"}