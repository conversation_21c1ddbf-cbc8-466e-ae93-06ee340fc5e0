# [1.2.0](https://github.com/streamich/hyperdyperid/compare/v1.1.0...v1.2.0) (2022-04-23)


### Features

* 🎸 add TypeScript ambient type annotations ([cc564fe](https://github.com/streamich/hyperdyperid/commit/cc564fe8eac6198d85b16b6da04fc3a780499f0e))

# [1.1.0](https://github.com/streamich/hyperdyperid/compare/v1.0.1...v1.1.0) (2022-04-23)


### Features

* 🎸 add str3_36 generators ([0ba3630](https://github.com/streamich/hyperdyperid/commit/0ba363069703fb80005555e74f2f469148f31fa7))
* 🎸 export xorshift32() and add randomU32() functions ([e342869](https://github.com/streamich/hyperdyperid/commit/e34286971ae90064bd4391b6a01d87d2d7a94d86))


### Performance Improvements

* ⚡️ update benchmarks ([6504b3d](https://github.com/streamich/hyperdyperid/commit/6504b3d54b29e8677f057612fec06783d9752a1e))
* ⚡️ update randomU32() benchmarks ([6bde79d](https://github.com/streamich/hyperdyperid/commit/6bde79deaee6a5f8c7781ba180675b9bf0ad3a51))

## [1.0.1](https://github.com/streamich/hyperdyperid/compare/v1.0.0...v1.0.1) (2020-10-12)


### Bug Fixes

* 🐛 initialize seeds using 32 bits ([3090f3c](https://github.com/streamich/hyperdyperid/commit/3090f3ce5d2a8392fb52d75bcc76cf7c2c41e76e))

# 1.0.0 (2020-10-12)


### Features

* 🎸 add hyperid() and str() methods ([7a06b5a](https://github.com/streamich/hyperdyperid/commit/7a06b5adc1a94df5cc99de1d687b69b9d396e8f4))
* 🎸 implement fast ID generators ([cda3d98](https://github.com/streamich/hyperdyperid/commit/cda3d98a07627d5b3cb2c07d0dde02517d4c34eb))


### Performance Improvements

* ⚡️ improve benchmarks ([1f10d9d](https://github.com/streamich/hyperdyperid/commit/1f10d9d76853d94648b545a8b4d3592dc1203857))
