{"name": "@walletconnect/jsonrpc-types", "description": "Typings for JSON-RPC", "version": "1.0.4", "author": "WalletConnect, Inc. <walletconnect.com>", "license": "MIT", "homepage": "https://github.com/WalletConnect/walletconnect-utils/", "repository": {"type": "git", "url": "git+https://github.com/WalletConnect/walletconnect-utils.git"}, "bugs": {"url": "https://github.com/WalletConnect/walletconnect-utils/issues"}, "main": "dist/index.cjs.js", "module": "dist/index.es.js", "browser": "dist/index.es.js", "unpkg": "dist/index.umd.js", "types": "dist/types/index.d.ts", "files": ["dist"], "keywords": ["json", "rpc", "jsonrpc", "json-rpc", "tools", "types", "utils", "provider"], "scripts": {"clean": "rm -rf dist", "build:pre": "npm run clean", "build:types": "tsc", "build:source": "rollup --config rollup.config.js", "build": "npm run build:pre && npm run build:source && npm run build:types", "test": "env TS_NODE_PROJECT=\"tsconfig.cjs.json\" mocha --timeout 5000 --exit -r ts-node/register ./test/**/*.test.ts", "lint": "eslint -c '../../.eslintrc' --fix './src/**/*.ts'", "npm-publish:latest": "npm publish --access public --tag latest", "npm-publish:canary": "npm publish --access public --tag canary", "prepublishOnly": "npm run test && npm run build", "prettier": "prettier --config ../../.prettierrc --check {src,test}/**/*.ts", "format": "prettier --config ../../.prettierrc --write {src,test}/**/*.ts"}, "dependencies": {"events": "^3.3.0", "keyvaluestorage-interface": "^1.0.0"}, "devDependencies": {"@babel/cli": "^7.12.1", "@babel/core": "^7.12.3", "@babel/node": "^7.12.1", "@babel/polyfill": "^7.12.1", "@babel/preset-env": "^7.12.1", "@babel/preset-typescript": "^7.12.1", "@babel/register": "^7.12.1", "@types/chai": "^4.2.14", "@types/jest": "^26.0.15", "@types/mocha": "^8.2.0", "@types/node": "^14.14.7", "chai": "^4.2.0", "core-js": "^3.6.5", "mocha": "^8.1.3", "npm-run-all": "^4.1.5", "webpack": "^4.41.6", "webpack-cli": "^3.3.11"}}