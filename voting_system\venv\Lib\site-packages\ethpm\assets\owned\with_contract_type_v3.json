{"contractTypes": {"Owned": {"abi": [{"inputs": [], "payable": false, "stateMutability": "nonpayable", "type": "constructor"}], "deploymentBytecode": {"bytecode": "0x6080604052348015600f57600080fd5b50336000806101000a81548173ffffffffffffffffffffffffffffffffffffffff021916908373ffffffffffffffffffffffffffffffffffffffff160217905550603580605d6000396000f3006080604052600080fd00a165627a7a72305820d6ab9e295aa1d1adb0fca69ce42c2c73e991afe290852e8247a208a78b352ff00029"}}}, "manifest": "ethpm/3", "name": "owned", "sources": {"Owned.sol": {"content": "pragma solidity ^0.4.24;\n\ncontract Owned {\n    address owner;\n    \n    modifier onlyOwner { require(msg.sender == owner); _; }\n\n    constructor() public {\n        owner = msg.sender;\n    }\n}\n"}}, "version": "1.0.1"}