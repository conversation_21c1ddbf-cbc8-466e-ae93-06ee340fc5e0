{"name": "parse-headers", "version": "2.0.6", "description": "Parse http headers, works with browserify/xhr", "main": "parse-headers.js", "types": "index.d.ts", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "https://github.com/kesla/parse-headers.git"}, "keywords": ["http", "headers"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/kesla/parse-headers/issues"}, "homepage": "https://github.com/kesla/parse-headers", "devDependencies": {"tape": "^4.10.1"}, "dependencies": {}}