{"name": "immediate", "version": "3.3.0", "description": "A cross browser microtask library", "contributors": ["Domenic Denicola <<EMAIL>> (http://domenicdenicola.com)", "Donavon West <<EMAIL>> (http://donavon.com)", "Yaffle", "<PERSON> <<EMAIL>>"], "testling": {"files": "test/tests.js", "browsers": ["ie/6..latest", "chrome/latest", "firefox/3..latest", "opera/10..latest", "safari/4..latest", "iphone/latest", "ipad/latest", "android-browser/latest"]}, "license": "MIT", "repository": {"type": "git", "url": "git://github.com/calvinmetcalf/immediate.git"}, "browser": {"./lib/nextTick": false}, "bugs": "https://github.com/calvinmetcalf/immediate/issues", "main": "lib/index.js", "scripts": {"build": "npm run build-js && npm run uglify", "uglify": "uglifyjs dist/immediate.js -mc > dist/immediate.min.js", "build-js": "browserify -s immediate ./lib/index.js | derequire > dist/immediate.js", "test": "jshint lib/*.js && node test/tests.js | tspec"}, "devDependencies": {"browserify": "^16.5.1", "derequire": "^2.1.1", "jshint": "^2.5.1", "tap-spec": "^5.0.0", "tape": "^4.0.0", "uglify-js": "^2.4.13"}}