{"name": "@walletconnect/randombytes", "description": "Isomorphic Library for Random Bytes", "version": "1.1.0", "author": "WalletConnect, Inc. <walletconnect.com>", "license": "MIT", "keywords": ["crypto", "cryptography", "isomorphic", "random", "bytes"], "files": ["dist"], "main": "dist/cjs/node/index.js", "react-native": "dist/cjs/node/index.js", "browser": "dist/esm/browser/index.js", "types": "./dist/cjs/node/index.d.ts", "unpkg": "dist/umd/index.min.js", "homepage": "https://github.com/walletconnect/walletconnect-utils", "repository": {"type": "git", "url": "git+https://github.com/walletconnect/walletconnect-utils.git"}, "bugs": {"url": "https://github.com/walletconnect/walletconnect-utils/issues"}, "scripts": {"clean": "rm -rf dist", "build:cjs": "tsc -p tsconfig.cjs.json", "build:esm": "tsc -p tsconfig.esm.json", "build:umd": "webpack", "build": "run-s clean build:cjs build:esm build:umd ", "test": "env TS_NODE_PROJECT=\"tsconfig.cjs.json\" mocha --exit -r ts-node/register -r jsdom-global/register ./test/**/*.test.ts", "lint": "eslint -c '../../.eslintrc' --fix './src/**/*.ts'", "npm-publish:latest": "npm publish --access public --tag latest", "npm-publish:canary": "npm publish --access public --tag canary", "prepublishOnly": "npm run test && npm run build", "prettier": "prettier --config ../../.prettierrc --check {src,test}/**/*.ts", "format": "prettier --config ../../.prettierrc --write {src,test}/**/*.ts"}, "dependencies": {"@walletconnect/encoding": "^1.0.2", "@walletconnect/environment": "^1.0.1", "@noble/hashes": "1.7.0", "tslib": "1.14.1"}, "devDependencies": {"@types/chai": "^4.2.14", "@types/jest": "^26.0.15", "@types/mocha": "^8.2.0", "@types/node": "^14.14.7", "@types/randombytes": "^2.0.0", "chai": "^4.2.0", "jsdom": "^16.6.0", "jsdom-global": "^3.0.2", "mocha": "^8.1.3", "webpack": "^4.41.6", "webpack-cli": "^3.3.11"}}