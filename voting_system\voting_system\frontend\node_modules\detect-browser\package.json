{"name": "detect-browser", "version": "5.2.0", "description": "Unpack a browser type and version from the useragent string", "main": "index.js", "module": "es/index.js", "types": "index.d.ts", "sideEffects": false, "scripts": {"pretest": "tsc", "test": "node test", "lint": "tslint -c tslint.json src/**", "compile": "npm-run-all -p compile:commonjs compile:es", "compile:commonjs": "tsc", "compile:es": "rimraf es && tsc --outDir es --module es6 --declaration false", "compile-and-size": "yarn tsc && cat index.js | gzip | wc -c", "prepare": "npm-run-all compile test lint", "patch-release": "npm version patch && npm publish && npm run postpublish", "minor-release": "npm version minor && npm publish && npm run postpublish", "major-release": "npm version major && npm publish && npm run postpublish", "postpublish": "git push origin master --follow-tags", "autoformat": "prettier --write src/** test/**", "gendocs": "embellish README.md"}, "stability": "stable", "repository": {"type": "git", "url": "https://github.com/<PERSON><PERSON><PERSON>/detect-browser.git"}, "keywords": ["browser", "detect", "useragent"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/<PERSON><PERSON><PERSON>/detect-browser/issues"}, "homepage": "https://github.com/<PERSON><PERSON><PERSON><PERSON>/detect-browser", "devDependencies": {"@types/node": "^13.9.1", "embellish-readme": "^1.7.2", "npm-run-all": "^4.1.5", "prettier": "^1.18.2", "rimraf": "^3.0.2", "semver": "^7.1.3", "tape": "^4.11.0", "tslint": "^6.1.3", "typescript": "^3.6.2"}}