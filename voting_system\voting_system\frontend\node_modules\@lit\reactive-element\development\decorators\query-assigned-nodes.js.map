{"version": 3, "file": "query-assigned-nodes.js", "sourceRoot": "", "sources": ["../../src/decorators/query-assigned-nodes.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH;;;;;GAKG;AAEH,OAAO,EAAC,gBAAgB,EAAC,MAAM,WAAW,CAAC;AAC3C,OAAO,EAAC,qBAAqB,EAAC,MAAM,8BAA8B,CAAC;AAyFnE,MAAM,UAAU,kBAAkB,CAChC,aAAkD,EAClD,OAAiB,EACjB,QAAiB;IAEjB,sCAAsC;IACtC,IAAI,IAAI,GAAG,aAAa,CAAC;IACzB,IAAI,oBAA0C,CAAC;IAC/C,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE;QACrC,IAAI,GAAG,aAAa,CAAC,IAAI,CAAC;QAC1B,oBAAoB,GAAG,aAAa,CAAC;KACtC;SAAM;QACL,oBAAoB,GAAG,EAAC,OAAO,EAAC,CAAC;KAClC;IAED,0EAA0E;IAC1E,sDAAsD;IACtD,IAAI,QAAQ,EAAE;QACZ,OAAO,qBAAqB,CAAC;YAC3B,IAAI,EAAE,IAAc;YACpB,OAAO;YACP,QAAQ;SACT,CAAC,CAAC;KACJ;IAED,OAAO,gBAAgB,CAAC;QACtB,UAAU,EAAE,CAAC,KAAkB,EAAE,EAAE,CAAC,CAAC;YACnC,GAAG;;gBACD,MAAM,YAAY,GAAG,OAAO,IAAI,CAAC,CAAC,CAAC,SAAS,IAAI,GAAG,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC;gBACvE,MAAM,MAAM,GACV,MAAA,IAAI,CAAC,UAAU,0CAAE,aAAa,CAAkB,YAAY,CAAC,CAAC;gBAChE,OAAO,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,aAAa,CAAC,oBAAoB,CAAC,mCAAI,EAAE,CAAC;YAC3D,CAAC;YACD,UAAU,EAAE,IAAI;YAChB,YAAY,EAAE,IAAI;SACnB,CAAC;KACH,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/*\n * IMPORTANT: For compatibility with <PERSON><PERSON><PERSON> and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\n\nimport {decorateProperty} from './base.js';\nimport {queryAssignedElements} from './query-assigned-elements.js';\n\nimport type {ReactiveElement} from '../reactive-element.js';\n\n/**\n * Options for the {@linkcode queryAssignedNodes} decorator. Extends the options\n * that can be passed into [HTMLSlotElement.assignedNodes](https://developer.mozilla.org/en-US/docs/Web/API/HTMLSlotElement/assignedNodes).\n */\nexport interface QueryAssignedNodesOptions extends AssignedNodesOptions {\n  /**\n   * Name of the slot to query. Leave empty for the default slot.\n   */\n  slot?: string;\n}\n\n// TypeScript requires the decorator return type to be `void|any`.\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntype TSDecoratorReturnType = void | any;\n\n/**\n * A property decorator that converts a class property into a getter that\n * returns the `assignedNodes` of the given `slot`.\n *\n * Can be passed an optional {@linkcode QueryAssignedNodesOptions} object.\n *\n * Example usage:\n * ```ts\n * class MyElement {\n *   @queryAssignedNodes({slot: 'list', flatten: true})\n *   listItems!: Array<Node>;\n *\n *   render() {\n *     return html`\n *       <slot name=\"list\"></slot>\n *     `;\n *   }\n * }\n * ```\n *\n * Note the type of this property should be annotated as `Array<Node>`.\n *\n * @category Decorator\n */\nexport function queryAssignedNodes(\n  options?: QueryAssignedNodesOptions\n): TSDecoratorReturnType;\n\n/**\n * A property decorator that converts a class property into a getter that\n * returns the `assignedNodes` of the given named `slot`.\n *\n * Example usage:\n * ```ts\n * class MyElement {\n *   @queryAssignedNodes('list', true, '.item')\n *   listItems!: Array<HTMLElement>;\n *\n *   render() {\n *     return html`\n *       <slot name=\"list\"></slot>\n *     `;\n *   }\n * }\n * ```\n *\n * Note the type of this property should be annotated as `Array<Node>` if used\n * without a `selector` or `Array<HTMLElement>` if a selector is provided.\n * Use {@linkcode queryAssignedElements @queryAssignedElements} to list only\n * elements, and optionally filter the element list using a CSS selector.\n *\n * @param slotName A string name of the slot.\n * @param flatten A boolean which when true flattens the assigned nodes,\n *     meaning any assigned nodes that are slot elements are replaced with their\n *     assigned nodes.\n * @param selector A CSS selector used to filter the elements returned.\n *\n * @category Decorator\n * @deprecated Prefer passing in a single options object, i.e. `{slot: 'list'}`.\n * If using `selector` please use `@queryAssignedElements`.\n * `@queryAssignedNodes('', false, '.item')` is functionally identical to\n * `@queryAssignedElements({slot: '', flatten: false, selector: '.item'})` or\n * `@queryAssignedElements({selector: '.item'})`.\n */\nexport function queryAssignedNodes(\n  slotName?: string,\n  flatten?: boolean,\n  selector?: string\n): TSDecoratorReturnType;\n\nexport function queryAssignedNodes(\n  slotOrOptions?: string | QueryAssignedNodesOptions,\n  flatten?: boolean,\n  selector?: string\n) {\n  // Normalize the overloaded arguments.\n  let slot = slotOrOptions;\n  let assignedNodesOptions: AssignedNodesOptions;\n  if (typeof slotOrOptions === 'object') {\n    slot = slotOrOptions.slot;\n    assignedNodesOptions = slotOrOptions;\n  } else {\n    assignedNodesOptions = {flatten};\n  }\n\n  // For backwards compatibility, queryAssignedNodes with a selector behaves\n  // exactly like queryAssignedElements with a selector.\n  if (selector) {\n    return queryAssignedElements({\n      slot: slot as string,\n      flatten,\n      selector,\n    });\n  }\n\n  return decorateProperty({\n    descriptor: (_name: PropertyKey) => ({\n      get(this: ReactiveElement) {\n        const slotSelector = `slot${slot ? `[name=${slot}]` : ':not([name])'}`;\n        const slotEl =\n          this.renderRoot?.querySelector<HTMLSlotElement>(slotSelector);\n        return slotEl?.assignedNodes(assignedNodesOptions) ?? [];\n      },\n      enumerable: true,\n      configurable: true,\n    }),\n  });\n}\n"]}