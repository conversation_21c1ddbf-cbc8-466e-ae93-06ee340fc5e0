{% extends "admin/base_site.html" %}
{% load i18n %}

{% block coltype %}colSM{% endblock %}
{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% translate 'Home' %}</a>
&rsaquo; <a href="{% url 'django-admindocs-docroot' %}">{% translate 'Documentation' %}</a>
&rsaquo; {% translate 'Tags' %}
</div>
{% endblock %}
{% block title %}{% translate 'Template tags' %}{% endblock %}

{% block content %}

<h1>{% translate 'Template tag documentation' %}</h1>

<div id="content-main">
{% regroup tags|dictsort:"library" by library as tag_libraries %}
{% for library in tag_libraries %}
<div class="module">
    <h2>{% firstof library.grouper _("Built-in tags") %}</h2>
    {% if library.grouper %}<p class="small quiet">{% blocktranslate with code="{"|add:"% load "|add:library.grouper|add:" %"|add:"}" %}To use these tags, put <code>{{ code }}</code> in your template before using the tag.{% endblocktranslate %}</p><hr>{% endif %}
    {% for tag in library.list|dictsort:"name" %}
    <h3 id="{{ library.grouper|default:"built_in" }}-{{ tag.name }}">{{ tag.name }}</h3>
    <h4>{{ tag.title|striptags }}</h4>
    {{ tag.body }}
    {% if not forloop.last %}<hr>{% endif %}
    {% endfor %}
</div>
{% endfor %}
</div>

{% endblock %}

{% block sidebar %}

<div id="content-related">

{% regroup tags|dictsort:"library" by library as tag_libraries %}
{% for library in tag_libraries %}
<div class="module">
    <h2>{% firstof library.grouper _("Built-in tags") %}</h2>
    <ul>
    {% for tag in library.list|dictsort:"name" %}
        <li><a href="#{{ library.grouper|default:"built_in" }}-{{ tag.name }}">{{ tag.name }}</a></li>
    {% endfor %}
    </ul>
</div>
{% endfor %}

</div>

{% endblock %}
