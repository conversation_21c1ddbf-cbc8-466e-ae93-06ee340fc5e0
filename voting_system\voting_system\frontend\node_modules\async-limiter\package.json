{"name": "async-limiter", "version": "1.0.1", "description": "asynchronous function queue with adjustable concurrency", "keywords": ["throttle", "async", "limiter", "asynchronous", "job", "task", "concurrency", "concurrent"], "dependencies": {}, "devDependencies": {"coveralls": "^3.0.3", "eslint": "^5.16.0", "eslint-plugin-mocha": "^5.3.0", "intelli-espower-loader": "^1.0.1", "mocha": "^6.1.4", "nyc": "^14.1.1", "power-assert": "^1.6.1"}, "scripts": {"test": "mocha --require intelli-espower-loader test/", "travis": "npm run lint && npm run test", "coverage": "nyc npm test && nyc report --reporter=text-lcov | coveralls", "example": "node example", "lint": "eslint ."}, "repository": "https://github.com/strml/async-limiter.git", "author": "<PERSON> <<EMAIL>", "license": "MIT"}