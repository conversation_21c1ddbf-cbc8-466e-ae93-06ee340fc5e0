{"version": 3, "file": "compat.module.js", "sources": ["../src/util.js", "../src/PureComponent.js", "../src/memo.js", "../src/forwardRef.js", "../src/Children.js", "../src/suspense.js", "../src/suspense-list.js", "../src/portals.js", "../src/render.js", "../src/events.js", "../src/index.js"], "sourcesContent": ["/**\n * Assign properties from `props` to `obj`\n * @template O, P The obj and props types\n * @param {O} obj The object to copy properties to\n * @param {P} props The object to copy properties from\n * @returns {O & P}\n */\nexport function assign(obj, props) {\n\tfor (let i in props) obj[i] = props[i];\n\treturn /** @type {O & P} */ (obj);\n}\n\n/**\n * Check if two objects have a different shape\n * @param {object} a\n * @param {object} b\n * @returns {boolean}\n */\nexport function shallowDiffers(a, b) {\n\tfor (let i in a) if (i !== '__source' && !(i in b)) return true;\n\tfor (let i in b) if (i !== '__source' && a[i] !== b[i]) return true;\n\treturn false;\n}\n", "import { Component } from 'preact';\nimport { shallowDiffers } from './util';\n\n/**\n * Component class with a predefined `shouldComponentUpdate` implementation\n */\nexport class PureComponent extends Component {\n\tconstructor(props) {\n\t\tsuper(props);\n\t\t// Some third-party libraries check if this property is present\n\t\tthis.isPureReactComponent = true;\n\t}\n\n\tshouldComponentUpdate(props, state) {\n\t\treturn (\n\t\t\tshallowDiffers(this.props, props) || shallowDiffers(this.state, state)\n\t\t);\n\t}\n}\n", "import { createElement } from 'preact';\nimport { shallowDiffers, assign } from './util';\n\n/**\n * Memoize a component, so that it only updates when the props actually have\n * changed. This was previously known as `React.pure`.\n * @param {import('./internal').FunctionalComponent} c functional component\n * @param {(prev: object, next: object) => boolean} [comparer] Custom equality function\n * @returns {import('./internal').FunctionalComponent}\n */\nexport function memo(c, comparer) {\n\tfunction shouldUpdate(nextProps) {\n\t\tlet ref = this.props.ref;\n\t\tlet updateRef = ref == nextProps.ref;\n\t\tif (!updateRef && ref) {\n\t\t\tref.call ? ref(null) : (ref.current = null);\n\t\t}\n\n\t\tif (!comparer) {\n\t\t\treturn shallowDiffers(this.props, nextProps);\n\t\t}\n\n\t\treturn !comparer(this.props, nextProps) || !updateRef;\n\t}\n\n\tfunction Memoed(props) {\n\t\tthis.shouldComponentUpdate = shouldUpdate;\n\t\treturn createElement(c, assign({}, props));\n\t}\n\tMemoed.prototype.isReactComponent = true;\n\tMemoed.displayName = 'Memo(' + (c.displayName || c.name) + ')';\n\tMemoed._forwarded = true;\n\treturn Memoed;\n}\n", "import { options } from 'preact';\nimport { assign } from './util';\n\nlet oldDiffHook = options._diff;\noptions._diff = vnode => {\n\tif (vnode.type && vnode.type._forwarded && vnode.ref) {\n\t\tvnode.props.ref = vnode.ref;\n\t\tvnode.ref = null;\n\t}\n\tif (oldDiffHook) oldDiffHook(vnode);\n};\n\n/**\n * Pass ref down to a child. This is mainly used in libraries with HOCs that\n * wrap components. Using `forwardRef` there is an easy way to get a reference\n * of the wrapped component instead of one of the wrapper itself.\n * @param {import('./index').ForwardFn} fn\n * @returns {import('./internal').FunctionalComponent}\n */\nexport function forwardRef(fn) {\n\tfunction Forwarded(props) {\n\t\tlet clone = assign({}, props);\n\t\tdelete clone.ref;\n\t\treturn fn(clone, props.ref);\n\t}\n\tForwarded.prototype.isReactComponent = Forwarded._forwarded = true;\n\tForwarded.displayName = 'ForwardRef(' + (fn.displayName || fn.name) + ')';\n\treturn Forwarded;\n}\n", "import { toChildArray } from 'preact';\n\nconst mapFn = (children, fn) => {\n\tif (!children) return null;\n\treturn toChildArray(children).reduce(\n\t\t(acc, value, index) => acc.concat(fn(value, index)),\n\t\t[]\n\t);\n};\n\n// This API is completely unnecessary for Preact, so it's basically passthrough.\nexport const Children = {\n\tmap: mapFn,\n\tforEach: mapFn,\n\tcount(children) {\n\t\treturn children ? toChildArray(children).length : 0;\n\t},\n\tonly(children) {\n\t\tchildren = toChildArray(children);\n\t\tif (children.length !== 1) {\n\t\t\tthrow new Error('Children.only() expects only one child.');\n\t\t}\n\t\treturn children[0];\n\t},\n\ttoArray: toChildArray\n};\n", "import { Component, createElement, options } from 'preact';\nimport { assign } from './util';\n\nconst oldCatchError = options._catchError;\noptions._catchError = function(error, newVNode, oldVNode) {\n\tif (error.then) {\n\t\t/** @type {import('./internal').Component} */\n\t\tlet component;\n\t\tlet vnode = newVNode;\n\n\t\tfor (; (vnode = vnode._parent); ) {\n\t\t\tif ((component = vnode._component) && component._childDidSuspend) {\n\t\t\t\t// Don't call oldCatchError if we found a Suspense\n\t\t\t\treturn component._childDidSuspend(error, newVNode._component);\n\t\t\t}\n\t\t}\n\t}\n\toldCatchError(error, newVNode, oldVNode);\n};\n\nfunction detachedClone(vnode) {\n\tif (vnode) {\n\t\tvnode = assign({}, vnode);\n\t\tvnode._component = null;\n\t\tvnode._children = vnode._children && vnode._children.map(detachedClone);\n\t}\n\treturn vnode;\n}\n\n// having custom inheritance instead of a class here saves a lot of bytes\nexport function Suspense() {\n\t// we do not call super here to golf some bytes...\n\tthis._pendingSuspensionCount = 0;\n\tthis._suspenders = null;\n\tthis._detachOnNextRender = null;\n}\n\n// Things we do here to save some bytes but are not proper JS inheritance:\n// - call `new Component()` as the prototype\n// - do not set `Suspense.prototype.constructor` to `Suspense`\nSuspense.prototype = new Component();\n\n/**\n * @param {Promise} promise The thrown promise\n * @param {Component<any, any>} suspendingComponent The suspending component\n */\nSuspense.prototype._childDidSuspend = function(promise, suspendingComponent) {\n\t/** @type {import('./internal').SuspenseComponent} */\n\tconst c = this;\n\n\tif (c._suspenders == null) {\n\t\tc._suspenders = [];\n\t}\n\tc._suspenders.push(suspendingComponent);\n\n\tconst resolve = suspended(c._vnode);\n\n\tlet resolved = false;\n\tconst onResolved = () => {\n\t\tif (resolved) return;\n\n\t\tresolved = true;\n\n\t\tif (resolve) {\n\t\t\tresolve(onSuspensionComplete);\n\t\t} else {\n\t\t\tonSuspensionComplete();\n\t\t}\n\t};\n\n\tsuspendingComponent._suspendedComponentWillUnmount =\n\t\tsuspendingComponent.componentWillUnmount;\n\tsuspendingComponent.componentWillUnmount = () => {\n\t\tonResolved();\n\n\t\tif (suspendingComponent._suspendedComponentWillUnmount) {\n\t\t\tsuspendingComponent._suspendedComponentWillUnmount();\n\t\t}\n\t};\n\n\tconst onSuspensionComplete = () => {\n\t\tif (!--c._pendingSuspensionCount) {\n\t\t\tc._vnode._children[0] = c.state._suspended;\n\t\t\tc.setState({ _suspended: (c._detachOnNextRender = null) });\n\n\t\t\tlet suspended;\n\t\t\twhile ((suspended = c._suspenders.pop())) {\n\t\t\t\tsuspended.forceUpdate();\n\t\t\t}\n\t\t}\n\t};\n\n\tif (!c._pendingSuspensionCount++) {\n\t\tc.setState({ _suspended: (c._detachOnNextRender = c._vnode._children[0]) });\n\t}\n\tpromise.then(onResolved, onResolved);\n};\n\nSuspense.prototype.render = function(props, state) {\n\tif (this._detachOnNextRender) {\n\t\tthis._vnode._children[0] = detachedClone(this._detachOnNextRender);\n\t\tthis._detachOnNextRender = null;\n\t}\n\n\treturn [\n\t\tcreateElement(Component, null, state._suspended ? null : props.children),\n\t\tstate._suspended && props.fallback\n\t];\n};\n\n/**\n * Checks and calls the parent component's _suspended method, passing in the\n * suspended vnode. This is a way for a parent (e.g. SuspenseList) to get notified\n * that one of its children/descendants suspended.\n *\n * The parent MAY return a callback. The callback will get called when the\n * suspension resolves, notifying the parent of the fact.\n * Moreover, the callback gets function `unsuspend` as a parameter. The resolved\n * child descendant will not actually get unsuspended until `unsuspend` gets called.\n * This is a way for the parent to delay unsuspending.\n *\n * If the parent does not return a callback then the resolved vnode\n * gets unsuspended immediately when it resolves.\n *\n * @param {import('../src/internal').VNode} vnode\n * @returns {((unsuspend: () => void) => void)?}\n */\nexport function suspended(vnode) {\n\tlet component = vnode._parent._component;\n\treturn component && component._suspended && component._suspended(vnode);\n}\n\nexport function lazy(loader) {\n\tlet prom;\n\tlet component;\n\tlet error;\n\n\tfunction Lazy(props) {\n\t\tif (!prom) {\n\t\t\tprom = loader();\n\t\t\tprom.then(\n\t\t\t\texports => {\n\t\t\t\t\tcomponent = exports.default || exports;\n\t\t\t\t},\n\t\t\t\te => {\n\t\t\t\t\terror = e;\n\t\t\t\t}\n\t\t\t);\n\t\t}\n\n\t\tif (error) {\n\t\t\tthrow error;\n\t\t}\n\n\t\tif (!component) {\n\t\t\tthrow prom;\n\t\t}\n\n\t\treturn createElement(component, props);\n\t}\n\n\tLazy.displayName = 'Lazy';\n\tLazy._forwarded = true;\n\treturn Lazy;\n}\n", "import { Component, toChildArray } from 'preact';\nimport { suspended } from './suspense.js';\n\n// Indexes to linked list nodes (nodes are stored as arrays to save bytes).\nconst SUSPENDED_COUNT = 0;\nconst RESOLVED_COUNT = 1;\nconst NEXT_NODE = 2;\n\n// Having custom inheritance instead of a class here saves a lot of bytes.\nexport function SuspenseList() {\n\tthis._next = null;\n\tthis._map = null;\n}\n\n// Mark one of child's earlier suspensions as resolved.\n// Some pending callbacks may become callable due to this\n// (e.g. the last suspended descendant gets resolved when\n// revealOrder === 'together'). Process those callbacks as well.\nconst resolve = (list, child, node) => {\n\tif (++node[RESOLVED_COUNT] === node[SUSPENDED_COUNT]) {\n\t\t// The number a child (or any of its descendants) has been suspended\n\t\t// matches the number of times it's been resolved. Therefore we\n\t\t// mark the child as completely resolved by deleting it from ._map.\n\t\t// This is used to figure out when *all* children have been completely\n\t\t// resolved when revealOrder is 'together'.\n\t\tlist._map.delete(child);\n\t}\n\n\t// If revealOrder is falsy then we can do an early exit, as the\n\t// callbacks won't get queued in the node anyway.\n\t// If revealOrder is 'together' then also do an early exit\n\t// if all suspended descendants have not yet been resolved.\n\tif (\n\t\t!list.props.revealOrder ||\n\t\t(list.props.revealOrder[0] === 't' && list._map.size)\n\t) {\n\t\treturn;\n\t}\n\n\t// Walk the currently suspended children in order, calling their\n\t// stored callbacks on the way. Stop if we encounter a child that\n\t// has not been completely resolved yet.\n\tnode = list._next;\n\twhile (node) {\n\t\twhile (node.length > 3) {\n\t\t\tnode.pop()();\n\t\t}\n\t\tif (node[RESOLVED_COUNT] < node[SUSPENDED_COUNT]) {\n\t\t\tbreak;\n\t\t}\n\t\tlist._next = node = node[NEXT_NODE];\n\t}\n};\n\n// Things we do here to save some bytes but are not proper JS inheritance:\n// - call `new Component()` as the prototype\n// - do not set `Suspense.prototype.constructor` to `Suspense`\nSuspenseList.prototype = new Component();\n\nSuspenseList.prototype._suspended = function(child) {\n\tconst list = this;\n\tconst delegated = suspended(list._vnode);\n\n\tlet node = list._map.get(child);\n\tnode[SUSPENDED_COUNT]++;\n\n\treturn unsuspend => {\n\t\tconst wrappedUnsuspend = () => {\n\t\t\tif (!list.props.revealOrder) {\n\t\t\t\t// Special case the undefined (falsy) revealOrder, as there\n\t\t\t\t// is no need to coordinate a specific order or unsuspends.\n\t\t\t\tunsuspend();\n\t\t\t} else {\n\t\t\t\tnode.push(unsuspend);\n\t\t\t\tresolve(list, child, node);\n\t\t\t}\n\t\t};\n\t\tif (delegated) {\n\t\t\tdelegated(wrappedUnsuspend);\n\t\t} else {\n\t\t\twrappedUnsuspend();\n\t\t}\n\t};\n};\n\nSuspenseList.prototype.render = function(props) {\n\tthis._next = null;\n\tthis._map = new Map();\n\n\tconst children = toChildArray(props.children);\n\tif (props.revealOrder && props.revealOrder[0] === 'b') {\n\t\t// If order === 'backwards' (or, well, anything starting with a 'b')\n\t\t// then flip the child list around so that the last child will be\n\t\t// the first in the linked list.\n\t\tchildren.reverse();\n\t}\n\t// Build the linked list. Iterate through the children in reverse order\n\t// so that `_next` points to the first linked list node to be resolved.\n\tfor (let i = children.length; i--; ) {\n\t\t// Create a new linked list node as an array of form:\n\t\t// \t[suspended_count, resolved_count, next_node]\n\t\t// where suspended_count and resolved_count are numeric counters for\n\t\t// keeping track how many times a node has been suspended and resolved.\n\t\t//\n\t\t// Note that suspended_count starts from 1 instead of 0, so we can block\n\t\t// processing callbacks until componentDidMount has been called. In a sense\n\t\t// node is suspended at least until componentDidMount gets called!\n\t\t//\n\t\t// Pending callbacks are added to the end of the node:\n\t\t// \t[suspended_count, resolved_count, next_node, callback_0, callback_1, ...]\n\t\tthis._map.set(children[i], (this._next = [1, 0, this._next]));\n\t}\n\treturn props.children;\n};\n\nSuspenseList.prototype.componentDidUpdate = SuspenseList.prototype.componentDidMount = function() {\n\t// Iterate through all children after mounting for two reasons:\n\t// 1. As each node[SUSPENDED_COUNT] starts from 1, this iteration increases\n\t//    each node[RELEASED_COUNT] by 1, therefore balancing the counters.\n\t//    The nodes can now be completely consumed from the linked list.\n\t// 2. Handle nodes that might have gotten resolved between render and\n\t//    componentDidMount.\n\tconst list = this;\n\tlist._map.forEach((node, child) => {\n\t\tresolve(list, child, node);\n\t});\n};\n", "import { createElement, hydrate, render, _unmount } from 'preact';\n\nclass ContextProvider {\n\tgetChildContext() {\n\t\treturn this.props.context;\n\t}\n\trender(props) {\n\t\treturn props.children;\n\t}\n}\n\n/**\n * Portal component\n * @param {object | null | undefined} props\n */\nfunction Portal(props) {\n\tlet _this = this;\n\tlet container = props.container;\n\tlet wrap = createElement(\n\t\tContextProvider,\n\t\t{ context: _this.context },\n\t\tprops.vnode\n\t);\n\n\t// When we change container we should clear our old container and\n\t// indicate a new mount.\n\tif (_this._container && _this._container !== container) {\n\t\tif (_this._temp.parentNode) _this._container.removeChild(_this._temp);\n\t\t_unmount(_this._wrap);\n\t\t_this._hasMounted = false;\n\t}\n\n\t// When props.vnode is undefined/false/null we are dealing with some kind of\n\t// conditional vnode. This should not trigger a render.\n\tif (props.vnode) {\n\t\tif (!_this._hasMounted) {\n\t\t\t// Create a placeholder that we can use to insert into.\n\t\t\t_this._temp = document.createTextNode('');\n\t\t\t// Hydrate existing nodes to keep the dom intact, when rendering\n\t\t\t// wrap into the container.\n\t\t\thydrate('', container);\n\t\t\t// Append to the container (this matches <PERSON><PERSON>'s behavior)\n\t\t\tcontainer.appendChild(_this._temp);\n\t\t\t// At this point we have mounted and should set our container.\n\t\t\t_this._hasMounted = true;\n\t\t\t_this._container = container;\n\t\t\t// Render our wrapping element into temp.\n\t\t\trender(wrap, container, _this._temp);\n\t\t\t_this._children = _this._temp._children;\n\t\t} else {\n\t\t\t// When we have mounted and the vnode is present it means the\n\t\t\t// props have changed or a parent is triggering a rerender.\n\t\t\t// This implies we only need to call render. But we need to keep\n\t\t\t// the old tree around, otherwise will treat the vnodes as new and\n\t\t\t// will wrongly call `componentDidMount` on them\n\t\t\tcontainer._children = _this._children;\n\t\t\trender(wrap, container);\n\t\t\t_this._children = container._children;\n\t\t}\n\t}\n\t// When we come from a conditional render, on a mounted\n\t// portal we should clear the DOM.\n\telse if (_this._hasMounted) {\n\t\tif (_this._temp.parentNode) _this._container.removeChild(_this._temp);\n\t\t_unmount(_this._wrap);\n\t}\n\t// Set the wrapping element for future unmounting.\n\t_this._wrap = wrap;\n\n\t_this.componentWillUnmount = () => {\n\t\tif (_this._temp.parentNode) _this._container.removeChild(_this._temp);\n\t\t_unmount(_this._wrap);\n\t};\n\n\treturn null;\n}\n\n/**\n * Create a `Portal` to continue rendering the vnode tree at a different DOM node\n * @param {import('./internal').VNode} vnode The vnode to render\n * @param {import('./internal').PreactElement} container The DOM node to continue rendering in to.\n */\nexport function createPortal(vnode, container) {\n\treturn createElement(Portal, { vnode, container });\n}\n", "import {\n\trender as preactRender,\n\thydrate as preactHydrate,\n\toptions,\n\ttoChildArray,\n\tComponent\n} from 'preact';\nimport { applyEventNormalization } from './events';\n\nconst CAMEL_PROPS = /^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|fill|flood|font|glyph(?!R)|horiz|marker(?!H|W|U)|overline|paint|stop|strikethrough|stroke|text(?!L)|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/;\n\n// Some libraries like `react-virtualized` explicitly check for this.\nComponent.prototype.isReactComponent = {};\n\nexport const REACT_ELEMENT_TYPE =\n\t(typeof Symbol != 'undefined' && Symbol.for && Symbol.for('react.element')) ||\n\t0xeac7;\n\n/**\n * Proxy render() since React returns a Component reference.\n * @param {import('./internal').VNode} vnode VNode tree to render\n * @param {import('./internal').PreactElement} parent DOM node to render vnode tree into\n * @param {() => void} [callback] Optional callback that will be called after rendering\n * @returns {import('./internal').Component | null} The root component reference or null\n */\nexport function render(vnode, parent, callback) {\n\t// React destroys any existing DOM nodes, see #1727\n\t// ...but only on the first render, see #1828\n\tif (parent._children == null) {\n\t\twhile (parent.firstChild) {\n\t\t\tparent.removeChild(parent.firstChild);\n\t\t}\n\t}\n\n\tpreactRender(vnode, parent);\n\tif (typeof callback == 'function') callback();\n\n\treturn vnode ? vnode._component : null;\n}\n\nexport function hydrate(vnode, parent, callback) {\n\tpreactHydrate(vnode, parent);\n\tif (typeof callback == 'function') callback();\n\n\treturn vnode ? vnode._component : null;\n}\n\nlet oldEventHook = options.event;\noptions.event = e => {\n\tif (oldEventHook) e = oldEventHook(e);\n\te.persist = () => {};\n\tlet stoppedPropagating = false,\n\t\tdefaultPrevented = false;\n\n\tconst origStopPropagation = e.stopPropagation;\n\te.stopPropagation = () => {\n\t\torigStopPropagation.call(e);\n\t\tstoppedPropagating = true;\n\t};\n\n\tconst origPreventDefault = e.preventDefault;\n\te.preventDefault = () => {\n\t\torigPreventDefault.call(e);\n\t\tdefaultPrevented = true;\n\t};\n\n\te.isPropagationStopped = () => stoppedPropagating;\n\te.isDefaultPrevented = () => defaultPrevented;\n\treturn (e.nativeEvent = e);\n};\n\n// Patch in `UNSAFE_*` lifecycle hooks\nfunction setSafeDescriptor(proto, key) {\n\tif (proto['UNSAFE_' + key] && !proto[key]) {\n\t\tObject.defineProperty(proto, key, {\n\t\t\tconfigurable: false,\n\t\t\tget() {\n\t\t\t\treturn this['UNSAFE_' + key];\n\t\t\t},\n\t\t\t// This `set` is only used if a user sets a lifecycle like cWU\n\t\t\t// after setting a lifecycle like UNSAFE_cWU. I doubt anyone\n\t\t\t// actually does this in practice so not testing it\n\t\t\t/* istanbul ignore next */\n\t\t\tset(v) {\n\t\t\t\tthis['UNSAFE_' + key] = v;\n\t\t\t}\n\t\t});\n\t}\n}\n\nlet classNameDescriptor = {\n\tconfigurable: true,\n\tget() {\n\t\treturn this.class;\n\t}\n};\n\nlet oldVNodeHook = options.vnode;\noptions.vnode = vnode => {\n\tvnode.$$typeof = REACT_ELEMENT_TYPE;\n\n\tlet type = vnode.type;\n\tlet props = vnode.props;\n\n\tif (type) {\n\t\t// Alias `class` prop to `className` if available\n\t\tif (props.class != props.className) {\n\t\t\tclassNameDescriptor.enumerable = 'className' in props;\n\t\t\tif (props.className != null) props.class = props.className;\n\t\t\tObject.defineProperty(props, 'className', classNameDescriptor);\n\t\t}\n\n\t\t// Apply DOM VNode compat\n\t\tif (typeof type != 'function') {\n\t\t\t// Apply defaultValue to value\n\t\t\tif (props.defaultValue && props.value !== undefined) {\n\t\t\t\tif (!props.value && props.value !== 0) {\n\t\t\t\t\tprops.value = props.defaultValue;\n\t\t\t\t}\n\t\t\t\tdelete props.defaultValue;\n\t\t\t}\n\n\t\t\t// Add support for array select values: <select value={[]} />\n\t\t\tif (Array.isArray(props.value) && props.multiple && type === 'select') {\n\t\t\t\ttoChildArray(props.children).forEach(child => {\n\t\t\t\t\tif (props.value.indexOf(child.props.value) != -1) {\n\t\t\t\t\t\tchild.props.selected = true;\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\tdelete props.value;\n\t\t\t}\n\n\t\t\t// Normalize DOM vnode properties.\n\t\t\tlet shouldSanitize, attrs, i;\n\t\t\tfor (i in props) if ((shouldSanitize = CAMEL_PROPS.test(i))) break;\n\t\t\tif (shouldSanitize) {\n\t\t\t\tattrs = vnode.props = {};\n\t\t\t\tfor (i in props) {\n\t\t\t\t\tattrs[\n\t\t\t\t\t\tCAMEL_PROPS.test(i) ? i.replace(/[A-Z0-9]/, '-$&').toLowerCase() : i\n\t\t\t\t\t] = props[i];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// Events\n\t\tapplyEventNormalization(vnode);\n\n\t\t// Component base class compat\n\t\t// We can't just patch the base component class, because components that use\n\t\t// inheritance and are transpiled down to ES5 will overwrite our patched\n\t\t// getters and setters. See #1941\n\t\tif (\n\t\t\ttypeof type == 'function' &&\n\t\t\t!type._patchedLifecycles &&\n\t\t\ttype.prototype\n\t\t) {\n\t\t\tsetSafeDescriptor(type.prototype, 'componentWillMount');\n\t\t\tsetSafeDescriptor(type.prototype, 'componentWillReceiveProps');\n\t\t\tsetSafeDescriptor(type.prototype, 'componentWillUpdate');\n\t\t\ttype._patchedLifecycles = true;\n\t\t}\n\t}\n\n\tif (oldVNodeHook) oldVNodeHook(vnode);\n};\n", "/**\n * Normalize event handlers like react does. Most famously it uses `onChange` for any input element.\n * @param {import('./internal').VNode} vnode The vnode to normalize events on\n */\nexport function applyEventNormalization({ type, props }) {\n\tif (!props || typeof type != 'string') return;\n\tlet newProps = {};\n\n\tfor (let i in props) {\n\t\tif (/^on(Ani|Tra|Tou)/.test(i)) {\n\t\t\tprops[i.toLowerCase()] = props[i];\n\t\t\tdelete props[i];\n\t\t}\n\t\tnewProps[i.toLowerCase()] = i;\n\t}\n\tif (newProps.ondoubleclick) {\n\t\tprops.ondblclick = props[newProps.ondoubleclick];\n\t\tdelete props[newProps.ondoubleclick];\n\t}\n\tif (newProps.onbeforeinput) {\n\t\tprops.onbeforeinput = props[newProps.onbeforeinput];\n\t\tdelete props[newProps.onbeforeinput];\n\t}\n\t// for *textual inputs* (incl textarea), normalize `onChange` -> `onInput`:\n\tif (\n\t\tnewProps.onchange &&\n\t\t(type === 'textarea' ||\n\t\t\t(type.toLowerCase() === 'input' && !/^fil|che|ra/i.test(props.type)))\n\t) {\n\t\tlet normalized = newProps.oninput || 'oninput';\n\t\tif (!props[normalized]) {\n\t\t\tprops[normalized] = props[newProps.onchange];\n\t\t\tdelete props[newProps.onchange];\n\t\t}\n\t}\n}\n", "import {\n\tcreateElement,\n\trender as preactRender,\n\tcloneElement as preactCloneElement,\n\tcreateRef,\n\tComponent,\n\tcreateContext,\n\tFragment\n} from 'preact';\nimport {\n\tuseState,\n\tuseReducer,\n\tuseEffect,\n\tuseLayoutEffect,\n\tuseRef,\n\tuseImperativeHandle,\n\tuseMemo,\n\tuseCallback,\n\tuseContext,\n\tuseDebugValue\n} from 'preact/hooks';\nimport { PureComponent } from './PureComponent';\nimport { memo } from './memo';\nimport { forwardRef } from './forwardRef';\nimport { Children } from './Children';\nimport { Suspense, lazy } from './suspense';\nimport { SuspenseList } from './suspense-list';\nimport { createPortal } from './portals';\nimport { hydrate, render, REACT_ELEMENT_TYPE } from './render';\n\nconst version = '16.8.0'; // trick libraries to think we are react\n\n/**\n * Legacy version of createElement.\n * @param {import('./internal').VNode[\"type\"]} type The node name or Component constructor\n */\nfunction createFactory(type) {\n\treturn createElement.bind(null, type);\n}\n\n/**\n * Check if the passed element is a valid (p)react node.\n * @param {*} element The element to check\n * @returns {boolean}\n */\nfunction isValidElement(element) {\n\treturn !!element && element.$$typeof === REACT_ELEMENT_TYPE;\n}\n\n/**\n * Wrap `cloneElement` to abort if the passed element is not a valid element and apply\n * all vnode normalizations.\n * @param {import('./internal').VNode} element The vnode to clone\n * @param {object} props Props to add when cloning\n * @param {Array<import('./internal').ComponentChildren>} rest Optional component children\n */\nfunction cloneElement(element) {\n\tif (!isValidElement(element)) return element;\n\treturn preactCloneElement.apply(null, arguments);\n}\n\n/**\n * Remove a component tree from the DOM, including state and event handlers.\n * @param {import('./internal').PreactElement} container\n * @returns {boolean}\n */\nfunction unmountComponentAtNode(container) {\n\tif (container._children) {\n\t\tpreactRender(null, container);\n\t\treturn true;\n\t}\n\treturn false;\n}\n\n/**\n * Get the matching DOM node for a component\n * @param {import('./internal').Component} component\n * @returns {import('./internal').PreactElement | null}\n */\nfunction findDOMNode(component) {\n\treturn (\n\t\t(component &&\n\t\t\t(component.base || (component.nodeType === 1 && component))) ||\n\t\tnull\n\t);\n}\n\n/**\n * Deprecated way to control batched rendering inside the reconciler, but we\n * already schedule in batches inside our rendering code\n * @template Arg\n * @param {(arg: Arg) => void} callback function that triggers the updated\n * @param {Arg} [arg] Optional argument that can be passed to the callback\n */\n// eslint-disable-next-line camelcase\nconst unstable_batchedUpdates = (callback, arg) => callback(arg);\n\nexport * from 'preact/hooks';\nexport {\n\tversion,\n\tChildren,\n\trender,\n\thydrate,\n\tunmountComponentAtNode,\n\tcreatePortal,\n\tcreateElement,\n\tcreateContext,\n\tcreateFactory,\n\tcloneElement,\n\tcreateRef,\n\tFragment,\n\tisValidElement,\n\tfindDOMNode,\n\tComponent,\n\tPureComponent,\n\tmemo,\n\tforwardRef,\n\t// eslint-disable-next-line camelcase\n\tunstable_batchedUpdates,\n\tSuspense,\n\tSuspenseList,\n\tlazy\n};\n\n// React copies the named exports to the default one.\nexport default {\n\tuseState,\n\tuseReducer,\n\tuseEffect,\n\tuseLayoutEffect,\n\tuseRef,\n\tuseImperativeHandle,\n\tuseMemo,\n\tuseCallback,\n\tuseContext,\n\tuseDebugValue,\n\tversion,\n\tChildren,\n\trender,\n\thydrate: render,\n\tunmountComponentAtNode,\n\tcreatePortal,\n\tcreateElement,\n\tcreateContext,\n\tcreateFactory,\n\tcloneElement,\n\tcreateRef,\n\tFragment,\n\tisValidElement,\n\tfindDOMNode,\n\tComponent,\n\tPureComponent,\n\tmemo,\n\tforwardRef,\n\tunstable_batchedUpdates,\n\tSuspense,\n\tSuspenseList,\n\tlazy\n};\n"], "names": ["assign", "obj", "props", "i", "shallow<PERSON>iffers", "a", "b", "PureComponent", "isPureReactComponent", "shouldComponentUpdate", "state", "this", "Component", "memo", "c", "comparer", "shouldUpdate", "nextProps", "ref", "updateRef", "call", "current", "Memoed", "createElement", "prototype", "isReactComponent", "displayName", "name", "_forwarded", "oldDiffHook", "options", "_diff", "forwardRef", "fn", "Forwarded", "clone", "vnode", "type", "mapFn", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reduce", "acc", "value", "index", "concat", "Children", "map", "for<PERSON>ach", "count", "length", "only", "Error", "toArray", "oldCatchError", "_catchError", "detachedClone", "_component", "_children", "Suspense", "_pendingSuspensionCount", "_suspenders", "_detachOnNextRender", "suspended", "component", "_parent", "_suspended", "lazy", "loader", "prom", "error", "Lazy", "then", "exports", "default", "e", "SuspenseList", "_next", "_map", "newVNode", "oldVNode", "_childDidSuspend", "promise", "suspendingComponent", "push", "resolve", "_vnode", "resolved", "onResolved", "onSuspensionComplete", "_suspendedComponentWillUnmount", "componentWillUnmount", "setState", "pop", "forceUpdate", "render", "fallback", "list", "child", "node", "delete", "revealOrder", "size", "delegated", "get", "unsuspend", "wrappedUnsuspend", "Map", "reverse", "set", "componentDidUpdate", "componentDidMount", "ContextProvider", "getChildContext", "context", "Portal", "_this", "container", "wrap", "_container", "_temp", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "_unmount", "_wrap", "_hasMounted", "document", "createTextNode", "hydrate", "append<PERSON><PERSON><PERSON>", "createPortal", "CAMEL_PROPS", "REACT_ELEMENT_TYPE", "Symbol", "for", "parent", "callback", "<PERSON><PERSON><PERSON><PERSON>", "preactRender", "preactHydrate", "oldEventHook", "event", "setSafeDescriptor", "proto", "key", "Object", "defineProperty", "configurable", "v", "persist", "stoppedPropagating", "defaultPrevented", "origStopPropagation", "stopPropagation", "origPreventDefault", "preventDefault", "isPropagationStopped", "isDefaultPrevented", "nativeEvent", "classNameDescriptor", "class", "oldVNodeHook", "$$typeof", "className", "enumerable", "shouldSanitize", "attrs", "defaultValue", "undefined", "Array", "isArray", "multiple", "indexOf", "selected", "test", "replace", "toLowerCase", "newProps", "ondoubleclick", "ondblclick", "onbeforeinput", "onchange", "normalized", "oninput", "applyEventNormalization", "_patchedLifecycles", "version", "createFactory", "bind", "isValidElement", "element", "cloneElement", "preactCloneElement", "apply", "arguments", "unmountComponentAtNode", "findDOMNode", "base", "nodeType", "unstable_batchedUpdates", "arg", "useState", "useReducer", "useEffect", "useLayoutEffect", "useRef", "useImperativeHandle", "useMemo", "useCallback", "useContext", "useDebugValue", "createContext", "createRef", "Fragment"], "mappings": "ueAOO,SAASA,EAAOC,EAAKC,OACtB,IAAIC,KAAKD,EAAOD,EAAIE,GAAKD,EAAMC,YAU9B,SAASC,EAAeC,EAAGC,OAC5B,IAAIH,KAAKE,KAAa,aAANF,KAAsBA,KAAKG,GAAI,OAAO,MACtD,IAAIH,KAAKG,KAAa,aAANH,GAAoBE,EAAEF,KAAOG,EAAEH,GAAI,OAAO,SACxD,MCfKI,EAAb,+BACaL,8BACLA,UAEDM,sBAAuB,8GAG7BC,sBAAA,SAAsBP,EAAOQ,UAE3BN,EAAeO,KAAKT,MAAOA,IAAUE,EAAeO,KAAKD,MAAOA,MATnE,CAAmCE,GCI5B,SAASC,EAAKC,EAAGC,YACdC,EAAaC,OACjBC,EAAMP,KAAKT,MAAMgB,IACjBC,EAAYD,GAAOD,EAAUC,WAC5BC,GAAaD,IACjBA,EAAIE,KAAOF,EAAI,MAASA,EAAIG,QAAU,MAGlCN,GAIGA,EAASJ,KAAKT,MAAOe,KAAeE,EAHpCf,EAAeO,KAAKT,MAAOe,YAM3BK,EAAOpB,eACVO,sBAAwBO,EACtBO,EAAcT,EAAGd,EAAO,GAAIE,WAEpCoB,EAAOE,UAAUC,kBAAmB,EACpCH,EAAOI,YAAc,SAAWZ,EAAEY,aAAeZ,EAAEa,MAAQ,IAC3DL,EAAOM,GAAa,EACbN,EC7BR,IAAIO,EAAcC,EAAQC,IAgBnB,SAASC,EAAWC,YACjBC,EAAUhC,OACdiC,EAAQnC,EAAO,GAAIE,iBAChBiC,EAAMjB,IACNe,EAAGE,EAAOjC,EAAMgB,YAExBgB,EAAUV,UAAUC,iBAAmBS,EAAUN,GAAa,EAC9DM,EAAUR,YAAc,eAAiBO,EAAGP,aAAeO,EAAGN,MAAQ,IAC/DO,EAvBRJ,EAAQC,IAAQ,SAAAK,GACXA,EAAMC,MAAQD,EAAMC,KAAKT,GAAcQ,EAAMlB,MAChDkB,EAAMlC,MAAMgB,IAAMkB,EAAMlB,IACxBkB,EAAMlB,IAAM,MAETW,GAAaA,EAAYO,QCPxBE,EAAQ,SAACC,EAAUN,UACnBM,EACEC,EAAaD,GAAUE,OAC7B,SAACC,EAAKC,EAAOC,UAAUF,EAAIG,OAAOZ,EAAGU,EAAOC,KAC5C,IAHqB,MAQVE,EAAW,CACvBC,IAAKT,EACLU,QAASV,EACTW,eAAMV,UACEA,EAAWC,EAAaD,GAAUW,OAAS,GAEnDC,cAAKZ,MAEoB,KADxBA,EAAWC,EAAaD,IACXW,aACN,IAAIE,MAAM,kDAEVb,EAAS,IAEjBc,QAASb,GCrBJc,EAAgBxB,EAAQyB,IAiB9B,SAASC,EAAcpB,UAClBA,KACHA,EAAQpC,EAAO,GAAIoC,IACbqB,IAAa,KACnBrB,EAAMsB,IAAYtB,EAAMsB,KAAatB,EAAMsB,IAAUX,IAAIS,IAEnDpB,EAIR,SAAgBuB,SAEVC,IAA0B,OAC1BC,EAAc,UACdC,IAAsB,KA6FrB,SAASC,EAAU3B,OACrB4B,EAAY5B,EAAM6B,GAAQR,WACvBO,GAAaA,EAAUE,GAAcF,EAAUE,EAAW9B,GAG3D,SAAS+B,EAAKC,OAChBC,EACAL,EACAM,WAEKC,EAAKrE,MACRmE,IACJA,EAAOD,KACFI,KACJ,SAAAC,GACCT,EAAYS,EAAQC,SAAWD,GAEhC,SAAAE,GACCL,EAAQK,IAKPL,QACGA,MAGFN,QACEK,SAGA9C,EAAcyC,EAAW9D,UAGjCqE,EAAK7C,YAAc,OACnB6C,EAAK3C,GAAa,EACX2C,EC1JR,SAAgBK,SACVC,EAAQ,UACRC,EAAO,KDPbhD,EAAQyB,IAAc,SAASe,EAAOS,EAAUC,MAC3CV,EAAME,aAELR,EACA5B,EAAQ2C,EAEJ3C,EAAQA,EAAM6B,QAChBD,EAAY5B,EAAMqB,MAAeO,EAAUiB,WAExCjB,EAAUiB,IAAiBX,EAAOS,EAAStB,KAIrDH,EAAcgB,EAAOS,EAAUC,KAuBhCrB,EAASnC,UAAY,IAAIZ,GAMNqE,IAAmB,SAASC,EAASC,OAEjDrE,EAAIH,KAEW,MAAjBG,EAAE+C,IACL/C,EAAE+C,EAAc,IAEjB/C,EAAE+C,EAAYuB,KAAKD,OAEbE,EAAUtB,EAAUjD,EAAEwE,KAExBC,GAAW,EACTC,EAAa,WACdD,IAEJA,GAAW,EAEPF,EACHA,EAAQI,GAERA,MAIFN,EAAoBO,IACnBP,EAAoBQ,qBACrBR,EAAoBQ,qBAAuB,WAC1CH,IAEIL,EAAoBO,KACvBP,EAAoBO,WAIhBD,EAAuB,eAKvB1B,QAJEjD,EAAE8C,QACR9C,EAAEwE,IAAO5B,IAAU,GAAK5C,EAAEJ,MAAMwD,EAChCpD,EAAE8E,SAAS,CAAE1B,EAAapD,EAAEgD,IAAsB,OAG1CC,EAAYjD,EAAE+C,EAAYgC,OACjC9B,EAAU+B,eAKRhF,EAAE8C,OACN9C,EAAE8E,SAAS,CAAE1B,EAAapD,EAAEgD,IAAsBhD,EAAEwE,IAAO5B,IAAU,KAEtEwB,EAAQV,KAAKgB,EAAYA,IAG1B7B,EAASnC,UAAUuE,OAAS,SAAS7F,EAAOQ,UACvCC,KAAKmD,WACHwB,IAAO5B,IAAU,GAAKF,EAAc7C,KAAKmD,UACzCA,IAAsB,MAGrB,CACNvC,EAAcX,EAAW,KAAMF,EAAMwD,EAAa,KAAOhE,EAAMqC,UAC/D7B,EAAMwD,GAAchE,EAAM8F,WCxF5B,IAAMX,EAAU,SAACY,EAAMC,EAAOC,QACvBA,EAdgB,KAcSA,EAfR,IAqBtBF,EAAKnB,EAAKsB,OAAOF,GAQhBD,EAAK/F,MAAMmG,cACmB,MAA9BJ,EAAK/F,MAAMmG,YAAY,KAAcJ,EAAKnB,EAAKwB,UAQjDH,EAAOF,EAAKpB,EACLsB,GAAM,MACLA,EAAKjD,OAAS,GACpBiD,EAAKN,KAALM,MAEGA,EA1CiB,GA0CMA,EA3CL,SA8CtBF,EAAKpB,EAAQsB,EAAOA,EA5CJ,MAmDlBvB,EAAapD,UAAY,IAAIZ,GAENsD,EAAa,SAASgC,OACtCD,EAAOtF,KACP4F,EAAYxC,EAAUkC,EAAKX,KAE7Ba,EAAOF,EAAKnB,EAAK0B,IAAIN,UACzBC,EA5DuB,KA8DhB,SAAAM,OACAC,EAAmB,WACnBT,EAAK/F,MAAMmG,aAKfF,EAAKf,KAAKqB,GACVpB,EAAQY,EAAMC,EAAOC,IAHrBM,KAMEF,EACHA,EAAUG,GAEVA,MAKH9B,EAAapD,UAAUuE,OAAS,SAAS7F,QACnC2E,EAAQ,UACRC,EAAO,IAAI6B,QAEVpE,EAAWC,EAAatC,EAAMqC,UAChCrC,EAAMmG,aAAwC,MAAzBnG,EAAMmG,YAAY,IAI1C9D,EAASqE,cAIL,IAAIzG,EAAIoC,EAASW,OAAQ/C,UAYxB2E,EAAK+B,IAAItE,EAASpC,GAAKQ,KAAKkE,EAAQ,CAAC,EAAG,EAAGlE,KAAKkE,WAE/C3E,EAAMqC,UAGdqC,EAAapD,UAAUsF,mBAAqBlC,EAAapD,UAAUuF,kBAAoB,eAOhFd,EAAOtF,KACbsF,EAAKnB,EAAK9B,QAAQ,SAACmD,EAAMD,GACxBb,EAAQY,EAAMC,EAAOC,UC1HjBa,sDACLC,gBAAA,kBACQtG,KAAKT,MAAMgH,WAEnBnB,OAAA,SAAO7F,UACCA,EAAMqC,eAQf,SAAS4E,EAAOjH,OACXkH,EAAQzG,KACR0G,EAAYnH,EAAMmH,UAClBC,EAAO/F,EACVyF,EACA,CAAEE,QAASE,EAAMF,SACjBhH,EAAMkC,cAKHgF,EAAMG,GAAcH,EAAMG,IAAeF,IACxCD,EAAMI,EAAMC,YAAYL,EAAMG,EAAWG,YAAYN,EAAMI,GAC/DG,EAASP,EAAMQ,GACfR,EAAMS,GAAc,GAKjB3H,EAAMkC,MACJgF,EAAMS,GAoBVR,EAAU3D,IAAY0D,EAAM1D,IAC5BqC,EAAOuB,EAAMD,GACbD,EAAM1D,IAAY2D,EAAU3D,MApB5B0D,EAAMI,EAAQM,SAASC,eAAe,IAGtCC,EAAQ,GAAIX,GAEZA,EAAUY,YAAYb,EAAMI,GAE5BJ,EAAMS,GAAc,EACpBT,EAAMG,EAAaF,EAEnBtB,EAAOuB,EAAMD,EAAWD,EAAMI,GAC9BJ,EAAM1D,IAAY0D,EAAMI,EAAM9D,KAcvB0D,EAAMS,IACVT,EAAMI,EAAMC,YAAYL,EAAMG,EAAWG,YAAYN,EAAMI,GAC/DG,EAASP,EAAMQ,IAGhBR,EAAMQ,EAAQN,EAEdF,EAAMzB,qBAAuB,WACxByB,EAAMI,EAAMC,YAAYL,EAAMG,EAAWG,YAAYN,EAAMI,GAC/DG,EAASP,EAAMQ,IAGT,KAQD,SAASM,EAAa9F,EAAOiF,UAC5B9F,EAAc4F,EAAQ,CAAE/E,MAAAA,EAAOiF,UAAAA,IC1EvC,IAAMc,EAAc,mOAGpBvH,EAAUY,UAAUC,iBAAmB,GAEvC,IAAa2G,EACM,oBAAVC,QAAyBA,OAAOC,KAAOD,OAAOC,IAAI,kBAC1D,MASD,SAAgBvC,EAAO3D,EAAOmG,EAAQC,MAGb,MAApBD,EAAO7E,SACH6E,EAAOE,YACbF,EAAOb,YAAYa,EAAOE,mBAI5BC,EAAatG,EAAOmG,GACG,mBAAZC,GAAwBA,IAE5BpG,EAAQA,EAAMqB,IAAa,KAGnC,SAAgBuE,EAAQ5F,EAAOmG,EAAQC,UACtCG,EAAcvG,EAAOmG,GACE,mBAAZC,GAAwBA,IAE5BpG,EAAQA,EAAMqB,IAAa,KAGnC,IAAImF,EAAe9G,EAAQ+G,MAyB3B,SAASC,EAAkBC,EAAOC,GAC7BD,EAAM,UAAYC,KAASD,EAAMC,IACpCC,OAAOC,eAAeH,EAAOC,EAAK,CACjCG,cAAc,EACd3C,sBACQ7F,KAAK,UAAYqI,IAMzBnC,aAAIuC,QACE,UAAYJ,GAAOI,KApC5BtH,EAAQ+G,MAAQ,SAAAlE,GACXiE,IAAcjE,EAAIiE,EAAajE,IACnCA,EAAE0E,QAAU,iBACRC,GAAqB,EACxBC,GAAmB,EAEdC,EAAsB7E,EAAE8E,gBAC9B9E,EAAE8E,gBAAkB,WACnBD,EAAoBpI,KAAKuD,GACzB2E,GAAqB,OAGhBI,EAAqB/E,EAAEgF,sBAC7BhF,EAAEgF,eAAiB,WAClBD,EAAmBtI,KAAKuD,GACxB4E,GAAmB,GAGpB5E,EAAEiF,qBAAuB,kBAAMN,GAC/B3E,EAAEkF,mBAAqB,kBAAMN,GACrB5E,EAAEmF,YAAcnF,GAsBzB,IAAIoF,EAAsB,CACzBZ,cAAc,EACd3C,sBACQ7F,KAAKqJ,QAIVC,EAAenI,EAAQM,MAC3BN,EAAQM,MAAQ,SAAAA,GACfA,EAAM8H,SAAW9B,MAEb/F,EAAOD,EAAMC,KACbnC,EAAQkC,EAAMlC,SAEdmC,EAAM,IAELnC,EAAM8J,OAAS9J,EAAMiK,YACxBJ,EAAoBK,WAAa,cAAelK,EACzB,MAAnBA,EAAMiK,YAAmBjK,EAAM8J,MAAQ9J,EAAMiK,WACjDlB,OAAOC,eAAehJ,EAAO,YAAa6J,IAIxB,mBAAR1H,EAAoB,KAoB1BgI,EAAgBC,EAAOnK,MACtBA,KAnBDD,EAAMqK,mBAAgCC,IAAhBtK,EAAMyC,QAC1BzC,EAAMyC,OAAyB,IAAhBzC,EAAMyC,QACzBzC,EAAMyC,MAAQzC,EAAMqK,qBAEdrK,EAAMqK,cAIVE,MAAMC,QAAQxK,EAAMyC,QAAUzC,EAAMyK,UAAqB,WAATtI,IACnDG,EAAatC,EAAMqC,UAAUS,QAAQ,SAAAkD,IACW,GAA3ChG,EAAMyC,MAAMiI,QAAQ1E,EAAMhG,MAAMyC,SACnCuD,EAAMhG,MAAM2K,UAAW,YAGlB3K,EAAMyC,OAKJzC,KAAYmK,EAAiBlC,EAAY2C,KAAK3K,GAAK,SACzDkK,MAEElK,KADLmK,EAAQlI,EAAMlC,MAAQ,GACZA,EACToK,EACCnC,EAAY2C,KAAK3K,GAAKA,EAAE4K,QAAQ,WAAY,OAAOC,cAAgB7K,GAChED,EAAMC,ICxIR,gBAAmCkC,ED8IhBD,EC9IgBC,KAAMnC,ED8ItBkC,EC9IsBlC,SAC1CA,GAAwB,iBAARmC,OACjB4I,EAAW,OAEV,IAAI9K,KAAKD,EACT,mBAAmB4K,KAAK3K,KAC3BD,EAAMC,EAAE6K,eAAiB9K,EAAMC,UACxBD,EAAMC,IAEd8K,EAAS9K,EAAE6K,eAAiB7K,KAEzB8K,EAASC,gBACZhL,EAAMiL,WAAajL,EAAM+K,EAASC,sBAC3BhL,EAAM+K,EAASC,gBAEnBD,EAASG,gBACZlL,EAAMkL,cAAgBlL,EAAM+K,EAASG,sBAC9BlL,EAAM+K,EAASG,gBAItBH,EAASI,WACC,aAAThJ,GACwB,UAAvBA,EAAK2I,gBAA8B,eAAeF,KAAK5K,EAAMmC,OAC9D,KACGiJ,EAAaL,EAASM,SAAW,UAChCrL,EAAMoL,KACVpL,EAAMoL,GAAcpL,EAAM+K,EAASI,iBAC5BnL,EAAM+K,EAASI,aDkHvBG,GAOgB,mBAARnJ,IACNA,EAAKoJ,GACNpJ,EAAKb,YAELsH,EAAkBzG,EAAKb,UAAW,sBAClCsH,EAAkBzG,EAAKb,UAAW,6BAClCsH,EAAkBzG,EAAKb,UAAW,uBAClCa,EAAKoJ,GAAqB,GAIxBxB,GAAcA,EAAa7H,QEtI1BsJ,EAAU,SAMhB,SAASC,EAActJ,UACfd,EAAcqK,KAAK,KAAMvJ,GAQjC,SAASwJ,EAAeC,WACdA,GAAWA,EAAQ5B,WAAa9B,EAU1C,SAAS2D,EAAaD,UAChBD,EAAeC,GACbE,EAAmBC,MAAM,KAAMC,WADDJ,EAStC,SAASK,EAAuB9E,WAC3BA,EAAU3D,MACbgF,EAAa,KAAMrB,IACZ,GAUT,SAAS+E,EAAYpI,UAElBA,IACCA,EAAUqI,MAAgC,IAAvBrI,EAAUsI,UAAkBtI,IACjD,KAYF,IAAMuI,EAA0B,SAAC/D,EAAUgE,UAAQhE,EAASgE,kBA8B7C,CACdC,SAAAA,EACAC,WAAAA,EACAC,UAAAA,EACAC,gBAAAA,EACAC,OAAAA,EACAC,oBAAAA,EACAC,QAAAA,EACAC,YAAAA,EACAC,WAAAA,EACAC,cAAAA,EACAxB,QA1Ge,SA2Gf5I,SAAAA,EACAiD,OAAAA,EACAiC,QAASjC,EACToG,uBAAAA,EACAjE,aAAAA,EACA3G,cAAAA,EACA4L,cAAAA,EACAxB,cAAAA,EACAI,aAAAA,EACAqB,UAAAA,EACAC,SAAAA,EACAxB,eAAAA,EACAO,YAAAA,EACAxL,UAAAA,EACAL,cAAAA,EACAM,KAAAA,EACAmB,WAAAA,EACAuK,wBAAAA,EACA5I,SAAAA,EACAiB,aAAAA,EACAT,KAAAA"}