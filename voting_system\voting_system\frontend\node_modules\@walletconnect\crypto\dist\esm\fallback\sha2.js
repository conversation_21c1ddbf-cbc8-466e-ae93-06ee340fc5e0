import { fallbackSha256, fallbackSha512, fallbackRipemd160 } from "../lib/fallback";
export async function sha256(msg) {
    const result = fallbackSha256(msg);
    return result;
}
export async function sha512(msg) {
    const result = fallbackSha512(msg);
    return result;
}
export async function ripemd160(msg) {
    const result = fallbackRipemd160(msg);
    return result;
}
//# sourceMappingURL=sha2.js.map