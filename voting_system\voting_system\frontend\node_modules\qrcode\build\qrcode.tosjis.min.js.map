{"version": 3, "sources": ["build/qrcode.tosjis.js"], "names": ["f", "exports", "module", "define", "amd", "g", "window", "global", "self", "this", "QRCode", "toSJIS", "r", "e", "n", "t", "o", "i", "c", "require", "u", "a", "Error", "code", "p", "call", "length", "1", "SJIS_UTF8", "utf8Char", "kanji", "posIndex", "indexOf"], "mappings": "CAAA,SAAUA,GAAG,GAAoB,gBAAVC,UAAoC,mBAATC,QAAsBA,OAAOD,QAAQD,QAAS,IAAmB,kBAATG,SAAqBA,OAAOC,IAAKD,UAAUH,OAAO,CAAC,GAAIK,EAAkCA,GAAb,mBAATC,QAAwBA,OAA+B,mBAATC,QAAwBA,OAA6B,mBAAPC,MAAsBA,KAAYC,MAAMJ,EAAEK,SAAWL,EAAEK,YAAcC,OAASX,MAAO,WAAqC,MAAO,YAAY,QAASY,GAAEC,EAAEC,EAAEC,GAAG,QAASC,GAAEC,EAAEjB,GAAG,IAAIc,EAAEG,GAAG,CAAC,IAAIJ,EAAEI,GAAG,CAAC,GAAIC,GAAE,kBAAmBC,UAASA,OAAQ,KAAInB,GAAGkB,EAAE,MAAOA,GAAED,GAAE,EAAI,IAAGG,EAAE,MAAOA,GAAEH,GAAE,EAAI,IAAII,GAAE,GAAIC,OAAM,uBAAuBL,EAAE,IAAK,MAAMI,GAAEE,KAAK,mBAAmBF,EAAE,GAAIG,GAAEV,EAAEG,IAAIhB,WAAYY,GAAEI,GAAG,GAAGQ,KAAKD,EAAEvB,QAAQ,SAASW,GAAoB,MAAOI,GAAlBH,EAAEI,GAAG,GAAGL,IAAeA,IAAIY,EAAEA,EAAEvB,QAAQW,EAAEC,EAAEC,EAAEC,GAAG,MAAOD,GAAEG,GAAGhB,QAAQ,IAAI,GAAImB,GAAE,kBAAmBD,UAASA,QAAQF,EAAE,EAAEA,EAAEF,EAAEW,OAAOT,IAAID,EAAED,EAAEE,GAAI,OAAOD,GAAE,MAAOJ,OAAOe,GAAG,SAASR,EAAQjB,EAAOD,GACl3B,GAAI2B,KACD,MAAQ,oEACR,MAAQ,UACR,MAAQ,iBACR,MAAQ,YACR,MAAQ,MACR,MAAQ,eACR,MAAQ,+BACR,MAAQ,+BACR,MAAQ,wFACR,MAAQ,oEACR,MAAQ,4BACR,MAAQ,6BACR,MAAQ,6BACR,MAAQ,sCACR,MAAQ,oBACR,MAAQ,uBACR,MAAQ,OACR,MAAQ,oEACR,MAAQ,kIACR,MAAQ,oEACR,MAAQ,kIACR,MAAQ,oEACR,MAAQ,kIACR,MAAQ,oEACR,MAAQ,kIACR,MAAQ,oEACR,MAAQ,kIACR,MAAQ,oEACR,MAAQ,kIACR,MAAQ,oEACR,MAAQ,kIACR,MAAQ,oEACR,MAAQ,kIACR,MAAQ,oEACR,MAAQ,kIACR,MAAQ,oEACR,MAAQ,kIACR,MAAQ,oEACR,MAAQ,kIACR,MAAQ,oEACR,MAAQ,kIACR,MAAQ,oEACR,MAAQ,kIACR,MAAQ,oEACR,MAAQ,kIACR,MAAQ,oEACR,MAAQ,kIACR,MAAQ,wDACR,MAAQ,mGACR,MAAQ,oEACR,MAAQ,kIACR,MAAQ,oEACR,MAAQ,kIACR,MAAQ,oEACR,MAAQ,kIACR,IAAQ,oEACR,MAAQ,kIACR,MAAQ,oEACR,MAAQ,kIACR,MAAQ,oEACR,MAAQ,kIACR,MAAQ,oEACR,MAAQ,kIACR,MAAQ,oEACR,MAAQ,kIACR,MAAQ,oEACR,MAAQ,kIACR,MAAQ,oEACR,MAAQ,kIACR,MAAQ,oEACR,MAAQ,kIACR,MAAQ,oEACR,MAAQ,kIACR,MAAQ,oEACR,MAAQ,kIACR,MAAQ,oEACR,MAAQ,kIACR,MAAQ,oEACR,MAAQ,kIACR,MAAQ,oEACR,MAAQ,kIACR,MAAQ,oEACR,MAAQ,kIACR,MAAQ,oEACR,MAAQ,yCAGX1B,GAAOD,QAAU,SAAiB4B,GAChC,GAAKA,GAAyB,KAAbA,EAEjB,IAAK,GAAIZ,GAAI,EAAGA,EAAIW,EAAUF,OAAQT,IAAK,CACzC,GAAIa,GAAQF,EAAUX,GAAG,GAErBc,EAAWD,EAAME,QAAQH,EAC7B,IAAIE,GAAY,EACd,MAAOH,GAAUX,GAAG,GAAKc,cAKpB,IAAI"}