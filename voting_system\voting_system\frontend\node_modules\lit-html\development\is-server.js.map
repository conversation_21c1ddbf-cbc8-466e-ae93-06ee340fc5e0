{"version": 3, "file": "is-server.js", "sourceRoot": "", "sources": ["../src/is-server.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH;;;;;GAKG;AAEH,MAAM,SAAS,GAAG,KAAK,CAAC;AAExB;;;;;;;GAOG;AACH,MAAM,CAAC,MAAM,QAAQ,GAAG,SAAS,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/**\n * @fileoverview\n *\n * This file exports a boolean const whose value will depend on what environment\n * the module is being imported from.\n */\n\nconst NODE_MODE = false;\n\n/**\n * A boolean that will be `true` in server environments like Node, and `false`\n * in browser environments. Note that your server environment or toolchain must\n * support the `\"node\"` export condition for this to be `true`.\n *\n * This can be used when authoring components to change behavior based on\n * whether or not the component is executing in an SSR context.\n */\nexport const isServer = NODE_MODE;\n"]}