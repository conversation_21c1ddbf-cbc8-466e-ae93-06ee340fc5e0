import React from 'react';

const ElectionDetail = ({ election, onBack, onViewCandidates, onViewResults, showResults = false }) => {
    if (!election) {
        return (
            <div className="alert alert-warning">
                No election selected. <button className="btn btn-link" onClick={onBack}>Go back</button>
            </div>
        );
    }

    const isActive = election.active;
    const electionDate = new Date(election.end_date);
    const today = new Date();
    const daysLeft = Math.ceil((electionDate - today) / (1000 * 60 * 60 * 24));

    return (
        <div>
            <button className="btn btn-outline-secondary mb-4" onClick={onBack}>
                &larr; Back to Elections
            </button>

            <div className="row">
                <div className="col-md-8">
                    <h1>{election.title}</h1>
                    <p className="lead">{election.description}</p>

                    <div className="card mb-4">
                        <div className="card-header">
                            Election Details
                        </div>
                        <div className="card-body">
                            <p><strong>Type:</strong> {election.election_type.name}</p>
                            <p><strong>Start Date:</strong> {new Date(election.start_date).toLocaleDateString()}</p>
                            <p><strong>End Date:</strong> {new Date(election.end_date).toLocaleDateString()}</p>
                            <p>
                                <strong>Status:</strong>{' '}
                                <span className={`badge ${isActive ? 'bg-success' : 'bg-secondary'}`}>
                                    {isActive ? 'Active' : 'Closed'}
                                </span>
                            </p>
                            
                            {isActive && (
                                <p><strong>Time Remaining:</strong> {daysLeft} days</p>
                            )}
                            
                            {election.contract_election_id && (
                                <p><strong>Blockchain ID:</strong> {election.contract_election_id}</p>
                            )}
                        </div>
                    </div>

                    {showResults ? (
                        <div className="card mb-4">
                            <div className="card-header">
                                Election Results
                            </div>
                            <div className="card-body">
                                {election.candidates && election.candidates.length > 0 ? (
                                    <div>
                                        <h5>Final Vote Counts</h5>
                                        <table className="table">
                                            <thead>
                                                <tr>
                                                    <th>Candidate</th>
                                                    <th>Party</th>
                                                    <th>Votes</th>
                                                    <th>Percentage</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {election.candidates.sort((a, b) => b.votes_count - a.votes_count).map(candidate => {
                                                    const totalVotes = election.candidates.reduce((sum, c) => sum + c.votes_count, 0);
                                                    const percentage = totalVotes > 0 
                                                        ? ((candidate.votes_count / totalVotes) * 100).toFixed(2) 
                                                        : '0.00';
                                                    
                                                    return (
                                                        <tr key={candidate.id}>
                                                            <td>{candidate.user.first_name} {candidate.user.last_name}</td>
                                                            <td>{candidate.is_independent ? 'Independent' : candidate.party.name}</td>
                                                            <td>{candidate.votes_count}</td>
                                                            <td>{percentage}%</td>
                                                        </tr>
                                                    );
                                                })}
                                            </tbody>
                                        </table>
                                    </div>
                                ) : (
                                    <p>No results available yet.</p>
                                )}
                            </div>
                        </div>
                    ) : (
                        <div className="d-grid gap-2">
                            {isActive && (
                                <button 
                                    className="btn btn-primary" 
                                    onClick={() => onViewCandidates(election.id)}
                                >
                                    View Candidates and Vote
                                </button>
                            )}
                            <button 
                                className="btn btn-outline-secondary" 
                                onClick={() => onViewResults(election.id)}
                            >
                                View Results
                            </button>
                        </div>
                    )}
                </div>
                
                <div className="col-md-4">
                    <div className="card">
                        <div className="card-header">
                            Blockchain Information
                        </div>
                        <div className="card-body">
                            <p>This election is stored on the blockchain, ensuring transparency and immutability of the voting process.</p>
                            <p>Each vote is recorded on the blockchain, making it impossible to tamper with the results.</p>
                            <p>The results are calculated automatically and cannot be manipulated.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ElectionDetail;
