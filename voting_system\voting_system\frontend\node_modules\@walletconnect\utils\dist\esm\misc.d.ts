import * as jsonRpcUtils from "@walletconnect/jsonrpc-utils";
import { IRpcConfig } from "@walletconnect/types";
export declare function sanitizeHex(hex: string): string;
export declare function addHexPrefix(hex: string): string;
export declare function removeHexPrefix(hex: string): string;
export declare function removeHexLeadingZeros(hex: string): string;
export declare const payloadId: typeof jsonRpcUtils.payloadId;
export declare function uuid(): string;
export declare function logDeprecationWarning(): void;
export declare function getInfuraRpcUrl(chainId: number, infuraId?: string): string | undefined;
export declare function getRpcUrl(chainId: number, rpc: IRpcConfig): string | undefined;
//# sourceMappingURL=misc.d.ts.map