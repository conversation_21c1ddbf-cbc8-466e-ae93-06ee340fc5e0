from django.db import models
from django.contrib.auth.models import User, Group
from django.utils import timezone
from django.core.validators import RegexValidator
import os

class County(models.Model):
    """Model representing a Kenyan county"""
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=3, unique=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name_plural = "Counties"

class Constituency(models.Model):
    """Model representing a constituency within a county"""
    name = models.CharField(max_length=100)
    county = models.ForeignKey(County, on_delete=models.CASCADE, related_name='constituencies')
    code = models.Char<PERSON>ield(max_length=5, unique=True)

    def __str__(self):
        return f"{self.name} ({self.county.name})"

    class Meta:
        verbose_name_plural = "Constituencies"

class PollingStation(models.Model):
    """Model representing a polling station within a constituency"""
    name = models.Char<PERSON>ield(max_length=100)
    code = models.Char<PERSON><PERSON>(max_length=10, unique=True)
    constituency = models.ForeignKey(Constituency, on_delete=models.CASCADE, related_name='polling_stations')
    location = models.CharField(max_length=255)

    def __str__(self):
        return f"{self.name} ({self.constituency.name})"

class Party(models.Model):
    """Model representing a political party"""
    name = models.CharField(max_length=100)
    abbreviation = models.CharField(max_length=10)
    symbol = models.ImageField(upload_to='party_symbols/', null=True, blank=True)
    description = models.TextField()
    registration_number = models.CharField(max_length=50, unique=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name_plural = "Parties"

class ElectionType(models.Model):
    """Model representing different types of elections"""
    ELECTION_TYPES = [
        ('PRES', 'Presidential'),
        ('PARL', 'Parliamentary (National Assembly)'),
        ('SEN', 'Senate'),
        ('GOV', 'County Governor'),
        ('WOM', 'Women Representative'),
        ('MCA', 'Member of County Assembly'),
    ]

    name = models.CharField(max_length=4, choices=ELECTION_TYPES, unique=True)
    description = models.TextField()

    def __str__(self):
        return self.get_name_display()

class Election(models.Model):
    """Model representing an election event"""
    title = models.CharField(max_length=200)
    description = models.TextField()
    election_type = models.ForeignKey(ElectionType, on_delete=models.CASCADE)
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    active = models.BooleanField(default=False)
    contract_election_id = models.IntegerField(null=True, blank=True)

    def __str__(self):
        return f"{self.title} ({self.election_type})"

    @property
    def is_active(self):
        now = timezone.now()
        return self.active and now >= self.start_date and now <= self.end_date


def voter_id_picture_path(instance, filename):
    """Generate upload path for voter ID pictures"""
    ext = filename.split('.')[-1]
    filename = f"voter_{instance.id_number}_id.{ext}"
    return os.path.join('voter_documents', 'id_pictures', filename)


def voter_photo_path(instance, filename):
    """Generate upload path for voter photos"""
    ext = filename.split('.')[-1]
    filename = f"voter_{instance.id_number}_photo.{ext}"
    return os.path.join('voter_documents', 'photos', filename)


class Worker(models.Model):
    """Model representing a registration worker"""
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    employee_id = models.CharField(max_length=20, unique=True)
    phone_number = models.CharField(
        max_length=12,
        validators=[RegexValidator(r'^\+254\d{9}$', 'Phone number must be in format +254XXXXXXXXX')]
    )
    station_assigned = models.ForeignKey(PollingStation, on_delete=models.CASCADE, null=True, blank=True)
    is_active = models.BooleanField(default=True)
    date_created = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_workers')

    def __str__(self):
        return f"Worker: {self.user.get_full_name()} ({self.employee_id})"

    class Meta:
        verbose_name = "Registration Worker"
        verbose_name_plural = "Registration Workers"

class Voter(models.Model):
    """Model representing a registered voter"""
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    id_number = models.CharField(
        max_length=8,
        unique=True,
        validators=[RegexValidator(r'^\d{8}$', 'ID number must be 8 digits')]
    )
    date_of_birth = models.DateField()
    phone_number = models.CharField(
        max_length=12,
        validators=[RegexValidator(r'^\+254\d{9}$', 'Phone number must be in format +254XXXXXXXXX')]
    )
    constituency = models.ForeignKey(Constituency, on_delete=models.CASCADE)
    polling_station = models.ForeignKey(PollingStation, on_delete=models.CASCADE)

    # Image fields
    id_picture = models.ImageField(
        upload_to=voter_id_picture_path,
        null=True,
        blank=True,
        help_text="Upload a clear photo of the voter's national ID"
    )
    voter_photo = models.ImageField(
        upload_to=voter_photo_path,
        null=True,
        blank=True,
        help_text="Upload a clear photo of the voter"
    )

    # Worker who registered this voter
    registered_by = models.ForeignKey(
        Worker,
        on_delete=models.SET_NULL,
        null=True,
        related_name='registered_voters',
        help_text="Worker who registered this voter"
    )

    is_verified = models.BooleanField(default=False)
    registration_date = models.DateTimeField(auto_now_add=True)
    verification_date = models.DateTimeField(null=True, blank=True)
    verified_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='verified_voters',
        help_text="Admin who verified this voter"
    )

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.id_number}"

class Candidate(models.Model):
    """Model representing an election candidate"""
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    party = models.ForeignKey(Party, on_delete=models.CASCADE, null=True, blank=True)
    election = models.ForeignKey(Election, on_delete=models.CASCADE)
    constituency = models.ForeignKey(Constituency, on_delete=models.CASCADE, null=True, blank=True)
    county = models.ForeignKey(County, on_delete=models.CASCADE, null=True, blank=True)
    bio = models.TextField()
    photo = models.ImageField(upload_to='candidate_photos/', null=True, blank=True)
    is_independent = models.BooleanField(default=False)
    is_approved = models.BooleanField(default=False)
    votes_count = models.IntegerField(default=0)
    contract_candidate_id = models.IntegerField(null=True, blank=True)

    def __str__(self):
        party_info = "Independent" if self.is_independent else self.party.name
        return f"{self.user.get_full_name()} - {party_info} ({self.election.election_type})"

class ValidNationalID(models.Model):
    """Model representing valid national ID numbers that can be used for voter registration"""
    id_number = models.CharField(
        max_length=8,
        unique=True,
        validators=[RegexValidator(r'^\d{8}$', 'ID number must be 8 digits')]
    )
    full_name = models.CharField(max_length=255, blank=True)
    date_of_birth = models.DateField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    date_added = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.id_number} - {self.full_name if self.full_name else 'Unknown'}"

    class Meta:
        verbose_name = "Valid National ID"
        verbose_name_plural = "Valid National IDs"


class Vote(models.Model):
    """Model representing a vote cast by a voter"""
    voter = models.ForeignKey(Voter, on_delete=models.CASCADE)
    candidate = models.ForeignKey(Candidate, on_delete=models.CASCADE)
    election = models.ForeignKey(Election, on_delete=models.CASCADE)
    polling_station = models.ForeignKey(PollingStation, on_delete=models.CASCADE)
    timestamp = models.DateTimeField(auto_now_add=True)
    transaction_hash = models.CharField(max_length=255, null=True, blank=True)

    class Meta:
        unique_together = ('voter', 'election')

    def __str__(self):
        return f"{self.voter} voted for {self.candidate} in {self.election}"
