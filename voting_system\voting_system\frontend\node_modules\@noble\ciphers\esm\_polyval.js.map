{"version": 3, "file": "_polyval.js", "sourceRoot": "", "sources": ["../src/_polyval.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;GAaG;AACH,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,cAAc,CAAC;AACxD,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAe,OAAO,EAAE,GAAG,EAAE,MAAM,YAAY,CAAC;AAErF,MAAM,UAAU,GAAG,EAAE,CAAC;AACtB,gBAAgB;AAChB,2BAA2B;AAC3B,MAAM,OAAO,GAAG,eAAe,CAAC,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC;AACnD,MAAM,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC,CAAC;AAC7B,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,iBAAiB;AAEpC,iBAAiB;AACjB,4DAA4D;AAC5D,4GAA4G;AAC5G,MAAM,IAAI,GAAG,CAAC,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,EAAE;IAC9D,MAAM,KAAK,GAAG,EAAE,GAAG,CAAC,CAAC;IACrB,OAAO;QACL,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;QAC3B,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;QAC3B,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;QAC3B,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,gBAAgB;KACjE,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,MAAM,GAAG,CAAC,CAAS,EAAE,EAAE,CAC3B,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;IAC1B,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;IAC1B,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC;IACnB,CAAC,CAAC;AAEJ;;;GAGG;AACH,MAAM,UAAU,WAAW,CAAC,CAAa;IACvC,CAAC,CAAC,OAAO,EAAE,CAAC;IACZ,MAAM,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;IACxB,UAAU;IACV,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAClC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC;QACzB,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;IACvB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,sDAAsD;IAC7E,OAAO,CAAC,CAAC;AACX,CAAC;AAID,MAAM,cAAc,GAAG,CAAC,KAAa,EAAE,EAAE;IACvC,IAAI,KAAK,GAAG,EAAE,GAAG,IAAI;QAAE,OAAO,CAAC,CAAC;IAChC,IAAI,KAAK,GAAG,IAAI;QAAE,OAAO,CAAC,CAAC;IAC3B,OAAO,CAAC,CAAC;AACX,CAAC,CAAC;AAEF,MAAM,KAAK;IAWT,+DAA+D;IAC/D,YAAY,GAAU,EAAE,cAAuB;QAXtC,aAAQ,GAAG,UAAU,CAAC;QACtB,cAAS,GAAG,UAAU,CAAC;QACtB,OAAE,GAAG,CAAC,CAAC;QACP,OAAE,GAAG,CAAC,CAAC;QACP,OAAE,GAAG,CAAC,CAAC;QACP,OAAE,GAAG,CAAC,CAAC;QACP,aAAQ,GAAG,KAAK,CAAC;QAMzB,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;QACnB,MAAM,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;QAChB,MAAM,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;QAC9B,IAAI,EAAE,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QACnC,IAAI,EAAE,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QACnC,IAAI,EAAE,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QACnC,IAAI,EAAE,GAAG,KAAK,CAAC,SAAS,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;QACpC,6DAA6D;QAC7D,MAAM,OAAO,GAAY,EAAE,CAAC;QAC5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7B,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACjF,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;QAC9D,CAAC;QACD,MAAM,CAAC,GAAG,cAAc,CAAC,cAAc,IAAI,IAAI,CAAC,CAAC;QACjD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;QACpE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QACX,MAAM,IAAI,GAAG,GAAG,CAAC,CAAC,mBAAmB;QACrC,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,CAAC;QACzB,MAAM,UAAU,GAAG,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;QAC9C,MAAM,KAAK,GAAY,EAAE,CAAC;QAC1B,+CAA+C;QAC/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC;YACjC,8BAA8B;YAC9B,KAAK,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,UAAU,EAAE,IAAI,EAAE,EAAE,CAAC;gBAC7C,kBAAkB;gBAClB,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;gBACnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;oBAC3B,MAAM,GAAG,GAAG,CAAC,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;oBACvC,IAAI,CAAC,GAAG;wBAAE,SAAS;oBACnB,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;oBAC9D,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;gBACjD,CAAC;gBACD,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;YACjC,CAAC;QACH,CAAC;QACD,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC;IACjB,CAAC;IACS,YAAY,CAAC,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU;QACnE,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC;QACnE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC;QAClC,kBAAkB;QAClB,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;QACnC,MAAM,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,8BAA8B;QACzD,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,KAAK,MAAM,GAAG,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC;YACnC,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG,CAAC,EAAE,OAAO,EAAE,EAAE,CAAC;gBAC7C,MAAM,IAAI,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC;gBAC5C,KAAK,IAAI,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,MAAM,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,CAAC;oBACnD,MAAM,GAAG,GAAG,CAAC,IAAI,KAAK,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC;oBAC3C,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,UAAU,GAAG,GAAG,CAAC,CAAC;oBACnE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;oBAC/C,CAAC,IAAI,CAAC,CAAC;gBACT,CAAC;YACH,CAAC;QACH,CAAC;QACD,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;IACf,CAAC;IACD,MAAM,CAAC,IAAW;QAChB,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;QACrB,OAAO,CAAC,IAAI,CAAC,CAAC;QACd,MAAM,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;QACtB,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC,CAAC;QACpD,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC;QACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAChC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACpF,CAAC;QACD,IAAI,IAAI,EAAE,CAAC;YACT,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,UAAU,CAAC,CAAC,CAAC;YAChD,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;YAClE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,mBAAmB;QACrC,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO;QACL,MAAM,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;QACnB,yBAAyB;QACzB,KAAK,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC;YACpB,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IACD,UAAU,CAAC,GAAe;QACxB,OAAO,CAAC,IAAI,CAAC,CAAC;QACd,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QACnB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC;QAChC,MAAM,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;QACrB,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;QACZ,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;QACZ,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;QACZ,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;QACZ,OAAO,GAAG,CAAC;IACb,CAAC;IACD,MAAM;QACJ,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC;QACvC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QACrB,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,OAAO,GAAG,CAAC;IACb,CAAC;CACF;AAED,MAAM,OAAQ,SAAQ,KAAK;IACzB,YAAY,GAAU,EAAE,cAAuB;QAC7C,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;QACnB,MAAM,KAAK,GAAG,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;QAC1C,KAAK,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;QAC7B,KAAK,CAAC,KAAK,CAAC,CAAC;IACf,CAAC;IACD,MAAM,CAAC,IAAW;QAChB,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;QACrB,OAAO,CAAC,IAAI,CAAC,CAAC;QACd,MAAM,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;QACtB,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC;QACtC,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC,CAAC;QACpD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAChC,IAAI,CAAC,YAAY,CACf,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EACtB,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EACtB,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EACtB,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CACvB,CAAC;QACJ,CAAC;QACD,IAAI,IAAI,EAAE,CAAC;YACT,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,UAAU,CAAC,CAAC,CAAC;YAChD,IAAI,CAAC,YAAY,CACf,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAClB,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAClB,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAClB,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CACnB,CAAC;YACF,KAAK,CAAC,OAAO,CAAC,CAAC;QACjB,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IACD,UAAU,CAAC,GAAe;QACxB,OAAO,CAAC,IAAI,CAAC,CAAC;QACd,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QACnB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,gBAAgB;QAChB,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC;QAChC,MAAM,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;QACrB,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;QACZ,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;QACZ,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;QACZ,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;QACZ,OAAO,GAAG,CAAC,OAAO,EAAE,CAAC;IACvB,CAAC;CACF;AAGD,SAAS,sBAAsB,CAC7B,QAA0D;IAO1D,MAAM,KAAK,GAAG,CAAC,GAAU,EAAE,GAAU,EAAc,EAAE,CACnD,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;IAC1D,MAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC5C,KAAK,CAAC,SAAS,GAAG,GAAG,CAAC,SAAS,CAAC;IAChC,KAAK,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;IAC9B,KAAK,CAAC,MAAM,GAAG,CAAC,GAAU,EAAE,cAAuB,EAAE,EAAE,CAAC,QAAQ,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;IACtF,OAAO,KAAK,CAAC;AACf,CAAC;AAED,6BAA6B;AAC7B,MAAM,CAAC,MAAM,KAAK,GAAY,sBAAsB,CAClD,CAAC,GAAG,EAAE,cAAc,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,GAAG,EAAE,cAAc,CAAC,CACxD,CAAC;AAEF,+BAA+B;AAC/B,MAAM,CAAC,MAAM,OAAO,GAAY,sBAAsB,CACpD,CAAC,GAAG,EAAE,cAAc,EAAE,EAAE,CAAC,IAAI,OAAO,CAAC,GAAG,EAAE,cAAc,CAAC,CAC1D,CAAC"}