{"language": "Solidity", "settings": {"outputSelection": {"*": {"*": ["abi", "evm.bytecode.object", "evm.deployedBytecode", "metadata", "<PERSON>v<PERSON><PERSON>"]}}}, "sources": {"PackageRegistryInterface.sol": {"urls": ["/Users/<USER>/ethereum/ethpm-cli/projects/simple-registry/contracts/PackageRegistryInterface.sol"]}, "PackageRegistry.sol": {"urls": ["/Users/<USER>/ethereum/ethpm-cli/projects/simple-registry/contracts/PackageRegistry.sol"]}, "Ownable.sol": {"urls": ["/Users/<USER>/ethereum/ethpm-cli/projects/simple-registry/contracts/Ownable.sol"]}}}