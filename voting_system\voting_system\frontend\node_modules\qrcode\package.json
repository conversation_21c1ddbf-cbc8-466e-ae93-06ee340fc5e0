{"name": "qrcode", "description": "QRCode / 2d Barcode api with both server side and client side support using canvas", "version": "1.4.4", "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>"], "keywords": ["qr", "code", "canvas", "qrcode"], "main": "./lib/index.js", "browser": {"./lib/index.js": "./lib/browser.js", "./lib/utils/buffer.js": "./lib/utils/typedarray-buffer.js", "fs": false}, "files": ["bin", "build", "lib", "helper"], "homepage": "http://github.com/soldair/node-qrcode", "license": "MIT", "scripts": {"lint": "standard", "pretest": "npm run lint", "test": "node --throw-deprecation test.js", "build": "node build.js", "prepublish": "npm run build", "browser": "node examples/clientsideserver.js"}, "bin": {"qrcode": "./bin/qrcode"}, "dependencies": {"buffer": "^5.4.3", "buffer-alloc": "^1.2.0", "buffer-from": "^1.1.1", "dijkstrajs": "^1.0.1", "isarray": "^2.0.1", "pngjs": "^3.3.0", "yargs": "^13.2.4"}, "devDependencies": {"browserify": "^16.2.3", "canvas": "^1.6.11", "canvasutil": "*", "colors": "*", "express": "^4.16.4", "htmlparser2": "^3.9.2", "os-tmpdir": "^1.0.2", "sinon": "^1.17.7", "standard": "^9.0.2", "tap": "^12.1.1", "uglify-js": "^2.7.5"}, "repository": {"type": "git", "url": "git://github.com/soldair/node-qrcode.git"}, "engines": {"node": ">=4"}, "standard": {"ignore": ["build/", "examples/vendors/"]}}