{"name": "levelup", "description": "Fast & simple storage - a Node.js-style LevelDB wrapper", "version": "1.3.9", "contributors": ["<PERSON>g <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/chesles/)", "<PERSON> <<EMAIL>> (https://github.com/raynos)", "<PERSON> <<EMAIL>> (https://github.com/dominictarr)", "<PERSON> <<EMAIL>> (https://github.com/maxogden)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/ralphtheninja)", "<PERSON> <<EMAIL>> (https://github.com/kesla)", "<PERSON> <<EMAIL>> (https://github.com/juliangruber)", "<PERSON> <<EMAIL>> (https://github.com/0x00a)", "<PERSON> <<EMAIL>> (https://github.com/No9)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON> <<EMAIL>> (https://github.com/pgte)", "<PERSON> <<EMAIL>> (https://github.com/substack)", "<PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "repository": {"type": "git", "url": "https://github.com/level/levelup.git"}, "homepage": "https://github.com/level/levelup", "keywords": ["leveldb", "stream", "database", "db", "store", "storage", "json"], "main": "lib/levelup.js", "dependencies": {"deferred-leveldown": "~1.2.1", "level-codec": "~7.0.0", "level-errors": "~1.0.3", "level-iterator-stream": "~1.3.0", "prr": "~1.0.1", "semver": "~5.4.1", "xtend": "~4.0.0"}, "devDependencies": {"after": "^0.8.2", "async": "^2.5.0", "bl": "^1.2.1", "browserify": "^14.3.0", "bustermove": "~1.0.0", "delayed": "~1.0.1", "faucet": "~0.0.1", "leveldown": "^1.1.0", "memdown": "^1.2.4", "msgpack-js": "~0.3.0", "referee": "~1.2.0", "rimraf": "^2.6.1", "safe-buffer": "^5.1.0", "slow-stream": "0.0.4", "standard": "^10.0.2", "tape": "^4.7.0"}, "browser": {"./lib/leveldown.js": false, "leveldown": false, "leveldown/package": false, "semver": false}, "scripts": {"test": "standard && tape test/*-test.js | faucet"}, "license": "MIT"}