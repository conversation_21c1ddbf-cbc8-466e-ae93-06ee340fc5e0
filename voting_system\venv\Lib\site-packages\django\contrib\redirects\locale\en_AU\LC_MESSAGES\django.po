# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2021
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-04-11 13:16+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: English (Australia) (http://www.transifex.com/django/django/"
"language/en_AU/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: en_AU\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Redirects"
msgstr "Redirects"

msgid "site"
msgstr "site"

msgid "redirect from"
msgstr "redirect from"

msgid ""
"This should be an absolute path, excluding the domain name. Example: “/"
"events/search/”."
msgstr ""
"This should be an absolute path, excluding the domain name. Example: “/"
"events/search/”."

msgid "redirect to"
msgstr "redirect to"

msgid ""
"This can be either an absolute path (as above) or a full URL starting with a "
"scheme such as “https://”."
msgstr ""
"This can be either an absolute path (as above) or a full URL starting with a "
"scheme such as “https://”."

msgid "redirect"
msgstr "redirect"

msgid "redirects"
msgstr "redirects"
