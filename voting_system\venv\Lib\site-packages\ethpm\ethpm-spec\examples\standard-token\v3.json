{"contractTypes": {"StandardToken": {"abi": [{"inputs": [{"internalType": "uint256", "name": "_totalSupply", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [{"internalType": "address", "name": "_owner", "type": "address"}, {"internalType": "address", "name": "_spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "remaining", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_spender", "type": "address"}, {"internalType": "uint256", "name": "_value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "success", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_owner", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "balance", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_to", "type": "address"}, {"internalType": "uint256", "name": "_value", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "success", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_from", "type": "address"}, {"internalType": "address", "name": "_to", "type": "address"}, {"internalType": "uint256", "name": "_value", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "success", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}], "devdoc": {"author": "<PERSON> - <<EMAIL>>", "methods": {"allowance(address,address)": {"details": "Returns number of allowed tokens for given address.", "params": {"_owner": "Address of token owner.", "_spender": "Address of token spender."}}, "approve(address,uint256)": {"details": "Sets approved amount of tokens for spender. Returns success.", "params": {"_spender": "Address of allowed account.", "_value": "Number of approved tokens."}}, "balanceOf(address)": {"details": "Returns number of tokens owned by given address.", "params": {"_owner": "Address of token owner."}}, "transfer(address,uint256)": {"details": "Transfers sender's tokens to a given address. Returns success.", "params": {"_to": "Address of token receiver.", "_value": "Number of tokens to transfer."}}, "transferFrom(address,address,uint256)": {"details": "Allows allowed third party to transfer tokens from one address to another. Returns success.", "params": {"_from": "Address from where tokens are withdrawn.", "_to": "Address to where tokens are sent.", "_value": "Number of tokens to transfer."}}}, "title": "Standard token contract"}, "sourceId": "StandardToken.sol"}, "Token": {"abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "remaining", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "success", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "balance", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "supply", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "success", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "success", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}], "devdoc": {"author": "<PERSON> - <<EMAIL>>", "methods": {}, "title": "Abstract token contract - Functions to be implemented by token contracts."}, "sourceId": "AbstractToken.sol", "userdoc": {"methods": {}, "notice": "Implements ERC 20 Token standard: https://github.com/ethereum/EIPs/issues/20"}}}, "manifest": "ethpm/3", "name": "standard-token", "sources": {"./AbstractToken.sol": {"installPath": "./AbstractToken.sol", "type": "solidity", "urls": ["ipfs://QmSBYuGKSH2veDepMbFQu3XVStYRCvuqFjUV7YCPufeHJz"]}, "./StandardToken.sol": {"installPath": "./StandardToken.sol", "type": "solidity", "urls": ["ipfs://QmUofKBtNJVaqoSAtnHfrarJyyLm1oMUTAK4yCtnmYMJVy"]}}, "version": "1.0.0"}