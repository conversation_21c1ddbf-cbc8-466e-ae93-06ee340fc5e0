{"version": 3, "file": "class-map.js", "sources": ["../../../src/directives/class-map.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {AttributePart, noChange} from '../lit-html.js';\nimport {\n  directive,\n  Directive,\n  DirectiveParameters,\n  PartInfo,\n  PartType,\n} from '../directive.js';\n\n/**\n * A key-value set of class names to truthy values.\n */\nexport interface ClassInfo {\n  readonly [name: string]: string | boolean | number;\n}\n\nclass ClassMapDirective extends Directive {\n  /**\n   * Stores the ClassInfo object applied to a given AttributePart.\n   * Used to unset existing values when a new ClassInfo object is applied.\n   */\n  private _previousClasses?: Set<string>;\n  private _staticClasses?: Set<string>;\n\n  constructor(partInfo: PartInfo) {\n    super(partInfo);\n    if (\n      partInfo.type !== PartType.ATTRIBUTE ||\n      partInfo.name !== 'class' ||\n      (partInfo.strings?.length as number) > 2\n    ) {\n      throw new Error(\n        '`classMap()` can only be used in the `class` attribute ' +\n          'and must be the only part in the attribute.'\n      );\n    }\n  }\n\n  render(classInfo: ClassInfo) {\n    // Add spaces to ensure separation from static classes\n    return (\n      ' ' +\n      Object.keys(classInfo)\n        .filter((key) => classInfo[key])\n        .join(' ') +\n      ' '\n    );\n  }\n\n  override update(part: AttributePart, [classInfo]: DirectiveParameters<this>) {\n    // Remember dynamic classes on the first render\n    if (this._previousClasses === undefined) {\n      this._previousClasses = new Set();\n      if (part.strings !== undefined) {\n        this._staticClasses = new Set(\n          part.strings\n            .join(' ')\n            .split(/\\s/)\n            .filter((s) => s !== '')\n        );\n      }\n      for (const name in classInfo) {\n        if (classInfo[name] && !this._staticClasses?.has(name)) {\n          this._previousClasses.add(name);\n        }\n      }\n      return this.render(classInfo);\n    }\n\n    const classList = part.element.classList;\n\n    // Remove old classes that no longer apply\n    // We use forEach() instead of for-of so that we don't require down-level\n    // iteration.\n    this._previousClasses.forEach((name) => {\n      if (!(name in classInfo)) {\n        classList.remove(name);\n        this._previousClasses!.delete(name);\n      }\n    });\n\n    // Add or remove classes based on their classMap value\n    for (const name in classInfo) {\n      // We explicitly want a loose truthy check of `value` because it seems\n      // more convenient that '' and 0 are skipped.\n      const value = !!classInfo[name];\n      if (\n        value !== this._previousClasses.has(name) &&\n        !this._staticClasses?.has(name)\n      ) {\n        if (value) {\n          classList.add(name);\n          this._previousClasses.add(name);\n        } else {\n          classList.remove(name);\n          this._previousClasses.delete(name);\n        }\n      }\n    }\n    return noChange;\n  }\n}\n\n/**\n * A directive that applies dynamic CSS classes.\n *\n * This must be used in the `class` attribute and must be the only part used in\n * the attribute. It takes each property in the `classInfo` argument and adds\n * the property name to the element's `classList` if the property value is\n * truthy; if the property value is falsey, the property name is removed from\n * the element's `class`.\n *\n * For example `{foo: bar}` applies the class `foo` if the value of `bar` is\n * truthy.\n *\n * @param classInfo\n */\nexport const classMap = directive(ClassMapDirective);\n\n/**\n * The type of the class that powers this directive. Necessary for naming the\n * directive's return type.\n */\nexport type {ClassMapDirective};\n"], "names": [], "mappings": ";;;AAAA;;;;AAIG;AAkBH,MAAM,iBAAkB,SAAQ,SAAS,CAAA;AAQvC,IAAA,WAAA,CAAY,QAAkB,EAAA;;QAC5B,KAAK,CAAC,QAAQ,CAAC,CAAC;AAChB,QAAA,IACE,QAAQ,CAAC,IAAI,KAAK,QAAQ,CAAC,SAAS;YACpC,QAAQ,CAAC,IAAI,KAAK,OAAO;YACzB,CAAC,CAAA,EAAA,GAAA,QAAQ,CAAC,OAAO,0CAAE,MAAiB,IAAG,CAAC,EACxC;YACA,MAAM,IAAI,KAAK,CACb,yDAAyD;AACvD,gBAAA,6CAA6C,CAChD,CAAC;AACH,SAAA;KACF;AAED,IAAA,MAAM,CAAC,SAAoB,EAAA;;AAEzB,QAAA,QACE,GAAG;AACH,YAAA,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;iBACnB,MAAM,CAAC,CAAC,GAAG,KAAK,SAAS,CAAC,GAAG,CAAC,CAAC;iBAC/B,IAAI,CAAC,GAAG,CAAC;AACZ,YAAA,GAAG,EACH;KACH;AAEQ,IAAA,MAAM,CAAC,IAAmB,EAAE,CAAC,SAAS,CAA4B,EAAA;;;AAEzE,QAAA,IAAI,IAAI,CAAC,gBAAgB,KAAK,SAAS,EAAE;AACvC,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,GAAG,EAAE,CAAC;AAClC,YAAA,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS,EAAE;gBAC9B,IAAI,CAAC,cAAc,GAAG,IAAI,GAAG,CAC3B,IAAI,CAAC,OAAO;qBACT,IAAI,CAAC,GAAG,CAAC;qBACT,KAAK,CAAC,IAAI,CAAC;qBACX,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAC3B,CAAC;AACH,aAAA;AACD,YAAA,KAAK,MAAM,IAAI,IAAI,SAAS,EAAE;AAC5B,gBAAA,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,EAAC,CAAA,EAAA,GAAA,IAAI,CAAC,cAAc,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,GAAG,CAAC,IAAI,CAAC,CAAA,EAAE;AACtD,oBAAA,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACjC,iBAAA;AACF,aAAA;AACD,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AAC/B,SAAA;AAED,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;;;;QAKzC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AACrC,YAAA,IAAI,EAAE,IAAI,IAAI,SAAS,CAAC,EAAE;AACxB,gBAAA,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACvB,gBAAA,IAAI,CAAC,gBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACrC,aAAA;AACH,SAAC,CAAC,CAAC;;AAGH,QAAA,KAAK,MAAM,IAAI,IAAI,SAAS,EAAE;;;YAG5B,MAAM,KAAK,GAAG,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAChC,IACE,KAAK,KAAK,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC;gBACzC,EAAC,CAAA,EAAA,GAAA,IAAI,CAAC,cAAc,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,GAAG,CAAC,IAAI,CAAC,CAAA,EAC/B;AACA,gBAAA,IAAI,KAAK,EAAE;AACT,oBAAA,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACpB,oBAAA,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACjC,iBAAA;AAAM,qBAAA;AACL,oBAAA,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACvB,oBAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACpC,iBAAA;AACF,aAAA;AACF,SAAA;AACD,QAAA,OAAO,QAAQ,CAAC;KACjB;AACF,CAAA;AAED;;;;;;;;;;;;;AAaG;MACU,QAAQ,GAAG,SAAS,CAAC,iBAAiB;;;;"}