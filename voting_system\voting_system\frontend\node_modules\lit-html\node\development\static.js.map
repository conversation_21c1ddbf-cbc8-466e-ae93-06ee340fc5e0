{"version": 3, "file": "static.js", "sources": ["../../src/static.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n// Any new exports need to be added to the export statement in\n// `packages/lit/src/index.all.ts`.\n\nimport {html as coreHtml, svg as coreSvg, TemplateResult} from './lit-html.js';\n\nexport interface StaticValue {\n  /** The value to interpolate as-is into the template. */\n  _$litStatic$: string;\n\n  /**\n   * A value that can't be decoded from ordinary JSON, make it harder for\n   * a attacker-controlled data that goes through JSON.parse to produce a valid\n   * StaticValue.\n   */\n  r: typeof brand;\n}\n\n/**\n * Prevents JSON injection attacks.\n *\n * The goals of this brand:\n *   1) fast to check\n *   2) code is small on the wire\n *   3) multiple versions of Lit in a single page will all produce mutually\n *      interoperable StaticValues\n *   4) normal JSON.parse (without an unusual reviver) can not produce a\n *      StaticValue\n *\n * Symbols satisfy (1), (2), and (4). We use Symbol.for to satisfy (3), but\n * we don't care about the key, so we break ties via (2) and use the empty\n * string.\n */\nconst brand = Symbol.for('');\n\n/** Safely extracts the string part of a StaticValue. */\nconst unwrapStaticValue = (value: unknown): string | undefined => {\n  if ((value as Partial<StaticValue>)?.r !== brand) {\n    return undefined;\n  }\n  return (value as Partial<StaticValue>)?.['_$litStatic$'];\n};\n\n/**\n * Wraps a string so that it behaves like part of the static template\n * strings instead of a dynamic value.\n *\n * Users must take care to ensure that adding the static string to the template\n * results in well-formed HTML, or else templates may break unexpectedly.\n *\n * Note that this function is unsafe to use on untrusted content, as it will be\n * directly parsed into HTML. Do not pass user input to this function\n * without sanitizing it.\n *\n * Static values can be changed, but they will cause a complete re-render\n * since they effectively create a new template.\n */\nexport const unsafeStatic = (value: string): StaticValue => ({\n  ['_$litStatic$']: value,\n  r: brand,\n});\n\nconst textFromStatic = (value: StaticValue) => {\n  if (value['_$litStatic$'] !== undefined) {\n    return value['_$litStatic$'];\n  } else {\n    throw new Error(\n      `Value passed to 'literal' function must be a 'literal' result: ${value}. Use 'unsafeStatic' to pass non-literal values, but\n            take care to ensure page security.`\n    );\n  }\n};\n\n/**\n * Tags a string literal so that it behaves like part of the static template\n * strings instead of a dynamic value.\n *\n * The only values that may be used in template expressions are other tagged\n * `literal` results or `unsafeStatic` values (note that untrusted content\n * should never be passed to `unsafeStatic`).\n *\n * Users must take care to ensure that adding the static string to the template\n * results in well-formed HTML, or else templates may break unexpectedly.\n *\n * Static values can be changed, but they will cause a complete re-render since\n * they effectively create a new template.\n */\nexport const literal = (\n  strings: TemplateStringsArray,\n  ...values: unknown[]\n): StaticValue => ({\n  ['_$litStatic$']: values.reduce(\n    (acc, v, idx) => acc + textFromStatic(v as StaticValue) + strings[idx + 1],\n    strings[0]\n  ) as string,\n  r: brand,\n});\n\nconst stringsCache = new Map<string, TemplateStringsArray>();\n\n/**\n * Wraps a lit-html template tag (`html` or `svg`) to add static value support.\n */\nexport const withStatic =\n  (coreTag: typeof coreHtml | typeof coreSvg) =>\n  (strings: TemplateStringsArray, ...values: unknown[]): TemplateResult => {\n    const l = values.length;\n    let staticValue: string | undefined;\n    let dynamicValue: unknown;\n    const staticStrings: Array<string> = [];\n    const dynamicValues: Array<unknown> = [];\n    let i = 0;\n    let hasStatics = false;\n    let s: string;\n\n    while (i < l) {\n      s = strings[i];\n      // Collect any unsafeStatic values, and their following template strings\n      // so that we treat a run of template strings and unsafe static values as\n      // a single template string.\n      while (\n        i < l &&\n        ((dynamicValue = values[i]),\n        (staticValue = unwrapStaticValue(dynamicValue))) !== undefined\n      ) {\n        s += staticValue + strings[++i];\n        hasStatics = true;\n      }\n      // If the last value is static, we don't need to push it.\n      if (i !== l) {\n        dynamicValues.push(dynamicValue);\n      }\n      staticStrings.push(s);\n      i++;\n    }\n    // If the last value isn't static (which would have consumed the last\n    // string), then we need to add the last string.\n    if (i === l) {\n      staticStrings.push(strings[l]);\n    }\n\n    if (hasStatics) {\n      const key = staticStrings.join('$$lit$$');\n      strings = stringsCache.get(key)!;\n      if (strings === undefined) {\n        // Beware: in general this pattern is unsafe, and doing so may bypass\n        // lit's security checks and allow an attacker to execute arbitrary\n        // code and inject arbitrary content.\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        (staticStrings as any).raw = staticStrings;\n        stringsCache.set(\n          key,\n          (strings = staticStrings as unknown as TemplateStringsArray)\n        );\n      }\n      values = dynamicValues;\n    }\n    return coreTag(strings, ...values);\n  };\n\n/**\n * Interprets a template literal as an HTML template that can efficiently\n * render to and update a container.\n *\n * Includes static value support from `lit-html/static.js`.\n */\nexport const html = withStatic(coreHtml);\n\n/**\n * Interprets a template literal as an SVG template that can efficiently\n * render to and update a container.\n *\n * Includes static value support from `lit-html/static.js`.\n */\nexport const svg = withStatic(coreSvg);\n"], "names": ["coreHtml", "coreSvg"], "mappings": ";;AAAA;;;;AAIG;AAmBH;;;;;;;;;;;;;;AAcG;AACH,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AAE7B;AACA,MAAM,iBAAiB,GAAG,CAAC,KAAc,KAAwB;IAC/D,IAAI,CAAC,KAA8B,KAAA,IAAA,IAA9B,KAAK,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAL,KAAK,CAA2B,CAAC,MAAK,KAAK,EAAE;AAChD,QAAA,OAAO,SAAS,CAAC;AAClB,KAAA;IACD,OAAQ,KAA8B,aAA9B,KAAK,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAL,KAAK,CAA4B,cAAc,CAAC,CAAC;AAC3D,CAAC,CAAC;AAEF;;;;;;;;;;;;;AAaG;MACU,YAAY,GAAG,CAAC,KAAa,MAAmB;IAC3D,CAAC,cAAc,GAAG,KAAK;AACvB,IAAA,CAAC,EAAE,KAAK;AACT,CAAA,EAAE;AAEH,MAAM,cAAc,GAAG,CAAC,KAAkB,KAAI;AAC5C,IAAA,IAAI,KAAK,CAAC,cAAc,CAAC,KAAK,SAAS,EAAE;AACvC,QAAA,OAAO,KAAK,CAAC,cAAc,CAAC,CAAC;AAC9B,KAAA;AAAM,SAAA;AACL,QAAA,MAAM,IAAI,KAAK,CACb,CAAA,+DAAA,EAAkE,KAAK,CAAA;AAC9B,8CAAA,CAAA,CAC1C,CAAC;AACH,KAAA;AACH,CAAC,CAAC;AAEF;;;;;;;;;;;;;AAaG;AACI,MAAM,OAAO,GAAG,CACrB,OAA6B,EAC7B,GAAG,MAAiB,MACH;AACjB,IAAA,CAAC,cAAc,GAAG,MAAM,CAAC,MAAM,CAC7B,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,KAAK,GAAG,GAAG,cAAc,CAAC,CAAgB,CAAC,GAAG,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC,EAC1E,OAAO,CAAC,CAAC,CAAC,CACD;AACX,IAAA,CAAC,EAAE,KAAK;AACT,CAAA,EAAE;AAEH,MAAM,YAAY,GAAG,IAAI,GAAG,EAAgC,CAAC;AAE7D;;AAEG;AACI,MAAM,UAAU,GACrB,CAAC,OAAyC,KAC1C,CAAC,OAA6B,EAAE,GAAG,MAAiB,KAAoB;AACtE,IAAA,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;AACxB,IAAA,IAAI,WAA+B,CAAC;AACpC,IAAA,IAAI,YAAqB,CAAC;IAC1B,MAAM,aAAa,GAAkB,EAAE,CAAC;IACxC,MAAM,aAAa,GAAmB,EAAE,CAAC;IACzC,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,IAAI,UAAU,GAAG,KAAK,CAAC;AACvB,IAAA,IAAI,CAAS,CAAC;IAEd,OAAO,CAAC,GAAG,CAAC,EAAE;AACZ,QAAA,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;;;;QAIf,OACE,CAAC,GAAG,CAAC;AACL,YAAA,CAAC,CAAC,YAAY,GAAG,MAAM,CAAC,CAAC,CAAC;iBACzB,WAAW,GAAG,iBAAiB,CAAC,YAAY,CAAC,CAAC,MAAM,SAAS,EAC9D;YACA,CAAC,IAAI,WAAW,GAAG,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;YAChC,UAAU,GAAG,IAAI,CAAC;AACnB,SAAA;;QAED,IAAI,CAAC,KAAK,CAAC,EAAE;AACX,YAAA,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AAClC,SAAA;AACD,QAAA,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACtB,QAAA,CAAC,EAAE,CAAC;AACL,KAAA;;;IAGD,IAAI,CAAC,KAAK,CAAC,EAAE;QACX,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;AAChC,KAAA;AAED,IAAA,IAAI,UAAU,EAAE;QACd,MAAM,GAAG,GAAG,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC1C,QAAA,OAAO,GAAG,YAAY,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC;QACjC,IAAI,OAAO,KAAK,SAAS,EAAE;;;;;AAKxB,YAAA,aAAqB,CAAC,GAAG,GAAG,aAAa,CAAC;YAC3C,YAAY,CAAC,GAAG,CACd,GAAG,GACF,OAAO,GAAG,aAAgD,EAC5D,CAAC;AACH,SAAA;QACD,MAAM,GAAG,aAAa,CAAC;AACxB,KAAA;AACD,IAAA,OAAO,OAAO,CAAC,OAAO,EAAE,GAAG,MAAM,CAAC,CAAC;AACrC,EAAE;AAEJ;;;;;AAKG;MACU,IAAI,GAAG,UAAU,CAACA,MAAQ,EAAE;AAEzC;;;;;AAKG;MACU,GAAG,GAAG,UAAU,CAACC,KAAO;;;;"}