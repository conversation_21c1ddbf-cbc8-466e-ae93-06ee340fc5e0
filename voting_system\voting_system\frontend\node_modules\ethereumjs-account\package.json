{"name": "ethereumjs-account", "version": "2.0.5", "description": "Encoding, decoding and validation of Ethereum's Account schema", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/ethereumjs/ethereumjs-account.git"}, "scripts": {"coverage": "nyc npm run test && nyc report --reporter=text-lcov > .nyc_output/lcov.info", "coveralls": "npm run coverage && coveralls <.nyc_output/lcov.info", "lint": "standard", "test": "tape ./test/index.js"}, "keywords": ["ethereum", "account"], "author": "mjbecze (<EMAIL>)", "license": "MPL-2.0", "bugs": {"url": "https://github.com/ethereumjs/ethereumjs-account/issues"}, "homepage": "https://github.com/ethereumjs/ethereumjs-account#readme", "dependencies": {"ethereumjs-util": "^5.0.0", "rlp": "^2.0.0", "safe-buffer": "^5.1.1"}, "devDependencies": {"coveralls": "^3.0.0", "nyc": "^11.7.1", "standard": "^11.0.1", "tape": "^4.0.3"}}