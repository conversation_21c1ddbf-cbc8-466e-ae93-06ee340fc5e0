import React, { useState, useEffect } from 'react';

const CandidateList = ({ candidates, election, onSelect, onBack }) => {
    const [voterStatus, setVoterStatus] = useState(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState('');

    // Check if the voter has already voted in this election
    useEffect(() => {
        const checkVoterStatus = async () => {
            if (!election || !election.id) return;

            try {
                setIsLoading(true);
                const token = localStorage.getItem('authToken');

                // If no token, user is not logged in
                if (!token) {
                    setVoterStatus({ is_registered: false, has_voted: false });
                    setIsLoading(false);
                    return;
                }

                const response = await fetch(`/api/elections/${election.id}/voter-status/`, {
                    headers: {
                        'Authorization': `Token ${token}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    setVoterStatus(data);
                } else {
                    // If 401 or 403, user is not authenticated
                    if (response.status === 401 || response.status === 403) {
                        setVoterStatus({ is_registered: false, has_voted: false });
                    } else {
                        const errorData = await response.json();
                        setError(errorData.error || 'Failed to check voter status');
                    }
                }
            } catch (err) {
                setError('Error checking voter status: ' + err.message);
            } finally {
                setIsLoading(false);
            }
        };

        checkVoterStatus();
    }, [election]);

    if (!election) {
        return (
            <div className="alert alert-warning">
                No election selected. <button className="btn btn-link" onClick={onBack}>Go back</button>
            </div>
        );
    }

    if (isLoading) {
        return (
            <div className="text-center my-5">
                <div className="spinner-border text-primary" role="status">
                    <span className="visually-hidden">Loading...</span>
                </div>
                <p className="mt-2">Checking voter status...</p>
            </div>
        );
    }

    if (error) {
        return (
            <div className="alert alert-danger">
                {error}
                <button className="btn btn-outline-secondary ms-3" onClick={onBack}>
                    Go back
                </button>
            </div>
        );
    }

    // If voter has already voted, show message
    if (voterStatus && voterStatus.has_voted) {
        return (
            <div>
                <button className="btn btn-outline-secondary mb-4" onClick={onBack}>
                    &larr; Back to Election Details
                </button>

                <div className="alert alert-info">
                    <h4 className="alert-heading">You have already voted in this election!</h4>
                    <p>According to our records, you have already cast your vote for this election.</p>
                    <hr />
                    <p className="mb-0">You can view the results once the election is complete.</p>
                </div>
            </div>
        );
    }

    // If voter is not registered, show registration message
    if (voterStatus && !voterStatus.is_registered) {
        return (
            <div>
                <button className="btn btn-outline-secondary mb-4" onClick={onBack}>
                    &larr; Back to Election Details
                </button>

                <div className="alert alert-warning">
                    <h4 className="alert-heading">Voter Registration Required</h4>
                    <p>You need to register as a voter before you can participate in this election.</p>
                    <hr />
                    <p className="mb-0">Please complete the voter registration process to continue.</p>
                </div>
            </div>
        );
    }

    return (
        <div>
            <button className="btn btn-outline-secondary mb-4" onClick={onBack}>
                &larr; Back to Election Details
            </button>

            <h2>Candidates for {election.title}</h2>
            <p className="lead">Select a candidate to vote</p>

            {candidates.length > 0 ? (
                <div className="row">
                    {candidates.map(candidate => (
                        <div className="col-md-4" key={candidate.id}>
                            <div className="card candidate-card mb-4">
                                <div className="card-header">
                                    {candidate.user.first_name} {candidate.user.last_name}
                                </div>
                                {candidate.photo && (
                                    <img
                                        src={candidate.photo}
                                        className="card-img-top candidate-photo"
                                        alt={`${candidate.user.first_name} ${candidate.user.last_name}`}
                                    />
                                )}
                                <div className="card-body">
                                    <h6 className="card-subtitle mb-2 text-muted">
                                        {candidate.is_independent ? 'Independent' : candidate.party.name}
                                    </h6>
                                    <p className="card-text">
                                        {candidate.bio.length > 100
                                            ? `${candidate.bio.substring(0, 100)}...`
                                            : candidate.bio}
                                    </p>
                                    <div className="d-flex justify-content-between align-items-center">
                                        {candidate.constituency && (
                                            <span className="text-muted">
                                                {candidate.constituency.name}
                                            </span>
                                        )}
                                        {candidate.county && (
                                            <span className="text-muted">
                                                {candidate.county.name} County
                                            </span>
                                        )}
                                    </div>
                                </div>
                                <div className="card-footer">
                                    <button
                                        className="btn btn-primary w-100"
                                        onClick={() => onSelect(candidate)}
                                    >
                                        Select to Vote
                                    </button>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            ) : (
                <div className="alert alert-info">
                    No candidates available for this election.
                </div>
            )}
        </div>
    );
};

export default CandidateList;
