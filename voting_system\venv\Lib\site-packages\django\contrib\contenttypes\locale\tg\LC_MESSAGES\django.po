# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <sirius<PERSON><EMAIL>>, 2020
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2020-05-15 00:27+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Tajik (http://www.transifex.com/django/django/language/tg/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: tg\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Content Types"
msgstr "Намуди контент"

msgid "python model class name"
msgstr "номи класси модел"

msgid "content type"
msgstr "намуди контент"

msgid "content types"
msgstr "намуди контентҳо"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "Намуди контенти %(ct_id)s модели алоқаманд надорад"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr ""

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr ""
