{"name": "rlp", "version": "2.2.7", "description": "Recursive Length Prefix Encoding Module", "files": ["dist", "dist.browser", "bin", "src"], "main": "./dist/index.js", "types": "./dist/index.d.ts", "browser": "dist.browser/index.js", "bin": {"rlp": "./bin/rlp"}, "scripts": {"build": "tsc -p ./tsconfig.prod.json && tsc -p tsconfig.browser.json", "prepublishOnly": "npm run test && npm run build", "coverage": "npm run build && nyc --reporter=lcov npm run test:unit", "tsc": "tsc -p ./tsconfig.prod.json --noEmit", "lint": "ethereumjs-config-lint", "lint:fix": "ethereumjs-config-lint-fix", "test": "npm run lint && npm run build && npm run test:unit && npm run test:browser", "test:unit": "mocha --reporter spec --require ts-node/register test/*.spec.ts", "test:browser": "karma start karma.conf.js"}, "husky": {"hooks": {"pre-push": "npm run lint"}}, "repository": {"type": "git", "url": "https://github.com/ethereumjs/rlp.git"}, "keywords": ["rlp", "ethereum"], "author": {"name": "martin be<PERSON>e", "email": "<EMAIL>"}, "contributors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <Holger.<PERSON>@gmail.com>"], "license": "MPL-2.0", "bugs": {"url": "https://github.com/ethereumjs/rlp/issues"}, "dependencies": {"bn.js": "^5.2.0"}, "devDependencies": {"@ethereumjs/eslint-config-defaults": "^2.0.0", "@ethereumjs/config-coverage": "^2.0.0", "@ethereumjs/config-typescript": "^2.0.0", "@types/bn.js": "^5.1.0", "@types/mocha": "^9.0.0", "@types/node": "^12.13.0", "husky": "^4.2.5", "karma": "^6.3.4", "karma-chrome-launcher": "^3.1.0", "karma-firefox-launcher": "^2.1.1", "karma-mocha": "^2.0.1", "karma-typescript": "^5.5.2", "mocha": "7.1.2", "nyc": "^15.1.0", "prettier": "^2.4.1", "ts-node": "^10.2.1", "typescript": "^4.4.3"}}