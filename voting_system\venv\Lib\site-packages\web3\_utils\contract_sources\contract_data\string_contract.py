"""
Generated by `compile_contracts.py` script.
Compiled with Solidity v0.8.19.
"""

# source: web3/_utils/contract_sources/StringContract.sol:StringContract
STRING_CONTRACT_BYTECODE = "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"  # noqa: E501
STRING_CONTRACT_RUNTIME = "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"  # noqa: E501
STRING_CONTRACT_ABI = [
    {
        "inputs": [{"internalType": "string", "name": "_value", "type": "string"}],
        "stateMutability": "nonpayable",
        "type": "constructor",
    },
    {"stateMutability": "nonpayable", "type": "fallback"},
    {
        "inputs": [],
        "name": "getValue",
        "outputs": [{"internalType": "string", "name": "", "type": "string"}],
        "stateMutability": "payable",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "string", "name": "_value", "type": "string"}],
        "name": "setValue",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "value",
        "outputs": [{"internalType": "string", "name": "", "type": "string"}],
        "stateMutability": "view",
        "type": "function",
    },
]
STRING_CONTRACT_DATA = {
    "bytecode": STRING_CONTRACT_BYTECODE,
    "bytecode_runtime": STRING_CONTRACT_RUNTIME,
    "abi": STRING_CONTRACT_ABI,
}
