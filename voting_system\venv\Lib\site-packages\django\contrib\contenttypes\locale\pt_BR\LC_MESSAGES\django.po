# This file is distributed under the same license as the Django package.
#
# Translators:
# Allisson Aze<PERSON>o <<EMAIL>>, 2014
# <PERSON> <<EMAIL>>, 2016,2019
# semente, 2012
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON> <<EMAIL>>, 2021
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2021-01-17 06:36+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Portuguese (Brazil) (http://www.transifex.com/django/django/"
"language/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

msgid "Content Types"
msgstr "Tipos de Conteúdo"

msgid "python model class name"
msgstr "nome da classe de modelo em python"

msgid "content type"
msgstr "tipo de conteúdo"

msgid "content types"
msgstr "tipos de conteúdo"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "Objeto do tipo de conteúdo %(ct_id)s não tem nenhum model associado"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "O objeto %(obj_id)s do tipo de conteúdo %(ct_id)s não existe."

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr "os objetos %(ct_name)s não têm o método get_absolute_url()."
