!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define("windowMetadata",[],e):"object"==typeof exports?exports.windowMetadata=e():t.windowMetadata=e()}(this,(function(){return function(t){var e={};function o(n){if(e[n])return e[n].exports;var r=e[n]={i:n,l:!1,exports:{}};return t[n].call(r.exports,r,r.exports,o),r.l=!0,r.exports}return o.m=t,o.c=e,o.d=function(t,e,n){o.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},o.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},o.t=function(t,e){if(1&e&&(t=o(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(o.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var r in t)o.d(n,r,function(e){return t[e]}.bind(null,r));return n},o.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return o.d(e,"a",e),e},o.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},o.p="",o(o.s=0)}([function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getWindowMetadata=void 0;const n=o(1);e.getWindowMetadata=function(){let t,e;try{t=n.getDocumentOrThrow(),e=n.getLocationOrThrow()}catch(t){return null}function o(...e){const o=t.getElementsByTagName("meta");for(let t=0;t<o.length;t++){const n=o[t],r=["itemprop","property","name"].map(t=>n.getAttribute(t)).filter(t=>!!t&&e.includes(t));if(r.length&&r){const t=n.getAttribute("content");if(t)return t}}return""}const r=function(){let e=o("name","og:site_name","og:title","twitter:title");return e||(e=t.title),e}();return{description:o("description","og:description","twitter:description","keywords"),url:e.origin,icons:function(){const o=t.getElementsByTagName("link"),n=[];for(let t=0;t<o.length;t++){const r=o[t],i=r.getAttribute("rel");if(i&&i.toLowerCase().indexOf("icon")>-1){const t=r.getAttribute("href");if(t)if(-1===t.toLowerCase().indexOf("https:")&&-1===t.toLowerCase().indexOf("http:")&&0!==t.indexOf("//")){let o=e.protocol+"//"+e.host;if(0===t.indexOf("/"))o+=t;else{const n=e.pathname.split("/");n.pop();o+=n.join("/")+"/"+t}n.push(o)}else if(0===t.indexOf("//")){const o=e.protocol+t;n.push(o)}else n.push(t)}}return n}(),name:r}}},function(t,e,o){"use strict";function n(t){let e=void 0;return"undefined"!=typeof window&&void 0!==window[t]&&(e=window[t]),e}function r(t){const e=n(t);if(!e)throw new Error(t+" is not defined in Window");return e}Object.defineProperty(e,"__esModule",{value:!0}),e.getLocalStorage=e.getLocalStorageOrThrow=e.getCrypto=e.getCryptoOrThrow=e.getLocation=e.getLocationOrThrow=e.getNavigator=e.getNavigatorOrThrow=e.getDocument=e.getDocumentOrThrow=e.getFromWindowOrThrow=e.getFromWindow=void 0,e.getFromWindow=n,e.getFromWindowOrThrow=r,e.getDocumentOrThrow=function(){return r("document")},e.getDocument=function(){return n("document")},e.getNavigatorOrThrow=function(){return r("navigator")},e.getNavigator=function(){return n("navigator")},e.getLocationOrThrow=function(){return r("location")},e.getLocation=function(){return n("location")},e.getCryptoOrThrow=function(){return r("crypto")},e.getCrypto=function(){return n("crypto")},e.getLocalStorageOrThrow=function(){return r("localStorage")},e.getLocalStorage=function(){return n("localStorage")}}])}));