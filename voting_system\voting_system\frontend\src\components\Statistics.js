import React, { useState, useEffect, lazy, Suspense, memo } from 'react';
import { Chart, registerables } from 'chart.js';

// Register Chart.js components
Chart.register(...registerables);

// Lazy load chart components to improve performance
const Bar = lazy(() => import('react-chartjs-2').then(module => ({ default: module.Bar })));
const Pie = lazy(() => import('react-chartjs-2').then(module => ({ default: module.Pie })));
const Line = lazy(() => import('react-chartjs-2').then(module => ({ default: module.Line })));

// Create memoized chart components to prevent unnecessary re-renders
const MemoizedBar = memo(Bar);
const MemoizedPie = memo(Pie);
const MemoizedLine = memo(Line);

// Loading placeholder for charts
const ChartPlaceholder = () => (
  <div className="chart-placeholder d-flex justify-content-center align-items-center" style={{ height: '100%', width: '100%' }}>
    <div className="spinner-border text-primary" role="status">
      <span className="visually-hidden">Loading chart...</span>
    </div>
  </div>
);

const Statistics = () => {
    const [stats, setStats] = useState(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState('');
    const [timeRange, setTimeRange] = useState('all'); // 'day', 'week', 'month', 'year', 'all'
    const [selectedElection, setSelectedElection] = useState('all');
    const [elections, setElections] = useState([]);

    useEffect(() => {
        fetchElections();
        fetchStatistics();
    }, [timeRange, selectedElection]);

    const fetchElections = async () => {
        try {
            const response = await fetch('/api/elections/');
            if (response.ok) {
                const data = await response.json();
                // Ensure data is an array
                if (Array.isArray(data)) {
                    setElections(data);
                } else {
                    console.warn('API did not return an array for elections');
                    // Use demo data
                    setElections([
                        { id: 1, title: 'Presidential Election 2023' },
                        { id: 2, title: 'Parliamentary Elections 2023' },
                        { id: 3, title: 'Gubernatorial Elections 2023' },
                    ]);
                }
            } else {
                console.warn('Failed to fetch elections');
                // Use demo data
                setElections([
                    { id: 1, title: 'Presidential Election 2023' },
                    { id: 2, title: 'Parliamentary Elections 2023' },
                    { id: 3, title: 'Gubernatorial Elections 2023' },
                ]);
            }
        } catch (error) {
            console.error('Error fetching elections:', error);
            // Use demo data
            setElections([
                { id: 1, title: 'Presidential Election 2023' },
                { id: 2, title: 'Parliamentary Elections 2023' },
                { id: 3, title: 'Gubernatorial Elections 2023' },
            ]);
        }
    };

    const fetchStatistics = async () => {
        setIsLoading(true);
        setError('');

        try {
            // Fetch data from the backend API
            const response = await fetch(`/api/statistics/?timeRange=${timeRange}&election=${selectedElection}`);

            if (!response.ok) {
                throw new Error(`Server responded with status: ${response.status}`);
            }

            const data = await response.json();

            // Validate the data structure
            if (!data || typeof data !== 'object') {
                throw new Error('Invalid data format received from server');
            }

            setStats(data);
            setIsLoading(false);
        } catch (error) {
            console.error('Error fetching statistics:', error);
            setError(`Failed to load statistics: ${error.message}. Using demo data instead.`);

            try {
                // Fallback to demo data
                const demoData = await generateDemoData();
                setStats(demoData);
            } catch (demoError) {
                console.error('Error generating demo data:', demoError);
                // If even demo data generation fails, set a more serious error
                setError('Failed to load statistics and demo data. Please try again later.');
            } finally {
                setIsLoading(false);
            }
        }
    };

    const generateDemoData = async () => {
        try {
            // Try to fetch dynamic demo data from the backend
            const response = await fetch('/api/statistics/demo-data/');

            if (response.ok) {
                const data = await response.json();
                return data;
            }
        } catch (error) {
            console.error('Error fetching dynamic demo data:', error);
        }

        // If the backend request fails, generate client-side demo data
        // This is a fallback mechanism only

        // Generate realistic demo data based on selected filters
        const voterTurnout = {
            labels: ['18-24', '25-34', '35-44', '45-54', '55-64', '65+'],
            datasets: [
                {
                    label: 'Voter Turnout by Age Group (%)',
                    data: [65, 78, 82, 88, 85, 72],
                    backgroundColor: 'rgba(54, 162, 235, 0.6)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1,
                }
            ]
        };

        const votingMethod = {
            labels: ['In Person', 'Mobile App', 'Web Browser'],
            datasets: [
                {
                    label: 'Voting Method',
                    data: [65, 20, 15],
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.6)',
                        'rgba(54, 162, 235, 0.6)',
                        'rgba(255, 206, 86, 0.6)',
                    ],
                    borderColor: [
                        'rgba(255, 99, 132, 1)',
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 206, 86, 1)',
                    ],
                    borderWidth: 1,
                }
            ]
        };

        const votingTrend = {
            labels: ['6am', '8am', '10am', '12pm', '2pm', '4pm', '6pm', '8pm'],
            datasets: [
                {
                    label: 'Votes Cast by Hour',
                    data: [120, 350, 580, 620, 750, 830, 920, 450],
                    fill: false,
                    backgroundColor: 'rgba(75, 192, 192, 0.6)',
                    borderColor: 'rgba(75, 192, 192, 1)',
                    tension: 0.1
                }
            ]
        };

        const geographicDistribution = {
            labels: ['Nairobi', 'Mombasa', 'Kisumu', 'Nakuru', 'Eldoret', 'Other Regions'],
            datasets: [
                {
                    label: 'Votes by Region (%)',
                    data: [28, 15, 12, 10, 8, 27],
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.6)',
                        'rgba(54, 162, 235, 0.6)',
                        'rgba(255, 206, 86, 0.6)',
                        'rgba(75, 192, 192, 0.6)',
                        'rgba(153, 102, 255, 0.6)',
                        'rgba(255, 159, 64, 0.6)',
                    ],
                    borderWidth: 1,
                }
            ]
        };

        // Generate random candidate data
        const candidateNames = ['Candidate A', 'Candidate B', 'Candidate C', 'Candidate D'];
        const candidateVotes = candidateNames.map(() => Math.floor(Math.random() * 1000000) + 500000);
        const totalVotes = candidateVotes.reduce((sum, votes) => sum + votes, 0);

        const candidatePerformance = {
            labels: candidateNames,
            datasets: [
                {
                    label: 'Votes Received',
                    data: candidateVotes,
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.6)',
                        'rgba(54, 162, 235, 0.6)',
                        'rgba(255, 206, 86, 0.6)',
                        'rgba(75, 192, 192, 0.6)',
                    ],
                    borderWidth: 1,
                }
            ]
        };

        // Generate realistic summary data
        const registeredVoters = Math.floor(Math.random() * 5000000) + 20000000;
        const turnoutPercentage = Math.floor(Math.random() * 20) + 65;
        const actualVoters = Math.floor(registeredVoters * (turnoutPercentage / 100));
        const malePercentage = Math.floor(Math.random() * 6) + 47; // 47-53%
        const maleVoters = Math.floor(actualVoters * (malePercentage / 100));
        const femaleVoters = actualVoters - maleVoters;

        return {
            summary: {
                totalVoters: actualVoters,
                registeredVoters: registeredVoters,
                turnoutPercentage: turnoutPercentage,
                maleVoters: maleVoters,
                femaleVoters: femaleVoters,
                averageAge: Math.floor(Math.random() * 7) + 38, // 38-45
                invalidVotes: Math.floor(actualVoters * 0.005), // 0.5% invalid votes
            },
            voterTurnout,
            votingMethod,
            votingTrend,
            geographicDistribution,
            candidatePerformance,
        };
    };

    if (isLoading) {
        return (
            <div className="container mt-4">
                <div className="text-center p-5">
                    <div className="spinner-border text-primary" role="status">
                        <span className="visually-hidden">Loading...</span>
                    </div>
                    <p className="mt-3">Loading statistics...</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="container mt-4">
                <div className="alert alert-danger" role="alert">
                    {error}
                </div>
            </div>
        );
    }

    return (
        <div className="container mt-4">
            <div className="d-flex justify-content-between align-items-center mb-4">
                <h2>Election Statistics Dashboard</h2>
                <div className="d-flex gap-2">
                    <select
                        className="form-select"
                        value={selectedElection}
                        onChange={(e) => setSelectedElection(e.target.value)}
                    >
                        <option value="all">All Elections</option>
                        {Array.isArray(elections) && elections.map(election => (
                            <option key={election.id} value={election.id}>
                                {election.title}
                            </option>
                        ))}
                    </select>
                    <select
                        className="form-select"
                        value={timeRange}
                        onChange={(e) => setTimeRange(e.target.value)}
                    >
                        <option value="all">All Time</option>
                        <option value="day">Last 24 Hours</option>
                        <option value="week">Last Week</option>
                        <option value="month">Last Month</option>
                        <option value="year">Last Year</option>
                    </select>
                </div>
            </div>

            {stats && (
                <>
                    {/* Summary Cards */}
                    <div className="row mb-4">
                        <div className="col-md-3 mb-3">
                            <div className="card h-100 border-primary">
                                <div className="card-body text-center">
                                    <h5 className="card-title">Total Votes Cast</h5>
                                    <h2 className="display-5 text-primary">{stats.summary.totalVoters.toLocaleString()}</h2>
                                </div>
                            </div>
                        </div>
                        <div className="col-md-3 mb-3">
                            <div className="card h-100 border-success">
                                <div className="card-body text-center">
                                    <h5 className="card-title">Voter Turnout</h5>
                                    <h2 className="display-5 text-success">{stats.summary.turnoutPercentage}%</h2>
                                </div>
                            </div>
                        </div>
                        <div className="col-md-3 mb-3">
                            <div className="card h-100 border-info">
                                <div className="card-body text-center">
                                    <h5 className="card-title">Average Voter Age</h5>
                                    <h2 className="display-5 text-info">{stats.summary.averageAge}</h2>
                                </div>
                            </div>
                        </div>
                        <div className="col-md-3 mb-3">
                            <div className="card h-100 border-warning">
                                <div className="card-body text-center">
                                    <h5 className="card-title">Invalid Votes</h5>
                                    <h2 className="display-5 text-warning">{stats.summary.invalidVotes.toLocaleString()}</h2>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Charts */}
                    <div className="row mb-4">
                        <div className="col-md-6 mb-4">
                            <div className="card h-100">
                                <div className="card-header">Voter Turnout by Age Group</div>
                                <div className="card-body" style={{ height: '300px' }}>
                                    <Suspense fallback={<ChartPlaceholder />}>
                                        <MemoizedBar
                                            data={stats.voterTurnout}
                                            options={{
                                                maintainAspectRatio: false,
                                                responsive: true,
                                                animation: {
                                                    duration: 500 // Reduce animation time for better performance
                                                }
                                            }}
                                        />
                                    </Suspense>
                                </div>
                            </div>
                        </div>
                        <div className="col-md-6 mb-4">
                            <div className="card h-100">
                                <div className="card-header">Voting Method Distribution</div>
                                <div className="card-body" style={{ height: '300px' }}>
                                    <Suspense fallback={<ChartPlaceholder />}>
                                        <MemoizedPie
                                            data={stats.votingMethod}
                                            options={{
                                                maintainAspectRatio: false,
                                                responsive: true,
                                                animation: {
                                                    duration: 500
                                                }
                                            }}
                                        />
                                    </Suspense>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="row mb-4">
                        <div className="col-md-12 mb-4">
                            <div className="card">
                                <div className="card-header">Voting Trend Throughout the Day</div>
                                <div className="card-body" style={{ height: '300px' }}>
                                    <Suspense fallback={<ChartPlaceholder />}>
                                        <MemoizedLine
                                            data={stats.votingTrend}
                                            options={{
                                                maintainAspectRatio: false,
                                                responsive: true,
                                                animation: {
                                                    duration: 500
                                                },
                                                elements: {
                                                    point: {
                                                        radius: 3 // Smaller points for better performance
                                                    },
                                                    line: {
                                                        tension: 0.2 // Less curved lines for better performance
                                                    }
                                                }
                                            }}
                                        />
                                    </Suspense>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="row mb-4">
                        <div className="col-md-6 mb-4">
                            <div className="card h-100">
                                <div className="card-header">Geographic Distribution of Votes</div>
                                <div className="card-body" style={{ height: '300px' }}>
                                    <Suspense fallback={<ChartPlaceholder />}>
                                        <MemoizedPie
                                            data={stats.geographicDistribution}
                                            options={{
                                                maintainAspectRatio: false,
                                                responsive: true,
                                                animation: {
                                                    duration: 500
                                                }
                                            }}
                                        />
                                    </Suspense>
                                </div>
                            </div>
                        </div>
                        <div className="col-md-6 mb-4">
                            <div className="card h-100">
                                <div className="card-header">Candidate Performance</div>
                                <div className="card-body" style={{ height: '300px' }}>
                                    <Suspense fallback={<ChartPlaceholder />}>
                                        <MemoizedBar
                                            data={stats.candidatePerformance}
                                            options={{
                                                maintainAspectRatio: false,
                                                responsive: true,
                                                animation: {
                                                    duration: 500
                                                }
                                            }}
                                        />
                                    </Suspense>
                                </div>
                            </div>
                        </div>
                    </div>
                </>
            )}
        </div>
    );
};

export default Statistics;
