/// <reference types="node" />
export declare function convertArrayBufferToBuffer(arrBuf: ArrayBuffer): Buffer;
export declare function convertArrayBufferToUtf8(arrBuf: ArrayBuffer): string;
export declare function convertArrayBufferToHex(arrBuf: ArrayBuffer, noPrefix?: boolean): string;
export declare function convertArrayBufferToNumber(arrBuf: ArrayBuffer): number;
export declare function concatArrayBuffers(...args: ArrayBuffer[]): ArrayBuffer;
export declare function convertBufferToArrayBuffer(buf: Buffer): ArrayBuffer;
export declare function convertBufferToUtf8(buf: Buffer): string;
export declare function convertBufferToHex(buf: Buffer, noPrefix?: boolean): string;
export declare function convertBufferToNumber(buf: Buffer): number;
export declare function concatBuffers(...args: Buffer[]): Buffer;
export declare function convertUtf8ToArrayBuffer(utf8: string): ArrayBuffer;
export declare function convertUtf8ToBuffer(utf8: string): Buffer;
export declare function convertUtf8ToHex(utf8: string, noPrefix?: boolean): string;
export declare function convertUtf8ToNumber(utf8: string): number;
export declare function convertHexToBuffer(hex: string): Buffer;
export declare function convertHexToArrayBuffer(hex: string): ArrayBuffer;
export declare function convertHexToUtf8(hex: string): string;
export declare function convertHexToNumber(hex: string): number;
export declare function convertNumberToBuffer(num: number): Buffer;
export declare function convertNumberToArrayBuffer(num: number): ArrayBuffer;
export declare function convertNumberToUtf8(num: number): string;
export declare function convertNumberToHex(num: number | string, noPrefix?: boolean): string;
//# sourceMappingURL=encoding.d.ts.map