{"version": 3, "file": "custom-element.js", "sources": ["../../../src/decorators/custom-element.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/*\n * IMPORTANT: For compatibility with <PERSON><PERSON><PERSON> and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\nimport {Constructor, ClassDescriptor} from './base.js';\n\n/**\n * Allow for custom element classes with private constructors\n */\ntype CustomElementClass = Omit<typeof HTMLElement, 'new'>;\n\nconst legacyCustomElement = (tagName: string, clazz: CustomElementClass) => {\n  customElements.define(tagName, clazz as CustomElementConstructor);\n  // Cast as any because TS doesn't recognize the return type as being a\n  // subtype of the decorated class when clazz is typed as\n  // `Constructor<HTMLElement>` for some reason.\n  // `Constructor<HTMLElement>` is helpful to make sure the decorator is\n  // applied to elements however.\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  return clazz as any;\n};\n\nconst standardCustomElement = (\n  tagName: string,\n  descriptor: ClassDescriptor\n) => {\n  const {kind, elements} = descriptor;\n  return {\n    kind,\n    elements,\n    // This callback is called once the class is otherwise fully defined\n    finisher(clazz: Constructor<HTMLElement>) {\n      customElements.define(tagName, clazz);\n    },\n  };\n};\n\n/**\n * Class decorator factory that defines the decorated class as a custom element.\n *\n * ```js\n * @customElement('my-element')\n * class MyElement extends LitElement {\n *   render() {\n *     return html``;\n *   }\n * }\n * ```\n * @category Decorator\n * @param tagName The tag name of the custom element to define.\n */\nexport const customElement =\n  (tagName: string) =>\n  (classOrDescriptor: CustomElementClass | ClassDescriptor) =>\n    typeof classOrDescriptor === 'function'\n      ? legacyCustomElement(tagName, classOrDescriptor)\n      : standardCustomElement(tagName, classOrDescriptor as ClassDescriptor);\n"], "names": [], "mappings": "AAAA;;;;AAIG;AAeH,MAAM,mBAAmB,GAAG,CAAC,OAAe,EAAE,KAAyB,KAAI;AACzE,IAAA,cAAc,CAAC,MAAM,CAAC,OAAO,EAAE,KAAiC,CAAC,CAAC;;;;;;;AAOlE,IAAA,OAAO,KAAY,CAAC;AACtB,CAAC,CAAC;AAEF,MAAM,qBAAqB,GAAG,CAC5B,OAAe,EACf,UAA2B,KACzB;AACF,IAAA,MAAM,EAAC,IAAI,EAAE,QAAQ,EAAC,GAAG,UAAU,CAAC;IACpC,OAAO;QACL,IAAI;QACJ,QAAQ;;AAER,QAAA,QAAQ,CAAC,KAA+B,EAAA;AACtC,YAAA,cAAc,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;SACvC;KACF,CAAC;AACJ,CAAC,CAAC;AAEF;;;;;;;;;;;;;AAaG;AACU,MAAA,aAAa,GACxB,CAAC,OAAe,KAChB,CAAC,iBAAuD,KACtD,OAAO,iBAAiB,KAAK,UAAU;AACrC,MAAE,mBAAmB,CAAC,OAAO,EAAE,iBAAiB,CAAC;AACjD,MAAE,qBAAqB,CAAC,OAAO,EAAE,iBAAoC;;;;"}