{"name": "cliui", "version": "5.0.0", "description": "easily create complex multi-column command-line-interfaces", "main": "index.js", "scripts": {"pretest": "standard", "test": "nyc mocha", "coverage": "nyc --reporter=text-lcov mocha | coveralls", "release": "standard-version"}, "repository": {"type": "git", "url": "http://github.com/yargs/cliui.git"}, "config": {"blanket": {"pattern": ["index.js"], "data-cover-never": ["node_modules", "test"], "output-reporter": "spec"}}, "standard": {"ignore": ["**/example/**"], "globals": ["it"]}, "keywords": ["cli", "command-line", "layout", "design", "console", "wrap", "table"], "author": "<PERSON> <<EMAIL>>", "license": "ISC", "dependencies": {"string-width": "^3.1.0", "strip-ansi": "^5.2.0", "wrap-ansi": "^5.1.0"}, "devDependencies": {"chai": "^4.2.0", "chalk": "^2.4.2", "coveralls": "^3.0.3", "mocha": "^6.0.2", "nyc": "^13.3.0", "standard": "^12.0.1", "standard-version": "^5.0.2"}, "files": ["index.js"], "engine": {"node": ">=6"}}