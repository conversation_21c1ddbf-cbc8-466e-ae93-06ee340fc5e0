"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ALL_EVENTS_ABI = exports.ALL_EVENTS = void 0;
/*
This file is part of web3.js.

web3.js is free software: you can redistribute it and/or modify
it under the terms of the GNU Lesser General Public License as published by
the Free Software Foundation, either version 3 of the License, or
(at your option) any later version.

web3.js is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU Lesser General Public License for more details.

You should have received a copy of the GNU Lesser General Public License
along with web3.js.  If not, see <http://www.gnu.org/licenses/>.
*/
var web3_eth_1 = require("web3-eth");
Object.defineProperty(exports, "ALL_EVENTS", { enumerable: true, get: function () { return web3_eth_1.ALL_EVENTS; } });
Object.defineProperty(exports, "ALL_EVENTS_ABI", { enumerable: true, get: function () { return web3_eth_1.ALL_EVENTS_ABI; } });
//# sourceMappingURL=constant.js.map