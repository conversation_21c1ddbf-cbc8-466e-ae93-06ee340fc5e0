# System Design Document

## 4.1 Introduction

### 4.1.1 Purpose
This document provides detailed system architecture and design specifications for the Blockchain-Based Decentralized Voting System.

### 4.1.2 Design Principles
- **Security by Design**: Security considerations integrated throughout
- **Modularity**: Loosely coupled components for maintainability
- **Scalability**: Architecture supports growth and expansion
- **Transparency**: Open and auditable system design

## 4.2 System Architecture

### 4.2.1 High-Level Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
├─────────────────────────────────────────────────────────────┤
│  React.js Frontend  │  Mobile App  │  Admin Dashboard      │
├─────────────────────────────────────────────────────────────┤
│                    Application Layer                        │
├─────────────────────────────────────────────────────────────┤
│  Django REST API    │  Authentication  │  Business Logic   │
├─────────────────────────────────────────────────────────────┤
│                    Blockchain Layer                         │
├─────────────────────────────────────────────────────────────┤
│  Ethereum Network  │  Smart Contracts │  Web3.js          │
├─────────────────────────────────────────────────────────────┤
│                    Data Layer                               │
├─────────────────────────────────────────────────────────────┤
│  PostgreSQL DB     │  IPFS Storage    │  File System       │
└─────────────────────────────────────────────────────────────┘
```

### 4.2.2 Component Interaction Flow

```
User Interface → Django API → Smart Contracts → Blockchain
     ↓              ↓              ↓              ↓
Database ← Business Logic ← Web3.js ← Ethereum Network
```

## 4.3 Detailed Component Design

### 4.3.1 Frontend Components

#### React.js Application Structure
```
src/
├── components/
│   ├── App.js                 # Main application component
│   ├── Header.js              # Navigation and user status
│   ├── ElectionList.js        # Display available elections
│   ├── VotingBooth.js         # Voting interface
│   ├── VoterRegistration.js   # Voter registration form
│   ├── WorkerLogin.js         # Worker authentication
│   ├── WorkerDashboard.js     # Worker management interface
│   ├── Statistics.js          # Voting analytics
│   └── AuthModal.js           # Authentication modal
├── services/
│   ├── api.js                 # API communication
│   ├── web3.js                # Blockchain interaction
│   └── auth.js                # Authentication services
└── utils/
    ├── constants.js           # Application constants
    └── helpers.js             # Utility functions
```

#### Key Frontend Features
- **Responsive Design**: Bootstrap-based responsive layout
- **Real-time Updates**: WebSocket connections for live data
- **Offline Capability**: Service worker for offline functionality
- **Accessibility**: WCAG 2.1 AA compliance

### 4.3.2 Backend Components

#### Django Application Structure
```
voting_system/
├── voting/
│   ├── models.py              # Database models
│   ├── views.py               # API endpoints
│   ├── serializers.py         # Data serialization
│   ├── urls.py                # URL routing
│   └── admin.py               # Admin interface
├── authentication/
│   ├── models.py              # User and worker models
│   ├── views.py               # Auth endpoints
│   └── permissions.py         # Access control
└── blockchain/
    ├── contracts.py           # Smart contract interaction
    ├── web3_utils.py          # Blockchain utilities
    └── deployment.py          # Contract deployment
```

#### API Endpoints
- **Authentication**: `/api/auth/login/`, `/api/auth/logout/`
- **Voter Management**: `/api/voters/`, `/api/voters/register/`
- **Elections**: `/api/elections/`, `/api/elections/{id}/`
- **Voting**: `/api/vote/`, `/api/results/`
- **Worker Functions**: `/api/worker/register-voter/`

### 4.3.3 Blockchain Components

#### Smart Contract Architecture
```solidity
contracts/
├── VotingSystem.sol           # Main voting contract
├── ElectionFactory.sol        # Election creation
├── VoterRegistry.sol          # Voter management
├── CandidateRegistry.sol      # Candidate management
└── AccessControl.sol          # Permission management
```

#### Smart Contract Functions
- **Election Management**: Create, configure, and manage elections
- **Vote Casting**: Secure vote recording with verification
- **Result Calculation**: Automated vote counting
- **Access Control**: Role-based permission system

## 4.4 Database Design

### 4.4.1 Entity Relationship Diagram

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│    User     │────│   Voter     │────│    Vote     │
│             │    │             │    │             │
│ - id        │    │ - id        │    │ - id        │
│ - username  │    │ - id_number │    │ - voter_id  │
│ - password  │    │ - phone     │    │ - election  │
│ - email     │    │ - county    │    │ - candidate │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       │            ┌─────────────┐    ┌─────────────┐
       └────────────│   Worker    │    │  Election   │
                    │             │    │             │
                    │ - id        │    │ - id        │
                    │ - emp_id    │    │ - name      │
                    │ - station   │    │ - start_date│
                    └─────────────┘    │ - end_date  │
                                       └─────────────┘
```

### 4.4.2 Key Database Tables

#### Voters Table
```sql
CREATE TABLE voters (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES auth_user(id),
    id_number VARCHAR(8) UNIQUE NOT NULL,
    phone_number VARCHAR(15),
    date_of_birth DATE,
    constituency_id INTEGER REFERENCES constituencies(id),
    polling_station_id INTEGER REFERENCES polling_stations(id),
    registered_by_id INTEGER REFERENCES workers(id),
    registration_date TIMESTAMP DEFAULT NOW(),
    is_verified BOOLEAN DEFAULT FALSE
);
```

#### Elections Table
```sql
CREATE TABLE elections (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    election_type VARCHAR(50),
    start_date TIMESTAMP,
    end_date TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    smart_contract_address VARCHAR(42),
    created_by_id INTEGER REFERENCES auth_user(id),
    created_at TIMESTAMP DEFAULT NOW()
);
```

## 4.5 Security Architecture

### 4.5.1 Authentication Flow
```
1. User Login → 2. Credential Validation → 3. Token Generation
       ↓                    ↓                       ↓
4. Token Storage ← 5. Session Creation ← 6. Permission Check
```

### 4.5.2 Security Measures
- **Encryption**: AES-256 for data at rest, TLS 1.3 for transit
- **Authentication**: JWT tokens with role-based access
- **Smart Contract Security**: Reentrancy guards, access modifiers
- **Input Validation**: Comprehensive validation on all inputs
- **Audit Logging**: Complete activity logs for all operations

## 4.6 Integration Architecture

### 4.6.1 External Integrations
- **Ethereum Network**: Mainnet/Testnet connectivity
- **IPFS**: Distributed file storage for documents
- **SMS Gateway**: Voter notification system
- **Email Service**: Administrative notifications

### 4.6.2 API Integration Points
- **Blockchain RPC**: Web3 provider connections
- **Database**: PostgreSQL connection pooling
- **File Storage**: Local and cloud storage options
- **Monitoring**: Application performance monitoring

## 4.7 Deployment Architecture

### 4.7.1 Production Environment
```
Load Balancer → Web Servers → Application Servers → Database
      ↓              ↓              ↓              ↓
   SSL/TLS    →   Django App  →  PostgreSQL  →  Blockchain
```

### 4.7.2 Infrastructure Components
- **Web Server**: Nginx for static files and reverse proxy
- **Application Server**: Gunicorn for Django application
- **Database**: PostgreSQL with replication
- **Blockchain**: Ethereum node or Infura connection
- **Monitoring**: Prometheus and Grafana for metrics

---

**Document Version**: 1.0  
**Date**: December 2024  
**Author**: [Student Name]  
**Institution**: [University Name]  
**Course**: [Course Code and Name]
