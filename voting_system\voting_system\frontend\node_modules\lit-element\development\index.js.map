{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,cAAc,uBAAuB,CAAC;AACtC,cAAc,UAAU,CAAC;AACzB,cAAc,kBAAkB,CAAC;AACjC,cAAc,iBAAiB,CAAC;AAEhC,OAAO,CAAC,IAAI,CACV,wEAAwE;IACtE,uEAAuE;IACvE,mDAAmD;IACnD,kEAAkE,CACrE,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nexport * from '@lit/reactive-element';\nexport * from 'lit-html';\nexport * from './lit-element.js';\nexport * from './decorators.js';\n\nconsole.warn(\n  \"The main 'lit-element' module entrypoint is deprecated. Please update \" +\n    \"your imports to use the 'lit' package: 'lit' and 'lit/decorators.ts' \" +\n    \"or import from 'lit-element/lit-element.ts'. See \" +\n    'https://lit.dev/msg/deprecated-import-path for more information.'\n);\n"]}