"""
Generated by `compile_contracts.py` script.
Compiled with Solidity v0.8.19.
"""

# source: web3/_utils/contract_sources/RevertContract.sol:RevertContract
REVERT_CONTRACT_BYTECODE = "0x608060405234801561001057600080fd5b506101aa806100206000396000f3fe608060405234801561001057600080fd5b50600436106100415760003560e01c8063185c38a414610046578063c06a97cb14610050578063d67e4b841461005a575b600080fd5b61004e610078565b005b6100586100b3565b005b6100626100b8565b60405161006f91906100dc565b60405180910390f35b6040517f08c379a00000000000000000000000000000000000000000000000000000000081526004016100aa90610154565b60405180910390fd5b600080fd5b60006001905090565b60008115159050919050565b6100d6816100c1565b82525050565b60006020820190506100f160008301846100cd565b92915050565b600082825260208201905092915050565b7f46756e6374696f6e20686173206265656e2072657665727465642e0000000000600082015250565b600061013e601b836100f7565b915061014982610108565b602082019050919050565b6000602082019050818103600083015261016d81610131565b905091905056fea26469706673582212201bcf63171d1edaa8e200af05c22b99aee2cc9f22b65c86f98f9722c252ae5ae664736f6c63430008130033"  # noqa: E501
REVERT_CONTRACT_RUNTIME = "0x608060405234801561001057600080fd5b50600436106100415760003560e01c8063185c38a414610046578063c06a97cb14610050578063d67e4b841461005a575b600080fd5b61004e610078565b005b6100586100b3565b005b6100626100b8565b60405161006f91906100dc565b60405180910390f35b6040517f08c379a00000000000000000000000000000000000000000000000000000000081526004016100aa90610154565b60405180910390fd5b600080fd5b60006001905090565b60008115159050919050565b6100d6816100c1565b82525050565b60006020820190506100f160008301846100cd565b92915050565b600082825260208201905092915050565b7f46756e6374696f6e20686173206265656e2072657665727465642e0000000000600082015250565b600061013e601b836100f7565b915061014982610108565b602082019050919050565b6000602082019050818103600083015261016d81610131565b905091905056fea26469706673582212201bcf63171d1edaa8e200af05c22b99aee2cc9f22b65c86f98f9722c252ae5ae664736f6c63430008130033"  # noqa: E501
REVERT_CONTRACT_ABI = [
    {
        "inputs": [],
        "name": "normalFunction",
        "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
        "stateMutability": "pure",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "revertWithMessage",
        "outputs": [],
        "stateMutability": "pure",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "revertWithoutMessage",
        "outputs": [],
        "stateMutability": "pure",
        "type": "function",
    },
]
REVERT_CONTRACT_DATA = {
    "bytecode": REVERT_CONTRACT_BYTECODE,
    "bytecode_runtime": REVERT_CONTRACT_RUNTIME,
    "abi": REVERT_CONTRACT_ABI,
}
