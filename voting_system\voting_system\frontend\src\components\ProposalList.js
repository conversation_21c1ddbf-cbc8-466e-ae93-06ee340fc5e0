import React from 'react';

const ProposalList = ({ proposals, onSelect, onCreateNew }) => {
    // Separate active and past proposals
    const activeProposals = proposals.filter(p => p.is_active);
    const pastProposals = proposals.filter(p => !p.is_active);

    return (
        <div>
            <div className="d-flex justify-content-between align-items-center mb-4">
                <h2>Active Proposals</h2>
                <button className="btn btn-primary" onClick={onCreateNew}>
                    Create New Proposal
                </button>
            </div>

            {activeProposals.length > 0 ? (
                <div className="row">
                    {activeProposals.map(proposal => (
                        <div className="col-md-4" key={proposal.id}>
                            <div className="card proposal-card">
                                <div className="card-body">
                                    <h5 className="card-title">{proposal.title}</h5>
                                    <p className="card-text">
                                        {proposal.description.length > 100
                                            ? `${proposal.description.substring(0, 100)}...`
                                            : proposal.description}
                                    </p>
                                    <div className="d-flex justify-content-between align-items-center">
                                        <span className="proposal-status status-active">Active</span>
                                        <span className="vote-count">{proposal.vote_count} votes</span>
                                    </div>
                                </div>
                                <div className="card-footer">
                                    <small className="text-muted">
                                        Ends: {new Date(proposal.end_time).toLocaleString()}
                                    </small>
                                    <button
                                        className="btn btn-outline-primary btn-sm"
                                        onClick={() => onSelect(proposal)}
                                    >
                                        View Details
                                    </button>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            ) : (
                <div className="alert alert-info">
                    No active proposals at the moment.
                </div>
            )}

            <h2 className="mt-5 mb-4">Past Proposals</h2>

            {pastProposals.length > 0 ? (
                <div className="row">
                    {pastProposals.map(proposal => (
                        <div className="col-md-4" key={proposal.id}>
                            <div className="card proposal-card">
                                <div className="card-body">
                                    <h5 className="card-title">{proposal.title}</h5>
                                    <p className="card-text">
                                        {proposal.description.length > 100
                                            ? `${proposal.description.substring(0, 100)}...`
                                            : proposal.description}
                                    </p>
                                    <div className="d-flex justify-content-between align-items-center">
                                        <span className="proposal-status status-closed">Closed</span>
                                        <span className="vote-count">{proposal.vote_count} votes</span>
                                    </div>
                                </div>
                                <div className="card-footer">
                                    <small className="text-muted">
                                        Ended: {new Date(proposal.end_time).toLocaleString()}
                                    </small>
                                    <button
                                        className="btn btn-outline-secondary btn-sm"
                                        onClick={() => onSelect(proposal)}
                                    >
                                        View Results
                                    </button>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            ) : (
                <div className="alert alert-info">
                    No past proposals.
                </div>
            )}
        </div>
    );
};

export default ProposalList;
