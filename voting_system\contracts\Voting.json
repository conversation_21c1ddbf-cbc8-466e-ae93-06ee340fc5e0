{"abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "proposalId", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "title", "type": "string"}, {"indexed": false, "internalType": "string", "name": "description", "type": "string"}, {"indexed": false, "internalType": "uint256", "name": "endTime", "type": "uint256"}], "name": "ProposalCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "proposalId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "voteCount", "type": "uint256"}], "name": "ProposalEnded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "voter", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "proposalId", "type": "uint256"}], "name": "Voted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "voterAddress", "type": "address"}], "name": "VoterRegistered", "type": "event"}, {"inputs": [], "name": "admin", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "_title", "type": "string"}, {"internalType": "string", "name": "_description", "type": "string"}, {"internalType": "uint256", "name": "_durationInMinutes", "type": "uint256"}], "name": "createProposal", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_proposalId", "type": "uint256"}], "name": "endProposal", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_proposalId", "type": "uint256"}], "name": "getProposal", "outputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "string", "name": "title", "type": "string"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "uint256", "name": "voteCount", "type": "uint256"}, {"internalType": "bool", "name": "active", "type": "bool"}, {"internalType": "uint256", "name": "createdAt", "type": "uint256"}, {"internalType": "uint256", "name": "endTime", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getProposalCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_voter", "type": "address"}, {"internalType": "uint256", "name": "_proposalId", "type": "uint256"}], "name": "hasVotedForProposal", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "proposalCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "proposals", "outputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "string", "name": "title", "type": "string"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "uint256", "name": "voteCount", "type": "uint256"}, {"internalType": "bool", "name": "active", "type": "bool"}, {"internalType": "uint256", "name": "createdAt", "type": "uint256"}, {"internalType": "uint256", "name": "endTime", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_voter", "type": "address"}], "name": "registerVoter", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_proposalId", "type": "uint256"}], "name": "vote", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "voters", "outputs": [{"internalType": "bool", "name": "isRegistered", "type": "bool"}, {"internalType": "bool", "name": "hasVoted", "type": "bool"}, {"internalType": "uint256", "name": "votedProposalId", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "address", "name": "", "type": "address"}], "name": "votes", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}], "bytecode": "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"}