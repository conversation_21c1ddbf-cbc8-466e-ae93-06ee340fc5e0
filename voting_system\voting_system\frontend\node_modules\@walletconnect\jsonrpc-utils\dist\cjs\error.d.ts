import { ErrorResponse } from "./types";
import { JsonRpcError, JsonRpcValidation } from "@walletconnect/jsonrpc-types";
export declare function isServerErrorCode(code: number): boolean;
export declare function isReservedErrorCode(code: number): boolean;
export declare function isValidErrorCode(code: number): boolean;
export declare function getError(type: string): ErrorResponse;
export declare function getErrorByCode(code: number): ErrorResponse;
export declare function validateJsonRpcError(response: JsonRpcError): JsonRpcValidation;
export declare function parseConnectionError(e: Error, url: string, type: string): Error;
//# sourceMappingURL=error.d.ts.map