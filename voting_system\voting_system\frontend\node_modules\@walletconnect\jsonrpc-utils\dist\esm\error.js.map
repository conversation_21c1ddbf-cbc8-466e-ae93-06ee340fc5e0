{"version": 3, "file": "error.js", "sourceRoot": "", "sources": ["../../src/error.ts"], "names": [], "mappings": "AACA,OAAO,EACL,uBAAuB,EACvB,oBAAoB,EACpB,kBAAkB,EAClB,aAAa,GACd,MAAM,aAAa,CAAC;AAGrB,MAAM,UAAU,iBAAiB,CAAC,IAAY;IAC5C,OAAO,IAAI,IAAI,uBAAuB,CAAC,CAAC,CAAC,IAAI,IAAI,IAAI,uBAAuB,CAAC,CAAC,CAAC,CAAC;AAClF,CAAC;AAED,MAAM,UAAU,mBAAmB,CAAC,IAAY;IAC9C,OAAO,oBAAoB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AAC7C,CAAC;AAED,MAAM,UAAU,gBAAgB,CAAC,IAAY;IAC3C,OAAO,OAAO,IAAI,KAAK,QAAQ,CAAC;AAClC,CAAC;AAED,MAAM,UAAU,QAAQ,CAAC,IAAY;IACnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QACnD,OAAO,kBAAkB,CAAC,aAAa,CAAC,CAAC;KAC1C;IACD,OAAO,kBAAkB,CAAC,IAAI,CAAC,CAAC;AAClC,CAAC;AAED,MAAM,UAAU,cAAc,CAAC,IAAY;IACzC,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;IAC3E,IAAI,CAAC,KAAK,EAAE;QACV,OAAO,kBAAkB,CAAC,aAAa,CAAC,CAAC;KAC1C;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,MAAM,UAAU,oBAAoB,CAAC,QAAsB;IACzD,IAAI,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,WAAW,EAAE;QAC9C,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,iCAAiC,EAAE,CAAC;KACnE;IACD,IAAI,OAAO,QAAQ,CAAC,KAAK,CAAC,OAAO,KAAK,WAAW,EAAE;QACjD,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,oCAAoC,EAAE,CAAC;KACtE;IACD,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;QAC1C,OAAO;YACL,KAAK,EAAE,KAAK;YACZ,KAAK,EAAE,yCAAyC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE;SACtE,CAAC;KACH;IACD,IAAI,mBAAmB,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,MAAM,KAAK,GAAG,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAClD,IACE,KAAK,CAAC,OAAO,KAAK,kBAAkB,CAAC,aAAa,CAAC,CAAC,OAAO;YAC3D,QAAQ,CAAC,KAAK,CAAC,OAAO,KAAK,KAAK,CAAC,OAAO,EACxC;YACA,OAAO;gBACL,KAAK,EAAE,KAAK;gBACZ,KAAK,EAAE,4CAA4C,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE;aACzE,CAAC;SACH;KACF;IACD,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;AACzB,CAAC;AAED,MAAM,UAAU,oBAAoB,CAAC,CAAQ,EAAE,GAAW,EAAE,IAAY;IACtE,OAAO,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,sBAAsB,CAAC;QAC9F,CAAC,CAAC,IAAI,KAAK,CAAC,eAAe,IAAI,eAAe,GAAG,EAAE,CAAC;QACpD,CAAC,CAAC,CAAC,CAAC;AACR,CAAC"}