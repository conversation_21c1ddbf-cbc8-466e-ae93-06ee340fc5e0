import{proxy as m,subscribe as f,snapshot as $}from"valtio/vanilla";import{Buffer as V}from"buffer";let N;const C={ethereumClient:void 0,setEthereumClient(e){N=e},client(){if(N)return N;throw new Error("ClientCtrl has no client set")}},i=m({history:["ConnectWallet"],view:"ConnectWallet",data:void 0}),b={state:i,subscribe(e){return f(i,()=>e(i))},push(e,t){e!==i.view&&(i.view=e,t&&(i.data=t),i.history.push(e))},reset(e){i.view=e,i.history=[e]},replace(e){i.history.length>1&&(i.history[i.history.length-1]=e,i.view=e)},goBack(){if(i.history.length>1){i.history.pop();const[e]=i.history.slice(-1);i.view=e}},setData(e){i.data=e}},c={WALLETCONNECT_DEEPLINK_CHOICE:"WALLETCONNECT_DEEPLINK_CHOICE",W3M_VERSION:"W3M_VERSION",W3M_PREFER_INJECTED_URL_FLAG:"w3mPreferInjected",RECOMMENDED_WALLET_AMOUNT:9,isMobile(){return typeof window<"u"?!!(window.matchMedia("(pointer:coarse)").matches||/Android|webOS|iPhone|iPad|iPod|BlackBerry|Opera Mini/u.test(navigator.userAgent)):!1},isAndroid(){return c.isMobile()&&navigator.userAgent.toLowerCase().includes("android")},isIos(){const e=navigator.userAgent.toLowerCase();return c.isMobile()&&(e.includes("iphone")||e.includes("ipad"))},isHttpUrl(e){return e.startsWith("http://")||e.startsWith("https://")},isArray(e){return Array.isArray(e)&&e.length>0},formatNativeUrl(e,t,n){if(c.isHttpUrl(e))return this.formatUniversalUrl(e,t,n);let s=e;s.includes("://")||(s=e.replaceAll("/","").replaceAll(":",""),s=`${s}://`),this.setWalletConnectDeepLink(s,n);const a=encodeURIComponent(t);return`${s}wc?uri=${a}`},formatUniversalUrl(e,t,n){if(!c.isHttpUrl(e))return this.formatNativeUrl(e,t,n);let s=e;e.endsWith("/")&&(s=e.slice(0,-1)),this.setWalletConnectDeepLink(s,n);const a=encodeURIComponent(t);return`${s}/wc?uri=${a}`},async wait(e){return new Promise(t=>{setTimeout(t,e)})},openHref(e,t){window.open(e,t,"noreferrer noopener")},setWalletConnectDeepLink(e,t){localStorage.setItem(c.WALLETCONNECT_DEEPLINK_CHOICE,JSON.stringify({href:e,name:t}))},setWalletConnectAndroidDeepLink(e){const[t]=e.split("?");localStorage.setItem(c.WALLETCONNECT_DEEPLINK_CHOICE,JSON.stringify({href:t,name:"Android"}))},removeWalletConnectDeepLink(){localStorage.removeItem(c.WALLETCONNECT_DEEPLINK_CHOICE)},setWeb3ModalVersionInStorage(){typeof localStorage<"u"&&localStorage.setItem(c.W3M_VERSION,"2.4.2")},getWalletRouterData(){var e;const t=(e=b.state.data)==null?void 0:e.Wallet;if(!t)throw new Error('Missing "Wallet" view data');return t},getSwitchNetworkRouterData(){var e;const t=(e=b.state.data)==null?void 0:e.SwitchNetwork;if(!t)throw new Error('Missing "SwitchNetwork" view data');return t},isPreferInjectedFlag(){return typeof location<"u"?new URLSearchParams(location.search).has(c.W3M_PREFER_INJECTED_URL_FLAG):!1}},B=typeof location<"u"&&(location.hostname.includes("localhost")||location.protocol.includes("https")),u=m({enabled:B,userSessionId:"",events:[],connectedWalletId:void 0}),H={state:u,subscribe(e){return f(u.events,()=>e($(u.events[u.events.length-1])))},initialize(){u.enabled&&typeof crypto<"u"&&(u.userSessionId=crypto.randomUUID())},setConnectedWalletId(e){u.connectedWalletId=e},click(e){if(u.enabled){const t={type:"CLICK",name:e.name,userSessionId:u.userSessionId,timestamp:Date.now(),data:e};u.events.push(t)}},track(e){if(u.enabled){const t={type:"TRACK",name:e.name,userSessionId:u.userSessionId,timestamp:Date.now(),data:e};u.events.push(t)}},view(e){if(u.enabled){const t={type:"VIEW",name:e.name,userSessionId:u.userSessionId,timestamp:Date.now(),data:e};u.events.push(t)}}},l=m({selectedChain:void 0,chains:void 0,standaloneChains:void 0,standaloneUri:void 0,isStandalone:!1,isAuth:!1,isCustomDesktop:!1,isCustomMobile:!1,isDataLoaded:!1,isUiLoaded:!1,isPreferInjected:!1,walletConnectVersion:1}),d={state:l,subscribe(e){return f(l,()=>e(l))},setChains(e){l.chains=e},setStandaloneChains(e){l.standaloneChains=e},setStandaloneUri(e){l.standaloneUri=e},getSelectedChain(){const e=C.client().getNetwork().chain;return e&&(l.selectedChain=e),l.selectedChain},setSelectedChain(e){l.selectedChain=e},setIsStandalone(e){l.isStandalone=e},setIsCustomDesktop(e){l.isCustomDesktop=e},setIsCustomMobile(e){l.isCustomMobile=e},setIsDataLoaded(e){l.isDataLoaded=e},setIsUiLoaded(e){l.isUiLoaded=e},setWalletConnectVersion(e){l.walletConnectVersion=e},setIsPreferInjected(e){l.isPreferInjected=e},setIsAuth(e){l.isAuth=e}},O=m({projectId:"",mobileWallets:void 0,desktopWallets:void 0,walletImages:void 0,chainImages:void 0,tokenImages:void 0,tokenContracts:void 0,standaloneChains:void 0,enableStandaloneMode:!1,enableAuthMode:!1,enableNetworkView:!1,enableAccountView:!0,enableExplorer:!0,defaultChain:void 0,explorerExcludedWalletIds:void 0,explorerRecommendedWalletIds:void 0,termsOfServiceUrl:void 0,privacyPolicyUrl:void 0}),y={state:O,subscribe(e){return f(O,()=>e(O))},setConfig(e){var t,n,s,a;H.initialize(),d.setStandaloneChains(e.standaloneChains),d.setIsStandalone(!!((t=e.standaloneChains)!=null&&t.length)||!!e.enableStandaloneMode),d.setIsAuth(!!e.enableAuthMode),d.setIsCustomMobile(!!((n=e.mobileWallets)!=null&&n.length)),d.setIsCustomDesktop(!!((s=e.desktopWallets)!=null&&s.length)),d.setWalletConnectVersion((a=e.walletConnectVersion)!=null?a:1),d.state.isStandalone||(d.setChains(C.client().chains),d.setIsPreferInjected(C.client().isInjectedProviderInstalled()&&c.isPreferInjectedFlag())),e.defaultChain&&d.setSelectedChain(e.defaultChain),c.setWeb3ModalVersionInStorage(),Object.assign(O,e)}},o=m({address:void 0,profileName:void 0,profileAvatar:void 0,profileLoading:!1,balanceLoading:!1,balance:void 0,isConnected:!1}),K={state:o,subscribe(e){return f(o,()=>e(o))},getAccount(){const e=C.client().getAccount();o.address=e.address,o.isConnected=e.isConnected},async fetchProfile(e,t){var n;try{o.profileLoading=!0;const s=t??o.address,a=(n=d.state.chains)==null?void 0:n.find(r=>r.id===1);if(s&&a){const r=await C.client().fetchEnsName({address:s,chainId:1});if(r){const p=await C.client().fetchEnsAvatar({name:r,chainId:1});p&&await e(p),o.profileAvatar=p}o.profileName=r}}finally{o.profileLoading=!1}},async fetchBalance(e){try{const{chain:t}=C.client().getNetwork(),{tokenContracts:n}=y.state;let s;t&&n&&(s=n[t.id]),o.balanceLoading=!0;const a=e??o.address;if(a){const r=await C.client().fetchBalance({address:a,token:s});o.balance={amount:r.formatted,symbol:r.symbol}}}finally{o.balanceLoading=!1}},setAddress(e){o.address=e},setIsConnected(e){o.isConnected=e},resetBalance(){o.balance=void 0},resetAccount(){o.address=void 0,o.isConnected=!1,o.profileName=void 0,o.profileAvatar=void 0,o.balance=void 0}},M="https://explorer-api.walletconnect.com";async function U(e,t){const n=new URL(e,M);return n.searchParams.append("projectId",y.state.projectId),Object.entries(t).forEach(([s,a])=>{a&&n.searchParams.append(s,String(a))}),(await fetch(n)).json()}const I={async getDesktopListings(e){return U("/w3m/v1/getDesktopListings",e)},async getMobileListings(e){return U("/w3m/v1/getMobileListings",e)},async getInjectedListings(e){return U("/w3m/v1/getInjectedListings",e)},async getAllListings(e){return U("/w3m/v1/getAllListings",e)},getWalletImageUrl(e){return`${M}/w3m/v1/getWalletImage/${e}?projectId=${y.state.projectId}`},getAssetImageUrl(e){return`${M}/w3m/v1/getAssetImage/${e}?projectId=${y.state.projectId}`}};var F=Object.defineProperty,D=Object.getOwnPropertySymbols,J=Object.prototype.hasOwnProperty,z=Object.prototype.propertyIsEnumerable,k=(e,t,n)=>t in e?F(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,G=(e,t)=>{for(var n in t||(t={}))J.call(t,n)&&k(e,n,t[n]);if(D)for(var n of D(t))z.call(t,n)&&k(e,n,t[n]);return e};const P=c.isMobile(),g=m({wallets:{listings:[],total:0,page:1},injectedWallets:[],search:{listings:[],total:0,page:1},recomendedWallets:[]}),ne={state:g,async getRecomendedWallets(){const{explorerRecommendedWalletIds:e,explorerExcludedWalletIds:t}=y.state;if(e==="NONE"||t==="ALL"&&!e)return g.recomendedWallets;if(c.isArray(e)){const n={recommendedIds:e.join(",")},{listings:s}=await I.getAllListings(n),a=Object.values(s);a.sort((r,p)=>{const h=e.indexOf(r.id),v=e.indexOf(p.id);return h-v}),g.recomendedWallets=a}else{const{standaloneChains:n,walletConnectVersion:s,isAuth:a}=d.state,r=n?.join(","),p=c.isArray(t),h={page:1,sdks:a?"auth_v1":void 0,entries:c.RECOMMENDED_WALLET_AMOUNT,chains:r,version:s,excludedIds:p?t.join(","):void 0},{listings:v}=P?await I.getMobileListings(h):await I.getDesktopListings(h);g.recomendedWallets=Object.values(v)}return g.recomendedWallets},async getWallets(e){const t=G({},e),{explorerRecommendedWalletIds:n,explorerExcludedWalletIds:s}=y.state,{recomendedWallets:a}=g;if(s==="ALL")return g.wallets;t.search||(a.length?t.excludedIds=a.map(W=>W.id).join(","):c.isArray(n)&&(t.excludedIds=n.join(","))),c.isArray(s)&&(t.excludedIds=[t.excludedIds,s].filter(Boolean).join(",")),d.state.isAuth&&(t.sdks="auth_v1");const{page:r,search:p}=e,{listings:h,total:v}=P?await I.getMobileListings(t):await I.getDesktopListings(t),j=Object.values(h),L=p?"search":"wallets";return g[L]={listings:[...g[L].listings,...j],total:v,page:r??1},{listings:j,total:v}},async getInjectedWallets(){const{listings:e}=await I.getInjectedListings({}),t=Object.values(e);return g.injectedWallets=t,g.injectedWallets},getWalletImageUrl(e){return I.getWalletImageUrl(e)},getAssetImageUrl(e){return I.getAssetImageUrl(e)},resetSearch(){g.search={listings:[],total:0,page:1}}},A=m({pairingUri:"",pairingError:!1}),_={state:A,subscribe(e){return f(A,()=>e(A))},setPairingUri(e){A.pairingUri=e},setPairingError(e){A.pairingError=e}},E=m({open:!1}),se={state:E,subscribe(e){return f(E,()=>e(E))},async open(e){return new Promise(t=>{const{isStandalone:n,isUiLoaded:s,isDataLoaded:a,isPreferInjected:r,selectedChain:p}=d.state,{isConnected:h}=K.state,{enableNetworkView:v}=y.state;if(n)d.setStandaloneUri(e?.uri),d.setStandaloneChains(e?.standaloneChains),b.reset("ConnectWallet");else if(e!=null&&e.route)b.reset(e.route);else if(h)b.reset("Account");else if(v)b.reset("SelectNetwork");else if(r){C.client().connectConnector("injected",p?.id).catch(L=>console.error(L)),t();return}else b.reset("ConnectWallet");const{pairingUri:j}=_.state;if(s&&a&&(n||j||h))E.open=!0,t();else{const L=setInterval(()=>{const W=d.state,T=_.state;W.isUiLoaded&&W.isDataLoaded&&(W.isStandalone||T.pairingUri||h)&&(clearInterval(L),E.open=!0,t())},200)}})},close(){E.open=!1}};var q=Object.defineProperty,x=Object.getOwnPropertySymbols,Q=Object.prototype.hasOwnProperty,X=Object.prototype.propertyIsEnumerable,R=(e,t,n)=>t in e?q(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,Y=(e,t)=>{for(var n in t||(t={}))Q.call(t,n)&&R(e,n,t[n]);if(x)for(var n of x(t))X.call(t,n)&&R(e,n,t[n]);return e};function Z(){return typeof matchMedia<"u"&&matchMedia("(prefers-color-scheme: dark)").matches}const S=m({themeMode:Z()?"dark":"light"}),ae={state:S,subscribe(e){return f(S,()=>e(S))},setThemeConfig(e){const{themeMode:t,themeVariables:n}=e;t&&(S.themeMode=t),n&&(S.themeVariables=Y({},n))}},w=m({open:!1,message:"",variant:"success"}),oe={state:w,subscribe(e){return f(w,()=>e(w))},openToast(e,t){w.open=!0,w.message=e,w.variant=t},closeToast(){w.open=!1}};typeof window<"u"&&(window.Buffer||(window.Buffer=V),window.global||(window.global=window),window.process||(window.process={env:{}}));export{K as AccountCtrl,C as ClientCtrl,y as ConfigCtrl,c as CoreUtil,H as EventsCtrl,ne as ExplorerCtrl,se as ModalCtrl,d as OptionsCtrl,b as RouterCtrl,ae as ThemeCtrl,oe as ToastCtrl,_ as WcConnectionCtrl};
