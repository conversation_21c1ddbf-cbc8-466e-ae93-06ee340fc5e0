{"name": "pify", "version": "5.0.0", "description": "Promisify a callback-style function", "license": "MIT", "repository": "sindresorhus/pify", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava", "optimization-test": "node --allow-natives-syntax optimization-test.js"}, "files": ["index.js"], "keywords": ["promisify", "callback", "promise", "promises", "denodify", "denodeify", "node", "then", "thenify", "convert", "transform", "wrap", "wrapper", "bind", "async", "await", "es2015", "bluebird"], "devDependencies": {"ava": "^2.4.0", "pinkie-promise": "^2.0.0", "v8-natives": "^1.1.0", "xo": "^0.26.1"}}