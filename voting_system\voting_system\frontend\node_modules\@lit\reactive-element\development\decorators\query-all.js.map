{"version": 3, "file": "query-all.js", "sourceRoot": "", "sources": ["../../src/decorators/query-all.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAUH,OAAO,EAAC,gBAAgB,EAAC,MAAM,WAAW,CAAC;AAE3C;;;;;;;;;;;;;;;;;;;;;;;GAuBG;AACH,MAAM,UAAU,QAAQ,CAAC,QAAgB;IACvC,OAAO,gBAAgB,CAAC;QACtB,UAAU,EAAE,CAAC,KAAkB,EAAE,EAAE,CAAC,CAAC;YACnC,GAAG;;gBACD,OAAO,MAAA,MAAA,IAAI,CAAC,UAAU,0CAAE,gBAAgB,CAAC,QAAQ,CAAC,mCAAI,EAAE,CAAC;YAC3D,CAAC;YACD,UAAU,EAAE,IAAI;YAChB,YAAY,EAAE,IAAI;SACnB,CAAC;KACH,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/*\n * IMPORTANT: For compatibility with <PERSON><PERSON><PERSON> and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\n\nimport {ReactiveElement} from '../reactive-element.js';\nimport {decorateProperty} from './base.js';\n\n/**\n * A property decorator that converts a class property into a getter\n * that executes a querySelectorAll on the element's renderRoot.\n *\n * @param selector A DOMString containing one or more selectors to match.\n *\n * See:\n * https://developer.mozilla.org/en-US/docs/Web/API/Document/querySelectorAll\n *\n * ```ts\n * class MyElement {\n *   @queryAll('div')\n *   divs: NodeListOf<HTMLDivElement>;\n *\n *   render() {\n *     return html`\n *       <div id=\"first\"></div>\n *       <div id=\"second\"></div>\n *     `;\n *   }\n * }\n * ```\n * @category Decorator\n */\nexport function queryAll(selector: string) {\n  return decorateProperty({\n    descriptor: (_name: PropertyKey) => ({\n      get(this: ReactiveElement) {\n        return this.renderRoot?.querySelectorAll(selector) ?? [];\n      },\n      enumerable: true,\n      configurable: true,\n    }),\n  });\n}\n"]}