!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("valtio/vanilla"),require("valtio/react")):"function"==typeof define&&define.amd?define(["exports","valtio/vanilla","valtio/react"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).valtio={},e.valtio<PERSON>,e.valtioReact)}(this,(function(e,t,n){"use strict";Object.keys(t).forEach((function(n){"default"===n||e.hasOwnProperty(n)||Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[n]}})})),Object.keys(n).forEach((function(t){"default"===t||e.hasOwnProperty(t)||Object.defineProperty(e,t,{enumerable:!0,get:function(){return n[t]}})}))}));
