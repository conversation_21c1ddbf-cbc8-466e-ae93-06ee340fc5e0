{"version": 3, "file": "browser.js", "sourceRoot": "", "sources": ["../../../src/lib/browser.ts"], "names": [], "mappings": ";;;;AAAA,wEAAkD;AAClD,4CAasB;AAGtB,SAAgB,OAAO,CAAC,IAAY;IAClC,OAAO,IAAI,KAAK,4BAAgB;QAC9B,CAAC,CAAC,EAAE,MAAM,EAAE,sBAAU,EAAE,IAAI,EAAE,4BAAgB,EAAE;QAChD,CAAC,CAAC;YACE,IAAI,EAAE,EAAE,IAAI,EAAE,6BAAiB,EAAE;YACjC,IAAI,EAAE,wBAAY;SACnB,CAAC;AACR,CAAC;AAPD,0BAOC;AAED,SAAgB,MAAM,CAAC,IAAY;IACjC,OAAO,IAAI,KAAK,4BAAgB,CAAC,CAAC,CAAC,CAAC,sBAAU,EAAE,sBAAU,CAAC,CAAC,CAAC,CAAC,CAAC,mBAAO,EAAE,qBAAS,CAAC,CAAC;AACrF,CAAC;AAFD,wBAEC;AAED,SAAsB,gBAAgB,CACpC,SAAoB,EACpB,QAAgB,4BAAgB;;QAEhC,MAAM,MAAM,GAAG,GAAG,CAAC,eAAe,EAAE,CAAC;QACrC,OAAO,IAAI,UAAU,CAAC,MAAM,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC;IAClE,CAAC;CAAA;AAND,4CAMC;AAED,SAAsB,gBAAgB,CACpC,MAAkB,EAClB,OAAe,4BAAgB;;QAE/B,OAAQ,GAAG,CAAC,eAAe,EAAU,CAAC,SAAS,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;IACpG,CAAC;CAAA;AALD,4CAKC;AAED,SAAsB,iBAAiB,CACrC,EAAc,EACd,GAAe,EACf,IAAgB;;QAEhB,MAAM,MAAM,GAAG,GAAG,CAAC,eAAe,EAAE,CAAC;QACrC,MAAM,SAAS,GAAG,MAAM,gBAAgB,CAAC,GAAG,EAAE,4BAAgB,CAAC,CAAC;QAChE,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,OAAO,CACjC;YACE,EAAE;YACF,IAAI,EAAE,4BAAgB;SACvB,EACD,SAAS,EACT,IAAI,CACL,CAAC;QACF,OAAO,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;IAChC,CAAC;CAAA;AAhBD,8CAgBC;AAED,SAAsB,iBAAiB,CACrC,EAAc,EACd,GAAe,EACf,IAAgB;;QAEhB,MAAM,MAAM,GAAG,GAAG,CAAC,eAAe,EAAE,CAAC;QACrC,MAAM,SAAS,GAAG,MAAM,gBAAgB,CAAC,GAAG,EAAE,4BAAgB,CAAC,CAAC;QAChE,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,OAAO,CACjC;YACE,EAAE;YACF,IAAI,EAAE,4BAAgB;SACvB,EACD,SAAS,EACT,IAAI,CACL,CAAC;QACF,OAAO,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;IAChC,CAAC;CAAA;AAhBD,8CAgBC;AAED,SAAsB,qBAAqB,CACzC,GAAe,EACf,IAAgB;;QAEhB,MAAM,MAAM,GAAG,GAAG,CAAC,eAAe,EAAE,CAAC;QACrC,MAAM,SAAS,GAAG,MAAM,gBAAgB,CAAC,GAAG,EAAE,wBAAY,CAAC,CAAC;QAC5D,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,IAAI,CACjC;YAEE,MAAM,EAAE,uBAAW;YACnB,IAAI,EAAE,wBAAY;SACnB,EACD,SAAS,EACT,IAAI,CACL,CAAC;QACF,OAAO,IAAI,UAAU,CAAC,SAAS,CAAC,CAAC;IACnC,CAAC;CAAA;AAhBD,sDAgBC;AAED,SAAsB,qBAAqB,CACzC,GAAe,EACf,IAAgB;;QAEhB,MAAM,MAAM,GAAG,GAAG,CAAC,eAAe,EAAE,CAAC;QACrC,MAAM,SAAS,GAAG,MAAM,gBAAgB,CAAC,GAAG,EAAE,wBAAY,CAAC,CAAC;QAC5D,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,IAAI,CACjC;YAEE,MAAM,EAAE,sBAAU;YAClB,IAAI,EAAE,wBAAY;SACnB,EACD,SAAS,EACT,IAAI,CACL,CAAC;QACF,OAAO,IAAI,UAAU,CAAC,SAAS,CAAC,CAAC;IACnC,CAAC;CAAA;AAhBD,sDAgBC;AAED,SAAsB,aAAa,CAAC,IAAgB;;QAClD,MAAM,MAAM,GAAG,GAAG,CAAC,eAAe,EAAE,CAAC;QACrC,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAChC;YACE,IAAI,EAAE,+BAAmB;SAC1B,EACD,IAAI,CACL,CAAC;QACF,OAAO,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;IAChC,CAAC;CAAA;AATD,sCASC;AAED,SAAsB,aAAa,CAAC,IAAgB;;QAClD,MAAM,MAAM,GAAG,GAAG,CAAC,eAAe,EAAE,CAAC;QACrC,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAChC;YACE,IAAI,EAAE,+BAAmB;SAC1B,EACD,IAAI,CACL,CAAC;QACF,OAAO,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;IAChC,CAAC;CAAA;AATD,sCASC"}