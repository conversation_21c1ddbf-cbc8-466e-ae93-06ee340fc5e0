const t=t=>t/1e3;const e=100,r=10,a=1;const s=({stiffness:s=e,damping:n=r,mass:c=a,from:h=0,to:o=1,velocity:M=0,restSpeed:u,restDistance:i}={})=>{M=M?t(M):0;const d={done:!1,hasReachedTarget:!1,current:h,target:o},f=o-h,p=Math.sqrt(s/c)/1e3,g=((t=e,s=r,n=a)=>s/(2*Math.sqrt(t*n)))(s,n,c),m=Math.abs(f)<5;let x;if(u||(u=m?.01:2),i||(i=m?.005:.5),g<1){const t=p*Math.sqrt(1-g*g);x=e=>o-Math.exp(-g*p*e)*((g*p*f-M)/t*Math.sin(t*e)+f*Math.cos(t*e))}else x=t=>o-Math.exp(-p*t)*(f+(p*f-M)*t);return t=>{d.current=x(t);const e=0===t?M:function(t,e,r){const a=Math.max(e-5,0);return s=r-t(a),(n=e-a)?s*(1e3/n):0;var s,n}(x,t,d.current),r=Math.abs(e)<=u,a=Math.abs(o-d.current)<=i;var s,n,c;return d.done=r&&a,d.hasReachedTarget=(s=h,n=o,c=d.current,s<n&&c>=n||s>n&&c<=n),d}};export{s as spring};
