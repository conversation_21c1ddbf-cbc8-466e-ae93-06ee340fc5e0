// Generated by CoffeeScript 1.12.7
(function() {
  var JsonpReceiver, transport,
    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },
    hasProp = {}.hasOwnProperty;

  transport = require('./transport');

  JsonpReceiver = (function(superClass) {
    extend(JsonpReceiver, superClass);

    JsonpReceiver.prototype.protocol = "jsonp-polling";

    JsonpReceiver.prototype.max_response_size = 1;

    function JsonpReceiver(req, res, options, callback1) {
      this.callback = callback1;
      JsonpReceiver.__super__.constructor.call(this, req, res, options);
    }

    JsonpReceiver.prototype.doSendFrame = function(payload) {
      return JsonpReceiver.__super__.doSendFrame.call(this, "/**/" + this.callback + "(" + JSON.stringify(payload) + ");\r\n");
    };

    return JsonpReceiver;

  })(transport.ResponseReceiver);

  exports.app = {
    jsonp: function(req, res, _, next_filter) {
      var callback;
      if (!('c' in req.query || 'callback' in req.query)) {
        throw {
          status: 500,
          message: '"callback" parameter required'
        };
      }
      callback = 'c' in req.query ? req.query['c'] : req.query['callback'];
      if (/[^a-zA-Z0-9-_.]/.test(callback) || callback.length > 32) {
        throw {
          status: 500,
          message: 'invalid "callback" parameter'
        };
      }
      res.setHeader('X-Content-Type-Options', 'nosniff');
      res.setHeader('Content-Type', 'application/javascript; charset=UTF-8');
      res.writeHead(200);
      transport.register(req, this, new JsonpReceiver(req, res, this.options, callback));
      return true;
    },
    jsonp_send: function(req, res, query) {
      var d, i, jsonp, len, message, x;
      if (!query) {
        throw {
          status: 500,
          message: 'Payload expected.'
        };
      }
      if (typeof query === 'string') {
        try {
          d = JSON.parse(query);
        } catch (error) {
          x = error;
          throw {
            status: 500,
            message: 'Broken JSON encoding.'
          };
        }
      } else {
        d = query.d;
      }
      if (typeof d === 'string' && d) {
        try {
          d = JSON.parse(d);
        } catch (error) {
          x = error;
          throw {
            status: 500,
            message: 'Broken JSON encoding.'
          };
        }
      }
      if (!d || d.__proto__.constructor !== Array) {
        throw {
          status: 500,
          message: 'Payload expected.'
        };
      }
      jsonp = transport.Session.bySessionId(req.session);
      if (jsonp === null) {
        throw {
          status: 404
        };
      }
      for (i = 0, len = d.length; i < len; i++) {
        message = d[i];
        jsonp.didMessage(message);
      }
      res.setHeader('Content-Length', '2');
      res.setHeader('Content-Type', 'text/plain; charset=UTF-8');
      res.writeHead(200);
      res.end('ok');
      return true;
    }
  };

}).call(this);
