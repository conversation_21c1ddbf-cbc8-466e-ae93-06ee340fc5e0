"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.safeJsonStringify = exports.safeJsonParse = void 0;
function safeJsonParse(value) {
    if (typeof value !== "string") {
        throw new Error(`Cannot safe json parse value of type ${typeof value}`);
    }
    try {
        return JSON.parse(value);
    }
    catch (_a) {
        return value;
    }
}
exports.safeJsonParse = safeJsonParse;
function safeJsonStringify(value) {
    return typeof value === "string" ? value : JSON.stringify(value);
}
exports.safeJsonStringify = safeJsonStringify;
//# sourceMappingURL=index.js.map