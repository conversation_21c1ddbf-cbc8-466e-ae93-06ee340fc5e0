{"name": "pngjs", "version": "3.4.0", "description": "PNG encoder/decoder in pure JS, supporting any bit size & interlace, async & sync with full test suite.", "contributors": ["<PERSON>", "Gaurav Mali", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "lian<PERSON><PERSON>g", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "toriningen", "<PERSON>"], "homepage": "https://github.com/lukeapage/pngjs", "keywords": ["PNG", "decoder", "encoder", "js-png", "node-png", "parser", "png", "png-js", "png-parse", "pngjs"], "engines": {"node": ">=4.0.0"}, "main": "./lib/png.js", "directories": {"lib": "lib", "example": "examples", "test": "test"}, "scripts": {"build": "npm run prepublish", "prepublish": "npm run browserify", "browserify": "browserify lib/png.js --standalone png > browser.js", "coverage": "istanbul -- cover node_modules/tape/bin/tape test/*-spec.js nolarge", "coverage-report": "npm run coverage && istanbul report html", "coveralls": "cat ./coverage/lcov.info | coveralls", "test": "npm run lint && tape test/*-spec.js | tap-dot && node test/run-compare", "lint": "eslint lib"}, "repository": {"type": "git", "url": "git://github.com/lukeapage/pngjs2.git"}, "license": "MIT", "bugs": {"url": "https://github.com/lukeapage/pngjs2/issues"}, "devDependencies": {"browserify": "^14.5.0", "buffer-equal": "1.0.0", "connect": "^3.4.0", "eslint": "^4.1.0", "istanbul": "^0.4.4", "phantomjs-prebuilt": "^2.1.7", "serve-static": "^1.10.0", "tap-dot": "^2.0.0", "tape": "^4.0.2"}}