import React, { useState, useEffect } from 'react';
import Web3 from 'web3';

const WalletConnector = ({ onConnect, onError }) => {
    const [isConnecting, setIsConnecting] = useState(false);
    const [qrCodeUrl, setQrCodeUrl] = useState('');
    const [showQrCode, setShowQrCode] = useState(false);
    const [connectionMethod, setConnectionMethod] = useState(''); // 'metamask', 'walletconnect', 'sms', 'guest'
    const [phoneNumber, setPhoneNumber] = useState('');
    const [verificationCode, setVerificationCode] = useState('');
    const [step, setStep] = useState(1); // For SMS auth: 1 = enter phone, 2 = enter code

    // Check if MetaMask is installed
    const isMetaMaskInstalled = () => {
        return window.ethereum && window.ethereum.isMetaMask;
    };

    // Check if on mobile device
    const isMobileDevice = () => {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    };

    // Connect with MetaMask
    const connectWithMetaMask = async () => {
        setConnectionMethod('metamask');
        setIsConnecting(true);

        try {
            if (!window.ethereum) {
                throw new Error('MetaMask is not installed');
            }

            const web3Instance = new Web3(window.ethereum);

            // Request account access
            const accounts = await window.ethereum.request({
                method: 'eth_requestAccounts',
                params: []
            });

            if (accounts && accounts.length > 0) {
                onConnect(web3Instance, accounts[0], 'metamask');
            } else {
                throw new Error('No accounts found. Please make sure MetaMask is unlocked.');
            }
        } catch (error) {
            console.error('MetaMask connection error:', error);
            onError(`MetaMask connection error: ${error.message || 'Unknown error'}`);
        } finally {
            setIsConnecting(false);
        }
    };

    // Connect with Mobile App (simulated for now)
    const connectWithMobileApp = async () => {
        setConnectionMethod('mobile');
        setIsConnecting(true);

        try {
            // In a real implementation, this would open a mobile app or show a QR code
            // For now, we'll just simulate a successful connection
            console.log("Connecting with mobile app...");

            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 1500));

            // Create a read-only web3 instance
            const web3Instance = new Web3(new Web3.providers.HttpProvider('https://mainnet.infura.io/v3/********************************'));

            // Generate a demo address
            const demoAddress = '0x' + Math.random().toString(16).substring(2, 42);

            onConnect(web3Instance, demoAddress, 'mobile');
        } catch (error) {
            console.error('Mobile app connection error:', error);
            onError(`Mobile app connection error: ${error.message || 'Unknown error'}`);
        } finally {
            setIsConnecting(false);
        }
    };

    // Connect with SMS (simulated for now)
    const startSmsAuth = async () => {
        setConnectionMethod('sms');
        setStep(1);
    };

    const sendVerificationCode = async () => {
        setIsConnecting(true);

        try {
            // In a real implementation, this would call your backend API
            // to send an SMS with a verification code
            console.log(`Sending verification code to ${phoneNumber}`);

            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 1500));

            // Move to next step
            setStep(2);
        } catch (error) {
            console.error('SMS verification error:', error);
            onError(`SMS verification error: ${error.message || 'Unknown error'}`);
        } finally {
            setIsConnecting(false);
        }
    };

    const verifyCode = async () => {
        setIsConnecting(true);

        try {
            // In a real implementation, this would verify the code with your backend
            console.log(`Verifying code: ${verificationCode}`);

            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 1500));

            // Generate a deterministic address based on phone number
            // This is just for demo purposes - in production you'd use a more secure method
            const demoAddress = `0x${Array.from(phoneNumber).reduce((a, b) => a + b.charCodeAt(0), 0).toString(16).padStart(40, '0')}`;

            // Create a read-only web3 instance
            const web3Instance = new Web3(new Web3.providers.HttpProvider('https://mainnet.infura.io/v3/********************************'));

            onConnect(web3Instance, demoAddress, 'sms');
        } catch (error) {
            console.error('Code verification error:', error);
            onError(`Code verification error: ${error.message || 'Unknown error'}`);
        } finally {
            setIsConnecting(false);
        }
    };

    // Connect as guest (demo mode)
    const connectAsGuest = () => {
        setConnectionMethod('guest');
        setIsConnecting(true);

        try {
            // Create a read-only web3 instance
            const web3Instance = new Web3(new Web3.providers.HttpProvider('https://mainnet.infura.io/v3/********************************'));

            // Use a demo address
            const demoAddress = '******************************************';

            onConnect(web3Instance, demoAddress, 'guest');
        } catch (error) {
            console.error('Guest mode error:', error);
            onError(`Guest mode error: ${error.message || 'Unknown error'}`);
        } finally {
            setIsConnecting(false);
        }
    };

    // Render the appropriate connection UI based on the selected method
    const renderConnectionUI = () => {
        if (!connectionMethod) {
            return (
                <div className="connection-options">
                    <h4 className="mb-3">Choose a Connection Method</h4>

                    <div className="row g-3">
                        {isMetaMaskInstalled() && (
                            <div className="col-md-6 mb-3">
                                <div className="card h-100">
                                    <div className="card-body d-flex flex-column">
                                        <h5 className="card-title">MetaMask</h5>
                                        <p className="card-text">Connect using the MetaMask browser extension or mobile app.</p>
                                        <button
                                            className="btn btn-primary mt-auto"
                                            onClick={connectWithMetaMask}
                                            disabled={isConnecting}
                                        >
                                            <img
                                                src="https://upload.wikimedia.org/wikipedia/commons/thumb/3/36/MetaMask_Fox.svg/64px-MetaMask_Fox.svg.png"
                                                alt="MetaMask"
                                                className="me-2"
                                                style={{ height: '20px' }}
                                            />
                                            Connect with MetaMask
                                        </button>
                                    </div>
                                </div>
                            </div>
                        )}

                        <div className="col-md-6 mb-3">
                            <div className="card h-100">
                                <div className="card-body d-flex flex-column">
                                    <h5 className="card-title">Mobile App</h5>
                                    <p className="card-text">Connect using the official Kenyan Voting mobile app.</p>
                                    <button
                                        className="btn btn-primary mt-auto"
                                        onClick={connectWithMobileApp}
                                        disabled={isConnecting}
                                    >
                                        <i className="bi bi-phone-fill me-2"></i>
                                        Connect with Mobile App
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div className="col-md-6 mb-3">
                            <div className="card h-100">
                                <div className="card-body d-flex flex-column">
                                    <h5 className="card-title">SMS Verification</h5>
                                    <p className="card-text">Use your phone number to verify your identity.</p>
                                    <button
                                        className="btn btn-primary mt-auto"
                                        onClick={startSmsAuth}
                                        disabled={isConnecting}
                                    >
                                        <i className="bi bi-phone me-2"></i>
                                        Connect with Phone
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div className="col-md-6 mb-3">
                            <div className="card h-100">
                                <div className="card-body d-flex flex-column">
                                    <h5 className="card-title">Guest Mode</h5>
                                    <p className="card-text">Explore the platform without connecting a wallet.</p>
                                    <button
                                        className="btn btn-secondary mt-auto"
                                        onClick={connectAsGuest}
                                        disabled={isConnecting}
                                    >
                                        <i className="bi bi-person me-2"></i>
                                        Continue as Guest
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            );
        }

        // Show connection-specific UI
        switch (connectionMethod) {
            case 'metamask':
                return (
                    <div className="text-center">
                        <div className="spinner-border text-primary mb-3" role="status">
                            <span className="visually-hidden">Loading...</span>
                        </div>
                        <p>Connecting to MetaMask...</p>
                        <p className="text-muted small">Please check your MetaMask extension for connection requests.</p>
                    </div>
                );

            case 'mobile':
                return (
                    <div className="text-center">
                        <div className="spinner-border text-primary mb-3" role="status">
                            <span className="visually-hidden">Loading...</span>
                        </div>
                        <p>Connecting to mobile app...</p>
                        <p className="text-muted small">In a real implementation, this would show a QR code or deep link to the mobile app.</p>
                    </div>
                );

            case 'sms':
                return (
                    <div>
                        <h4 className="mb-3">Phone Verification</h4>

                        {step === 1 ? (
                            <div>
                                <div className="mb-3">
                                    <label htmlFor="phoneNumber" className="form-label">Enter your phone number</label>
                                    <input
                                        type="tel"
                                        className="form-control"
                                        id="phoneNumber"
                                        value={phoneNumber}
                                        onChange={(e) => setPhoneNumber(e.target.value)}
                                        placeholder="+254 7XX XXX XXX"
                                    />
                                    <div className="form-text">We'll send a verification code to this number.</div>
                                </div>

                                <button
                                    className="btn btn-primary"
                                    onClick={sendVerificationCode}
                                    disabled={isConnecting || !phoneNumber}
                                >
                                    {isConnecting ? (
                                        <>
                                            <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                            Sending...
                                        </>
                                    ) : (
                                        'Send Verification Code'
                                    )}
                                </button>
                            </div>
                        ) : (
                            <div>
                                <div className="mb-3">
                                    <label htmlFor="verificationCode" className="form-label">Enter verification code</label>
                                    <input
                                        type="text"
                                        className="form-control"
                                        id="verificationCode"
                                        value={verificationCode}
                                        onChange={(e) => setVerificationCode(e.target.value)}
                                        placeholder="123456"
                                    />
                                    <div className="form-text">Enter the 6-digit code sent to {phoneNumber}</div>
                                </div>

                                <div className="d-flex gap-2">
                                    <button
                                        className="btn btn-outline-secondary"
                                        onClick={() => setStep(1)}
                                        disabled={isConnecting}
                                    >
                                        Back
                                    </button>

                                    <button
                                        className="btn btn-primary"
                                        onClick={verifyCode}
                                        disabled={isConnecting || verificationCode.length < 4}
                                    >
                                        {isConnecting ? (
                                            <>
                                                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                                Verifying...
                                            </>
                                        ) : (
                                            'Verify Code'
                                        )}
                                    </button>
                                </div>
                            </div>
                        )}
                    </div>
                );

            case 'guest':
                return (
                    <div className="text-center">
                        <div className="spinner-border text-primary mb-3" role="status">
                            <span className="visually-hidden">Loading...</span>
                        </div>
                        <p>Setting up guest mode...</p>
                    </div>
                );

            default:
                return null;
        }
    };

    return (
        <div className="wallet-connector">
            {renderConnectionUI()}

            {connectionMethod && (
                <button
                    className="btn btn-link mt-3"
                    onClick={() => {
                        setConnectionMethod('');
                        setShowQrCode(false);
                        setQrCodeUrl('');
                    }}
                    disabled={isConnecting}
                >
                    <i className="bi bi-arrow-left me-1"></i>
                    Back to connection options
                </button>
            )}
        </div>
    );
};

export default WalletConnector;
