{"name": "ethereumjs-util", "version": "5.2.1", "description": "a collection of utility functions for Ethereum", "main": "dist/index.js", "files": ["dist"], "scripts": {"coverage": "npm run build:dist && istanbul cover _mocha", "coveralls": "npm run coverage && coveralls <coverage/lcov.info", "lint": "standard", "prepublishOnly": "npm run lint && npm run build:dist && npm run test", "test": "npm run test:node && npm run test:browser", "test:browser": "npm run build:dist && karma start karma.conf.js", "test:node": "npm run build:dist && istanbul test mocha -- --reporter spec", "build:dist": "babel index.js secp256k1-adapter.js secp256k1-lib/* --source-root ./ -d ./dist", "build:docs": "documentation build ./index.js --github --sort-order='alpha' -f md > ./docs/index.md"}, "repository": {"type": "git", "url": "https://github.com/ethereumjs/ethereumjs-util.git"}, "keywords": ["ethereum", "utilties"], "author": "mjbecze <<EMAIL>>", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tcoulter", "contributions": 1, "additions": 2, "deletions": 2}, {"name": "<PERSON>", "url": "https://github.com/SilentCicero", "contributions": 2, "additions": 26, "deletions": 2}, {"name": "Mr. <PERSON>", "url": "https://github.com/MrChico", "contributions": 1, "additions": 11, "deletions": 1}, {"name": "Dũng Trần", "email": "<EMAIL>", "url": "https://github.com/tad88dev", "contributions": 2, "additions": 5, "deletions": 5}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/axic", "contributions": 77, "additions": 1796, "deletions": 642}, {"name": "<PERSON>", "url": "https://github.com/tgerring", "contributions": 1, "additions": 1, "deletions": 1}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/fanatid", "contributions": 8, "additions": 32, "deletions": 16}, {"name": "kuma<PERSON>", "email": "<EMAIL>", "url": "https://github.com/kumavis", "contributions": 2, "additions": 2, "deletions": 2}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/asinyagin", "contributions": 1, "additions": 3, "deletions": 1}], "license": "MPL-2.0", "bugs": {"url": "https://github.com/ethereumjs/ethereumjs-util/issues"}, "homepage": "https://github.com/ethereumjs/ethereumjs-util", "dependencies": {"bn.js": "^4.11.0", "create-hash": "^1.1.2", "ethereum-cryptography": "^0.1.3", "elliptic": "^6.5.2", "ethjs-util": "^0.1.3", "rlp": "^2.0.0", "safe-buffer": "^5.1.1"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-preset-env": "^1.6.1", "babelify": "^8.0.0", "browserify": "^14.0.0", "contributor": "^0.1.25", "coveralls": "^3.0.0", "documentation": "^5.2.0", "istanbul": "^0.4.1", "karma": "^2.0.0", "karma-browserify": "^5.0.0", "karma-chrome-launcher": "^2.0.0", "karma-detect-browsers": "2.2.6", "karma-env-preprocessor": "^0.1.1", "karma-firefox-launcher": "^1.0.0", "karma-mocha": "^1.3.0", "mocha": "^4.0.0", "standard": "^11.0.1"}, "standard": {"globals": ["describe", "it"], "ignore": ["dist/**"]}}