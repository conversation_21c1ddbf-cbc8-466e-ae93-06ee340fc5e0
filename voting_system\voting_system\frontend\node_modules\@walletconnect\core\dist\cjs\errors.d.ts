export declare const ERROR_SESSION_CONNECTED = "Session currently connected";
export declare const ERROR_SESSION_DISCONNECTED = "Session currently disconnected";
export declare const ERROR_SESSION_REJECTED = "Session Rejected";
export declare const ERROR_MISSING_JSON_RPC = "Missing JSON RPC response";
export declare const ERROR_MISSING_RESULT = "JSON-RPC success response must include \"result\" field";
export declare const ERROR_MISSING_ERROR = "JSON-RPC error response must include \"error\" field";
export declare const ERROR_MISSING_METHOD = "JSON RPC request must have valid \"method\" value";
export declare const ERROR_MISSING_ID = "JSON RPC request must have valid \"id\" value";
export declare const ERROR_MISSING_REQUIRED = "Missing one of the required parameters: bridge / uri / session";
export declare const ERROR_INVALID_RESPONSE = "JSON RPC response format is invalid";
export declare const ERROR_INVALID_URI = "URI format is invalid";
export declare const ERROR_QRCODE_MODAL_NOT_PROVIDED = "QRCode Modal not provided";
export declare const ERROR_QRCODE_MODAL_USER_CLOSED = "User close QRCode Modal";
//# sourceMappingURL=errors.d.ts.map