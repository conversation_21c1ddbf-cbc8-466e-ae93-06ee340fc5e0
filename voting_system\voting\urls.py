from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

# Create a router for our API views
router = DefaultRouter()
router.register(r'counties', views.CountyViewSet)
router.register(r'constituencies', views.ConstituencyViewSet)
router.register(r'polling-stations', views.PollingStationViewSet)
router.register(r'parties', views.PartyViewSet)
router.register(r'election-types', views.ElectionTypeViewSet)
router.register(r'elections', views.ElectionViewSet)
router.register(r'voters', views.VoterViewSet)
router.register(r'candidates', views.CandidateViewSet)
router.register(r'votes', views.VoteViewSet)
router.register(r'workers', views.WorkerViewSet)

urlpatterns = [
    # Web views - for now, just use the index view for the frontend
    path('', views.index, name='index'),

    # API endpoints
    path('api/', include(router.urls)),
    path('api/contract-info/', views.contract_info, name='contract-info'),

    # API endpoints for elections
    path('api/elections/<int:election_id>/candidates/', views.election_candidates_api, name='election_candidates_api'),
    path('api/elections/<int:election_id>/vote/<int:candidate_id>/', views.cast_vote_api, name='cast_vote_api'),

    # API endpoints for voters
    path('api/voters/me/', views.voter_me_api, name='voter_me_api'),
    path('api/counties/<int:county_id>/constituencies/', views.county_constituencies_api, name='county_constituencies_api'),
    path('api/constituencies/<int:constituency_id>/polling-stations/', views.constituency_polling_stations_api, name='constituency_polling_stations_api'),
    path('api/check-id-number/', views.check_id_number_api, name='check_id_number_api'),
    path('api/elections/<int:election_id>/voter-status/', views.check_voter_status_api, name='check_voter_status_api'),

    # API endpoints for statistics
    path('api/statistics/', views.get_statistics, name='get_statistics'),
    path('api/advanced-analytics/', views.get_advanced_analytics, name='get_advanced_analytics'),

    # Authentication endpoints
    path('api/auth/login/', views.login_view, name='login_view'),
    path('api/auth/register/', views.register_user, name='register_user'),

    # Worker endpoints
    path('api/worker/login/', views.worker_login, name='worker_login'),
    path('api/worker/register-voter/', views.worker_register_voter, name='worker_register_voter'),
]
