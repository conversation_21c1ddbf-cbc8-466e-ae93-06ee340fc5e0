import React, { useState } from 'react';

const ProposalDetail = ({ proposal, onBack, onVote, account }) => {
    const [isVoting, setIsVoting] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');

    // Check if the user has already voted
    const [hasVoted, setHasVoted] = useState(false);

    // Handle vote button click
    const handleVote = async () => {
        if (!account) {
            setError('Please connect your wallet to vote');
            return;
        }

        if (hasVoted) {
            setError('You have already voted on this proposal');
            return;
        }

        try {
            setIsVoting(true);
            setError('');
            
            await onVote(proposal.contract_proposal_id);
            
            setSuccess('Your vote has been recorded successfully!');
            setHasVoted(true);
        } catch (error) {
            console.error('Error voting:', error);
            setError('Error submitting vote. Please try again.');
        } finally {
            setIsVoting(false);
        }
    };

    return (
        <div>
            <button className="btn btn-outline-secondary mb-4" onClick={onBack}>
                &larr; Back to Proposals
            </button>

            <div className="row">
                <div className="col-md-8">
                    <h2>{proposal.title}</h2>
                    <p className="lead">{proposal.description}</p>

                    <div className="card mb-4">
                        <div className="card-header">
                            Proposal Details
                        </div>
                        <div className="card-body">
                            <p><strong>Created:</strong> {new Date(proposal.created_at).toLocaleString()}</p>
                            <p><strong>Voting Ends:</strong> {new Date(proposal.end_time).toLocaleString()}</p>
                            <p><strong>Current Votes:</strong> {proposal.vote_count}</p>
                            <p>
                                <strong>Status:</strong>{' '}
                                <span className={`proposal-status ${proposal.is_active ? 'status-active' : 'status-closed'}`}>
                                    {proposal.is_active ? 'Active' : 'Closed'}
                                </span>
                            </p>
                            
                            {proposal.contract_proposal_id && (
                                <p><strong>Blockchain ID:</strong> {proposal.contract_proposal_id}</p>
                            )}
                        </div>
                    </div>

                    {error && (
                        <div className="alert alert-danger" role="alert">
                            {error}
                        </div>
                    )}

                    {success && (
                        <div className="alert alert-success" role="alert">
                            {success}
                        </div>
                    )}

                    {proposal.is_active && !hasVoted && (
                        <button
                            className="btn btn-primary vote-button"
                            onClick={handleVote}
                            disabled={isVoting || !account}
                        >
                            {isVoting ? (
                                <>
                                    <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                    Voting...
                                </>
                            ) : (
                                'Vote for this Proposal'
                            )}
                        </button>
                    )}

                    {proposal.is_active && hasVoted && (
                        <div className="alert alert-success">
                            You have already voted for this proposal.
                        </div>
                    )}

                    {!proposal.is_active && (
                        <div className="alert alert-info">
                            This proposal is no longer active.
                        </div>
                    )}
                </div>

                <div className="col-md-4">
                    <div className="card">
                        <div className="card-header">
                            Blockchain Information
                        </div>
                        <div className="card-body">
                            <p>This proposal is stored on the Ethereum blockchain, ensuring transparency and immutability of the voting process.</p>
                            <p>Each vote is recorded on the blockchain, making it impossible to tamper with the results.</p>
                            
                            {account ? (
                                <div className="alert alert-success">
                                    <small>Connected with account:</small>
                                    <div className="text-break">
                                        {account}
                                    </div>
                                </div>
                            ) : (
                                <div className="alert alert-warning">
                                    Please connect your Ethereum wallet to vote.
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ProposalDetail;
