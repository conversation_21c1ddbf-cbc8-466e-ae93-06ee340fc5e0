{"name": "frontend", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "webpack serve --mode development", "build": "webpack --mode production"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@babel/core": "^7.27.1", "@babel/preset-env": "^7.27.1", "@babel/preset-react": "^7.27.1", "@walletconnect/web3-provider": "^1.8.0", "@web3modal/standalone": "^2.4.3", "assert": "^2.1.0", "babel-loader": "^10.0.0", "buffer": "^6.0.3", "chart.js": "^4.4.9", "css-loader": "^7.1.2", "frontend": "file:", "html-webpack-plugin": "^5.6.3", "https-browserify": "^1.0.0", "os-browserify": "^0.3.0", "process": "^0.11.10", "qrcode.react": "^4.2.0", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "react-router-dom": "^7.5.3", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "style-loader": "^4.0.0", "url": "^0.11.4", "web3": "^4.16.0", "webpack": "^5.99.7", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.1"}}