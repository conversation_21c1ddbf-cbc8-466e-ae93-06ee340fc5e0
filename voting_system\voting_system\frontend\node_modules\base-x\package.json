{"name": "base-x", "version": "3.0.11", "description": "Fast base encoding / decoding of any given alphabet", "keywords": ["base-x", "base58", "base62", "base64", "crypto", "crytography", "decode", "decoding", "encode", "encoding"], "homepage": "https://github.com/cryptocoinjs/base-x", "bugs": {"url": "https://github.com/cryptocoinjs/base-x/issues"}, "license": "MIT", "author": "<PERSON>", "files": ["src"], "main": "src/index.js", "types": "src/index.d.ts", "repository": {"type": "git", "url": "https://github.com/cryptocoinjs/base-x.git"}, "scripts": {"build": "tsc -p ./tsconfig.json ; standard --fix", "gitdiff": "npm run build && git diff --exit-code", "prepublish": "npm run gitdiff", "standard": "standard", "test": "npm run unit && npm run standard", "unit": "tape test/*.js"}, "devDependencies": {"@types/node": "12.0.10", "standard": "^10.0.3", "tape": "^4.5.1", "typescript": "3.5.2"}, "dependencies": {"safe-buffer": "^5.0.1"}}