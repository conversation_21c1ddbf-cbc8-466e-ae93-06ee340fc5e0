<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

# blockFromRpc

Creates a new block object from Ethereum JSON RPC.

**Parameters**

-   `blockParams` **[Object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** Ethereum JSON RPC of block (eth_getBlockByNumber)
-   `Optional` **[Array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array)&lt;[Object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)>** list of Ethereum JSON RPC of uncles (eth_getUncleByBlockHashAndIndex)
-   `uncles`  
