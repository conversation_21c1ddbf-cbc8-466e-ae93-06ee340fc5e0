{"version": 3, "file": "registry.js", "sourceRoot": "", "sources": ["../../src/registry.ts"], "names": [], "mappings": ";;;AAEA,MAAM,OAAO,GAAG,oCAAoC,CAAC;AAErD,SAAgB,oBAAoB;IAClC,OAAO,OAAO,GAAG,iBAAiB,CAAC;AACrC,CAAC;AAFD,oDAEC;AAED,SAAgB,kBAAkB;IAChC,OAAO,OAAO,GAAG,eAAe,CAAC;AACnC,CAAC;AAFD,gDAEC;AAED,SAAgB,yBAAyB,CAAC,KAAgB,EAAE,WAAiC,QAAQ;;IACnG,OAAO;QACL,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,EAAE;QACtB,SAAS,EAAE,KAAK,CAAC,QAAQ,CAAC,SAAS,IAAI,EAAE;QACzC,KAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,IAAI,EAAE;QAC1C,IAAI,EAAE,MAAA,KAAK,CAAC,SAAS,CAAC,EAAE,mCAAI,EAAE;QAC9B,aAAa,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC,SAAS,IAAI,EAAE;QAC9C,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC,MAAM,IAAI,EAAE;KACvC,CAAC;AACJ,CAAC;AATD,8DASC;AAED,SAAgB,oBAAoB,CAAC,QAAsB,EAAE,WAAiC,QAAQ;IACpG,OAAO,MAAM,CAAC,MAAM,CAAM,QAAQ,CAAC;SAChC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC;SACxE,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,yBAAyB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC;AAChE,CAAC;AAJD,oDAIC"}