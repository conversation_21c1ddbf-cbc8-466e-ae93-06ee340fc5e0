# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON>, 2011-2012
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-10-09 17:42+0200\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: <PERSON><PERSON> <jann<PERSON>@leidel.info>\n"
"Language-Team: Spanish (Mexico) (http://www.transifex.com/django/django/"
"language/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: es_MX\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Advanced options"
msgstr "Opciones avanzadas"

msgid "Flat Pages"
msgstr ""

msgid "URL"
msgstr "URL"

msgid ""
"Example: '/about/contact/'. Make sure to have leading and trailing slashes."
msgstr ""
"Ejemplo: '/acerca/contacto/'. Asegúrese de usar barras '/' al principio y al "
"final."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"Este valor debe contener solamente letras, números, puntos, guiones bajos, "
"guiones (-), barras (/) o tildes."

msgid "URL is missing a leading slash."
msgstr "A la URL le falta una diagonal al inicio"

msgid "URL is missing a trailing slash."
msgstr "A la URL le falta una diagonal al final"

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr "La página con la url %(url)s ya existe para el sitio %(site)s"

msgid "title"
msgstr "título"

msgid "content"
msgstr "contenido"

msgid "enable comments"
msgstr "activar comentarios"

msgid "template name"
msgstr "nombre de la plantilla"

msgid ""
"Example: 'flatpages/contact_page.html'. If this isn't provided, the system "
"will use 'flatpages/default.html'."
msgstr ""
"Ejemplo: 'flatpages/pagina_contacto.html'. Si no lo proporciona, el sistema "
"usará 'flatpages/default.html'."

msgid "registration required"
msgstr "necesario registrarse"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr "Si está marcado, sólo los usuarios registrados podrán ver la página."

msgid "sites"
msgstr ""

msgid "flat page"
msgstr "página estática"

msgid "flat pages"
msgstr "páginas estática"
