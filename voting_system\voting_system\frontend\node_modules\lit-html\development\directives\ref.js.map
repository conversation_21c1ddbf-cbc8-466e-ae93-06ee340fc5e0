{"version": 3, "file": "ref.js", "sourceRoot": "", "sources": ["../../src/directives/ref.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AACH,OAAO,EAAC,OAAO,EAAc,MAAM,gBAAgB,CAAC;AACpD,OAAO,EAAC,SAAS,EAAE,cAAc,EAAC,MAAM,uBAAuB,CAAC;AAEhE;;GAEG;AACH,MAAM,CAAC,MAAM,SAAS,GAAG,GAAgB,EAAE,CAAC,IAAI,GAAG,EAAK,CAAC;AAEzD;;GAEG;AACH,MAAM,GAAG;CAMR;AAQD,gFAAgF;AAChF,6EAA6E;AAC7E,0EAA0E;AAC1E,8EAA8E;AAC9E,qBAAqB;AACrB,MAAM,gCAAgC,GAGlC,IAAI,OAAO,EAAE,CAAC;AAIlB,MAAM,YAAa,SAAQ,cAAc;IAKvC,MAAM,CAAC,IAAoB;QACzB,OAAO,OAAO,CAAC;IACjB,CAAC;IAEQ,MAAM,CAAC,IAAiB,EAAE,CAAC,GAAG,CAA6B;;QAClE,MAAM,UAAU,GAAG,GAAG,KAAK,IAAI,CAAC,IAAI,CAAC;QACrC,IAAI,UAAU,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE;YACzC,+CAA+C;YAC/C,iCAAiC;YACjC,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;SACjC;QACD,IAAI,UAAU,IAAI,IAAI,CAAC,kBAAkB,KAAK,IAAI,CAAC,QAAQ,EAAE;YAC3D,uDAAuD;YACvD,+CAA+C;YAC/C,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;YAChB,IAAI,CAAC,QAAQ,GAAG,MAAA,IAAI,CAAC,OAAO,0CAAE,IAAI,CAAC;YACnC,IAAI,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;SACtD;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,eAAe,CAAC,OAA4B;;QAClD,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,UAAU,EAAE;YACnC,iEAAiE;YACjE,yEAAyE;YACzE,qEAAqE;YACrE,wEAAwE;YACxE,wEAAwE;YACxE,0DAA0D;YAC1D,oEAAoE;YACpE,kEAAkE;YAClE,6CAA6C;YAC7C,MAAM,OAAO,GAAG,MAAA,IAAI,CAAC,QAAQ,mCAAI,UAAU,CAAC;YAC5C,IAAI,sBAAsB,GACxB,gCAAgC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAChD,IAAI,sBAAsB,KAAK,SAAS,EAAE;gBACxC,sBAAsB,GAAG,IAAI,OAAO,EAAE,CAAC;gBACvC,gCAAgC,CAAC,GAAG,CAAC,OAAO,EAAE,sBAAsB,CAAC,CAAC;aACvE;YACD,IAAI,sBAAsB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,SAAS,EAAE;gBACvD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;aAC1C;YACD,sBAAsB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAC/C,0CAA0C;YAC1C,IAAI,OAAO,KAAK,SAAS,EAAE;gBACzB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;aACxC;SACF;aAAM;YACJ,IAAI,CAAC,IAAqB,CAAC,KAAK,GAAG,OAAO,CAAC;SAC7C;IACH,CAAC;IAED,IAAY,kBAAkB;;QAC5B,OAAO,OAAO,IAAI,CAAC,IAAI,KAAK,UAAU;YACpC,CAAC,CAAC,MAAA,gCAAgC;iBAC7B,GAAG,CAAC,MAAA,IAAI,CAAC,QAAQ,mCAAI,UAAU,CAAC,0CAC/B,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;YACpB,CAAC,CAAC,MAAA,IAAI,CAAC,IAAI,0CAAE,KAAK,CAAC;IACvB,CAAC;IAEQ,YAAY;QACnB,yEAAyE;QACzE,wEAAwE;QACxE,2EAA2E;QAC3E,iBAAiB;QACjB,IAAI,IAAI,CAAC,kBAAkB,KAAK,IAAI,CAAC,QAAQ,EAAE;YAC7C,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;SACjC;IACH,CAAC;IAEQ,WAAW;QAClB,0EAA0E;QAC1E,sEAAsE;QACtE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACtC,CAAC;CACF;AAED;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACH,MAAM,CAAC,MAAM,GAAG,GAAG,SAAS,CAAC,YAAY,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nimport {nothing, ElementPart} from '../lit-html.js';\nimport {directive, AsyncDirective} from '../async-directive.js';\n\n/**\n * Creates a new Ref object, which is container for a reference to an element.\n */\nexport const createRef = <T = Element>() => new Ref<T>();\n\n/**\n * An object that holds a ref value.\n */\nclass Ref<T = Element> {\n  /**\n   * The current Element value of the ref, or else `undefined` if the ref is no\n   * longer rendered.\n   */\n  readonly value?: T;\n}\n\nexport type {Ref};\n\ninterface RefInternal {\n  value: Element | undefined;\n}\n\n// When callbacks are used for refs, this map tracks the last value the callback\n// was called with, for ensuring a directive doesn't clear the ref if the ref\n// has already been rendered to a new spot. It is double-keyed on both the\n// context (`options.host`) and the callback, since we auto-bind class methods\n// to `options.host`.\nconst lastElementForContextAndCallback: WeakMap<\n  object,\n  WeakMap<Function, Element | undefined>\n> = new WeakMap();\n\nexport type RefOrCallback<T = Element> = Ref<T> | ((el: T | undefined) => void);\n\nclass RefDirective extends AsyncDirective {\n  private _element?: Element;\n  private _ref?: RefOrCallback;\n  private _context?: object;\n\n  render(_ref?: RefOrCallback) {\n    return nothing;\n  }\n\n  override update(part: ElementPart, [ref]: Parameters<this['render']>) {\n    const refChanged = ref !== this._ref;\n    if (refChanged && this._ref !== undefined) {\n      // The ref passed to the directive has changed;\n      // unset the previous ref's value\n      this._updateRefValue(undefined);\n    }\n    if (refChanged || this._lastElementForRef !== this._element) {\n      // We either got a new ref or this is the first render;\n      // store the ref/element & update the ref value\n      this._ref = ref;\n      this._context = part.options?.host;\n      this._updateRefValue((this._element = part.element));\n    }\n    return nothing;\n  }\n\n  private _updateRefValue(element: Element | undefined) {\n    if (typeof this._ref === 'function') {\n      // If the current ref was called with a previous value, call with\n      // `undefined`; We do this to ensure callbacks are called in a consistent\n      // way regardless of whether a ref might be moving up in the tree (in\n      // which case it would otherwise be called with the new value before the\n      // previous one unsets it) and down in the tree (where it would be unset\n      // before being set). Note that element lookup is keyed by\n      // both the context and the callback, since we allow passing unbound\n      // functions that are called on options.host, and we want to treat\n      // these as unique \"instances\" of a function.\n      const context = this._context ?? globalThis;\n      let lastElementForCallback =\n        lastElementForContextAndCallback.get(context);\n      if (lastElementForCallback === undefined) {\n        lastElementForCallback = new WeakMap();\n        lastElementForContextAndCallback.set(context, lastElementForCallback);\n      }\n      if (lastElementForCallback.get(this._ref) !== undefined) {\n        this._ref.call(this._context, undefined);\n      }\n      lastElementForCallback.set(this._ref, element);\n      // Call the ref with the new element value\n      if (element !== undefined) {\n        this._ref.call(this._context, element);\n      }\n    } else {\n      (this._ref as RefInternal)!.value = element;\n    }\n  }\n\n  private get _lastElementForRef() {\n    return typeof this._ref === 'function'\n      ? lastElementForContextAndCallback\n          .get(this._context ?? globalThis)\n          ?.get(this._ref)\n      : this._ref?.value;\n  }\n\n  override disconnected() {\n    // Only clear the box if our element is still the one in it (i.e. another\n    // directive instance hasn't rendered its element to it before us); that\n    // only happens in the event of the directive being cleared (not via manual\n    // disconnection)\n    if (this._lastElementForRef === this._element) {\n      this._updateRefValue(undefined);\n    }\n  }\n\n  override reconnected() {\n    // If we were manually disconnected, we can safely put our element back in\n    // the box, since no rendering could have occurred to change its state\n    this._updateRefValue(this._element);\n  }\n}\n\n/**\n * Sets the value of a Ref object or calls a ref callback with the element it's\n * bound to.\n *\n * A Ref object acts as a container for a reference to an element. A ref\n * callback is a function that takes an element as its only argument.\n *\n * The ref directive sets the value of the Ref object or calls the ref callback\n * during rendering, if the referenced element changed.\n *\n * Note: If a ref callback is rendered to a different element position or is\n * removed in a subsequent render, it will first be called with `undefined`,\n * followed by another call with the new element it was rendered to (if any).\n *\n * ```js\n * // Using Ref object\n * const inputRef = createRef();\n * render(html`<input ${ref(inputRef)}>`, container);\n * inputRef.value.focus();\n *\n * // Using callback\n * const callback = (inputElement) => inputElement.focus();\n * render(html`<input ${ref(callback)}>`, container);\n * ```\n */\nexport const ref = directive(RefDirective);\n\n/**\n * The type of the class that powers this directive. Necessary for naming the\n * directive's return type.\n */\nexport type {RefDirective};\n"]}