{"version": 3, "file": "base.js", "sourceRoot": "", "sources": ["../../src/decorators/base.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAoCH,MAAM,CAAC,MAAM,qBAAqB,GAAG,CACnC,UAA8B,EAC9B,KAAa,EACb,IAAiB,EACjB,EAAE;IACF,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;AACjD,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,uBAAuB,GAAG,CACrC,UAA8B,EAC9B,OAAqB,EACrB,EAAE,CAAC,CAAC;IACJ,IAAI,EAAE,QAAQ;IACd,SAAS,EAAE,WAAW;IACtB,GAAG,EAAE,OAAO,CAAC,GAAG;IAChB,UAAU;CACX,CAAC,CAAC;AAEH;;;;;;;;;;;;GAYG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAC3B,CAAC,EACC,QAAQ,EACR,UAAU,GAMX,EAAE,EAAE,CACL,CACE,iBAA4D,EAC5D,IAAkB;AAClB,4DAA4D;AAC5D,8DAA8D;EAClD,EAAE;;IACd,iCAAiC;IACjC,IAAI,IAAI,KAAK,SAAS,EAAE;QACtB,MAAM,IAAI,GAAI,iBAAqC;aAChD,WAAqC,CAAC;QACzC,IAAI,UAAU,KAAK,SAAS,EAAE;YAC5B,MAAM,CAAC,cAAc,CAAC,iBAAiB,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;SAClE;QACD,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAG,IAAI,EAAE,IAAK,CAAC,CAAC;QACxB,sBAAsB;KACvB;SAAM;QACL,6DAA6D;QAC7D,yBAAyB;QACzB,MAAM,GAAG;QACP,8DAA8D;QAC9D,MAAC,iBAAyB,CAAC,WAAW,mCACrC,iBAAkC,CAAC,GAAG,CAAC;QAC1C,MAAM,IAAI,GACR,UAAU,IAAI,SAAS;YACrB,CAAC,CAAC;gBACE,IAAI,EAAE,QAAQ;gBACd,SAAS,EAAE,WAAW;gBACtB,GAAG;gBACH,UAAU,EAAE,UAAU,CAAE,iBAAkC,CAAC,GAAG,CAAC;aAChE;YACH,CAAC,CAAC,EAAC,GAAI,iBAAkC,EAAE,GAAG,EAAC,CAAC;QACpD,IAAI,QAAQ,IAAI,SAAS,EAAE;YACzB,IAAI,CAAC,QAAQ,GAAG,UACd,IAAkC;gBAElC,QAAQ,CAAC,IAAyC,EAAE,GAAG,CAAC,CAAC;YAC3D,CAAC,CAAC;SACH;QACD,OAAO,IAAI,CAAC;KACb;AACH,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {ReactiveElement} from '../reactive-element.js';\n\n/**\n * Generates a public interface type that removes private and protected fields.\n * This allows accepting otherwise compatible versions of the type (e.g. from\n * multiple copies of the same package in `node_modules`).\n */\nexport type Interface<T> = {\n  [K in keyof T]: T[K];\n};\n\nexport type Constructor<T> = {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  new (...args: any[]): T;\n};\n\n// From the TC39 Decorators proposal\nexport interface ClassDescriptor {\n  kind: 'class';\n  elements: ClassElement[];\n  finisher?: <T>(clazz: Constructor<T>) => void | Constructor<T>;\n}\n\n// From the TC39 Decorators proposal\nexport interface ClassElement {\n  kind: 'field' | 'method';\n  key: PropertyKey;\n  placement: 'static' | 'prototype' | 'own';\n  initializer?: Function;\n  extras?: ClassElement[];\n  finisher?: <T>(clazz: Constructor<T>) => void | Constructor<T>;\n  descriptor?: PropertyDescriptor;\n}\n\nexport const legacyPrototypeMethod = (\n  descriptor: PropertyDescriptor,\n  proto: Object,\n  name: PropertyKey\n) => {\n  Object.defineProperty(proto, name, descriptor);\n};\n\nexport const standardPrototypeMethod = (\n  descriptor: PropertyDescriptor,\n  element: ClassElement\n) => ({\n  kind: 'method',\n  placement: 'prototype',\n  key: element.key,\n  descriptor,\n});\n\n/**\n * Helper for decorating a property that is compatible with both TypeScript\n * and Babel decorators. The optional `finisher` can be used to perform work on\n * the class. The optional `descriptor` should return a PropertyDescriptor\n * to install for the given property.\n *\n * @param finisher {function} Optional finisher method; receives the element\n * constructor and property key as arguments and has no return value.\n * @param descriptor {function} Optional descriptor method; receives the\n * property key as an argument and returns a property descriptor to define for\n * the given property.\n * @returns {ClassElement|void}\n */\nexport const decorateProperty =\n  ({\n    finisher,\n    descriptor,\n  }: {\n    finisher?:\n      | ((ctor: typeof ReactiveElement, property: PropertyKey) => void)\n      | null;\n    descriptor?: (property: PropertyKey) => PropertyDescriptor;\n  }) =>\n  (\n    protoOrDescriptor: Interface<ReactiveElement> | ClassElement,\n    name?: PropertyKey\n    // Note TypeScript requires the return type to be `void|any`\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  ): void | any => {\n    // TypeScript / Babel legacy mode\n    if (name !== undefined) {\n      const ctor = (protoOrDescriptor as ReactiveElement)\n        .constructor as typeof ReactiveElement;\n      if (descriptor !== undefined) {\n        Object.defineProperty(protoOrDescriptor, name, descriptor(name));\n      }\n      finisher?.(ctor, name!);\n      // Babel standard mode\n    } else {\n      // Note, the @property decorator saves `key` as `originalKey`\n      // so try to use it here.\n      const key =\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        (protoOrDescriptor as any).originalKey ??\n        (protoOrDescriptor as ClassElement).key;\n      const info: ClassElement =\n        descriptor != undefined\n          ? {\n              kind: 'method',\n              placement: 'prototype',\n              key,\n              descriptor: descriptor((protoOrDescriptor as ClassElement).key),\n            }\n          : {...(protoOrDescriptor as ClassElement), key};\n      if (finisher != undefined) {\n        info.finisher = function <ReactiveElement>(\n          ctor: Constructor<ReactiveElement>\n        ) {\n          finisher(ctor as unknown as typeof ReactiveElement, key);\n        };\n      }\n      return info;\n    }\n  };\n"]}