{"version": 3, "file": "polyfill-support.js", "sourceRoot": "", "sources": ["../src/polyfill-support.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AA4EH,0EAA0E;AAC1E,SAAS;AACT,IAAM,YAAY,GAAgB,IAAI,GAAG,EAAE,CAAC;AAC5C,+EAA+E;AAC/E,2CAA2C;AAC3C,IAAM,aAAa,GAA0B,IAAI,GAAG,EAAE,CAAC;AAEvD,IAAM,uBAAuB,GAAG,IAAI,CAAC;AAErC,sEAAsE;AACtE,WAAW;AACX,kCAAkC;AAClC,IAAI,QAAQ,GAAG,IAAI,CAAC;AAEpB;;;;GAIG;AACH,IAAM,eAAe,GAA+C,UAClE,QAAsC,EACtC,SAAwC;;IAExC,yEAAyE;IACzE,gEAAgE;IAChE,uDAAuD;IACvD,IACE,MAAM,CAAC,QAAQ,KAAK,SAAS;QAC7B,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,EAC5D;QACA,OAAO;KACR;IAED,eAAe;IACf,wDAAwD;IACxD,4CAA4C;IAC5C,KAAK;IAEL,IAAM,IAAI,GACR,uBAAuB;SACvB,MAAA,MAAM,CAAC,QAAQ,0CAAE,KAAK,CAAA;QACtB,CAAA,MAAA,MAAM,CAAC,QAAQ,0CAAE,OAAO,MAAK,IAAI;QAC/B,CAAC,CAAC,MAAM,CAAC,QAAS,CAAC,IAAI;QACvB,CAAC,CAAC,UAAC,IAAU,IAAK,OAAA,IAAI,EAAJ,CAAI,CAAC;IAE3B,IAAM,kBAAkB,GAAG,UAAC,IAAwB;QAClD,OAAA,IAAI,KAAK,SAAS,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC;IAA7C,CAA6C,CAAC;IAEhD,IAAM,WAAW,GAAG,UAAC,IAAY;QAC/B,IAAI,QAAQ,GAAG,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACvC,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC1B,aAAa,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC;SAC1C;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC,CAAC;IAEF,IAAM,aAAa,GAAG,UAAC,IAAY,EAAE,QAA6B;QAChE,aAAa;QACb,IAAM,QAAQ,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;QACnC,IAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,KAAK,CAAC,CAAC;QAC1C,IAAI,WAAW,EAAE;YACf,IAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YAC9C,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxC,wEAAwE;YACxE,mCAAmC;YACnC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;SACrC;QACD,6BAA6B;QAC7B,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACvB,kDAAkD;QAClD,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAC3B,0EAA0E;QAC1E,6BAA6B;QAC7B,MAAM,CAAC,QAAS,CAAC,qBAAqB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QACvD,4EAA4E;QAC5E,wEAAwE;QACxE,wBAAwB;QACxB,IAAI,WAAW,IAAI,MAAM,CAAC,QAAS,CAAC,YAAY,EAAE;YAChD,iEAAiE;YACjE,kEAAkE;YAClE,IAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YACtD,IAAI,KAAK,KAAK,IAAI,EAAE;gBAClB,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;aACrC;SACF;IACH,CAAC,CAAC;IAEF,IAAM,mBAAmB,GAAG,IAAI,GAAG,EAGhC,CAAC;IAEJ;;;;OAIG;IACH,IAAM,qBAAqB,GAAG,QAAQ,CAAC,aAAa,CAAC;IACrD,QAAQ,CAAC,aAAa,GAAG,UAAU,IAAY,EAAE,OAAuB;QACtE,IAAM,OAAO,GAAG,qBAAqB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QACpE,IAAM,KAAK,GAAG,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,KAAK,CAAC;QAC7B,IAAI,KAAK,KAAK,SAAS,EAAE;YACvB,IAAI,CAAC,MAAM,CAAC,QAAS,CAAC,YAAY,EAAE;gBAClC,MAAM,CAAC,QAAS,CAAC,kBAAkB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;aACrD;YACD,kEAAkE;YAClE,gDAAgD;YAChD,IAAI,kBAAkB,CAAC,KAAK,CAAC,EAAE;gBAC7B,IAAM,QAAQ,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;gBACpC,uCAAuC;gBACvC,IAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAC7C,OAAO,CACwB,CAAC;gBAClC,yEAAyE;gBACzE,iEAAiE;gBACjE,QAAQ,CAAC,IAAI,OAAb,QAAQ,EACH,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,UAAC,KAAK;;oBAC9B,MAAA,KAAK,CAAC,UAAU,0CAAE,WAAW,CAAC,KAAK,CAAC,CAAC;oBACrC,OAAO,KAAK,CAAC,WAAY,CAAC;gBAC5B,CAAC,CAAC,EACF;aACH;SACF;QACD,OAAO,OAAO,CAAC;IACjB,CAAC,CAAC;IAEF,IAAM,eAAe,GAAG,QAAQ,CAAC,sBAAsB,EAAE,CAAC;IAC1D,IAAM,qBAAqB,GAAG,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;IAEzD,IAAM,cAAc,GAAG,SAAS,CAAC,SAAS,CAAC;IAC3C;;OAEG;IACH,IAAM,QAAQ,GAAG,cAAc,CAAC,UAAU,CAAC;IAC3C,cAAc,CAAC,UAAU,GAAG,UAE1B,KAAc,EACd,eAAuC;;QAAvC,gCAAA,EAAA,sBAAuC;QAEvC,IAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,UAAW,CAAC;QACrD,IAAM,KAAK,GAAG,MAAA,IAAI,CAAC,OAAO,0CAAE,KAAK,CAAC;QAClC,IAAI,SAAS,YAAY,UAAU,IAAI,kBAAkB,CAAC,KAAK,CAAC,EAAE;YAChE,kEAAkE;YAClE,sEAAsE;YACtE,yEAAyE;YACzE,0EAA0E;YAC1E,mEAAmE;YACnE,yEAAyE;YACzE,uEAAuE;YACvE,oCAAoC;YACpC,IAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC;YACnC,IAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC;YAE/B,uDAAuD;YACvD,eAAe,CAAC,WAAW,CAAC,qBAAqB,CAAC,CAAC;YACnD,IAAI,CAAC,WAAW,GAAG,qBAAqB,CAAC;YACzC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YAEtB,sEAAsE;YACtE,8BAA8B;YAC9B,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,eAAe,CAAC,CAAC;YAE5C,qEAAqE;YACrE,yBAAyB;YACzB,4CAA4C;YAC5C,IAAM,QAAQ,GAAG,CAAC,KAA6B,aAA7B,KAAK,uBAAL,KAAK,CAA2B,YAAY,CAAC;gBAC7D,CAAC,CAAE,IAAI,CAAC,gBAA8C,CAAC,UAAU,CAAC,EAAE;gBACpE,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;YACvC,aAAa,CAAC,KAAM,EAAE,QAAQ,CAAC,CAAC;YAEhC,yCAAyC;YACzC,eAAe,CAAC,WAAW,CAAC,qBAAqB,CAAC,CAAC;YACnD,sEAAsE;YACtE,IAAI,MAAA,MAAM,CAAC,QAAQ,0CAAE,YAAY,EAAE;gBACjC,IAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;gBACtD,IAAI,KAAK,KAAK,IAAI,EAAE;oBAClB,eAAe,CAAC,WAAW,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;iBACpD;aACF;YACD,SAAS,CAAC,YAAY,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;YACjD,wCAAwC;YACxC,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC;YAC7B,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC;SAC1B;aAAM;YACL,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,eAAe,CAAC,CAAC;SAC7C;IACH,CAAC,CAAC;IAEF;;;OAGG;IACH,cAAc,CAAC,aAAa,GAAG,UAE7B,MAA2B;;QAE3B,IAAM,KAAK,GAAG,MAAA,IAAI,CAAC,OAAO,0CAAE,KAAK,CAAC;QAClC,IAAI,aAAa,GAAG,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACnD,IAAI,aAAa,KAAK,SAAS,EAAE;YAC/B,mBAAmB,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,aAAa,GAAG,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC;SAC7D;QACD,IAAI,QAAQ,GAAG,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACjD,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC1B,aAAa,CAAC,GAAG,CACf,MAAM,CAAC,OAAO,EACd,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAChD,CAAC;SACH;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,IAAI,uBAAuB,EAAE;IAC3B,eAAe,CAAC,gBAAgB,GAAG,uBAAuB,CAAC;CAC5D;AAED,IAAI,QAAQ,EAAE;IACZ,MAAA,UAAU,CAAC,6BAA6B,oCAAxC,UAAU,CAAC,6BAA6B,GAAK,eAAe,EAAC;CAC9D;KAAM;IACL,MAAA,UAAU,CAAC,sBAAsB,oCAAjC,UAAU,CAAC,sBAAsB,GAAK,eAAe,EAAC;CACvD", "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/**\n * lit-html patch to support browsers without native web components.\n *\n * This module should be used in addition to loading the web components\n * polyfills via @webcomponents/webcomponentjs. When using those polyfills\n * support for polyfilled Shadow DOM is automatic via the ShadyDOM polyfill.\n * Scoping classes are added to DOM nodes to facilitate CSS scoping that\n * simulates the style scoping Shadow DOM provides. ShadyDOM does this scoping\n * to all elements added to the DOM. This module provides an important\n * optimization for this process by pre-scoping lit-html template\n * DOM. This means ShadyDOM does not have to scope each instance of the\n * template DOM. Instead, each template is scoped only once.\n *\n * Creating scoped CSS is not covered by this module. It is, however, integrated\n * into the lit-element and @lit/reactive-element packages. See the ShadyCSS docs\n * for how to apply scoping to CSS:\n * https://github.com/webcomponents/polyfills/tree/master/packages/shadycss#usage.\n *\n * @packageDocumentation\n */\n\nexport {};\n\ninterface RenderOptions {\n  readonly renderBefore?: ChildNode | null;\n  scope?: string;\n}\n\ninterface ShadyTemplateResult {\n  strings: TemplateStringsArray;\n  // This property needs to remain unminified.\n  ['_$litType$']?: string;\n}\n\n// Note, this is a dummy type as the full type here is big.\ninterface Directive {\n  __directive?: Directive;\n}\n\ninterface DirectiveParent {\n  _$parent?: DirectiveParent;\n  __directive?: Directive;\n  __directives?: Array<Directive | undefined>;\n}\n\ninterface PatchableChildPartConstructor {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-misused-new\n  new (...args: any[]): PatchableChildPart;\n}\n\ninterface PatchableChildPart {\n  __directive?: Directive;\n  _$committedValue: unknown;\n  _$startNode: ChildNode;\n  _$endNode: ChildNode | null;\n  options: RenderOptions;\n  _$setValue(value: unknown, directiveParent: DirectiveParent): void;\n  _$getTemplate(result: ShadyTemplateResult): HTMLTemplateElement;\n}\n\ninterface PatchableTemplate {\n  el: HTMLTemplateElement;\n}\n\ninterface PatchableTemplateConstructor {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-misused-new\n  new (...args: any[]): PatchableTemplate;\n  createElement(html: string, options?: RenderOptions): HTMLTemplateElement;\n}\n\ninterface PatchableTemplateInstance {\n  _$template: PatchableTemplate;\n}\n\n// Scopes that have had styling prepared. Note, must only be done once per\n// scope.\nconst styledScopes: Set<string> = new Set();\n// Map of css per scope. This is collected during first scope render, used when\n// styling is prepared, and then discarded.\nconst scopeCssStore: Map<string, string[]> = new Map();\n\nconst ENABLE_SHADYDOM_NOPATCH = true;\n\n// Note, explicitly use `var` here so that this can be re-defined when\n// bundled.\n// eslint-disable-next-line no-var\nvar DEV_MODE = true;\n\n/**\n * lit-html patches. These properties cannot be renamed.\n * * ChildPart.prototype._$getTemplate\n * * ChildPart.prototype._$setValue\n */\nconst polyfillSupport: NonNullable<typeof litHtmlPolyfillSupport> = (\n  Template: PatchableTemplateConstructor,\n  ChildPart: PatchableChildPartConstructor\n) => {\n  // polyfill-support is only needed if ShadyCSS or the ApplyShim is in use\n  // We test at the point of patching, which makes it safe to load\n  // webcomponentsjs and polyfill-support in either order\n  if (\n    window.ShadyCSS === undefined ||\n    (window.ShadyCSS.nativeShadow && !window.ShadyCSS.ApplyShim)\n  ) {\n    return;\n  }\n\n  // console.log(\n  //   '%c Making lit-html compatible with ShadyDOM/CSS.',\n  //   'color: lightgreen; font-style: italic'\n  // );\n\n  const wrap =\n    ENABLE_SHADYDOM_NOPATCH &&\n    window.ShadyDOM?.inUse &&\n    window.ShadyDOM?.noPatch === true\n      ? window.ShadyDOM!.wrap\n      : (node: Node) => node;\n\n  const needsPrepareStyles = (name: string | undefined) =>\n    name !== undefined && !styledScopes.has(name);\n\n  const cssForScope = (name: string) => {\n    let scopeCss = scopeCssStore.get(name);\n    if (scopeCss === undefined) {\n      scopeCssStore.set(name, (scopeCss = []));\n    }\n    return scopeCss;\n  };\n\n  const prepareStyles = (name: string, template: HTMLTemplateElement) => {\n    // Get styles\n    const scopeCss = cssForScope(name);\n    const hasScopeCss = scopeCss.length !== 0;\n    if (hasScopeCss) {\n      const style = document.createElement('style');\n      style.textContent = scopeCss.join('\\n');\n      // Note, it's important to add the style to the *end* of the template so\n      // it doesn't mess up part indices.\n      template.content.appendChild(style);\n    }\n    // Mark this scope as styled.\n    styledScopes.add(name);\n    // Remove stored data since it's no longer needed.\n    scopeCssStore.delete(name);\n    // ShadyCSS removes scopes and removes the style under ShadyDOM and leaves\n    // it under native Shadow DOM\n    window.ShadyCSS!.prepareTemplateStyles(template, name);\n    // Note, under native Shadow DOM, the style is added to the beginning of the\n    // template. It must be moved to the *end* of the template so it doesn't\n    // mess up part indices.\n    if (hasScopeCss && window.ShadyCSS!.nativeShadow) {\n      // If there were styles but the CSS text was empty, ShadyCSS will\n      // eliminate the style altogether, so the style here could be null\n      const style = template.content.querySelector('style');\n      if (style !== null) {\n        template.content.appendChild(style);\n      }\n    }\n  };\n\n  const scopedTemplateCache = new Map<\n    string | undefined,\n    Map<TemplateStringsArray, PatchableTemplate>\n  >();\n\n  /**\n   * Override to extract style elements from the template\n   * and store all style.textContent in the shady scope data.\n   * Note, it's ok to patch Template since it's only used via ChildPart.\n   */\n  const originalCreateElement = Template.createElement;\n  Template.createElement = function (html: string, options?: RenderOptions) {\n    const element = originalCreateElement.call(Template, html, options);\n    const scope = options?.scope;\n    if (scope !== undefined) {\n      if (!window.ShadyCSS!.nativeShadow) {\n        window.ShadyCSS!.prepareTemplateDom(element, scope);\n      }\n      // Process styles only if this scope is being prepared. Otherwise,\n      // leave styles as is for back compat with Lit1.\n      if (needsPrepareStyles(scope)) {\n        const scopeCss = cssForScope(scope);\n        // Remove styles and store textContent.\n        const styles = element.content.querySelectorAll(\n          'style'\n        ) as NodeListOf<HTMLStyleElement>;\n        // Store the css in this template in the scope css and remove the <style>\n        // from the template _before_ the node-walk captures part indices\n        scopeCss.push(\n          ...Array.from(styles).map((style) => {\n            style.parentNode?.removeChild(style);\n            return style.textContent!;\n          })\n        );\n      }\n    }\n    return element;\n  };\n\n  const renderContainer = document.createDocumentFragment();\n  const renderContainerMarker = document.createComment('');\n\n  const childPartProto = ChildPart.prototype;\n  /**\n   * Patch to apply gathered css via ShadyCSS. This is done only once per scope.\n   */\n  const setValue = childPartProto._$setValue;\n  childPartProto._$setValue = function (\n    this: PatchableChildPart,\n    value: unknown,\n    directiveParent: DirectiveParent = this\n  ) {\n    const container = wrap(this._$startNode).parentNode!;\n    const scope = this.options?.scope;\n    if (container instanceof ShadowRoot && needsPrepareStyles(scope)) {\n      // Note, @apply requires outer => inner scope rendering on initial\n      // scope renders to apply property values correctly. Style preparation\n      // is tied to rendering into `shadowRoot`'s and this is typically done by\n      // custom elements. If this is done in `connectedCallback`, as is typical,\n      // the code below ensures the right order since content is rendered\n      // into a fragment first so the hosting element can prepare styles first.\n      // If rendering is done in the constructor, this won't work, but that's\n      // not supported in ShadyDOM anyway.\n      const startNode = this._$startNode;\n      const endNode = this._$endNode;\n\n      // Temporarily move this part into the renderContainer.\n      renderContainer.appendChild(renderContainerMarker);\n      this._$startNode = renderContainerMarker;\n      this._$endNode = null;\n\n      // Note, any nested template results render here and their styles will\n      // be extracted and collected.\n      setValue.call(this, value, directiveParent);\n\n      // Get the template for this result or create a dummy one if a result\n      // is not being rendered.\n      // This property needs to remain unminified.\n      const template = (value as ShadyTemplateResult)?.['_$litType$']\n        ? (this._$committedValue as PatchableTemplateInstance)._$template.el\n        : document.createElement('template');\n      prepareStyles(scope!, template);\n\n      // Note, this is the temporary startNode.\n      renderContainer.removeChild(renderContainerMarker);\n      // When using native Shadow DOM, include prepared style in shadowRoot.\n      if (window.ShadyCSS?.nativeShadow) {\n        const style = template.content.querySelector('style');\n        if (style !== null) {\n          renderContainer.appendChild(style.cloneNode(true));\n        }\n      }\n      container.insertBefore(renderContainer, endNode);\n      // Move part back to original container.\n      this._$startNode = startNode;\n      this._$endNode = endNode;\n    } else {\n      setValue.call(this, value, directiveParent);\n    }\n  };\n\n  /**\n   * Patch ChildPart._$getTemplate to look up templates in a cache bucketed\n   * by element name.\n   */\n  childPartProto._$getTemplate = function (\n    this: PatchableChildPart,\n    result: ShadyTemplateResult\n  ) {\n    const scope = this.options?.scope;\n    let templateCache = scopedTemplateCache.get(scope);\n    if (templateCache === undefined) {\n      scopedTemplateCache.set(scope, (templateCache = new Map()));\n    }\n    let template = templateCache.get(result.strings);\n    if (template === undefined) {\n      templateCache.set(\n        result.strings,\n        (template = new Template(result, this.options))\n      );\n    }\n    return template;\n  };\n};\n\nif (ENABLE_SHADYDOM_NOPATCH) {\n  polyfillSupport.noPatchSupported = ENABLE_SHADYDOM_NOPATCH;\n}\n\nif (DEV_MODE) {\n  globalThis.litHtmlPolyfillSupportDevMode ??= polyfillSupport;\n} else {\n  globalThis.litHtmlPolyfillSupport ??= polyfillSupport;\n}\n"]}