# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2011-2012
# <PERSON> <<EMAIL>>, 2012
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-24 13:46+0200\n"
"PO-Revision-Date: 2017-09-24 14:24+0000\n"
"Last-Translator: <PERSON><PERSON> <jann<PERSON>@leidel.info>\n"
"Language-Team: English (United Kingdom) (http://www.transifex.com/django/"
"django/language/en_GB/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: en_GB\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Personal info"
msgstr "Personal info"

msgid "Permissions"
msgstr "Permissions"

msgid "Important dates"
msgstr "Important dates"

#, python-format
msgid "%(name)s object with primary key %(key)r does not exist."
msgstr ""

msgid "Password changed successfully."
msgstr "Password changed successfully."

#, python-format
msgid "Change password: %s"
msgstr "Change password: %s"

msgid "Authentication and Authorization"
msgstr ""

msgid "password"
msgstr "password"

msgid "last login"
msgstr "last login"

msgid "No password set."
msgstr ""

msgid "Invalid password format or unknown hashing algorithm."
msgstr ""

msgid "The two password fields didn't match."
msgstr "The two password fields didn't match."

msgid "Password"
msgstr "Password"

msgid "Password confirmation"
msgstr "Password confirmation"

msgid "Enter the same password as before, for verification."
msgstr ""

msgid ""
"Raw passwords are not stored, so there is no way to see this user's "
"password, but you can change the password using <a href=\"{}\">this form</a>."
msgstr ""

#, python-format
msgid ""
"Please enter a correct %(username)s and password. Note that both fields may "
"be case-sensitive."
msgstr ""

msgid "This account is inactive."
msgstr "This account is inactive."

msgid "Email"
msgstr ""

msgid "New password"
msgstr "New password"

msgid "New password confirmation"
msgstr "New password confirmation"

msgid "Your old password was entered incorrectly. Please enter it again."
msgstr "Your old password was entered incorrectly. Please enter it again."

msgid "Old password"
msgstr "Old password"

msgid "Password (again)"
msgstr "Password (again)"

msgid "algorithm"
msgstr "algorithm"

msgid "iterations"
msgstr "iterations"

msgid "salt"
msgstr "salt"

msgid "hash"
msgstr "hash"

msgid "variety"
msgstr ""

msgid "version"
msgstr ""

msgid "memory cost"
msgstr ""

msgid "time cost"
msgstr ""

msgid "parallelism"
msgstr ""

msgid "work factor"
msgstr "work factor"

msgid "checksum"
msgstr "checksum"

msgid "name"
msgstr "name"

msgid "content type"
msgstr ""

msgid "codename"
msgstr "codename"

msgid "permission"
msgstr "permission"

msgid "permissions"
msgstr "permissions"

msgid "group"
msgstr "group"

msgid "groups"
msgstr "groups"

msgid "superuser status"
msgstr "superuser status"

msgid ""
"Designates that this user has all permissions without explicitly assigning "
"them."
msgstr ""
"Designates that this user has all permissions without explicitly assigning "
"them."

msgid ""
"The groups this user belongs to. A user will get all permissions granted to "
"each of their groups."
msgstr ""

msgid "user permissions"
msgstr "user permissions"

msgid "Specific permissions for this user."
msgstr ""

msgid "username"
msgstr "username"

msgid "Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only."
msgstr ""

msgid "A user with that username already exists."
msgstr "A user with that username already exists."

msgid "first name"
msgstr "first name"

msgid "last name"
msgstr "last name"

msgid "email address"
msgstr ""

msgid "staff status"
msgstr "staff status"

msgid "Designates whether the user can log into this admin site."
msgstr "Designates whether the user can log into this admin site."

msgid "active"
msgstr "active"

msgid ""
"Designates whether this user should be treated as active. Unselect this "
"instead of deleting accounts."
msgstr ""
"Designates whether this user should be treated as active. Unselect this "
"instead of deleting accounts."

msgid "date joined"
msgstr "date joined"

msgid "user"
msgstr "user"

msgid "users"
msgstr "users"

#, python-format
msgid ""
"This password is too short. It must contain at least %(min_length)d "
"character."
msgid_plural ""
"This password is too short. It must contain at least %(min_length)d "
"characters."
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "Your password must contain at least %(min_length)d character."
msgid_plural "Your password must contain at least %(min_length)d characters."
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "The password is too similar to the %(verbose_name)s."
msgstr ""

msgid "Your password can't be too similar to your other personal information."
msgstr ""

msgid "This password is too common."
msgstr ""

msgid "Your password can't be a commonly used password."
msgstr ""

msgid "This password is entirely numeric."
msgstr ""

msgid "Your password can't be entirely numeric."
msgstr ""

#, python-format
msgid "Password reset on %(site_name)s"
msgstr "Password reset on %(site_name)s"

msgid ""
"Enter a valid username. This value may contain only English letters, "
"numbers, and @/./+/-/_ characters."
msgstr ""

msgid ""
"Enter a valid username. This value may contain only letters, numbers, and "
"@/./+/-/_ characters."
msgstr ""

msgid "Logged out"
msgstr "Logged out"

msgid "Password reset"
msgstr ""

msgid "Password reset sent"
msgstr ""

msgid "Enter new password"
msgstr ""

msgid "Password reset unsuccessful"
msgstr ""

msgid "Password reset complete"
msgstr ""

msgid "Password change"
msgstr ""

msgid "Password change successful"
msgstr ""
