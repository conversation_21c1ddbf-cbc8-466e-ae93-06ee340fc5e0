import React, { useState, useEffect } from 'react';
import './styles.css';

const Header = ({ account, onConnectWallet, onNavigate, currentView, connectionType, isAuthenticated, onLogout, isWorkerAuthenticated, workerData, onWorkerLogout }) => {
    const [isConnecting, setIsConnecting] = useState(false);
    const [metaMaskInstalled, setMetaMaskInstalled] = useState(false);
    const [metaMaskStatus, setMetaMaskStatus] = useState('checking');

    // Check if MetaMask is installed
    useEffect(() => {
        const checkMetaMask = async () => {
            // Check if window.ethereum exists
            if (window.ethereum) {
                // Check if it's MetaMask specifically
                if (window.ethereum.isMetaMask) {
                    setMetaMaskInstalled(true);

                    // Check if already connected
                    try {
                        const accounts = await window.ethereum.request({
                            method: 'eth_accounts'
                        });

                        if (accounts && accounts.length > 0) {
                            setMetaMaskStatus('connected');
                        } else {
                            setMetaMaskStatus('installed');
                        }
                    } catch (error) {
                        console.warn("Error checking MetaMask accounts:", error);
                        setMetaMaskStatus('installed');
                    }
                } else {
                    setMetaMaskInstalled(false);
                    setMetaMaskStatus('not-metamask');
                }
            } else {
                setMetaMaskInstalled(false);
                setMetaMaskStatus('not-installed');
            }
        };

        checkMetaMask();
    }, [account]); // Re-check when account changes

    // Handle wallet connection with loading state
    const handleConnectWallet = async () => {
        setIsConnecting(true);
        try {
            await onConnectWallet();
        } finally {
            setIsConnecting(false);
        }
    };
    return (
        <header className="mb-5">
            <div className="app-header text-center py-4">
                <div className="container">
                    <div className="row align-items-center">
                        <div className="col-md-8 text-md-start">
                            <h1 className="display-5 fw-bold">Kenyan Election Voting System</h1>
                            <p className="lead mb-0">Secure, Transparent, and Decentralized</p>
                        </div>
                        <div className="col-md-4 text-md-end mt-3 mt-md-0">
                            {account ? (
                                <div className="account-info d-inline-block bg-success bg-opacity-10 p-2 rounded border border-success">
                                    <div className="d-flex align-items-center">
                                        {connectionType === 'metamask' && (
                                            <img
                                                src="https://upload.wikimedia.org/wikipedia/commons/thumb/3/36/MetaMask_Fox.svg/64px-MetaMask_Fox.svg.png"
                                                alt="MetaMask"
                                                className="me-2"
                                                style={{ height: '24px' }}
                                            />
                                        )}

                                        {connectionType === 'walletconnect' && (
                                            <img
                                                src="https://walletconnect.com/images/logo.svg"
                                                alt="WalletConnect"
                                                className="me-2"
                                                style={{ height: '24px' }}
                                            />
                                        )}

                                        {connectionType === 'sms' && (
                                            <i className="bi bi-phone-fill me-2 fs-5"></i>
                                        )}

                                        {connectionType === 'guest' && (
                                            <i className="bi bi-person-fill me-2 fs-5"></i>
                                        )}

                                        {!connectionType && (
                                            <i className="bi bi-wallet2 me-2 fs-5"></i>
                                        )}

                                        <div>
                                            <small className="d-block text-success fw-bold mb-1">
                                                <i className="bi bi-check-circle-fill me-1"></i>
                                                {connectionType === 'metamask' && 'Connected with MetaMask'}
                                                {connectionType === 'walletconnect' && 'Connected with WalletConnect'}
                                                {connectionType === 'sms' && 'Connected with Phone'}
                                                {connectionType === 'guest' && 'Guest Mode'}
                                                {!connectionType && 'Connected'}
                                            </small>
                                            <span className="text-truncate d-inline-block" style={{ maxWidth: '200px' }}>
                                                {account.substring(0, 6)}...{account.substring(account.length - 4)}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            ) : (
                                <div className="d-flex flex-column align-items-end">
                                    {metaMaskStatus === 'checking' && (
                                        <div className="account-info d-inline-block bg-info bg-opacity-25 p-2 rounded border border-info mb-2">
                                            <span className="text-info">
                                                <i className="bi bi-hourglass-split me-1"></i>
                                                Checking MetaMask...
                                            </span>
                                        </div>
                                    )}

                                    {metaMaskStatus === 'not-installed' && (
                                        <div className="account-info d-inline-block bg-danger bg-opacity-25 p-2 rounded border border-danger mb-2">
                                            <span className="text-danger">
                                                <i className="bi bi-exclamation-circle-fill me-1"></i>
                                                MetaMask Not Installed
                                            </span>
                                        </div>
                                    )}

                                    {metaMaskStatus === 'not-metamask' && (
                                        <div className="account-info d-inline-block bg-warning bg-opacity-25 p-2 rounded border border-warning mb-2">
                                            <span className="text-warning">
                                                <i className="bi bi-exclamation-triangle-fill me-1"></i>
                                                Not Using MetaMask
                                            </span>
                                        </div>
                                    )}

                                    {metaMaskStatus === 'installed' && (
                                        <div className="account-info d-inline-block bg-warning bg-opacity-25 p-2 rounded border border-warning mb-2">
                                            <span className="text-warning">
                                                <i className="bi bi-exclamation-triangle-fill me-1"></i>
                                                MetaMask Not Connected
                                            </span>
                                        </div>
                                    )}

                                    {metaMaskStatus === 'connected' && (
                                        <div className="account-info d-inline-block bg-info bg-opacity-25 p-2 rounded border border-info mb-2">
                                            <span className="text-info">
                                                <i className="bi bi-info-circle-fill me-1"></i>
                                                Waiting for Account...
                                            </span>
                                        </div>
                                    )}

                                    {metaMaskStatus === 'not-installed' ? (
                                        <a
                                            href="https://metamask.io/download/"
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="btn btn-primary"
                                        >
                                            <img
                                                src="https://upload.wikimedia.org/wikipedia/commons/thumb/3/36/MetaMask_Fox.svg/64px-MetaMask_Fox.svg.png"
                                                alt="MetaMask"
                                                className="me-2"
                                                style={{ height: '20px' }}
                                            />
                                            Install MetaMask
                                        </a>
                                    ) : (
                                        <button
                                            className="btn btn-primary"
                                            onClick={handleConnectWallet}
                                            disabled={isConnecting || metaMaskStatus === 'not-metamask' || metaMaskStatus === 'not-installed'}
                                        >
                                            {isConnecting ? (
                                                <>
                                                    <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                                    Connecting...
                                                </>
                                            ) : (
                                                <>
                                                    <img
                                                        src="https://upload.wikimedia.org/wikipedia/commons/thumb/3/36/MetaMask_Fox.svg/64px-MetaMask_Fox.svg.png"
                                                        alt="MetaMask"
                                                        className="me-2"
                                                        style={{ height: '20px' }}
                                                    />
                                                    Connect MetaMask
                                                </>
                                            )}
                                        </button>
                                    )}
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
            <div className="kenya-flag-colors"></div>

            {/* Navigation Menu */}
            <nav className="navbar navbar-expand-lg navbar-light bg-light">
                <div className="container">
                    <button className="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                        <span className="navbar-toggler-icon"></span>
                    </button>
                    <div className="collapse navbar-collapse" id="navbarNav">
                        <ul className="navbar-nav me-auto">
                            <li className="nav-item">
                                <button
                                    className={`nav-link btn btn-link ${currentView === 'home' ? 'active fw-bold' : ''}`}
                                    onClick={() => onNavigate('home')}
                                >
                                    <i className="bi bi-house-door me-1"></i>
                                    Home
                                </button>
                            </li>
                            <li className="nav-item">
                                <button
                                    className={`nav-link btn btn-link ${currentView === 'elections' ? 'active fw-bold' : ''}`}
                                    onClick={() => onNavigate('elections')}
                                >
                                    <i className="bi bi-check2-square me-1"></i>
                                    Elections
                                </button>
                            </li>
                            <li className="nav-item">
                                <button
                                    className={`nav-link btn btn-link ${currentView === 'statistics' ? 'active fw-bold' : ''}`}
                                    onClick={() => onNavigate('statistics')}
                                >
                                    <i className="bi bi-bar-chart-fill me-1"></i>
                                    Statistics
                                </button>
                            </li>
                            <li className="nav-item">
                                <button
                                    className={`nav-link btn btn-link ${currentView === 'analytics' ? 'active fw-bold' : ''}`}
                                    onClick={() => onNavigate('analytics')}
                                >
                                    <i className="bi bi-graph-up me-1"></i>
                                    Analytics
                                </button>
                            </li>
                            <li className="nav-item">
                                <button
                                    className={`nav-link btn btn-link ${currentView === 'results' ? 'active fw-bold' : ''}`}
                                    onClick={() => onNavigate('results')}
                                >
                                    <i className="bi bi-trophy me-1"></i>
                                    Election Results
                                </button>
                            </li>
                        </ul>

                        {/* Account and Authentication Section */}
                        <ul className="navbar-nav">
                            <li className="nav-item">
                                <button
                                    className={`nav-link btn btn-link ${currentView === 'profile' ? 'active fw-bold' : ''}`}
                                    onClick={() => onNavigate('profile')}
                                >
                                    <i className="bi bi-person-circle me-1"></i>
                                    My Profile
                                </button>
                            </li>
                            {isAuthenticated || isWorkerAuthenticated ? (
                                <>
                                    {isWorkerAuthenticated && workerData && (
                                        <li className="nav-item">
                                            <span className="nav-link text-success">
                                                <i className="bi bi-shield-check me-1"></i>
                                                Worker: {workerData.username}
                                            </span>
                                        </li>
                                    )}
                                    {isWorkerAuthenticated && (
                                        <li className="nav-item">
                                            <button
                                                className={`nav-link btn btn-link ${currentView === 'worker-dashboard' ? 'active fw-bold' : ''}`}
                                                onClick={() => onNavigate('worker-dashboard')}
                                            >
                                                <i className="bi bi-speedometer2 me-1"></i>
                                                Dashboard
                                            </button>
                                        </li>
                                    )}
                                    <li className="nav-item">
                                        <button
                                            className="nav-link btn btn-link"
                                            onClick={isWorkerAuthenticated ? onWorkerLogout : onLogout}
                                        >
                                            <i className="bi bi-box-arrow-right me-1"></i>
                                            Logout
                                        </button>
                                    </li>
                                </>
                            ) : (
                                <>
                                    <li className="nav-item">
                                        <button
                                            className={`nav-link btn btn-link ${currentView === 'login' ? 'active fw-bold' : ''}`}
                                            onClick={() => onNavigate('login')}
                                        >
                                            <i className="bi bi-box-arrow-in-right me-1"></i>
                                            Voter Login
                                        </button>
                                    </li>
                                    <li className="nav-item">
                                        <button
                                            className={`nav-link btn btn-link ${currentView === 'worker-login' ? 'active fw-bold' : ''}`}
                                            onClick={() => onNavigate('worker-login')}
                                        >
                                            <i className="bi bi-shield-check me-1"></i>
                                            Worker Login
                                        </button>
                                    </li>
                                </>
                            )}
                        </ul>
                    </div>
                </div>
            </nav>

            <div className="container mb-4 mt-4">
                <div className="row">
                    <div className="col-md-8">
                        <p className="lead">
                            Welcome to Kenya's first blockchain-based voting system. This platform ensures
                            secure, transparent, and tamper-proof elections through Ethereum smart contracts.
                        </p>
                    </div>
                    <div className="col-md-4">
                        <div className="d-flex align-items-center justify-content-md-end mt-3 mt-md-0">
                            <img
                                src="https://upload.wikimedia.org/wikipedia/commons/thumb/4/49/Flag_of_Kenya.svg/120px-Flag_of_Kenya.svg.png"
                                alt="Kenyan Flag"
                                className="me-3"
                                style={{ height: '40px' }}
                            />
                            <div className="text-muted">
                                <small>Powered by</small>
                                <div className="fw-bold">Blockchain Technology</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>
    );
};

export default Header;
