{% extends "admin/base_site.html" %}
{% load i18n %}

{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% translate 'Home' %}</a>
&rsaquo; <a href="{% url 'django-admindocs-docroot' %}">{% translate 'Documentation' %}</a>
&rsaquo; {% translate 'Bookmarklets' %}
</div>
{% endblock %}
{% block title %}{% translate "Documentation bookmarklets" %}{% endblock %}

{% block content %}

<p class="quiet">{% blocktranslate trimmed %}
To install bookmarklets, drag the link to your bookmarks toolbar, or right-click
the link and add it to your bookmarks. Now you can select the bookmarklet
from any page in the site.
{% endblocktranslate %}</p>

<div id="content-main">
    <h3><a href="javascript:(function(){if(typeof XMLHttpRequest!='undefined'){x=new XMLHttpRequest()}else{return;}x.open('HEAD',location.href,false);x.send(null);try{view=x.getResponseHeader('x-view');}catch(e){alert('No view found for this page');return;}if(view=='undefined'){alert('No view found for this page');}document.location='{% url 'django-admindocs-views-index' %}'+view+'/';})()">{% translate "Documentation for this page" %}</a></h3>
    <p>{% translate "Jumps you from any page to the documentation for the view that generates that page." %}</p>
</div>

{% endblock %}
