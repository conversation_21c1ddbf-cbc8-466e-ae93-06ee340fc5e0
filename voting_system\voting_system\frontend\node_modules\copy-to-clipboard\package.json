{"name": "copy-to-clipboard", "version": "3.3.3", "description": "Copy stuff into clipboard using JS with fallbacks", "main": "index.js", "types": "index.d.ts", "scripts": {"pretest": "browserify ./index.js --standalone copyToClipboard | uglifyjs -cm > example/index.js", "test": "nightwatch"}, "keywords": ["clipboard", "copy", "browser"], "author": "sudo<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sudodoki/copy-to-clipboard"}, "contributors": [{"name": "Alek<PERSON><PERSON>", "url": "https://github.com/shvaikalesh"}], "dependencies": {"toggle-selection": "^1.0.6"}, "directories": {"example": "example"}, "devDependencies": {"browserify": "^13.0.1", "minimist": "^1.2.6", "nightwatch": "^2.3.0", "@brettz9/node-static": "^0.1.1", "optimist": "^0.5.2", "selenium-server-standalone-jar": "2.53.0", "uglify-js": "^3.5.3"}}