import type { ConfigCtrlState, ThemeCtrlState } from '@web3modal/core';
/**
 * Types
 */
export type Web3ModalConfig = Omit<ConfigCtrlState, 'enableStandaloneMode'> & ThemeCtrlState & {
    walletConnectVersion: 1 | 2;
};
/**
 * Client
 */
export declare class Web3Modal {
    constructor(config: Web3ModalConfig);
    private initUi;
    openModal: (options?: import("packages/core/dist/_types/src/controllers/ModalCtrl").OpenOptions | undefined) => Promise<void>;
    closeModal: () => void;
    subscribeModal: (callback: (newState: import("packages/core/dist/_types/src/types/controllerTypes").ModalCtrlState) => void) => () => void;
    setTheme: (theme: ThemeCtrlState) => void;
}
