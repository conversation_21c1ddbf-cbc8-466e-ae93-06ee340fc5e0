declare const _default: import("vue").DefineComponent<{
    as: {
        type: StringConstructor;
        default: string;
    };
}, void, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<{
    as?: unknown;
} & {
    as: string;
} & {}>, {
    as: string;
}>;
export default _default;
//# sourceMappingURL=Motion.vue?vue&type=script&lang.d.ts.map