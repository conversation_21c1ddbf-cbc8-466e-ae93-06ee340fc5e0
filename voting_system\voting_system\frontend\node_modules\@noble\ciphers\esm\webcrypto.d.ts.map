{"version": 3, "file": "webcrypto.d.ts", "sourceRoot": "", "sources": ["../src/webcrypto.ts"], "names": [], "mappings": "AAaA,OAAO,EAAE,WAAW,EAAE,MAAM,EAAe,MAAM,YAAY,CAAC;AAE9D;;GAEG;AACH,wBAAgB,WAAW,CAAC,WAAW,SAAK,GAAG,UAAU,CASxD;AAED,wBAAgB,kBAAkB,IAAI,GAAG,CAGxC;AAED,KAAK,gBAAgB,CAAC,CAAC,SAAS,GAAG,EAAE,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,SAAS,CAC1E,IAAI,EAAE,GAAG,EACT,IAAI,EAAE,GAAG,EACT,GAAG,IAAI,EAAE,MAAM,CAAC,KACb,GAAG,GACJ,CAAC,GAAG,EAAE,UAAU,EAAE,GAAG,IAAI,EAAE,CAAC,KAAK,GAAG,GACpC,KAAK,CAAC;AAEV,KAAK,WAAW,CAAC,CAAC,SAAS,CAAC,GAAG,IAAI,EAAE,GAAG,KAAK,GAAG,IAAI,gBAAgB,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;AACnG,KAAK,eAAe,GAAG,CAAC,CAAC,GAAG,EAAE,UAAU,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,MAAM,CAAC,GAAG;IACxF,WAAW,EAAE,MAAM,CAAC;CACrB,CAAC;AAEF;;;;;;GAMG;AACH,wBAAgB,YAAY,CAAC,CAAC,SAAS,eAAe,EAAE,EAAE,EAAE,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAiB7E;AAID,eAAO,MAAM,KAAK,EAAE;IAClB,OAAO,EAAE,CAAC,GAAG,EAAE,UAAU,EAAE,GAAG,GAAG,EAAE,GAAG,EAAE,KAAK,OAAO,CAAC,UAAU,CAAC,CAAC;IACjE,OAAO,EAAE,CAAC,GAAG,EAAE,UAAU,EAAE,GAAG,GAAG,EAAE,GAAG,EAAE,KAAK,OAAO,CAAC,UAAU,CAAC,CAAC;CAwBlE,CAAC;AA2CF,wCAAwC;AACxC,eAAO,MAAM,GAAG,EAAE,CAAC,GAAG,EAAE,UAAU,EAAE,EAAE,EAAE,UAAU,KAAK,WAChC,CAAC;AACxB,wCAAwC;AACxC,eAAO,MAAM,GAAG,EAAE,CAAC,GAAG,EAAE,UAAU,EAAE,KAAK,EAAE,UAAU,KAAK,WACnC,CAAC;AACxB,wCAAwC;AACxC,eAAO,MAAM,GAAG,EAAE,CAAC,GAAG,EAAE,UAAU,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,CAAC,EAAE,UAAU,KAAK,WAC9B,CAAC"}