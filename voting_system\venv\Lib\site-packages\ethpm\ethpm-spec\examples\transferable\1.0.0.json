{"build_dependencies": {"owned": "ipfs://QmbeVyFLSuEUxiXKwSsEjef6icpdTdA4kGG9BcrJXKNKUW"}, "manifest_version": "2", "meta": {"authors": ["<PERSON>am <<EMAIL>>"], "description": "Reusable contracts which implement a privileged 'owner' model for authorization with functionality for transfering ownership.", "keywords": ["authorization"], "license": "MIT"}, "package_name": "transferable", "sources": {"./contracts/Transferable.sol": "ipfs://QmZYkdUUTwREjfy4vQc3enzu6WKk8eNyvGERqy1cNNVkAD"}, "version": "1.0.0"}