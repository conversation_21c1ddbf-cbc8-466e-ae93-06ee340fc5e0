# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2011-2012,2020
# <PERSON> <<EMAIL>>, 2015
# <PERSON> <<EMAIL>>, 2013
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON><PERSON>, 2016
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2013-2014
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-04-03 06:13+0000\n"
"Last-Translator: rahim agh <<EMAIL>>\n"
"Language-Team: Persian (http://www.transifex.com/django/django/language/"
"fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

msgid "Administrative Documentation"
msgstr "مستندات مدیریت"

msgid "Home"
msgstr "خانه"

msgid "Documentation"
msgstr "مستندات"

msgid "Bookmarklets"
msgstr "Bookmarklet ها"

msgid "Documentation bookmarklets"
msgstr "مستندات bookmarklet ها"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"برای نصب بوکمارکلت‌ها، لینک را به نوار‌ابزار بوکمارک خود بکشید، یا راست‌کلیک "
"کنید و به بوکمارکهای خود اضافه کنید. حالا شما میتوانید از هر صفحه ای در سایت "
"بوکمارکلت انتخاب کنید."

msgid "Documentation for this page"
msgstr "مستندات این صفحه"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"شما را از هر صفحه‌ای که باشید به مستندات نمایی که صفحه را ایجاد کرده می‌برد"

msgid "Tags"
msgstr "برچسب‌ها"

msgid "List of all the template tags and their functions."
msgstr "لیست همه برچسب‌های قالب‌ها و توابعشان"

msgid "Filters"
msgstr "فیلترها"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"فیلترها عملیاتی هستند که میتوان روی متغیرها و قالب‌ها اعمال شود تا خروجی را "
"تغییر دهند."

msgid "Models"
msgstr "مدل‌ها"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"مدلها توضحاتی هستند از اشیایی در سیستم و فیلدهای مرتبط. هر مدل یک لیست از "
"فیلد یک لیست از فیلدها را دارد که میتواند از طریق متغیرهای قالب در دسترس "
"قرار بگیرد."

msgid "Views"
msgstr "نمایش ها"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"هر صفحه در وبسایت عمومی با یک نمایش ساخته میشود. نمایش تعیین میکند کدام قالب "
"برای ساخت صفحه استفاده شود و کدام شیء برای قالب در دسترس باشد."

msgid "Tools for your browser to quickly access admin functionality."
msgstr "ابزار مرورگر خود را به سرعت قابلیت اداری دسترسی داشته باشید."

msgid "Please install docutils"
msgstr "لطفاً docutils نصب کنید"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"سیستم مستندات مدیر نیازمند کتابخانه‌ی <a href=\"%(link)s\">docutils</a> "
"پایتون است."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"لطفا از مدیر خود درخواست کنید تا کتابخانه‌ی <a href=\"%(link)s\">docutils</a> "
"را نصب کند."

#, python-format
msgid "Model: %(name)s"
msgstr "مدل: %(name)s"

msgid "Fields"
msgstr "فیلدها"

msgid "Field"
msgstr "فیلد"

msgid "Type"
msgstr "نوع"

msgid "Description"
msgstr "توضیحات"

msgid "Methods with arguments"
msgstr "متدهایی با آرگومانها"

msgid "Method"
msgstr "متد"

msgid "Arguments"
msgstr "آرکومانها"

msgid "Back to Model documentation"
msgstr "برگشت به مستندات مدل"

msgid "Model documentation"
msgstr "مستندات مدل"

msgid "Model groups"
msgstr "گروه‌های مدل"

msgid "Templates"
msgstr "قالب‌ها"

#, python-format
msgid "Template: %(name)s"
msgstr "قالب: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "قالب: %(name)s"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "مسیر جستجو برای قالب \"%(name)s\":"

msgid "(does not exist)"
msgstr "(وجود ندارد)"

msgid "Back to Documentation"
msgstr "برگشت به مستندات"

msgid "Template filters"
msgstr "فیلترهای قالب"

msgid "Template filter documentation"
msgstr "مستندات فیلتر قالب"

msgid "Built-in filters"
msgstr "فیلترهای از اول موجود"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"برای استفاده از این فیلترها، کد <code>%(code)s</code> را قبل از استفاده از "
"فیلتر، در قالب قرار دهید."

msgid "Template tags"
msgstr "برچسب‌های قالب"

msgid "Template tag documentation"
msgstr "مستندات برچسب قالب"

msgid "Built-in tags"
msgstr "برچسب‌های از اول موجود"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"برای استفاده از این تگ‌ها، کد <code>%(code)s</code> را قبل از استفاده از تگ، "
"در قالب قرار دهید."

#, python-format
msgid "View: %(name)s"
msgstr "نمایش: %(name)s"

msgid "Context:"
msgstr "زمینه:"

msgid "Templates:"
msgstr "قالب‌ها:"

msgid "Back to View documentation"
msgstr "برگشت به مستندات نمایش"

msgid "View documentation"
msgstr "مشاهده مستندات"

msgid "Jump to namespace"
msgstr "پرش به فضای نام"

msgid "Empty namespace"
msgstr "فضای نام خالی"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "نمایشها بر اساس فضای نام %(name)s"

msgid "Views by empty namespace"
msgstr "نمایشها بر اساس فضای نام خالی"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"نمایش تابع: <code>%(full_name)s</code>. نام: <code>%(url_name)s</code>.\n"

msgid "tag:"
msgstr "برچسب:"

msgid "filter:"
msgstr "فیلتر"

msgid "view:"
msgstr "نمایش:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "برنامه‌ی %(app_label)r پیدا نشد"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "مدل %(model_name)r در برنامهٔ %(app_label)r یافت نشد"

msgid "model:"
msgstr "مدل:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "شیء «%(app_label)s.%(data_type)s» مرتبط"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "اشیاء «%(app_label)s.%(object_name)s» مرتبط"

#, python-format
msgid "all %s"
msgstr "همهٔ %s"

#, python-format
msgid "number of %s"
msgstr "تعداد %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "به نظر نمی رسد %s یک شیء از نوع urlpattern باشد"
