{"version": 3, "file": "choose.js", "sources": ["../../src/directives/choose.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/**\n * Chooses and evaluates a template function from a list based on matching\n * the given `value` to a case.\n *\n * Cases are structured as `[caseValue, func]`. `value` is matched to\n * `caseValue` by strict equality. The first match is selected. Case values\n * can be of any type including primitives, objects, and symbols.\n *\n * This is similar to a switch statement, but as an expression and without\n * fallthrough.\n *\n * @example\n *\n * ```ts\n * render() {\n *   return html`\n *     ${choose(this.section, [\n *       ['home', () => html`<h1>Home</h1>`],\n *       ['about', () => html`<h1>About</h1>`]\n *     ],\n *     () => html`<h1>Error</h1>`)}\n *   `;\n * }\n * ```\n */\nexport const choose = <T, V>(\n  value: T,\n  cases: Array<[T, () => V]>,\n  defaultCase?: () => V\n) => {\n  for (const c of cases) {\n    const caseValue = c[0];\n    if (caseValue === value) {\n      const fn = c[1];\n      return fn();\n    }\n  }\n  return defaultCase?.();\n};\n"], "names": ["choose", "value", "cases", "defaultCase", "c", "fn"], "mappings": ";;;;;AA+Ba,MAAAA,EAAS,CACpBC,EACAC,EACAC,KAEA,IAAK,MAAMC,KAAKF,EAEd,GADkBE,EAAE,KACFH,EAEhB,OAAOI,EADID,EAAE,MAIjB,OAAOD,aAAA,EAAAA,GAAe"}