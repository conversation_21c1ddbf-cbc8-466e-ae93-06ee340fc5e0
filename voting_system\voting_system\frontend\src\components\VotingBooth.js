import React, { useState } from 'react';

const VotingBooth = ({ candidate, election, onVote, onCancel, account }) => {
    const [isVoting, setIsVoting] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');
    const [confirmed, setConfirmed] = useState(false);

    if (!candidate || !election) {
        return (
            <div className="alert alert-warning">
                No candidate or election selected. <button className="btn btn-link" onClick={onCancel}>Go back</button>
            </div>
        );
    }

    // Handle vote button click
    const handleVoteClick = () => {
        setConfirmed(true);
    };

    // Handle vote confirmation
    const handleConfirmVote = async () => {
        if (!account) {
            setError('Please connect your wallet to vote');
            return;
        }

        try {
            setIsVoting(true);
            setError('');
            
            await onVote(candidate.id, election.id);
            
            setSuccess('Your vote has been recorded successfully!');
        } catch (error) {
            console.error('Error voting:', error);
            setError('Error submitting vote. Please try again.');
        } finally {
            setIsVoting(false);
        }
    };

    return (
        <div className="row justify-content-center">
            <div className="col-md-8">
                <button className="btn btn-outline-secondary mb-4" onClick={onCancel}>
                    &larr; Back to Candidates
                </button>

                <div className="card">
                    <div className="card-header bg-primary text-white">
                        <h3 className="mb-0">Voting Booth</h3>
                    </div>
                    <div className="card-body">
                        {error && (
                            <div className="alert alert-danger" role="alert">
                                {error}
                            </div>
                        )}
                        
                        {success && (
                            <div className="alert alert-success" role="alert">
                                {success}
                                <div className="mt-3">
                                    <button className="btn btn-primary" onClick={onCancel}>
                                        Return to Candidates
                                    </button>
                                </div>
                            </div>
                        )}
                        
                        {!success && (
                            <>
                                <h4>Election: {election.title}</h4>
                                <p className="text-muted">{election.election_type.name}</p>
                                
                                <div className="card mb-4">
                                    <div className="card-header">
                                        Selected Candidate
                                    </div>
                                    <div className="card-body">
                                        <div className="row">
                                            {candidate.photo && (
                                                <div className="col-md-4">
                                                    <img 
                                                        src={candidate.photo} 
                                                        className="img-fluid rounded" 
                                                        alt={`${candidate.user.first_name} ${candidate.user.last_name}`} 
                                                    />
                                                </div>
                                            )}
                                            <div className={candidate.photo ? "col-md-8" : "col-md-12"}>
                                                <h5>{candidate.user.first_name} {candidate.user.last_name}</h5>
                                                <p className="text-muted">
                                                    {candidate.is_independent ? 'Independent' : candidate.party.name}
                                                </p>
                                                <p>{candidate.bio}</p>
                                                {candidate.constituency && (
                                                    <p><strong>Constituency:</strong> {candidate.constituency.name}</p>
                                                )}
                                                {candidate.county && (
                                                    <p><strong>County:</strong> {candidate.county.name}</p>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                {!confirmed ? (
                                    <div className="alert alert-info">
                                        <p>You are about to cast your vote for <strong>{candidate.user.first_name} {candidate.user.last_name}</strong> in the <strong>{election.title}</strong> election.</p>
                                        <p>Please review your selection carefully. Once you confirm your vote, it cannot be changed.</p>
                                        
                                        <div className="d-grid gap-2 mt-3">
                                            <button
                                                className="btn btn-primary"
                                                onClick={handleVoteClick}
                                                disabled={!account}
                                            >
                                                I Confirm My Selection
                                            </button>
                                            <button
                                                className="btn btn-outline-secondary"
                                                onClick={onCancel}
                                            >
                                                Cancel and Go Back
                                            </button>
                                        </div>
                                        
                                        {!account && (
                                            <div className="alert alert-warning mt-3">
                                                Please connect your Ethereum wallet to vote.
                                            </div>
                                        )}
                                    </div>
                                ) : (
                                    <div className="alert alert-warning">
                                        <p><strong>Final Confirmation</strong></p>
                                        <p>Are you absolutely sure you want to cast your vote for <strong>{candidate.user.first_name} {candidate.user.last_name}</strong>?</p>
                                        <p>This action cannot be undone and your vote will be permanently recorded on the blockchain.</p>
                                        
                                        <div className="d-grid gap-2 mt-3">
                                            <button
                                                className="btn btn-danger"
                                                onClick={handleConfirmVote}
                                                disabled={isVoting}
                                            >
                                                {isVoting ? (
                                                    <>
                                                        <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                        <span className="ms-2">Processing Vote...</span>
                                                    </>
                                                ) : (
                                                    'Yes, Cast My Vote Now'
                                                )}
                                            </button>
                                            <button
                                                className="btn btn-outline-secondary"
                                                onClick={() => setConfirmed(false)}
                                                disabled={isVoting}
                                            >
                                                Go Back
                                            </button>
                                        </div>
                                    </div>
                                )}
                                
                                <div className="card mt-4">
                                    <div className="card-header">
                                        Blockchain Information
                                    </div>
                                    <div className="card-body">
                                        <p>Your vote will be securely recorded on the Ethereum blockchain.</p>
                                        <p>Blockchain Address: <small className="text-muted">{account}</small></p>
                                        <p>Election Contract ID: <small className="text-muted">{election.contract_election_id}</small></p>
                                    </div>
                                </div>
                            </>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default VotingBooth;
