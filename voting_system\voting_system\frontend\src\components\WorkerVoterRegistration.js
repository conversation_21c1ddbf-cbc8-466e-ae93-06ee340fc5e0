import React, { useState, useEffect } from 'react';

const WorkerVoterRegistration = ({ onBack, onSuccess }) => {
    const [formData, setFormData] = useState({
        id_number: '',
        date_of_birth: '',
        phone_number: '',
        constituency: '',
        polling_station: '',
        id_picture: null,
        voter_photo: null
    });
    const [counties, setCounties] = useState([]);
    const [constituencies, setConstituencies] = useState([]);
    const [pollingStations, setPollingStations] = useState([]);
    const [selectedCounty, setSelectedCounty] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState('');
    const [formErrors, setFormErrors] = useState({});
    const [idVerificationStatus, setIdVerificationStatus] = useState({
        checked: false,
        valid: false,
        message: ''
    });

    // Image preview states
    const [idPicturePreview, setIdPicturePreview] = useState(null);
    const [voterPhotoPreview, setVoterPhotoPreview] = useState(null);

    useEffect(() => {
        fetchCounties();
    }, []);

    useEffect(() => {
        if (selectedCounty) {
            fetchConstituencies(selectedCounty);
        } else {
            setConstituencies([]);
        }
    }, [selectedCounty]);

    useEffect(() => {
        if (formData.constituency) {
            fetchPollingStations(formData.constituency);
        } else {
            setPollingStations([]);
        }
    }, [formData.constituency]);

    const fetchCounties = async () => {
        try {
            const response = await fetch('/api/counties/');
            if (response.ok) {
                const data = await response.json();
                // Make sure data is an array
                if (Array.isArray(data)) {
                    setCounties(data);
                } else if (data.results && Array.isArray(data.results)) {
                    // Handle paginated response
                    setCounties(data.results);
                } else {
                    console.warn('Counties data is not an array:', data);
                    setCounties([]);
                }
            } else {
                console.error('Failed to fetch counties:', response.status);
                setCounties([]);
            }
        } catch (error) {
            console.error('Error fetching counties:', error);
            setCounties([]);
        }
    };

    const fetchConstituencies = async (countyId) => {
        try {
            const response = await fetch(`/api/counties/${countyId}/constituencies/`);
            if (response.ok) {
                const data = await response.json();
                // Make sure data is an array
                if (Array.isArray(data)) {
                    setConstituencies(data);
                } else if (data.results && Array.isArray(data.results)) {
                    // Handle paginated response
                    setConstituencies(data.results);
                } else {
                    console.warn('Constituencies data is not an array:', data);
                    setConstituencies([]);
                }
            } else {
                console.error('Failed to fetch constituencies:', response.status);
                setConstituencies([]);
            }
        } catch (error) {
            console.error('Error fetching constituencies:', error);
            setConstituencies([]);
        }
    };

    const fetchPollingStations = async (constituencyId) => {
        try {
            const response = await fetch(`/api/constituencies/${constituencyId}/polling-stations/`);
            if (response.ok) {
                const data = await response.json();
                // Make sure data is an array
                if (Array.isArray(data)) {
                    setPollingStations(data);
                } else if (data.results && Array.isArray(data.results)) {
                    // Handle paginated response
                    setPollingStations(data.results);
                } else {
                    console.warn('Polling stations data is not an array:', data);
                    setPollingStations([]);
                }
            } else {
                console.error('Failed to fetch polling stations:', response.status);
                setPollingStations([]);
            }
        } catch (error) {
            console.error('Error fetching polling stations:', error);
            setPollingStations([]);
        }
    };

    const checkIdNumber = async (idNumber) => {
        if (!idNumber || idNumber.length < 8) return;

        try {
            const response = await fetch('/api/check-id-number/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ id_number: idNumber }),
            });

            const data = await response.json();
            setIdVerificationStatus({
                checked: true,
                valid: data.valid && !data.verified,
                message: data.message
            });

            if (data.verified) {
                setFormErrors(prev => ({
                    ...prev,
                    id_number: 'This ID number is already registered.'
                }));
            } else if (!data.valid) {
                setFormErrors(prev => ({
                    ...prev,
                    id_number: 'This ID number is not in the national database.'
                }));
            } else {
                setFormErrors(prev => ({
                    ...prev,
                    id_number: ''
                }));
            }
        } catch (error) {
            console.error('Error checking ID number:', error);
        }
    };

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));

        // Clear errors for this field
        if (formErrors[name]) {
            setFormErrors(prev => ({
                ...prev,
                [name]: ''
            }));
        }

        // Check ID number when it's 8 digits
        if (name === 'id_number' && value.length === 8) {
            checkIdNumber(value);
        }

        // Reset constituency and polling station when county changes
        if (name === 'constituency') {
            setFormData(prev => ({
                ...prev,
                polling_station: ''
            }));
        }
    };

    const handleCountyChange = (e) => {
        const value = e.target.value;
        setSelectedCounty(value);
        setFormData(prev => ({
            ...prev,
            constituency: '',
            polling_station: ''
        }));
    };

    const handleFileChange = (e) => {
        const { name, files } = e.target;
        const file = files[0];

        if (file) {
            // Validate file type
            if (!file.type.startsWith('image/')) {
                setFormErrors(prev => ({
                    ...prev,
                    [name]: 'Please select a valid image file.'
                }));
                return;
            }

            // Validate file size (max 5MB)
            if (file.size > 5 * 1024 * 1024) {
                setFormErrors(prev => ({
                    ...prev,
                    [name]: 'File size must be less than 5MB.'
                }));
                return;
            }

            setFormData(prev => ({
                ...prev,
                [name]: file
            }));

            // Create preview
            const reader = new FileReader();
            reader.onload = (e) => {
                if (name === 'id_picture') {
                    setIdPicturePreview(e.target.result);
                } else if (name === 'voter_photo') {
                    setVoterPhotoPreview(e.target.result);
                }
            };
            reader.readAsDataURL(file);

            // Clear error
            setFormErrors(prev => ({
                ...prev,
                [name]: ''
            }));
        }
    };

    const validateForm = () => {
        const errors = {};

        if (!formData.id_number) {
            errors.id_number = 'ID number is required';
        } else if (!/^\d{8}$/.test(formData.id_number)) {
            errors.id_number = 'ID number must be 8 digits';
        } else if (!idVerificationStatus.valid) {
            errors.id_number = 'Invalid or already registered ID number';
        }

        if (!formData.date_of_birth) {
            errors.date_of_birth = 'Date of birth is required';
        } else {
            const dob = new Date(formData.date_of_birth);
            const today = new Date();
            const age = today.getFullYear() - dob.getFullYear();
            if (age < 18) {
                errors.date_of_birth = 'Voter must be at least 18 years old';
            }
        }

        if (!formData.phone_number) {
            errors.phone_number = 'Phone number is required';
        } else if (!/^\+254\d{9}$/.test(formData.phone_number)) {
            errors.phone_number = 'Phone number must be in format +254XXXXXXXXX';
        }

        if (!formData.constituency) {
            errors.constituency = 'Constituency is required';
        }

        if (!formData.polling_station) {
            errors.polling_station = 'Polling station is required';
        }

        if (!formData.id_picture) {
            errors.id_picture = 'ID picture is required';
        }

        if (!formData.voter_photo) {
            errors.voter_photo = 'Voter photo is required';
        }

        return errors;
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        const errors = validateForm();
        if (Object.keys(errors).length > 0) {
            setFormErrors(errors);
            return;
        }

        setIsLoading(true);
        setError('');

        try {
            const token = localStorage.getItem('workerToken');
            const formDataToSend = new FormData();

            // Append all form fields
            Object.keys(formData).forEach(key => {
                if (formData[key] !== null && formData[key] !== '') {
                    formDataToSend.append(key, formData[key]);
                }
            });

            const response = await fetch('/api/worker/register-voter/', {
                method: 'POST',
                headers: {
                    'Authorization': `Token ${token}`,
                },
                body: formDataToSend,
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'Registration failed');
            }

            // Success
            if (onSuccess) {
                onSuccess(data);
            }

        } catch (error) {
            console.error('Registration error:', error);
            setError(error.message || 'Registration failed. Please try again.');
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="container mt-4">
            <div className="row justify-content-center">
                <div className="col-md-10">
                    <div className="card">
                        <div className="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                            <h3 className="mb-0">Register New Voter</h3>
                            <button
                                type="button"
                                className="btn btn-outline-light btn-sm"
                                onClick={onBack}
                            >
                                Back to Dashboard
                            </button>
                        </div>
                        <div className="card-body">
                            {error && (
                                <div className="alert alert-danger" role="alert">
                                    {error}
                                </div>
                            )}

                            <form onSubmit={handleSubmit}>
                                <div className="row">
                                    {/* Left Column - Personal Information */}
                                    <div className="col-md-6">
                                        <h5 className="mb-3">Personal Information</h5>

                                        <div className="mb-3">
                                            <label htmlFor="id_number" className="form-label">National ID Number</label>
                                            <div className="input-group">
                                                <input
                                                    type="text"
                                                    className={`form-control ${formErrors.id_number ? 'is-invalid' : ''} ${idVerificationStatus.checked && idVerificationStatus.valid ? 'is-valid' : ''}`}
                                                    id="id_number"
                                                    name="id_number"
                                                    value={formData.id_number}
                                                    onChange={handleChange}
                                                    placeholder="Enter 8-digit ID number"
                                                    maxLength="8"
                                                />
                                                <button
                                                    className="btn btn-outline-secondary"
                                                    type="button"
                                                    onClick={() => checkIdNumber(formData.id_number)}
                                                    disabled={!formData.id_number || formData.id_number.length < 8}
                                                >
                                                    Verify
                                                </button>
                                            </div>
                                            {formErrors.id_number && (
                                                <div className="invalid-feedback d-block">{formErrors.id_number}</div>
                                            )}
                                            {idVerificationStatus.checked && !formErrors.id_number && (
                                                <div className={`mt-1 ${idVerificationStatus.valid ? 'text-success' : 'text-warning'}`}>
                                                    {idVerificationStatus.message}
                                                </div>
                                            )}
                                        </div>

                                        <div className="mb-3">
                                            <label htmlFor="date_of_birth" className="form-label">Date of Birth</label>
                                            <input
                                                type="date"
                                                className={`form-control ${formErrors.date_of_birth ? 'is-invalid' : ''}`}
                                                id="date_of_birth"
                                                name="date_of_birth"
                                                value={formData.date_of_birth}
                                                onChange={handleChange}
                                            />
                                            {formErrors.date_of_birth && (
                                                <div className="invalid-feedback">{formErrors.date_of_birth}</div>
                                            )}
                                        </div>

                                        <div className="mb-3">
                                            <label htmlFor="phone_number" className="form-label">Phone Number</label>
                                            <input
                                                type="text"
                                                className={`form-control ${formErrors.phone_number ? 'is-invalid' : ''}`}
                                                id="phone_number"
                                                name="phone_number"
                                                value={formData.phone_number}
                                                onChange={handleChange}
                                                placeholder="+254712345678"
                                            />
                                            {formErrors.phone_number && (
                                                <div className="invalid-feedback">{formErrors.phone_number}</div>
                                            )}
                                        </div>
                                    </div>

                                    {/* Right Column - Location & Images */}
                                    <div className="col-md-6">
                                        <h5 className="mb-3">Location & Documents</h5>

                                        <div className="mb-3">
                                            <label htmlFor="county" className="form-label">County</label>
                                            <select
                                                className="form-select"
                                                id="county"
                                                value={selectedCounty}
                                                onChange={handleCountyChange}
                                            >
                                                <option value="">Select County</option>
                                                {Array.isArray(counties) && counties.map(county => (
                                                    <option key={county.id} value={county.id}>
                                                        {county.name}
                                                    </option>
                                                ))}
                                            </select>
                                        </div>

                                        <div className="mb-3">
                                            <label htmlFor="constituency" className="form-label">Constituency</label>
                                            <select
                                                className={`form-select ${formErrors.constituency ? 'is-invalid' : ''}`}
                                                id="constituency"
                                                name="constituency"
                                                value={formData.constituency}
                                                onChange={handleChange}
                                                disabled={!selectedCounty}
                                            >
                                                <option value="">Select Constituency</option>
                                                {Array.isArray(constituencies) && constituencies.map(constituency => (
                                                    <option key={constituency.id} value={constituency.id}>
                                                        {constituency.name}
                                                    </option>
                                                ))}
                                            </select>
                                            {formErrors.constituency && (
                                                <div className="invalid-feedback">{formErrors.constituency}</div>
                                            )}
                                        </div>

                                        <div className="mb-3">
                                            <label htmlFor="polling_station" className="form-label">Polling Station</label>
                                            <select
                                                className={`form-select ${formErrors.polling_station ? 'is-invalid' : ''}`}
                                                id="polling_station"
                                                name="polling_station"
                                                value={formData.polling_station}
                                                onChange={handleChange}
                                                disabled={!formData.constituency}
                                            >
                                                <option value="">Select Polling Station</option>
                                                {Array.isArray(pollingStations) && pollingStations.map(station => (
                                                    <option key={station.id} value={station.id}>
                                                        {station.name}
                                                    </option>
                                                ))}
                                            </select>
                                            {formErrors.polling_station && (
                                                <div className="invalid-feedback">{formErrors.polling_station}</div>
                                            )}
                                        </div>
                                    </div>
                                </div>

                                {/* Image Upload Section */}
                                <div className="row mt-4">
                                    <div className="col-12">
                                        <h5 className="mb-3">Document Upload</h5>
                                    </div>

                                    <div className="col-md-6">
                                        <div className="mb-3">
                                            <label htmlFor="id_picture" className="form-label">ID Picture</label>
                                            <input
                                                type="file"
                                                className={`form-control ${formErrors.id_picture ? 'is-invalid' : ''}`}
                                                id="id_picture"
                                                name="id_picture"
                                                accept="image/*"
                                                onChange={handleFileChange}
                                            />
                                            {formErrors.id_picture && (
                                                <div className="invalid-feedback">{formErrors.id_picture}</div>
                                            )}
                                            {idPicturePreview && (
                                                <div className="mt-2">
                                                    <img
                                                        src={idPicturePreview}
                                                        alt="ID Preview"
                                                        className="img-thumbnail"
                                                        style={{ maxWidth: '200px', maxHeight: '150px' }}
                                                    />
                                                </div>
                                            )}
                                        </div>
                                    </div>

                                    <div className="col-md-6">
                                        <div className="mb-3">
                                            <label htmlFor="voter_photo" className="form-label">Voter Photo</label>
                                            <input
                                                type="file"
                                                className={`form-control ${formErrors.voter_photo ? 'is-invalid' : ''}`}
                                                id="voter_photo"
                                                name="voter_photo"
                                                accept="image/*"
                                                onChange={handleFileChange}
                                            />
                                            {formErrors.voter_photo && (
                                                <div className="invalid-feedback">{formErrors.voter_photo}</div>
                                            )}
                                            {voterPhotoPreview && (
                                                <div className="mt-2">
                                                    <img
                                                        src={voterPhotoPreview}
                                                        alt="Voter Preview"
                                                        className="img-thumbnail"
                                                        style={{ maxWidth: '200px', maxHeight: '150px' }}
                                                    />
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </div>

                                <div className="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                                    <button
                                        type="button"
                                        className="btn btn-secondary me-md-2"
                                        onClick={onBack}
                                    >
                                        Cancel
                                    </button>
                                    <button
                                        type="submit"
                                        className="btn btn-primary"
                                        disabled={isLoading}
                                    >
                                        {isLoading ? (
                                            <>
                                                <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                <span className="ms-2">Registering...</span>
                                            </>
                                        ) : (
                                            'Register Voter'
                                        )}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default WorkerVoterRegistration;
