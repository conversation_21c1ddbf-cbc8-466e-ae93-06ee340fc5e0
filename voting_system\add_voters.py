import os
import django
import datetime
import random

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'voting_system.settings')
django.setup()

from django.contrib.auth.models import User
from voting.models import Voter, County, Constituency, PollingStation
from django.utils import timezone

def generate_phone_number():
    """Generate a random Kenyan phone number in the format +254XXXXXXXXX"""
    prefixes = ['7', '1']
    prefix = random.choice(prefixes)
    suffix = ''.join([str(random.randint(0, 9)) for _ in range(8)])
    return f"+254{prefix}{suffix}"

def generate_id_number():
    """Generate a random 8-digit ID number"""
    return ''.join([str(random.randint(0, 9)) for _ in range(8)])

def generate_date_of_birth():
    """Generate a random date of birth for someone 18-80 years old"""
    today = timezone.now().date()
    min_age = 18
    max_age = 80
    days_in_year = 365.25

    min_days = int(min_age * days_in_year)
    max_days = int(max_age * days_in_year)

    random_days = random.randint(min_days, max_days)
    return today - datetime.timedelta(days=random_days)

def add_voters(num_voters=10):
    """Add test voters to the database"""
    print(f"Adding {num_voters} test voters...")

    # Get all counties, constituencies, and polling stations
    counties = list(County.objects.all())
    if not counties:
        print("No counties found. Please add counties first.")
        return

    constituencies = list(Constituency.objects.all())
    if not constituencies:
        print("No constituencies found. Please add constituencies first.")
        return

    polling_stations = list(PollingStation.objects.all())
    if not polling_stations:
        print("No polling stations found. Please add polling stations first.")
        return

    # Create voters
    for i in range(num_voters):
        try:
            # Generate random data
            first_name = f"Voter{i+1}"
            last_name = f"Test{i+1}"
            username = f"voter{i+1}"
            email = f"voter{i+1}@example.com"
            id_number = generate_id_number()
            phone_number = generate_phone_number()
            date_of_birth = generate_date_of_birth()

            # Use specific constituencies and polling stations that we know exist
            try:
                # Get Nairobi county
                county = County.objects.get(name='Nairobi')
                # Get Westlands constituency
                constituency = Constituency.objects.get(name='Westlands')
                # Get Westlands Primary School polling station
                polling_station = PollingStation.objects.get(name='Westlands Primary School')

                print(f"Using county: {county.name}, constituency: {constituency.name}, polling station: {polling_station.name}")
            except Exception as e:
                print(f"Error getting locations: {str(e)}")
                raise

            # Create user
            user, created = User.objects.get_or_create(
                username=username,
                defaults={
                    'email': email,
                    'first_name': first_name,
                    'last_name': last_name,
                    'is_active': True
                }
            )

            if created:
                user.set_password('password123')
                user.save()
                print(f"Created user {username}")
            else:
                print(f"User {username} already exists")

            # Create voter
            voter, created = Voter.objects.get_or_create(
                user=user,
                defaults={
                    'id_number': id_number,
                    'phone_number': phone_number,
                    'date_of_birth': date_of_birth,
                    'constituency': constituency,
                    'polling_station': polling_station,
                    'is_verified': True
                }
            )

            if created:
                print(f"Created voter for {username} at {polling_station.name}")
            else:
                print(f"Voter for {username} already exists")

        except Exception as e:
            print(f"Error creating voter {i+1}: {str(e)}")

    print(f"Added {num_voters} test voters successfully!")

if __name__ == "__main__":
    add_voters(10)  # Add 10 test voters
