{"version": 3, "file": "browser.js", "sourceRoot": "", "sources": ["../../src/browser.ts"], "names": [], "mappings": ";;;;AAEA,uFAAiE;AACjE,qFAA+D;AAC/D,mDAOwB;AAExB,SAAgB,SAAS,CACvB,SAAkB;IAElB,OAAO,IAAA,uBAAM,EAAC,SAAS,CAAC,CAAC;AAC3B,CAAC;AAJD,8BAIC;AAED,SAAgB,QAAQ;IACtB,MAAM,GAAG,GAAG,SAAS,EAAE,CAAC;IACxB,OAAO,GAAG,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;AAC5C,CAAC;AAHD,4BAGC;AAED,SAAgB,SAAS;IACvB,MAAM,EAAE,GAAG,QAAQ,EAAE,CAAC;IACtB,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;AAC3D,CAAC;AAHD,8BAGC;AAED,SAAgB,KAAK;IACnB,MAAM,EAAE,GAAG,QAAQ,EAAE,CAAC;IACtB,OAAO,EAAE;QACP,CAAC,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC;YAC9B,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,SAAS,CAAC,cAAc,GAAG,CAAC,CAAC;QACtE,CAAC,CAAC,KAAK,CAAC;AACZ,CAAC;AAND,sBAMC;AAED,SAAgB,QAAQ;IACtB,MAAM,EAAE,GAAG,QAAQ,EAAE,CAAC;IACtB,OAAO,EAAE,CAAC,CAAC,CAAC,SAAS,EAAE,IAAI,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;AAC7C,CAAC;AAHD,4BAGC;AAED,SAAgB,MAAM;IACpB,MAAM,GAAG,GAAG,SAAS,EAAE,CAAC;IACxB,MAAM,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC;IAC3E,OAAO,MAAM,CAAC;AAChB,CAAC;AAJD,wBAIC;AAED,SAAgB,SAAS;IACvB,MAAM,MAAM,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,IAAA,oBAAY,GAAE,CAAC;IAC7C,OAAO,MAAM,CAAC;AAChB,CAAC;AAHD,8BAGC;AAEY,QAAA,aAAa,GAAG,aAAa,CAAC,aAAa,CAAC;AAE5C,QAAA,oBAAoB,GAAG,aAAa,CAAC,oBAAoB,CAAC;AAE1D,QAAA,kBAAkB,GAAG,aAAa,CAAC,kBAAkB,CAAC;AAEtD,QAAA,WAAW,GAAG,aAAa,CAAC,WAAW,CAAC;AAExC,QAAA,mBAAmB,GAAG,aAAa,CAAC,mBAAmB,CAAC;AAExD,QAAA,YAAY,GAAG,aAAa,CAAC,YAAY,CAAC;AAE1C,QAAA,kBAAkB,GAAG,aAAa,CAAC,kBAAkB,CAAC;AAEtD,QAAA,WAAW,GAAG,aAAa,CAAC,WAAW,CAAC;AAExC,QAAA,gBAAgB,GAAG,aAAa,CAAC,gBAAgB,CAAC;AAElD,QAAA,SAAS,GAAG,aAAa,CAAC,SAAS,CAAC;AAEpC,QAAA,sBAAsB,GAAG,aAAa,CAAC,sBAAsB,CAAC;AAE9D,QAAA,eAAe,GAAG,aAAa,CAAC,eAAe,CAAC;AAE7D,SAAgB,aAAa;IAC3B,OAAO,cAAc,CAAC,iBAAiB,EAAE,CAAC;AAC5C,CAAC;AAFD,sCAEC"}