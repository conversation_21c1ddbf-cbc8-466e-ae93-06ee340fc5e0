# Changelog
All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](http://keepachangelog.com/en/1.0.0/) 
(modification: no type change headlines) and this project adheres to 
[Semantic Versioning](http://semver.org/spec/v2.0.0.html).


## [1.7.1] - 2018-02-15
- Fix ``browserify`` issue blocking updates for packages depending on ``ethereumjs-block``
  library, PR [#40](https://github.com/ethereumjs/ethereumjs-block/pull/40)
- Updated ``ethereumjs/common`` dependency, PR [#38](https://github.com/ethereumjs/ethereumjs-block/pull/38)

[1.7.1]: https://github.com/ethereumjs/ethereumjs-vm/compare/v1.7.0...v1.7.1

## [1.7.0] - 2017-10-11
- ``Metro-Byzantium`` compatible
- New difficulty formula (EIP 100)
- Difficulty bomb delay (EIP 649)
- Removed ``isHomestead``, ``isHomesteadReprice`` from API methods

[1.7.0]: https://github.com/ethereumjs/ethereumjs-vm/compare/v1.6.0...v1.7.0

## [1.6.0] - 2017-07-12
- Breakout header-from-rpc as separate module

[1.6.0]: https://github.com/ethereumjs/ethereumjs-block/compare/v1.5.1...v1.6.0

## [1.5.1] - 2017-06-04
- Dev dependency updates
- BN for gas limit

[1.5.1]: https://github.com/ethereumjs/ethereumjs-block/compare/v1.5.0...v1.5.1

## Older releases:

- [1.5.0](https://github.com/ethereumjs/ethereumjs-block/compare/v1.4.0...v1.5.0) - 2017-01-31
- [1.4.0](https://github.com/ethereumjs/ethereumjs-block/compare/v1.3.1...v1.4.0) - 2016-12-15
- [1.3.1](https://github.com/ethereumjs/ethereumjs-block/compare/v1.3.0...v1.3.1) - 2016-10-14
- [1.3.0](https://github.com/ethereumjs/ethereumjs-block/compare/v1.2.2...v1.3.0) - 2017-10-11


