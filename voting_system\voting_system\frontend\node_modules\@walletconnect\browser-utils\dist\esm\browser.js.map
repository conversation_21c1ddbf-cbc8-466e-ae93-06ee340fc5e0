{"version": 3, "file": "browser.js", "sourceRoot": "", "sources": ["../../src/browser.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,cAAc,MAAM,gCAAgC,CAAC;AACjE,OAAO,KAAK,aAAa,MAAM,+BAA+B,CAAC;AAC/D,OAAO,EACL,MAAM,GAMP,MAAM,gBAAgB,CAAC;AAExB,MAAM,UAAU,SAAS,CACvB,SAAkB;IAElB,OAAO,MAAM,CAAC,SAAS,CAAC,CAAC;AAC3B,CAAC;AAED,MAAM,UAAU,QAAQ;IACtB,MAAM,GAAG,GAAG,SAAS,EAAE,CAAC;IACxB,OAAO,GAAG,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;AAC5C,CAAC;AAED,MAAM,UAAU,SAAS;IACvB,MAAM,EAAE,GAAG,QAAQ,EAAE,CAAC;IACtB,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;AAC3D,CAAC;AAED,MAAM,UAAU,KAAK;IACnB,MAAM,EAAE,GAAG,QAAQ,EAAE,CAAC;IACtB,OAAO,EAAE;QACP,CAAC,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC;YAC9B,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,SAAS,CAAC,cAAc,GAAG,CAAC,CAAC;QACtE,CAAC,CAAC,KAAK,CAAC;AACZ,CAAC;AAED,MAAM,UAAU,QAAQ;IACtB,MAAM,EAAE,GAAG,QAAQ,EAAE,CAAC;IACtB,OAAO,EAAE,CAAC,CAAC,CAAC,SAAS,EAAE,IAAI,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;AAC7C,CAAC;AAED,MAAM,UAAU,MAAM;IACpB,MAAM,GAAG,GAAG,SAAS,EAAE,CAAC;IACxB,MAAM,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC;IAC3E,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,MAAM,UAAU,SAAS;IACvB,MAAM,MAAM,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,YAAY,EAAE,CAAC;IAC7C,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,MAAM,CAAC,MAAM,aAAa,GAAG,aAAa,CAAC,aAAa,CAAC;AAEzD,MAAM,CAAC,MAAM,oBAAoB,GAAG,aAAa,CAAC,oBAAoB,CAAC;AAEvE,MAAM,CAAC,MAAM,kBAAkB,GAAG,aAAa,CAAC,kBAAkB,CAAC;AAEnE,MAAM,CAAC,MAAM,WAAW,GAAG,aAAa,CAAC,WAAW,CAAC;AAErD,MAAM,CAAC,MAAM,mBAAmB,GAAG,aAAa,CAAC,mBAAmB,CAAC;AAErE,MAAM,CAAC,MAAM,YAAY,GAAG,aAAa,CAAC,YAAY,CAAC;AAEvD,MAAM,CAAC,MAAM,kBAAkB,GAAG,aAAa,CAAC,kBAAkB,CAAC;AAEnE,MAAM,CAAC,MAAM,WAAW,GAAG,aAAa,CAAC,WAAW,CAAC;AAErD,MAAM,CAAC,MAAM,gBAAgB,GAAG,aAAa,CAAC,gBAAgB,CAAC;AAE/D,MAAM,CAAC,MAAM,SAAS,GAAG,aAAa,CAAC,SAAS,CAAC;AAEjD,MAAM,CAAC,MAAM,sBAAsB,GAAG,aAAa,CAAC,sBAAsB,CAAC;AAE3E,MAAM,CAAC,MAAM,eAAe,GAAG,aAAa,CAAC,eAAe,CAAC;AAE7D,MAAM,UAAU,aAAa;IAC3B,OAAO,cAAc,CAAC,iBAAiB,EAAE,CAAC;AAC5C,CAAC"}