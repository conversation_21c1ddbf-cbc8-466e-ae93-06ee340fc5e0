var n,t,r,u=require("preact"),o=0,i=[],c=u.options.__r,e=u.options.diffed,f=u.options.__c,a=u.options.unmount;function p(n,r){u.options.__h&&u.options.__h(t,n,o||r),o=0;var i=t.__H||(t.__H={__:[],__h:[]});return n>=i.__.length&&i.__.push({}),i.__[n]}function v(n){return o=1,s(T,n)}function s(r,u,o){var i=p(n++,2);return i.__c||(i.__c=t,i.__=[o?o(u):T(void 0,u),function(n){var t=r(i.__[0],n);i.__[0]!==t&&(i.__[0]=t,i.__c.setState({}))}]),i.__}function x(r,o){var i=p(n++,4);!u.options.__s&&h(i.__H,o)&&(i.__=r,i.__H=o,t.__h.push(i))}function l(t,r){var u=p(n++,7);return h(u.__H,r)?(u.__H=r,u.__h=t,u.__=t()):u.__}function m(){i.some(function(n){if(n.__P)try{n.__H.__h.forEach(y),n.__H.__h.forEach(d),n.__H.__h=[]}catch(t){return n.__H.__h=[],u.options.__e(t,n.__v),!0}}),i=[]}function y(n){n.t&&n.t()}function d(n){var t=n.__();"function"==typeof t&&(n.t=t)}function h(n,t){return!n||t.some(function(t,r){return t!==n[r]})}function T(n,t){return"function"==typeof t?t(n):t}u.options.__r=function(r){c&&c(r),n=0,(t=r.__c).__H&&(t.__H.__h.forEach(y),t.__H.__h.forEach(d),t.__H.__h=[])},u.options.diffed=function(n){e&&e(n);var t=n.__c;if(t){var o=t.__H;o&&o.__h.length&&(1!==i.push(t)&&r===u.options.requestAnimationFrame||((r=u.options.requestAnimationFrame)||function(n){var t,r=function(){clearTimeout(u),cancelAnimationFrame(t),setTimeout(n)},u=setTimeout(r,100);"undefined"!=typeof window&&(t=requestAnimationFrame(r))})(m))}},u.options.__c=function(n,t){t.some(function(n){try{n.__h.forEach(y),n.__h=n.__h.filter(function(n){return!n.__||d(n)})}catch(r){t.some(function(n){n.__h&&(n.__h=[])}),t=[],u.options.__e(r,n.__v)}}),f&&f(n,t)},u.options.unmount=function(n){a&&a(n);var t=n.__c;if(t){var r=t.__H;if(r)try{r.__.forEach(function(n){return n.t&&n.t()})}catch(n){u.options.__e(n,t.__v)}}},exports.useState=v,exports.useReducer=s,exports.useEffect=function(r,o){var i=p(n++,3);!u.options.__s&&h(i.__H,o)&&(i.__=r,i.__H=o,t.__H.__h.push(i))},exports.useLayoutEffect=x,exports.useRef=function(n){return o=5,l(function(){return{current:n}},[])},exports.useImperativeHandle=function(n,t,r){o=6,x(function(){"function"==typeof n?n(t()):n&&(n.current=t())},null==r?r:r.concat(n))},exports.useMemo=l,exports.useCallback=function(n,t){return o=8,l(function(){return n},t)},exports.useContext=function(r){var u=t.context[r.__c],o=p(n++,9);return o.__c=r,u?(null==o.__&&(o.__=!0,u.sub(t)),u.props.value):r.__},exports.useDebugValue=function(n,t){u.options.useDebugValue&&u.options.useDebugValue(t?t(n):n)},exports.useErrorBoundary=function(r){var u=p(n++,10),o=v();return u.__=r,t.componentDidCatch||(t.componentDidCatch=function(n){u.__&&u.__(n),o[1](n)}),[o[0],function(){o[1](void 0)}]};
//# sourceMappingURL=hooks.js.map
