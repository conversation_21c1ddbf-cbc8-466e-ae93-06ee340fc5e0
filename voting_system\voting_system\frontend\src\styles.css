/* Global Styles */
body {
    font-family: 'Roboto', sans-serif;
    background-color: #f8f9fa;
    color: #333;
    padding-top: 20px;
    padding-bottom: 40px;
}

.container {
    max-width: 1200px;
}

/* Card Styles */
.card {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    margin-bottom: 1.5rem;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.card-header {
    font-weight: 600;
    border-bottom: none;
}

.card-footer {
    background-color: white;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Election Cards */
.election-card .card-title {
    color: #1a237e;
    font-weight: 600;
}

.badge {
    padding: 0.5rem 0.75rem;
    border-radius: 1rem;
}

/* Candidate Cards */
.candidate-card {
    height: 100%;
}

.candidate-card .card-header {
    background-color: #1a237e;
    color: white;
    font-weight: 600;
}

.candidate-photo {
    height: 200px;
    object-fit: cover;
}

/* Buttons */
.btn-primary {
    background-color: #1a237e;
    border-color: #1a237e;
}

.btn-primary:hover, .btn-primary:focus {
    background-color: #283593;
    border-color: #283593;
}

.btn-outline-primary {
    color: #1a237e;
    border-color: #1a237e;
}

.btn-outline-primary:hover, .btn-outline-primary:focus {
    background-color: #1a237e;
    border-color: #1a237e;
}

/* Kenya Flag Colors */
.kenya-flag-colors {
    background: linear-gradient(to right, #000000 25%, #ffffff 25%, #ffffff 50%, #ff0000 50%, #ff0000 75%, #006600 75%);
    height: 5px;
    margin-bottom: 1.5rem;
}

/* Loading Spinner */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 300px;
}

.loading-spinner {
    width: 3rem;
    height: 3rem;
    border-width: 0.25rem;
}

/* Results Table */
.results-table th {
    background-color: #f8f9fa;
}

.results-table .winner {
    background-color: rgba(40, 167, 69, 0.1);
    font-weight: 600;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container {
        padding-left: 15px;
        padding-right: 15px;
    }
}
