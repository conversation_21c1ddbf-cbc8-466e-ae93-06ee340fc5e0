{"version": 3, "file": "format.js", "sourceRoot": "", "sources": ["../../src/format.ts"], "names": [], "mappings": ";;;AAAA,mCAA2F;AAC3F,2CAA2D;AAG3D,SAAgB,SAAS,CAAC,OAAO,GAAG,CAAC;IACnC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;IAChD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC;IAChE,OAAO,IAAI,GAAG,KAAK,CAAC;AACtB,CAAC;AAJD,8BAIC;AAED,SAAgB,cAAc,CAAC,OAAO,GAAG,CAAC;IACxC,OAAO,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;AACpC,CAAC;AAFD,wCAEC;AAED,SAAgB,oBAAoB,CAClC,MAAc,EACd,MAAS,EACT,EAAW;IAEX,OAAO;QACL,EAAE,EAAE,EAAE,IAAI,SAAS,EAAE;QACrB,OAAO,EAAE,KAAK;QACd,MAAM;QACN,MAAM;KACP,CAAC;AACJ,CAAC;AAXD,oDAWC;AAED,SAAgB,mBAAmB,CAAU,EAAU,EAAE,MAAS;IAChE,OAAO;QACL,EAAE;QACF,OAAO,EAAE,KAAK;QACd,MAAM;KACP,CAAC;AACJ,CAAC;AAND,kDAMC;AAED,SAAgB,kBAAkB,CAChC,EAAU,EACV,KAA8B,EAC9B,IAAa;IAEb,OAAO;QACL,EAAE;QACF,OAAO,EAAE,KAAK;QACd,KAAK,EAAE,kBAAkB,CAAC,KAAK,EAAE,IAAI,CAAC;KACvC,CAAC;AACJ,CAAC;AAVD,gDAUC;AAED,SAAgB,kBAAkB,CAAC,KAA8B,EAAE,IAAa;IAC9E,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;QAChC,OAAO,IAAA,gBAAQ,EAAC,0BAAc,CAAC,CAAC;KACjC;IACD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC7B,KAAK,mCACA,IAAA,gBAAQ,EAAC,wBAAY,CAAC,KACzB,OAAO,EAAE,KAAK,GACf,CAAC;KACH;IACD,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;QAC/B,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;KACnB;IACD,IAAI,IAAA,2BAAmB,EAAC,KAAK,CAAC,IAAI,CAAC,EAAE;QACnC,KAAK,GAAG,IAAA,sBAAc,EAAC,KAAK,CAAC,IAAI,CAAC,CAAC;KACpC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAjBD,gDAiBC"}