"use strict";

var ProviderEngine = require('./index.js');

var DefaultFixture = require('./subproviders/default-fixture.js');

var NonceTrackerSubprovider = require('./subproviders/nonce-tracker.js');

var CacheSubprovider = require('./subproviders/cache.js');

var FilterSubprovider = require('./subproviders/filters');

var SubscriptionSubprovider = require('./subproviders/subscriptions');

var InflightCacheSubprovider = require('./subproviders/inflight-cache');

var HookedWalletSubprovider = require('./subproviders/hooked-wallet.js');

var SanitizingSubprovider = require('./subproviders/sanitizer.js');

var InfuraSubprovider = require('./subproviders/infura.js');

var FetchSubprovider = require('./subproviders/fetch.js');

var WebSocketSubprovider = require('./subproviders/websocket.js');

module.exports = ZeroClientProvider;

function ZeroClientProvider() {
  var opts = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  var connectionType = getConnectionType(opts);
  var engine = new ProviderEngine(opts.engineParams); // static

  var staticSubprovider = new DefaultFixture(opts["static"]);
  engine.addProvider(staticSubprovider); // nonce tracker

  engine.addProvider(new NonceTrackerSubprovider()); // sanitization

  var sanitizer = new SanitizingSubprovider();
  engine.addProvider(sanitizer); // cache layer

  var cacheSubprovider = new CacheSubprovider();
  engine.addProvider(cacheSubprovider); // filters + subscriptions
  // only polyfill if not websockets

  if (connectionType !== 'ws') {
    engine.addProvider(new SubscriptionSubprovider());
    engine.addProvider(new FilterSubprovider());
  } // inflight cache


  var inflightCache = new InflightCacheSubprovider();
  engine.addProvider(inflightCache); // id mgmt

  var idmgmtSubprovider = new HookedWalletSubprovider({
    // accounts
    getAccounts: opts.getAccounts,
    // transactions
    processTransaction: opts.processTransaction,
    approveTransaction: opts.approveTransaction,
    signTransaction: opts.signTransaction,
    publishTransaction: opts.publishTransaction,
    // messages
    // old eth_sign
    processMessage: opts.processMessage,
    approveMessage: opts.approveMessage,
    signMessage: opts.signMessage,
    // new personal_sign
    processPersonalMessage: opts.processPersonalMessage,
    processTypedMessage: opts.processTypedMessage,
    approvePersonalMessage: opts.approvePersonalMessage,
    approveTypedMessage: opts.approveTypedMessage,
    signPersonalMessage: opts.signPersonalMessage,
    signTypedMessage: opts.signTypedMessage,
    personalRecoverSigner: opts.personalRecoverSigner
  });
  engine.addProvider(idmgmtSubprovider); // data source

  var dataSubprovider = opts.dataSubprovider || createDataSubprovider(connectionType, opts);
  engine.addProvider(dataSubprovider); // start polling

  if (!opts.stopped) {
    engine.start();
  }

  return engine;
}

function createDataSubprovider(connectionType, opts) {
  var rpcUrl = opts.rpcUrl,
      debug = opts.debug; // default to infura

  if (!connectionType) {
    return new InfuraSubprovider();
  }

  if (connectionType === 'http') {
    return new FetchSubprovider({
      rpcUrl: rpcUrl,
      debug: debug
    });
  }

  if (connectionType === 'ws') {
    return new WebSocketSubprovider({
      rpcUrl: rpcUrl,
      debug: debug
    });
  }

  throw new Error("ProviderEngine - unrecognized connectionType \"".concat(connectionType, "\""));
}

function getConnectionType(_ref) {
  var rpcUrl = _ref.rpcUrl;
  if (!rpcUrl) return undefined;
  var protocol = rpcUrl.split(':')[0].toLowerCase();

  switch (protocol) {
    case 'http':
    case 'https':
      return 'http';

    case 'ws':
    case 'wss':
      return 'ws';

    default:
      throw new Error("ProviderEngine - unrecognized protocol in \"".concat(rpcUrl, "\""));
  }
}