"""
Generated by `compile_contracts.py` script.
Compiled with Solidity v0.8.19.
"""

# source: web3/_utils/contract_sources/EventContracts.sol:EventContract
EVENT_CONTRACT_BYTECODE = "0x608060405234801561001057600080fd5b50610185806100206000396000f3fe608060405234801561001057600080fd5b506004361061002b5760003560e01c80635818fad714610030575b600080fd5b61004a600480360381019061004591906100f8565b61004c565b005b7ff70fe689e290d8ce2b2a388ac28db36fbb0e16a6d89c6804c461f65a1b40bb158160405161007b9190610134565b60405180910390a17f56d2ef3c5228bf5d88573621e325a4672ab50e033749a601e4f4a5e1dce905d4816040516100b29190610134565b60405180910390a150565b600080fd5b6000819050919050565b6100d5816100c2565b81146100e057600080fd5b50565b6000813590506100f2816100cc565b92915050565b60006020828403121561010e5761010d6100bd565b5b600061011c848285016100e3565b91505092915050565b61012e816100c2565b82525050565b60006020820190506101496000830184610125565b9291505056fea2646970667358221220722d2c8e718484643ec6e2a231cf7652e5dc0660ce16ca85abd3e52518abd80164736f6c63430008130033"  # noqa: E501
EVENT_CONTRACT_RUNTIME = "0x608060405234801561001057600080fd5b506004361061002b5760003560e01c80635818fad714610030575b600080fd5b61004a600480360381019061004591906100f8565b61004c565b005b7ff70fe689e290d8ce2b2a388ac28db36fbb0e16a6d89c6804c461f65a1b40bb158160405161007b9190610134565b60405180910390a17f56d2ef3c5228bf5d88573621e325a4672ab50e033749a601e4f4a5e1dce905d4816040516100b29190610134565b60405180910390a150565b600080fd5b6000819050919050565b6100d5816100c2565b81146100e057600080fd5b50565b6000813590506100f2816100cc565b92915050565b60006020828403121561010e5761010d6100bd565b5b600061011c848285016100e3565b91505092915050565b61012e816100c2565b82525050565b60006020820190506101496000830184610125565b9291505056fea2646970667358221220722d2c8e718484643ec6e2a231cf7652e5dc0660ce16ca85abd3e52518abd80164736f6c63430008130033"  # noqa: E501
EVENT_CONTRACT_ABI = [
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "arg0",
                "type": "uint256",
            }
        ],
        "name": "LogSingleArg",
        "type": "event",
    },
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "arg0",
                "type": "uint256",
            }
        ],
        "name": "LogSingleWithIndex",
        "type": "event",
    },
    {
        "inputs": [{"internalType": "uint256", "name": "_arg0", "type": "uint256"}],
        "name": "logTwoEvents",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
]
EVENT_CONTRACT_DATA = {
    "bytecode": EVENT_CONTRACT_BYTECODE,
    "bytecode_runtime": EVENT_CONTRACT_RUNTIME,
    "abi": EVENT_CONTRACT_ABI,
}


# source: web3/_utils/contract_sources/EventContracts.sol:IndexedEventContract
INDEXED_EVENT_CONTRACT_BYTECODE = "0x608060405234801561001057600080fd5b5061017b806100206000396000f3fe608060405234801561001057600080fd5b506004361061002b5760003560e01c80635818fad714610030575b600080fd5b61004a600480360381019061004591906100ee565b61004c565b005b807ff70fe689e290d8ce2b2a388ac28db36fbb0e16a6d89c6804c461f65a1b40bb1560405160405180910390a27f56d2ef3c5228bf5d88573621e325a4672ab50e033749a601e4f4a5e1dce905d4816040516100a8919061012a565b60405180910390a150565b600080fd5b6000819050919050565b6100cb816100b8565b81146100d657600080fd5b50565b6000813590506100e8816100c2565b92915050565b600060208284031215610104576101036100b3565b5b6000610112848285016100d9565b91505092915050565b610124816100b8565b82525050565b600060208201905061013f600083018461011b565b9291505056fea2646970667358221220e3ae4f34b41d4603e54206e0c06c86c3b16bdd5d8fcbb6876dd3205457c7b2eb64736f6c63430008130033"  # noqa: E501
INDEXED_EVENT_CONTRACT_RUNTIME = "0x608060405234801561001057600080fd5b506004361061002b5760003560e01c80635818fad714610030575b600080fd5b61004a600480360381019061004591906100ee565b61004c565b005b807ff70fe689e290d8ce2b2a388ac28db36fbb0e16a6d89c6804c461f65a1b40bb1560405160405180910390a27f56d2ef3c5228bf5d88573621e325a4672ab50e033749a601e4f4a5e1dce905d4816040516100a8919061012a565b60405180910390a150565b600080fd5b6000819050919050565b6100cb816100b8565b81146100d657600080fd5b50565b6000813590506100e8816100c2565b92915050565b600060208284031215610104576101036100b3565b5b6000610112848285016100d9565b91505092915050565b610124816100b8565b82525050565b600060208201905061013f600083018461011b565b9291505056fea2646970667358221220e3ae4f34b41d4603e54206e0c06c86c3b16bdd5d8fcbb6876dd3205457c7b2eb64736f6c63430008130033"  # noqa: E501
INDEXED_EVENT_CONTRACT_ABI = [
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": False,
                "internalType": "uint256",
                "name": "arg0",
                "type": "uint256",
            }
        ],
        "name": "LogSingleArg",
        "type": "event",
    },
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": True,
                "internalType": "uint256",
                "name": "arg0",
                "type": "uint256",
            }
        ],
        "name": "LogSingleWithIndex",
        "type": "event",
    },
    {
        "inputs": [{"internalType": "uint256", "name": "_arg0", "type": "uint256"}],
        "name": "logTwoEvents",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
]
INDEXED_EVENT_CONTRACT_DATA = {
    "bytecode": INDEXED_EVENT_CONTRACT_BYTECODE,
    "bytecode_runtime": INDEXED_EVENT_CONTRACT_RUNTIME,
    "abi": INDEXED_EVENT_CONTRACT_ABI,
}
