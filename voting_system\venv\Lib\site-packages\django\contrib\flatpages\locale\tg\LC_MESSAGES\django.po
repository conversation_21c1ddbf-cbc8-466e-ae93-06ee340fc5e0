# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2020
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2020-05-15 00:29+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Tajik (http://www.transifex.com/django/django/language/tg/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: tg\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Advanced options"
msgstr "Танзимоти васеъ"

msgid "Flat Pages"
msgstr "Саҳифаҳои муқарарӣ"

msgid "URL"
msgstr "URL"

msgid ""
"Example: “/about/contact/”. Make sure to have leading and trailing slashes."
msgstr ""

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"Қимат бояд танҳо аз ҳарф, рақам, ва аломатҳои нуқта, зерхат, дефис, слеш ва "
"зада иборат бошад."

msgid "Example: “/about/contact”. Make sure to have a leading slash."
msgstr ""

msgid "URL is missing a leading slash."
msgstr "Дар аввали URL слеш(/) мавҷуд нест"

msgid "URL is missing a trailing slash."
msgstr "Дар охири URL слеш(/) мавҷуд нест"

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr ""
"Саҳифаи муқарарӣ бо чунин суроға %(url)s  барои сомонаи %(site)s алакай "
"мавҷуд аст."

msgid "title"
msgstr "сархат"

msgid "content"
msgstr "контент"

msgid "enable comments"
msgstr "фаъолсозии 'андешаҳо'"

msgid "template name"
msgstr "номи нусха"

msgid ""
"Example: “flatpages/contact_page.html”. If this isn’t provided, the system "
"will use “flatpages/default.html”."
msgstr ""

msgid "registration required"
msgstr "Бақайдгирӣ талаб карда мешавад"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr ""
"Агар қайд шуда бошад, танҳо истифодабарандагони рухсатдор метавонанд аз "
"назар гузаронанд."

msgid "sites"
msgstr "сомонаҳо"

msgid "flat page"
msgstr "саҳифаи муқарарӣ"

msgid "flat pages"
msgstr "саҳифаҳои муқарарӣ"
