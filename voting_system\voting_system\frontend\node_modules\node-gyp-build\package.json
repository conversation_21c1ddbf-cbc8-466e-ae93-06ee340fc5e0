{"name": "node-gyp-build", "version": "4.8.4", "description": "Build tool and bindings loader for node-gyp that supports prebuilds", "main": "index.js", "imports": {"fs": {"bare": "builtin:fs", "default": "fs"}, "path": {"bare": "builtin:path", "default": "path"}, "os": {"bare": "builtin:os", "default": "os"}}, "devDependencies": {"array-shuffle": "^1.0.1", "standard": "^14.0.0", "tape": "^5.0.0"}, "scripts": {"test": "standard && node test"}, "bin": {"node-gyp-build": "./bin.js", "node-gyp-build-optional": "./optional.js", "node-gyp-build-test": "./build-test.js"}, "repository": {"type": "git", "url": "https://github.com/prebuild/node-gyp-build.git"}, "author": "<PERSON> (@mafintosh)", "license": "MIT", "bugs": {"url": "https://github.com/prebuild/node-gyp-build/issues"}, "homepage": "https://github.com/prebuild/node-gyp-build"}