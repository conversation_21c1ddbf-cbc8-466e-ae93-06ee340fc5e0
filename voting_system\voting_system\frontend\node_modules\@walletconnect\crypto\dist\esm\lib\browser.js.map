{"version": 3, "file": "browser.js", "sourceRoot": "", "sources": ["../../../src/lib/browser.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,GAAG,MAAM,4BAA4B,CAAC;AAClD,OAAO,EACL,UAAU,EACV,UAAU,EACV,OAAO,EACP,SAAS,EACT,gBAAgB,EAChB,UAAU,EACV,iBAAiB,EACjB,YAAY,EACZ,WAAW,EACX,mBAAmB,EACnB,mBAAmB,EACnB,UAAU,GACX,MAAM,cAAc,CAAC;AAGtB,MAAM,UAAU,OAAO,CAAC,IAAY;IAClC,OAAO,IAAI,KAAK,gBAAgB;QAC9B,CAAC,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,gBAAgB,EAAE;QAChD,CAAC,CAAC;YACE,IAAI,EAAE,EAAE,IAAI,EAAE,iBAAiB,EAAE;YACjC,IAAI,EAAE,YAAY;SACnB,CAAC;AACR,CAAC;AAED,MAAM,UAAU,MAAM,CAAC,IAAY;IACjC,OAAO,IAAI,KAAK,gBAAgB,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;AACrF,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,gBAAgB,CACpC,SAAoB,EACpB,QAAgB,gBAAgB;IAEhC,MAAM,MAAM,GAAG,GAAG,CAAC,eAAe,EAAE,CAAC;IACrC,OAAO,IAAI,UAAU,CAAC,MAAM,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC;AAClE,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,gBAAgB,CACpC,MAAkB,EAClB,OAAe,gBAAgB;IAE/B,OAAQ,GAAG,CAAC,eAAe,EAAU,CAAC,SAAS,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;AACpG,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,iBAAiB,CACrC,EAAc,EACd,GAAe,EACf,IAAgB;IAEhB,MAAM,MAAM,GAAG,GAAG,CAAC,eAAe,EAAE,CAAC;IACrC,MAAM,SAAS,GAAG,MAAM,gBAAgB,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAC;IAChE,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,OAAO,CACjC;QACE,EAAE;QACF,IAAI,EAAE,gBAAgB;KACvB,EACD,SAAS,EACT,IAAI,CACL,CAAC;IACF,OAAO,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;AAChC,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,iBAAiB,CACrC,EAAc,EACd,GAAe,EACf,IAAgB;IAEhB,MAAM,MAAM,GAAG,GAAG,CAAC,eAAe,EAAE,CAAC;IACrC,MAAM,SAAS,GAAG,MAAM,gBAAgB,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAC;IAChE,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,OAAO,CACjC;QACE,EAAE;QACF,IAAI,EAAE,gBAAgB;KACvB,EACD,SAAS,EACT,IAAI,CACL,CAAC;IACF,OAAO,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;AAChC,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,qBAAqB,CACzC,GAAe,EACf,IAAgB;IAEhB,MAAM,MAAM,GAAG,GAAG,CAAC,eAAe,EAAE,CAAC;IACrC,MAAM,SAAS,GAAG,MAAM,gBAAgB,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC;IAC5D,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,IAAI,CACjC;QAEE,MAAM,EAAE,WAAW;QACnB,IAAI,EAAE,YAAY;KACnB,EACD,SAAS,EACT,IAAI,CACL,CAAC;IACF,OAAO,IAAI,UAAU,CAAC,SAAS,CAAC,CAAC;AACnC,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,qBAAqB,CACzC,GAAe,EACf,IAAgB;IAEhB,MAAM,MAAM,GAAG,GAAG,CAAC,eAAe,EAAE,CAAC;IACrC,MAAM,SAAS,GAAG,MAAM,gBAAgB,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC;IAC5D,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,IAAI,CACjC;QAEE,MAAM,EAAE,UAAU;QAClB,IAAI,EAAE,YAAY;KACnB,EACD,SAAS,EACT,IAAI,CACL,CAAC;IACF,OAAO,IAAI,UAAU,CAAC,SAAS,CAAC,CAAC;AACnC,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,aAAa,CAAC,IAAgB;IAClD,MAAM,MAAM,GAAG,GAAG,CAAC,eAAe,EAAE,CAAC;IACrC,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAChC;QACE,IAAI,EAAE,mBAAmB;KAC1B,EACD,IAAI,CACL,CAAC;IACF,OAAO,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;AAChC,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,aAAa,CAAC,IAAgB;IAClD,MAAM,MAAM,GAAG,GAAG,CAAC,eAAe,EAAE,CAAC;IACrC,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAChC;QACE,IAAI,EAAE,mBAAmB;KAC1B,EACD,IAAI,CACL,CAAC;IACF,OAAO,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;AAChC,CAAC"}