{"version": 3, "file": "experimental-hydrate-support.js", "sourceRoot": "", "sources": ["../src/experimental-hydrate-support.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH;;;;GAIG;AAEH,cAAc,6CAA6C,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/**\n * LitElement support for hydration of content rendered using lit-ssr.\n *\n * @packageDocumentation\n */\n\nexport * from 'lit-element/experimental-hydrate-support.js';\n"]}