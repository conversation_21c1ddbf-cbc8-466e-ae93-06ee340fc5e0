{"version": 3, "file": "encoding.js", "sourceRoot": "", "sources": ["../../src/encoding.ts"], "names": [], "mappings": ";;;;AAAA,0DAAuB;AACvB,0EAAoD;AAIpD,SAAgB,0BAA0B,CAAC,MAAmB;IAC5D,OAAO,QAAQ,CAAC,aAAa,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;AACxD,CAAC;AAFD,gEAEC;AAED,SAAgB,wBAAwB,CAAC,MAAmB;IAC1D,OAAO,QAAQ,CAAC,WAAW,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;AACtD,CAAC;AAFD,4DAEC;AAED,SAAgB,uBAAuB,CAAC,MAAmB,EAAE,QAAkB;IAC7E,OAAO,QAAQ,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;AAChE,CAAC;AAFD,0DAEC;AAED,SAAgB,0BAA0B,CAAC,MAAmB;IAC5D,OAAO,QAAQ,CAAC,aAAa,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;AACxD,CAAC;AAFD,gEAEC;AAED,SAAgB,kBAAkB,CAAC,GAAG,IAAmB;IACvD,OAAO,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC;AACpG,CAAC;AAFD,gDAEC;AAID,SAAgB,0BAA0B,CAAC,GAAW;IACpD,OAAO,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;AAC5C,CAAC;AAFD,gEAEC;AAED,SAAgB,mBAAmB,CAAC,GAAW;IAC7C,OAAO,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AACpC,CAAC;AAFD,kDAEC;AAED,SAAgB,kBAAkB,CAAC,GAAW,EAAE,QAAkB;IAChE,OAAO,QAAQ,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,CAAC;AAC9C,CAAC;AAFD,gDAEC;AAED,SAAgB,qBAAqB,CAAC,GAAW;IAC/C,OAAO,QAAQ,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;AACtC,CAAC;AAFD,sDAEC;AAED,SAAgB,aAAa,CAAC,GAAG,IAAc;IAC7C,OAAO,QAAQ,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC,CAAC;AACzC,CAAC;AAFD,sCAEC;AAID,SAAgB,wBAAwB,CAAC,IAAY;IACnD,OAAO,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;AAC3C,CAAC;AAFD,4DAEC;AAED,SAAgB,mBAAmB,CAAC,IAAY;IAC9C,OAAO,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;AACrC,CAAC;AAFD,kDAEC;AAED,SAAgB,gBAAgB,CAAC,IAAY,EAAE,QAAkB;IAC/D,OAAO,QAAQ,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,CAAC;AAC7C,CAAC;AAFD,4CAEC;AAED,SAAgB,mBAAmB,CAAC,IAAY;IAC9C,OAAO,IAAI,eAAE,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC;AACrC,CAAC;AAFD,kDAEC;AAID,SAAgB,kBAAkB,CAAC,GAAW;IAC5C,OAAO,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;AACnC,CAAC;AAFD,gDAEC;AAED,SAAgB,uBAAuB,CAAC,GAAW;IACjD,OAAO,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;AACzC,CAAC;AAFD,0DAEC;AAED,SAAgB,gBAAgB,CAAC,GAAW;IAC1C,OAAO,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;AACjC,CAAC;AAFD,4CAEC;AAED,SAAgB,kBAAkB,CAAC,GAAW;IAC5C,OAAO,IAAI,eAAE,CAAC,QAAQ,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;AACjE,CAAC;AAFD,gDAEC;AAID,SAAgB,qBAAqB,CAAC,GAAW;IAC/C,OAAO,QAAQ,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;AACtC,CAAC;AAFD,sDAEC;AAED,SAAgB,0BAA0B,CAAC,GAAW;IACpD,OAAO,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;AAC5C,CAAC;AAFD,gEAEC;AAED,SAAgB,mBAAmB,CAAC,GAAW;IAC7C,OAAO,IAAI,eAAE,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC;AAChC,CAAC;AAFD,kDAEC;AAED,SAAgB,kBAAkB,CAAC,GAAoB,EAAE,QAAkB;IACzE,MAAM,GAAG,GAAG,QAAQ,CAAC,eAAe,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,eAAE,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACrF,OAAO,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AACrD,CAAC;AAHD,gDAGC"}