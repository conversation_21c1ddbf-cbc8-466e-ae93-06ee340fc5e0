!function(t,r){"object"==typeof exports&&"object"==typeof module?module.exports=r():"function"==typeof define&&define.amd?define("encoding",[],r):"object"==typeof exports?exports.encoding=r():t.encoding=r()}(this,(function(){return function(t){var r={};function n(e){if(r[e])return r[e].exports;var o=r[e]={i:e,l:!1,exports:{}};return t[e].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=t,n.c=r,n.d=function(t,r,e){n.o(t,r)||Object.defineProperty(t,r,{enumerable:!0,get:e})},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,r){if(1&r&&(t=n(t)),8&r)return t;if(4&r&&"object"==typeof t&&t&&t.__esModule)return t;var e=Object.create(null);if(n.r(e),Object.defineProperty(e,"default",{enumerable:!0,value:t}),2&r&&"string"!=typeof t)for(var o in t)n.d(e,o,function(r){return t[r]}.bind(null,o));return e},n.n=function(t){var r=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(r,"a",r),r},n.o=function(t,r){return Object.prototype.hasOwnProperty.call(t,r)},n.p="",n(n.s=2)}([function(t,r,n){"use strict";(function(t){
/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <http://feross.org>
 * @license  MIT
 */
var e=n(4),o=n(5),i=n(6);function u(){return a.TYPED_ARRAY_SUPPORT?**********:**********}function f(t,r){if(u()<r)throw new RangeError("Invalid typed array length");return a.TYPED_ARRAY_SUPPORT?(t=new Uint8Array(r)).__proto__=a.prototype:(null===t&&(t=new a(r)),t.length=r),t}function a(t,r,n){if(!(a.TYPED_ARRAY_SUPPORT||this instanceof a))return new a(t,r,n);if("number"==typeof t){if("string"==typeof r)throw new Error("If encoding is specified then the first argument must be a string");return h(this,t)}return s(this,t,r,n)}function s(t,r,n,e){if("number"==typeof r)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&r instanceof ArrayBuffer?function(t,r,n,e){if(r.byteLength,n<0||r.byteLength<n)throw new RangeError("'offset' is out of bounds");if(r.byteLength<n+(e||0))throw new RangeError("'length' is out of bounds");r=void 0===n&&void 0===e?new Uint8Array(r):void 0===e?new Uint8Array(r,n):new Uint8Array(r,n,e);a.TYPED_ARRAY_SUPPORT?(t=r).__proto__=a.prototype:t=l(t,r);return t}(t,r,n,e):"string"==typeof r?function(t,r,n){"string"==typeof n&&""!==n||(n="utf8");if(!a.isEncoding(n))throw new TypeError('"encoding" must be a valid string encoding');var e=0|y(r,n),o=(t=f(t,e)).write(r,n);o!==e&&(t=t.slice(0,o));return t}(t,r,n):function(t,r){if(a.isBuffer(r)){var n=0|p(r.length);return 0===(t=f(t,n)).length||r.copy(t,0,0,n),t}if(r){if("undefined"!=typeof ArrayBuffer&&r.buffer instanceof ArrayBuffer||"length"in r)return"number"!=typeof r.length||(e=r.length)!=e?f(t,0):l(t,r);if("Buffer"===r.type&&i(r.data))return l(t,r.data)}var e;throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(t,r)}function c(t){if("number"!=typeof t)throw new TypeError('"size" argument must be a number');if(t<0)throw new RangeError('"size" argument must not be negative')}function h(t,r){if(c(r),t=f(t,r<0?0:0|p(r)),!a.TYPED_ARRAY_SUPPORT)for(var n=0;n<r;++n)t[n]=0;return t}function l(t,r){var n=r.length<0?0:0|p(r.length);t=f(t,n);for(var e=0;e<n;e+=1)t[e]=255&r[e];return t}function p(t){if(t>=u())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+u().toString(16)+" bytes");return 0|t}function y(t,r){if(a.isBuffer(t))return t.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(t)||t instanceof ArrayBuffer))return t.byteLength;"string"!=typeof t&&(t=""+t);var n=t.length;if(0===n)return 0;for(var e=!1;;)switch(r){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":case void 0:return H(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return k(t).length;default:if(e)return H(t).length;r=(""+r).toLowerCase(),e=!0}}function g(t,r,n){var e=!1;if((void 0===r||r<0)&&(r=0),r>this.length)return"";if((void 0===n||n>this.length)&&(n=this.length),n<=0)return"";if((n>>>=0)<=(r>>>=0))return"";for(t||(t="utf8");;)switch(t){case"hex":return S(this,r,n);case"utf8":case"utf-8":return P(this,r,n);case"ascii":return R(this,r,n);case"latin1":case"binary":return x(this,r,n);case"base64":return B(this,r,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return U(this,r,n);default:if(e)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),e=!0}}function d(t,r,n){var e=t[r];t[r]=t[n],t[n]=e}function b(t,r,n,e,o){if(0===t.length)return-1;if("string"==typeof n?(e=n,n=0):n>**********?n=**********:n<-2147483648&&(n=-2147483648),n=+n,isNaN(n)&&(n=o?0:t.length-1),n<0&&(n=t.length+n),n>=t.length){if(o)return-1;n=t.length-1}else if(n<0){if(!o)return-1;n=0}if("string"==typeof r&&(r=a.from(r,e)),a.isBuffer(r))return 0===r.length?-1:w(t,r,n,e,o);if("number"==typeof r)return r&=255,a.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?o?Uint8Array.prototype.indexOf.call(t,r,n):Uint8Array.prototype.lastIndexOf.call(t,r,n):w(t,[r],n,e,o);throw new TypeError("val must be string, number or Buffer")}function w(t,r,n,e,o){var i,u=1,f=t.length,a=r.length;if(void 0!==e&&("ucs2"===(e=String(e).toLowerCase())||"ucs-2"===e||"utf16le"===e||"utf-16le"===e)){if(t.length<2||r.length<2)return-1;u=2,f/=2,a/=2,n/=2}function s(t,r){return 1===u?t[r]:t.readUInt16BE(r*u)}if(o){var c=-1;for(i=n;i<f;i++)if(s(t,i)===s(r,-1===c?0:i-c)){if(-1===c&&(c=i),i-c+1===a)return c*u}else-1!==c&&(i-=i-c),c=-1}else for(n+a>f&&(n=f-a),i=n;i>=0;i--){for(var h=!0,l=0;l<a;l++)if(s(t,i+l)!==s(r,l)){h=!1;break}if(h)return i}return-1}function v(t,r,n,e){n=Number(n)||0;var o=t.length-n;e?(e=Number(e))>o&&(e=o):e=o;var i=r.length;if(i%2!=0)throw new TypeError("Invalid hex string");e>i/2&&(e=i/2);for(var u=0;u<e;++u){var f=parseInt(r.substr(2*u,2),16);if(isNaN(f))return u;t[n+u]=f}return u}function m(t,r,n,e){return F(H(r,t.length-n),t,n,e)}function _(t,r,n,e){return F(function(t){for(var r=[],n=0;n<t.length;++n)r.push(255&t.charCodeAt(n));return r}(r),t,n,e)}function A(t,r,n,e){return _(t,r,n,e)}function T(t,r,n,e){return F(k(r),t,n,e)}function E(t,r,n,e){return F(function(t,r){for(var n,e,o,i=[],u=0;u<t.length&&!((r-=2)<0);++u)n=t.charCodeAt(u),e=n>>8,o=n%256,i.push(o),i.push(e);return i}(r,t.length-n),t,n,e)}function B(t,r,n){return 0===r&&n===t.length?e.fromByteArray(t):e.fromByteArray(t.slice(r,n))}function P(t,r,n){n=Math.min(t.length,n);for(var e=[],o=r;o<n;){var i,u,f,a,s=t[o],c=null,h=s>239?4:s>223?3:s>191?2:1;if(o+h<=n)switch(h){case 1:s<128&&(c=s);break;case 2:128==(192&(i=t[o+1]))&&(a=(31&s)<<6|63&i)>127&&(c=a);break;case 3:i=t[o+1],u=t[o+2],128==(192&i)&&128==(192&u)&&(a=(15&s)<<12|(63&i)<<6|63&u)>2047&&(a<55296||a>57343)&&(c=a);break;case 4:i=t[o+1],u=t[o+2],f=t[o+3],128==(192&i)&&128==(192&u)&&128==(192&f)&&(a=(15&s)<<18|(63&i)<<12|(63&u)<<6|63&f)>65535&&a<1114112&&(c=a)}null===c?(c=65533,h=1):c>65535&&(c-=65536,e.push(c>>>10&1023|55296),c=56320|1023&c),e.push(c),o+=h}return function(t){var r=t.length;if(r<=4096)return String.fromCharCode.apply(String,t);var n="",e=0;for(;e<r;)n+=String.fromCharCode.apply(String,t.slice(e,e+=4096));return n}(e)}r.Buffer=a,r.SlowBuffer=function(t){+t!=t&&(t=0);return a.alloc(+t)},r.INSPECT_MAX_BYTES=50,a.TYPED_ARRAY_SUPPORT=void 0!==t.TYPED_ARRAY_SUPPORT?t.TYPED_ARRAY_SUPPORT:function(){try{var t=new Uint8Array(1);return t.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===t.foo()&&"function"==typeof t.subarray&&0===t.subarray(1,1).byteLength}catch(t){return!1}}(),r.kMaxLength=u(),a.poolSize=8192,a._augment=function(t){return t.__proto__=a.prototype,t},a.from=function(t,r,n){return s(null,t,r,n)},a.TYPED_ARRAY_SUPPORT&&(a.prototype.__proto__=Uint8Array.prototype,a.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&a[Symbol.species]===a&&Object.defineProperty(a,Symbol.species,{value:null,configurable:!0})),a.alloc=function(t,r,n){return function(t,r,n,e){return c(r),r<=0?f(t,r):void 0!==n?"string"==typeof e?f(t,r).fill(n,e):f(t,r).fill(n):f(t,r)}(null,t,r,n)},a.allocUnsafe=function(t){return h(null,t)},a.allocUnsafeSlow=function(t){return h(null,t)},a.isBuffer=function(t){return!(null==t||!t._isBuffer)},a.compare=function(t,r){if(!a.isBuffer(t)||!a.isBuffer(r))throw new TypeError("Arguments must be Buffers");if(t===r)return 0;for(var n=t.length,e=r.length,o=0,i=Math.min(n,e);o<i;++o)if(t[o]!==r[o]){n=t[o],e=r[o];break}return n<e?-1:e<n?1:0},a.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},a.concat=function(t,r){if(!i(t))throw new TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return a.alloc(0);var n;if(void 0===r)for(r=0,n=0;n<t.length;++n)r+=t[n].length;var e=a.allocUnsafe(r),o=0;for(n=0;n<t.length;++n){var u=t[n];if(!a.isBuffer(u))throw new TypeError('"list" argument must be an Array of Buffers');u.copy(e,o),o+=u.length}return e},a.byteLength=y,a.prototype._isBuffer=!0,a.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var r=0;r<t;r+=2)d(this,r,r+1);return this},a.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var r=0;r<t;r+=4)d(this,r,r+3),d(this,r+1,r+2);return this},a.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var r=0;r<t;r+=8)d(this,r,r+7),d(this,r+1,r+6),d(this,r+2,r+5),d(this,r+3,r+4);return this},a.prototype.toString=function(){var t=0|this.length;return 0===t?"":0===arguments.length?P(this,0,t):g.apply(this,arguments)},a.prototype.equals=function(t){if(!a.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t||0===a.compare(this,t)},a.prototype.inspect=function(){var t="",n=r.INSPECT_MAX_BYTES;return this.length>0&&(t=this.toString("hex",0,n).match(/.{2}/g).join(" "),this.length>n&&(t+=" ... ")),"<Buffer "+t+">"},a.prototype.compare=function(t,r,n,e,o){if(!a.isBuffer(t))throw new TypeError("Argument must be a Buffer");if(void 0===r&&(r=0),void 0===n&&(n=t?t.length:0),void 0===e&&(e=0),void 0===o&&(o=this.length),r<0||n>t.length||e<0||o>this.length)throw new RangeError("out of range index");if(e>=o&&r>=n)return 0;if(e>=o)return-1;if(r>=n)return 1;if(this===t)return 0;for(var i=(o>>>=0)-(e>>>=0),u=(n>>>=0)-(r>>>=0),f=Math.min(i,u),s=this.slice(e,o),c=t.slice(r,n),h=0;h<f;++h)if(s[h]!==c[h]){i=s[h],u=c[h];break}return i<u?-1:u<i?1:0},a.prototype.includes=function(t,r,n){return-1!==this.indexOf(t,r,n)},a.prototype.indexOf=function(t,r,n){return b(this,t,r,n,!0)},a.prototype.lastIndexOf=function(t,r,n){return b(this,t,r,n,!1)},a.prototype.write=function(t,r,n,e){if(void 0===r)e="utf8",n=this.length,r=0;else if(void 0===n&&"string"==typeof r)e=r,n=this.length,r=0;else{if(!isFinite(r))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");r|=0,isFinite(n)?(n|=0,void 0===e&&(e="utf8")):(e=n,n=void 0)}var o=this.length-r;if((void 0===n||n>o)&&(n=o),t.length>0&&(n<0||r<0)||r>this.length)throw new RangeError("Attempt to write outside buffer bounds");e||(e="utf8");for(var i=!1;;)switch(e){case"hex":return v(this,t,r,n);case"utf8":case"utf-8":return m(this,t,r,n);case"ascii":return _(this,t,r,n);case"latin1":case"binary":return A(this,t,r,n);case"base64":return T(this,t,r,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return E(this,t,r,n);default:if(i)throw new TypeError("Unknown encoding: "+e);e=(""+e).toLowerCase(),i=!0}},a.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};function R(t,r,n){var e="";n=Math.min(t.length,n);for(var o=r;o<n;++o)e+=String.fromCharCode(127&t[o]);return e}function x(t,r,n){var e="";n=Math.min(t.length,n);for(var o=r;o<n;++o)e+=String.fromCharCode(t[o]);return e}function S(t,r,n){var e=t.length;(!r||r<0)&&(r=0),(!n||n<0||n>e)&&(n=e);for(var o="",i=r;i<n;++i)o+=N(t[i]);return o}function U(t,r,n){for(var e=t.slice(r,n),o="",i=0;i<e.length;i+=2)o+=String.fromCharCode(e[i]+256*e[i+1]);return o}function O(t,r,n){if(t%1!=0||t<0)throw new RangeError("offset is not uint");if(t+r>n)throw new RangeError("Trying to access beyond buffer length")}function I(t,r,n,e,o,i){if(!a.isBuffer(t))throw new TypeError('"buffer" argument must be a Buffer instance');if(r>o||r<i)throw new RangeError('"value" argument is out of bounds');if(n+e>t.length)throw new RangeError("Index out of range")}function j(t,r,n,e){r<0&&(r=65535+r+1);for(var o=0,i=Math.min(t.length-n,2);o<i;++o)t[n+o]=(r&255<<8*(e?o:1-o))>>>8*(e?o:1-o)}function Y(t,r,n,e){r<0&&(r=4294967295+r+1);for(var o=0,i=Math.min(t.length-n,4);o<i;++o)t[n+o]=r>>>8*(e?o:3-o)&255}function L(t,r,n,e,o,i){if(n+e>t.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("Index out of range")}function M(t,r,n,e,i){return i||L(t,0,n,4),o.write(t,r,n,e,23,4),n+4}function C(t,r,n,e,i){return i||L(t,0,n,8),o.write(t,r,n,e,52,8),n+8}a.prototype.slice=function(t,r){var n,e=this.length;if((t=~~t)<0?(t+=e)<0&&(t=0):t>e&&(t=e),(r=void 0===r?e:~~r)<0?(r+=e)<0&&(r=0):r>e&&(r=e),r<t&&(r=t),a.TYPED_ARRAY_SUPPORT)(n=this.subarray(t,r)).__proto__=a.prototype;else{var o=r-t;n=new a(o,void 0);for(var i=0;i<o;++i)n[i]=this[i+t]}return n},a.prototype.readUIntLE=function(t,r,n){t|=0,r|=0,n||O(t,r,this.length);for(var e=this[t],o=1,i=0;++i<r&&(o*=256);)e+=this[t+i]*o;return e},a.prototype.readUIntBE=function(t,r,n){t|=0,r|=0,n||O(t,r,this.length);for(var e=this[t+--r],o=1;r>0&&(o*=256);)e+=this[t+--r]*o;return e},a.prototype.readUInt8=function(t,r){return r||O(t,1,this.length),this[t]},a.prototype.readUInt16LE=function(t,r){return r||O(t,2,this.length),this[t]|this[t+1]<<8},a.prototype.readUInt16BE=function(t,r){return r||O(t,2,this.length),this[t]<<8|this[t+1]},a.prototype.readUInt32LE=function(t,r){return r||O(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},a.prototype.readUInt32BE=function(t,r){return r||O(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},a.prototype.readIntLE=function(t,r,n){t|=0,r|=0,n||O(t,r,this.length);for(var e=this[t],o=1,i=0;++i<r&&(o*=256);)e+=this[t+i]*o;return e>=(o*=128)&&(e-=Math.pow(2,8*r)),e},a.prototype.readIntBE=function(t,r,n){t|=0,r|=0,n||O(t,r,this.length);for(var e=r,o=1,i=this[t+--e];e>0&&(o*=256);)i+=this[t+--e]*o;return i>=(o*=128)&&(i-=Math.pow(2,8*r)),i},a.prototype.readInt8=function(t,r){return r||O(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},a.prototype.readInt16LE=function(t,r){r||O(t,2,this.length);var n=this[t]|this[t+1]<<8;return 32768&n?4294901760|n:n},a.prototype.readInt16BE=function(t,r){r||O(t,2,this.length);var n=this[t+1]|this[t]<<8;return 32768&n?4294901760|n:n},a.prototype.readInt32LE=function(t,r){return r||O(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},a.prototype.readInt32BE=function(t,r){return r||O(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},a.prototype.readFloatLE=function(t,r){return r||O(t,4,this.length),o.read(this,t,!0,23,4)},a.prototype.readFloatBE=function(t,r){return r||O(t,4,this.length),o.read(this,t,!1,23,4)},a.prototype.readDoubleLE=function(t,r){return r||O(t,8,this.length),o.read(this,t,!0,52,8)},a.prototype.readDoubleBE=function(t,r){return r||O(t,8,this.length),o.read(this,t,!1,52,8)},a.prototype.writeUIntLE=function(t,r,n,e){(t=+t,r|=0,n|=0,e)||I(this,t,r,n,Math.pow(2,8*n)-1,0);var o=1,i=0;for(this[r]=255&t;++i<n&&(o*=256);)this[r+i]=t/o&255;return r+n},a.prototype.writeUIntBE=function(t,r,n,e){(t=+t,r|=0,n|=0,e)||I(this,t,r,n,Math.pow(2,8*n)-1,0);var o=n-1,i=1;for(this[r+o]=255&t;--o>=0&&(i*=256);)this[r+o]=t/i&255;return r+n},a.prototype.writeUInt8=function(t,r,n){return t=+t,r|=0,n||I(this,t,r,1,255,0),a.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),this[r]=255&t,r+1},a.prototype.writeUInt16LE=function(t,r,n){return t=+t,r|=0,n||I(this,t,r,2,65535,0),a.TYPED_ARRAY_SUPPORT?(this[r]=255&t,this[r+1]=t>>>8):j(this,t,r,!0),r+2},a.prototype.writeUInt16BE=function(t,r,n){return t=+t,r|=0,n||I(this,t,r,2,65535,0),a.TYPED_ARRAY_SUPPORT?(this[r]=t>>>8,this[r+1]=255&t):j(this,t,r,!1),r+2},a.prototype.writeUInt32LE=function(t,r,n){return t=+t,r|=0,n||I(this,t,r,4,4294967295,0),a.TYPED_ARRAY_SUPPORT?(this[r+3]=t>>>24,this[r+2]=t>>>16,this[r+1]=t>>>8,this[r]=255&t):Y(this,t,r,!0),r+4},a.prototype.writeUInt32BE=function(t,r,n){return t=+t,r|=0,n||I(this,t,r,4,4294967295,0),a.TYPED_ARRAY_SUPPORT?(this[r]=t>>>24,this[r+1]=t>>>16,this[r+2]=t>>>8,this[r+3]=255&t):Y(this,t,r,!1),r+4},a.prototype.writeIntLE=function(t,r,n,e){if(t=+t,r|=0,!e){var o=Math.pow(2,8*n-1);I(this,t,r,n,o-1,-o)}var i=0,u=1,f=0;for(this[r]=255&t;++i<n&&(u*=256);)t<0&&0===f&&0!==this[r+i-1]&&(f=1),this[r+i]=(t/u>>0)-f&255;return r+n},a.prototype.writeIntBE=function(t,r,n,e){if(t=+t,r|=0,!e){var o=Math.pow(2,8*n-1);I(this,t,r,n,o-1,-o)}var i=n-1,u=1,f=0;for(this[r+i]=255&t;--i>=0&&(u*=256);)t<0&&0===f&&0!==this[r+i+1]&&(f=1),this[r+i]=(t/u>>0)-f&255;return r+n},a.prototype.writeInt8=function(t,r,n){return t=+t,r|=0,n||I(this,t,r,1,127,-128),a.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),t<0&&(t=255+t+1),this[r]=255&t,r+1},a.prototype.writeInt16LE=function(t,r,n){return t=+t,r|=0,n||I(this,t,r,2,32767,-32768),a.TYPED_ARRAY_SUPPORT?(this[r]=255&t,this[r+1]=t>>>8):j(this,t,r,!0),r+2},a.prototype.writeInt16BE=function(t,r,n){return t=+t,r|=0,n||I(this,t,r,2,32767,-32768),a.TYPED_ARRAY_SUPPORT?(this[r]=t>>>8,this[r+1]=255&t):j(this,t,r,!1),r+2},a.prototype.writeInt32LE=function(t,r,n){return t=+t,r|=0,n||I(this,t,r,4,**********,-2147483648),a.TYPED_ARRAY_SUPPORT?(this[r]=255&t,this[r+1]=t>>>8,this[r+2]=t>>>16,this[r+3]=t>>>24):Y(this,t,r,!0),r+4},a.prototype.writeInt32BE=function(t,r,n){return t=+t,r|=0,n||I(this,t,r,4,**********,-2147483648),t<0&&(t=4294967295+t+1),a.TYPED_ARRAY_SUPPORT?(this[r]=t>>>24,this[r+1]=t>>>16,this[r+2]=t>>>8,this[r+3]=255&t):Y(this,t,r,!1),r+4},a.prototype.writeFloatLE=function(t,r,n){return M(this,t,r,!0,n)},a.prototype.writeFloatBE=function(t,r,n){return M(this,t,r,!1,n)},a.prototype.writeDoubleLE=function(t,r,n){return C(this,t,r,!0,n)},a.prototype.writeDoubleBE=function(t,r,n){return C(this,t,r,!1,n)},a.prototype.copy=function(t,r,n,e){if(n||(n=0),e||0===e||(e=this.length),r>=t.length&&(r=t.length),r||(r=0),e>0&&e<n&&(e=n),e===n)return 0;if(0===t.length||0===this.length)return 0;if(r<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("sourceStart out of bounds");if(e<0)throw new RangeError("sourceEnd out of bounds");e>this.length&&(e=this.length),t.length-r<e-n&&(e=t.length-r+n);var o,i=e-n;if(this===t&&n<r&&r<e)for(o=i-1;o>=0;--o)t[o+r]=this[o+n];else if(i<1e3||!a.TYPED_ARRAY_SUPPORT)for(o=0;o<i;++o)t[o+r]=this[o+n];else Uint8Array.prototype.set.call(t,this.subarray(n,n+i),r);return i},a.prototype.fill=function(t,r,n,e){if("string"==typeof t){if("string"==typeof r?(e=r,r=0,n=this.length):"string"==typeof n&&(e=n,n=this.length),1===t.length){var o=t.charCodeAt(0);o<256&&(t=o)}if(void 0!==e&&"string"!=typeof e)throw new TypeError("encoding must be a string");if("string"==typeof e&&!a.isEncoding(e))throw new TypeError("Unknown encoding: "+e)}else"number"==typeof t&&(t&=255);if(r<0||this.length<r||this.length<n)throw new RangeError("Out of range index");if(n<=r)return this;var i;if(r>>>=0,n=void 0===n?this.length:n>>>0,t||(t=0),"number"==typeof t)for(i=r;i<n;++i)this[i]=t;else{var u=a.isBuffer(t)?t:H(new a(t,e).toString()),f=u.length;for(i=0;i<n-r;++i)this[i+r]=u[i%f]}return this};var D=/[^+\/0-9A-Za-z-_]/g;function N(t){return t<16?"0"+t.toString(16):t.toString(16)}function H(t,r){var n;r=r||1/0;for(var e=t.length,o=null,i=[],u=0;u<e;++u){if((n=t.charCodeAt(u))>55295&&n<57344){if(!o){if(n>56319){(r-=3)>-1&&i.push(239,191,189);continue}if(u+1===e){(r-=3)>-1&&i.push(239,191,189);continue}o=n;continue}if(n<56320){(r-=3)>-1&&i.push(239,191,189),o=n;continue}n=65536+(o-55296<<10|n-56320)}else o&&(r-=3)>-1&&i.push(239,191,189);if(o=null,n<128){if((r-=1)<0)break;i.push(n)}else if(n<2048){if((r-=2)<0)break;i.push(n>>6|192,63&n|128)}else if(n<65536){if((r-=3)<0)break;i.push(n>>12|224,n>>6&63|128,63&n|128)}else{if(!(n<1114112))throw new Error("Invalid code point");if((r-=4)<0)break;i.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}}return i}function k(t){return e.toByteArray(function(t){if((t=function(t){return t.trim?t.trim():t.replace(/^\s+|\s+$/g,"")}(t).replace(D,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function F(t,r,n,e){for(var o=0;o<e&&!(o+n>=r.length||o>=t.length);++o)r[o+n]=t[o];return o}}).call(this,n(3))},function(t,r){t.exports=o,o.strict=i,o.loose=u;var n=Object.prototype.toString,e={"[object Int8Array]":!0,"[object Int16Array]":!0,"[object Int32Array]":!0,"[object Uint8Array]":!0,"[object Uint8ClampedArray]":!0,"[object Uint16Array]":!0,"[object Uint32Array]":!0,"[object Float32Array]":!0,"[object Float64Array]":!0};function o(t){return i(t)||u(t)}function i(t){return t instanceof Int8Array||t instanceof Int16Array||t instanceof Int32Array||t instanceof Uint8Array||t instanceof Uint8ClampedArray||t instanceof Uint16Array||t instanceof Uint32Array||t instanceof Float32Array||t instanceof Float64Array}function u(t){return e[n.call(t)]}},function(t,r,n){"use strict";(function(t){Object.defineProperty(r,"__esModule",{value:!0}),r.removeHexLeadingZeros=r.sanitizeHex=r.addHexPrefix=r.removeHexPrefix=r.padRight=r.padLeft=r.sanitizeBytes=r.swapHex=r.swapBytes=r.splitBytes=r.calcByteLength=r.trimRight=r.trimLeft=r.concatArrays=r.concatBuffers=r.getEncoding=r.getType=r.isArrayBuffer=r.isTypedArray=r.isBuffer=r.isHexString=r.isBinaryString=r.binaryToNumber=r.binaryToUtf8=r.binaryToHex=r.binaryToArray=r.binaryToBuffer=r.numberToBinary=r.numberToUtf8=r.numberToHex=r.numberToArray=r.numberToBuffer=r.utf8ToBinary=r.utf8ToNumber=r.utf8ToHex=r.utf8ToArray=r.utf8ToBuffer=r.hexToBinary=r.hexToNumber=r.hexToUtf8=r.hexToArray=r.hexToBuffer=r.arrayToBinary=r.arrayToNumber=r.arrayToUtf8=r.arrayToHex=r.arrayToBuffer=r.bufferToBinary=r.bufferToNumber=r.bufferToUtf8=r.bufferToHex=r.bufferToArray=void 0;const e=n(7),o=e.__importDefault(n(1)),i=e.__importDefault(n(8));function u(t){return new Uint8Array(t)}function f(t,r=!1){const n=t.toString("hex");return r?L(n):n}function a(t){return t.toString("utf8")}function s(t){return t.readUIntBE(0,t.length)}function c(t){return i.default(t)}function h(t,r=!1){return f(c(t),r)}function l(t){return a(c(t))}function p(t){return s(c(t))}function y(t){return Array.from(t).map(m).join("")}function g(r){return t.from(Y(r),"hex")}function d(t){return u(g(t))}function b(t){return y(d(t))}function w(r){return t.from(r,"utf8")}function v(t){return u(w(t))}function m(t){return I((t>>>0).toString(2))}function _(t){return c(A(t))}function A(t){return new Uint8Array(U(t).map(t=>parseInt(t,2)))}function T(t,r){return h(A(t),r)}function E(t){return!("string"!=typeof t||!new RegExp(/^[01]+$/).test(t))&&t.length%8==0}function B(t,r){return!("string"!=typeof t||!t.match(/^0x[0-9A-Fa-f]*$/))&&(!r||t.length===2+2*r)}function P(r){return t.isBuffer(r)}function R(t){return o.default.strict(t)&&!P(t)}function x(t){return!R(t)&&!P(t)&&void 0!==t.byteLength}function S(t,r=8){const n=t%r;return n?(t-n)/r*r+r:t}function U(t,r=8){const n=I(t).match(new RegExp(`.{${r}}`,"gi"));return Array.from(n||[])}function O(t){return U(t).map(M).join("")}function I(t,r=8,n="0"){return j(t,S(t.length,r),n)}function j(t,r,n="0"){return C(t,r,!0,n)}function Y(t){return t.replace(/^0x/,"")}function L(t){return t.startsWith("0x")?t:"0x"+t}function M(t){return t.split("").reverse().join("")}function C(t,r,n,e="0"){const o=r-t.length;let i=t;if(o>0){const r=e.repeat(o);i=n?r+t:t+r}return i}r.bufferToArray=u,r.bufferToHex=f,r.bufferToUtf8=a,r.bufferToNumber=s,r.bufferToBinary=function(t){return y(u(t))},r.arrayToBuffer=c,r.arrayToHex=h,r.arrayToUtf8=l,r.arrayToNumber=p,r.arrayToBinary=y,r.hexToBuffer=g,r.hexToArray=d,r.hexToUtf8=function(t){return a(g(t))},r.hexToNumber=function(t){return p(d(t))},r.hexToBinary=b,r.utf8ToBuffer=w,r.utf8ToArray=v,r.utf8ToHex=function(t,r=!1){return f(w(t),r)},r.utf8ToNumber=function(t){const r=parseInt(t,10);return function(t,r){if(!t)throw new Error(r)}(!function(t){return void 0===t}(r),"Number can only safely store up to 53 bits"),r},r.utf8ToBinary=function(t){return y(v(t))},r.numberToBuffer=function(t){return _(m(t))},r.numberToArray=function(t){return A(m(t))},r.numberToHex=function(t,r){return T(m(t),r)},r.numberToUtf8=function(t){return""+t},r.numberToBinary=m,r.binaryToBuffer=_,r.binaryToArray=A,r.binaryToHex=T,r.binaryToUtf8=function(t){return l(A(t))},r.binaryToNumber=function(t){return p(A(t))},r.isBinaryString=E,r.isHexString=B,r.isBuffer=P,r.isTypedArray=R,r.isArrayBuffer=x,r.getType=function(t){return P(t)?"buffer":R(t)?"typed-array":x(t)?"array-buffer":Array.isArray(t)?"array":typeof t},r.getEncoding=function(t){return E(t)?"binary":B(t)?"hex":"utf8"},r.concatBuffers=function(...r){return t.concat(r)},r.concatArrays=function(...t){let r=[];return t.forEach(t=>r=r.concat(Array.from(t))),new Uint8Array([...r])},r.trimLeft=function(t,r){const n=t.length-r;return n>0&&(t=t.slice(n)),t},r.trimRight=function(t,r){return t.slice(0,r)},r.calcByteLength=S,r.splitBytes=U,r.swapBytes=O,r.swapHex=function(t){return T(O(b(t)))},r.sanitizeBytes=I,r.padLeft=j,r.padRight=function(t,r,n="0"){return C(t,r,!1,n)},r.removeHexPrefix=Y,r.addHexPrefix=L,r.sanitizeHex=function(t){return(t=I(t=Y(t),2))&&(t=L(t)),t},r.removeHexLeadingZeros=function(t){const r=t.startsWith("0x");return t=(t=Y(t)).startsWith("0")?t.substring(1):t,r?L(t):t}}).call(this,n(0).Buffer)},function(t,r){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(t){"object"==typeof window&&(n=window)}t.exports=n},function(t,r,n){"use strict";r.byteLength=function(t){var r=s(t),n=r[0],e=r[1];return 3*(n+e)/4-e},r.toByteArray=function(t){var r,n,e=s(t),u=e[0],f=e[1],a=new i(function(t,r,n){return 3*(r+n)/4-n}(0,u,f)),c=0,h=f>0?u-4:u;for(n=0;n<h;n+=4)r=o[t.charCodeAt(n)]<<18|o[t.charCodeAt(n+1)]<<12|o[t.charCodeAt(n+2)]<<6|o[t.charCodeAt(n+3)],a[c++]=r>>16&255,a[c++]=r>>8&255,a[c++]=255&r;2===f&&(r=o[t.charCodeAt(n)]<<2|o[t.charCodeAt(n+1)]>>4,a[c++]=255&r);1===f&&(r=o[t.charCodeAt(n)]<<10|o[t.charCodeAt(n+1)]<<4|o[t.charCodeAt(n+2)]>>2,a[c++]=r>>8&255,a[c++]=255&r);return a},r.fromByteArray=function(t){for(var r,n=t.length,o=n%3,i=[],u=0,f=n-o;u<f;u+=16383)i.push(c(t,u,u+16383>f?f:u+16383));1===o?(r=t[n-1],i.push(e[r>>2]+e[r<<4&63]+"==")):2===o&&(r=(t[n-2]<<8)+t[n-1],i.push(e[r>>10]+e[r>>4&63]+e[r<<2&63]+"="));return i.join("")};for(var e=[],o=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,u="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",f=0,a=u.length;f<a;++f)e[f]=u[f],o[u.charCodeAt(f)]=f;function s(t){var r=t.length;if(r%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var n=t.indexOf("=");return-1===n&&(n=r),[n,n===r?0:4-n%4]}function c(t,r,n){for(var o,i,u=[],f=r;f<n;f+=3)o=(t[f]<<16&16711680)+(t[f+1]<<8&65280)+(255&t[f+2]),u.push(e[(i=o)>>18&63]+e[i>>12&63]+e[i>>6&63]+e[63&i]);return u.join("")}o["-".charCodeAt(0)]=62,o["_".charCodeAt(0)]=63},function(t,r){
/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */
r.read=function(t,r,n,e,o){var i,u,f=8*o-e-1,a=(1<<f)-1,s=a>>1,c=-7,h=n?o-1:0,l=n?-1:1,p=t[r+h];for(h+=l,i=p&(1<<-c)-1,p>>=-c,c+=f;c>0;i=256*i+t[r+h],h+=l,c-=8);for(u=i&(1<<-c)-1,i>>=-c,c+=e;c>0;u=256*u+t[r+h],h+=l,c-=8);if(0===i)i=1-s;else{if(i===a)return u?NaN:1/0*(p?-1:1);u+=Math.pow(2,e),i-=s}return(p?-1:1)*u*Math.pow(2,i-e)},r.write=function(t,r,n,e,o,i){var u,f,a,s=8*i-o-1,c=(1<<s)-1,h=c>>1,l=23===o?Math.pow(2,-24)-Math.pow(2,-77):0,p=e?0:i-1,y=e?1:-1,g=r<0||0===r&&1/r<0?1:0;for(r=Math.abs(r),isNaN(r)||r===1/0?(f=isNaN(r)?1:0,u=c):(u=Math.floor(Math.log(r)/Math.LN2),r*(a=Math.pow(2,-u))<1&&(u--,a*=2),(r+=u+h>=1?l/a:l*Math.pow(2,1-h))*a>=2&&(u++,a/=2),u+h>=c?(f=0,u=c):u+h>=1?(f=(r*a-1)*Math.pow(2,o),u+=h):(f=r*Math.pow(2,h-1)*Math.pow(2,o),u=0));o>=8;t[n+p]=255&f,p+=y,f/=256,o-=8);for(u=u<<o|f,s+=o;s>0;t[n+p]=255&u,p+=y,u/=256,s-=8);t[n+p-y]|=128*g}},function(t,r){var n={}.toString;t.exports=Array.isArray||function(t){return"[object Array]"==n.call(t)}},function(t,r,n){"use strict";n.r(r),n.d(r,"__extends",(function(){return o})),n.d(r,"__assign",(function(){return i})),n.d(r,"__rest",(function(){return u})),n.d(r,"__decorate",(function(){return f})),n.d(r,"__param",(function(){return a})),n.d(r,"__metadata",(function(){return s})),n.d(r,"__awaiter",(function(){return c})),n.d(r,"__generator",(function(){return h})),n.d(r,"__createBinding",(function(){return l})),n.d(r,"__exportStar",(function(){return p})),n.d(r,"__values",(function(){return y})),n.d(r,"__read",(function(){return g})),n.d(r,"__spread",(function(){return d})),n.d(r,"__spreadArrays",(function(){return b})),n.d(r,"__await",(function(){return w})),n.d(r,"__asyncGenerator",(function(){return v})),n.d(r,"__asyncDelegator",(function(){return m})),n.d(r,"__asyncValues",(function(){return _})),n.d(r,"__makeTemplateObject",(function(){return A})),n.d(r,"__importStar",(function(){return T})),n.d(r,"__importDefault",(function(){return E})),n.d(r,"__classPrivateFieldGet",(function(){return B})),n.d(r,"__classPrivateFieldSet",(function(){return P}));
/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */
var e=function(t,r){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var n in r)r.hasOwnProperty(n)&&(t[n]=r[n])})(t,r)};function o(t,r){function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}var i=function(){return(i=Object.assign||function(t){for(var r,n=1,e=arguments.length;n<e;n++)for(var o in r=arguments[n])Object.prototype.hasOwnProperty.call(r,o)&&(t[o]=r[o]);return t}).apply(this,arguments)};function u(t,r){var n={};for(var e in t)Object.prototype.hasOwnProperty.call(t,e)&&r.indexOf(e)<0&&(n[e]=t[e]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(e=Object.getOwnPropertySymbols(t);o<e.length;o++)r.indexOf(e[o])<0&&Object.prototype.propertyIsEnumerable.call(t,e[o])&&(n[e[o]]=t[e[o]])}return n}function f(t,r,n,e){var o,i=arguments.length,u=i<3?r:null===e?e=Object.getOwnPropertyDescriptor(r,n):e;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)u=Reflect.decorate(t,r,n,e);else for(var f=t.length-1;f>=0;f--)(o=t[f])&&(u=(i<3?o(u):i>3?o(r,n,u):o(r,n))||u);return i>3&&u&&Object.defineProperty(r,n,u),u}function a(t,r){return function(n,e){r(n,e,t)}}function s(t,r){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(t,r)}function c(t,r,n,e){return new(n||(n=Promise))((function(o,i){function u(t){try{a(e.next(t))}catch(t){i(t)}}function f(t){try{a(e.throw(t))}catch(t){i(t)}}function a(t){var r;t.done?o(t.value):(r=t.value,r instanceof n?r:new n((function(t){t(r)}))).then(u,f)}a((e=e.apply(t,r||[])).next())}))}function h(t,r){var n,e,o,i,u={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:f(0),throw:f(1),return:f(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function f(i){return function(f){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;u;)try{if(n=1,e&&(o=2&i[0]?e.return:i[0]?e.throw||((o=e.return)&&o.call(e),0):e.next)&&!(o=o.call(e,i[1])).done)return o;switch(e=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return u.label++,{value:i[1],done:!1};case 5:u.label++,e=i[1],i=[0];continue;case 7:i=u.ops.pop(),u.trys.pop();continue;default:if(!(o=u.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){u=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){u.label=i[1];break}if(6===i[0]&&u.label<o[1]){u.label=o[1],o=i;break}if(o&&u.label<o[2]){u.label=o[2],u.ops.push(i);break}o[2]&&u.ops.pop(),u.trys.pop();continue}i=r.call(t,u)}catch(t){i=[6,t],e=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,f])}}}function l(t,r,n,e){void 0===e&&(e=n),t[e]=r[n]}function p(t,r){for(var n in t)"default"===n||r.hasOwnProperty(n)||(r[n]=t[n])}function y(t){var r="function"==typeof Symbol&&Symbol.iterator,n=r&&t[r],e=0;if(n)return n.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&e>=t.length&&(t=void 0),{value:t&&t[e++],done:!t}}};throw new TypeError(r?"Object is not iterable.":"Symbol.iterator is not defined.")}function g(t,r){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var e,o,i=n.call(t),u=[];try{for(;(void 0===r||r-- >0)&&!(e=i.next()).done;)u.push(e.value)}catch(t){o={error:t}}finally{try{e&&!e.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return u}function d(){for(var t=[],r=0;r<arguments.length;r++)t=t.concat(g(arguments[r]));return t}function b(){for(var t=0,r=0,n=arguments.length;r<n;r++)t+=arguments[r].length;var e=Array(t),o=0;for(r=0;r<n;r++)for(var i=arguments[r],u=0,f=i.length;u<f;u++,o++)e[o]=i[u];return e}function w(t){return this instanceof w?(this.v=t,this):new w(t)}function v(t,r,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e,o=n.apply(t,r||[]),i=[];return e={},u("next"),u("throw"),u("return"),e[Symbol.asyncIterator]=function(){return this},e;function u(t){o[t]&&(e[t]=function(r){return new Promise((function(n,e){i.push([t,r,n,e])>1||f(t,r)}))})}function f(t,r){try{(n=o[t](r)).value instanceof w?Promise.resolve(n.value.v).then(a,s):c(i[0][2],n)}catch(t){c(i[0][3],t)}var n}function a(t){f("next",t)}function s(t){f("throw",t)}function c(t,r){t(r),i.shift(),i.length&&f(i[0][0],i[0][1])}}function m(t){var r,n;return r={},e("next"),e("throw",(function(t){throw t})),e("return"),r[Symbol.iterator]=function(){return this},r;function e(e,o){r[e]=t[e]?function(r){return(n=!n)?{value:w(t[e](r)),done:"return"===e}:o?o(r):r}:o}}function _(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r,n=t[Symbol.asyncIterator];return n?n.call(t):(t=y(t),r={},e("next"),e("throw"),e("return"),r[Symbol.asyncIterator]=function(){return this},r);function e(n){r[n]=t[n]&&function(r){return new Promise((function(e,o){(function(t,r,n,e){Promise.resolve(e).then((function(r){t({value:r,done:n})}),r)})(e,o,(r=t[n](r)).done,r.value)}))}}}function A(t,r){return Object.defineProperty?Object.defineProperty(t,"raw",{value:r}):t.raw=r,t}function T(t){if(t&&t.__esModule)return t;var r={};if(null!=t)for(var n in t)Object.hasOwnProperty.call(t,n)&&(r[n]=t[n]);return r.default=t,r}function E(t){return t&&t.__esModule?t:{default:t}}function B(t,r){if(!r.has(t))throw new TypeError("attempted to get private field on non-instance");return r.get(t)}function P(t,r,n){if(!r.has(t))throw new TypeError("attempted to set private field on non-instance");return r.set(t,n),n}},function(t,r,n){(function(r){var e=n(1).strict;t.exports=function(t){if(e(t)){var n=r.from(t.buffer);return t.byteLength!==t.buffer.byteLength&&(n=n.slice(t.byteOffset,t.byteOffset+t.byteLength)),n}return r.from(t)}}).call(this,n(0).Buffer)}])}));