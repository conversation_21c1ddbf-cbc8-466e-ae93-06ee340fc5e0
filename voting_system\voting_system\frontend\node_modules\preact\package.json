{"name": "preact", "amdName": "preact", "version": "10.4.1", "private": false, "description": "Fast 3kb React-compatible Virtual DOM library.", "main": "dist/preact.js", "module": "dist/preact.module.js", "umd:main": "dist/preact.umd.js", "unpkg": "dist/preact.umd.js", "source": "src/index.js", "exports": {".": {"browser": "./dist/preact.module.js", "umd": "./dist/preact.umd.js", "import": "./dist/preact.mjs", "require": "./dist/preact.js"}, "./compat": {"browser": "./compat/dist/compat.module.js", "umd": "./compat/dist/compat.umd.js", "require": "./compat/dist/compat.js", "import": "./compat/dist/compat.mjs"}, "./debug": {"browser": "./debug/dist/debug.module.js", "umd": "./debug/dist/debug.umd.js", "require": "./debug/dist/debug.js", "import": "./debug/dist/debug.mjs"}, "./devtools": {"browser": "./devtools/dist/devtools.module.js", "umd": "./devtools/dist/devtools.umd.js", "require": "./devtools/dist/devtools.js", "import": "./devtools/dist/devtools.mjs"}, "./hooks": {"browser": "./hooks/dist/hooks.module.js", "umd": "./hooks/dist/hooks.umd.js", "require": "./hooks/dist/hooks.js", "import": "./hooks/dist/hooks.mjs"}, "./test-utils": {"browser": "./test-utils/dist/testUtils.module.js", "umd": "./test-utils/dist/testUtils.umd.js", "require": "./test-utils/dist/testUtils.js", "import": "./test-utils/dist/testUtils.mjs"}, "./compat/server": {"require": "./compat/server.js"}, "./package.json": "./package.json", "./": "./"}, "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/preact"}, "types": "src/index.d.ts", "scripts": {"prepare": "run-s build", "build": "npm-run-all --parallel build:*", "build:core": "microbundle build --raw", "build:debug": "microbundle build --raw --cwd debug", "build:devtools": "microbundle build --raw --cwd devtools", "build:hooks": "microbundle build --raw --cwd hooks", "build:test-utils": "microbundle build --raw --cwd test-utils", "build:compat": "microbundle build --raw --cwd compat --globals 'preact/hooks=preactHooks'", "postbuild": "node ./config/node-13-exports.js", "dev": "microbundle watch --raw --format cjs", "dev:hooks": "microbundle watch --raw --format cjs --cwd hooks", "dev:compat": "microbundle watch --raw --format cjs --cwd compat --globals 'preact/hooks=preactHooks'", "test": "npm-run-all build lint test:unit", "test:unit": "run-p test:mocha test:karma:minify test:ts", "test:ts": "run-p test:ts:*", "test:ts:core": "tsc -p test/ts/ && mocha --require \"@babel/register\" test/ts/**/*-test.js", "test:ts:compat": "tsc -p compat/test/ts/", "test:mocha": "mocha --recursive --require \"@babel/register\" test/shared test/node", "test:mocha:watch": "npm run test:mocha -- --watch", "test:karma": "cross-env COVERAGE=true karma start karma.conf.js --single-run", "test:karma:minify": "cross-env COVERAGE=true MINIFY=true karma start karma.conf.js --single-run", "test:karma:watch": "karma start karma.conf.js --no-single-run", "test:karma:hooks": "cross-env COVERAGE=false karma start karma.conf.js --grep=hooks/test/browser/**.js --no-single-run", "test:karma:test-utils": "cross-env PERFORMANCE=false COVERAGE=false karma start karma.conf.js --grep=test-utils/test/shared/**.js --no-single-run", "test:karma:bench": "cross-env PERFORMANCE=true COVERAGE=false karma start karma.conf.js --grep=test/benchmarks/**.js --single-run", "benchmark": "npm run test:karma:bench -- no-single-run", "lint": "eslint src test debug compat hooks test-utils"}, "eslintConfig": {"extends": ["developit", "prettier"], "settings": {"react": {"pragma": "createElement"}}, "rules": {"camelcase": [1, {"allow": ["__test__*", "unstable_*", "UNSAFE_*"]}], "no-unused-vars": [2, {"args": "none", "varsIgnorePattern": "^h|React$"}], "prefer-rest-params": 0, "prefer-spread": 0, "no-cond-assign": 0, "react/jsx-no-bind": 0, "react/no-danger": "off", "react/prefer-stateless-function": 0, "react/sort-comp": 0, "jest/valid-expect": 0, "jest/no-disabled-tests": 0, "react/no-find-dom-node": 0}}, "eslintIgnore": ["test/fixtures", "test/ts/", "*.ts", "dist"], "prettier": {"singleQuote": true, "trailingComma": "none", "useTabs": true, "tabWidth": 2}, "lint-staged": {"**/*.{js,jsx,ts,tsx}": ["prettier --write", "git add"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "files": ["src", "dist", "compat/dist", "compat/src", "compat/server.js", "compat/package.json", "debug/dist", "debug/src", "debug/package.json", "devtools/dist", "devtools/src", "devtools/package.json", "hooks/dist", "hooks/src", "hooks/package.json", "test-utils/src", "test-utils/package.json", "test-utils/dist"], "keywords": ["preact", "react", "ui", "user interface", "virtual dom", "vdom", "components", "dom diff"], "authors": ["<PERSON> <<EMAIL>>"], "repository": "preactjs/preact", "bugs": "https://github.com/preactjs/preact/issues", "homepage": "https://preactjs.com", "devDependencies": {"@babel/core": "^7.7.0", "@babel/plugin-proposal-object-rest-spread": "^7.6.2", "@babel/plugin-transform-react-jsx": "^7.7.0", "@babel/plugin-transform-react-jsx-source": "^7.7.4", "@babel/preset-env": "^7.7.1", "@babel/register": "^7.7.0", "@types/chai": "^4.1.2", "@types/mocha": "^5.0.0", "@types/node": "^10.5.2", "babel-loader": "^8.0.6", "babel-plugin-istanbul": "^6.0.0", "babel-plugin-transform-async-to-promises": "^0.8.15", "babel-plugin-transform-rename-properties": "0.0.3", "benchmark": "^2.1.4", "chai": "^4.1.2", "coveralls": "^3.0.0", "cross-env": "^5.2.0", "diff": "^3.5.0", "eslint": "5.15.1", "eslint-config-developit": "^1.1.1", "eslint-config-prettier": "^6.5.0", "eslint-plugin-react": "7.12.4", "husky": "^3.0.9", "karma": "^3.0.0", "karma-chai-sinon": "^0.1.5", "karma-chrome-launcher": "^2.2.0", "karma-coverage": "^2.0.1", "karma-mocha": "^1.3.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-sinon": "^1.0.5", "karma-sourcemap-loader": "^0.3.7", "karma-webpack": "^3.0.5", "lint-staged": "^9.4.2", "lodash": "^4.17.10", "microbundle": "^0.11.0", "mocha": "^5.2.0", "npm-merge-driver-install": "^1.1.1", "npm-run-all": "^4.0.0", "prettier": "^1.18.2", "prop-types": "^15.7.2", "sinon": "^6.1.3", "sinon-chai": "^3.0.0", "typescript": "^3.0.1", "webpack": "^4.3.0"}}