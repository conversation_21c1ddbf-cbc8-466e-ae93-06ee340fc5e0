"""
Fork names for Ethereum network upgrades.
"""


class ForkName:
    """
    Constants for each fork name.

    .. doctest::

        >>> from eth_typing import ForkName
        >>> ForkName.Frontier
        'Frontier'
    """

    Frontier = "Frontier"
    Homestead = "Homestead"
    EIP150 = "EIP150"
    EIP158 = "EIP158"
    Byzantium = "Byzantium"
    Constantinople = "Constantinople"
    Metropolis = "Metropolis"
    ConstantinopleFix = "ConstantinopleFix"
    Istanbul = "Istanbul"
    Berlin = "Berlin"
    London = "London"
    ArrowGlacier = "ArrowGlacier"
    GrayGlacier = "GrayGlacier"
    Paris = "Paris"
    Shanghai = "Shanghai"
    Cancun = "Cancun"
    Prague = "Prague"
