import React, { useState, useEffect } from 'react';

const WorkerDashboard = ({ workerData, onLogout, onNavigate }) => {
    const [stats, setStats] = useState({
        totalRegistered: 0,
        todayRegistered: 0,
        pendingVerification: 0
    });
    const [recentRegistrations, setRecentRegistrations] = useState([]);
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        fetchWorkerStats();
        fetchRecentRegistrations();
    }, []);

    const fetchWorkerStats = async () => {
        try {
            const token = localStorage.getItem('workerToken');
            const response = await fetch('/api/workers/me/', {
                headers: {
                    'Authorization': `Token ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (response.ok) {
                const data = await response.json();
                // Calculate stats from worker data
                setStats({
                    totalRegistered: data.registered_voters?.length || 0,
                    todayRegistered: 0, // Would need to filter by today's date
                    pendingVerification: 0 // Would need to filter by verification status
                });
            }
        } catch (error) {
            console.error('Error fetching worker stats:', error);
        }
    };

    const fetchRecentRegistrations = async () => {
        try {
            const token = localStorage.getItem('workerToken');
            const response = await fetch('/api/voters/', {
                headers: {
                    'Authorization': `Token ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (response.ok) {
                const data = await response.json();
                // Get recent registrations (last 10)
                setRecentRegistrations(data.slice(0, 10));
            }
        } catch (error) {
            console.error('Error fetching recent registrations:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const handleLogout = () => {
        localStorage.removeItem('workerToken');
        localStorage.removeItem('workerData');
        onLogout();
    };

    if (isLoading) {
        return (
            <div className="container mt-5">
                <div className="text-center">
                    <div className="spinner-border" role="status">
                        <span className="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="container mt-4">
            {/* Header */}
            <div className="row mb-4">
                <div className="col-md-8">
                    <h2>Worker Dashboard</h2>
                    <p className="text-muted">Welcome, {workerData.username}</p>
                    <p className="text-muted">Employee ID: {workerData.employee_id}</p>
                    {workerData.station_assigned && (
                        <p className="text-muted">Assigned Station: {workerData.station_assigned}</p>
                    )}
                </div>
                <div className="col-md-4 text-end">
                    <button 
                        className="btn btn-primary me-2"
                        onClick={() => onNavigate('worker-register-voter')}
                    >
                        Register New Voter
                    </button>
                    <button 
                        className="btn btn-outline-secondary"
                        onClick={handleLogout}
                    >
                        Logout
                    </button>
                </div>
            </div>

            {/* Stats Cards */}
            <div className="row mb-4">
                <div className="col-md-4">
                    <div className="card bg-primary text-white">
                        <div className="card-body">
                            <h5 className="card-title">Total Registered</h5>
                            <h2 className="card-text">{stats.totalRegistered}</h2>
                        </div>
                    </div>
                </div>
                <div className="col-md-4">
                    <div className="card bg-success text-white">
                        <div className="card-body">
                            <h5 className="card-title">Today's Registrations</h5>
                            <h2 className="card-text">{stats.todayRegistered}</h2>
                        </div>
                    </div>
                </div>
                <div className="col-md-4">
                    <div className="card bg-warning text-white">
                        <div className="card-body">
                            <h5 className="card-title">Pending Verification</h5>
                            <h2 className="card-text">{stats.pendingVerification}</h2>
                        </div>
                    </div>
                </div>
            </div>

            {/* Recent Registrations */}
            <div className="row">
                <div className="col-12">
                    <div className="card">
                        <div className="card-header">
                            <h5 className="mb-0">Recent Registrations</h5>
                        </div>
                        <div className="card-body">
                            {recentRegistrations.length > 0 ? (
                                <div className="table-responsive">
                                    <table className="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>ID Number</th>
                                                <th>Name</th>
                                                <th>Constituency</th>
                                                <th>Registration Date</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {recentRegistrations.map((voter, index) => (
                                                <tr key={index}>
                                                    <td>{voter.id_number}</td>
                                                    <td>{voter.user?.first_name} {voter.user?.last_name}</td>
                                                    <td>{voter.constituency?.name}</td>
                                                    <td>{new Date(voter.registration_date).toLocaleDateString()}</td>
                                                    <td>
                                                        <span className={`badge ${voter.is_verified ? 'bg-success' : 'bg-warning'}`}>
                                                            {voter.is_verified ? 'Verified' : 'Pending'}
                                                        </span>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>
                            ) : (
                                <p className="text-muted">No registrations found.</p>
                            )}
                        </div>
                    </div>
                </div>
            </div>

            {/* Quick Actions */}
            <div className="row mt-4">
                <div className="col-12">
                    <div className="card">
                        <div className="card-header">
                            <h5 className="mb-0">Quick Actions</h5>
                        </div>
                        <div className="card-body">
                            <div className="row">
                                <div className="col-md-3">
                                    <button 
                                        className="btn btn-outline-primary w-100 mb-2"
                                        onClick={() => onNavigate('worker-register-voter')}
                                    >
                                        Register Voter
                                    </button>
                                </div>
                                <div className="col-md-3">
                                    <button 
                                        className="btn btn-outline-info w-100 mb-2"
                                        onClick={() => onNavigate('worker-view-registrations')}
                                    >
                                        View All Registrations
                                    </button>
                                </div>
                                <div className="col-md-3">
                                    <button 
                                        className="btn btn-outline-success w-100 mb-2"
                                        onClick={() => onNavigate('worker-statistics')}
                                    >
                                        View Statistics
                                    </button>
                                </div>
                                <div className="col-md-3">
                                    <button 
                                        className="btn btn-outline-secondary w-100 mb-2"
                                        onClick={() => onNavigate('worker-help')}
                                    >
                                        Help & Support
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default WorkerDashboard;
