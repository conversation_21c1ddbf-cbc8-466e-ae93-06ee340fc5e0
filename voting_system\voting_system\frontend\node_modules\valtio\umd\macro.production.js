!function(e,n){"object"==typeof exports&&"undefined"!=typeof module?module.exports=n(require("@babel/helper-module-imports"),require("@babel/types"),require("babel-plugin-macros")):"function"==typeof define&&define.amd?define(["@babel/helper-module-imports","@babel/types","babel-plugin-macros"],n):(e="undefined"!=typeof globalThis?globalThis:e||self).valtioMacro=n(e.helperModuleImports,e.t,e.babelPluginMacros)}(this,(function(e,n,t){"use strict";function r(e){var n=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var r=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(n,t,r.get?r:{enumerable:!0,get:function(){return e[t]}})}})),n.default=e,Object.freeze(n)}var a=r(n);return t.createMacro((function(n){var r;null==(r=n.references.useProxy)||r.forEach((function(n){var r,o,i,l,u,c,f=e.addNamed(n,"useSnapshot","valtio"),s=null==(r=n.parentPath)||null==(o=r.get("arguments.0"))?void 0:o.node;if(!a.isIdentifier(s))throw new t.MacroError("no proxy object");var p=a.identifier("valtio_macro_snap_"+s.name);null==(i=n.parentPath)||null==(l=i.parentPath)||l.replaceWith(a.variableDeclaration("const",[a.variableDeclarator(p,a.callExpression(f,[s]))]));var d=0;null==(u=n.parentPath)||null==(c=u.getFunctionParent())||c.traverse({Identifier:function(e){0===d&&e.node!==s&&e.node.name===s.name&&(e.node.name=p.name)},Function:{enter:function(){++d},exit:function(){--d}}})}))}),{configName:"valtio"})}));
