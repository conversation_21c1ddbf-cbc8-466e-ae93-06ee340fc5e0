{"version": 3, "file": "cache.js", "sourceRoot": "", "sources": ["../../src/directives/cache.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAIL,MAAM,EACN,OAAO,GAER,MAAM,gBAAgB,CAAC;AACxB,OAAO,EACL,SAAS,EACT,SAAS,GAGV,MAAM,iBAAiB,CAAC;AACzB,OAAO,EACL,SAAS,EACT,iBAAiB,EACjB,UAAU,EACV,wBAAwB,EACxB,gBAAgB,EAChB,iBAAiB,GAClB,MAAM,yBAAyB,CAAC;AAEjC;;;;GAIG;AACH,MAAM,4BAA4B,GAAG,CACnC,MAA+C,EACzB,EAAE,CACxB,wBAAwB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC;AAE7E,MAAM,cAAe,SAAQ,SAAS;IAIpC,YAAY,QAAkB;QAC5B,KAAK,CAAC,QAAQ,CAAC,CAAC;QAJV,mBAAc,GAAG,IAAI,OAAO,EAAkC,CAAC;IAKvE,CAAC;IAED,MAAM,CAAC,CAAU;QACf,wEAAwE;QACxE,iDAAiD;QACjD,OAAO,CAAC,CAAC,CAAC,CAAC;IACb,CAAC;IAEQ,MAAM,CAAC,aAAwB,EAAE,CAAC,CAAC,CAA4B;QACtE,MAAM,SAAS,GAAG,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC;YAC7C,CAAC,CAAC,4BAA4B,CAAC,IAAI,CAAC,MAAM,CAAC;YAC3C,CAAC,CAAC,IAAI,CAAC;QACT,MAAM,IAAI,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,4BAA4B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAE1E,sEAAsE;QACtE,wEAAwE;QACxE,kBAAkB;QAClB,IAAI,SAAS,KAAK,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,IAAI,SAAS,KAAK,IAAI,CAAC,EAAE;YAC/D,4DAA4D;YAC5D,MAAM,SAAS,GAAG,iBAAiB,CAAC,aAAa,CAAqB,CAAC;YACvE,MAAM,SAAS,GAAG,SAAS,CAAC,GAAG,EAAG,CAAC;YACnC,IAAI,mBAAmB,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAC7D,IAAI,mBAAmB,KAAK,SAAS,EAAE;gBACrC,MAAM,QAAQ,GAAG,QAAQ,CAAC,sBAAsB,EAAE,CAAC;gBACnD,mBAAmB,GAAG,MAAM,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;gBAChD,mBAAmB,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBACxC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC;aACzD;YACD,kBAAkB;YAClB,iBAAiB,CAAC,mBAAmB,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;YACpD,UAAU,CAAC,mBAAmB,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;SACvD;QACD,sEAAsE;QACtE,sEAAsE;QACtE,uBAAuB;QACvB,IAAI,IAAI,KAAK,IAAI,EAAE;YACjB,IAAI,SAAS,KAAK,IAAI,IAAI,SAAS,KAAK,IAAI,EAAE;gBAC5C,MAAM,mBAAmB,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAC1D,IAAI,mBAAmB,KAAK,SAAS,EAAE;oBACrC,0DAA0D;oBAC1D,MAAM,SAAS,GAAG,iBAAiB,CACjC,mBAAmB,CACA,CAAC;oBACtB,MAAM,UAAU,GAAG,SAAS,CAAC,GAAG,EAAG,CAAC;oBACpC,iCAAiC;oBACjC,SAAS,CAAC,aAAa,CAAC,CAAC;oBACzB,UAAU,CAAC,aAAa,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;oBACjD,iBAAiB,CAAC,aAAa,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC;iBAChD;aACF;YACD,wDAAwD;YACxD,IAAI,CAAC,MAAM,GAAG,CAA4C,CAAC;SAC5D;aAAM;YACL,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;SACzB;QACD,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC;CACF;AAED;;;;;;;;;;;;;GAaG;AACH,MAAM,CAAC,MAAM,KAAK,GAAG,SAAS,CAAC,cAAc,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {\n  TemplateResult,\n  ChildPart,\n  RootPart,\n  render,\n  nothing,\n  CompiledTemplateResult,\n} from '../lit-html.js';\nimport {\n  directive,\n  Directive,\n  DirectiveParameters,\n  PartInfo,\n} from '../directive.js';\nimport {\n  clearPart,\n  getCommittedValue,\n  insertPart,\n  isCompiledTemplateResult,\n  isTemplateResult,\n  setCommittedValue,\n} from '../directive-helpers.js';\n\n/**\n * The template strings array contents are not compatible between the two\n * template result types as the compiled template contains a prepared string;\n * only use the returned template strings array as a cache key.\n */\nconst getStringsFromTemplateResult = (\n  result: TemplateResult | CompiledTemplateResult\n): TemplateStringsArray =>\n  isCompiledTemplateResult(result) ? result['_$litType$'].h : result.strings;\n\nclass CacheDirective extends Directive {\n  private _templateCache = new WeakMap<TemplateStringsArray, RootPart>();\n  private _value?: TemplateResult | CompiledTemplateResult;\n\n  constructor(partInfo: PartInfo) {\n    super(partInfo);\n  }\n\n  render(v: unknown) {\n    // Return an array of the value to induce lit-html to create a ChildPart\n    // for the value that we can move into the cache.\n    return [v];\n  }\n\n  override update(containerPart: ChildPart, [v]: DirectiveParameters<this>) {\n    const _valueKey = isTemplateResult(this._value)\n      ? getStringsFromTemplateResult(this._value)\n      : null;\n    const vKey = isTemplateResult(v) ? getStringsFromTemplateResult(v) : null;\n\n    // If the previous value is a TemplateResult and the new value is not,\n    // or is a different Template as the previous value, move the child part\n    // into the cache.\n    if (_valueKey !== null && (vKey === null || _valueKey !== vKey)) {\n      // This is always an array because we return [v] in render()\n      const partValue = getCommittedValue(containerPart) as Array<ChildPart>;\n      const childPart = partValue.pop()!;\n      let cachedContainerPart = this._templateCache.get(_valueKey);\n      if (cachedContainerPart === undefined) {\n        const fragment = document.createDocumentFragment();\n        cachedContainerPart = render(nothing, fragment);\n        cachedContainerPart.setConnected(false);\n        this._templateCache.set(_valueKey, cachedContainerPart);\n      }\n      // Move into cache\n      setCommittedValue(cachedContainerPart, [childPart]);\n      insertPart(cachedContainerPart, undefined, childPart);\n    }\n    // If the new value is a TemplateResult and the previous value is not,\n    // or is a different Template as the previous value, restore the child\n    // part from the cache.\n    if (vKey !== null) {\n      if (_valueKey === null || _valueKey !== vKey) {\n        const cachedContainerPart = this._templateCache.get(vKey);\n        if (cachedContainerPart !== undefined) {\n          // Move the cached part back into the container part value\n          const partValue = getCommittedValue(\n            cachedContainerPart\n          ) as Array<ChildPart>;\n          const cachedPart = partValue.pop()!;\n          // Move cached part back into DOM\n          clearPart(containerPart);\n          insertPart(containerPart, undefined, cachedPart);\n          setCommittedValue(containerPart, [cachedPart]);\n        }\n      }\n      // Because vKey is non null, v must be a TemplateResult.\n      this._value = v as TemplateResult | CompiledTemplateResult;\n    } else {\n      this._value = undefined;\n    }\n    return this.render(v);\n  }\n}\n\n/**\n * Enables fast switching between multiple templates by caching the DOM nodes\n * and TemplateInstances produced by the templates.\n *\n * Example:\n *\n * ```js\n * let checked = false;\n *\n * html`\n *   ${cache(checked ? html`input is checked` : html`input is not checked`)}\n * `\n * ```\n */\nexport const cache = directive(CacheDirective);\n\n/**\n * The type of the class that powers this directive. Necessary for naming the\n * directive's return type.\n */\nexport type {CacheDirective};\n"]}