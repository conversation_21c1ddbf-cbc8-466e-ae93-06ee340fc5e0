{"name": "cross-fetch", "version": "2.2.6", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "homepage": "https://github.com/lquixada/cross-fetch", "main": "dist/node-ponyfill.js", "browser": "dist/browser-ponyfill.js", "typings": "index.d.ts", "scripts": {"pretest:node:bundle": "webpack-cli --progress --config test/node-bundle/webpack.config.js", "build": "rollup -c", "codecov": "nyc report --reporter=text-lcov > coverage.lcov && codecov", "deploy:major": "npm version major && git push --follow-tags", "deploy:minor": "npm version minor && git push --follow-tags", "deploy:patch": "npm version patch && git push --follow-tags", "lint": "standard", "sauce": "./tasks/sauce", "security": "snyk test", "test": "npm run -s test:browser:headless && npm run -s test:node:plain && npm run -s test:node:bundle && npm run -s lint", "test:browser:headless": "mocha-headless-chrome -f test/browser-headless/index.html -a no-sandbox -a disable-setuid-sandbox", "test:browser:plain": "opn test/browser/index.html", "test:node:bundle": "nyc mocha test/node-bundle/index.js", "test:node:plain": "nyc mocha test/node-plain/index.js"}, "lint-staged": {"*.js": ["standard --fix", "git add"]}, "husky": {"hooks": {"pre-commit": "npm run -s build && lint-staged"}}, "standard": {"env": ["mocha", "browser"], "globals": ["expect", "chai", "sinon"], "ignore": ["/dist/", "test/node-bundle/index.js"]}, "nyc": {"temp-dir": ".reports/.coverage"}, "repository": {"type": "git", "url": "https://github.com/lquixada/cross-fetch.git"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "dependencies": {"node-fetch": "^2.6.7", "whatwg-fetch": "^2.0.4"}, "devDependencies": {"chai": "4.2.0", "codecov": "3.1.0", "husky": "1.1.2", "lint-staged": "7.3.0", "mocha": "5.2.0", "mocha-headless-chrome": "2.0.1", "nock": "10.0.1", "nyc": "13.1.0", "opn-cli": "3.1.0", "ora": "3.0.0", "rollup": "0.66.6", "rollup-plugin-copy": "0.2.3", "rollup-plugin-uglify": "6.0.0", "sinon": "7.0.0", "snyk": "1.105.0", "standard": "12.0.1", "webpack": "4.23.0", "webpack-cli": "3.1.2"}, "files": ["dist", "polyfill", "index.d.ts"], "keywords": ["fetch", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"]}