!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("JsonRpcUtils",[],t):"object"==typeof exports?exports.JsonRpcUtils=t():e.JsonRpcUtils=t()}(this,(function(){return function(e){var t={};function r(n){if(t[n])return t[n].exports;var o=t[n]={i:n,l:!1,exports:{}};return e[n].call(o.exports,o,o.exports,r),o.l=!0,o.exports}return r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)r.d(n,o,function(t){return e[t]}.bind(null,o));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=4)}([function(e,t,r){"use strict";r.r(t),r.d(t,"__extends",(function(){return o})),r.d(t,"__assign",(function(){return i})),r.d(t,"__rest",(function(){return u})),r.d(t,"__decorate",(function(){return c})),r.d(t,"__param",(function(){return s})),r.d(t,"__metadata",(function(){return a})),r.d(t,"__awaiter",(function(){return f})),r.d(t,"__generator",(function(){return l})),r.d(t,"__createBinding",(function(){return d})),r.d(t,"__exportStar",(function(){return R})),r.d(t,"__values",(function(){return p})),r.d(t,"__read",(function(){return _})),r.d(t,"__spread",(function(){return y})),r.d(t,"__spreadArrays",(function(){return E})),r.d(t,"__await",(function(){return v})),r.d(t,"__asyncGenerator",(function(){return O})),r.d(t,"__asyncDelegator",(function(){return h})),r.d(t,"__asyncValues",(function(){return m})),r.d(t,"__makeTemplateObject",(function(){return b})),r.d(t,"__importStar",(function(){return g})),r.d(t,"__importDefault",(function(){return S})),r.d(t,"__classPrivateFieldGet",(function(){return w})),r.d(t,"__classPrivateFieldSet",(function(){return A}));
/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */
var n=function(e,t){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(e,t)};function o(e,t){function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var i=function(){return(i=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function u(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]])}return r}function c(e,t,r,n){var o,i=arguments.length,u=i<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)u=Reflect.decorate(e,t,r,n);else for(var c=e.length-1;c>=0;c--)(o=e[c])&&(u=(i<3?o(u):i>3?o(t,r,u):o(t,r))||u);return i>3&&u&&Object.defineProperty(t,r,u),u}function s(e,t){return function(r,n){t(r,n,e)}}function a(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)}function f(e,t,r,n){return new(r||(r=Promise))((function(o,i){function u(e){try{s(n.next(e))}catch(e){i(e)}}function c(e){try{s(n.throw(e))}catch(e){i(e)}}function s(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(u,c)}s((n=n.apply(e,t||[])).next())}))}function l(e,t){var r,n,o,i,u={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(i){return function(c){return function(i){if(r)throw new TypeError("Generator is already executing.");for(;u;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return u.label++,{value:i[1],done:!1};case 5:u.label++,n=i[1],i=[0];continue;case 7:i=u.ops.pop(),u.trys.pop();continue;default:if(!(o=u.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){u=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){u.label=i[1];break}if(6===i[0]&&u.label<o[1]){u.label=o[1],o=i;break}if(o&&u.label<o[2]){u.label=o[2],u.ops.push(i);break}o[2]&&u.ops.pop(),u.trys.pop();continue}i=t.call(e,u)}catch(e){i=[6,e],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,c])}}}function d(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}function R(e,t){for(var r in e)"default"===r||t.hasOwnProperty(r)||(t[r]=e[r])}function p(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function _(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),u=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)u.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return u}function y(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(_(arguments[t]));return e}function E(){for(var e=0,t=0,r=arguments.length;t<r;t++)e+=arguments[t].length;var n=Array(e),o=0;for(t=0;t<r;t++)for(var i=arguments[t],u=0,c=i.length;u<c;u++,o++)n[o]=i[u];return n}function v(e){return this instanceof v?(this.v=e,this):new v(e)}function O(e,t,r){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n,o=r.apply(e,t||[]),i=[];return n={},u("next"),u("throw"),u("return"),n[Symbol.asyncIterator]=function(){return this},n;function u(e){o[e]&&(n[e]=function(t){return new Promise((function(r,n){i.push([e,t,r,n])>1||c(e,t)}))})}function c(e,t){try{(r=o[e](t)).value instanceof v?Promise.resolve(r.value.v).then(s,a):f(i[0][2],r)}catch(e){f(i[0][3],e)}var r}function s(e){c("next",e)}function a(e){c("throw",e)}function f(e,t){e(t),i.shift(),i.length&&c(i[0][0],i[0][1])}}function h(e){var t,r;return t={},n("next"),n("throw",(function(e){throw e})),n("return"),t[Symbol.iterator]=function(){return this},t;function n(n,o){t[n]=e[n]?function(t){return(r=!r)?{value:v(e[n](t)),done:"return"===n}:o?o(t):t}:o}}function m(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,r=e[Symbol.asyncIterator];return r?r.call(e):(e=p(e),t={},n("next"),n("throw"),n("return"),t[Symbol.asyncIterator]=function(){return this},t);function n(r){t[r]=e[r]&&function(t){return new Promise((function(n,o){(function(e,t,r,n){Promise.resolve(n).then((function(t){e({value:t,done:r})}),t)})(n,o,(t=e[r](t)).done,t.value)}))}}}function b(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e}function g(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}function S(e){return e&&e.__esModule?e:{default:e}}function w(e,t){if(!t.has(e))throw new TypeError("attempted to get private field on non-instance");return t.get(e)}function A(e,t,r){if(!t.has(e))throw new TypeError("attempted to set private field on non-instance");return t.set(e,r),r}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DEFAULT_ERROR=t.STANDARD_ERROR_MAP=t.SERVER_ERROR_CODE_RANGE=t.RESERVED_ERROR_CODES=t.SERVER_ERROR=t.INTERNAL_ERROR=t.INVALID_PARAMS=t.METHOD_NOT_FOUND=t.INVALID_REQUEST=t.PARSE_ERROR=void 0,t.PARSE_ERROR="PARSE_ERROR",t.INVALID_REQUEST="INVALID_REQUEST",t.METHOD_NOT_FOUND="METHOD_NOT_FOUND",t.INVALID_PARAMS="INVALID_PARAMS",t.INTERNAL_ERROR="INTERNAL_ERROR",t.SERVER_ERROR="SERVER_ERROR",t.RESERVED_ERROR_CODES=[-32700,-32600,-32601,-32602,-32603],t.SERVER_ERROR_CODE_RANGE=[-32e3,-32099],t.STANDARD_ERROR_MAP={[t.PARSE_ERROR]:{code:-32700,message:"Parse error"},[t.INVALID_REQUEST]:{code:-32600,message:"Invalid Request"},[t.METHOD_NOT_FOUND]:{code:-32601,message:"Method not found"},[t.INVALID_PARAMS]:{code:-32602,message:"Invalid params"},[t.INTERNAL_ERROR]:{code:-32603,message:"Internal error"},[t.SERVER_ERROR]:{code:-32e3,message:"Server error"}},t.DEFAULT_ERROR=t.SERVER_ERROR},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.parseConnectionError=t.validateJsonRpcError=t.getErrorByCode=t.getError=t.isValidErrorCode=t.isReservedErrorCode=t.isServerErrorCode=void 0;const n=r(1);function o(e){return n.RESERVED_ERROR_CODES.includes(e)}function i(e){return"number"==typeof e}function u(e){const t=Object.values(n.STANDARD_ERROR_MAP).find(t=>t.code===e);return t||n.STANDARD_ERROR_MAP[n.DEFAULT_ERROR]}t.isServerErrorCode=function(e){return e<=n.SERVER_ERROR_CODE_RANGE[0]&&e>=n.SERVER_ERROR_CODE_RANGE[1]},t.isReservedErrorCode=o,t.isValidErrorCode=i,t.getError=function(e){return Object.keys(n.STANDARD_ERROR_MAP).includes(e)?n.STANDARD_ERROR_MAP[e]:n.STANDARD_ERROR_MAP[n.DEFAULT_ERROR]},t.getErrorByCode=u,t.validateJsonRpcError=function(e){if(void 0===e.error.code)return{valid:!1,error:"Missing code for JSON-RPC error"};if(void 0===e.error.message)return{valid:!1,error:"Missing message for JSON-RPC error"};if(!i(e.error.code))return{valid:!1,error:"Invalid error code type for JSON-RPC: "+e.error.code};if(o(e.error.code)){const t=u(e.error.code);if(t.message!==n.STANDARD_ERROR_MAP[n.DEFAULT_ERROR].message&&e.error.message===t.message)return{valid:!1,error:"Invalid error code message for JSON-RPC: "+e.error.code}}return{valid:!0}},t.parseConnectionError=function(e,t,r){return e.message.includes("getaddrinfo ENOTFOUND")||e.message.includes("connect ECONNREFUSED")?new Error(`Unavailable ${r} RPC url at ${t}`):e}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(0);n.__exportStar(r(6),t),n.__exportStar(r(8),t)},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(0);n.__exportStar(r(1),t),n.__exportStar(r(2),t),n.__exportStar(r(5),t),n.__exportStar(r(10),t),n.__exportStar(r(11),t),n.__exportStar(r(12),t),n.__exportStar(r(13),t),n.__exportStar(r(14),t)},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isNodeJs=void 0;const n=r(0),o=r(3);t.isNodeJs=o.isNode,n.__exportStar(r(3),t)},function(e,t,r){"use strict";(function(e){function r(){return(null==e?void 0:e.crypto)||(null==e?void 0:e.msCrypto)||{}}function n(){const e=r();return e.subtle||e.webkitSubtle}Object.defineProperty(t,"__esModule",{value:!0}),t.isBrowserCryptoAvailable=t.getSubtleCrypto=t.getBrowerCrypto=void 0,t.getBrowerCrypto=r,t.getSubtleCrypto=n,t.isBrowserCryptoAvailable=function(){return!!r()&&!!n()}}).call(this,r(7))},function(e,t){var r;r=function(){return this}();try{r=r||new Function("return this")()}catch(e){"object"==typeof window&&(r=window)}e.exports=r},function(e,t,r){"use strict";(function(e){function r(){return"undefined"==typeof document&&"undefined"!=typeof navigator&&"ReactNative"===navigator.product}function n(){return void 0!==e&&void 0!==e.versions&&void 0!==e.versions.node}Object.defineProperty(t,"__esModule",{value:!0}),t.isBrowser=t.isNode=t.isReactNative=void 0,t.isReactNative=r,t.isNode=n,t.isBrowser=function(){return!r()&&!n()}}).call(this,r(9))},function(e,t){var r,n,o=e.exports={};function i(){throw new Error("setTimeout has not been defined")}function u(){throw new Error("clearTimeout has not been defined")}function c(e){if(r===setTimeout)return setTimeout(e,0);if((r===i||!r)&&setTimeout)return r=setTimeout,setTimeout(e,0);try{return r(e,0)}catch(t){try{return r.call(null,e,0)}catch(t){return r.call(this,e,0)}}}!function(){try{r="function"==typeof setTimeout?setTimeout:i}catch(e){r=i}try{n="function"==typeof clearTimeout?clearTimeout:u}catch(e){n=u}}();var s,a=[],f=!1,l=-1;function d(){f&&s&&(f=!1,s.length?a=s.concat(a):l=-1,a.length&&R())}function R(){if(!f){var e=c(d);f=!0;for(var t=a.length;t;){for(s=a,a=[];++l<t;)s&&s[l].run();l=-1,t=a.length}s=null,f=!1,function(e){if(n===clearTimeout)return clearTimeout(e);if((n===u||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(e);try{n(e)}catch(t){try{return n.call(null,e)}catch(t){return n.call(this,e)}}}(e)}}function p(e,t){this.fun=e,this.array=t}function _(){}o.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];a.push(new p(e,t)),1!==a.length||f||c(R)},p.prototype.run=function(){this.fun.apply(null,this.array)},o.title="browser",o.browser=!0,o.env={},o.argv=[],o.version="",o.versions={},o.on=_,o.addListener=_,o.once=_,o.off=_,o.removeListener=_,o.removeAllListeners=_,o.emit=_,o.prependListener=_,o.prependOnceListener=_,o.listeners=function(e){return[]},o.binding=function(e){throw new Error("process.binding is not supported")},o.cwd=function(){return"/"},o.chdir=function(e){throw new Error("process.chdir is not supported")},o.umask=function(){return 0}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.formatErrorMessage=t.formatJsonRpcError=t.formatJsonRpcResult=t.formatJsonRpcRequest=t.getBigIntRpcId=t.payloadId=void 0;const n=r(2),o=r(1);function i(e=3){return Date.now()*Math.pow(10,e)+Math.floor(Math.random()*Math.pow(10,e))}function u(e,t){return void 0===e?(0,n.getError)(o.INTERNAL_ERROR):("string"==typeof e&&(e=Object.assign(Object.assign({},(0,n.getError)(o.SERVER_ERROR)),{message:e})),void 0!==t&&(e.data=t),(0,n.isReservedErrorCode)(e.code)&&(e=(0,n.getErrorByCode)(e.code)),e)}t.payloadId=i,t.getBigIntRpcId=function(e=6){return BigInt(i(e))},t.formatJsonRpcRequest=function(e,t,r){return{id:r||i(),jsonrpc:"2.0",method:e,params:t}},t.formatJsonRpcResult=function(e,t){return{id:e,jsonrpc:"2.0",result:t}},t.formatJsonRpcError=function(e,t,r){return{id:e,jsonrpc:"2.0",error:u(t,r)}},t.formatErrorMessage=u},function(e,t,r){"use strict";function n(e){return"*"===e}function o(e){return!!n(e)||!!e.includes("*")&&(2===e.split("*").length&&1===e.split("*").filter(e=>""===e.trim()).length)}Object.defineProperty(t,"__esModule",{value:!0}),t.isValidTrailingWildcardRoute=t.isValidLeadingWildcardRoute=t.isValidWildcardRoute=t.isValidDefaultRoute=t.isValidRoute=void 0,t.isValidRoute=function(e){return e.includes("*")?o(e):!/\W/g.test(e)},t.isValidDefaultRoute=n,t.isValidWildcardRoute=o,t.isValidLeadingWildcardRoute=function(e){return!n(e)&&o(e)&&!e.split("*")[0].trim()},t.isValidTrailingWildcardRoute=function(e){return!n(e)&&o(e)&&!e.split("*")[1].trim()}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});r(0).__exportStar(r(15),t)},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isLocalhostUrl=t.isWsUrl=t.isHttpUrl=void 0;function n(e,t){const r=function(e){const t=e.match(new RegExp(/^\w+:/,"gi"));if(t&&t.length)return t[0]}(e);return void 0!==r&&new RegExp(t).test(r)}t.isHttpUrl=function(e){return n(e,"^https?:")},t.isWsUrl=function(e){return n(e,"^wss?:")},t.isLocalhostUrl=function(e){return new RegExp("wss?://localhost(:d{2,5})?").test(e)}},function(e,t,r){"use strict";function n(e){return"object"==typeof e&&"id"in e&&"jsonrpc"in e&&"2.0"===e.jsonrpc}function o(e){return"result"in e}function i(e){return"error"in e}Object.defineProperty(t,"__esModule",{value:!0}),t.isJsonRpcValidationInvalid=t.isJsonRpcError=t.isJsonRpcResult=t.isJsonRpcResponse=t.isJsonRpcRequest=t.isJsonRpcPayload=void 0,t.isJsonRpcPayload=n,t.isJsonRpcRequest=function(e){return n(e)&&"method"in e},t.isJsonRpcResponse=function(e){return n(e)&&(o(e)||i(e))},t.isJsonRpcResult=o,t.isJsonRpcError=i,t.isJsonRpcValidationInvalid=function(e){return"error"in e&&!1===e.valid}},function(e,t,r){"use strict";r.r(t),r.d(t,"IEvents",(function(){return n})),r.d(t,"IJsonRpcConnection",(function(){return o})),r.d(t,"IBaseJsonRpcProvider",(function(){return i})),r.d(t,"IJsonRpcProvider",(function(){return u}));class n{}class o extends n{constructor(e){super()}}class i extends n{constructor(){super()}}class u extends i{constructor(e){super()}}}])}));