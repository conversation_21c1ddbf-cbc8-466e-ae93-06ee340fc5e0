{"version": 3, "file": "createFromBase64Bin.js", "sourceRoot": "", "sources": ["../src/createFromBase64Bin.ts"], "names": [], "mappings": ";;;AAAA,2CAAqC;AAE9B,MAAM,mBAAmB,GAAG,CAAC,QAAgB,oBAAQ,EAAE,MAAc,GAAG,EAAE,EAAE;IACjF,IAAI,KAAK,CAAC,MAAM,KAAK,EAAE;QAAE,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;IAC7E,IAAI,GAAG,GAAG,CAAC,CAAC;IACZ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE;QAAE,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IAChF,MAAM,KAAK,GAAa,EAAE,CAAC;IAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC;QAAE,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAChD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE;QAAE,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAEtE,MAAM,eAAe,GAAG,GAAG,CAAC,MAAM,KAAK,CAAC,CAAC;IACzC,MAAM,GAAG,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAEpD,OAAO,CAAC,IAAc,EAAE,MAAc,EAAE,MAAc,EAAc,EAAE;QACpE,IAAI,CAAC,MAAM;YAAE,OAAO,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;QACtC,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,IAAI,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;YACrB,OAAO,GAAG,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAC3B,MAAM,IAAI,OAAO,CAAC;QACpB,CAAC;aAAM,CAAC;YACN,MAAM,GAAG,GAAG,MAAM,GAAG,MAAM,CAAC;YAC5B,MAAM,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC;YACrB,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;gBAChC,OAAO,GAAG,CAAC,CAAC;gBACZ,IAAI,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,CAAC,KAAK,GAAG;oBAAE,OAAO,GAAG,CAAC,CAAC;YACjE,CAAC;QACH,CAAC;QACD,IAAI,MAAM,GAAG,CAAC,KAAK,CAAC;YAAE,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;QACtF,MAAM,OAAO,GAAG,MAAM,GAAG,MAAM,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpD,MAAM,YAAY,GAAG,CAAC,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC;QACjD,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,YAAY,CAAC,CAAC;QACzC,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,IAAI,CAAC,GAAG,MAAM,CAAC;QACf,OAAO,CAAC,GAAG,OAAO,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAC3B,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,MAAM,GAAG,IAAI,KAAK,EAAE,CAAC;YAC3B,MAAM,MAAM,GAAG,CAAC,IAAI,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC;YACpC,MAAM,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC;YACnC,MAAM,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC;YAC3B,MAAM,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;YAC9B,MAAM,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;YAC9B,MAAM,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;YAC9B,MAAM,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;YAC9B,IAAI,OAAO,GAAG,CAAC,IAAI,OAAO,GAAG,CAAC,IAAI,OAAO,GAAG,CAAC,IAAI,OAAO,GAAG,CAAC;gBAAE,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;YACpG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,IAAI,CAAC,CAAC,GAAG,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC;YACzC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,OAAO,IAAI,CAAC,CAAC,GAAG,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC;YAC7C,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,OAAO,IAAI,CAAC,CAAC,GAAG,OAAO,CAAC;YACtC,CAAC,IAAI,CAAC,CAAC;QACT,CAAC;QACD,IAAI,CAAC,OAAO;YAAE,OAAO,GAAG,CAAC;QACzB,IAAI,OAAO,KAAK,CAAC,EAAE,CAAC;YAClB,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YACrC,MAAM,MAAM,GAAG,IAAI,IAAI,CAAC,CAAC;YACzB,MAAM,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC;YAC3B,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;YAC1C,MAAM,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;YAC9B,MAAM,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;YAC9B,MAAM,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;YAC9B,IAAI,OAAO,GAAG,CAAC,IAAI,OAAO,GAAG,CAAC,IAAI,OAAO,GAAG,CAAC;gBAAE,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;YACrF,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,IAAI,CAAC,CAAC,GAAG,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC;YACzC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,OAAO,IAAI,CAAC,CAAC,GAAG,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC;YAC7C,OAAO,GAAG,CAAC;QACb,CAAC;QACD,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QACrC,MAAM,MAAM,GAAG,IAAI,IAAI,CAAC,CAAC;QACzB,MAAM,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC;QAC3B,MAAM,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;QAC9B,MAAM,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;QAC9B,IAAI,OAAO,GAAG,CAAC,IAAI,OAAO,GAAG,CAAC;YAAE,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;QACtE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,IAAI,CAAC,CAAC,GAAG,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC;QACzC,OAAO,GAAG,CAAC;IACb,CAAC,CAAC;AACJ,CAAC,CAAC;AAtEW,QAAA,mBAAmB,uBAsE9B"}