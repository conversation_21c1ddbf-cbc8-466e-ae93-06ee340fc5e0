{"name": "@web3modal/ui", "version": "2.4.3", "main": "dist/index.es.js", "type": "module", "types": "dist/_types/index.d.ts", "files": ["dist"], "scripts": {"build:clean": "rm -rf dist", "build:types": "tsc --emitDeclarationOnly", "build:source": "rollup --silent --config rollup.config.js", "build": "npm run build:clean; npm run build:types & npm run build:source", "dev": "rollup --config rollup.config.js --watch", "typecheck": "tsc --noEmit"}, "dependencies": {"@web3modal/core": "2.4.3", "lit": "2.7.5", "motion": "10.16.2", "qrcode": "1.5.3"}, "devDependencies": {"@types/qrcode": "1.5.0", "eslint-plugin-lit": "1.8.3", "eslint-plugin-wc": "1.5.0"}, "keywords": ["web3", "crypto", "ethereum", "web3modal", "walletconnect", "lit", "webcomponents"], "author": "WalletConnect <walletconnect.com>", "license": "Apache-2.0", "homepage": "https://github.com/web3modal/web3modal", "repository": {"type": "git", "url": "git+https://github.com/web3modal/web3modal.git"}, "bugs": {"url": "https://github.com/web3modal/web3modal/issues"}}