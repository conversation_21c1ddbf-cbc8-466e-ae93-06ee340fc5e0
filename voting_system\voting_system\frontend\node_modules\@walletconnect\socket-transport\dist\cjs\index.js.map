{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";;;AAOA,gDAM8B;AAE9B,gEAAuC;AAGvC,MAAM,EAAE,GAAG,OAAO,MAAM,CAAC,SAAS,KAAK,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAItF,MAAM,eAAe;IAanB,YAAoB,IAA6B;QAA7B,SAAI,GAAJ,IAAI,CAAyB;QANzC,WAAM,GAAqB,EAAE,CAAC;QAC9B,YAAO,GAAsB,EAAE,CAAC;QAChC,mBAAc,GAAa,EAAE,CAAC;QAKpC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC/B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;QACf,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa,IAAI,EAAE,CAAC;QAC/C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,IAAI,IAAI,iBAAc,EAAE,CAAC;QAE3D,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,OAAO,IAAI,CAAC,GAAG,KAAK,QAAQ,EAAE;YAC7C,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;SACrD;QAED,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC;QAErB,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;IAC5D,CAAC;IAED,IAAI,UAAU,CAAC,KAAK;IAEpB,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrD,CAAC;IAED,IAAI,UAAU,CAAC,KAAK;IAEpB,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,UAAU,KAAK,CAAC,CAAC;IAC/B,CAAC;IAED,IAAI,SAAS,CAAC,KAAK;IAEnB,CAAC;IAED,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,UAAU,KAAK,CAAC,CAAC;IAC/B,CAAC;IAED,IAAI,OAAO,CAAC,KAAK;IAEjB,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,UAAU,KAAK,CAAC,CAAC;IAC/B,CAAC;IAED,IAAI,MAAM,CAAC,KAAK;IAEhB,CAAC;IAED,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,UAAU,KAAK,CAAC,CAAC;IAC/B,CAAC;IAIM,IAAI;QACT,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC;IAEM,KAAK;QACV,IAAI,CAAC,YAAY,EAAE,CAAC;IACtB,CAAC;IAEM,IAAI,CAAC,OAAe,EAAE,KAAc,EAAE,MAAgB;QAC3D,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YACvC,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;SACnD;QAED,IAAI,CAAC,WAAW,CAAC;YACf,KAAK,EAAE,KAAK;YACZ,IAAI,EAAE,KAAK;YACX,OAAO,EAAE,OAAO;YAChB,MAAM,EAAE,CAAC,CAAC,MAAM;SACjB,CAAC,CAAC;IACL,CAAC;IAEM,SAAS,CAAC,KAAa;QAC5B,IAAI,CAAC,WAAW,CAAC;YACf,KAAK,EAAE,KAAK;YACZ,IAAI,EAAE,KAAK;YACX,OAAO,EAAE,EAAE;YACX,MAAM,EAAE,IAAI;SACb,CAAC,CAAC;IACL,CAAC;IAEM,EAAE,CAAC,KAAa,EAAE,QAAgC;QACvD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;IACzC,CAAC;IAIO,aAAa;QACnB,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,OAAO;SACR;QAED,MAAM,GAAG,GAAG,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEtE,IAAI,CAAC,WAAW,GAAG,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC;QAE/B,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;SAC5C;QAED,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,CAAC,KAAmB,EAAE,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;QAEjF,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;QAEnD,IAAI,CAAC,WAAW,CAAC,OAAO,GAAG,CAAC,KAAY,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAEtE,IAAI,CAAC,WAAW,CAAC,OAAO,GAAG,GAAG,EAAE;YAC9B,UAAU,CAAC,GAAG,EAAE;gBACd,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;gBACxB,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,CAAC,EAAE,IAAI,CAAC,CAAC;QACX,CAAC,CAAC;IACJ,CAAC;IAEO,WAAW;QACjB,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC;QAChC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAEO,YAAY;QAClB,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,GAAG,EAAE;YAE5B,CAAC,CAAC;YACF,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;SACtB;IACH,CAAC;IAEO,WAAW,CAAC,aAA6B;QAC/C,MAAM,OAAO,GAAW,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QAEtD,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,KAAK,CAAC,EAAE;YACjD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAC5B;aAAM;YACL,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;YAChC,IAAI,CAAC,aAAa,EAAE,CAAC;SACtB;IACH,CAAC;IAEa,cAAc,CAAC,KAAmB;;YAC9C,IAAI,aAA6B,CAAC;YAElC,IAAI;gBACF,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;aACxC;YAAC,OAAO,KAAK,EAAE;gBACd,OAAO;aACR;YAED,IAAI,CAAC,WAAW,CAAC;gBACf,KAAK,EAAE,aAAa,CAAC,KAAK;gBAC1B,IAAI,EAAE,KAAK;gBACX,OAAO,EAAE,EAAE;gBACX,MAAM,EAAE,IAAI;aACb,CAAC,CAAC;YAEH,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,KAAK,CAAC,EAAE;gBACjD,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,KAAK,SAAS,CAAC,CAAC;gBACvE,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE;oBAC3B,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC;iBACxD;aACF;QACH,CAAC;KAAA;IAEO,YAAY,CAAC,CAAQ;QAC3B,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,KAAK,OAAO,CAAC,CAAC;QACrE,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE;YAC3B,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;SAC5C;IACH,CAAC;IAEO,mBAAmB;QACzB,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;QAE1C,aAAa,CAAC,OAAO,CAAC,CAAC,KAAa,EAAE,EAAE,CACtC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACf,KAAK,EAAE,KAAK;YACZ,IAAI,EAAE,KAAK;YACX,OAAO,EAAE,EAAE;YACX,MAAM,EAAE,IAAI;SACb,CAAC,CACH,CAAC;QAEF,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,EAAE,CAAC;IACtD,CAAC;IAEO,WAAW,CAAC,aAA6B;QAC/C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAClC,CAAC;IAEO,UAAU;QAChB,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAE1B,KAAK,CAAC,OAAO,CAAC,CAAC,aAA6B,EAAE,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC;QAElF,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;IACnB,CAAC;CACF;AAED,SAAS,eAAe,CAAC,IAAY,EAAE,QAAgB,EAAE,OAAe;;IACtE,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;QAClC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC;QAC9B,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;YACzB,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC;YAC5B,CAAC,CAAC,IAAI,CAAC;IACT,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAChC,MAAM,MAAM,GAAG,IAAA,iBAAS,GAAE;QACxB,CAAC,CAAC;YACE,QAAQ;YACR,OAAO;YACP,GAAG,EAAE,SAAS;YACd,IAAI,EAAE,CAAA,MAAA,IAAA,mBAAW,GAAE,0CAAE,IAAI,KAAI,EAAE;SAChC;QACH,CAAC,CAAC;YACE,QAAQ;YACR,OAAO;YACP,GAAG,EAAE,CAAA,MAAA,IAAA,iBAAS,GAAE,0CAAE,IAAI,KAAI,EAAE;SAC7B,CAAC;IACN,MAAM,WAAW,GAAG,IAAA,2BAAmB,EAAC,IAAA,sBAAc,EAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;IACnF,OAAO,QAAQ,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,WAAW,CAAC;AACzC,CAAC;AAED,kBAAe,eAAe,CAAC"}