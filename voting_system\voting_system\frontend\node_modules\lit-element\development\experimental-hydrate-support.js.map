{"version": 3, "file": "experimental-hydrate-support.js", "sourceRoot": "", "sources": ["../src/experimental-hydrate-support.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAWH,OAAO,EAAC,MAAM,EAAgB,MAAM,UAAU,CAAC;AAC/C,OAAO,EAAC,OAAO,EAAC,MAAM,kCAAkC,CAAC;AACzD,OAAO,EAAC,6BAA6B,EAAC,MAAM,wBAAwB,CAAC;AAarE,8DAA8D;AAC9D,UAAU,CAAC,wBAAwB,GAAG,CAAC,EACrC,UAAU,GAGX,EAAE,EAAE;IACH,MAAM,kBAAkB,GAAG,MAAM,CAAC,wBAAwB,CACxD,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,EACjC,oBAAoB,CACpB,CAAC,GAAI,CAAC;IAER,8CAA8C;IAC9C,MAAM,CAAC,cAAc,CAAC,UAAU,EAAE,oBAAoB,EAAE;QACtD,GAAG;YACD,OAAO,CAAC,GAAG,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,iBAAiB,CAAC,CAAC;QAC/D,CAAC;KACF,CAAC,CAAC;IAEH,4EAA4E;IAC5E,4BAA4B;IAC5B,MAAM,wBAAwB,GAC5B,UAAU,CAAC,SAAS,CAAC,wBAAwB,CAAC;IAChD,UAAU,CAAC,SAAS,CAAC,wBAAwB,GAAG,UAC9C,IAAY,EACZ,GAAkB,EAClB,KAAoB;QAEpB,IAAI,IAAI,KAAK,iBAAiB,IAAI,KAAK,KAAK,IAAI,EAAE;YAChD,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC9B;QACD,wBAAwB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;IACxD,CAAC,CAAC;IAEF,yEAAyE;IACzE,8EAA8E;IAC9E,MAAM,iBAAiB,GAAG,UAAU,CAAC,SAAS,CAAC,iBAAiB,CAAC;IACjE,UAAU,CAAC,SAAS,CAAC,iBAAiB,GAAG;QAGvC,2EAA2E;QAC3E,yDAAyD;QACzD,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,EAAE;YACzC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC9B;IACH,CAAC,CAAC;IAEF,2EAA2E;IAC3E,wEAAwE;IACxE,MAAM,gBAAgB,GAAG,UAAU,CAAC,SAAS,CAAC,gBAAgB,CAAC;IAC/D,UAAU,CAAC,SAAS,CAAC,gBAAgB,GAAG;QACtC,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;YAC7B,OAAO,IAAI,CAAC,UAAU,CAAC;SACxB;aAAM;YACL,OAAO,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACpC;IACH,CAAC,CAAC;IAEF,sCAAsC;IACtC,MAAM,MAAM,GAAG,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC;IAClE,UAAU,CAAC,SAAS,CAAC,MAAM,GAAG,UAE5B,iBAAiC;QAEjC,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QAC5B,qEAAqE;QACrE,6CAA6C;QAC7C,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAC;QACrC,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;YAC9B,4DAA4D;YAC5D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC/C,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gBAChC,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,6BAA6B,CAAC,EAAE;oBACvD,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAC9B,6BAA6B,CAAC,MAAM,CACrC,CAAC;oBACF,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;oBAC/B,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBACjC;aACF;YACD,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;SACrD;aAAM;YACL,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;SACpD;IACH,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,OAAO,CAAC,IAAI,CACV,0EAA0E;IACxE,uEAAuE,CAC1E,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/**\n * LitElement support for hydration of content rendered using lit-ssr.\n *\n * @packageDocumentation\n *\n * @deprecated Moved to `@lit-labs/ssr-client/lit-element-hydrate-support.js`.\n */\n\nimport type {PropertyValues} from '@lit/reactive-element';\nimport {render, RenderOptions} from 'lit-html';\nimport {hydrate} from 'lit-html/experimental-hydrate.js';\nimport {HYDRATE_INTERNALS_ATTR_PREFIX} from '@lit-labs/ssr-dom-shim';\n\ninterface PatchableLitElement extends HTMLElement {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-misused-new\n  new (...args: any[]): PatchableLitElement;\n  enableUpdating(requestedUpdate?: boolean): void;\n  createRenderRoot(): Element | ShadowRoot;\n  renderRoot: HTMLElement | DocumentFragment;\n  render(): unknown;\n  renderOptions: RenderOptions;\n  _$needsHydration: boolean;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nglobalThis.litElementHydrateSupport = ({\n  LitElement,\n}: {\n  LitElement: PatchableLitElement;\n}) => {\n  const observedAttributes = Object.getOwnPropertyDescriptor(\n    Object.getPrototypeOf(LitElement),\n    'observedAttributes'\n  )!.get!;\n\n  // Add `defer-hydration` to observedAttributes\n  Object.defineProperty(LitElement, 'observedAttributes', {\n    get() {\n      return [...observedAttributes.call(this), 'defer-hydration'];\n    },\n  });\n\n  // Enable element when 'defer-hydration' attribute is removed by calling the\n  // super.connectedCallback()\n  const attributeChangedCallback =\n    LitElement.prototype.attributeChangedCallback;\n  LitElement.prototype.attributeChangedCallback = function (\n    name: string,\n    old: string | null,\n    value: string | null\n  ) {\n    if (name === 'defer-hydration' && value === null) {\n      connectedCallback.call(this);\n    }\n    attributeChangedCallback.call(this, name, old, value);\n  };\n\n  // Override `connectedCallback` to capture whether we need hydration, and\n  // defer `super.connectedCallback()` if the 'defer-hydration' attribute is set\n  const connectedCallback = LitElement.prototype.connectedCallback;\n  LitElement.prototype.connectedCallback = function (\n    this: PatchableLitElement\n  ) {\n    // If the outer scope of this element has not yet been hydrated, wait until\n    // 'defer-hydration' attribute has been removed to enable\n    if (!this.hasAttribute('defer-hydration')) {\n      connectedCallback.call(this);\n    }\n  };\n\n  // If we've been server-side rendered, just return `this.shadowRoot`, don't\n  // call the base implementation, which would also adopt styles (for now)\n  const createRenderRoot = LitElement.prototype.createRenderRoot;\n  LitElement.prototype.createRenderRoot = function (this: PatchableLitElement) {\n    if (this.shadowRoot) {\n      this._$needsHydration = true;\n      return this.shadowRoot;\n    } else {\n      return createRenderRoot.call(this);\n    }\n  };\n\n  // Hydrate on first update when needed\n  const update = Object.getPrototypeOf(LitElement.prototype).update;\n  LitElement.prototype.update = function (\n    this: PatchableLitElement,\n    changedProperties: PropertyValues\n  ) {\n    const value = this.render();\n    // Since this is a patch, we can't call super.update(), so we capture\n    // it off the proto chain and call it instead\n    update.call(this, changedProperties);\n    if (this._$needsHydration) {\n      this._$needsHydration = false;\n      // Remove aria attributes added by internals shim during SSR\n      for (let i = 0; i < this.attributes.length; i++) {\n        const attr = this.attributes[i];\n        if (attr.name.startsWith(HYDRATE_INTERNALS_ATTR_PREFIX)) {\n          const ariaAttr = attr.name.slice(\n            HYDRATE_INTERNALS_ATTR_PREFIX.length\n          );\n          this.removeAttribute(ariaAttr);\n          this.removeAttribute(attr.name);\n        }\n      }\n      hydrate(value, this.renderRoot, this.renderOptions);\n    } else {\n      render(value, this.renderRoot, this.renderOptions);\n    }\n  };\n};\n\nconsole.warn(\n  'Import from `lit-element/experimental-hydrate-support.js` is deprecated.' +\n    'Import `@lit-labs/ssr-client/lit-element-hydrate-support.js` instead.'\n);\n"]}