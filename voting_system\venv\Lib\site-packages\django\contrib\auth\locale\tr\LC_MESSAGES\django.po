# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> Emre Aladağ <<EMAIL>>, 2013
# BouRock, 2015-2017,2019-2021,2023-2025
# BouRock, 2014-2015
# <PERSON><PERSON> <<EMAIL>>, 2013
# C<PERSON><PERSON> GÜNDOĞDU <<EMAIL>>, 2014
# <AUTHOR> <EMAIL>, 2013
# <PERSON><PERSON> <<EMAIL>>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2012
# <PERSON><PERSON> <<EMAIL>>, 2012
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2011
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 11:30-0500\n"
"PO-Revision-Date: 2025-04-01 15:04-0500\n"
"Last-Translator: BouRock, 2015-2017,2019-2021,2023-2025\n"
"Language-Team: Turkish (http://app.transifex.com/django/django/language/"
"tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

msgid "Personal info"
msgstr "Kişisel bilgiler"

msgid "Permissions"
msgstr "İzinler"

msgid "Important dates"
msgstr "Önemli tarihler"

#, python-format
msgid "%(name)s object with primary key %(key)r does not exist."
msgstr "%(key)r birincil anahtarı olan %(name)s nesnesi mevcut değil."

msgid "Conflicting form data submitted. Please try again."
msgstr "Çakışan form verileri gönderildi. Lütfen tekrar deneyin."

msgid "Password changed successfully."
msgstr "Parola başarılı olarak değiştirildi."

msgid "Password-based authentication was disabled."
msgstr "Parola tabanlı kimlik doğrulaması etkisizleştirildi."

#, python-format
msgid "Change password: %s"
msgstr "Parolayı değiştir: %s"

#, python-format
msgid "Set password: %s"
msgstr "Parola ayarla: %s"

msgid "Authentication and Authorization"
msgstr "Kimlik Doğrulama ve Yetkilendirme"

msgid "password"
msgstr "parola"

msgid "last login"
msgstr "son oturum açma"

msgid "Invalid password format or unknown hashing algorithm."
msgstr "Geçersiz parola biçimi veya bilinmeyen adresleme algoritması."

msgid "No password set."
msgstr "Ayarlı parola yok."

msgid "Reset password"
msgstr "Parolayı sıfırla"

msgid "Set password"
msgstr "Parola ayarla"

msgid "The two password fields didn’t match."
msgstr "İki parola alanı eşleşmedi."

msgid "Password"
msgstr "Parola"

msgid "Password confirmation"
msgstr "Parola onayı"

msgid "Enter the same password as before, for verification."
msgstr "Doğrulama için önceki gibi aynı parolayı girin."

msgid ""
"Whether the user will be able to authenticate using a password or not. If "
"disabled, they may still be able to authenticate using other backends, such "
"as Single Sign-On or LDAP."
msgstr ""
"Kullanıcının parola kullanarak ya da kullanmadan kimlik doğrulaması yapıp "
"yapamayacağı. Eğer etkisizleştirildiyse, Tek Oturum Açma veya LDAP gibi "
"diğer arka uçları kullanarak kimlik doğrulaması yapmaya devam edebilirler."

msgid "Password-based authentication"
msgstr "Parola tabanlı kimlik doğrulaması"

msgid "Enabled"
msgstr "Etkinleştirildi"

msgid "Disabled"
msgstr "Etkisizleştirildi"

msgid ""
"Raw passwords are not stored, so there is no way to see the user’s password."
msgstr ""
"Ham parolalar saklanmaz, dolayısıyla kullanıcının parolasını görmenin bir "
"yolu yoktur."

msgid ""
"Enable password-based authentication for this user by setting a password."
msgstr ""
"Bir parola ayarlayarak bu kullanıcı için parola tabanlı kimlik doğrulamasını "
"etkinleştirin."

#, python-format
msgid ""
"Please enter a correct %(username)s and password. Note that both fields may "
"be case-sensitive."
msgstr ""
"Lütfen doğru %(username)s ve parola girin. Her iki alanın da büyük/küçük "
"harfe duyarlı olabileceğini unutmayın."

msgid "This account is inactive."
msgstr "Bu hesap devre dışı."

msgid "Email"
msgstr "E-posta"

msgid "New password"
msgstr "Yeni parola"

msgid "New password confirmation"
msgstr "Yeni parola onayı"

msgid "Your old password was entered incorrectly. Please enter it again."
msgstr "Eski parolanız yanlış girildi. Lütfen tekrar girin."

msgid "Old password"
msgstr "Eski parola"

msgid "algorithm"
msgstr "algoritma"

msgid "iterations"
msgstr "yinelemeler"

msgid "salt"
msgstr "tuz"

msgid "hash"
msgstr "adresleme"

msgid "variety"
msgstr "çeşitlilik"

msgid "version"
msgstr "sürüm"

msgid "memory cost"
msgstr "bellek maliyeti"

msgid "time cost"
msgstr "zaman maliyeti"

msgid "parallelism"
msgstr "paralellik"

msgid "work factor"
msgstr "iş faktörü"

msgid "checksum"
msgstr "sağlama"

msgid "block size"
msgstr "blok boyutu"

msgid "name"
msgstr "adı"

msgid "content type"
msgstr "içerik türü"

msgid "codename"
msgstr "kod adı"

msgid "permission"
msgstr "izin"

msgid "permissions"
msgstr "izinler"

msgid "group"
msgstr "grup"

msgid "groups"
msgstr "gruplar"

msgid "superuser status"
msgstr "süper kullanıcı durumu"

msgid ""
"Designates that this user has all permissions without explicitly assigning "
"them."
msgstr ""
"Bu kullanıcıya ayrı ayrı izin atamadan tüm izinlerin verilip verilmeyeceğini "
"belirler."

msgid ""
"The groups this user belongs to. A user will get all permissions granted to "
"each of their groups."
msgstr ""
"Bu kullanıcının ait olduğu gruplar. Bir kullanıcı kendi gruplarının her "
"birine verilmiş olan tüm izinleri alacak."

msgid "user permissions"
msgstr "kullanıcı izinleri"

msgid "Specific permissions for this user."
msgstr "Bu kullanıcı için belirli izinler."

msgid "username"
msgstr "kullanıcı adı"

msgid "Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only."
msgstr ""
"Zorunlu. 150 karakter ya da daha az olmalı. Sadece harfler, rakamlar ve @/./"
"+/-/_ karakterleri kullanılabilir."

msgid "A user with that username already exists."
msgstr "Bu kullanıcı adında bir kullanıcı zaten mevcut."

msgid "first name"
msgstr "adı"

msgid "last name"
msgstr "soyadı"

msgid "email address"
msgstr "e-posta adresi"

msgid "staff status"
msgstr "görev durumu"

msgid "Designates whether the user can log into this admin site."
msgstr "Kullanıcının bu yönetici sitesine oturum açıp açamayacağını belirler."

msgid "active"
msgstr "etkin"

msgid ""
"Designates whether this user should be treated as active. Unselect this "
"instead of deleting accounts."
msgstr ""
"Bu kullanıcının etkin olarak işlem görüp görmediğini belirler. Hesapları "
"silmek yerine bunun işaretini kaldırın."

msgid "date joined"
msgstr "katılma tarihi"

msgid "user"
msgstr "kullanıcı"

msgid "users"
msgstr "kullanıcılar"

#, python-format
msgid "This password is too short. It must contain at least %d character."
msgid_plural ""
"This password is too short. It must contain at least %d characters."
msgstr[0] "Bu parola çok kısa. En az %d karakter içermek zorunda."
msgstr[1] "Bu parola çok kısa. En az %d karakter içermek zorunda."

#, python-format
msgid "Your password must contain at least %(min_length)d character."
msgid_plural "Your password must contain at least %(min_length)d characters."
msgstr[0] "Parolanız en az %(min_length)d karakter içermek zorunda."
msgstr[1] "Parolanız en az %(min_length)d karakter içermek zorundadır."

#, python-format
msgid "The password is too similar to the %(verbose_name)s."
msgstr "Parolanız %(verbose_name)s ile çok benzerdir."

msgid "Your password can’t be too similar to your other personal information."
msgstr "Parolanız diğer kişisel bilgileriniz ile çok benzer olamaz."

msgid "This password is too common."
msgstr "Bu parola çok geneldir."

msgid "Your password can’t be a commonly used password."
msgstr "Parolanız yaygın olarak kullanılan bir parola olamaz."

msgid "This password is entirely numeric."
msgstr "Bu parola tamamıyla sayısaldır."

msgid "Your password can’t be entirely numeric."
msgstr "Parolanız tamamıyla sayısal olamaz."

#, python-format
msgid "Password reset on %(site_name)s"
msgstr "%(site_name)s sitesinde parola sıfırlama"

msgid ""
"Enter a valid username. This value may contain only unaccented lowercase a-z "
"and uppercase A-Z letters, numbers, and @/./+/-/_ characters."
msgstr ""
"Geçerli bir kullanıcı adı girin. Bu değer sadece aksansız küçük a-z ve büyük "
"A-Z harfleri, sayıları, ve @/./+/-/_ karakterlerini içerebilir."

msgid ""
"Enter a valid username. This value may contain only letters, numbers, and "
"@/./+/-/_ characters."
msgstr ""
"Geçerli bir kullanıcı adı girin. Bu değer sadece harfleri, sayıları, ve @/./"
"+/-/_ karakterlerini içerebilir."

msgid "Logged out"
msgstr "Oturum kapatıldı"

msgid "Password reset"
msgstr "Parolayı sıfırla"

msgid "Password reset sent"
msgstr "Parola sıfırlama gönderildi"

msgid "Enter new password"
msgstr "Yeni parolayı girin"

msgid "Password reset unsuccessful"
msgstr "Parola sıfırlama başarısız"

msgid "Password reset complete"
msgstr "Parola sıfırlama tamamlandı"

msgid "Password change"
msgstr "Parola değiştirme"

msgid "Password change successful"
msgstr "Parola değiştirme başarılı"
