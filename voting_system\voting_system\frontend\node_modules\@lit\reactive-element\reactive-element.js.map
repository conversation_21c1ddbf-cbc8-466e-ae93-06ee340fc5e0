{"version": 3, "file": "reactive-element.js", "sources": ["src/reactive-element.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/**\n * Use this module if you want to create your own base class extending\n * {@link ReactiveElement}.\n * @packageDocumentation\n */\n\nimport {\n  getCompatibleStyle,\n  adoptStyles,\n  CSSResultGroup,\n  CSSResultOrNative,\n} from './css-tag.js';\nimport type {\n  ReactiveController,\n  ReactiveControllerHost,\n} from './reactive-controller.js';\n\n// In the Node build, this import will be injected by Rollup:\n// import {HTMLElement, customElements} from '@lit-labs/ssr-dom-shim';\n\nexport * from './css-tag.js';\nexport type {\n  ReactiveController,\n  ReactiveControllerHost,\n} from './reactive-controller.js';\n\nconst NODE_MODE = false;\nconst global = NODE_MODE ? globalThis : window;\n\nif (NODE_MODE) {\n  global.customElements ??= customElements;\n}\n\nconst DEV_MODE = true;\n\nlet requestUpdateThenable: (name: string) => {\n  then: (\n    onfulfilled?: (value: boolean) => void,\n    _onrejected?: () => void\n  ) => void;\n};\n\nlet issueWarning: (code: string, warning: string) => void;\n\nconst trustedTypes = (global as unknown as {trustedTypes?: {emptyScript: ''}})\n  .trustedTypes;\n\n// Temporary workaround for https://crbug.com/993268\n// Currently, any attribute starting with \"on\" is considered to be a\n// TrustedScript source. Such boolean attributes must be set to the equivalent\n// trusted emptyScript value.\nconst emptyStringForBooleanAttribute = trustedTypes\n  ? (trustedTypes.emptyScript as unknown as '')\n  : '';\n\nconst polyfillSupport = DEV_MODE\n  ? global.reactiveElementPolyfillSupportDevMode\n  : global.reactiveElementPolyfillSupport;\n\nif (DEV_MODE) {\n  // Ensure warnings are issued only 1x, even if multiple versions of Lit\n  // are loaded.\n  const issuedWarnings: Set<string | undefined> = (global.litIssuedWarnings ??=\n    new Set());\n\n  // Issue a warning, if we haven't already.\n  issueWarning = (code: string, warning: string) => {\n    warning += ` See https://lit.dev/msg/${code} for more information.`;\n    if (!issuedWarnings.has(warning)) {\n      console.warn(warning);\n      issuedWarnings.add(warning);\n    }\n  };\n\n  issueWarning(\n    'dev-mode',\n    `Lit is in dev mode. Not recommended for production!`\n  );\n\n  // Issue polyfill support warning.\n  if (global.ShadyDOM?.inUse && polyfillSupport === undefined) {\n    issueWarning(\n      'polyfill-support-missing',\n      `Shadow DOM is being polyfilled via \\`ShadyDOM\\` but ` +\n        `the \\`polyfill-support\\` module has not been loaded.`\n    );\n  }\n\n  requestUpdateThenable = (name) => ({\n    then: (\n      onfulfilled?: (value: boolean) => void,\n      _onrejected?: () => void\n    ) => {\n      issueWarning(\n        'request-update-promise',\n        `The \\`requestUpdate\\` method should no longer return a Promise but ` +\n          `does so on \\`${name}\\`. Use \\`updateComplete\\` instead.`\n      );\n      if (onfulfilled !== undefined) {\n        onfulfilled(false);\n      }\n    },\n  });\n}\n\n/**\n * Contains types that are part of the unstable debug API.\n *\n * Everything in this API is not stable and may change or be removed in the future,\n * even on patch releases.\n */\n// eslint-disable-next-line @typescript-eslint/no-namespace\nexport namespace ReactiveUnstable {\n  /**\n   * When Lit is running in dev mode and `window.emitLitDebugLogEvents` is true,\n   * we will emit 'lit-debug' events to window, with live details about the update and render\n   * lifecycle. These can be useful for writing debug tooling and visualizations.\n   *\n   * Please be aware that running with window.emitLitDebugLogEvents has performance overhead,\n   * making certain operations that are normally very cheap (like a no-op render) much slower,\n   * because we must copy data and dispatch events.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-namespace\n  export namespace DebugLog {\n    export type Entry = Update;\n    export interface Update {\n      kind: 'update';\n    }\n  }\n}\n\ninterface DebugLoggingWindow {\n  // Even in dev mode, we generally don't want to emit these events, as that's\n  // another level of cost, so only emit them when DEV_MODE is true _and_ when\n  // window.emitLitDebugEvents is true.\n  emitLitDebugLogEvents?: boolean;\n}\n\n/**\n * Useful for visualizing and logging insights into what the Lit template system is doing.\n *\n * Compiled out of prod mode builds.\n */\nconst debugLogEvent = DEV_MODE\n  ? (event: ReactiveUnstable.DebugLog.Entry) => {\n      const shouldEmit = (global as unknown as DebugLoggingWindow)\n        .emitLitDebugLogEvents;\n      if (!shouldEmit) {\n        return;\n      }\n      global.dispatchEvent(\n        new CustomEvent<ReactiveUnstable.DebugLog.Entry>('lit-debug', {\n          detail: event,\n        })\n      );\n    }\n  : undefined;\n\n/*\n * When using Closure Compiler, JSCompiler_renameProperty(property, object) is\n * replaced at compile time by the munged name for object[property]. We cannot\n * alias this function, so we have to use a small shim that has the same\n * behavior when not compiling.\n */\n/*@__INLINE__*/\nconst JSCompiler_renameProperty = <P extends PropertyKey>(\n  prop: P,\n  _obj: unknown\n): P => prop;\n\n/**\n * Converts property values to and from attribute values.\n */\nexport interface ComplexAttributeConverter<Type = unknown, TypeHint = unknown> {\n  /**\n   * Called to convert an attribute value to a property\n   * value.\n   */\n  fromAttribute?(value: string | null, type?: TypeHint): Type;\n\n  /**\n   * Called to convert a property value to an attribute\n   * value.\n   *\n   * It returns unknown instead of string, to be compatible with\n   * https://github.com/WICG/trusted-types (and similar efforts).\n   */\n  toAttribute?(value: Type, type?: TypeHint): unknown;\n}\n\ntype AttributeConverter<Type = unknown, TypeHint = unknown> =\n  | ComplexAttributeConverter<Type>\n  | ((value: string | null, type?: TypeHint) => Type);\n\n/**\n * Defines options for a property accessor.\n */\nexport interface PropertyDeclaration<Type = unknown, TypeHint = unknown> {\n  /**\n   * When set to `true`, indicates the property is internal private state. The\n   * property should not be set by users. When using TypeScript, this property\n   * should be marked as `private` or `protected`, and it is also a common\n   * practice to use a leading `_` in the name. The property is not added to\n   * `observedAttributes`.\n   */\n  readonly state?: boolean;\n\n  /**\n   * Indicates how and whether the property becomes an observed attribute.\n   * If the value is `false`, the property is not added to `observedAttributes`.\n   * If true or absent, the lowercased property name is observed (e.g. `fooBar`\n   * becomes `foobar`). If a string, the string value is observed (e.g\n   * `attribute: 'foo-bar'`).\n   */\n  readonly attribute?: boolean | string;\n\n  /**\n   * Indicates the type of the property. This is used only as a hint for the\n   * `converter` to determine how to convert the attribute\n   * to/from a property.\n   */\n  readonly type?: TypeHint;\n\n  /**\n   * Indicates how to convert the attribute to/from a property. If this value\n   * is a function, it is used to convert the attribute value a the property\n   * value. If it's an object, it can have keys for `fromAttribute` and\n   * `toAttribute`. If no `toAttribute` function is provided and\n   * `reflect` is set to `true`, the property value is set directly to the\n   * attribute. A default `converter` is used if none is provided; it supports\n   * `Boolean`, `String`, `Number`, `Object`, and `Array`. Note,\n   * when a property changes and the converter is used to update the attribute,\n   * the property is never updated again as a result of the attribute changing,\n   * and vice versa.\n   */\n  readonly converter?: AttributeConverter<Type, TypeHint>;\n\n  /**\n   * Indicates if the property should reflect to an attribute.\n   * If `true`, when the property is set, the attribute is set using the\n   * attribute name determined according to the rules for the `attribute`\n   * property option and the value of the property converted using the rules\n   * from the `converter` property option.\n   */\n  readonly reflect?: boolean;\n\n  /**\n   * A function that indicates if a property should be considered changed when\n   * it is set. The function should take the `newValue` and `oldValue` and\n   * return `true` if an update should be requested.\n   */\n  hasChanged?(value: Type, oldValue: Type): boolean;\n\n  /**\n   * Indicates whether an accessor will be created for this property. By\n   * default, an accessor will be generated for this property that requests an\n   * update when set. If this flag is `true`, no accessor will be created, and\n   * it will be the user's responsibility to call\n   * `this.requestUpdate(propertyName, oldValue)` to request an update when\n   * the property changes.\n   */\n  readonly noAccessor?: boolean;\n}\n\n/**\n * Map of properties to PropertyDeclaration options. For each property an\n * accessor is made, and the property is processed according to the\n * PropertyDeclaration options.\n */\nexport interface PropertyDeclarations {\n  readonly [key: string]: PropertyDeclaration;\n}\n\ntype PropertyDeclarationMap = Map<PropertyKey, PropertyDeclaration>;\n\ntype AttributeMap = Map<string, PropertyKey>;\n\n/**\n * A Map of property keys to values.\n *\n * Takes an optional type parameter T, which when specified as a non-any,\n * non-unknown type, will make the Map more strongly-typed, associating the map\n * keys with their corresponding value type on T.\n *\n * Use `PropertyValues<this>` when overriding ReactiveElement.update() and\n * other lifecycle methods in order to get stronger type-checking on keys\n * and values.\n */\n// This type is conditional so that if the parameter T is not specified, or\n// is `any`, the type will include `Map<PropertyKey, unknown>`. Since T is not\n// given in the uses of PropertyValues in this file, all uses here fallback to\n// meaning `Map<PropertyKey, unknown>`, but if a developer uses\n// `PropertyValues<this>` (or any other value for T) they will get a\n// strongly-typed Map type.\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport type PropertyValues<T = any> = T extends object\n  ? PropertyValueMap<T>\n  : Map<PropertyKey, unknown>;\n\n/**\n * Do not use, instead prefer {@linkcode PropertyValues}.\n */\n// This type must be exported such that JavaScript generated by the Google\n// Closure Compiler can import a type reference.\nexport interface PropertyValueMap<T> extends Map<PropertyKey, unknown> {\n  get<K extends keyof T>(k: K): T[K];\n  set<K extends keyof T>(key: K, value: T[K]): this;\n  has<K extends keyof T>(k: K): boolean;\n  delete<K extends keyof T>(k: K): boolean;\n}\n\nexport const defaultConverter: ComplexAttributeConverter = {\n  toAttribute(value: unknown, type?: unknown): unknown {\n    switch (type) {\n      case Boolean:\n        value = value ? emptyStringForBooleanAttribute : null;\n        break;\n      case Object:\n      case Array:\n        // if the value is `null` or `undefined` pass this through\n        // to allow removing/no change behavior.\n        value = value == null ? value : JSON.stringify(value);\n        break;\n    }\n    return value;\n  },\n\n  fromAttribute(value: string | null, type?: unknown) {\n    let fromValue: unknown = value;\n    switch (type) {\n      case Boolean:\n        fromValue = value !== null;\n        break;\n      case Number:\n        fromValue = value === null ? null : Number(value);\n        break;\n      case Object:\n      case Array:\n        // Do *not* generate exception when invalid JSON is set as elements\n        // don't normally complain on being mis-configured.\n        // TODO(sorvell): Do generate exception in *dev mode*.\n        try {\n          // Assert to adhere to Bazel's \"must type assert JSON parse\" rule.\n          fromValue = JSON.parse(value!) as unknown;\n        } catch (e) {\n          fromValue = null;\n        }\n        break;\n    }\n    return fromValue;\n  },\n};\n\nexport interface HasChanged {\n  (value: unknown, old: unknown): boolean;\n}\n\n/**\n * Change function that returns true if `value` is different from `oldValue`.\n * This method is used as the default for a property's `hasChanged` function.\n */\nexport const notEqual: HasChanged = (value: unknown, old: unknown): boolean => {\n  // This ensures (old==NaN, value==NaN) always returns false\n  return old !== value && (old === old || value === value);\n};\n\nconst defaultPropertyDeclaration: PropertyDeclaration = {\n  attribute: true,\n  type: String,\n  converter: defaultConverter,\n  reflect: false,\n  hasChanged: notEqual,\n};\n\n/**\n * The Closure JS Compiler doesn't currently have good support for static\n * property semantics where \"this\" is dynamic (e.g.\n * https://github.com/google/closure-compiler/issues/3177 and others) so we use\n * this hack to bypass any rewriting by the compiler.\n */\nconst finalized = 'finalized';\n\n/**\n * A string representing one of the supported dev mode warning categories.\n */\nexport type WarningKind = 'change-in-update' | 'migration';\n\nexport type Initializer = (element: ReactiveElement) => void;\n\n/**\n * Base element class which manages element properties and attributes. When\n * properties change, the `update` method is asynchronously called. This method\n * should be supplied by subclassers to render updates as desired.\n * @noInheritDoc\n */\nexport abstract class ReactiveElement\n  // In the Node build, this `extends` clause will be substituted with\n  // `(globalThis.HTMLElement ?? HTMLElement)`.\n  //\n  // This way, we will first prefer any global `HTMLElement` polyfill that the\n  // user has assigned, and then fall back to the `HTMLElement` shim which has\n  // been imported (see note at the top of this file about how this import is\n  // generated by Rollup). Note that the `HTMLElement` variable has been\n  // shadowed by this import, so it no longer refers to the global.\n  extends HTMLElement\n  implements ReactiveControllerHost\n{\n  // Note: these are patched in only in DEV_MODE.\n  /**\n   * Read or set all the enabled warning categories for this class.\n   *\n   * This property is only used in development builds.\n   *\n   * @nocollapse\n   * @category dev-mode\n   */\n  static enabledWarnings?: WarningKind[];\n\n  /**\n   * Enable the given warning category for this class.\n   *\n   * This method only exists in development builds, so it should be accessed\n   * with a guard like:\n   *\n   * ```ts\n   * // Enable for all ReactiveElement subclasses\n   * ReactiveElement.enableWarning?.('migration');\n   *\n   * // Enable for only MyElement and subclasses\n   * MyElement.enableWarning?.('migration');\n   * ```\n   *\n   * @nocollapse\n   * @category dev-mode\n   */\n  static enableWarning?: (warningKind: WarningKind) => void;\n\n  /**\n   * Disable the given warning category for this class.\n   *\n   * This method only exists in development builds, so it should be accessed\n   * with a guard like:\n   *\n   * ```ts\n   * // Disable for all ReactiveElement subclasses\n   * ReactiveElement.disableWarning?.('migration');\n   *\n   * // Disable for only MyElement and subclasses\n   * MyElement.disableWarning?.('migration');\n   * ```\n   *\n   * @nocollapse\n   * @category dev-mode\n   */\n  static disableWarning?: (warningKind: WarningKind) => void;\n\n  /**\n   * Adds an initializer function to the class that is called during instance\n   * construction.\n   *\n   * This is useful for code that runs against a `ReactiveElement`\n   * subclass, such as a decorator, that needs to do work for each\n   * instance, such as setting up a `ReactiveController`.\n   *\n   * ```ts\n   * const myDecorator = (target: typeof ReactiveElement, key: string) => {\n   *   target.addInitializer((instance: ReactiveElement) => {\n   *     // This is run during construction of the element\n   *     new MyController(instance);\n   *   });\n   * }\n   * ```\n   *\n   * Decorating a field will then cause each instance to run an initializer\n   * that adds a controller:\n   *\n   * ```ts\n   * class MyElement extends LitElement {\n   *   @myDecorator foo;\n   * }\n   * ```\n   *\n   * Initializers are stored per-constructor. Adding an initializer to a\n   * subclass does not add it to a superclass. Since initializers are run in\n   * constructors, initializers will run in order of the class hierarchy,\n   * starting with superclasses and progressing to the instance's class.\n   *\n   * @nocollapse\n   */\n  static addInitializer(initializer: Initializer) {\n    this.finalize();\n    (this._initializers ??= []).push(initializer);\n  }\n\n  static _initializers?: Initializer[];\n\n  /*\n   * Due to closure compiler ES6 compilation bugs, @nocollapse is required on\n   * all static methods and properties with initializers.  Reference:\n   * - https://github.com/google/closure-compiler/issues/1776\n   */\n\n  /**\n   * Maps attribute names to properties; for example `foobar` attribute to\n   * `fooBar` property. Created lazily on user subclasses when finalizing the\n   * class.\n   * @nocollapse\n   */\n  private static __attributeToPropertyMap: AttributeMap;\n\n  /**\n   * Marks class as having finished creating properties.\n   */\n  protected static [finalized] = true;\n\n  /**\n   * Memoized list of all element properties, including any superclass properties.\n   * Created lazily on user subclasses when finalizing the class.\n   * @nocollapse\n   * @category properties\n   */\n  static elementProperties: PropertyDeclarationMap = new Map();\n\n  /**\n   * User-supplied object that maps property names to `PropertyDeclaration`\n   * objects containing options for configuring reactive properties. When\n   * a reactive property is set the element will update and render.\n   *\n   * By default properties are public fields, and as such, they should be\n   * considered as primarily settable by element users, either via attribute or\n   * the property itself.\n   *\n   * Generally, properties that are changed by the element should be private or\n   * protected fields and should use the `state: true` option. Properties\n   * marked as `state` do not reflect from the corresponding attribute\n   *\n   * However, sometimes element code does need to set a public property. This\n   * should typically only be done in response to user interaction, and an event\n   * should be fired informing the user; for example, a checkbox sets its\n   * `checked` property when clicked and fires a `changed` event. Mutating\n   * public properties should typically not be done for non-primitive (object or\n   * array) properties. In other cases when an element needs to manage state, a\n   * private property set with the `state: true` option should be used. When\n   * needed, state properties can be initialized via public properties to\n   * facilitate complex interactions.\n   * @nocollapse\n   * @category properties\n   */\n  static properties: PropertyDeclarations;\n\n  /**\n   * Memoized list of all element styles.\n   * Created lazily on user subclasses when finalizing the class.\n   * @nocollapse\n   * @category styles\n   */\n  static elementStyles: Array<CSSResultOrNative> = [];\n\n  /**\n   * Array of styles to apply to the element. The styles should be defined\n   * using the {@linkcode css} tag function, via constructible stylesheets, or\n   * imported from native CSS module scripts.\n   *\n   * Note on Content Security Policy:\n   *\n   * Element styles are implemented with `<style>` tags when the browser doesn't\n   * support adopted StyleSheets. To use such `<style>` tags with the style-src\n   * CSP directive, the style-src value must either include 'unsafe-inline' or\n   * `nonce-<base64-value>` with `<base64-value>` replaced be a server-generated\n   * nonce.\n   *\n   * To provide a nonce to use on generated `<style>` elements, set\n   * `window.litNonce` to a server-generated nonce in your page's HTML, before\n   * loading application code:\n   *\n   * ```html\n   * <script>\n   *   // Generated and unique per request:\n   *   window.litNonce = 'a1b2c3d4';\n   * </script>\n   * ```\n   * @nocollapse\n   * @category styles\n   */\n  static styles?: CSSResultGroup;\n\n  /**\n   * The set of properties defined by this class that caused an accessor to be\n   * added during `createProperty`.\n   * @nocollapse\n   */\n  private static __reactivePropertyKeys?: Set<PropertyKey>;\n\n  /**\n   * Returns a list of attributes corresponding to the registered properties.\n   * @nocollapse\n   * @category attributes\n   */\n  static get observedAttributes() {\n    // note: piggy backing on this to ensure we're finalized.\n    this.finalize();\n    const attributes: string[] = [];\n    // Use forEach so this works even if for/of loops are compiled to for loops\n    // expecting arrays\n    this.elementProperties.forEach((v, p) => {\n      const attr = this.__attributeNameForProperty(p, v);\n      if (attr !== undefined) {\n        this.__attributeToPropertyMap.set(attr, p);\n        attributes.push(attr);\n      }\n    });\n    return attributes;\n  }\n\n  /**\n   * Creates a property accessor on the element prototype if one does not exist\n   * and stores a {@linkcode PropertyDeclaration} for the property with the\n   * given options. The property setter calls the property's `hasChanged`\n   * property option or uses a strict identity check to determine whether or not\n   * to request an update.\n   *\n   * This method may be overridden to customize properties; however,\n   * when doing so, it's important to call `super.createProperty` to ensure\n   * the property is setup correctly. This method calls\n   * `getPropertyDescriptor` internally to get a descriptor to install.\n   * To customize what properties do when they are get or set, override\n   * `getPropertyDescriptor`. To customize the options for a property,\n   * implement `createProperty` like this:\n   *\n   * ```ts\n   * static createProperty(name, options) {\n   *   options = Object.assign(options, {myOption: true});\n   *   super.createProperty(name, options);\n   * }\n   * ```\n   *\n   * @nocollapse\n   * @category properties\n   */\n  static createProperty(\n    name: PropertyKey,\n    options: PropertyDeclaration = defaultPropertyDeclaration\n  ) {\n    // if this is a state property, force the attribute to false.\n    if (options.state) {\n      // Cast as any since this is readonly.\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      (options as any).attribute = false;\n    }\n    // Note, since this can be called by the `@property` decorator which\n    // is called before `finalize`, we ensure finalization has been kicked off.\n    this.finalize();\n    this.elementProperties.set(name, options);\n    // Do not generate an accessor if the prototype already has one, since\n    // it would be lost otherwise and that would never be the user's intention;\n    // Instead, we expect users to call `requestUpdate` themselves from\n    // user-defined accessors. Note that if the super has an accessor we will\n    // still overwrite it\n    if (!options.noAccessor && !this.prototype.hasOwnProperty(name)) {\n      const key = typeof name === 'symbol' ? Symbol() : `__${name}`;\n      const descriptor = this.getPropertyDescriptor(name, key, options);\n      if (descriptor !== undefined) {\n        Object.defineProperty(this.prototype, name, descriptor);\n        if (DEV_MODE) {\n          // If this class doesn't have its own set, create one and initialize\n          // with the values in the set from the nearest ancestor class, if any.\n          if (!this.hasOwnProperty('__reactivePropertyKeys')) {\n            this.__reactivePropertyKeys = new Set(\n              this.__reactivePropertyKeys ?? []\n            );\n          }\n          this.__reactivePropertyKeys!.add(name);\n        }\n      }\n    }\n  }\n\n  /**\n   * Returns a property descriptor to be defined on the given named property.\n   * If no descriptor is returned, the property will not become an accessor.\n   * For example,\n   *\n   * ```ts\n   * class MyElement extends LitElement {\n   *   static getPropertyDescriptor(name, key, options) {\n   *     const defaultDescriptor =\n   *         super.getPropertyDescriptor(name, key, options);\n   *     const setter = defaultDescriptor.set;\n   *     return {\n   *       get: defaultDescriptor.get,\n   *       set(value) {\n   *         setter.call(this, value);\n   *         // custom action.\n   *       },\n   *       configurable: true,\n   *       enumerable: true\n   *     }\n   *   }\n   * }\n   * ```\n   *\n   * @nocollapse\n   * @category properties\n   */\n  protected static getPropertyDescriptor(\n    name: PropertyKey,\n    key: string | symbol,\n    options: PropertyDeclaration\n  ): PropertyDescriptor | undefined {\n    return {\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      get(): any {\n        return (this as {[key: string]: unknown})[key as string];\n      },\n      set(this: ReactiveElement, value: unknown) {\n        const oldValue = (this as {} as {[key: string]: unknown})[\n          name as string\n        ];\n        (this as {} as {[key: string]: unknown})[key as string] = value;\n        (this as unknown as ReactiveElement).requestUpdate(\n          name,\n          oldValue,\n          options\n        );\n      },\n      configurable: true,\n      enumerable: true,\n    };\n  }\n\n  /**\n   * Returns the property options associated with the given property.\n   * These options are defined with a `PropertyDeclaration` via the `properties`\n   * object or the `@property` decorator and are registered in\n   * `createProperty(...)`.\n   *\n   * Note, this method should be considered \"final\" and not overridden. To\n   * customize the options for a given property, override\n   * {@linkcode createProperty}.\n   *\n   * @nocollapse\n   * @final\n   * @category properties\n   */\n  static getPropertyOptions(name: PropertyKey) {\n    return this.elementProperties.get(name) || defaultPropertyDeclaration;\n  }\n\n  /**\n   * Creates property accessors for registered properties, sets up element\n   * styling, and ensures any superclasses are also finalized. Returns true if\n   * the element was finalized.\n   * @nocollapse\n   */\n  protected static finalize() {\n    if (this.hasOwnProperty(finalized)) {\n      return false;\n    }\n    this[finalized] = true;\n    // finalize any superclasses\n    const superCtor = Object.getPrototypeOf(this) as typeof ReactiveElement;\n    superCtor.finalize();\n    // Create own set of initializers for this class if any exist on the\n    // superclass and copy them down. Note, for a small perf boost, avoid\n    // creating initializers unless needed.\n    if (superCtor._initializers !== undefined) {\n      this._initializers = [...superCtor._initializers];\n    }\n    this.elementProperties = new Map(superCtor.elementProperties);\n    // initialize Map populated in observedAttributes\n    this.__attributeToPropertyMap = new Map();\n    // make any properties\n    // Note, only process \"own\" properties since this element will inherit\n    // any properties defined on the superClass, and finalization ensures\n    // the entire prototype chain is finalized.\n    if (this.hasOwnProperty(JSCompiler_renameProperty('properties', this))) {\n      const props = this.properties;\n      // support symbols in properties (IE11 does not support this)\n      const propKeys = [\n        ...Object.getOwnPropertyNames(props),\n        ...Object.getOwnPropertySymbols(props),\n      ];\n      // This for/of is ok because propKeys is an array\n      for (const p of propKeys) {\n        // note, use of `any` is due to TypeScript lack of support for symbol in\n        // index types\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        this.createProperty(p, (props as any)[p]);\n      }\n    }\n    this.elementStyles = this.finalizeStyles(this.styles);\n    // DEV mode warnings\n    if (DEV_MODE) {\n      const warnRemovedOrRenamed = (name: string, renamed = false) => {\n        if (this.prototype.hasOwnProperty(name)) {\n          issueWarning(\n            renamed ? 'renamed-api' : 'removed-api',\n            `\\`${name}\\` is implemented on class ${this.name}. It ` +\n              `has been ${renamed ? 'renamed' : 'removed'} ` +\n              `in this version of LitElement.`\n          );\n        }\n      };\n      warnRemovedOrRenamed('initialize');\n      warnRemovedOrRenamed('requestUpdateInternal');\n      warnRemovedOrRenamed('_getUpdateComplete', true);\n    }\n    return true;\n  }\n\n  /**\n   * Options used when calling `attachShadow`. Set this property to customize\n   * the options for the shadowRoot; for example, to create a closed\n   * shadowRoot: `{mode: 'closed'}`.\n   *\n   * Note, these options are used in `createRenderRoot`. If this method\n   * is customized, options should be respected if possible.\n   * @nocollapse\n   * @category rendering\n   */\n  static shadowRootOptions: ShadowRootInit = {mode: 'open'};\n\n  /**\n   * Takes the styles the user supplied via the `static styles` property and\n   * returns the array of styles to apply to the element.\n   * Override this method to integrate into a style management system.\n   *\n   * Styles are deduplicated preserving the _last_ instance in the list. This\n   * is a performance optimization to avoid duplicated styles that can occur\n   * especially when composing via subclassing. The last item is kept to try\n   * to preserve the cascade order with the assumption that it's most important\n   * that last added styles override previous styles.\n   *\n   * @nocollapse\n   * @category styles\n   */\n  protected static finalizeStyles(\n    styles?: CSSResultGroup\n  ): Array<CSSResultOrNative> {\n    const elementStyles = [];\n    if (Array.isArray(styles)) {\n      // Dedupe the flattened array in reverse order to preserve the last items.\n      // Casting to Array<unknown> works around TS error that\n      // appears to come from trying to flatten a type CSSResultArray.\n      const set = new Set((styles as Array<unknown>).flat(Infinity).reverse());\n      // Then preserve original order by adding the set items in reverse order.\n      for (const s of set) {\n        elementStyles.unshift(getCompatibleStyle(s as CSSResultOrNative));\n      }\n    } else if (styles !== undefined) {\n      elementStyles.push(getCompatibleStyle(styles));\n    }\n    return elementStyles;\n  }\n\n  /**\n   * Node or ShadowRoot into which element DOM should be rendered. Defaults\n   * to an open shadowRoot.\n   * @category rendering\n   */\n  readonly renderRoot!: HTMLElement | ShadowRoot;\n\n  /**\n   * Returns the property name for the given attribute `name`.\n   * @nocollapse\n   */\n  private static __attributeNameForProperty(\n    name: PropertyKey,\n    options: PropertyDeclaration\n  ) {\n    const attribute = options.attribute;\n    return attribute === false\n      ? undefined\n      : typeof attribute === 'string'\n      ? attribute\n      : typeof name === 'string'\n      ? name.toLowerCase()\n      : undefined;\n  }\n\n  private __instanceProperties?: PropertyValues = new Map();\n  // Initialize to an unresolved Promise so we can make sure the element has\n  // connected before first update.\n  private __updatePromise!: Promise<boolean>;\n\n  /**\n   * True if there is a pending update as a result of calling `requestUpdate()`.\n   * Should only be read.\n   * @category updates\n   */\n  isUpdatePending = false;\n\n  /**\n   * Is set to `true` after the first update. The element code cannot assume\n   * that `renderRoot` exists before the element `hasUpdated`.\n   * @category updates\n   */\n  hasUpdated = false;\n\n  /**\n   * Map with keys for any properties that have changed since the last\n   * update cycle with previous values.\n   *\n   * @internal\n   */\n  _$changedProperties!: PropertyValues;\n\n  /**\n   * Map with keys of properties that should be reflected when updated.\n   */\n  private __reflectingProperties?: Map<PropertyKey, PropertyDeclaration>;\n\n  /**\n   * Name of currently reflecting property\n   */\n  private __reflectingProperty: PropertyKey | null = null;\n\n  /**\n   * Set of controllers.\n   */\n  private __controllers?: ReactiveController[];\n\n  constructor() {\n    super();\n    this.__initialize();\n  }\n\n  /**\n   * Internal only override point for customizing work done when elements\n   * are constructed.\n   */\n  private __initialize() {\n    this.__updatePromise = new Promise<boolean>(\n      (res) => (this.enableUpdating = res)\n    );\n    this._$changedProperties = new Map();\n    this.__saveInstanceProperties();\n    // ensures first update will be caught by an early access of\n    // `updateComplete`\n    this.requestUpdate();\n    (this.constructor as typeof ReactiveElement)._initializers?.forEach((i) =>\n      i(this)\n    );\n  }\n\n  /**\n   * Registers a `ReactiveController` to participate in the element's reactive\n   * update cycle. The element automatically calls into any registered\n   * controllers during its lifecycle callbacks.\n   *\n   * If the element is connected when `addController()` is called, the\n   * controller's `hostConnected()` callback will be immediately called.\n   * @category controllers\n   */\n  addController(controller: ReactiveController) {\n    (this.__controllers ??= []).push(controller);\n    // If a controller is added after the element has been connected,\n    // call hostConnected. Note, re-using existence of `renderRoot` here\n    // (which is set in connectedCallback) to avoid the need to track a\n    // first connected state.\n    if (this.renderRoot !== undefined && this.isConnected) {\n      controller.hostConnected?.();\n    }\n  }\n\n  /**\n   * Removes a `ReactiveController` from the element.\n   * @category controllers\n   */\n  removeController(controller: ReactiveController) {\n    // Note, if the indexOf is -1, the >>> will flip the sign which makes the\n    // splice do nothing.\n    this.__controllers?.splice(this.__controllers.indexOf(controller) >>> 0, 1);\n  }\n\n  /**\n   * Fixes any properties set on the instance before upgrade time.\n   * Otherwise these would shadow the accessor and break these properties.\n   * The properties are stored in a Map which is played back after the\n   * constructor runs. Note, on very old versions of Safari (<=9) or Chrome\n   * (<=41), properties created for native platform properties like (`id` or\n   * `name`) may not have default values set in the element constructor. On\n   * these browsers native properties appear on instances and therefore their\n   * default value will overwrite any element default (e.g. if the element sets\n   * this.id = 'id' in the constructor, the 'id' will become '' since this is\n   * the native platform default).\n   */\n  private __saveInstanceProperties() {\n    // Use forEach so this works even if for/of loops are compiled to for loops\n    // expecting arrays\n    (this.constructor as typeof ReactiveElement).elementProperties.forEach(\n      (_v, p) => {\n        if (this.hasOwnProperty(p)) {\n          this.__instanceProperties!.set(p, this[p as keyof this]);\n          delete this[p as keyof this];\n        }\n      }\n    );\n  }\n\n  /**\n   * Returns the node into which the element should render and by default\n   * creates and returns an open shadowRoot. Implement to customize where the\n   * element's DOM is rendered. For example, to render into the element's\n   * childNodes, return `this`.\n   *\n   * @return Returns a node into which to render.\n   * @category rendering\n   */\n  protected createRenderRoot(): Element | ShadowRoot {\n    const renderRoot =\n      this.shadowRoot ??\n      this.attachShadow(\n        (this.constructor as typeof ReactiveElement).shadowRootOptions\n      );\n    adoptStyles(\n      renderRoot,\n      (this.constructor as typeof ReactiveElement).elementStyles\n    );\n    return renderRoot;\n  }\n\n  /**\n   * On first connection, creates the element's renderRoot, sets up\n   * element styling, and enables updating.\n   * @category lifecycle\n   */\n  connectedCallback() {\n    // create renderRoot before first update.\n    if (this.renderRoot === undefined) {\n      (\n        this as {\n          renderRoot: Element | DocumentFragment;\n        }\n      ).renderRoot = this.createRenderRoot();\n    }\n    this.enableUpdating(true);\n    this.__controllers?.forEach((c) => c.hostConnected?.());\n  }\n\n  /**\n   * Note, this method should be considered final and not overridden. It is\n   * overridden on the element instance with a function that triggers the first\n   * update.\n   * @category updates\n   */\n  protected enableUpdating(_requestedUpdate: boolean) {}\n\n  /**\n   * Allows for `super.disconnectedCallback()` in extensions while\n   * reserving the possibility of making non-breaking feature additions\n   * when disconnecting at some point in the future.\n   * @category lifecycle\n   */\n  disconnectedCallback() {\n    this.__controllers?.forEach((c) => c.hostDisconnected?.());\n  }\n\n  /**\n   * Synchronizes property values when attributes change.\n   *\n   * Specifically, when an attribute is set, the corresponding property is set.\n   * You should rarely need to implement this callback. If this method is\n   * overridden, `super.attributeChangedCallback(name, _old, value)` must be\n   * called.\n   *\n   * See [using the lifecycle callbacks](https://developer.mozilla.org/en-US/docs/Web/Web_Components/Using_custom_elements#using_the_lifecycle_callbacks)\n   * on MDN for more information about the `attributeChangedCallback`.\n   * @category attributes\n   */\n  attributeChangedCallback(\n    name: string,\n    _old: string | null,\n    value: string | null\n  ) {\n    this._$attributeToProperty(name, value);\n  }\n\n  private __propertyToAttribute(\n    name: PropertyKey,\n    value: unknown,\n    options: PropertyDeclaration = defaultPropertyDeclaration\n  ) {\n    const attr = (\n      this.constructor as typeof ReactiveElement\n    ).__attributeNameForProperty(name, options);\n    if (attr !== undefined && options.reflect === true) {\n      const converter =\n        (options.converter as ComplexAttributeConverter)?.toAttribute !==\n        undefined\n          ? (options.converter as ComplexAttributeConverter)\n          : defaultConverter;\n      const attrValue = converter.toAttribute!(value, options.type);\n      if (\n        DEV_MODE &&\n        (this.constructor as typeof ReactiveElement).enabledWarnings!.indexOf(\n          'migration'\n        ) >= 0 &&\n        attrValue === undefined\n      ) {\n        issueWarning(\n          'undefined-attribute-value',\n          `The attribute value for the ${name as string} property is ` +\n            `undefined on element ${this.localName}. The attribute will be ` +\n            `removed, but in the previous version of \\`ReactiveElement\\`, ` +\n            `the attribute would not have changed.`\n        );\n      }\n      // Track if the property is being reflected to avoid\n      // setting the property again via `attributeChangedCallback`. Note:\n      // 1. this takes advantage of the fact that the callback is synchronous.\n      // 2. will behave incorrectly if multiple attributes are in the reaction\n      // stack at time of calling. However, since we process attributes\n      // in `update` this should not be possible (or an extreme corner case\n      // that we'd like to discover).\n      // mark state reflecting\n      this.__reflectingProperty = name;\n      if (attrValue == null) {\n        this.removeAttribute(attr);\n      } else {\n        this.setAttribute(attr, attrValue as string);\n      }\n      // mark state not reflecting\n      this.__reflectingProperty = null;\n    }\n  }\n\n  /** @internal */\n  _$attributeToProperty(name: string, value: string | null) {\n    const ctor = this.constructor as typeof ReactiveElement;\n    // Note, hint this as an `AttributeMap` so closure clearly understands\n    // the type; it has issues with tracking types through statics\n    const propName = (ctor.__attributeToPropertyMap as AttributeMap).get(name);\n    // Use tracking info to avoid reflecting a property value to an attribute\n    // if it was just set because the attribute changed.\n    if (propName !== undefined && this.__reflectingProperty !== propName) {\n      const options = ctor.getPropertyOptions(propName);\n      const converter =\n        typeof options.converter === 'function'\n          ? {fromAttribute: options.converter}\n          : options.converter?.fromAttribute !== undefined\n          ? options.converter\n          : defaultConverter;\n      // mark state reflecting\n      this.__reflectingProperty = propName;\n      this[propName as keyof this] = converter.fromAttribute!(\n        value,\n        options.type\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      ) as any;\n      // mark state not reflecting\n      this.__reflectingProperty = null;\n    }\n  }\n\n  /**\n   * Requests an update which is processed asynchronously. This should be called\n   * when an element should update based on some state not triggered by setting\n   * a reactive property. In this case, pass no arguments. It should also be\n   * called when manually implementing a property setter. In this case, pass the\n   * property `name` and `oldValue` to ensure that any configured property\n   * options are honored.\n   *\n   * @param name name of requesting property\n   * @param oldValue old value of requesting property\n   * @param options property options to use instead of the previously\n   *     configured options\n   * @category updates\n   */\n  requestUpdate(\n    name?: PropertyKey,\n    oldValue?: unknown,\n    options?: PropertyDeclaration\n  ): void {\n    let shouldRequestUpdate = true;\n    // If we have a property key, perform property update steps.\n    if (name !== undefined) {\n      options =\n        options ||\n        (this.constructor as typeof ReactiveElement).getPropertyOptions(name);\n      const hasChanged = options.hasChanged || notEqual;\n      if (hasChanged(this[name as keyof this], oldValue)) {\n        if (!this._$changedProperties.has(name)) {\n          this._$changedProperties.set(name, oldValue);\n        }\n        // Add to reflecting properties set.\n        // Note, it's important that every change has a chance to add the\n        // property to `_reflectingProperties`. This ensures setting\n        // attribute + property reflects correctly.\n        if (options.reflect === true && this.__reflectingProperty !== name) {\n          if (this.__reflectingProperties === undefined) {\n            this.__reflectingProperties = new Map();\n          }\n          this.__reflectingProperties.set(name, options);\n        }\n      } else {\n        // Abort the request if the property should not be considered changed.\n        shouldRequestUpdate = false;\n      }\n    }\n    if (!this.isUpdatePending && shouldRequestUpdate) {\n      this.__updatePromise = this.__enqueueUpdate();\n    }\n    // Note, since this no longer returns a promise, in dev mode we return a\n    // thenable which warns if it's called.\n    return DEV_MODE\n      ? (requestUpdateThenable(this.localName) as unknown as void)\n      : undefined;\n  }\n\n  /**\n   * Sets up the element to asynchronously update.\n   */\n  private async __enqueueUpdate() {\n    this.isUpdatePending = true;\n    try {\n      // Ensure any previous update has resolved before updating.\n      // This `await` also ensures that property changes are batched.\n      await this.__updatePromise;\n    } catch (e) {\n      // Refire any previous errors async so they do not disrupt the update\n      // cycle. Errors are refired so developers have a chance to observe\n      // them, and this can be done by implementing\n      // `window.onunhandledrejection`.\n      Promise.reject(e);\n    }\n    const result = this.scheduleUpdate();\n    // If `scheduleUpdate` returns a Promise, we await it. This is done to\n    // enable coordinating updates with a scheduler. Note, the result is\n    // checked to avoid delaying an additional microtask unless we need to.\n    if (result != null) {\n      await result;\n    }\n    return !this.isUpdatePending;\n  }\n\n  /**\n   * Schedules an element update. You can override this method to change the\n   * timing of updates by returning a Promise. The update will await the\n   * returned Promise, and you should resolve the Promise to allow the update\n   * to proceed. If this method is overridden, `super.scheduleUpdate()`\n   * must be called.\n   *\n   * For instance, to schedule updates to occur just before the next frame:\n   *\n   * ```ts\n   * override protected async scheduleUpdate(): Promise<unknown> {\n   *   await new Promise((resolve) => requestAnimationFrame(() => resolve()));\n   *   super.scheduleUpdate();\n   * }\n   * ```\n   * @category updates\n   */\n  protected scheduleUpdate(): void | Promise<unknown> {\n    return this.performUpdate();\n  }\n\n  /**\n   * Performs an element update. Note, if an exception is thrown during the\n   * update, `firstUpdated` and `updated` will not be called.\n   *\n   * Call `performUpdate()` to immediately process a pending update. This should\n   * generally not be needed, but it can be done in rare cases when you need to\n   * update synchronously.\n   *\n   * Note: To ensure `performUpdate()` synchronously completes a pending update,\n   * it should not be overridden. In LitElement 2.x it was suggested to override\n   * `performUpdate()` to also customizing update scheduling. Instead, you should now\n   * override `scheduleUpdate()`. For backwards compatibility with LitElement 2.x,\n   * scheduling updates via `performUpdate()` continues to work, but will make\n   * also calling `performUpdate()` to synchronously process updates difficult.\n   *\n   * @category updates\n   */\n  protected performUpdate(): void | Promise<unknown> {\n    // Abort any update if one is not pending when this is called.\n    // This can happen if `performUpdate` is called early to \"flush\"\n    // the update.\n    if (!this.isUpdatePending) {\n      return;\n    }\n    debugLogEvent?.({kind: 'update'});\n    // create renderRoot before first update.\n    if (!this.hasUpdated) {\n      // Produce warning if any class properties are shadowed by class fields\n      if (DEV_MODE) {\n        const shadowedProperties: string[] = [];\n        (\n          this.constructor as typeof ReactiveElement\n        ).__reactivePropertyKeys?.forEach((p) => {\n          if (this.hasOwnProperty(p) && !this.__instanceProperties?.has(p)) {\n            shadowedProperties.push(p as string);\n          }\n        });\n        if (shadowedProperties.length) {\n          throw new Error(\n            `The following properties on element ${this.localName} will not ` +\n              `trigger updates as expected because they are set using class ` +\n              `fields: ${shadowedProperties.join(', ')}. ` +\n              `Native class fields and some compiled output will overwrite ` +\n              `accessors used for detecting changes. See ` +\n              `https://lit.dev/msg/class-field-shadowing ` +\n              `for more information.`\n          );\n        }\n      }\n    }\n    // Mixin instance properties once, if they exist.\n    if (this.__instanceProperties) {\n      // Use forEach so this works even if for/of loops are compiled to for loops\n      // expecting arrays\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      this.__instanceProperties!.forEach((v, p) => ((this as any)[p] = v));\n      this.__instanceProperties = undefined;\n    }\n    let shouldUpdate = false;\n    const changedProperties = this._$changedProperties;\n    try {\n      shouldUpdate = this.shouldUpdate(changedProperties);\n      if (shouldUpdate) {\n        this.willUpdate(changedProperties);\n        this.__controllers?.forEach((c) => c.hostUpdate?.());\n        this.update(changedProperties);\n      } else {\n        this.__markUpdated();\n      }\n    } catch (e) {\n      // Prevent `firstUpdated` and `updated` from running when there's an\n      // update exception.\n      shouldUpdate = false;\n      // Ensure element can accept additional updates after an exception.\n      this.__markUpdated();\n      throw e;\n    }\n    // The update is no longer considered pending and further updates are now allowed.\n    if (shouldUpdate) {\n      this._$didUpdate(changedProperties);\n    }\n  }\n\n  /**\n   * Invoked before `update()` to compute values needed during the update.\n   *\n   * Implement `willUpdate` to compute property values that depend on other\n   * properties and are used in the rest of the update process.\n   *\n   * ```ts\n   * willUpdate(changedProperties) {\n   *   // only need to check changed properties for an expensive computation.\n   *   if (changedProperties.has('firstName') || changedProperties.has('lastName')) {\n   *     this.sha = computeSHA(`${this.firstName} ${this.lastName}`);\n   *   }\n   * }\n   *\n   * render() {\n   *   return html`SHA: ${this.sha}`;\n   * }\n   * ```\n   *\n   * @category updates\n   */\n  protected willUpdate(_changedProperties: PropertyValues): void {}\n\n  // Note, this is an override point for polyfill-support.\n  // @internal\n  _$didUpdate(changedProperties: PropertyValues) {\n    this.__controllers?.forEach((c) => c.hostUpdated?.());\n    if (!this.hasUpdated) {\n      this.hasUpdated = true;\n      this.firstUpdated(changedProperties);\n    }\n    this.updated(changedProperties);\n    if (\n      DEV_MODE &&\n      this.isUpdatePending &&\n      (this.constructor as typeof ReactiveElement).enabledWarnings!.indexOf(\n        'change-in-update'\n      ) >= 0\n    ) {\n      issueWarning(\n        'change-in-update',\n        `Element ${this.localName} scheduled an update ` +\n          `(generally because a property was set) ` +\n          `after an update completed, causing a new update to be scheduled. ` +\n          `This is inefficient and should be avoided unless the next update ` +\n          `can only be scheduled as a side effect of the previous update.`\n      );\n    }\n  }\n\n  private __markUpdated() {\n    this._$changedProperties = new Map();\n    this.isUpdatePending = false;\n  }\n\n  /**\n   * Returns a Promise that resolves when the element has completed updating.\n   * The Promise value is a boolean that is `true` if the element completed the\n   * update without triggering another update. The Promise result is `false` if\n   * a property was set inside `updated()`. If the Promise is rejected, an\n   * exception was thrown during the update.\n   *\n   * To await additional asynchronous work, override the `getUpdateComplete`\n   * method. For example, it is sometimes useful to await a rendered element\n   * before fulfilling this Promise. To do this, first await\n   * `super.getUpdateComplete()`, then any subsequent state.\n   *\n   * @return A promise of a boolean that resolves to true if the update completed\n   *     without triggering another update.\n   * @category updates\n   */\n  get updateComplete(): Promise<boolean> {\n    return this.getUpdateComplete();\n  }\n\n  /**\n   * Override point for the `updateComplete` promise.\n   *\n   * It is not safe to override the `updateComplete` getter directly due to a\n   * limitation in TypeScript which means it is not possible to call a\n   * superclass getter (e.g. `super.updateComplete.then(...)`) when the target\n   * language is ES5 (https://github.com/microsoft/TypeScript/issues/338).\n   * This method should be overridden instead. For example:\n   *\n   * ```ts\n   * class MyElement extends LitElement {\n   *   override async getUpdateComplete() {\n   *     const result = await super.getUpdateComplete();\n   *     await this._myChild.updateComplete;\n   *     return result;\n   *   }\n   * }\n   * ```\n   *\n   * @return A promise of a boolean that resolves to true if the update completed\n   *     without triggering another update.\n   * @category updates\n   */\n  protected getUpdateComplete(): Promise<boolean> {\n    return this.__updatePromise;\n  }\n\n  /**\n   * Controls whether or not `update()` should be called when the element requests\n   * an update. By default, this method always returns `true`, but this can be\n   * customized to control when to update.\n   *\n   * @param _changedProperties Map of changed properties with old values\n   * @category updates\n   */\n  protected shouldUpdate(_changedProperties: PropertyValues): boolean {\n    return true;\n  }\n\n  /**\n   * Updates the element. This method reflects property values to attributes.\n   * It can be overridden to render and keep updated element DOM.\n   * Setting properties inside this method will *not* trigger\n   * another update.\n   *\n   * @param _changedProperties Map of changed properties with old values\n   * @category updates\n   */\n  protected update(_changedProperties: PropertyValues) {\n    if (this.__reflectingProperties !== undefined) {\n      // Use forEach so this works even if for/of loops are compiled to for\n      // loops expecting arrays\n      this.__reflectingProperties.forEach((v, k) =>\n        this.__propertyToAttribute(k, this[k as keyof this], v)\n      );\n      this.__reflectingProperties = undefined;\n    }\n    this.__markUpdated();\n  }\n\n  /**\n   * Invoked whenever the element is updated. Implement to perform\n   * post-updating tasks via DOM APIs, for example, focusing an element.\n   *\n   * Setting properties inside this method will trigger the element to update\n   * again after this update cycle completes.\n   *\n   * @param _changedProperties Map of changed properties with old values\n   * @category updates\n   */\n  protected updated(_changedProperties: PropertyValues) {}\n\n  /**\n   * Invoked when the element is first updated. Implement to perform one time\n   * work on the element after update.\n   *\n   * ```ts\n   * firstUpdated() {\n   *   this.renderRoot.getElementById('my-text-area').focus();\n   * }\n   * ```\n   *\n   * Setting properties inside this method will trigger the element to update\n   * again after this update cycle completes.\n   *\n   * @param _changedProperties Map of changed properties with old values\n   * @category updates\n   */\n  protected firstUpdated(_changedProperties: PropertyValues) {}\n}\n\n// Apply polyfills if available\npolyfillSupport?.({ReactiveElement});\n\n// Dev mode warnings...\nif (DEV_MODE) {\n  // Default warning set.\n  ReactiveElement.enabledWarnings = ['change-in-update'];\n  const ensureOwnWarnings = function (ctor: typeof ReactiveElement) {\n    if (\n      !ctor.hasOwnProperty(JSCompiler_renameProperty('enabledWarnings', ctor))\n    ) {\n      ctor.enabledWarnings = ctor.enabledWarnings!.slice();\n    }\n  };\n  ReactiveElement.enableWarning = function (\n    this: typeof ReactiveElement,\n    warning: WarningKind\n  ) {\n    ensureOwnWarnings(this);\n    if (this.enabledWarnings!.indexOf(warning) < 0) {\n      this.enabledWarnings!.push(warning);\n    }\n  };\n  ReactiveElement.disableWarning = function (\n    this: typeof ReactiveElement,\n    warning: WarningKind\n  ) {\n    ensureOwnWarnings(this);\n    const i = this.enabledWarnings!.indexOf(warning);\n    if (i >= 0) {\n      this.enabledWarnings!.splice(i, 1);\n    }\n  };\n}\n\n// IMPORTANT: do not change the property name or the assignment expression.\n// This line will be used in regexes to search for ReactiveElement usage.\n(global.reactiveElementVersions ??= []).push('1.6.3');\nif (DEV_MODE && global.reactiveElementVersions.length > 1) {\n  issueWarning!(\n    'multiple-versions',\n    `Multiple versions of Lit loaded. Loading multiple versions ` +\n      `is not recommended.`\n  );\n}\n"], "names": ["global", "window", "trustedTypes", "emptyStringForBooleanAttribute", "emptyScript", "polyfillSupport", "reactiveElementPolyfillSupport", "defaultConverter", "toAttribute", "value", "type", "Boolean", "Object", "Array", "JSON", "stringify", "fromAttribute", "fromValue", "Number", "parse", "e", "notEqual", "old", "defaultPropertyDeclaration", "attribute", "String", "converter", "reflect", "has<PERSON><PERSON>ed", "finalized", "ReactiveElement", "HTMLElement", "constructor", "super", "this", "__instanceProperties", "Map", "isUpdatePending", "hasUpdated", "__reflectingProperty", "__initialize", "static", "initializer", "finalize", "_a", "_initializers", "push", "observedAttributes", "attributes", "elementProperties", "for<PERSON>ach", "v", "p", "attr", "__attributeNameForProperty", "undefined", "__attributeToPropertyMap", "set", "name", "options", "state", "noAccessor", "prototype", "hasOwnProperty", "key", "Symbol", "descriptor", "getPropertyDescriptor", "defineProperty", "get", "oldValue", "requestUpdate", "configurable", "enumerable", "superCtor", "getPrototypeOf", "props", "properties", "propKeys", "getOwnPropertyNames", "getOwnPropertySymbols", "createProperty", "elementStyles", "finalizeStyles", "styles", "isArray", "Set", "flat", "Infinity", "reverse", "s", "unshift", "getCompatibleStyle", "toLowerCase", "__updatePromise", "Promise", "res", "enableUpdating", "_$changedProperties", "__saveInstanceProperties", "i", "addController", "controller", "__controllers", "renderRoot", "isConnected", "_b", "hostConnected", "call", "removeController", "splice", "indexOf", "_v", "createRenderRoot", "shadowRoot", "attachShadow", "shadowRootOptions", "adoptStyles", "connectedCallback", "c", "_requestedUpdate", "disconnectedCallback", "hostDisconnected", "attributeChangedCallback", "_old", "_$attributeToProperty", "__propertyToAttribute", "attrValue", "removeAttribute", "setAttribute", "ctor", "propName", "getPropertyOptions", "shouldRequestUpdate", "has", "__reflectingProperties", "__enqueueUpdate", "async", "reject", "result", "scheduleUpdate", "performUpdate", "shouldUpdate", "changedProperties", "willUpdate", "hostUpdate", "update", "__markUpdated", "_$didUpdate", "_changedProperties", "hostUpdated", "firstUpdated", "updated", "updateComplete", "getUpdateComplete", "k", "mode", "_d", "reactiveElementVersions"], "mappings": ";;;;;SAiCA,MAAMA,EAAkCC,OAiBlCC,EAAgBF,EACnBE,aAMGC,EAAiCD,EAClCA,EAAaE,YACd,GAEEC,EAEFL,EAAOM,+BA8PEC,EAA8C,CACzDC,YAAYC,EAAgBC,GAC1B,OAAQA,GACN,KAAKC,QACHF,EAAQA,EAAQN,EAAiC,KACjD,MACF,KAAKS,OACL,KAAKC,MAGHJ,EAAiB,MAATA,EAAgBA,EAAQK,KAAKC,UAAUN,GAGnD,OAAOA,CACR,EAEDO,cAAcP,EAAsBC,GAClC,IAAIO,EAAqBR,EACzB,OAAQC,GACN,KAAKC,QACHM,EAAsB,OAAVR,EACZ,MACF,KAAKS,OACHD,EAAsB,OAAVR,EAAiB,KAAOS,OAAOT,GAC3C,MACF,KAAKG,OACL,KAAKC,MAIH,IAEEI,EAAYH,KAAKK,MAAMV,EACxB,CAAC,MAAOW,GACPH,EAAY,IACb,EAGL,OAAOA,CACR,GAWUI,EAAuB,CAACZ,EAAgBa,IAE5CA,IAAQb,IAAUa,GAAQA,GAAOb,GAAUA,GAG9Cc,EAAkD,CACtDC,WAAW,EACXd,KAAMe,OACNC,UAAWnB,EACXoB,SAAS,EACTC,WAAYP,GASRQ,EAAY,kBAeIC,UASZC,YAsgBRC,cACEC,QA3CMC,KAAAC,KAAwC,IAAIC,IAUpDF,KAAeG,iBAAG,EAOlBH,KAAUI,YAAG,EAkBLJ,KAAoBK,KAAuB,KASjDL,KAAKM,MACN,CApbDC,sBAAsBC,SACpBR,KAAKS,YACc,QAAnBC,EAACV,KAAKW,SAAa,IAAAD,EAAAA,EAAlBV,KAAKW,EAAkB,IAAIC,KAAKJ,EAClC,CA0GUK,gCAETb,KAAKS,WACL,MAAMK,EAAuB,GAU7B,OAPAd,KAAKe,kBAAkBC,SAAQ,CAACC,EAAGC,KACjC,MAAMC,EAAOnB,KAAKoB,KAA2BF,EAAGD,QACnCI,IAATF,IACFnB,KAAKsB,KAAyBC,IAAIJ,EAAMD,GACxCJ,EAAWF,KAAKO,GACjB,IAEIL,CACR,CA2BDP,sBACEiB,EACAC,EAA+BpC,GAiB/B,GAdIoC,EAAQC,QAGTD,EAAgBnC,WAAY,GAI/BU,KAAKS,WACLT,KAAKe,kBAAkBQ,IAAIC,EAAMC,IAM5BA,EAAQE,aAAe3B,KAAK4B,UAAUC,eAAeL,GAAO,CAC/D,MAAMM,EAAsB,iBAATN,EAAoBO,SAAW,KAAKP,EACjDQ,EAAahC,KAAKiC,sBAAsBT,EAAMM,EAAKL,QACtCJ,IAAfW,GACFtD,OAAOwD,eAAelC,KAAK4B,UAAWJ,EAAMQ,EAY/C,CACF,CA6BSzB,6BACRiB,EACAM,EACAL,GAEA,MAAO,CAELU,MACE,OAAQnC,KAAkC8B,EAC3C,EACDP,IAA2BhD,GACzB,MAAM6D,EAAYpC,KAChBwB,GAEDxB,KAAwC8B,GAAiBvD,EACzDyB,KAAoCqC,cACnCb,EACAY,EACAX,EAEH,EACDa,cAAc,EACdC,YAAY,EAEf,CAgBDhC,0BAA0BiB,GACxB,OAAOxB,KAAKe,kBAAkBoB,IAAIX,IAASnC,CAC5C,CAQSkB,kBACR,GAAIP,KAAK6B,eAAelC,GACtB,OAAO,EAETK,KAAKL,IAAa,EAElB,MAAM6C,EAAY9D,OAAO+D,eAAezC,MAexC,GAdAwC,EAAU/B,gBAIsBY,IAA5BmB,EAAU7B,IACZX,KAAKW,EAAgB,IAAI6B,EAAU7B,IAErCX,KAAKe,kBAAoB,IAAIb,IAAIsC,EAAUzB,mBAE3Cf,KAAKsB,KAA2B,IAAIpB,IAKhCF,KAAK6B,eAAyC,cAAsB,CACtE,MAAMa,EAAQ1C,KAAK2C,WAEbC,EAAW,IACZlE,OAAOmE,oBAAoBH,MAC3BhE,OAAOoE,sBAAsBJ,IAGlC,IAAK,MAAMxB,KAAK0B,EAId5C,KAAK+C,eAAe7B,EAAIwB,EAAcxB,GAEzC,CAkBD,OAjBAlB,KAAKgD,cAAgBhD,KAAKiD,eAAejD,KAAKkD,SAiBvC,CACR,CA4BS3C,sBACR2C,GAEA,MAAMF,EAAgB,GACtB,GAAIrE,MAAMwE,QAAQD,GAAS,CAIzB,MAAM3B,EAAM,IAAI6B,IAAKF,EAA0BG,KAAKC,KAAUC,WAE9D,IAAK,MAAMC,KAAKjC,EACdyB,EAAcS,QAAQC,EAAmBF,GAE5C,WAAqBnC,IAAX6B,GACTF,EAAcpC,KAAK8C,EAAmBR,IAExC,OAAOF,CACR,CAaOzC,YACNiB,EACAC,GAEA,MAAMnC,EAAYmC,EAAQnC,UAC1B,OAAqB,IAAdA,OACH+B,EACqB,iBAAd/B,EACPA,EACgB,iBAATkC,EACPA,EAAKmC,mBACLtC,CACL,CAqDOf,aACNN,KAAK4D,KAAkB,IAAIC,SACxBC,GAAS9D,KAAK+D,eAAiBD,IAElC9D,KAAKgE,KAAsB,IAAI9D,IAC/BF,KAAKiE,OAGLjE,KAAKqC,gBACqD,QAA1D3B,EAACV,KAAKF,YAAuCa,SAAa,IAAAD,GAAAA,EAAEM,SAASkD,GACnEA,EAAElE,OAEL,CAWDmE,cAAcC,YACO,QAAnB1D,EAACV,KAAKqE,YAAa,IAAA3D,EAAAA,EAAlBV,KAAKqE,KAAkB,IAAIzD,KAAKwD,QAKT/C,IAApBrB,KAAKsE,YAA4BtE,KAAKuE,cAChB,QAAxBC,EAAAJ,EAAWK,qBAAa,IAAAD,GAAAA,EAAAE,KAAAN,GAE3B,CAMDO,iBAAiBP,iBAGf1D,EAAAV,KAAKqE,qBAAeO,OAAO5E,KAAKqE,KAAcQ,QAAQT,KAAgB,EAAG,EAC1E,CAcOH,OAGLjE,KAAKF,YAAuCiB,kBAAkBC,SAC7D,CAAC8D,EAAI5D,KACClB,KAAK6B,eAAeX,KACtBlB,KAAKC,KAAsBsB,IAAIL,EAAGlB,KAAKkB,WAChClB,KAAKkB,GACb,GAGN,CAWS6D,yBACR,MAAMT,EAEJ,QADA5D,EAAAV,KAAKgF,kBACL,IAAAtE,EAAAA,EAAAV,KAAKiF,aACFjF,KAAKF,YAAuCoF,mBAMjD,OAJAC,EACEb,EACCtE,KAAKF,YAAuCkD,eAExCsB,CACR,CAODc,+BAE0B/D,IAApBrB,KAAKsE,aAELtE,KAGAsE,WAAatE,KAAK+E,oBAEtB/E,KAAK+D,gBAAe,GACF,QAAlBrD,EAAAV,KAAKqE,YAAa,IAAA3D,GAAAA,EAAEM,SAASqE,UAAM,eAAA3E,EAAA2E,EAAEZ,2CAAiB,GACvD,CAQSV,eAAeuB,GAA6B,CAQtDC,6BACoB,QAAlB7E,EAAAV,KAAKqE,YAAa,IAAA3D,GAAAA,EAAEM,SAASqE,UAAM,eAAA3E,EAAA2E,EAAEG,8CAAoB,GAC1D,CAcDC,yBACEjE,EACAkE,EACAnH,GAEAyB,KAAK2F,KAAsBnE,EAAMjD,EAClC,CAEOqH,KACNpE,EACAjD,EACAkD,EAA+BpC,SAE/B,MAAM8B,EACJnB,KAAKF,YACLsB,KAA2BI,EAAMC,GACnC,QAAaJ,IAATF,IAA0C,IAApBM,EAAQhC,QAAkB,CAClD,MAKMoG,QAHJxE,aADAX,EAACe,EAAQjC,gCAAyClB,aAE7CmD,EAAQjC,UACTnB,GACsBC,YAAaC,EAAOkD,EAAQjD,MAwBxDwB,KAAKK,KAAuBmB,EACX,MAAbqE,EACF7F,KAAK8F,gBAAgB3E,GAErBnB,KAAK+F,aAAa5E,EAAM0E,GAG1B7F,KAAKK,KAAuB,IAC7B,CACF,CAGDsF,KAAsBnE,EAAcjD,SAClC,MAAMyH,EAAOhG,KAAKF,YAGZmG,EAAYD,EAAK1E,KAA0Ca,IAAIX,GAGrE,QAAiBH,IAAb4E,GAA0BjG,KAAKK,OAAyB4F,EAAU,CACpE,MAAMxE,EAAUuE,EAAKE,mBAAmBD,GAClCzG,EACyB,mBAAtBiC,EAAQjC,UACX,CAACV,cAAe2C,EAAQjC,gBACa6B,KAApB,QAAjBX,EAAAe,EAAQjC,iBAAS,IAAAkB,OAAA,EAAAA,EAAE5B,eACnB2C,EAAQjC,UACRnB,EAEN2B,KAAKK,KAAuB4F,EAC5BjG,KAAKiG,GAA0BzG,EAAUV,cACvCP,EACAkD,EAAQjD,MAIVwB,KAAKK,KAAuB,IAC7B,CACF,CAgBDgC,cACEb,EACAY,EACAX,GAEA,IAAI0E,GAAsB,OAEb9E,IAATG,MACFC,EACEA,GACCzB,KAAKF,YAAuCoG,mBAAmB1E,IACvC9B,YAAcP,GAC1Ba,KAAKwB,GAAqBY,IAClCpC,KAAKgE,KAAoBoC,IAAI5E,IAChCxB,KAAKgE,KAAoBzC,IAAIC,EAAMY,IAMb,IAApBX,EAAQhC,SAAoBO,KAAKK,OAAyBmB,SACxBH,IAAhCrB,KAAKqG,OACPrG,KAAKqG,KAAyB,IAAInG,KAEpCF,KAAKqG,KAAuB9E,IAAIC,EAAMC,KAIxC0E,GAAsB,IAGrBnG,KAAKG,iBAAmBgG,IAC3BnG,KAAK4D,KAAkB5D,KAAKsG,OAO/B,CAKOC,aACNvG,KAAKG,iBAAkB,EACvB,UAGQH,KAAK4D,IACZ,CAAC,MAAO1E,GAKP2E,QAAQ2C,OAAOtH,EAChB,CACD,MAAMuH,EAASzG,KAAK0G,iBAOpB,OAHc,MAAVD,SACIA,GAEAzG,KAAKG,eACd,CAmBSuG,iBACR,OAAO1G,KAAK2G,eACb,CAmBSA,sBAIR,IAAK3G,KAAKG,gBACR,OAIGH,KAAKI,WAyBNJ,KAAKC,OAIPD,KAAKC,KAAsBe,SAAQ,CAACC,EAAGC,IAAQlB,KAAakB,GAAKD,IACjEjB,KAAKC,UAAuBoB,GAE9B,IAAIuF,GAAe,EACnB,MAAMC,EAAoB7G,KAAKgE,KAC/B,IACE4C,EAAe5G,KAAK4G,aAAaC,GAC7BD,GACF5G,KAAK8G,WAAWD,GACE,QAAlBrC,EAAAxE,KAAKqE,YAAa,IAAAG,GAAAA,EAAExD,SAASqE,UAAM,eAAA3E,EAAA2E,EAAE0B,wCAAc,IACnD/G,KAAKgH,OAAOH,IAEZ7G,KAAKiH,MAER,CAAC,MAAO/H,GAMP,MAHA0H,GAAe,EAEf5G,KAAKiH,OACC/H,CACP,CAEG0H,GACF5G,KAAKkH,KAAYL,EAEpB,CAuBSC,WAAWK,GAA4C,CAIjED,KAAYL,SACQ,QAAlBnG,EAAAV,KAAKqE,YAAa,IAAA3D,GAAAA,EAAEM,SAASqE,UAAM,eAAA3E,EAAA2E,EAAE+B,yCAAe,IAC/CpH,KAAKI,aACRJ,KAAKI,YAAa,EAClBJ,KAAKqH,aAAaR,IAEpB7G,KAAKsH,QAAQT,EAiBd,CAEOI,OACNjH,KAAKgE,KAAsB,IAAI9D,IAC/BF,KAAKG,iBAAkB,CACxB,CAkBGoH,qBACF,OAAOvH,KAAKwH,mBACb,CAyBSA,oBACR,OAAOxH,KAAK4D,IACb,CAUSgD,aAAaO,GACrB,OAAO,CACR,CAWSH,OAAOG,QACqB9F,IAAhCrB,KAAKqG,OAGPrG,KAAKqG,KAAuBrF,SAAQ,CAACC,EAAGwG,IACtCzH,KAAK4F,KAAsB6B,EAAGzH,KAAKyH,GAAkBxG,KAEvDjB,KAAKqG,UAAyBhF,GAEhCrB,KAAKiH,MACN,CAYSK,QAAQH,GAAsC,CAkB9CE,aAAaF,GAAsC,EA99B5CvH,EAACD,IAAa,EAQxBC,EAAAmB,kBAA4C,IAAIb,IAmChDN,EAAaoD,cAA6B,GAwQ1CpD,EAAAsF,kBAAoC,CAACwC,KAAM,QA+qBpDvJ,SAAAA,EAAkB,CAACyB,qBAoCY,QAA/B+H,EAAC7J,EAAO8J,+BAAuB,IAAAD,EAAAA,EAA9B7J,EAAO8J,wBAA4B,IAAIhH,KAAK"}