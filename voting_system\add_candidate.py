import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'voting_system.settings')
django.setup()

from django.contrib.auth.models import User
from voting.models import Party, Election, Candidate, County, Constituency

def add_candidate():
    try:
        # Get required objects
        party = Party.objects.get(name='Jubilee Party')
        election = Election.objects.get(title='2022 Presidential Election')
        county = County.objects.get(name='Nairobi')
        constituency = Constituency.objects.get(name='Westlands')
        
        # Create or get user
        user, created = User.objects.get_or_create(
            username='johndoe',
            defaults={
                'email': '<EMAIL>',
                'first_name': '<PERSON>',
                'last_name': '<PERSON><PERSON>'
            }
        )
        
        if created:
            user.set_password('password123')
            user.save()
            print("Created user <PERSON>")
        else:
            print("User <PERSON> already exists")
        
        # Create candidate
        candidate, created = Candidate.objects.get_or_create(
            user=user,
            election=election,
            defaults={
                'party': party,
                'county': county,
                'constituency': constituency,
                'bio': 'Experienced leader with a vision for Kenya',
                'is_approved': True,
                'votes_count': 0
            }
        )
        
        if created:
            print("Created candidate <PERSON>")
        else:
            print("Candidate <PERSON> <PERSON>e already exists")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    add_candidate()
