!function(t,r){"object"==typeof exports&&"object"==typeof module?module.exports=r():"function"==typeof define&&define.amd?define("WalletConnectSocketTransport",[],r):"object"==typeof exports?exports.WalletConnectSocketTransport=r():t.WalletConnectSocketTransport=r()}(this,(function(){return function(t){var r={};function n(e){if(r[e])return r[e].exports;var i=r[e]={i:e,l:!1,exports:{}};return t[e].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=t,n.c=r,n.d=function(t,r,e){n.o(t,r)||Object.defineProperty(t,r,{enumerable:!0,get:e})},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,r){if(1&r&&(t=n(t)),8&r)return t;if(4&r&&"object"==typeof t&&t&&t.__esModule)return t;var e=Object.create(null);if(n.r(e),Object.defineProperty(e,"default",{enumerable:!0,value:t}),2&r&&"string"!=typeof t)for(var i in t)n.d(e,i,function(r){return t[r]}.bind(null,i));return e},n.n=function(t){var r=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(r,"a",r),r},n.o=function(t,r){return Object.prototype.hasOwnProperty.call(t,r)},n.p="",n(n.s=27)}([function(t,r,n){"use strict";(function(t){n.d(r,"f",(function(){return s})),n.d(r,"g",(function(){return a})),n.d(r,"i",(function(){return h})),n.d(r,"h",(function(){return f})),n.d(r,"b",(function(){return c})),n.d(r,"c",(function(){return l})),n.d(r,"e",(function(){return d})),n.d(r,"d",(function(){return p})),n.d(r,"n",(function(){return m})),n.d(r,"m",(function(){return g})),n.d(r,"o",(function(){return y})),n.d(r,"z",(function(){return v})),n.d(r,"y",(function(){return w})),n.d(r,"A",(function(){return M})),n.d(r,"u",(function(){return b})),n.d(r,"t",(function(){return _})),n.d(r,"r",(function(){return O})),n.d(r,"q",(function(){return E})),n.d(r,"s",(function(){return k})),n.d(r,"p",(function(){return x})),n.d(r,"l",(function(){return T})),n.d(r,"k",(function(){return R})),n.d(r,"j",(function(){return B})),n.d(r,"w",(function(){return I})),n.d(r,"a",(function(){return j})),n.d(r,"x",(function(){return L})),n.d(r,"v",(function(){return N}));var e=n(9),i=n.n(e),o=n(24),u=n.n(o);function s(t){return new Uint8Array(t)}function a(t,r=!1){const n=t.toString("hex");return r?j(n):n}function h(t){return t.toString("utf8")}function f(t){return t.readUIntBE(0,t.length)}function c(t){return u()(t)}function l(t,r=!1){return a(c(t),r)}function d(t){return h(c(t))}function p(t){return f(c(t))}function m(r){return t.from(I(r),"hex")}function g(t){return s(m(t))}function y(t){return h(m(t))}function v(r){return t.from(r,"utf8")}function w(t){return s(v(t))}function M(t,r=!1){return a(v(t),r)}function b(t){return c(S(A(t)))}function _(t){return S(A(t))}function A(t){return C((t>>>0).toString(2))}function S(t){return new Uint8Array(P(t).map(t=>parseInt(t,2)))}function O(t,r){return!("string"!=typeof t||!t.match(/^0x[0-9A-Fa-f]*$/))&&(!r||t.length===2+2*r)}function E(r){return t.isBuffer(r)}function k(t){return i.a.strict(t)&&!E(t)}function x(t){return!k(t)&&!E(t)&&void 0!==t.byteLength}function T(t){return E(t)?"buffer":k(t)?"typed-array":x(t)?"array-buffer":Array.isArray(t)?"array":typeof t}function R(t){return function(t){return!("string"!=typeof t||!new RegExp(/^[01]+$/).test(t))&&t.length%8==0}(t)?"binary":O(t)?"hex":"utf8"}function B(...r){return t.concat(r)}function P(t,r=8){const n=C(t).match(new RegExp(`.{${r}}`,"gi"));return Array.from(n||[])}function C(t,r=8,n="0"){return function(t,r,n="0"){return U(t,r,!0,n)}(t,function(t,r=8){const n=t%r;return n?(t-n)/r*r+r:t}(t.length,r),n)}function I(t){return t.replace(/^0x/,"")}function j(t){return t.startsWith("0x")?t:"0x"+t}function L(t){return(t=C(t=I(t),2))&&(t=j(t)),t}function N(t){const r=t.startsWith("0x");return t=(t=I(t)).startsWith("0")?t.substring(1):t,r?j(t):t}function U(t,r,n,e="0"){const i=r-t.length;let o=t;if(i>0){const r=e.repeat(i);o=n?r+t:t+r}return o}}).call(this,n(11).Buffer)},function(t,r,n){"use strict";n.d(r,"b",(function(){return e})),n.d(r,"d",(function(){return i})),n.d(r,"c",(function(){return o})),n.d(r,"e",(function(){return u})),n.d(r,"f",(function(){return s})),n.d(r,"a",(function(){return a}));const e="INTERNAL_ERROR",i="SERVER_ERROR",o=[-32700,-32600,-32601,-32602,-32603],u=[-32e3,-32099],s={PARSE_ERROR:{code:-32700,message:"Parse error"},INVALID_REQUEST:{code:-32600,message:"Invalid Request"},METHOD_NOT_FOUND:{code:-32601,message:"Method not found"},INVALID_PARAMS:{code:-32602,message:"Invalid params"},[e]:{code:-32603,message:"Internal error"},[i]:{code:-32e3,message:"Server error"}},a=i},function(t,r,n){"use strict";function e(t){let r=void 0;return"undefined"!=typeof window&&void 0!==window[t]&&(r=window[t]),r}function i(t){const r=e(t);if(!r)throw new Error(t+" is not defined in Window");return r}Object.defineProperty(r,"__esModule",{value:!0}),r.getLocalStorage=r.getLocalStorageOrThrow=r.getCrypto=r.getCryptoOrThrow=r.getLocation=r.getLocationOrThrow=r.getNavigator=r.getNavigatorOrThrow=r.getDocument=r.getDocumentOrThrow=r.getFromWindowOrThrow=r.getFromWindow=void 0,r.getFromWindow=e,r.getFromWindowOrThrow=i,r.getDocumentOrThrow=function(){return i("document")},r.getDocument=function(){return e("document")},r.getNavigatorOrThrow=function(){return i("navigator")},r.getNavigator=function(){return e("navigator")},r.getLocationOrThrow=function(){return i("location")},r.getLocation=function(){return e("location")},r.getCryptoOrThrow=function(){return i("crypto")},r.getCrypto=function(){return e("crypto")},r.getLocalStorageOrThrow=function(){return i("localStorage")},r.getLocalStorage=function(){return e("localStorage")}},function(t,r,n){"use strict";n.d(r,"c",(function(){return i})),n.d(r,"a",(function(){return o})),n.d(r,"b",(function(){return u}));var e=n(1);function i(t){return e.c.includes(t)}function o(t){return Object.keys(e.f).includes(t)?e.f[t]:e.f[e.a]}function u(t){const r=Object.values(e.f).find(r=>r.code===t);return r||e.f[e.a]}},function(t,r,n){"use strict";n.d(r,"a",(function(){return e}));class e{}},function(t,r,n){(function(t){!function(t,r){"use strict";function e(t,r){if(!t)throw new Error(r||"Assertion failed")}function i(t,r){t.super_=r;var n=function(){};n.prototype=r.prototype,t.prototype=new n,t.prototype.constructor=t}function o(t,r,n){if(o.isBN(t))return t;this.negative=0,this.words=null,this.length=0,this.red=null,null!==t&&("le"!==r&&"be"!==r||(n=r,r=10),this._init(t||0,r||10,n||"be"))}var u;"object"==typeof t?t.exports=o:r.BN=o,o.BN=o,o.wordSize=26;try{u=n(30).Buffer}catch(t){}function s(t,r,n){for(var e=0,i=Math.min(t.length,n),o=r;o<i;o++){var u=t.charCodeAt(o)-48;e<<=4,e|=u>=49&&u<=54?u-49+10:u>=17&&u<=22?u-17+10:15&u}return e}function a(t,r,n,e){for(var i=0,o=Math.min(t.length,n),u=r;u<o;u++){var s=t.charCodeAt(u)-48;i*=e,i+=s>=49?s-49+10:s>=17?s-17+10:s}return i}o.isBN=function(t){return t instanceof o||null!==t&&"object"==typeof t&&t.constructor.wordSize===o.wordSize&&Array.isArray(t.words)},o.max=function(t,r){return t.cmp(r)>0?t:r},o.min=function(t,r){return t.cmp(r)<0?t:r},o.prototype._init=function(t,r,n){if("number"==typeof t)return this._initNumber(t,r,n);if("object"==typeof t)return this._initArray(t,r,n);"hex"===r&&(r=16),e(r===(0|r)&&r>=2&&r<=36);var i=0;"-"===(t=t.toString().replace(/\s+/g,""))[0]&&i++,16===r?this._parseHex(t,i):this._parseBase(t,r,i),"-"===t[0]&&(this.negative=1),this.strip(),"le"===n&&this._initArray(this.toArray(),r,n)},o.prototype._initNumber=function(t,r,n){t<0&&(this.negative=1,t=-t),t<67108864?(this.words=[67108863&t],this.length=1):t<4503599627370496?(this.words=[67108863&t,t/67108864&67108863],this.length=2):(e(t<9007199254740992),this.words=[67108863&t,t/67108864&67108863,1],this.length=3),"le"===n&&this._initArray(this.toArray(),r,n)},o.prototype._initArray=function(t,r,n){if(e("number"==typeof t.length),t.length<=0)return this.words=[0],this.length=1,this;this.length=Math.ceil(t.length/3),this.words=new Array(this.length);for(var i=0;i<this.length;i++)this.words[i]=0;var o,u,s=0;if("be"===n)for(i=t.length-1,o=0;i>=0;i-=3)u=t[i]|t[i-1]<<8|t[i-2]<<16,this.words[o]|=u<<s&67108863,this.words[o+1]=u>>>26-s&67108863,(s+=24)>=26&&(s-=26,o++);else if("le"===n)for(i=0,o=0;i<t.length;i+=3)u=t[i]|t[i+1]<<8|t[i+2]<<16,this.words[o]|=u<<s&67108863,this.words[o+1]=u>>>26-s&67108863,(s+=24)>=26&&(s-=26,o++);return this.strip()},o.prototype._parseHex=function(t,r){this.length=Math.ceil((t.length-r)/6),this.words=new Array(this.length);for(var n=0;n<this.length;n++)this.words[n]=0;var e,i,o=0;for(n=t.length-6,e=0;n>=r;n-=6)i=s(t,n,n+6),this.words[e]|=i<<o&67108863,this.words[e+1]|=i>>>26-o&4194303,(o+=24)>=26&&(o-=26,e++);n+6!==r&&(i=s(t,r,n+6),this.words[e]|=i<<o&67108863,this.words[e+1]|=i>>>26-o&4194303),this.strip()},o.prototype._parseBase=function(t,r,n){this.words=[0],this.length=1;for(var e=0,i=1;i<=67108863;i*=r)e++;e--,i=i/r|0;for(var o=t.length-n,u=o%e,s=Math.min(o,o-u)+n,h=0,f=n;f<s;f+=e)h=a(t,f,f+e,r),this.imuln(i),this.words[0]+h<67108864?this.words[0]+=h:this._iaddn(h);if(0!==u){var c=1;for(h=a(t,f,t.length,r),f=0;f<u;f++)c*=r;this.imuln(c),this.words[0]+h<67108864?this.words[0]+=h:this._iaddn(h)}},o.prototype.copy=function(t){t.words=new Array(this.length);for(var r=0;r<this.length;r++)t.words[r]=this.words[r];t.length=this.length,t.negative=this.negative,t.red=this.red},o.prototype.clone=function(){var t=new o(null);return this.copy(t),t},o.prototype._expand=function(t){for(;this.length<t;)this.words[this.length++]=0;return this},o.prototype.strip=function(){for(;this.length>1&&0===this.words[this.length-1];)this.length--;return this._normSign()},o.prototype._normSign=function(){return 1===this.length&&0===this.words[0]&&(this.negative=0),this},o.prototype.inspect=function(){return(this.red?"<BN-R: ":"<BN: ")+this.toString(16)+">"};var h=["","0","00","000","0000","00000","000000","0000000","00000000","000000000","0000000000","00000000000","000000000000","0000000000000","00000000000000","000000000000000","0000000000000000","00000000000000000","000000000000000000","0000000000000000000","00000000000000000000","000000000000000000000","0000000000000000000000","00000000000000000000000","000000000000000000000000","0000000000000000000000000"],f=[0,0,25,16,12,11,10,9,8,8,7,7,7,7,6,6,6,6,6,6,6,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5],c=[0,0,33554432,43046721,16777216,48828125,60466176,40353607,16777216,43046721,1e7,19487171,35831808,62748517,7529536,11390625,16777216,24137569,34012224,47045881,64e6,4084101,5153632,6436343,7962624,9765625,11881376,14348907,17210368,20511149,243e5,28629151,33554432,39135393,45435424,52521875,60466176];function l(t,r,n){n.negative=r.negative^t.negative;var e=t.length+r.length|0;n.length=e,e=e-1|0;var i=0|t.words[0],o=0|r.words[0],u=i*o,s=67108863&u,a=u/67108864|0;n.words[0]=s;for(var h=1;h<e;h++){for(var f=a>>>26,c=67108863&a,l=Math.min(h,r.length-1),d=Math.max(0,h-t.length+1);d<=l;d++){var p=h-d|0;f+=(u=(i=0|t.words[p])*(o=0|r.words[d])+c)/67108864|0,c=67108863&u}n.words[h]=0|c,a=0|f}return 0!==a?n.words[h]=0|a:n.length--,n.strip()}o.prototype.toString=function(t,r){var n;if(r=0|r||1,16===(t=t||10)||"hex"===t){n="";for(var i=0,o=0,u=0;u<this.length;u++){var s=this.words[u],a=(16777215&(s<<i|o)).toString(16);n=0!==(o=s>>>24-i&16777215)||u!==this.length-1?h[6-a.length]+a+n:a+n,(i+=2)>=26&&(i-=26,u--)}for(0!==o&&(n=o.toString(16)+n);n.length%r!=0;)n="0"+n;return 0!==this.negative&&(n="-"+n),n}if(t===(0|t)&&t>=2&&t<=36){var l=f[t],d=c[t];n="";var p=this.clone();for(p.negative=0;!p.isZero();){var m=p.modn(d).toString(t);n=(p=p.idivn(d)).isZero()?m+n:h[l-m.length]+m+n}for(this.isZero()&&(n="0"+n);n.length%r!=0;)n="0"+n;return 0!==this.negative&&(n="-"+n),n}e(!1,"Base should be between 2 and 36")},o.prototype.toNumber=function(){var t=this.words[0];return 2===this.length?t+=67108864*this.words[1]:3===this.length&&1===this.words[2]?t+=4503599627370496+67108864*this.words[1]:this.length>2&&e(!1,"Number can only safely store up to 53 bits"),0!==this.negative?-t:t},o.prototype.toJSON=function(){return this.toString(16)},o.prototype.toBuffer=function(t,r){return e(void 0!==u),this.toArrayLike(u,t,r)},o.prototype.toArray=function(t,r){return this.toArrayLike(Array,t,r)},o.prototype.toArrayLike=function(t,r,n){var i=this.byteLength(),o=n||Math.max(1,i);e(i<=o,"byte array longer than desired length"),e(o>0,"Requested array length <= 0"),this.strip();var u,s,a="le"===r,h=new t(o),f=this.clone();if(a){for(s=0;!f.isZero();s++)u=f.andln(255),f.iushrn(8),h[s]=u;for(;s<o;s++)h[s]=0}else{for(s=0;s<o-i;s++)h[s]=0;for(s=0;!f.isZero();s++)u=f.andln(255),f.iushrn(8),h[o-s-1]=u}return h},Math.clz32?o.prototype._countBits=function(t){return 32-Math.clz32(t)}:o.prototype._countBits=function(t){var r=t,n=0;return r>=4096&&(n+=13,r>>>=13),r>=64&&(n+=7,r>>>=7),r>=8&&(n+=4,r>>>=4),r>=2&&(n+=2,r>>>=2),n+r},o.prototype._zeroBits=function(t){if(0===t)return 26;var r=t,n=0;return 0==(8191&r)&&(n+=13,r>>>=13),0==(127&r)&&(n+=7,r>>>=7),0==(15&r)&&(n+=4,r>>>=4),0==(3&r)&&(n+=2,r>>>=2),0==(1&r)&&n++,n},o.prototype.bitLength=function(){var t=this.words[this.length-1],r=this._countBits(t);return 26*(this.length-1)+r},o.prototype.zeroBits=function(){if(this.isZero())return 0;for(var t=0,r=0;r<this.length;r++){var n=this._zeroBits(this.words[r]);if(t+=n,26!==n)break}return t},o.prototype.byteLength=function(){return Math.ceil(this.bitLength()/8)},o.prototype.toTwos=function(t){return 0!==this.negative?this.abs().inotn(t).iaddn(1):this.clone()},o.prototype.fromTwos=function(t){return this.testn(t-1)?this.notn(t).iaddn(1).ineg():this.clone()},o.prototype.isNeg=function(){return 0!==this.negative},o.prototype.neg=function(){return this.clone().ineg()},o.prototype.ineg=function(){return this.isZero()||(this.negative^=1),this},o.prototype.iuor=function(t){for(;this.length<t.length;)this.words[this.length++]=0;for(var r=0;r<t.length;r++)this.words[r]=this.words[r]|t.words[r];return this.strip()},o.prototype.ior=function(t){return e(0==(this.negative|t.negative)),this.iuor(t)},o.prototype.or=function(t){return this.length>t.length?this.clone().ior(t):t.clone().ior(this)},o.prototype.uor=function(t){return this.length>t.length?this.clone().iuor(t):t.clone().iuor(this)},o.prototype.iuand=function(t){var r;r=this.length>t.length?t:this;for(var n=0;n<r.length;n++)this.words[n]=this.words[n]&t.words[n];return this.length=r.length,this.strip()},o.prototype.iand=function(t){return e(0==(this.negative|t.negative)),this.iuand(t)},o.prototype.and=function(t){return this.length>t.length?this.clone().iand(t):t.clone().iand(this)},o.prototype.uand=function(t){return this.length>t.length?this.clone().iuand(t):t.clone().iuand(this)},o.prototype.iuxor=function(t){var r,n;this.length>t.length?(r=this,n=t):(r=t,n=this);for(var e=0;e<n.length;e++)this.words[e]=r.words[e]^n.words[e];if(this!==r)for(;e<r.length;e++)this.words[e]=r.words[e];return this.length=r.length,this.strip()},o.prototype.ixor=function(t){return e(0==(this.negative|t.negative)),this.iuxor(t)},o.prototype.xor=function(t){return this.length>t.length?this.clone().ixor(t):t.clone().ixor(this)},o.prototype.uxor=function(t){return this.length>t.length?this.clone().iuxor(t):t.clone().iuxor(this)},o.prototype.inotn=function(t){e("number"==typeof t&&t>=0);var r=0|Math.ceil(t/26),n=t%26;this._expand(r),n>0&&r--;for(var i=0;i<r;i++)this.words[i]=67108863&~this.words[i];return n>0&&(this.words[i]=~this.words[i]&67108863>>26-n),this.strip()},o.prototype.notn=function(t){return this.clone().inotn(t)},o.prototype.setn=function(t,r){e("number"==typeof t&&t>=0);var n=t/26|0,i=t%26;return this._expand(n+1),this.words[n]=r?this.words[n]|1<<i:this.words[n]&~(1<<i),this.strip()},o.prototype.iadd=function(t){var r,n,e;if(0!==this.negative&&0===t.negative)return this.negative=0,r=this.isub(t),this.negative^=1,this._normSign();if(0===this.negative&&0!==t.negative)return t.negative=0,r=this.isub(t),t.negative=1,r._normSign();this.length>t.length?(n=this,e=t):(n=t,e=this);for(var i=0,o=0;o<e.length;o++)r=(0|n.words[o])+(0|e.words[o])+i,this.words[o]=67108863&r,i=r>>>26;for(;0!==i&&o<n.length;o++)r=(0|n.words[o])+i,this.words[o]=67108863&r,i=r>>>26;if(this.length=n.length,0!==i)this.words[this.length]=i,this.length++;else if(n!==this)for(;o<n.length;o++)this.words[o]=n.words[o];return this},o.prototype.add=function(t){var r;return 0!==t.negative&&0===this.negative?(t.negative=0,r=this.sub(t),t.negative^=1,r):0===t.negative&&0!==this.negative?(this.negative=0,r=t.sub(this),this.negative=1,r):this.length>t.length?this.clone().iadd(t):t.clone().iadd(this)},o.prototype.isub=function(t){if(0!==t.negative){t.negative=0;var r=this.iadd(t);return t.negative=1,r._normSign()}if(0!==this.negative)return this.negative=0,this.iadd(t),this.negative=1,this._normSign();var n,e,i=this.cmp(t);if(0===i)return this.negative=0,this.length=1,this.words[0]=0,this;i>0?(n=this,e=t):(n=t,e=this);for(var o=0,u=0;u<e.length;u++)o=(r=(0|n.words[u])-(0|e.words[u])+o)>>26,this.words[u]=67108863&r;for(;0!==o&&u<n.length;u++)o=(r=(0|n.words[u])+o)>>26,this.words[u]=67108863&r;if(0===o&&u<n.length&&n!==this)for(;u<n.length;u++)this.words[u]=n.words[u];return this.length=Math.max(this.length,u),n!==this&&(this.negative=1),this.strip()},o.prototype.sub=function(t){return this.clone().isub(t)};var d=function(t,r,n){var e,i,o,u=t.words,s=r.words,a=n.words,h=0,f=0|u[0],c=8191&f,l=f>>>13,d=0|u[1],p=8191&d,m=d>>>13,g=0|u[2],y=8191&g,v=g>>>13,w=0|u[3],M=8191&w,b=w>>>13,_=0|u[4],A=8191&_,S=_>>>13,O=0|u[5],E=8191&O,k=O>>>13,x=0|u[6],T=8191&x,R=x>>>13,B=0|u[7],P=8191&B,C=B>>>13,I=0|u[8],j=8191&I,L=I>>>13,N=0|u[9],U=8191&N,W=N>>>13,D=0|s[0],F=8191&D,Y=D>>>13,q=0|s[1],z=8191&q,$=q>>>13,Z=0|s[2],H=8191&Z,J=Z>>>13,V=0|s[3],K=8191&V,Q=V>>>13,X=0|s[4],G=8191&X,tt=X>>>13,rt=0|s[5],nt=8191&rt,et=rt>>>13,it=0|s[6],ot=8191&it,ut=it>>>13,st=0|s[7],at=8191&st,ht=st>>>13,ft=0|s[8],ct=8191&ft,lt=ft>>>13,dt=0|s[9],pt=8191&dt,mt=dt>>>13;n.negative=t.negative^r.negative,n.length=19;var gt=(h+(e=Math.imul(c,F))|0)+((8191&(i=(i=Math.imul(c,Y))+Math.imul(l,F)|0))<<13)|0;h=((o=Math.imul(l,Y))+(i>>>13)|0)+(gt>>>26)|0,gt&=67108863,e=Math.imul(p,F),i=(i=Math.imul(p,Y))+Math.imul(m,F)|0,o=Math.imul(m,Y);var yt=(h+(e=e+Math.imul(c,z)|0)|0)+((8191&(i=(i=i+Math.imul(c,$)|0)+Math.imul(l,z)|0))<<13)|0;h=((o=o+Math.imul(l,$)|0)+(i>>>13)|0)+(yt>>>26)|0,yt&=67108863,e=Math.imul(y,F),i=(i=Math.imul(y,Y))+Math.imul(v,F)|0,o=Math.imul(v,Y),e=e+Math.imul(p,z)|0,i=(i=i+Math.imul(p,$)|0)+Math.imul(m,z)|0,o=o+Math.imul(m,$)|0;var vt=(h+(e=e+Math.imul(c,H)|0)|0)+((8191&(i=(i=i+Math.imul(c,J)|0)+Math.imul(l,H)|0))<<13)|0;h=((o=o+Math.imul(l,J)|0)+(i>>>13)|0)+(vt>>>26)|0,vt&=67108863,e=Math.imul(M,F),i=(i=Math.imul(M,Y))+Math.imul(b,F)|0,o=Math.imul(b,Y),e=e+Math.imul(y,z)|0,i=(i=i+Math.imul(y,$)|0)+Math.imul(v,z)|0,o=o+Math.imul(v,$)|0,e=e+Math.imul(p,H)|0,i=(i=i+Math.imul(p,J)|0)+Math.imul(m,H)|0,o=o+Math.imul(m,J)|0;var wt=(h+(e=e+Math.imul(c,K)|0)|0)+((8191&(i=(i=i+Math.imul(c,Q)|0)+Math.imul(l,K)|0))<<13)|0;h=((o=o+Math.imul(l,Q)|0)+(i>>>13)|0)+(wt>>>26)|0,wt&=67108863,e=Math.imul(A,F),i=(i=Math.imul(A,Y))+Math.imul(S,F)|0,o=Math.imul(S,Y),e=e+Math.imul(M,z)|0,i=(i=i+Math.imul(M,$)|0)+Math.imul(b,z)|0,o=o+Math.imul(b,$)|0,e=e+Math.imul(y,H)|0,i=(i=i+Math.imul(y,J)|0)+Math.imul(v,H)|0,o=o+Math.imul(v,J)|0,e=e+Math.imul(p,K)|0,i=(i=i+Math.imul(p,Q)|0)+Math.imul(m,K)|0,o=o+Math.imul(m,Q)|0;var Mt=(h+(e=e+Math.imul(c,G)|0)|0)+((8191&(i=(i=i+Math.imul(c,tt)|0)+Math.imul(l,G)|0))<<13)|0;h=((o=o+Math.imul(l,tt)|0)+(i>>>13)|0)+(Mt>>>26)|0,Mt&=67108863,e=Math.imul(E,F),i=(i=Math.imul(E,Y))+Math.imul(k,F)|0,o=Math.imul(k,Y),e=e+Math.imul(A,z)|0,i=(i=i+Math.imul(A,$)|0)+Math.imul(S,z)|0,o=o+Math.imul(S,$)|0,e=e+Math.imul(M,H)|0,i=(i=i+Math.imul(M,J)|0)+Math.imul(b,H)|0,o=o+Math.imul(b,J)|0,e=e+Math.imul(y,K)|0,i=(i=i+Math.imul(y,Q)|0)+Math.imul(v,K)|0,o=o+Math.imul(v,Q)|0,e=e+Math.imul(p,G)|0,i=(i=i+Math.imul(p,tt)|0)+Math.imul(m,G)|0,o=o+Math.imul(m,tt)|0;var bt=(h+(e=e+Math.imul(c,nt)|0)|0)+((8191&(i=(i=i+Math.imul(c,et)|0)+Math.imul(l,nt)|0))<<13)|0;h=((o=o+Math.imul(l,et)|0)+(i>>>13)|0)+(bt>>>26)|0,bt&=67108863,e=Math.imul(T,F),i=(i=Math.imul(T,Y))+Math.imul(R,F)|0,o=Math.imul(R,Y),e=e+Math.imul(E,z)|0,i=(i=i+Math.imul(E,$)|0)+Math.imul(k,z)|0,o=o+Math.imul(k,$)|0,e=e+Math.imul(A,H)|0,i=(i=i+Math.imul(A,J)|0)+Math.imul(S,H)|0,o=o+Math.imul(S,J)|0,e=e+Math.imul(M,K)|0,i=(i=i+Math.imul(M,Q)|0)+Math.imul(b,K)|0,o=o+Math.imul(b,Q)|0,e=e+Math.imul(y,G)|0,i=(i=i+Math.imul(y,tt)|0)+Math.imul(v,G)|0,o=o+Math.imul(v,tt)|0,e=e+Math.imul(p,nt)|0,i=(i=i+Math.imul(p,et)|0)+Math.imul(m,nt)|0,o=o+Math.imul(m,et)|0;var _t=(h+(e=e+Math.imul(c,ot)|0)|0)+((8191&(i=(i=i+Math.imul(c,ut)|0)+Math.imul(l,ot)|0))<<13)|0;h=((o=o+Math.imul(l,ut)|0)+(i>>>13)|0)+(_t>>>26)|0,_t&=67108863,e=Math.imul(P,F),i=(i=Math.imul(P,Y))+Math.imul(C,F)|0,o=Math.imul(C,Y),e=e+Math.imul(T,z)|0,i=(i=i+Math.imul(T,$)|0)+Math.imul(R,z)|0,o=o+Math.imul(R,$)|0,e=e+Math.imul(E,H)|0,i=(i=i+Math.imul(E,J)|0)+Math.imul(k,H)|0,o=o+Math.imul(k,J)|0,e=e+Math.imul(A,K)|0,i=(i=i+Math.imul(A,Q)|0)+Math.imul(S,K)|0,o=o+Math.imul(S,Q)|0,e=e+Math.imul(M,G)|0,i=(i=i+Math.imul(M,tt)|0)+Math.imul(b,G)|0,o=o+Math.imul(b,tt)|0,e=e+Math.imul(y,nt)|0,i=(i=i+Math.imul(y,et)|0)+Math.imul(v,nt)|0,o=o+Math.imul(v,et)|0,e=e+Math.imul(p,ot)|0,i=(i=i+Math.imul(p,ut)|0)+Math.imul(m,ot)|0,o=o+Math.imul(m,ut)|0;var At=(h+(e=e+Math.imul(c,at)|0)|0)+((8191&(i=(i=i+Math.imul(c,ht)|0)+Math.imul(l,at)|0))<<13)|0;h=((o=o+Math.imul(l,ht)|0)+(i>>>13)|0)+(At>>>26)|0,At&=67108863,e=Math.imul(j,F),i=(i=Math.imul(j,Y))+Math.imul(L,F)|0,o=Math.imul(L,Y),e=e+Math.imul(P,z)|0,i=(i=i+Math.imul(P,$)|0)+Math.imul(C,z)|0,o=o+Math.imul(C,$)|0,e=e+Math.imul(T,H)|0,i=(i=i+Math.imul(T,J)|0)+Math.imul(R,H)|0,o=o+Math.imul(R,J)|0,e=e+Math.imul(E,K)|0,i=(i=i+Math.imul(E,Q)|0)+Math.imul(k,K)|0,o=o+Math.imul(k,Q)|0,e=e+Math.imul(A,G)|0,i=(i=i+Math.imul(A,tt)|0)+Math.imul(S,G)|0,o=o+Math.imul(S,tt)|0,e=e+Math.imul(M,nt)|0,i=(i=i+Math.imul(M,et)|0)+Math.imul(b,nt)|0,o=o+Math.imul(b,et)|0,e=e+Math.imul(y,ot)|0,i=(i=i+Math.imul(y,ut)|0)+Math.imul(v,ot)|0,o=o+Math.imul(v,ut)|0,e=e+Math.imul(p,at)|0,i=(i=i+Math.imul(p,ht)|0)+Math.imul(m,at)|0,o=o+Math.imul(m,ht)|0;var St=(h+(e=e+Math.imul(c,ct)|0)|0)+((8191&(i=(i=i+Math.imul(c,lt)|0)+Math.imul(l,ct)|0))<<13)|0;h=((o=o+Math.imul(l,lt)|0)+(i>>>13)|0)+(St>>>26)|0,St&=67108863,e=Math.imul(U,F),i=(i=Math.imul(U,Y))+Math.imul(W,F)|0,o=Math.imul(W,Y),e=e+Math.imul(j,z)|0,i=(i=i+Math.imul(j,$)|0)+Math.imul(L,z)|0,o=o+Math.imul(L,$)|0,e=e+Math.imul(P,H)|0,i=(i=i+Math.imul(P,J)|0)+Math.imul(C,H)|0,o=o+Math.imul(C,J)|0,e=e+Math.imul(T,K)|0,i=(i=i+Math.imul(T,Q)|0)+Math.imul(R,K)|0,o=o+Math.imul(R,Q)|0,e=e+Math.imul(E,G)|0,i=(i=i+Math.imul(E,tt)|0)+Math.imul(k,G)|0,o=o+Math.imul(k,tt)|0,e=e+Math.imul(A,nt)|0,i=(i=i+Math.imul(A,et)|0)+Math.imul(S,nt)|0,o=o+Math.imul(S,et)|0,e=e+Math.imul(M,ot)|0,i=(i=i+Math.imul(M,ut)|0)+Math.imul(b,ot)|0,o=o+Math.imul(b,ut)|0,e=e+Math.imul(y,at)|0,i=(i=i+Math.imul(y,ht)|0)+Math.imul(v,at)|0,o=o+Math.imul(v,ht)|0,e=e+Math.imul(p,ct)|0,i=(i=i+Math.imul(p,lt)|0)+Math.imul(m,ct)|0,o=o+Math.imul(m,lt)|0;var Ot=(h+(e=e+Math.imul(c,pt)|0)|0)+((8191&(i=(i=i+Math.imul(c,mt)|0)+Math.imul(l,pt)|0))<<13)|0;h=((o=o+Math.imul(l,mt)|0)+(i>>>13)|0)+(Ot>>>26)|0,Ot&=67108863,e=Math.imul(U,z),i=(i=Math.imul(U,$))+Math.imul(W,z)|0,o=Math.imul(W,$),e=e+Math.imul(j,H)|0,i=(i=i+Math.imul(j,J)|0)+Math.imul(L,H)|0,o=o+Math.imul(L,J)|0,e=e+Math.imul(P,K)|0,i=(i=i+Math.imul(P,Q)|0)+Math.imul(C,K)|0,o=o+Math.imul(C,Q)|0,e=e+Math.imul(T,G)|0,i=(i=i+Math.imul(T,tt)|0)+Math.imul(R,G)|0,o=o+Math.imul(R,tt)|0,e=e+Math.imul(E,nt)|0,i=(i=i+Math.imul(E,et)|0)+Math.imul(k,nt)|0,o=o+Math.imul(k,et)|0,e=e+Math.imul(A,ot)|0,i=(i=i+Math.imul(A,ut)|0)+Math.imul(S,ot)|0,o=o+Math.imul(S,ut)|0,e=e+Math.imul(M,at)|0,i=(i=i+Math.imul(M,ht)|0)+Math.imul(b,at)|0,o=o+Math.imul(b,ht)|0,e=e+Math.imul(y,ct)|0,i=(i=i+Math.imul(y,lt)|0)+Math.imul(v,ct)|0,o=o+Math.imul(v,lt)|0;var Et=(h+(e=e+Math.imul(p,pt)|0)|0)+((8191&(i=(i=i+Math.imul(p,mt)|0)+Math.imul(m,pt)|0))<<13)|0;h=((o=o+Math.imul(m,mt)|0)+(i>>>13)|0)+(Et>>>26)|0,Et&=67108863,e=Math.imul(U,H),i=(i=Math.imul(U,J))+Math.imul(W,H)|0,o=Math.imul(W,J),e=e+Math.imul(j,K)|0,i=(i=i+Math.imul(j,Q)|0)+Math.imul(L,K)|0,o=o+Math.imul(L,Q)|0,e=e+Math.imul(P,G)|0,i=(i=i+Math.imul(P,tt)|0)+Math.imul(C,G)|0,o=o+Math.imul(C,tt)|0,e=e+Math.imul(T,nt)|0,i=(i=i+Math.imul(T,et)|0)+Math.imul(R,nt)|0,o=o+Math.imul(R,et)|0,e=e+Math.imul(E,ot)|0,i=(i=i+Math.imul(E,ut)|0)+Math.imul(k,ot)|0,o=o+Math.imul(k,ut)|0,e=e+Math.imul(A,at)|0,i=(i=i+Math.imul(A,ht)|0)+Math.imul(S,at)|0,o=o+Math.imul(S,ht)|0,e=e+Math.imul(M,ct)|0,i=(i=i+Math.imul(M,lt)|0)+Math.imul(b,ct)|0,o=o+Math.imul(b,lt)|0;var kt=(h+(e=e+Math.imul(y,pt)|0)|0)+((8191&(i=(i=i+Math.imul(y,mt)|0)+Math.imul(v,pt)|0))<<13)|0;h=((o=o+Math.imul(v,mt)|0)+(i>>>13)|0)+(kt>>>26)|0,kt&=67108863,e=Math.imul(U,K),i=(i=Math.imul(U,Q))+Math.imul(W,K)|0,o=Math.imul(W,Q),e=e+Math.imul(j,G)|0,i=(i=i+Math.imul(j,tt)|0)+Math.imul(L,G)|0,o=o+Math.imul(L,tt)|0,e=e+Math.imul(P,nt)|0,i=(i=i+Math.imul(P,et)|0)+Math.imul(C,nt)|0,o=o+Math.imul(C,et)|0,e=e+Math.imul(T,ot)|0,i=(i=i+Math.imul(T,ut)|0)+Math.imul(R,ot)|0,o=o+Math.imul(R,ut)|0,e=e+Math.imul(E,at)|0,i=(i=i+Math.imul(E,ht)|0)+Math.imul(k,at)|0,o=o+Math.imul(k,ht)|0,e=e+Math.imul(A,ct)|0,i=(i=i+Math.imul(A,lt)|0)+Math.imul(S,ct)|0,o=o+Math.imul(S,lt)|0;var xt=(h+(e=e+Math.imul(M,pt)|0)|0)+((8191&(i=(i=i+Math.imul(M,mt)|0)+Math.imul(b,pt)|0))<<13)|0;h=((o=o+Math.imul(b,mt)|0)+(i>>>13)|0)+(xt>>>26)|0,xt&=67108863,e=Math.imul(U,G),i=(i=Math.imul(U,tt))+Math.imul(W,G)|0,o=Math.imul(W,tt),e=e+Math.imul(j,nt)|0,i=(i=i+Math.imul(j,et)|0)+Math.imul(L,nt)|0,o=o+Math.imul(L,et)|0,e=e+Math.imul(P,ot)|0,i=(i=i+Math.imul(P,ut)|0)+Math.imul(C,ot)|0,o=o+Math.imul(C,ut)|0,e=e+Math.imul(T,at)|0,i=(i=i+Math.imul(T,ht)|0)+Math.imul(R,at)|0,o=o+Math.imul(R,ht)|0,e=e+Math.imul(E,ct)|0,i=(i=i+Math.imul(E,lt)|0)+Math.imul(k,ct)|0,o=o+Math.imul(k,lt)|0;var Tt=(h+(e=e+Math.imul(A,pt)|0)|0)+((8191&(i=(i=i+Math.imul(A,mt)|0)+Math.imul(S,pt)|0))<<13)|0;h=((o=o+Math.imul(S,mt)|0)+(i>>>13)|0)+(Tt>>>26)|0,Tt&=67108863,e=Math.imul(U,nt),i=(i=Math.imul(U,et))+Math.imul(W,nt)|0,o=Math.imul(W,et),e=e+Math.imul(j,ot)|0,i=(i=i+Math.imul(j,ut)|0)+Math.imul(L,ot)|0,o=o+Math.imul(L,ut)|0,e=e+Math.imul(P,at)|0,i=(i=i+Math.imul(P,ht)|0)+Math.imul(C,at)|0,o=o+Math.imul(C,ht)|0,e=e+Math.imul(T,ct)|0,i=(i=i+Math.imul(T,lt)|0)+Math.imul(R,ct)|0,o=o+Math.imul(R,lt)|0;var Rt=(h+(e=e+Math.imul(E,pt)|0)|0)+((8191&(i=(i=i+Math.imul(E,mt)|0)+Math.imul(k,pt)|0))<<13)|0;h=((o=o+Math.imul(k,mt)|0)+(i>>>13)|0)+(Rt>>>26)|0,Rt&=67108863,e=Math.imul(U,ot),i=(i=Math.imul(U,ut))+Math.imul(W,ot)|0,o=Math.imul(W,ut),e=e+Math.imul(j,at)|0,i=(i=i+Math.imul(j,ht)|0)+Math.imul(L,at)|0,o=o+Math.imul(L,ht)|0,e=e+Math.imul(P,ct)|0,i=(i=i+Math.imul(P,lt)|0)+Math.imul(C,ct)|0,o=o+Math.imul(C,lt)|0;var Bt=(h+(e=e+Math.imul(T,pt)|0)|0)+((8191&(i=(i=i+Math.imul(T,mt)|0)+Math.imul(R,pt)|0))<<13)|0;h=((o=o+Math.imul(R,mt)|0)+(i>>>13)|0)+(Bt>>>26)|0,Bt&=67108863,e=Math.imul(U,at),i=(i=Math.imul(U,ht))+Math.imul(W,at)|0,o=Math.imul(W,ht),e=e+Math.imul(j,ct)|0,i=(i=i+Math.imul(j,lt)|0)+Math.imul(L,ct)|0,o=o+Math.imul(L,lt)|0;var Pt=(h+(e=e+Math.imul(P,pt)|0)|0)+((8191&(i=(i=i+Math.imul(P,mt)|0)+Math.imul(C,pt)|0))<<13)|0;h=((o=o+Math.imul(C,mt)|0)+(i>>>13)|0)+(Pt>>>26)|0,Pt&=67108863,e=Math.imul(U,ct),i=(i=Math.imul(U,lt))+Math.imul(W,ct)|0,o=Math.imul(W,lt);var Ct=(h+(e=e+Math.imul(j,pt)|0)|0)+((8191&(i=(i=i+Math.imul(j,mt)|0)+Math.imul(L,pt)|0))<<13)|0;h=((o=o+Math.imul(L,mt)|0)+(i>>>13)|0)+(Ct>>>26)|0,Ct&=67108863;var It=(h+(e=Math.imul(U,pt))|0)+((8191&(i=(i=Math.imul(U,mt))+Math.imul(W,pt)|0))<<13)|0;return h=((o=Math.imul(W,mt))+(i>>>13)|0)+(It>>>26)|0,It&=67108863,a[0]=gt,a[1]=yt,a[2]=vt,a[3]=wt,a[4]=Mt,a[5]=bt,a[6]=_t,a[7]=At,a[8]=St,a[9]=Ot,a[10]=Et,a[11]=kt,a[12]=xt,a[13]=Tt,a[14]=Rt,a[15]=Bt,a[16]=Pt,a[17]=Ct,a[18]=It,0!==h&&(a[19]=h,n.length++),n};function p(t,r,n){return(new m).mulp(t,r,n)}function m(t,r){this.x=t,this.y=r}Math.imul||(d=l),o.prototype.mulTo=function(t,r){var n=this.length+t.length;return 10===this.length&&10===t.length?d(this,t,r):n<63?l(this,t,r):n<1024?function(t,r,n){n.negative=r.negative^t.negative,n.length=t.length+r.length;for(var e=0,i=0,o=0;o<n.length-1;o++){var u=i;i=0;for(var s=67108863&e,a=Math.min(o,r.length-1),h=Math.max(0,o-t.length+1);h<=a;h++){var f=o-h,c=(0|t.words[f])*(0|r.words[h]),l=67108863&c;s=67108863&(l=l+s|0),i+=(u=(u=u+(c/67108864|0)|0)+(l>>>26)|0)>>>26,u&=67108863}n.words[o]=s,e=u,u=i}return 0!==e?n.words[o]=e:n.length--,n.strip()}(this,t,r):p(this,t,r)},m.prototype.makeRBT=function(t){for(var r=new Array(t),n=o.prototype._countBits(t)-1,e=0;e<t;e++)r[e]=this.revBin(e,n,t);return r},m.prototype.revBin=function(t,r,n){if(0===t||t===n-1)return t;for(var e=0,i=0;i<r;i++)e|=(1&t)<<r-i-1,t>>=1;return e},m.prototype.permute=function(t,r,n,e,i,o){for(var u=0;u<o;u++)e[u]=r[t[u]],i[u]=n[t[u]]},m.prototype.transform=function(t,r,n,e,i,o){this.permute(o,t,r,n,e,i);for(var u=1;u<i;u<<=1)for(var s=u<<1,a=Math.cos(2*Math.PI/s),h=Math.sin(2*Math.PI/s),f=0;f<i;f+=s)for(var c=a,l=h,d=0;d<u;d++){var p=n[f+d],m=e[f+d],g=n[f+d+u],y=e[f+d+u],v=c*g-l*y;y=c*y+l*g,g=v,n[f+d]=p+g,e[f+d]=m+y,n[f+d+u]=p-g,e[f+d+u]=m-y,d!==s&&(v=a*c-h*l,l=a*l+h*c,c=v)}},m.prototype.guessLen13b=function(t,r){var n=1|Math.max(r,t),e=1&n,i=0;for(n=n/2|0;n;n>>>=1)i++;return 1<<i+1+e},m.prototype.conjugate=function(t,r,n){if(!(n<=1))for(var e=0;e<n/2;e++){var i=t[e];t[e]=t[n-e-1],t[n-e-1]=i,i=r[e],r[e]=-r[n-e-1],r[n-e-1]=-i}},m.prototype.normalize13b=function(t,r){for(var n=0,e=0;e<r/2;e++){var i=8192*Math.round(t[2*e+1]/r)+Math.round(t[2*e]/r)+n;t[e]=67108863&i,n=i<67108864?0:i/67108864|0}return t},m.prototype.convert13b=function(t,r,n,i){for(var o=0,u=0;u<r;u++)o+=0|t[u],n[2*u]=8191&o,o>>>=13,n[2*u+1]=8191&o,o>>>=13;for(u=2*r;u<i;++u)n[u]=0;e(0===o),e(0==(-8192&o))},m.prototype.stub=function(t){for(var r=new Array(t),n=0;n<t;n++)r[n]=0;return r},m.prototype.mulp=function(t,r,n){var e=2*this.guessLen13b(t.length,r.length),i=this.makeRBT(e),o=this.stub(e),u=new Array(e),s=new Array(e),a=new Array(e),h=new Array(e),f=new Array(e),c=new Array(e),l=n.words;l.length=e,this.convert13b(t.words,t.length,u,e),this.convert13b(r.words,r.length,h,e),this.transform(u,o,s,a,e,i),this.transform(h,o,f,c,e,i);for(var d=0;d<e;d++){var p=s[d]*f[d]-a[d]*c[d];a[d]=s[d]*c[d]+a[d]*f[d],s[d]=p}return this.conjugate(s,a,e),this.transform(s,a,l,o,e,i),this.conjugate(l,o,e),this.normalize13b(l,e),n.negative=t.negative^r.negative,n.length=t.length+r.length,n.strip()},o.prototype.mul=function(t){var r=new o(null);return r.words=new Array(this.length+t.length),this.mulTo(t,r)},o.prototype.mulf=function(t){var r=new o(null);return r.words=new Array(this.length+t.length),p(this,t,r)},o.prototype.imul=function(t){return this.clone().mulTo(t,this)},o.prototype.imuln=function(t){e("number"==typeof t),e(t<67108864);for(var r=0,n=0;n<this.length;n++){var i=(0|this.words[n])*t,o=(67108863&i)+(67108863&r);r>>=26,r+=i/67108864|0,r+=o>>>26,this.words[n]=67108863&o}return 0!==r&&(this.words[n]=r,this.length++),this},o.prototype.muln=function(t){return this.clone().imuln(t)},o.prototype.sqr=function(){return this.mul(this)},o.prototype.isqr=function(){return this.imul(this.clone())},o.prototype.pow=function(t){var r=function(t){for(var r=new Array(t.bitLength()),n=0;n<r.length;n++){var e=n/26|0,i=n%26;r[n]=(t.words[e]&1<<i)>>>i}return r}(t);if(0===r.length)return new o(1);for(var n=this,e=0;e<r.length&&0===r[e];e++,n=n.sqr());if(++e<r.length)for(var i=n.sqr();e<r.length;e++,i=i.sqr())0!==r[e]&&(n=n.mul(i));return n},o.prototype.iushln=function(t){e("number"==typeof t&&t>=0);var r,n=t%26,i=(t-n)/26,o=67108863>>>26-n<<26-n;if(0!==n){var u=0;for(r=0;r<this.length;r++){var s=this.words[r]&o,a=(0|this.words[r])-s<<n;this.words[r]=a|u,u=s>>>26-n}u&&(this.words[r]=u,this.length++)}if(0!==i){for(r=this.length-1;r>=0;r--)this.words[r+i]=this.words[r];for(r=0;r<i;r++)this.words[r]=0;this.length+=i}return this.strip()},o.prototype.ishln=function(t){return e(0===this.negative),this.iushln(t)},o.prototype.iushrn=function(t,r,n){var i;e("number"==typeof t&&t>=0),i=r?(r-r%26)/26:0;var o=t%26,u=Math.min((t-o)/26,this.length),s=67108863^67108863>>>o<<o,a=n;if(i-=u,i=Math.max(0,i),a){for(var h=0;h<u;h++)a.words[h]=this.words[h];a.length=u}if(0===u);else if(this.length>u)for(this.length-=u,h=0;h<this.length;h++)this.words[h]=this.words[h+u];else this.words[0]=0,this.length=1;var f=0;for(h=this.length-1;h>=0&&(0!==f||h>=i);h--){var c=0|this.words[h];this.words[h]=f<<26-o|c>>>o,f=c&s}return a&&0!==f&&(a.words[a.length++]=f),0===this.length&&(this.words[0]=0,this.length=1),this.strip()},o.prototype.ishrn=function(t,r,n){return e(0===this.negative),this.iushrn(t,r,n)},o.prototype.shln=function(t){return this.clone().ishln(t)},o.prototype.ushln=function(t){return this.clone().iushln(t)},o.prototype.shrn=function(t){return this.clone().ishrn(t)},o.prototype.ushrn=function(t){return this.clone().iushrn(t)},o.prototype.testn=function(t){e("number"==typeof t&&t>=0);var r=t%26,n=(t-r)/26,i=1<<r;return!(this.length<=n)&&!!(this.words[n]&i)},o.prototype.imaskn=function(t){e("number"==typeof t&&t>=0);var r=t%26,n=(t-r)/26;if(e(0===this.negative,"imaskn works only with positive numbers"),this.length<=n)return this;if(0!==r&&n++,this.length=Math.min(n,this.length),0!==r){var i=67108863^67108863>>>r<<r;this.words[this.length-1]&=i}return this.strip()},o.prototype.maskn=function(t){return this.clone().imaskn(t)},o.prototype.iaddn=function(t){return e("number"==typeof t),e(t<67108864),t<0?this.isubn(-t):0!==this.negative?1===this.length&&(0|this.words[0])<t?(this.words[0]=t-(0|this.words[0]),this.negative=0,this):(this.negative=0,this.isubn(t),this.negative=1,this):this._iaddn(t)},o.prototype._iaddn=function(t){this.words[0]+=t;for(var r=0;r<this.length&&this.words[r]>=67108864;r++)this.words[r]-=67108864,r===this.length-1?this.words[r+1]=1:this.words[r+1]++;return this.length=Math.max(this.length,r+1),this},o.prototype.isubn=function(t){if(e("number"==typeof t),e(t<67108864),t<0)return this.iaddn(-t);if(0!==this.negative)return this.negative=0,this.iaddn(t),this.negative=1,this;if(this.words[0]-=t,1===this.length&&this.words[0]<0)this.words[0]=-this.words[0],this.negative=1;else for(var r=0;r<this.length&&this.words[r]<0;r++)this.words[r]+=67108864,this.words[r+1]-=1;return this.strip()},o.prototype.addn=function(t){return this.clone().iaddn(t)},o.prototype.subn=function(t){return this.clone().isubn(t)},o.prototype.iabs=function(){return this.negative=0,this},o.prototype.abs=function(){return this.clone().iabs()},o.prototype._ishlnsubmul=function(t,r,n){var i,o,u=t.length+n;this._expand(u);var s=0;for(i=0;i<t.length;i++){o=(0|this.words[i+n])+s;var a=(0|t.words[i])*r;s=((o-=67108863&a)>>26)-(a/67108864|0),this.words[i+n]=67108863&o}for(;i<this.length-n;i++)s=(o=(0|this.words[i+n])+s)>>26,this.words[i+n]=67108863&o;if(0===s)return this.strip();for(e(-1===s),s=0,i=0;i<this.length;i++)s=(o=-(0|this.words[i])+s)>>26,this.words[i]=67108863&o;return this.negative=1,this.strip()},o.prototype._wordDiv=function(t,r){var n=(this.length,t.length),e=this.clone(),i=t,u=0|i.words[i.length-1];0!==(n=26-this._countBits(u))&&(i=i.ushln(n),e.iushln(n),u=0|i.words[i.length-1]);var s,a=e.length-i.length;if("mod"!==r){(s=new o(null)).length=a+1,s.words=new Array(s.length);for(var h=0;h<s.length;h++)s.words[h]=0}var f=e.clone()._ishlnsubmul(i,1,a);0===f.negative&&(e=f,s&&(s.words[a]=1));for(var c=a-1;c>=0;c--){var l=67108864*(0|e.words[i.length+c])+(0|e.words[i.length+c-1]);for(l=Math.min(l/u|0,67108863),e._ishlnsubmul(i,l,c);0!==e.negative;)l--,e.negative=0,e._ishlnsubmul(i,1,c),e.isZero()||(e.negative^=1);s&&(s.words[c]=l)}return s&&s.strip(),e.strip(),"div"!==r&&0!==n&&e.iushrn(n),{div:s||null,mod:e}},o.prototype.divmod=function(t,r,n){return e(!t.isZero()),this.isZero()?{div:new o(0),mod:new o(0)}:0!==this.negative&&0===t.negative?(s=this.neg().divmod(t,r),"mod"!==r&&(i=s.div.neg()),"div"!==r&&(u=s.mod.neg(),n&&0!==u.negative&&u.iadd(t)),{div:i,mod:u}):0===this.negative&&0!==t.negative?(s=this.divmod(t.neg(),r),"mod"!==r&&(i=s.div.neg()),{div:i,mod:s.mod}):0!=(this.negative&t.negative)?(s=this.neg().divmod(t.neg(),r),"div"!==r&&(u=s.mod.neg(),n&&0!==u.negative&&u.isub(t)),{div:s.div,mod:u}):t.length>this.length||this.cmp(t)<0?{div:new o(0),mod:this}:1===t.length?"div"===r?{div:this.divn(t.words[0]),mod:null}:"mod"===r?{div:null,mod:new o(this.modn(t.words[0]))}:{div:this.divn(t.words[0]),mod:new o(this.modn(t.words[0]))}:this._wordDiv(t,r);var i,u,s},o.prototype.div=function(t){return this.divmod(t,"div",!1).div},o.prototype.mod=function(t){return this.divmod(t,"mod",!1).mod},o.prototype.umod=function(t){return this.divmod(t,"mod",!0).mod},o.prototype.divRound=function(t){var r=this.divmod(t);if(r.mod.isZero())return r.div;var n=0!==r.div.negative?r.mod.isub(t):r.mod,e=t.ushrn(1),i=t.andln(1),o=n.cmp(e);return o<0||1===i&&0===o?r.div:0!==r.div.negative?r.div.isubn(1):r.div.iaddn(1)},o.prototype.modn=function(t){e(t<=67108863);for(var r=(1<<26)%t,n=0,i=this.length-1;i>=0;i--)n=(r*n+(0|this.words[i]))%t;return n},o.prototype.idivn=function(t){e(t<=67108863);for(var r=0,n=this.length-1;n>=0;n--){var i=(0|this.words[n])+67108864*r;this.words[n]=i/t|0,r=i%t}return this.strip()},o.prototype.divn=function(t){return this.clone().idivn(t)},o.prototype.egcd=function(t){e(0===t.negative),e(!t.isZero());var r=this,n=t.clone();r=0!==r.negative?r.umod(t):r.clone();for(var i=new o(1),u=new o(0),s=new o(0),a=new o(1),h=0;r.isEven()&&n.isEven();)r.iushrn(1),n.iushrn(1),++h;for(var f=n.clone(),c=r.clone();!r.isZero();){for(var l=0,d=1;0==(r.words[0]&d)&&l<26;++l,d<<=1);if(l>0)for(r.iushrn(l);l-- >0;)(i.isOdd()||u.isOdd())&&(i.iadd(f),u.isub(c)),i.iushrn(1),u.iushrn(1);for(var p=0,m=1;0==(n.words[0]&m)&&p<26;++p,m<<=1);if(p>0)for(n.iushrn(p);p-- >0;)(s.isOdd()||a.isOdd())&&(s.iadd(f),a.isub(c)),s.iushrn(1),a.iushrn(1);r.cmp(n)>=0?(r.isub(n),i.isub(s),u.isub(a)):(n.isub(r),s.isub(i),a.isub(u))}return{a:s,b:a,gcd:n.iushln(h)}},o.prototype._invmp=function(t){e(0===t.negative),e(!t.isZero());var r=this,n=t.clone();r=0!==r.negative?r.umod(t):r.clone();for(var i,u=new o(1),s=new o(0),a=n.clone();r.cmpn(1)>0&&n.cmpn(1)>0;){for(var h=0,f=1;0==(r.words[0]&f)&&h<26;++h,f<<=1);if(h>0)for(r.iushrn(h);h-- >0;)u.isOdd()&&u.iadd(a),u.iushrn(1);for(var c=0,l=1;0==(n.words[0]&l)&&c<26;++c,l<<=1);if(c>0)for(n.iushrn(c);c-- >0;)s.isOdd()&&s.iadd(a),s.iushrn(1);r.cmp(n)>=0?(r.isub(n),u.isub(s)):(n.isub(r),s.isub(u))}return(i=0===r.cmpn(1)?u:s).cmpn(0)<0&&i.iadd(t),i},o.prototype.gcd=function(t){if(this.isZero())return t.abs();if(t.isZero())return this.abs();var r=this.clone(),n=t.clone();r.negative=0,n.negative=0;for(var e=0;r.isEven()&&n.isEven();e++)r.iushrn(1),n.iushrn(1);for(;;){for(;r.isEven();)r.iushrn(1);for(;n.isEven();)n.iushrn(1);var i=r.cmp(n);if(i<0){var o=r;r=n,n=o}else if(0===i||0===n.cmpn(1))break;r.isub(n)}return n.iushln(e)},o.prototype.invm=function(t){return this.egcd(t).a.umod(t)},o.prototype.isEven=function(){return 0==(1&this.words[0])},o.prototype.isOdd=function(){return 1==(1&this.words[0])},o.prototype.andln=function(t){return this.words[0]&t},o.prototype.bincn=function(t){e("number"==typeof t);var r=t%26,n=(t-r)/26,i=1<<r;if(this.length<=n)return this._expand(n+1),this.words[n]|=i,this;for(var o=i,u=n;0!==o&&u<this.length;u++){var s=0|this.words[u];o=(s+=o)>>>26,s&=67108863,this.words[u]=s}return 0!==o&&(this.words[u]=o,this.length++),this},o.prototype.isZero=function(){return 1===this.length&&0===this.words[0]},o.prototype.cmpn=function(t){var r,n=t<0;if(0!==this.negative&&!n)return-1;if(0===this.negative&&n)return 1;if(this.strip(),this.length>1)r=1;else{n&&(t=-t),e(t<=67108863,"Number is too big");var i=0|this.words[0];r=i===t?0:i<t?-1:1}return 0!==this.negative?0|-r:r},o.prototype.cmp=function(t){if(0!==this.negative&&0===t.negative)return-1;if(0===this.negative&&0!==t.negative)return 1;var r=this.ucmp(t);return 0!==this.negative?0|-r:r},o.prototype.ucmp=function(t){if(this.length>t.length)return 1;if(this.length<t.length)return-1;for(var r=0,n=this.length-1;n>=0;n--){var e=0|this.words[n],i=0|t.words[n];if(e!==i){e<i?r=-1:e>i&&(r=1);break}}return r},o.prototype.gtn=function(t){return 1===this.cmpn(t)},o.prototype.gt=function(t){return 1===this.cmp(t)},o.prototype.gten=function(t){return this.cmpn(t)>=0},o.prototype.gte=function(t){return this.cmp(t)>=0},o.prototype.ltn=function(t){return-1===this.cmpn(t)},o.prototype.lt=function(t){return-1===this.cmp(t)},o.prototype.lten=function(t){return this.cmpn(t)<=0},o.prototype.lte=function(t){return this.cmp(t)<=0},o.prototype.eqn=function(t){return 0===this.cmpn(t)},o.prototype.eq=function(t){return 0===this.cmp(t)},o.red=function(t){return new _(t)},o.prototype.toRed=function(t){return e(!this.red,"Already a number in reduction context"),e(0===this.negative,"red works only with positives"),t.convertTo(this)._forceRed(t)},o.prototype.fromRed=function(){return e(this.red,"fromRed works only with numbers in reduction context"),this.red.convertFrom(this)},o.prototype._forceRed=function(t){return this.red=t,this},o.prototype.forceRed=function(t){return e(!this.red,"Already a number in reduction context"),this._forceRed(t)},o.prototype.redAdd=function(t){return e(this.red,"redAdd works only with red numbers"),this.red.add(this,t)},o.prototype.redIAdd=function(t){return e(this.red,"redIAdd works only with red numbers"),this.red.iadd(this,t)},o.prototype.redSub=function(t){return e(this.red,"redSub works only with red numbers"),this.red.sub(this,t)},o.prototype.redISub=function(t){return e(this.red,"redISub works only with red numbers"),this.red.isub(this,t)},o.prototype.redShl=function(t){return e(this.red,"redShl works only with red numbers"),this.red.shl(this,t)},o.prototype.redMul=function(t){return e(this.red,"redMul works only with red numbers"),this.red._verify2(this,t),this.red.mul(this,t)},o.prototype.redIMul=function(t){return e(this.red,"redMul works only with red numbers"),this.red._verify2(this,t),this.red.imul(this,t)},o.prototype.redSqr=function(){return e(this.red,"redSqr works only with red numbers"),this.red._verify1(this),this.red.sqr(this)},o.prototype.redISqr=function(){return e(this.red,"redISqr works only with red numbers"),this.red._verify1(this),this.red.isqr(this)},o.prototype.redSqrt=function(){return e(this.red,"redSqrt works only with red numbers"),this.red._verify1(this),this.red.sqrt(this)},o.prototype.redInvm=function(){return e(this.red,"redInvm works only with red numbers"),this.red._verify1(this),this.red.invm(this)},o.prototype.redNeg=function(){return e(this.red,"redNeg works only with red numbers"),this.red._verify1(this),this.red.neg(this)},o.prototype.redPow=function(t){return e(this.red&&!t.red,"redPow(normalNum)"),this.red._verify1(this),this.red.pow(this,t)};var g={k256:null,p224:null,p192:null,p25519:null};function y(t,r){this.name=t,this.p=new o(r,16),this.n=this.p.bitLength(),this.k=new o(1).iushln(this.n).isub(this.p),this.tmp=this._tmp()}function v(){y.call(this,"k256","ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffe fffffc2f")}function w(){y.call(this,"p224","ffffffff ffffffff ffffffff ffffffff 00000000 00000000 00000001")}function M(){y.call(this,"p192","ffffffff ffffffff ffffffff fffffffe ffffffff ffffffff")}function b(){y.call(this,"25519","7fffffffffffffff ffffffffffffffff ffffffffffffffff ffffffffffffffed")}function _(t){if("string"==typeof t){var r=o._prime(t);this.m=r.p,this.prime=r}else e(t.gtn(1),"modulus must be greater than 1"),this.m=t,this.prime=null}function A(t){_.call(this,t),this.shift=this.m.bitLength(),this.shift%26!=0&&(this.shift+=26-this.shift%26),this.r=new o(1).iushln(this.shift),this.r2=this.imod(this.r.sqr()),this.rinv=this.r._invmp(this.m),this.minv=this.rinv.mul(this.r).isubn(1).div(this.m),this.minv=this.minv.umod(this.r),this.minv=this.r.sub(this.minv)}y.prototype._tmp=function(){var t=new o(null);return t.words=new Array(Math.ceil(this.n/13)),t},y.prototype.ireduce=function(t){var r,n=t;do{this.split(n,this.tmp),r=(n=(n=this.imulK(n)).iadd(this.tmp)).bitLength()}while(r>this.n);var e=r<this.n?-1:n.ucmp(this.p);return 0===e?(n.words[0]=0,n.length=1):e>0?n.isub(this.p):n.strip(),n},y.prototype.split=function(t,r){t.iushrn(this.n,0,r)},y.prototype.imulK=function(t){return t.imul(this.k)},i(v,y),v.prototype.split=function(t,r){for(var n=Math.min(t.length,9),e=0;e<n;e++)r.words[e]=t.words[e];if(r.length=n,t.length<=9)return t.words[0]=0,void(t.length=1);var i=t.words[9];for(r.words[r.length++]=4194303&i,e=10;e<t.length;e++){var o=0|t.words[e];t.words[e-10]=(4194303&o)<<4|i>>>22,i=o}i>>>=22,t.words[e-10]=i,0===i&&t.length>10?t.length-=10:t.length-=9},v.prototype.imulK=function(t){t.words[t.length]=0,t.words[t.length+1]=0,t.length+=2;for(var r=0,n=0;n<t.length;n++){var e=0|t.words[n];r+=977*e,t.words[n]=67108863&r,r=64*e+(r/67108864|0)}return 0===t.words[t.length-1]&&(t.length--,0===t.words[t.length-1]&&t.length--),t},i(w,y),i(M,y),i(b,y),b.prototype.imulK=function(t){for(var r=0,n=0;n<t.length;n++){var e=19*(0|t.words[n])+r,i=67108863&e;e>>>=26,t.words[n]=i,r=e}return 0!==r&&(t.words[t.length++]=r),t},o._prime=function(t){if(g[t])return g[t];var r;if("k256"===t)r=new v;else if("p224"===t)r=new w;else if("p192"===t)r=new M;else{if("p25519"!==t)throw new Error("Unknown prime "+t);r=new b}return g[t]=r,r},_.prototype._verify1=function(t){e(0===t.negative,"red works only with positives"),e(t.red,"red works only with red numbers")},_.prototype._verify2=function(t,r){e(0==(t.negative|r.negative),"red works only with positives"),e(t.red&&t.red===r.red,"red works only with red numbers")},_.prototype.imod=function(t){return this.prime?this.prime.ireduce(t)._forceRed(this):t.umod(this.m)._forceRed(this)},_.prototype.neg=function(t){return t.isZero()?t.clone():this.m.sub(t)._forceRed(this)},_.prototype.add=function(t,r){this._verify2(t,r);var n=t.add(r);return n.cmp(this.m)>=0&&n.isub(this.m),n._forceRed(this)},_.prototype.iadd=function(t,r){this._verify2(t,r);var n=t.iadd(r);return n.cmp(this.m)>=0&&n.isub(this.m),n},_.prototype.sub=function(t,r){this._verify2(t,r);var n=t.sub(r);return n.cmpn(0)<0&&n.iadd(this.m),n._forceRed(this)},_.prototype.isub=function(t,r){this._verify2(t,r);var n=t.isub(r);return n.cmpn(0)<0&&n.iadd(this.m),n},_.prototype.shl=function(t,r){return this._verify1(t),this.imod(t.ushln(r))},_.prototype.imul=function(t,r){return this._verify2(t,r),this.imod(t.imul(r))},_.prototype.mul=function(t,r){return this._verify2(t,r),this.imod(t.mul(r))},_.prototype.isqr=function(t){return this.imul(t,t.clone())},_.prototype.sqr=function(t){return this.mul(t,t)},_.prototype.sqrt=function(t){if(t.isZero())return t.clone();var r=this.m.andln(3);if(e(r%2==1),3===r){var n=this.m.add(new o(1)).iushrn(2);return this.pow(t,n)}for(var i=this.m.subn(1),u=0;!i.isZero()&&0===i.andln(1);)u++,i.iushrn(1);e(!i.isZero());var s=new o(1).toRed(this),a=s.redNeg(),h=this.m.subn(1).iushrn(1),f=this.m.bitLength();for(f=new o(2*f*f).toRed(this);0!==this.pow(f,h).cmp(a);)f.redIAdd(a);for(var c=this.pow(f,i),l=this.pow(t,i.addn(1).iushrn(1)),d=this.pow(t,i),p=u;0!==d.cmp(s);){for(var m=d,g=0;0!==m.cmp(s);g++)m=m.redSqr();e(g<p);var y=this.pow(c,new o(1).iushln(p-g-1));l=l.redMul(y),c=y.redSqr(),d=d.redMul(c),p=g}return l},_.prototype.invm=function(t){var r=t._invmp(this.m);return 0!==r.negative?(r.negative=0,this.imod(r).redNeg()):this.imod(r)},_.prototype.pow=function(t,r){if(r.isZero())return new o(1).toRed(this);if(0===r.cmpn(1))return t.clone();var n=new Array(16);n[0]=new o(1).toRed(this),n[1]=t;for(var e=2;e<n.length;e++)n[e]=this.mul(n[e-1],t);var i=n[0],u=0,s=0,a=r.bitLength()%26;for(0===a&&(a=26),e=r.length-1;e>=0;e--){for(var h=r.words[e],f=a-1;f>=0;f--){var c=h>>f&1;i!==n[0]&&(i=this.sqr(i)),0!==c||0!==u?(u<<=1,u|=c,(4===++s||0===e&&0===f)&&(i=this.mul(i,n[u]),s=0,u=0)):s=0}a=26}return i},_.prototype.convertTo=function(t){var r=t.umod(this.m);return r===t?r.clone():r},_.prototype.convertFrom=function(t){var r=t.clone();return r.red=null,r},o.mont=function(t){return new A(t)},i(A,_),A.prototype.convertTo=function(t){return this.imod(t.ushln(this.shift))},A.prototype.convertFrom=function(t){var r=this.imod(t.mul(this.rinv));return r.red=null,r},A.prototype.imul=function(t,r){if(t.isZero()||r.isZero())return t.words[0]=0,t.length=1,t;var n=t.imul(r),e=n.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m),i=n.isub(e).iushrn(this.shift),o=i;return i.cmp(this.m)>=0?o=i.isub(this.m):i.cmpn(0)<0&&(o=i.iadd(this.m)),o._forceRed(this)},A.prototype.mul=function(t,r){if(t.isZero()||r.isZero())return new o(0)._forceRed(this);var n=t.mul(r),e=n.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m),i=n.isub(e).iushrn(this.shift),u=i;return i.cmp(this.m)>=0?u=i.isub(this.m):i.cmpn(0)<0&&(u=i.iadd(this.m)),u._forceRed(this)},A.prototype.invm=function(t){return this.imod(t._invmp(this.m).mul(this.r2))._forceRed(this)}}(t,this)}).call(this,n(29)(t))},function(t,r){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(t){"object"==typeof window&&(n=window)}t.exports=n},function(t,r,n){"use strict";var e=this&&this.__createBinding||(Object.create?function(t,r,n,e){void 0===e&&(e=n),Object.defineProperty(t,e,{enumerable:!0,get:function(){return r[n]}})}:function(t,r,n,e){void 0===e&&(e=n),t[e]=r[n]}),i=this&&this.__exportStar||function(t,r){for(var n in t)"default"===n||r.hasOwnProperty(n)||e(r,t,n)};Object.defineProperty(r,"__esModule",{value:!0}),i(n(35),r),i(n(36),r)},function(t,r){var n,e,i=t.exports={};function o(){throw new Error("setTimeout has not been defined")}function u(){throw new Error("clearTimeout has not been defined")}function s(t){if(n===setTimeout)return setTimeout(t,0);if((n===o||!n)&&setTimeout)return n=setTimeout,setTimeout(t,0);try{return n(t,0)}catch(r){try{return n.call(null,t,0)}catch(r){return n.call(this,t,0)}}}!function(){try{n="function"==typeof setTimeout?setTimeout:o}catch(t){n=o}try{e="function"==typeof clearTimeout?clearTimeout:u}catch(t){e=u}}();var a,h=[],f=!1,c=-1;function l(){f&&a&&(f=!1,a.length?h=a.concat(h):c=-1,h.length&&d())}function d(){if(!f){var t=s(l);f=!0;for(var r=h.length;r;){for(a=h,h=[];++c<r;)a&&a[c].run();c=-1,r=h.length}a=null,f=!1,function(t){if(e===clearTimeout)return clearTimeout(t);if((e===u||!e)&&clearTimeout)return e=clearTimeout,clearTimeout(t);try{e(t)}catch(r){try{return e.call(null,t)}catch(r){return e.call(this,t)}}}(t)}}function p(t,r){this.fun=t,this.array=r}function m(){}i.nextTick=function(t){var r=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)r[n-1]=arguments[n];h.push(new p(t,r)),1!==h.length||f||s(d)},p.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=m,i.addListener=m,i.once=m,i.off=m,i.removeListener=m,i.removeAllListeners=m,i.emit=m,i.prependListener=m,i.prependOnceListener=m,i.listeners=function(t){return[]},i.binding=function(t){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(t){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},function(t,r){t.exports=i,i.strict=o,i.loose=u;var n=Object.prototype.toString,e={"[object Int8Array]":!0,"[object Int16Array]":!0,"[object Int32Array]":!0,"[object Uint8Array]":!0,"[object Uint8ClampedArray]":!0,"[object Uint16Array]":!0,"[object Uint32Array]":!0,"[object Float32Array]":!0,"[object Float64Array]":!0};function i(t){return o(t)||u(t)}function o(t){return t instanceof Int8Array||t instanceof Int16Array||t instanceof Int32Array||t instanceof Uint8Array||t instanceof Uint8ClampedArray||t instanceof Uint16Array||t instanceof Uint32Array||t instanceof Float32Array||t instanceof Float64Array}function u(t){return e[n.call(t)]}},function(t,r,n){"use strict";const e=n(37),i=n(38),o=n(39);function u(t){if("string"!=typeof t||1!==t.length)throw new TypeError("arrayFormatSeparator must be single character string")}function s(t,r){return r.encode?r.strict?e(t):encodeURIComponent(t):t}function a(t,r){return r.decode?i(t):t}function h(t){const r=t.indexOf("#");return-1!==r&&(t=t.slice(0,r)),t}function f(t){const r=(t=h(t)).indexOf("?");return-1===r?"":t.slice(r+1)}function c(t,r){return r.parseNumbers&&!Number.isNaN(Number(t))&&"string"==typeof t&&""!==t.trim()?t=Number(t):!r.parseBooleans||null===t||"true"!==t.toLowerCase()&&"false"!==t.toLowerCase()||(t="true"===t.toLowerCase()),t}function l(t,r){u((r=Object.assign({decode:!0,sort:!0,arrayFormat:"none",arrayFormatSeparator:",",parseNumbers:!1,parseBooleans:!1},r)).arrayFormatSeparator);const n=function(t){let r;switch(t.arrayFormat){case"index":return(t,n,e)=>{r=/\[(\d*)\]$/.exec(t),t=t.replace(/\[\d*\]$/,""),r?(void 0===e[t]&&(e[t]={}),e[t][r[1]]=n):e[t]=n};case"bracket":return(t,n,e)=>{r=/(\[\])$/.exec(t),t=t.replace(/\[\]$/,""),r?void 0!==e[t]?e[t]=[].concat(e[t],n):e[t]=[n]:e[t]=n};case"comma":case"separator":return(r,n,e)=>{const i="string"==typeof n&&n.split("").indexOf(t.arrayFormatSeparator)>-1?n.split(t.arrayFormatSeparator).map(r=>a(r,t)):null===n?n:a(n,t);e[r]=i};default:return(t,r,n)=>{void 0!==n[t]?n[t]=[].concat(n[t],r):n[t]=r}}}(r),e=Object.create(null);if("string"!=typeof t)return e;if(!(t=t.trim().replace(/^[?#&]/,"")))return e;for(const i of t.split("&")){let[t,u]=o(r.decode?i.replace(/\+/g," "):i,"=");u=void 0===u?null:["comma","separator"].includes(r.arrayFormat)?u:a(u,r),n(a(t,r),u,e)}for(const t of Object.keys(e)){const n=e[t];if("object"==typeof n&&null!==n)for(const t of Object.keys(n))n[t]=c(n[t],r);else e[t]=c(n,r)}return!1===r.sort?e:(!0===r.sort?Object.keys(e).sort():Object.keys(e).sort(r.sort)).reduce((t,r)=>{const n=e[r];return Boolean(n)&&"object"==typeof n&&!Array.isArray(n)?t[r]=function t(r){return Array.isArray(r)?r.sort():"object"==typeof r?t(Object.keys(r)).sort((t,r)=>Number(t)-Number(r)).map(t=>r[t]):r}(n):t[r]=n,t},Object.create(null))}r.extract=f,r.parse=l,r.stringify=(t,r)=>{if(!t)return"";u((r=Object.assign({encode:!0,strict:!0,arrayFormat:"none",arrayFormatSeparator:","},r)).arrayFormatSeparator);const n=n=>r.skipNull&&null==t[n]||r.skipEmptyString&&""===t[n],e=function(t){switch(t.arrayFormat){case"index":return r=>(n,e)=>{const i=n.length;return void 0===e||t.skipNull&&null===e||t.skipEmptyString&&""===e?n:null===e?[...n,[s(r,t),"[",i,"]"].join("")]:[...n,[s(r,t),"[",s(i,t),"]=",s(e,t)].join("")]};case"bracket":return r=>(n,e)=>void 0===e||t.skipNull&&null===e||t.skipEmptyString&&""===e?n:null===e?[...n,[s(r,t),"[]"].join("")]:[...n,[s(r,t),"[]=",s(e,t)].join("")];case"comma":case"separator":return r=>(n,e)=>null==e||0===e.length?n:0===n.length?[[s(r,t),"=",s(e,t)].join("")]:[[n,s(e,t)].join(t.arrayFormatSeparator)];default:return r=>(n,e)=>void 0===e||t.skipNull&&null===e||t.skipEmptyString&&""===e?n:null===e?[...n,s(r,t)]:[...n,[s(r,t),"=",s(e,t)].join("")]}}(r),i={};for(const r of Object.keys(t))n(r)||(i[r]=t[r]);const o=Object.keys(i);return!1!==r.sort&&o.sort(r.sort),o.map(n=>{const i=t[n];return void 0===i?"":null===i?s(n,r):Array.isArray(i)?i.reduce(e(n),[]).join("&"):s(n,r)+"="+s(i,r)}).filter(t=>t.length>0).join("&")},r.parseUrl=(t,r)=>{r=Object.assign({decode:!0},r);const[n,e]=o(t,"#");return Object.assign({url:n.split("?")[0]||"",query:l(f(t),r)},r&&r.parseFragmentIdentifier&&e?{fragmentIdentifier:a(e,r)}:{})},r.stringifyUrl=(t,n)=>{n=Object.assign({encode:!0,strict:!0},n);const e=h(t.url).split("?")[0]||"",i=r.extract(t.url),o=r.parse(i,{sort:!1}),u=Object.assign(o,t.query);let a=r.stringify(u,n);a&&(a="?"+a);let f=function(t){let r="";const n=t.indexOf("#");return-1!==n&&(r=t.slice(n)),r}(t.url);return t.fragmentIdentifier&&(f="#"+s(t.fragmentIdentifier,n)),`${e}${a}${f}`}},function(t,r,n){"use strict";(function(t){
/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <http://feross.org>
 * @license  MIT
 */
var e=n(31),i=n(32),o=n(33);function u(){return a.TYPED_ARRAY_SUPPORT?**********:**********}function s(t,r){if(u()<r)throw new RangeError("Invalid typed array length");return a.TYPED_ARRAY_SUPPORT?(t=new Uint8Array(r)).__proto__=a.prototype:(null===t&&(t=new a(r)),t.length=r),t}function a(t,r,n){if(!(a.TYPED_ARRAY_SUPPORT||this instanceof a))return new a(t,r,n);if("number"==typeof t){if("string"==typeof r)throw new Error("If encoding is specified then the first argument must be a string");return c(this,t)}return h(this,t,r,n)}function h(t,r,n,e){if("number"==typeof r)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&r instanceof ArrayBuffer?function(t,r,n,e){if(r.byteLength,n<0||r.byteLength<n)throw new RangeError("'offset' is out of bounds");if(r.byteLength<n+(e||0))throw new RangeError("'length' is out of bounds");r=void 0===n&&void 0===e?new Uint8Array(r):void 0===e?new Uint8Array(r,n):new Uint8Array(r,n,e);a.TYPED_ARRAY_SUPPORT?(t=r).__proto__=a.prototype:t=l(t,r);return t}(t,r,n,e):"string"==typeof r?function(t,r,n){"string"==typeof n&&""!==n||(n="utf8");if(!a.isEncoding(n))throw new TypeError('"encoding" must be a valid string encoding');var e=0|p(r,n),i=(t=s(t,e)).write(r,n);i!==e&&(t=t.slice(0,i));return t}(t,r,n):function(t,r){if(a.isBuffer(r)){var n=0|d(r.length);return 0===(t=s(t,n)).length||r.copy(t,0,0,n),t}if(r){if("undefined"!=typeof ArrayBuffer&&r.buffer instanceof ArrayBuffer||"length"in r)return"number"!=typeof r.length||(e=r.length)!=e?s(t,0):l(t,r);if("Buffer"===r.type&&o(r.data))return l(t,r.data)}var e;throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(t,r)}function f(t){if("number"!=typeof t)throw new TypeError('"size" argument must be a number');if(t<0)throw new RangeError('"size" argument must not be negative')}function c(t,r){if(f(r),t=s(t,r<0?0:0|d(r)),!a.TYPED_ARRAY_SUPPORT)for(var n=0;n<r;++n)t[n]=0;return t}function l(t,r){var n=r.length<0?0:0|d(r.length);t=s(t,n);for(var e=0;e<n;e+=1)t[e]=255&r[e];return t}function d(t){if(t>=u())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+u().toString(16)+" bytes");return 0|t}function p(t,r){if(a.isBuffer(t))return t.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(t)||t instanceof ArrayBuffer))return t.byteLength;"string"!=typeof t&&(t=""+t);var n=t.length;if(0===n)return 0;for(var e=!1;;)switch(r){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":case void 0:return D(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return F(t).length;default:if(e)return D(t).length;r=(""+r).toLowerCase(),e=!0}}function m(t,r,n){var e=!1;if((void 0===r||r<0)&&(r=0),r>this.length)return"";if((void 0===n||n>this.length)&&(n=this.length),n<=0)return"";if((n>>>=0)<=(r>>>=0))return"";for(t||(t="utf8");;)switch(t){case"hex":return T(this,r,n);case"utf8":case"utf-8":return E(this,r,n);case"ascii":return k(this,r,n);case"latin1":case"binary":return x(this,r,n);case"base64":return O(this,r,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return R(this,r,n);default:if(e)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),e=!0}}function g(t,r,n){var e=t[r];t[r]=t[n],t[n]=e}function y(t,r,n,e,i){if(0===t.length)return-1;if("string"==typeof n?(e=n,n=0):n>**********?n=**********:n<-**********&&(n=-**********),n=+n,isNaN(n)&&(n=i?0:t.length-1),n<0&&(n=t.length+n),n>=t.length){if(i)return-1;n=t.length-1}else if(n<0){if(!i)return-1;n=0}if("string"==typeof r&&(r=a.from(r,e)),a.isBuffer(r))return 0===r.length?-1:v(t,r,n,e,i);if("number"==typeof r)return r&=255,a.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(t,r,n):Uint8Array.prototype.lastIndexOf.call(t,r,n):v(t,[r],n,e,i);throw new TypeError("val must be string, number or Buffer")}function v(t,r,n,e,i){var o,u=1,s=t.length,a=r.length;if(void 0!==e&&("ucs2"===(e=String(e).toLowerCase())||"ucs-2"===e||"utf16le"===e||"utf-16le"===e)){if(t.length<2||r.length<2)return-1;u=2,s/=2,a/=2,n/=2}function h(t,r){return 1===u?t[r]:t.readUInt16BE(r*u)}if(i){var f=-1;for(o=n;o<s;o++)if(h(t,o)===h(r,-1===f?0:o-f)){if(-1===f&&(f=o),o-f+1===a)return f*u}else-1!==f&&(o-=o-f),f=-1}else for(n+a>s&&(n=s-a),o=n;o>=0;o--){for(var c=!0,l=0;l<a;l++)if(h(t,o+l)!==h(r,l)){c=!1;break}if(c)return o}return-1}function w(t,r,n,e){n=Number(n)||0;var i=t.length-n;e?(e=Number(e))>i&&(e=i):e=i;var o=r.length;if(o%2!=0)throw new TypeError("Invalid hex string");e>o/2&&(e=o/2);for(var u=0;u<e;++u){var s=parseInt(r.substr(2*u,2),16);if(isNaN(s))return u;t[n+u]=s}return u}function M(t,r,n,e){return Y(D(r,t.length-n),t,n,e)}function b(t,r,n,e){return Y(function(t){for(var r=[],n=0;n<t.length;++n)r.push(255&t.charCodeAt(n));return r}(r),t,n,e)}function _(t,r,n,e){return b(t,r,n,e)}function A(t,r,n,e){return Y(F(r),t,n,e)}function S(t,r,n,e){return Y(function(t,r){for(var n,e,i,o=[],u=0;u<t.length&&!((r-=2)<0);++u)n=t.charCodeAt(u),e=n>>8,i=n%256,o.push(i),o.push(e);return o}(r,t.length-n),t,n,e)}function O(t,r,n){return 0===r&&n===t.length?e.fromByteArray(t):e.fromByteArray(t.slice(r,n))}function E(t,r,n){n=Math.min(t.length,n);for(var e=[],i=r;i<n;){var o,u,s,a,h=t[i],f=null,c=h>239?4:h>223?3:h>191?2:1;if(i+c<=n)switch(c){case 1:h<128&&(f=h);break;case 2:128==(192&(o=t[i+1]))&&(a=(31&h)<<6|63&o)>127&&(f=a);break;case 3:o=t[i+1],u=t[i+2],128==(192&o)&&128==(192&u)&&(a=(15&h)<<12|(63&o)<<6|63&u)>2047&&(a<55296||a>57343)&&(f=a);break;case 4:o=t[i+1],u=t[i+2],s=t[i+3],128==(192&o)&&128==(192&u)&&128==(192&s)&&(a=(15&h)<<18|(63&o)<<12|(63&u)<<6|63&s)>65535&&a<1114112&&(f=a)}null===f?(f=65533,c=1):f>65535&&(f-=65536,e.push(f>>>10&1023|55296),f=56320|1023&f),e.push(f),i+=c}return function(t){var r=t.length;if(r<=4096)return String.fromCharCode.apply(String,t);var n="",e=0;for(;e<r;)n+=String.fromCharCode.apply(String,t.slice(e,e+=4096));return n}(e)}r.Buffer=a,r.SlowBuffer=function(t){+t!=t&&(t=0);return a.alloc(+t)},r.INSPECT_MAX_BYTES=50,a.TYPED_ARRAY_SUPPORT=void 0!==t.TYPED_ARRAY_SUPPORT?t.TYPED_ARRAY_SUPPORT:function(){try{var t=new Uint8Array(1);return t.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===t.foo()&&"function"==typeof t.subarray&&0===t.subarray(1,1).byteLength}catch(t){return!1}}(),r.kMaxLength=u(),a.poolSize=8192,a._augment=function(t){return t.__proto__=a.prototype,t},a.from=function(t,r,n){return h(null,t,r,n)},a.TYPED_ARRAY_SUPPORT&&(a.prototype.__proto__=Uint8Array.prototype,a.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&a[Symbol.species]===a&&Object.defineProperty(a,Symbol.species,{value:null,configurable:!0})),a.alloc=function(t,r,n){return function(t,r,n,e){return f(r),r<=0?s(t,r):void 0!==n?"string"==typeof e?s(t,r).fill(n,e):s(t,r).fill(n):s(t,r)}(null,t,r,n)},a.allocUnsafe=function(t){return c(null,t)},a.allocUnsafeSlow=function(t){return c(null,t)},a.isBuffer=function(t){return!(null==t||!t._isBuffer)},a.compare=function(t,r){if(!a.isBuffer(t)||!a.isBuffer(r))throw new TypeError("Arguments must be Buffers");if(t===r)return 0;for(var n=t.length,e=r.length,i=0,o=Math.min(n,e);i<o;++i)if(t[i]!==r[i]){n=t[i],e=r[i];break}return n<e?-1:e<n?1:0},a.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},a.concat=function(t,r){if(!o(t))throw new TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return a.alloc(0);var n;if(void 0===r)for(r=0,n=0;n<t.length;++n)r+=t[n].length;var e=a.allocUnsafe(r),i=0;for(n=0;n<t.length;++n){var u=t[n];if(!a.isBuffer(u))throw new TypeError('"list" argument must be an Array of Buffers');u.copy(e,i),i+=u.length}return e},a.byteLength=p,a.prototype._isBuffer=!0,a.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var r=0;r<t;r+=2)g(this,r,r+1);return this},a.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var r=0;r<t;r+=4)g(this,r,r+3),g(this,r+1,r+2);return this},a.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var r=0;r<t;r+=8)g(this,r,r+7),g(this,r+1,r+6),g(this,r+2,r+5),g(this,r+3,r+4);return this},a.prototype.toString=function(){var t=0|this.length;return 0===t?"":0===arguments.length?E(this,0,t):m.apply(this,arguments)},a.prototype.equals=function(t){if(!a.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t||0===a.compare(this,t)},a.prototype.inspect=function(){var t="",n=r.INSPECT_MAX_BYTES;return this.length>0&&(t=this.toString("hex",0,n).match(/.{2}/g).join(" "),this.length>n&&(t+=" ... ")),"<Buffer "+t+">"},a.prototype.compare=function(t,r,n,e,i){if(!a.isBuffer(t))throw new TypeError("Argument must be a Buffer");if(void 0===r&&(r=0),void 0===n&&(n=t?t.length:0),void 0===e&&(e=0),void 0===i&&(i=this.length),r<0||n>t.length||e<0||i>this.length)throw new RangeError("out of range index");if(e>=i&&r>=n)return 0;if(e>=i)return-1;if(r>=n)return 1;if(this===t)return 0;for(var o=(i>>>=0)-(e>>>=0),u=(n>>>=0)-(r>>>=0),s=Math.min(o,u),h=this.slice(e,i),f=t.slice(r,n),c=0;c<s;++c)if(h[c]!==f[c]){o=h[c],u=f[c];break}return o<u?-1:u<o?1:0},a.prototype.includes=function(t,r,n){return-1!==this.indexOf(t,r,n)},a.prototype.indexOf=function(t,r,n){return y(this,t,r,n,!0)},a.prototype.lastIndexOf=function(t,r,n){return y(this,t,r,n,!1)},a.prototype.write=function(t,r,n,e){if(void 0===r)e="utf8",n=this.length,r=0;else if(void 0===n&&"string"==typeof r)e=r,n=this.length,r=0;else{if(!isFinite(r))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");r|=0,isFinite(n)?(n|=0,void 0===e&&(e="utf8")):(e=n,n=void 0)}var i=this.length-r;if((void 0===n||n>i)&&(n=i),t.length>0&&(n<0||r<0)||r>this.length)throw new RangeError("Attempt to write outside buffer bounds");e||(e="utf8");for(var o=!1;;)switch(e){case"hex":return w(this,t,r,n);case"utf8":case"utf-8":return M(this,t,r,n);case"ascii":return b(this,t,r,n);case"latin1":case"binary":return _(this,t,r,n);case"base64":return A(this,t,r,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return S(this,t,r,n);default:if(o)throw new TypeError("Unknown encoding: "+e);e=(""+e).toLowerCase(),o=!0}},a.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};function k(t,r,n){var e="";n=Math.min(t.length,n);for(var i=r;i<n;++i)e+=String.fromCharCode(127&t[i]);return e}function x(t,r,n){var e="";n=Math.min(t.length,n);for(var i=r;i<n;++i)e+=String.fromCharCode(t[i]);return e}function T(t,r,n){var e=t.length;(!r||r<0)&&(r=0),(!n||n<0||n>e)&&(n=e);for(var i="",o=r;o<n;++o)i+=W(t[o]);return i}function R(t,r,n){for(var e=t.slice(r,n),i="",o=0;o<e.length;o+=2)i+=String.fromCharCode(e[o]+256*e[o+1]);return i}function B(t,r,n){if(t%1!=0||t<0)throw new RangeError("offset is not uint");if(t+r>n)throw new RangeError("Trying to access beyond buffer length")}function P(t,r,n,e,i,o){if(!a.isBuffer(t))throw new TypeError('"buffer" argument must be a Buffer instance');if(r>i||r<o)throw new RangeError('"value" argument is out of bounds');if(n+e>t.length)throw new RangeError("Index out of range")}function C(t,r,n,e){r<0&&(r=65535+r+1);for(var i=0,o=Math.min(t.length-n,2);i<o;++i)t[n+i]=(r&255<<8*(e?i:1-i))>>>8*(e?i:1-i)}function I(t,r,n,e){r<0&&(r=4294967295+r+1);for(var i=0,o=Math.min(t.length-n,4);i<o;++i)t[n+i]=r>>>8*(e?i:3-i)&255}function j(t,r,n,e,i,o){if(n+e>t.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("Index out of range")}function L(t,r,n,e,o){return o||j(t,0,n,4),i.write(t,r,n,e,23,4),n+4}function N(t,r,n,e,o){return o||j(t,0,n,8),i.write(t,r,n,e,52,8),n+8}a.prototype.slice=function(t,r){var n,e=this.length;if((t=~~t)<0?(t+=e)<0&&(t=0):t>e&&(t=e),(r=void 0===r?e:~~r)<0?(r+=e)<0&&(r=0):r>e&&(r=e),r<t&&(r=t),a.TYPED_ARRAY_SUPPORT)(n=this.subarray(t,r)).__proto__=a.prototype;else{var i=r-t;n=new a(i,void 0);for(var o=0;o<i;++o)n[o]=this[o+t]}return n},a.prototype.readUIntLE=function(t,r,n){t|=0,r|=0,n||B(t,r,this.length);for(var e=this[t],i=1,o=0;++o<r&&(i*=256);)e+=this[t+o]*i;return e},a.prototype.readUIntBE=function(t,r,n){t|=0,r|=0,n||B(t,r,this.length);for(var e=this[t+--r],i=1;r>0&&(i*=256);)e+=this[t+--r]*i;return e},a.prototype.readUInt8=function(t,r){return r||B(t,1,this.length),this[t]},a.prototype.readUInt16LE=function(t,r){return r||B(t,2,this.length),this[t]|this[t+1]<<8},a.prototype.readUInt16BE=function(t,r){return r||B(t,2,this.length),this[t]<<8|this[t+1]},a.prototype.readUInt32LE=function(t,r){return r||B(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},a.prototype.readUInt32BE=function(t,r){return r||B(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},a.prototype.readIntLE=function(t,r,n){t|=0,r|=0,n||B(t,r,this.length);for(var e=this[t],i=1,o=0;++o<r&&(i*=256);)e+=this[t+o]*i;return e>=(i*=128)&&(e-=Math.pow(2,8*r)),e},a.prototype.readIntBE=function(t,r,n){t|=0,r|=0,n||B(t,r,this.length);for(var e=r,i=1,o=this[t+--e];e>0&&(i*=256);)o+=this[t+--e]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*r)),o},a.prototype.readInt8=function(t,r){return r||B(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},a.prototype.readInt16LE=function(t,r){r||B(t,2,this.length);var n=this[t]|this[t+1]<<8;return 32768&n?4294901760|n:n},a.prototype.readInt16BE=function(t,r){r||B(t,2,this.length);var n=this[t+1]|this[t]<<8;return 32768&n?4294901760|n:n},a.prototype.readInt32LE=function(t,r){return r||B(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},a.prototype.readInt32BE=function(t,r){return r||B(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},a.prototype.readFloatLE=function(t,r){return r||B(t,4,this.length),i.read(this,t,!0,23,4)},a.prototype.readFloatBE=function(t,r){return r||B(t,4,this.length),i.read(this,t,!1,23,4)},a.prototype.readDoubleLE=function(t,r){return r||B(t,8,this.length),i.read(this,t,!0,52,8)},a.prototype.readDoubleBE=function(t,r){return r||B(t,8,this.length),i.read(this,t,!1,52,8)},a.prototype.writeUIntLE=function(t,r,n,e){(t=+t,r|=0,n|=0,e)||P(this,t,r,n,Math.pow(2,8*n)-1,0);var i=1,o=0;for(this[r]=255&t;++o<n&&(i*=256);)this[r+o]=t/i&255;return r+n},a.prototype.writeUIntBE=function(t,r,n,e){(t=+t,r|=0,n|=0,e)||P(this,t,r,n,Math.pow(2,8*n)-1,0);var i=n-1,o=1;for(this[r+i]=255&t;--i>=0&&(o*=256);)this[r+i]=t/o&255;return r+n},a.prototype.writeUInt8=function(t,r,n){return t=+t,r|=0,n||P(this,t,r,1,255,0),a.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),this[r]=255&t,r+1},a.prototype.writeUInt16LE=function(t,r,n){return t=+t,r|=0,n||P(this,t,r,2,65535,0),a.TYPED_ARRAY_SUPPORT?(this[r]=255&t,this[r+1]=t>>>8):C(this,t,r,!0),r+2},a.prototype.writeUInt16BE=function(t,r,n){return t=+t,r|=0,n||P(this,t,r,2,65535,0),a.TYPED_ARRAY_SUPPORT?(this[r]=t>>>8,this[r+1]=255&t):C(this,t,r,!1),r+2},a.prototype.writeUInt32LE=function(t,r,n){return t=+t,r|=0,n||P(this,t,r,4,4294967295,0),a.TYPED_ARRAY_SUPPORT?(this[r+3]=t>>>24,this[r+2]=t>>>16,this[r+1]=t>>>8,this[r]=255&t):I(this,t,r,!0),r+4},a.prototype.writeUInt32BE=function(t,r,n){return t=+t,r|=0,n||P(this,t,r,4,4294967295,0),a.TYPED_ARRAY_SUPPORT?(this[r]=t>>>24,this[r+1]=t>>>16,this[r+2]=t>>>8,this[r+3]=255&t):I(this,t,r,!1),r+4},a.prototype.writeIntLE=function(t,r,n,e){if(t=+t,r|=0,!e){var i=Math.pow(2,8*n-1);P(this,t,r,n,i-1,-i)}var o=0,u=1,s=0;for(this[r]=255&t;++o<n&&(u*=256);)t<0&&0===s&&0!==this[r+o-1]&&(s=1),this[r+o]=(t/u>>0)-s&255;return r+n},a.prototype.writeIntBE=function(t,r,n,e){if(t=+t,r|=0,!e){var i=Math.pow(2,8*n-1);P(this,t,r,n,i-1,-i)}var o=n-1,u=1,s=0;for(this[r+o]=255&t;--o>=0&&(u*=256);)t<0&&0===s&&0!==this[r+o+1]&&(s=1),this[r+o]=(t/u>>0)-s&255;return r+n},a.prototype.writeInt8=function(t,r,n){return t=+t,r|=0,n||P(this,t,r,1,127,-128),a.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),t<0&&(t=255+t+1),this[r]=255&t,r+1},a.prototype.writeInt16LE=function(t,r,n){return t=+t,r|=0,n||P(this,t,r,2,32767,-32768),a.TYPED_ARRAY_SUPPORT?(this[r]=255&t,this[r+1]=t>>>8):C(this,t,r,!0),r+2},a.prototype.writeInt16BE=function(t,r,n){return t=+t,r|=0,n||P(this,t,r,2,32767,-32768),a.TYPED_ARRAY_SUPPORT?(this[r]=t>>>8,this[r+1]=255&t):C(this,t,r,!1),r+2},a.prototype.writeInt32LE=function(t,r,n){return t=+t,r|=0,n||P(this,t,r,4,**********,-**********),a.TYPED_ARRAY_SUPPORT?(this[r]=255&t,this[r+1]=t>>>8,this[r+2]=t>>>16,this[r+3]=t>>>24):I(this,t,r,!0),r+4},a.prototype.writeInt32BE=function(t,r,n){return t=+t,r|=0,n||P(this,t,r,4,**********,-**********),t<0&&(t=4294967295+t+1),a.TYPED_ARRAY_SUPPORT?(this[r]=t>>>24,this[r+1]=t>>>16,this[r+2]=t>>>8,this[r+3]=255&t):I(this,t,r,!1),r+4},a.prototype.writeFloatLE=function(t,r,n){return L(this,t,r,!0,n)},a.prototype.writeFloatBE=function(t,r,n){return L(this,t,r,!1,n)},a.prototype.writeDoubleLE=function(t,r,n){return N(this,t,r,!0,n)},a.prototype.writeDoubleBE=function(t,r,n){return N(this,t,r,!1,n)},a.prototype.copy=function(t,r,n,e){if(n||(n=0),e||0===e||(e=this.length),r>=t.length&&(r=t.length),r||(r=0),e>0&&e<n&&(e=n),e===n)return 0;if(0===t.length||0===this.length)return 0;if(r<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("sourceStart out of bounds");if(e<0)throw new RangeError("sourceEnd out of bounds");e>this.length&&(e=this.length),t.length-r<e-n&&(e=t.length-r+n);var i,o=e-n;if(this===t&&n<r&&r<e)for(i=o-1;i>=0;--i)t[i+r]=this[i+n];else if(o<1e3||!a.TYPED_ARRAY_SUPPORT)for(i=0;i<o;++i)t[i+r]=this[i+n];else Uint8Array.prototype.set.call(t,this.subarray(n,n+o),r);return o},a.prototype.fill=function(t,r,n,e){if("string"==typeof t){if("string"==typeof r?(e=r,r=0,n=this.length):"string"==typeof n&&(e=n,n=this.length),1===t.length){var i=t.charCodeAt(0);i<256&&(t=i)}if(void 0!==e&&"string"!=typeof e)throw new TypeError("encoding must be a string");if("string"==typeof e&&!a.isEncoding(e))throw new TypeError("Unknown encoding: "+e)}else"number"==typeof t&&(t&=255);if(r<0||this.length<r||this.length<n)throw new RangeError("Out of range index");if(n<=r)return this;var o;if(r>>>=0,n=void 0===n?this.length:n>>>0,t||(t=0),"number"==typeof t)for(o=r;o<n;++o)this[o]=t;else{var u=a.isBuffer(t)?t:D(new a(t,e).toString()),s=u.length;for(o=0;o<n-r;++o)this[o+r]=u[o%s]}return this};var U=/[^+\/0-9A-Za-z-_]/g;function W(t){return t<16?"0"+t.toString(16):t.toString(16)}function D(t,r){var n;r=r||1/0;for(var e=t.length,i=null,o=[],u=0;u<e;++u){if((n=t.charCodeAt(u))>55295&&n<57344){if(!i){if(n>56319){(r-=3)>-1&&o.push(239,191,189);continue}if(u+1===e){(r-=3)>-1&&o.push(239,191,189);continue}i=n;continue}if(n<56320){(r-=3)>-1&&o.push(239,191,189),i=n;continue}n=65536+(i-55296<<10|n-56320)}else i&&(r-=3)>-1&&o.push(239,191,189);if(i=null,n<128){if((r-=1)<0)break;o.push(n)}else if(n<2048){if((r-=2)<0)break;o.push(n>>6|192,63&n|128)}else if(n<65536){if((r-=3)<0)break;o.push(n>>12|224,n>>6&63|128,63&n|128)}else{if(!(n<1114112))throw new Error("Invalid code point");if((r-=4)<0)break;o.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}}return o}function F(t){return e.toByteArray(function(t){if((t=function(t){return t.trim?t.trim():t.replace(/^\s+|\s+$/g,"")}(t).replace(U,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function Y(t,r,n,e){for(var i=0;i<e&&!(i+n>=r.length||i>=t.length);++i)r[i+n]=t[i];return i}}).call(this,n(6))},function(t,r,n){"use strict";var e=n(7);n.o(e,"payloadId")&&n.d(r,"payloadId",(function(){return e.payloadId}));e.isNode},function(t,r,n){"use strict";n.d(r,"a",(function(){return e}));n(3),n(1);function e(){return Date.now()*Math.pow(10,3)+Math.floor(Math.random()*Math.pow(10,3))}},function(t,r,n){"use strict"},function(t,r,n){"use strict";n(16)},function(t,r,n){"use strict";n(17),n(4),n(18),n(19)},function(t,r){},function(t,r,n){"use strict";var e=n(4);e.a;e.a},function(t,r){},function(t,r,n){"use strict"},function(t,r,n){"use strict"},function(t,r,n){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.getWindowMetadata=void 0;const e=n(2);r.getWindowMetadata=function(){let t,r;try{t=e.getDocumentOrThrow(),r=e.getLocationOrThrow()}catch(t){return null}function n(...r){const n=t.getElementsByTagName("meta");for(let t=0;t<n.length;t++){const e=n[t],i=["itemprop","property","name"].map(t=>e.getAttribute(t)).filter(t=>!!t&&r.includes(t));if(i.length&&i){const t=e.getAttribute("content");if(t)return t}}return""}const i=function(){let r=n("name","og:site_name","og:title","twitter:title");return r||(r=t.title),r}();return{description:n("description","og:description","twitter:description","keywords"),url:r.origin,icons:function(){const n=t.getElementsByTagName("link"),e=[];for(let t=0;t<n.length;t++){const i=n[t],o=i.getAttribute("rel");if(o&&o.toLowerCase().indexOf("icon")>-1){const t=i.getAttribute("href");if(t)if(-1===t.toLowerCase().indexOf("https:")&&-1===t.toLowerCase().indexOf("http:")&&0!==t.indexOf("//")){let n=r.protocol+"//"+r.host;if(0===t.indexOf("/"))n+=t;else{const e=r.pathname.split("/");e.pop();n+=e.join("/")+"/"+t}e.push(n)}else if(0===t.indexOf("//")){const n=r.protocol+t;e.push(n)}else e.push(t)}}return e}(),name:i}}},function(t,r,n){"use strict";(function(t){n.d(r,"a",(function(){return l}));var e=function(){for(var t=0,r=0,n=arguments.length;r<n;r++)t+=arguments[r].length;var e=Array(t),i=0;for(r=0;r<n;r++)for(var o=arguments[r],u=0,s=o.length;u<s;u++,i++)e[i]=o[u];return e},i=function(t,r,n){this.name=t,this.version=r,this.os=n,this.type="browser"},o=function(r){this.version=r,this.type="node",this.name="node",this.os=t.platform},u=function(t,r,n,e){this.name=t,this.version=r,this.os=n,this.bot=e,this.type="bot-device"},s=function(){this.type="bot",this.bot=!0,this.name="bot",this.version=null,this.os=null},a=function(){this.type="react-native",this.name="react-native",this.version=null,this.os=null},h=/(nuhk|Googlebot|Yammybot|Openbot|Slurp|MSNBot|Ask\ Jeeves\/Teoma|ia_archiver)/,f=[["aol",/AOLShield\/([0-9\._]+)/],["edge",/Edge\/([0-9\._]+)/],["edge-ios",/EdgiOS\/([0-9\._]+)/],["yandexbrowser",/YaBrowser\/([0-9\._]+)/],["kakaotalk",/KAKAOTALK\s([0-9\.]+)/],["samsung",/SamsungBrowser\/([0-9\.]+)/],["silk",/\bSilk\/([0-9._-]+)\b/],["miui",/MiuiBrowser\/([0-9\.]+)$/],["beaker",/BeakerBrowser\/([0-9\.]+)/],["edge-chromium",/EdgA?\/([0-9\.]+)/],["chromium-webview",/(?!Chrom.*OPR)wv\).*Chrom(?:e|ium)\/([0-9\.]+)(:?\s|$)/],["chrome",/(?!Chrom.*OPR)Chrom(?:e|ium)\/([0-9\.]+)(:?\s|$)/],["phantomjs",/PhantomJS\/([0-9\.]+)(:?\s|$)/],["crios",/CriOS\/([0-9\.]+)(:?\s|$)/],["firefox",/Firefox\/([0-9\.]+)(?:\s|$)/],["fxios",/FxiOS\/([0-9\.]+)/],["opera-mini",/Opera Mini.*Version\/([0-9\.]+)/],["opera",/Opera\/([0-9\.]+)(?:\s|$)/],["opera",/OPR\/([0-9\.]+)(:?\s|$)/],["ie",/Trident\/7\.0.*rv\:([0-9\.]+).*\).*Gecko$/],["ie",/MSIE\s([0-9\.]+);.*Trident\/[4-7].0/],["ie",/MSIE\s(7\.0)/],["bb10",/BB10;\sTouch.*Version\/([0-9\.]+)/],["android",/Android\s([0-9\.]+)/],["ios",/Version\/([0-9\._]+).*Mobile.*Safari.*/],["safari",/Version\/([0-9\._]+).*Safari/],["facebook",/FBAV\/([0-9\.]+)/],["instagram",/Instagram\s([0-9\.]+)/],["ios-webview",/AppleWebKit\/([0-9\.]+).*Mobile/],["ios-webview",/AppleWebKit\/([0-9\.]+).*Gecko\)$/],["searchbot",/alexa|bot|crawl(er|ing)|facebookexternalhit|feedburner|google web preview|nagios|postrank|pingdom|slurp|spider|yahoo!|yandex/]],c=[["iOS",/iP(hone|od|ad)/],["Android OS",/Android/],["BlackBerry OS",/BlackBerry|BB10/],["Windows Mobile",/IEMobile/],["Amazon OS",/Kindle/],["Windows 3.11",/Win16/],["Windows 95",/(Windows 95)|(Win95)|(Windows_95)/],["Windows 98",/(Windows 98)|(Win98)/],["Windows 2000",/(Windows NT 5.0)|(Windows 2000)/],["Windows XP",/(Windows NT 5.1)|(Windows XP)/],["Windows Server 2003",/(Windows NT 5.2)/],["Windows Vista",/(Windows NT 6.0)/],["Windows 7",/(Windows NT 6.1)/],["Windows 8",/(Windows NT 6.2)/],["Windows 8.1",/(Windows NT 6.3)/],["Windows 10",/(Windows NT 10.0)/],["Windows ME",/Windows ME/],["Open BSD",/OpenBSD/],["Sun OS",/SunOS/],["Chrome OS",/CrOS/],["Linux",/(Linux)|(X11)/],["Mac OS",/(Mac_PowerPC)|(Macintosh)/],["QNX",/QNX/],["BeOS",/BeOS/],["OS/2",/OS\/2/]];function l(r){return r?p(r):"undefined"==typeof document&&"undefined"!=typeof navigator&&"ReactNative"===navigator.product?new a:"undefined"!=typeof navigator?p(navigator.userAgent):void 0!==t&&t.version?new o(t.version.slice(1)):null}function d(t){return""!==t&&f.reduce((function(r,n){var e=n[0],i=n[1];if(r)return r;var o=i.exec(t);return!!o&&[e,o]}),!1)}function p(t){var r=d(t);if(!r)return null;var n=r[0],o=r[1];if("searchbot"===n)return new s;var a=o[1]&&o[1].split(/[._]/).slice(0,3);a?a.length<3&&(a=e(a,function(t){for(var r=[],n=0;n<t;n++)r.push("0");return r}(3-a.length))):a=[];var f=a.join("."),l=function(t){for(var r=0,n=c.length;r<n;r++){var e=c[r],i=e[0];if(e[1].exec(t))return i}return null}(t),p=h.exec(t);return p&&p[1]?new u(n,f,l,p[1]):new i(n,f,l)}}).call(this,n(8))},function(t,r,n){(function(r){var e=n(9).strict;t.exports=function(t){if(e(t)){var n=r.from(t.buffer);return t.byteLength!==t.buffer.byteLength&&(n=n.slice(t.byteOffset,t.byteOffset+t.byteLength)),n}return r.from(t)}}).call(this,n(11).Buffer)},function(t,r,n){(function(e,i){var o;
/**
 * [js-sha3]{@link https://github.com/emn178/js-sha3}
 *
 * @version 0.8.0
 * <AUTHOR> Yi-Cyuan [<EMAIL>]
 * @copyright Chen, Yi-Cyuan 2015-2018
 * @license MIT
 */!function(){"use strict";var u="input is invalid type",s="object"==typeof window,a=s?window:{};a.JS_SHA3_NO_WINDOW&&(s=!1);var h=!s&&"object"==typeof self;!a.JS_SHA3_NO_NODE_JS&&"object"==typeof e&&e.versions&&e.versions.node?a=i:h&&(a=self);var f=!a.JS_SHA3_NO_COMMON_JS&&"object"==typeof t&&t.exports,c=n(34),l=!a.JS_SHA3_NO_ARRAY_BUFFER&&"undefined"!=typeof ArrayBuffer,d="0123456789abcdef".split(""),p=[4,1024,262144,67108864],m=[0,8,16,24],g=[1,0,32898,0,32906,**********,**********,**********,32907,0,**********,0,**********,**********,32777,**********,138,0,136,0,**********,0,**********,0,**********,0,139,**********,32905,**********,32771,**********,32770,**********,128,**********,32778,0,**********,**********,**********,**********,32896,**********,**********,0,**********,**********],y=[224,256,384,512],v=[128,256],w=["hex","buffer","arrayBuffer","array","digest"],M={128:168,256:136};!a.JS_SHA3_NO_NODE_JS&&Array.isArray||(Array.isArray=function(t){return"[object Array]"===Object.prototype.toString.call(t)}),!l||!a.JS_SHA3_NO_ARRAY_BUFFER_IS_VIEW&&ArrayBuffer.isView||(ArrayBuffer.isView=function(t){return"object"==typeof t&&t.buffer&&t.buffer.constructor===ArrayBuffer});for(var b=function(t,r,n){return function(e){return new L(t,r,t).update(e)[n]()}},_=function(t,r,n){return function(e,i){return new L(t,r,i).update(e)[n]()}},A=function(t,r,n){return function(r,e,i,o){return x["cshake"+t].update(r,e,i,o)[n]()}},S=function(t,r,n){return function(r,e,i,o){return x["kmac"+t].update(r,e,i,o)[n]()}},O=function(t,r,n,e){for(var i=0;i<w.length;++i){var o=w[i];t[o]=r(n,e,o)}return t},E=function(t,r){var n=b(t,r,"hex");return n.create=function(){return new L(t,r,t)},n.update=function(t){return n.create().update(t)},O(n,b,t,r)},k=[{name:"keccak",padding:[1,256,65536,16777216],bits:y,createMethod:E},{name:"sha3",padding:[6,1536,393216,100663296],bits:y,createMethod:E},{name:"shake",padding:[31,7936,2031616,520093696],bits:v,createMethod:function(t,r){var n=_(t,r,"hex");return n.create=function(n){return new L(t,r,n)},n.update=function(t,r){return n.create(r).update(t)},O(n,_,t,r)}},{name:"cshake",padding:p,bits:v,createMethod:function(t,r){var n=M[t],e=A(t,0,"hex");return e.create=function(e,i,o){return i||o?new L(t,r,e).bytepad([i,o],n):x["shake"+t].create(e)},e.update=function(t,r,n,i){return e.create(r,n,i).update(t)},O(e,A,t,r)}},{name:"kmac",padding:p,bits:v,createMethod:function(t,r){var n=M[t],e=S(t,0,"hex");return e.create=function(e,i,o){return new N(t,r,i).bytepad(["KMAC",o],n).bytepad([e],n)},e.update=function(t,r,n,i){return e.create(t,n,i).update(r)},O(e,S,t,r)}}],x={},T=[],R=0;R<k.length;++R)for(var B=k[R],P=B.bits,C=0;C<P.length;++C){var I=B.name+"_"+P[C];if(T.push(I),x[I]=B.createMethod(P[C],B.padding),"sha3"!==B.name){var j=B.name+P[C];T.push(j),x[j]=x[I]}}function L(t,r,n){this.blocks=[],this.s=[],this.padding=r,this.outputBits=n,this.reset=!0,this.finalized=!1,this.block=0,this.start=0,this.blockCount=1600-(t<<1)>>5,this.byteCount=this.blockCount<<2,this.outputBlocks=n>>5,this.extraBytes=(31&n)>>3;for(var e=0;e<50;++e)this.s[e]=0}function N(t,r,n){L.call(this,t,r,n)}L.prototype.update=function(t){if(this.finalized)throw new Error("finalize already called");var r,n=typeof t;if("string"!==n){if("object"!==n)throw new Error(u);if(null===t)throw new Error(u);if(l&&t.constructor===ArrayBuffer)t=new Uint8Array(t);else if(!(Array.isArray(t)||l&&ArrayBuffer.isView(t)))throw new Error(u);r=!0}for(var e,i,o=this.blocks,s=this.byteCount,a=t.length,h=this.blockCount,f=0,c=this.s;f<a;){if(this.reset)for(this.reset=!1,o[0]=this.block,e=1;e<h+1;++e)o[e]=0;if(r)for(e=this.start;f<a&&e<s;++f)o[e>>2]|=t[f]<<m[3&e++];else for(e=this.start;f<a&&e<s;++f)(i=t.charCodeAt(f))<128?o[e>>2]|=i<<m[3&e++]:i<2048?(o[e>>2]|=(192|i>>6)<<m[3&e++],o[e>>2]|=(128|63&i)<<m[3&e++]):i<55296||i>=57344?(o[e>>2]|=(224|i>>12)<<m[3&e++],o[e>>2]|=(128|i>>6&63)<<m[3&e++],o[e>>2]|=(128|63&i)<<m[3&e++]):(i=65536+((1023&i)<<10|1023&t.charCodeAt(++f)),o[e>>2]|=(240|i>>18)<<m[3&e++],o[e>>2]|=(128|i>>12&63)<<m[3&e++],o[e>>2]|=(128|i>>6&63)<<m[3&e++],o[e>>2]|=(128|63&i)<<m[3&e++]);if(this.lastByteIndex=e,e>=s){for(this.start=e-s,this.block=o[h],e=0;e<h;++e)c[e]^=o[e];U(c),this.reset=!0}else this.start=e}return this},L.prototype.encode=function(t,r){var n=255&t,e=1,i=[n];for(n=255&(t>>=8);n>0;)i.unshift(n),n=255&(t>>=8),++e;return r?i.push(e):i.unshift(e),this.update(i),i.length},L.prototype.encodeString=function(t){var r,n=typeof t;if("string"!==n){if("object"!==n)throw new Error(u);if(null===t)throw new Error(u);if(l&&t.constructor===ArrayBuffer)t=new Uint8Array(t);else if(!(Array.isArray(t)||l&&ArrayBuffer.isView(t)))throw new Error(u);r=!0}var e=0,i=t.length;if(r)e=i;else for(var o=0;o<t.length;++o){var s=t.charCodeAt(o);s<128?e+=1:s<2048?e+=2:s<55296||s>=57344?e+=3:(s=65536+((1023&s)<<10|1023&t.charCodeAt(++o)),e+=4)}return e+=this.encode(8*e),this.update(t),e},L.prototype.bytepad=function(t,r){for(var n=this.encode(r),e=0;e<t.length;++e)n+=this.encodeString(t[e]);var i=r-n%r,o=[];return o.length=i,this.update(o),this},L.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var t=this.blocks,r=this.lastByteIndex,n=this.blockCount,e=this.s;if(t[r>>2]|=this.padding[3&r],this.lastByteIndex===this.byteCount)for(t[0]=t[n],r=1;r<n+1;++r)t[r]=0;for(t[n-1]|=**********,r=0;r<n;++r)e[r]^=t[r];U(e)}},L.prototype.toString=L.prototype.hex=function(){this.finalize();for(var t,r=this.blockCount,n=this.s,e=this.outputBlocks,i=this.extraBytes,o=0,u=0,s="";u<e;){for(o=0;o<r&&u<e;++o,++u)t=n[o],s+=d[t>>4&15]+d[15&t]+d[t>>12&15]+d[t>>8&15]+d[t>>20&15]+d[t>>16&15]+d[t>>28&15]+d[t>>24&15];u%r==0&&(U(n),o=0)}return i&&(t=n[o],s+=d[t>>4&15]+d[15&t],i>1&&(s+=d[t>>12&15]+d[t>>8&15]),i>2&&(s+=d[t>>20&15]+d[t>>16&15])),s},L.prototype.arrayBuffer=function(){this.finalize();var t,r=this.blockCount,n=this.s,e=this.outputBlocks,i=this.extraBytes,o=0,u=0,s=this.outputBits>>3;t=i?new ArrayBuffer(e+1<<2):new ArrayBuffer(s);for(var a=new Uint32Array(t);u<e;){for(o=0;o<r&&u<e;++o,++u)a[u]=n[o];u%r==0&&U(n)}return i&&(a[o]=n[o],t=t.slice(0,s)),t},L.prototype.buffer=L.prototype.arrayBuffer,L.prototype.digest=L.prototype.array=function(){this.finalize();for(var t,r,n=this.blockCount,e=this.s,i=this.outputBlocks,o=this.extraBytes,u=0,s=0,a=[];s<i;){for(u=0;u<n&&s<i;++u,++s)t=s<<2,r=e[u],a[t]=255&r,a[t+1]=r>>8&255,a[t+2]=r>>16&255,a[t+3]=r>>24&255;s%n==0&&U(e)}return o&&(t=s<<2,r=e[u],a[t]=255&r,o>1&&(a[t+1]=r>>8&255),o>2&&(a[t+2]=r>>16&255)),a},N.prototype=new L,N.prototype.finalize=function(){return this.encode(this.outputBits,!0),L.prototype.finalize.call(this)};var U=function(t){var r,n,e,i,o,u,s,a,h,f,c,l,d,p,m,y,v,w,M,b,_,A,S,O,E,k,x,T,R,B,P,C,I,j,L,N,U,W,D,F,Y,q,z,$,Z,H,J,V,K,Q,X,G,tt,rt,nt,et,it,ot,ut,st,at,ht,ft;for(e=0;e<48;e+=2)i=t[0]^t[10]^t[20]^t[30]^t[40],o=t[1]^t[11]^t[21]^t[31]^t[41],u=t[2]^t[12]^t[22]^t[32]^t[42],s=t[3]^t[13]^t[23]^t[33]^t[43],a=t[4]^t[14]^t[24]^t[34]^t[44],h=t[5]^t[15]^t[25]^t[35]^t[45],f=t[6]^t[16]^t[26]^t[36]^t[46],c=t[7]^t[17]^t[27]^t[37]^t[47],r=(l=t[8]^t[18]^t[28]^t[38]^t[48])^(u<<1|s>>>31),n=(d=t[9]^t[19]^t[29]^t[39]^t[49])^(s<<1|u>>>31),t[0]^=r,t[1]^=n,t[10]^=r,t[11]^=n,t[20]^=r,t[21]^=n,t[30]^=r,t[31]^=n,t[40]^=r,t[41]^=n,r=i^(a<<1|h>>>31),n=o^(h<<1|a>>>31),t[2]^=r,t[3]^=n,t[12]^=r,t[13]^=n,t[22]^=r,t[23]^=n,t[32]^=r,t[33]^=n,t[42]^=r,t[43]^=n,r=u^(f<<1|c>>>31),n=s^(c<<1|f>>>31),t[4]^=r,t[5]^=n,t[14]^=r,t[15]^=n,t[24]^=r,t[25]^=n,t[34]^=r,t[35]^=n,t[44]^=r,t[45]^=n,r=a^(l<<1|d>>>31),n=h^(d<<1|l>>>31),t[6]^=r,t[7]^=n,t[16]^=r,t[17]^=n,t[26]^=r,t[27]^=n,t[36]^=r,t[37]^=n,t[46]^=r,t[47]^=n,r=f^(i<<1|o>>>31),n=c^(o<<1|i>>>31),t[8]^=r,t[9]^=n,t[18]^=r,t[19]^=n,t[28]^=r,t[29]^=n,t[38]^=r,t[39]^=n,t[48]^=r,t[49]^=n,p=t[0],m=t[1],H=t[11]<<4|t[10]>>>28,J=t[10]<<4|t[11]>>>28,T=t[20]<<3|t[21]>>>29,R=t[21]<<3|t[20]>>>29,st=t[31]<<9|t[30]>>>23,at=t[30]<<9|t[31]>>>23,q=t[40]<<18|t[41]>>>14,z=t[41]<<18|t[40]>>>14,j=t[2]<<1|t[3]>>>31,L=t[3]<<1|t[2]>>>31,y=t[13]<<12|t[12]>>>20,v=t[12]<<12|t[13]>>>20,V=t[22]<<10|t[23]>>>22,K=t[23]<<10|t[22]>>>22,B=t[33]<<13|t[32]>>>19,P=t[32]<<13|t[33]>>>19,ht=t[42]<<2|t[43]>>>30,ft=t[43]<<2|t[42]>>>30,rt=t[5]<<30|t[4]>>>2,nt=t[4]<<30|t[5]>>>2,N=t[14]<<6|t[15]>>>26,U=t[15]<<6|t[14]>>>26,w=t[25]<<11|t[24]>>>21,M=t[24]<<11|t[25]>>>21,Q=t[34]<<15|t[35]>>>17,X=t[35]<<15|t[34]>>>17,C=t[45]<<29|t[44]>>>3,I=t[44]<<29|t[45]>>>3,O=t[6]<<28|t[7]>>>4,E=t[7]<<28|t[6]>>>4,et=t[17]<<23|t[16]>>>9,it=t[16]<<23|t[17]>>>9,W=t[26]<<25|t[27]>>>7,D=t[27]<<25|t[26]>>>7,b=t[36]<<21|t[37]>>>11,_=t[37]<<21|t[36]>>>11,G=t[47]<<24|t[46]>>>8,tt=t[46]<<24|t[47]>>>8,$=t[8]<<27|t[9]>>>5,Z=t[9]<<27|t[8]>>>5,k=t[18]<<20|t[19]>>>12,x=t[19]<<20|t[18]>>>12,ot=t[29]<<7|t[28]>>>25,ut=t[28]<<7|t[29]>>>25,F=t[38]<<8|t[39]>>>24,Y=t[39]<<8|t[38]>>>24,A=t[48]<<14|t[49]>>>18,S=t[49]<<14|t[48]>>>18,t[0]=p^~y&w,t[1]=m^~v&M,t[10]=O^~k&T,t[11]=E^~x&R,t[20]=j^~N&W,t[21]=L^~U&D,t[30]=$^~H&V,t[31]=Z^~J&K,t[40]=rt^~et&ot,t[41]=nt^~it&ut,t[2]=y^~w&b,t[3]=v^~M&_,t[12]=k^~T&B,t[13]=x^~R&P,t[22]=N^~W&F,t[23]=U^~D&Y,t[32]=H^~V&Q,t[33]=J^~K&X,t[42]=et^~ot&st,t[43]=it^~ut&at,t[4]=w^~b&A,t[5]=M^~_&S,t[14]=T^~B&C,t[15]=R^~P&I,t[24]=W^~F&q,t[25]=D^~Y&z,t[34]=V^~Q&G,t[35]=K^~X&tt,t[44]=ot^~st&ht,t[45]=ut^~at&ft,t[6]=b^~A&p,t[7]=_^~S&m,t[16]=B^~C&O,t[17]=P^~I&E,t[26]=F^~q&j,t[27]=Y^~z&L,t[36]=Q^~G&$,t[37]=X^~tt&Z,t[46]=st^~ht&rt,t[47]=at^~ft&nt,t[8]=A^~p&y,t[9]=S^~m&v,t[18]=C^~O&k,t[19]=I^~E&x,t[28]=q^~j&N,t[29]=z^~L&U,t[38]=G^~$&H,t[39]=tt^~Z&J,t[48]=ht^~rt&et,t[49]=ft^~nt&it,t[0]^=g[e],t[1]^=g[e+1]};if(f)t.exports=x;else{for(R=0;R<T.length;++R)a[T[R]]=x[T[R]];c&&(void 0===(o=function(){return x}.call(r,n,r,t))||(t.exports=o))}}()}).call(this,n(8),n(6))},function(t,r,n){"use strict";n(1),n(3);var e=n(12);n.o(e,"payloadId")&&n.d(r,"payloadId",(function(){return e.payloadId}));var i=n(13);n.d(r,"payloadId",(function(){return i.a}));n(14),n(15),n(20),n(21)},function(t,r,n){"use strict";(function(t){Object.defineProperty(r,"__esModule",{value:!0});const e=n(28),i=n(42),o=e.__importDefault(n(40)),u=void 0!==t.WebSocket?t.WebSocket:n(41);r.default=class{constructor(t){if(this.opts=t,this._queue=[],this._events=[],this._subscriptions=[],this._protocol=t.protocol,this._version=t.version,this._url="",this._netMonitor=null,this._socket=null,this._nextSocket=null,this._subscriptions=t.subscriptions||[],this._netMonitor=t.netMonitor||new o.default,!t.url||"string"!=typeof t.url)throw new Error("Missing or invalid WebSocket url");this._url=t.url,this._netMonitor.on("online",()=>this._socketCreate())}set readyState(t){}get readyState(){return this._socket?this._socket.readyState:-1}set connecting(t){}get connecting(){return 0===this.readyState}set connected(t){}get connected(){return 1===this.readyState}set closing(t){}get closing(){return 2===this.readyState}set closed(t){}get closed(){return 3===this.readyState}open(){this._socketCreate()}close(){this._socketClose()}send(t,r,n){if(!r||"string"!=typeof r)throw new Error("Missing or invalid topic field");this._socketSend({topic:r,type:"pub",payload:t,silent:!!n})}subscribe(t){this._socketSend({topic:t,type:"sub",payload:"",silent:!0})}on(t,r){this._events.push({event:t,callback:r})}_socketCreate(){if(this._nextSocket)return;const t=function(t,r,n){var e,o;const u=(t.startsWith("https")?t.replace("https","wss"):t.startsWith("http")?t.replace("http","ws"):t).split("?"),s=(0,i.isBrowser)()?{protocol:r,version:n,env:"browser",host:(null===(e=(0,i.getLocation)())||void 0===e?void 0:e.host)||""}:{protocol:r,version:n,env:(null===(o=(0,i.detectEnv)())||void 0===o?void 0:o.name)||""},a=(0,i.appendToQueryString)((0,i.getQueryString)(u[1]||""),s);return u[0]+"?"+a}(this._url,this._protocol,this._version);if(this._nextSocket=new u(t),!this._nextSocket)throw new Error("Failed to create socket");this._nextSocket.onmessage=t=>this._socketReceive(t),this._nextSocket.onopen=()=>this._socketOpen(),this._nextSocket.onerror=t=>this._socketError(t),this._nextSocket.onclose=()=>{setTimeout(()=>{this._nextSocket=null,this._socketCreate()},1e3)}}_socketOpen(){this._socketClose(),this._socket=this._nextSocket,this._nextSocket=null,this._queueSubscriptions(),this._pushQueue()}_socketClose(){this._socket&&(this._socket.onclose=()=>{},this._socket.close())}_socketSend(t){const r=JSON.stringify(t);this._socket&&1===this._socket.readyState?this._socket.send(r):(this._setToQueue(t),this._socketCreate())}_socketReceive(t){return e.__awaiter(this,void 0,void 0,(function*(){let r;try{r=JSON.parse(t.data)}catch(t){return}if(this._socketSend({topic:r.topic,type:"ack",payload:"",silent:!0}),this._socket&&1===this._socket.readyState){const t=this._events.filter(t=>"message"===t.event);t&&t.length&&t.forEach(t=>t.callback(r))}}))}_socketError(t){const r=this._events.filter(t=>"error"===t.event);r&&r.length&&r.forEach(r=>r.callback(t))}_queueSubscriptions(){this._subscriptions.forEach(t=>this._queue.push({topic:t,type:"sub",payload:"",silent:!0})),this._subscriptions=this.opts.subscriptions||[]}_setToQueue(t){this._queue.push(t)}_pushQueue(){this._queue.forEach(t=>this._socketSend(t)),this._queue=[]}}}).call(this,n(6))},function(t,r,n){"use strict";n.r(r),n.d(r,"__extends",(function(){return i})),n.d(r,"__assign",(function(){return o})),n.d(r,"__rest",(function(){return u})),n.d(r,"__decorate",(function(){return s})),n.d(r,"__param",(function(){return a})),n.d(r,"__metadata",(function(){return h})),n.d(r,"__awaiter",(function(){return f})),n.d(r,"__generator",(function(){return c})),n.d(r,"__exportStar",(function(){return l})),n.d(r,"__values",(function(){return d})),n.d(r,"__read",(function(){return p})),n.d(r,"__spread",(function(){return m})),n.d(r,"__await",(function(){return g})),n.d(r,"__asyncGenerator",(function(){return y})),n.d(r,"__asyncDelegator",(function(){return v})),n.d(r,"__asyncValues",(function(){return w})),n.d(r,"__makeTemplateObject",(function(){return M})),n.d(r,"__importStar",(function(){return b})),n.d(r,"__importDefault",(function(){return _}));
/*! *****************************************************************************
Copyright (c) Microsoft Corporation. All rights reserved.
Licensed under the Apache License, Version 2.0 (the "License"); you may not use
this file except in compliance with the License. You may obtain a copy of the
License at http://www.apache.org/licenses/LICENSE-2.0

THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
MERCHANTABLITY OR NON-INFRINGEMENT.

See the Apache Version 2.0 License for specific language governing permissions
and limitations under the License.
***************************************************************************** */
var e=function(t,r){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var n in r)r.hasOwnProperty(n)&&(t[n]=r[n])})(t,r)};function i(t,r){function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}var o=function(){return(o=Object.assign||function(t){for(var r,n=1,e=arguments.length;n<e;n++)for(var i in r=arguments[n])Object.prototype.hasOwnProperty.call(r,i)&&(t[i]=r[i]);return t}).apply(this,arguments)};function u(t,r){var n={};for(var e in t)Object.prototype.hasOwnProperty.call(t,e)&&r.indexOf(e)<0&&(n[e]=t[e]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(e=Object.getOwnPropertySymbols(t);i<e.length;i++)r.indexOf(e[i])<0&&(n[e[i]]=t[e[i]])}return n}function s(t,r,n,e){var i,o=arguments.length,u=o<3?r:null===e?e=Object.getOwnPropertyDescriptor(r,n):e;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)u=Reflect.decorate(t,r,n,e);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(u=(o<3?i(u):o>3?i(r,n,u):i(r,n))||u);return o>3&&u&&Object.defineProperty(r,n,u),u}function a(t,r){return function(n,e){r(n,e,t)}}function h(t,r){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(t,r)}function f(t,r,n,e){return new(n||(n=Promise))((function(i,o){function u(t){try{a(e.next(t))}catch(t){o(t)}}function s(t){try{a(e.throw(t))}catch(t){o(t)}}function a(t){t.done?i(t.value):new n((function(r){r(t.value)})).then(u,s)}a((e=e.apply(t,r||[])).next())}))}function c(t,r){var n,e,i,o,u={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(o){return function(s){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;u;)try{if(n=1,e&&(i=2&o[0]?e.return:o[0]?e.throw||((i=e.return)&&i.call(e),0):e.next)&&!(i=i.call(e,o[1])).done)return i;switch(e=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return u.label++,{value:o[1],done:!1};case 5:u.label++,e=o[1],o=[0];continue;case 7:o=u.ops.pop(),u.trys.pop();continue;default:if(!(i=u.trys,(i=i.length>0&&i[i.length-1])||6!==o[0]&&2!==o[0])){u=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){u.label=o[1];break}if(6===o[0]&&u.label<i[1]){u.label=i[1],i=o;break}if(i&&u.label<i[2]){u.label=i[2],u.ops.push(o);break}i[2]&&u.ops.pop(),u.trys.pop();continue}o=r.call(t,u)}catch(t){o=[6,t],e=0}finally{n=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,s])}}}function l(t,r){for(var n in t)r.hasOwnProperty(n)||(r[n]=t[n])}function d(t){var r="function"==typeof Symbol&&t[Symbol.iterator],n=0;return r?r.call(t):{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}}}function p(t,r){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var e,i,o=n.call(t),u=[];try{for(;(void 0===r||r-- >0)&&!(e=o.next()).done;)u.push(e.value)}catch(t){i={error:t}}finally{try{e&&!e.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return u}function m(){for(var t=[],r=0;r<arguments.length;r++)t=t.concat(p(arguments[r]));return t}function g(t){return this instanceof g?(this.v=t,this):new g(t)}function y(t,r,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e,i=n.apply(t,r||[]),o=[];return e={},u("next"),u("throw"),u("return"),e[Symbol.asyncIterator]=function(){return this},e;function u(t){i[t]&&(e[t]=function(r){return new Promise((function(n,e){o.push([t,r,n,e])>1||s(t,r)}))})}function s(t,r){try{(n=i[t](r)).value instanceof g?Promise.resolve(n.value.v).then(a,h):f(o[0][2],n)}catch(t){f(o[0][3],t)}var n}function a(t){s("next",t)}function h(t){s("throw",t)}function f(t,r){t(r),o.shift(),o.length&&s(o[0][0],o[0][1])}}function v(t){var r,n;return r={},e("next"),e("throw",(function(t){throw t})),e("return"),r[Symbol.iterator]=function(){return this},r;function e(e,i){r[e]=t[e]?function(r){return(n=!n)?{value:g(t[e](r)),done:"return"===e}:i?i(r):r}:i}}function w(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r,n=t[Symbol.asyncIterator];return n?n.call(t):(t=d(t),r={},e("next"),e("throw"),e("return"),r[Symbol.asyncIterator]=function(){return this},r);function e(n){r[n]=t[n]&&function(r){return new Promise((function(e,i){(function(t,r,n,e){Promise.resolve(e).then((function(r){t({value:r,done:n})}),r)})(e,i,(r=t[n](r)).done,r.value)}))}}}function M(t,r){return Object.defineProperty?Object.defineProperty(t,"raw",{value:r}):t.raw=r,t}function b(t){if(t&&t.__esModule)return t;var r={};if(null!=t)for(var n in t)Object.hasOwnProperty.call(t,n)&&(r[n]=t[n]);return r.default=t,r}function _(t){return t&&t.__esModule?t:{default:t}}},function(t,r){t.exports=function(t){return t.webpackPolyfill||(t.deprecate=function(){},t.paths=[],t.children||(t.children=[]),Object.defineProperty(t,"loaded",{enumerable:!0,get:function(){return t.l}}),Object.defineProperty(t,"id",{enumerable:!0,get:function(){return t.i}}),t.webpackPolyfill=1),t}},function(t,r){},function(t,r,n){"use strict";r.byteLength=function(t){var r=h(t),n=r[0],e=r[1];return 3*(n+e)/4-e},r.toByteArray=function(t){var r,n,e=h(t),u=e[0],s=e[1],a=new o(function(t,r,n){return 3*(r+n)/4-n}(0,u,s)),f=0,c=s>0?u-4:u;for(n=0;n<c;n+=4)r=i[t.charCodeAt(n)]<<18|i[t.charCodeAt(n+1)]<<12|i[t.charCodeAt(n+2)]<<6|i[t.charCodeAt(n+3)],a[f++]=r>>16&255,a[f++]=r>>8&255,a[f++]=255&r;2===s&&(r=i[t.charCodeAt(n)]<<2|i[t.charCodeAt(n+1)]>>4,a[f++]=255&r);1===s&&(r=i[t.charCodeAt(n)]<<10|i[t.charCodeAt(n+1)]<<4|i[t.charCodeAt(n+2)]>>2,a[f++]=r>>8&255,a[f++]=255&r);return a},r.fromByteArray=function(t){for(var r,n=t.length,i=n%3,o=[],u=0,s=n-i;u<s;u+=16383)o.push(f(t,u,u+16383>s?s:u+16383));1===i?(r=t[n-1],o.push(e[r>>2]+e[r<<4&63]+"==")):2===i&&(r=(t[n-2]<<8)+t[n-1],o.push(e[r>>10]+e[r>>4&63]+e[r<<2&63]+"="));return o.join("")};for(var e=[],i=[],o="undefined"!=typeof Uint8Array?Uint8Array:Array,u="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",s=0,a=u.length;s<a;++s)e[s]=u[s],i[u.charCodeAt(s)]=s;function h(t){var r=t.length;if(r%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var n=t.indexOf("=");return-1===n&&(n=r),[n,n===r?0:4-n%4]}function f(t,r,n){for(var i,o,u=[],s=r;s<n;s+=3)i=(t[s]<<16&16711680)+(t[s+1]<<8&65280)+(255&t[s+2]),u.push(e[(o=i)>>18&63]+e[o>>12&63]+e[o>>6&63]+e[63&o]);return u.join("")}i["-".charCodeAt(0)]=62,i["_".charCodeAt(0)]=63},function(t,r){
/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */
r.read=function(t,r,n,e,i){var o,u,s=8*i-e-1,a=(1<<s)-1,h=a>>1,f=-7,c=n?i-1:0,l=n?-1:1,d=t[r+c];for(c+=l,o=d&(1<<-f)-1,d>>=-f,f+=s;f>0;o=256*o+t[r+c],c+=l,f-=8);for(u=o&(1<<-f)-1,o>>=-f,f+=e;f>0;u=256*u+t[r+c],c+=l,f-=8);if(0===o)o=1-h;else{if(o===a)return u?NaN:1/0*(d?-1:1);u+=Math.pow(2,e),o-=h}return(d?-1:1)*u*Math.pow(2,o-e)},r.write=function(t,r,n,e,i,o){var u,s,a,h=8*o-i-1,f=(1<<h)-1,c=f>>1,l=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,d=e?0:o-1,p=e?1:-1,m=r<0||0===r&&1/r<0?1:0;for(r=Math.abs(r),isNaN(r)||r===1/0?(s=isNaN(r)?1:0,u=f):(u=Math.floor(Math.log(r)/Math.LN2),r*(a=Math.pow(2,-u))<1&&(u--,a*=2),(r+=u+c>=1?l/a:l*Math.pow(2,1-c))*a>=2&&(u++,a/=2),u+c>=f?(s=0,u=f):u+c>=1?(s=(r*a-1)*Math.pow(2,i),u+=c):(s=r*Math.pow(2,c-1)*Math.pow(2,i),u=0));i>=8;t[n+d]=255&s,d+=p,s/=256,i-=8);for(u=u<<i|s,h+=i;h>0;t[n+d]=255&u,d+=p,u/=256,h-=8);t[n+d-p]|=128*m}},function(t,r){var n={}.toString;t.exports=Array.isArray||function(t){return"[object Array]"==n.call(t)}},function(t,r){(function(r){t.exports=r}).call(this,{})},function(t,r,n){"use strict";(function(t){function n(){return(null==t?void 0:t.crypto)||(null==t?void 0:t.msCrypto)||{}}function e(){const t=n();return t.subtle||t.webkitSubtle}Object.defineProperty(r,"__esModule",{value:!0}),r.isBrowserCryptoAvailable=r.getSubtleCrypto=r.getBrowerCrypto=void 0,r.getBrowerCrypto=n,r.getSubtleCrypto=e,r.isBrowserCryptoAvailable=function(){return!!n()&&!!e()}}).call(this,n(6))},function(t,r,n){"use strict";(function(t){function n(){return"undefined"==typeof document&&"undefined"!=typeof navigator&&"ReactNative"===navigator.product}function e(){return void 0!==t&&void 0!==t.versions&&void 0!==t.versions.node}Object.defineProperty(r,"__esModule",{value:!0}),r.isBrowser=r.isNode=r.isReactNative=void 0,r.isReactNative=n,r.isNode=e,r.isBrowser=function(){return!n()&&!e()}}).call(this,n(8))},function(t,r,n){"use strict";t.exports=t=>encodeURIComponent(t).replace(/[!'()*]/g,t=>"%"+t.charCodeAt(0).toString(16).toUpperCase())},function(t,r,n){"use strict";var e=new RegExp("%[a-f0-9]{2}","gi"),i=new RegExp("(%[a-f0-9]{2})+","gi");function o(t,r){try{return decodeURIComponent(t.join(""))}catch(t){}if(1===t.length)return t;r=r||1;var n=t.slice(0,r),e=t.slice(r);return Array.prototype.concat.call([],o(n),o(e))}function u(t){try{return decodeURIComponent(t)}catch(i){for(var r=t.match(e),n=1;n<r.length;n++)r=(t=o(r,n).join("")).match(e);return t}}t.exports=function(t){if("string"!=typeof t)throw new TypeError("Expected `encodedURI` to be of type `string`, got `"+typeof t+"`");try{return t=t.replace(/\+/g," "),decodeURIComponent(t)}catch(r){return function(t){for(var r={"%FE%FF":"��","%FF%FE":"��"},n=i.exec(t);n;){try{r[n[0]]=decodeURIComponent(n[0])}catch(t){var e=u(n[0]);e!==n[0]&&(r[n[0]]=e)}n=i.exec(t)}r["%C2"]="�";for(var o=Object.keys(r),s=0;s<o.length;s++){var a=o[s];t=t.replace(new RegExp(a,"g"),r[a])}return t}(t)}}},function(t,r,n){"use strict";t.exports=(t,r)=>{if("string"!=typeof t||"string"!=typeof r)throw new TypeError("Expected the arguments to be of type `string`");if(""===r)return[t];const n=t.indexOf(r);return-1===n?[t]:[t.slice(0,n),t.slice(n+r.length)]}},function(t,r,n){"use strict";Object.defineProperty(r,"__esModule",{value:!0});r.default=class{constructor(){this._eventEmitters=[],"undefined"!=typeof window&&void 0!==window.addEventListener&&(window.addEventListener("online",()=>this.trigger("online")),window.addEventListener("offline",()=>this.trigger("offline")))}on(t,r){this._eventEmitters.push({event:t,callback:r})}trigger(t){let r=[];t&&(r=this._eventEmitters.filter(r=>r.event===t)),r.forEach(t=>{t.callback()})}}},function(t,r,n){"use strict";t.exports=function(){throw new Error("ws does not work in the browser. Browser clients must use the native WebSocket object")}},function(t,r,n){"use strict";n.r(r);var e=n(22),i=n(2),o=n(23);function u(t){return Object(o.a)(t)}function s(){const t=u();return t&&t.os?t.os:void 0}function a(){const t=s();return!!t&&t.toLowerCase().includes("android")}function h(){const t=s();return!!t&&(t.toLowerCase().includes("ios")||t.toLowerCase().includes("mac")&&navigator.maxTouchPoints>1)}function f(){return!!s()&&(a()||h())}function c(){const t=u();return!(!t||!t.name)&&"node"===t.name.toLowerCase()}function l(){return!c()&&!!v()}const d=i.getFromWindow,p=i.getFromWindowOrThrow,m=i.getDocumentOrThrow,g=i.getDocument,y=i.getNavigatorOrThrow,v=i.getNavigator,w=i.getLocationOrThrow,M=i.getLocation,b=i.getCryptoOrThrow,_=i.getCrypto,A=i.getLocalStorageOrThrow,S=i.getLocalStorage;function O(){return e.getWindowMetadata()}const E=function(t){if("string"!=typeof t)throw new Error("Cannot safe json parse value of type "+typeof t);try{return JSON.parse(t)}catch(r){return t}},k=function(t){return"string"==typeof t?t:JSON.stringify(t)};function x(t,r){const n=k(r),e=S();e&&e.setItem(t,n)}function T(t){let r=null,n=null;const e=S();return e&&(n=e.getItem(t)),r=n?E(n):n,r}function R(t){const r=S();r&&r.removeItem(t)}function B(t,r){const n=encodeURIComponent(t);return r.universalLink?`${r.universalLink}/wc?uri=${n}`:r.deepLink?`${r.deepLink}${r.deepLink.endsWith(":")?"//":"/"}wc?uri=${n}`:""}function P(t){const r=t.href.split("?")[0];x("WALLETCONNECT_DEEPLINK_CHOICE",Object.assign(Object.assign({},t),{href:r}))}function C(t,r){return t.filter(t=>t.name.toLowerCase().includes(r.toLowerCase()))[0]}function I(t,r){let n=t;return r&&(n=r.map(r=>C(t,r)).filter(Boolean)),n}const j="https://registry.walletconnect.com";function L(){return j+"/api/v2/wallets"}function N(){return j+"/api/v2/dapps"}function U(t,r="mobile"){var n;return{name:t.name||"",shortName:t.metadata.shortName||"",color:t.metadata.colors.primary||"",logo:null!==(n=t.image_url.sm)&&void 0!==n?n:"",universalLink:t[r].universal||"",deepLink:t[r].native||""}}function W(t,r="mobile"){return Object.values(t).filter(t=>!!t[r].universal||!!t[r].native).map(t=>U(t,r))}const D=["session_request","session_update","exchange_key","connect","disconnect","display_uri","modal_closed","transport_open","transport_close","transport_error"],F=["eth_sendTransaction","eth_signTransaction","eth_sign","eth_signTypedData","eth_signTypedData_v1","eth_signTypedData_v2","eth_signTypedData_v3","eth_signTypedData_v4","personal_sign","wallet_addEthereumChain","wallet_switchEthereumChain","wallet_getPermissions","wallet_requestPermissions","wallet_registerOnboarding","wallet_watchAsset","wallet_scanQRCode"],Y=["eth_accounts","eth_chainId","net_version"],q={1:"mainnet",3:"ropsten",4:"rinkeby",5:"goerli",42:"kovan"};var z=n(5),$=n.n(z),Z=n(0);function H(t){return Z.b(new Uint8Array(t))}function J(t){return Z.e(new Uint8Array(t))}function V(t,r){return Z.c(new Uint8Array(t),!r)}function K(t){return Z.d(new Uint8Array(t))}function Q(...t){return Z.m(t.map(t=>Z.c(new Uint8Array(t))).join("")).buffer}function X(t){return Z.f(t).buffer}function G(t){return Z.i(t)}function tt(t,r){return Z.g(t,!r)}function rt(t){return Z.h(t)}function nt(...t){return Z.j(...t)}function et(t){return Z.y(t).buffer}function it(t){return Z.z(t)}function ot(t,r){return Z.A(t,!r)}function ut(t){return new $.a(t,10).toNumber()}function st(t){return Z.n(t)}function at(t){return Z.m(t).buffer}function ht(t){return Z.o(t)}function ft(t){return new $.a(Z.w(t),"hex").toNumber()}function ct(t){return Z.u(t)}function lt(t){return Z.t(t).buffer}function dt(t){return new $.a(t).toString()}function pt(t,r){const n=Z.w(Z.x(new $.a(t).toString(16)));return r?n:Z.a(n)}var mt=n(25);function gt(t){return Z.x(t)}function yt(t){return Z.a(t)}function vt(t){return Z.w(t)}function wt(t){return Z.v(Z.a(t))}const Mt=n(26).payloadId;function bt(){return((t,r)=>{for(r=t="";t++<36;r+=51*t&52?(15^t?8^Math.random()*(20^t?16:4):4).toString(16):"-");return r})()}function _t(){console.warn("DEPRECATION WARNING: This WalletConnect client library will be deprecated in favor of @walletconnect/client. Please check docs.walletconnect.org to learn more about this migration!")}function At(t,r){let n;const e=q[t];return e&&(n=`https://${e}.infura.io/v3/${r}`),n}function St(t,r){let n;const e=At(t,r.infuraId);return r.custom&&r.custom[t]?n=r.custom[t]:e&&(n=e),n}function Ot(t){return""===t||"string"==typeof t&&""===t.trim()}function Et(t){return!(t&&t.length)}function kt(t){return Z.q(t)}function xt(t){return Z.s(t)}function Tt(t){return Z.p(t)}function Rt(t){return Z.l(t)}function Bt(t){return Z.k(t)}function Pt(t,r){return Z.r(t,r)}function Ct(t){return"object"==typeof t.params}function It(t){return void 0!==t.method}function jt(t){return void 0!==t.result}function Lt(t){return void 0!==t.error}function Nt(t){return void 0!==t.event}function Ut(t){return D.includes(t)||t.startsWith("wc_")}function Wt(t){return!!t.method.startsWith("wc_")||!F.includes(t.method)}function Dt(t){t=Object(Z.w)(t.toLowerCase());const r=Object(Z.w)(Object(mt.keccak_256)(it(t)));let n="";for(let e=0;e<t.length;e++)parseInt(r[e],16)>7?n+=t[e].toUpperCase():n+=t[e];return Object(Z.a)(n)}const Ft=t=>!!t&&("0x"===t.toLowerCase().substring(0,2)&&(!!/^(0x)?[0-9a-f]{40}$/i.test(t)&&(!(!/^(0x)?[0-9a-f]{40}$/.test(t)&&!/^(0x)?[0-9A-F]{40}$/.test(t))||t===Dt(t))));function Yt(t){return Et(t)||Pt(t[0])||(t[0]=ot(t[0])),t}function qt(t){if(void 0!==t.type&&"0"!==t.type)return t;if(void 0===t.from||!Ft(t.from))throw new Error("Transaction object must include a valid 'from' value.");function r(t){let r=t;return("number"==typeof t||"string"==typeof t&&!Ot(t))&&(Pt(t)?"string"==typeof t&&(r=gt(t)):r=pt(t)),"string"==typeof r&&(r=wt(r)),r}const n={from:gt(t.from),to:void 0===t.to?void 0:gt(t.to),gasPrice:void 0===t.gasPrice?"":r(t.gasPrice),gas:void 0===t.gas?void 0===t.gasLimit?"":r(t.gasLimit):r(t.gas),value:void 0===t.value?"":r(t.value),nonce:void 0===t.nonce?"":r(t.nonce),data:void 0===t.data?"":gt(t.data)||"0x"},e=["gasPrice","gas","value","nonce"];return Object.keys(n).forEach(t=>{(void 0===n[t]||"string"==typeof n[t]&&!n[t].trim().length)&&e.includes(t)&&delete n[t]}),n}function zt(t,r){return async(...n)=>new Promise((e,i)=>{t.apply(r,[...n,(t,r)=>{null==t&&i(t),e(r)}])})}function $t(t){const r=t.message||"Failed or Rejected Request";let n=-32e3;if(t&&!t.code)switch(r){case"Parse error":n=-32700;break;case"Invalid request":n=-32600;break;case"Method not found":n=-32601;break;case"Invalid params":n=-32602;break;case"Internal error":n=-32603;break;default:n=-32e3}const e={code:n,message:r};return t.data&&(e.data=t.data),e}var Zt=n(10);function Ht(t){const r=-1!==t.indexOf("?")?t.indexOf("?"):void 0;return void 0!==r?t.substr(r):""}function Jt(t,r){let n=Vt(t);return n=Object.assign(Object.assign({},n),r),t=Kt(n)}function Vt(t){return Zt.parse(t)}function Kt(t){return Zt.stringify(t)}function Qt(t){return void 0!==t.bridge}function Xt(t){const r=t.indexOf(":"),n=-1!==t.indexOf("?")?t.indexOf("?"):void 0,e=t.substring(0,r);const i=function(t){const r=t.split("@");return{handshakeTopic:r[0],version:parseInt(r[1],10)}}(t.substring(r+1,n));const o=function(t){const r=Vt(t);return{key:r.key||"",bridge:r.bridge||""}}(void 0!==n?t.substr(n):"");return Object.assign(Object.assign({protocol:e},i),o)}n.d(r,"detectEnv",(function(){return u})),n.d(r,"detectOS",(function(){return s})),n.d(r,"isAndroid",(function(){return a})),n.d(r,"isIOS",(function(){return h})),n.d(r,"isMobile",(function(){return f})),n.d(r,"isNode",(function(){return c})),n.d(r,"isBrowser",(function(){return l})),n.d(r,"getFromWindow",(function(){return d})),n.d(r,"getFromWindowOrThrow",(function(){return p})),n.d(r,"getDocumentOrThrow",(function(){return m})),n.d(r,"getDocument",(function(){return g})),n.d(r,"getNavigatorOrThrow",(function(){return y})),n.d(r,"getNavigator",(function(){return v})),n.d(r,"getLocationOrThrow",(function(){return w})),n.d(r,"getLocation",(function(){return M})),n.d(r,"getCryptoOrThrow",(function(){return b})),n.d(r,"getCrypto",(function(){return _})),n.d(r,"getLocalStorageOrThrow",(function(){return A})),n.d(r,"getLocalStorage",(function(){return S})),n.d(r,"getClientMeta",(function(){return O})),n.d(r,"safeJsonParse",(function(){return E})),n.d(r,"safeJsonStringify",(function(){return k})),n.d(r,"setLocal",(function(){return x})),n.d(r,"getLocal",(function(){return T})),n.d(r,"removeLocal",(function(){return R})),n.d(r,"mobileLinkChoiceKey",(function(){return"WALLETCONNECT_DEEPLINK_CHOICE"})),n.d(r,"formatIOSMobile",(function(){return B})),n.d(r,"saveMobileLinkInfo",(function(){return P})),n.d(r,"getMobileRegistryEntry",(function(){return C})),n.d(r,"getMobileLinkRegistry",(function(){return I})),n.d(r,"getWalletRegistryUrl",(function(){return L})),n.d(r,"getDappRegistryUrl",(function(){return N})),n.d(r,"formatMobileRegistryEntry",(function(){return U})),n.d(r,"formatMobileRegistry",(function(){return W})),n.d(r,"reservedEvents",(function(){return D})),n.d(r,"signingMethods",(function(){return F})),n.d(r,"stateMethods",(function(){return Y})),n.d(r,"infuraNetworks",(function(){return q})),n.d(r,"convertArrayBufferToBuffer",(function(){return H})),n.d(r,"convertArrayBufferToUtf8",(function(){return J})),n.d(r,"convertArrayBufferToHex",(function(){return V})),n.d(r,"convertArrayBufferToNumber",(function(){return K})),n.d(r,"concatArrayBuffers",(function(){return Q})),n.d(r,"convertBufferToArrayBuffer",(function(){return X})),n.d(r,"convertBufferToUtf8",(function(){return G})),n.d(r,"convertBufferToHex",(function(){return tt})),n.d(r,"convertBufferToNumber",(function(){return rt})),n.d(r,"concatBuffers",(function(){return nt})),n.d(r,"convertUtf8ToArrayBuffer",(function(){return et})),n.d(r,"convertUtf8ToBuffer",(function(){return it})),n.d(r,"convertUtf8ToHex",(function(){return ot})),n.d(r,"convertUtf8ToNumber",(function(){return ut})),n.d(r,"convertHexToBuffer",(function(){return st})),n.d(r,"convertHexToArrayBuffer",(function(){return at})),n.d(r,"convertHexToUtf8",(function(){return ht})),n.d(r,"convertHexToNumber",(function(){return ft})),n.d(r,"convertNumberToBuffer",(function(){return ct})),n.d(r,"convertNumberToArrayBuffer",(function(){return lt})),n.d(r,"convertNumberToUtf8",(function(){return dt})),n.d(r,"convertNumberToHex",(function(){return pt})),n.d(r,"toChecksumAddress",(function(){return Dt})),n.d(r,"isValidAddress",(function(){return Ft})),n.d(r,"parsePersonalSign",(function(){return Yt})),n.d(r,"parseTransactionData",(function(){return qt})),n.d(r,"sanitizeHex",(function(){return gt})),n.d(r,"addHexPrefix",(function(){return yt})),n.d(r,"removeHexPrefix",(function(){return vt})),n.d(r,"removeHexLeadingZeros",(function(){return wt})),n.d(r,"payloadId",(function(){return Mt})),n.d(r,"uuid",(function(){return bt})),n.d(r,"logDeprecationWarning",(function(){return _t})),n.d(r,"getInfuraRpcUrl",(function(){return At})),n.d(r,"getRpcUrl",(function(){return St})),n.d(r,"promisify",(function(){return zt})),n.d(r,"formatRpcError",(function(){return $t})),n.d(r,"isWalletConnectSession",(function(){return Qt})),n.d(r,"parseWalletConnectUri",(function(){return Xt})),n.d(r,"getQueryString",(function(){return Ht})),n.d(r,"appendToQueryString",(function(){return Jt})),n.d(r,"parseQueryString",(function(){return Vt})),n.d(r,"formatQueryString",(function(){return Kt})),n.d(r,"isEmptyString",(function(){return Ot})),n.d(r,"isEmptyArray",(function(){return Et})),n.d(r,"isBuffer",(function(){return kt})),n.d(r,"isTypedArray",(function(){return xt})),n.d(r,"isArrayBuffer",(function(){return Tt})),n.d(r,"getType",(function(){return Rt})),n.d(r,"getEncoding",(function(){return Bt})),n.d(r,"isHexString",(function(){return Pt})),n.d(r,"isJsonRpcSubscription",(function(){return Ct})),n.d(r,"isJsonRpcRequest",(function(){return It})),n.d(r,"isJsonRpcResponseSuccess",(function(){return jt})),n.d(r,"isJsonRpcResponseError",(function(){return Lt})),n.d(r,"isInternalEvent",(function(){return Nt})),n.d(r,"isReservedEvent",(function(){return Ut})),n.d(r,"isSilentPayload",(function(){return Wt}))}])}));