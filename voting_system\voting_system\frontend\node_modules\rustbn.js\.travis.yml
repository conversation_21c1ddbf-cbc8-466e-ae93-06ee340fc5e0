sudo: false
os:
  - linux

language: node_js
node_js:
  - "8"

addons:
  apt:
    sources:
    - ubuntu-toolchain-r-test
    packages:
    - g++-4.8

branches:
  # We need to whitelist the branches which we want to have "push" automation.
  # Pull request automation is not constrained to this set of branches.
  only:
  - master

env:
  global:
    - DISPLAY=:99.0
  matrix:
    - CXX=g++-4.8 TEST_SUITE=test

matrix:
  fast_finish: true
  include:
    - os: linux
      node_js: "8"
      env: CXX=g++-4.8 TEST_SUITE=test

script: npm run $TEST_SUITE