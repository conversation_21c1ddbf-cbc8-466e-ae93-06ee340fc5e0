{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,OAAO,EAAW,MAAM,UAAU,CAAC;AAI5C,MAAM,UAAU,SAAS,CAAC,KAAa,EAAE,OAAqB;IAArB,wBAAA,EAAA,YAAqB;IAC5D,OAAO,OAAO,CAAC,KAAK,aAClB,SAAS,EAAE,GAAG,IACX,OAAO,EACV,CAAC;AACL,CAAC", "sourcesContent": ["import { dotCase, Options } from \"dot-case\";\n\nexport { Options };\n\nexport function paramCase(input: string, options: Options = {}) {\n  return dotCase(input, {\n    delimiter: \"-\",\n    ...options,\n  });\n}\n"]}