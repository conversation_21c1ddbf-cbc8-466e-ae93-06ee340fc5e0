# webpack-cli info

[![NPM Downloads][downloads]][downloads-url]

> **Note**
>
> This package is used by webpack-cli under-the-hood and is not intended for installation

## Description

This package returns a set of information related to the local environment.

## Installation

```bash
#npm
npm i -D @webpack-cli/info

#yarn
yarn add -D @webpack-cli/info

```

## Usage

```bash
#npx
npx webpack info [options]

#global installation
webpack info [options]

```

### Args / Flags

#### Output format

| Flag                                  | Description                             | Type   |
| ------------------------------------- | --------------------------------------- | ------ |
| `-o, --output < json or markdown >`   | To get the output in a specified format | string |
| `-a, --additional-package <value...>` | Adds additional packages to the output  | string |

_Not supported for config_

#### Options

| Flag        | Description                                | Type    |
| ----------- | ------------------------------------------ | ------- |
| `--help`    | Show help                                  | boolean |
| `--version` | Show version number of `@webpack-cli/info` | boolean |

[downloads]: https://img.shields.io/npm/dm/@webpack-cli/info.svg
[downloads-url]: https://www.npmjs.com/package/@webpack-cli/info
