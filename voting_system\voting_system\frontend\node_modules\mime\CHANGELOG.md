# Changelog

## v1.6.0 (24/11/2017)
*No changelog for this release.*

---

## v2.0.4 (24/11/2017)
- [**closed**] Switch to mime-score module for resolving extension contention issues. [#182](https://github.com/broofa/node-mime/issues/182)
- [**closed**] Update mime-db to 1.31.0 in v1.x branch [#181](https://github.com/broofa/node-mime/issues/181)

---

## v1.5.0 (22/11/2017)
- [**closed**] need ES5 version ready in npm package [#179](https://github.com/broofa/node-mime/issues/179)
- [**closed**] mime-db no trace of iWork - pages / numbers / etc. [#178](https://github.com/broofa/node-mime/issues/178)
- [**closed**] How it works in brownser ? [#176](https://github.com/broofa/node-mime/issues/176)
- [**closed**] Missing `./Mime` [#175](https://github.com/broofa/node-mime/issues/175)
- [**closed**] Vulnerable Regular Expression [#167](https://github.com/broofa/node-mime/issues/167)

---

## v2.0.3 (25/09/2017)
*No changelog for this release.*

---

## v1.4.1 (25/09/2017)
- [**closed**] Issue when bundling with webpack [#172](https://github.com/broofa/node-mime/issues/172)

---

## v2.0.2 (15/09/2017)
- [**V2**] fs.readFileSync is not a function [#165](https://github.com/broofa/node-mime/issues/165)
- [**closed**] The extension for video/quicktime should map to .mov, not .qt [#164](https://github.com/broofa/node-mime/issues/164)
- [**V2**] [v2 Feedback request] Mime class API [#163](https://github.com/broofa/node-mime/issues/163)
- [**V2**] [v2 Feedback request] Resolving conflicts over extensions [#162](https://github.com/broofa/node-mime/issues/162)
- [**V2**] Allow callers to load module with official, full, or no defined types.  [#161](https://github.com/broofa/node-mime/issues/161)
- [**V2**] Use "facets" to resolve extension conflicts [#160](https://github.com/broofa/node-mime/issues/160)
- [**V2**] Remove fs and path dependencies [#152](https://github.com/broofa/node-mime/issues/152)
- [**V2**] Default content-type should not be application/octet-stream [#139](https://github.com/broofa/node-mime/issues/139)
- [**V2**] reset mime-types [#124](https://github.com/broofa/node-mime/issues/124)
- [**V2**] Extensionless paths should return null or false [#113](https://github.com/broofa/node-mime/issues/113)

---

## v2.0.1 (14/09/2017)
- [**closed**] Changelog for v2.0 does not mention breaking changes [#171](https://github.com/broofa/node-mime/issues/171)
- [**closed**] MIME breaking with 'class' declaration as it is without 'use strict mode' [#170](https://github.com/broofa/node-mime/issues/170)

---

## v2.0.0 (12/09/2017)
- [**closed**] woff and woff2 [#168](https://github.com/broofa/node-mime/issues/168)

---

## v1.4.0 (28/08/2017)
- [**closed**] support for ac3 voc files [#159](https://github.com/broofa/node-mime/issues/159)
- [**closed**] Help understanding change from application/xml to text/xml [#158](https://github.com/broofa/node-mime/issues/158)
- [**closed**] no longer able to override mimetype [#157](https://github.com/broofa/node-mime/issues/157)
- [**closed**] application/vnd.adobe.photoshop [#147](https://github.com/broofa/node-mime/issues/147)
- [**closed**] Directories should appear as something other than application/octet-stream [#135](https://github.com/broofa/node-mime/issues/135)
- [**closed**] requested features [#131](https://github.com/broofa/node-mime/issues/131)
- [**closed**] Make types.json loading optional? [#129](https://github.com/broofa/node-mime/issues/129)
- [**closed**] Cannot find module './types.json' [#120](https://github.com/broofa/node-mime/issues/120)
- [**V2**] .wav files show up as "audio/x-wav" instead of "audio/x-wave" [#118](https://github.com/broofa/node-mime/issues/118)
- [**closed**] Don't be a pain in the ass for node community [#108](https://github.com/broofa/node-mime/issues/108)
- [**closed**] don't make default_type global [#78](https://github.com/broofa/node-mime/issues/78)
- [**closed**] mime.extension() fails if the content-type is parameterized [#74](https://github.com/broofa/node-mime/issues/74)

---

## v1.3.6 (11/05/2017)
- [**closed**] .md should be text/markdown as of March 2016 [#154](https://github.com/broofa/node-mime/issues/154)
- [**closed**] Error while installing mime [#153](https://github.com/broofa/node-mime/issues/153)
- [**closed**] application/manifest+json [#149](https://github.com/broofa/node-mime/issues/149)
- [**closed**] Dynamic adaptive streaming over HTTP (DASH) file extension typo [#141](https://github.com/broofa/node-mime/issues/141)
- [**closed**] charsets image/png undefined [#140](https://github.com/broofa/node-mime/issues/140)
- [**closed**] Mime-db dependency out of date [#130](https://github.com/broofa/node-mime/issues/130)
- [**closed**] how to support plist？ [#126](https://github.com/broofa/node-mime/issues/126)
- [**closed**] how does .types file format look like? [#123](https://github.com/broofa/node-mime/issues/123)
- [**closed**] Feature: support for expanding MIME patterns [#121](https://github.com/broofa/node-mime/issues/121)
- [**closed**] DEBUG_MIME doesn't work [#117](https://github.com/broofa/node-mime/issues/117)

---

## v1.3.4 (06/02/2015)
*No changelog for this release.*

---

## v1.3.3 (06/02/2015)
*No changelog for this release.*

---

## v1.3.1 (05/02/2015)
- [**closed**] Consider adding support for Handlebars .hbs file ending [#111](https://github.com/broofa/node-mime/issues/111)
- [**closed**] Consider adding support for hjson. [#110](https://github.com/broofa/node-mime/issues/110)
- [**closed**] Add mime type for Opus audio files [#94](https://github.com/broofa/node-mime/issues/94)
- [**closed**] Consider making the `Requesting New Types` information more visible [#77](https://github.com/broofa/node-mime/issues/77)

---

## v1.3.0 (05/02/2015)
- [**closed**] Add common name? [#114](https://github.com/broofa/node-mime/issues/114)
- [**closed**] application/x-yaml [#104](https://github.com/broofa/node-mime/issues/104)
- [**closed**] Add mime type for WOFF file format 2.0 [#102](https://github.com/broofa/node-mime/issues/102)
- [**closed**] application/x-msi for .msi [#99](https://github.com/broofa/node-mime/issues/99)
- [**closed**] Add mimetype for gettext translation files [#98](https://github.com/broofa/node-mime/issues/98)
- [**closed**] collaborators [#88](https://github.com/broofa/node-mime/issues/88)
- [**closed**] getting errot in installation of mime module...any1 can help? [#87](https://github.com/broofa/node-mime/issues/87)
- [**closed**] should application/json's charset be utf8? [#86](https://github.com/broofa/node-mime/issues/86)
- [**closed**] Add "license" and "licenses" to package.json [#81](https://github.com/broofa/node-mime/issues/81)
- [**closed**] lookup with extension-less file on Windows returns wrong type [#68](https://github.com/broofa/node-mime/issues/68)

---

## v1.2.11 (15/08/2013)
- [**closed**] Update mime.types [#65](https://github.com/broofa/node-mime/issues/65)
- [**closed**] Publish a new version [#63](https://github.com/broofa/node-mime/issues/63)
- [**closed**] README should state upfront that "application/octet-stream" is default for unknown extension [#55](https://github.com/broofa/node-mime/issues/55)
- [**closed**] Suggested improvement to the charset API [#52](https://github.com/broofa/node-mime/issues/52)

---

## v1.2.10 (25/07/2013)
- [**closed**] Mime type for woff files should be application/font-woff and not application/x-font-woff [#62](https://github.com/broofa/node-mime/issues/62)
- [**closed**] node.types in conflict with mime.types [#51](https://github.com/broofa/node-mime/issues/51)

---

## v1.2.9 (17/01/2013)
- [**closed**] Please update "mime" NPM [#49](https://github.com/broofa/node-mime/issues/49)
- [**closed**] Please add semicolon [#46](https://github.com/broofa/node-mime/issues/46)
- [**closed**] parse full mime types [#43](https://github.com/broofa/node-mime/issues/43)

---

## v1.2.8 (10/01/2013)
- [**closed**] /js directory mime is application/javascript. Is it correct? [#47](https://github.com/broofa/node-mime/issues/47)
- [**closed**] Add mime types for lua code. [#45](https://github.com/broofa/node-mime/issues/45)

---

## v1.2.7 (19/10/2012)
- [**closed**] cannot install 1.2.7 via npm [#41](https://github.com/broofa/node-mime/issues/41)
- [**closed**] Transfer ownership to @broofa [#36](https://github.com/broofa/node-mime/issues/36)
- [**closed**] it's wrong to set charset to UTF-8 for text [#30](https://github.com/broofa/node-mime/issues/30)
- [**closed**] Allow multiple instances of MIME types container [#27](https://github.com/broofa/node-mime/issues/27)

---

## v1.2.5 (16/02/2012)
- [**closed**] When looking up a types, check hasOwnProperty [#23](https://github.com/broofa/node-mime/issues/23)
- [**closed**] Bump version to 1.2.2 [#18](https://github.com/broofa/node-mime/issues/18)
- [**closed**] No license [#16](https://github.com/broofa/node-mime/issues/16)
- [**closed**] Some types missing that are used by html5/css3 [#13](https://github.com/broofa/node-mime/issues/13)
- [**closed**] npm install fails for 1.2.1 [#12](https://github.com/broofa/node-mime/issues/12)
- [**closed**] image/pjpeg + image/x-png [#10](https://github.com/broofa/node-mime/issues/10)
- [**closed**] symlink [#8](https://github.com/broofa/node-mime/issues/8)
- [**closed**] gzip [#2](https://github.com/broofa/node-mime/issues/2)
- [**closed**] ALL CAPS filenames return incorrect mime type [#1](https://github.com/broofa/node-mime/issues/1)
