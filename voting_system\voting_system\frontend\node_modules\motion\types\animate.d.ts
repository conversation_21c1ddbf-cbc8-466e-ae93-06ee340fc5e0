import { ElementOrSelector, AnimationOptionsWithOverrides, MotionKeyframesDefinition } from "@motionone/dom";
import { AnimationControls, AnimationOptions, ProgressFunction } from "@motionone/types";
export declare function animateProgress(target: ProgressFunction, options?: AnimationOptions): AnimationControls;
export declare function animate(elements: ElementOrSelector, keyframes: MotionKeyframesDefinition, options?: AnimationOptionsWithOverrides): AnimationControls;
export declare function animate(target: ProgressFunction, options?: AnimationOptions): AnimationControls;
//# sourceMappingURL=animate.d.ts.map