{"name": "@motionone/vue", "version": "10.16.4", "description": "A tiny, performant animation library for Vue", "author": "<PERSON>", "license": "MIT", "main": "dist/motion-vue.cjs.js", "module": "dist/motion-vue.esm.js", "types": "dist/index.d.ts", "sideEffects": false, "scripts": {"build": "cross-env NODE_ENV=production rollup --config rollup.config.js && npm run build-vue:ssr && npm run build-vue:es && npm run build-vue:unpkg", "build-vue:ssr": "cross-env NODE_ENV=production rollup --config rollup.config.js --format cjs", "build-vue:es": "cross-env NODE_ENV=production rollup --config rollup.config.js --format es", "build-vue:unpkg": "cross-env NODE_ENV=production rollup --config rollup.config.js --format iife", "dev": "yarn build-vue:es -w", "test": "vue-cli-service test:unit --config jest.config.js", "measure": "bundlesize"}, "dependencies": {"@motionone/dom": "^10.16.4", "tslib": "^2.3.1"}, "devDependencies": {"@rollup/plugin-alias": "^3.1.8", "@rollup/plugin-babel": "^5.3.0", "@rollup/plugin-commonjs": "^21.0.1", "@testing-library/dom": "^8.11.1", "@testing-library/jest-dom": "^5.16.1", "@vue/cli-plugin-babel": "5.0.0-rc.1", "@vue/cli-plugin-typescript": "5.0.0-rc.1", "@vue/cli-plugin-unit-jest": "5.0.0-rc.1", "@vue/cli-service": "5.0.0-rc.1", "@vue/compiler-sfc": "^3.0.0", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^7.0.0", "@vue/test-utils": "^2.0.0-rc.17", "@vue/vue3-jest": "^27.0.0-alpha.4", "@zerollup/ts-transform-paths": "^1.7.18", "cross-env": "^7.0.3", "minimist": "^1.2.5", "rollup-plugin-typescript2": "^0.31.1", "rollup-plugin-vue": "^6.0.0", "ttypescript": "^1.5.13", "vue": "^3.2.23"}, "bundlesize": [{"path": "./dist/motion-vue.min.js", "maxSize": "5.8 kB"}], "gitHead": "1b73773be02af31fedb8ac4b5d391650d06f4094"}