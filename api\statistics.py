from django.http import JsonResponse
from django.db.models import Count, Sum, Avg, F, Q
from django.utils import timezone
from datetime import timedelta
from ..models import Election, Candidate, Vote, Voter, County, Constituency, PollingStation
import json
import random

def get_statistics(request):
    """
    Get general statistics for all elections or a specific election
    """
    election_id = request.GET.get('election', 'all')
    time_range = request.GET.get('timeRange', 'all')
    
    # Filter by election if specified
    if election_id != 'all':
        try:
            election = Election.objects.get(id=election_id)
            elections = [election]
        except Election.DoesNotExist:
            return JsonResponse({'error': 'Election not found'}, status=404)
    else:
        elections = Election.objects.all()
    
    # Filter by time range if specified
    if time_range != 'all':
        now = timezone.now()
        if time_range == 'day':
            start_date = now - timedelta(days=1)
        elif time_range == 'week':
            start_date = now - timedelta(weeks=1)
        elif time_range == 'month':
            start_date = now - timedelta(days=30)
        elif time_range == 'year':
            start_date = now - timedelta(days=365)
        else:
            start_date = None
        
        if start_date:
            elections = elections.filter(start_date__gte=start_date)
    
    # If no elections match the criteria, return demo data
    if not elections.exists():
        return JsonResponse(generate_demo_data(), safe=False)
    
    # Calculate statistics
    total_votes = Vote.objects.filter(election__in=elections).count()
    total_voters = Voter.objects.count()
    
    # Get age distribution (using random data for demo)
    age_groups = ['18-24', '25-34', '35-44', '45-54', '55-64', '65+']
    voter_turnout_data = [random.randint(60, 90) for _ in range(len(age_groups))]
    
    # Get voting method distribution (using random data for demo)
    voting_methods = ['In Person', 'Mobile App', 'Web Browser']
    voting_method_data = [65, 20, 15]  # Percentages
    
    # Get voting trend throughout the day (using random data for demo)
    hours = ['6am', '8am', '10am', '12pm', '2pm', '4pm', '6pm', '8pm']
    voting_trend_data = [
        random.randint(100, 200),
        random.randint(300, 400),
        random.randint(500, 600),
        random.randint(600, 700),
        random.randint(700, 800),
        random.randint(800, 900),
        random.randint(900, 1000),
        random.randint(400, 500)
    ]
    
    # Get geographic distribution (using real counties if available)
    counties = County.objects.all()[:6]
    if counties.exists():
        geographic_labels = [county.name for county in counties]
    else:
        geographic_labels = ['Nairobi', 'Mombasa', 'Kisumu', 'Nakuru', 'Eldoret', 'Other Regions']
    
    geographic_data = [random.randint(5, 30) for _ in range(len(geographic_labels))]
    
    # Get candidate performance for the first election
    if elections:
        election = elections.first()
        candidates = Candidate.objects.filter(election=election)
        if candidates.exists():
            candidate_labels = [f"{c.first_name} {c.last_name}" for c in candidates]
            candidate_data = [c.votes_count for c in candidates]
        else:
            # Demo data
            candidate_labels = ['William Ruto', 'Raila Odinga', 'George Wajackoyah', 'David Mwaure']
            candidate_data = [7176141, 6942930, 61969, 31987]
    else:
        # Demo data
        candidate_labels = ['William Ruto', 'Raila Odinga', 'George Wajackoyah', 'David Mwaure']
        candidate_data = [7176141, 6942930, 61969, 31987]
    
    # Prepare the response
    response = {
        'summary': {
            'totalVoters': total_votes,
            'registeredVoters': total_voters,
            'turnoutPercentage': round((total_votes / total_voters * 100) if total_voters > 0 else 80.93, 2),
            'maleVoters': total_votes // 2,  # Demo data
            'femaleVoters': total_votes // 2,  # Demo data
            'averageAge': 41,  # Demo data
            'invalidVotes': int(total_votes * 0.005),  # Demo data (0.5% invalid votes)
        },
        'voterTurnout': {
            'labels': age_groups,
            'datasets': [
                {
                    'label': 'Voter Turnout by Age Group (%)',
                    'data': voter_turnout_data,
                    'backgroundColor': 'rgba(54, 162, 235, 0.6)',
                    'borderColor': 'rgba(54, 162, 235, 1)',
                    'borderWidth': 1,
                }
            ]
        },
        'votingMethod': {
            'labels': voting_methods,
            'datasets': [
                {
                    'label': 'Voting Method',
                    'data': voting_method_data,
                    'backgroundColor': [
                        'rgba(255, 99, 132, 0.6)',
                        'rgba(54, 162, 235, 0.6)',
                        'rgba(255, 206, 86, 0.6)',
                    ],
                    'borderColor': [
                        'rgba(255, 99, 132, 1)',
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 206, 86, 1)',
                    ],
                    'borderWidth': 1,
                }
            ]
        },
        'votingTrend': {
            'labels': hours,
            'datasets': [
                {
                    'label': 'Votes Cast by Hour',
                    'data': voting_trend_data,
                    'fill': False,
                    'backgroundColor': 'rgba(75, 192, 192, 0.6)',
                    'borderColor': 'rgba(75, 192, 192, 1)',
                    'tension': 0.1
                }
            ]
        },
        'geographicDistribution': {
            'labels': geographic_labels,
            'datasets': [
                {
                    'label': 'Votes by Region',
                    'data': geographic_data,
                    'backgroundColor': [
                        'rgba(255, 99, 132, 0.6)',
                        'rgba(54, 162, 235, 0.6)',
                        'rgba(255, 206, 86, 0.6)',
                        'rgba(75, 192, 192, 0.6)',
                        'rgba(153, 102, 255, 0.6)',
                        'rgba(255, 159, 64, 0.6)',
                    ],
                    'borderWidth': 1,
                }
            ]
        },
        'candidatePerformance': {
            'labels': candidate_labels,
            'datasets': [
                {
                    'label': 'Votes Received',
                    'data': candidate_data,
                    'backgroundColor': [
                        'rgba(255, 99, 132, 0.6)',
                        'rgba(54, 162, 235, 0.6)',
                        'rgba(255, 206, 86, 0.6)',
                        'rgba(75, 192, 192, 0.6)',
                    ],
                    'borderWidth': 1,
                }
            ]
        }
    }
    
    return JsonResponse(response, safe=False)

def generate_demo_data():
    """Generate demo data for statistics"""
    return {
        'summary': {
            'totalVoters': 22120458,
            'registeredVoters': 27333566,
            'turnoutPercentage': 80.93,
            'maleVoters': 11060229,
            'femaleVoters': 11060229,
            'averageAge': 41,
            'invalidVotes': 113614,
        },
        'voterTurnout': {
            'labels': ['18-24', '25-34', '35-44', '45-54', '55-64', '65+'],
            'datasets': [
                {
                    'label': 'Voter Turnout by Age Group (%)',
                    'data': [65, 78, 82, 88, 85, 72],
                    'backgroundColor': 'rgba(54, 162, 235, 0.6)',
                    'borderColor': 'rgba(54, 162, 235, 1)',
                    'borderWidth': 1,
                }
            ]
        },
        'votingMethod': {
            'labels': ['In Person', 'Mobile App', 'Web Browser'],
            'datasets': [
                {
                    'label': 'Voting Method',
                    'data': [65, 20, 15],
                    'backgroundColor': [
                        'rgba(255, 99, 132, 0.6)',
                        'rgba(54, 162, 235, 0.6)',
                        'rgba(255, 206, 86, 0.6)',
                    ],
                    'borderColor': [
                        'rgba(255, 99, 132, 1)',
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 206, 86, 1)',
                    ],
                    'borderWidth': 1,
                }
            ]
        },
        'votingTrend': {
            'labels': ['6am', '8am', '10am', '12pm', '2pm', '4pm', '6pm', '8pm'],
            'datasets': [
                {
                    'label': 'Votes Cast by Hour',
                    'data': [120, 350, 580, 620, 750, 830, 920, 450],
                    'fill': False,
                    'backgroundColor': 'rgba(75, 192, 192, 0.6)',
                    'borderColor': 'rgba(75, 192, 192, 1)',
                    'tension': 0.1
                }
            ]
        },
        'geographicDistribution': {
            'labels': ['Nairobi', 'Mombasa', 'Kisumu', 'Nakuru', 'Eldoret', 'Other Regions'],
            'datasets': [
                {
                    'label': 'Votes by Region',
                    'data': [28, 15, 12, 10, 8, 27],
                    'backgroundColor': [
                        'rgba(255, 99, 132, 0.6)',
                        'rgba(54, 162, 235, 0.6)',
                        'rgba(255, 206, 86, 0.6)',
                        'rgba(75, 192, 192, 0.6)',
                        'rgba(153, 102, 255, 0.6)',
                        'rgba(255, 159, 64, 0.6)',
                    ],
                    'borderWidth': 1,
                }
            ]
        },
        'candidatePerformance': {
            'labels': ['William Ruto', 'Raila Odinga', 'George Wajackoyah', 'David Mwaure'],
            'datasets': [
                {
                    'label': 'Votes Received',
                    'data': [7176141, 6942930, 61969, 31987],
                    'backgroundColor': [
                        'rgba(255, 99, 132, 0.6)',
                        'rgba(54, 162, 235, 0.6)',
                        'rgba(255, 206, 86, 0.6)',
                        'rgba(75, 192, 192, 0.6)',
                    ],
                    'borderWidth': 1,
                }
            ]
        }
    }

def get_advanced_analytics(request):
    """
    Get advanced analytics data
    """
    # For now, return demo data
    return JsonResponse({
        'historicalTrends': {
            'labels': ['2002', '2007', '2013', '2017', '2022'],
            'datasets': [
                {
                    'label': 'Voter Turnout (%)',
                    'data': [57.2, 69.1, 85.9, 79.5, 80.9],
                    'fill': False,
                    'backgroundColor': 'rgba(75, 192, 192, 0.6)',
                    'borderColor': 'rgba(75, 192, 192, 1)',
                    'tension': 0.1
                },
                {
                    'label': 'Invalid Votes (%)',
                    'data': [2.1, 2.5, 1.8, 1.2, 0.5],
                    'fill': False,
                    'backgroundColor': 'rgba(255, 99, 132, 0.6)',
                    'borderColor': 'rgba(255, 99, 132, 1)',
                    'tension': 0.1
                }
            ]
        },
        'demographicAnalysis': {
            'labels': ['Education', 'Income Level', 'Urban/Rural', 'Age', 'Gender', 'Employment'],
            'datasets': [
                {
                    'label': 'Correlation with Voting Behavior',
                    'data': [0.75, 0.62, 0.81, 0.58, 0.32, 0.45],
                    'fill': True,
                    'backgroundColor': 'rgba(54, 162, 235, 0.2)',
                    'borderColor': 'rgba(54, 162, 235, 1)',
                    'pointBackgroundColor': 'rgba(54, 162, 235, 1)',
                    'pointBorderColor': '#fff',
                    'pointHoverBackgroundColor': '#fff',
                    'pointHoverBorderColor': 'rgba(54, 162, 235, 1)'
                }
            ]
        },
        'votingTimeAnalysis': {
            'datasets': [
                {
                    'label': 'Voting Patterns',
                    'data': [
                        { 'x': 6, 'y': 25, 'r': 5 },
                        { 'x': 8, 'y': 35, 'r': 10 },
                        { 'x': 10, 'y': 45, 'r': 15 },
                        { 'x': 12, 'y': 30, 'r': 20 },
                        { 'x': 14, 'y': 40, 'r': 15 },
                        { 'x': 16, 'y': 50, 'r': 10 },
                        { 'x': 18, 'y': 28, 'r': 20 },
                        { 'x': 20, 'y': 32, 'r': 5 },
                    ],
                    'backgroundColor': 'rgba(255, 99, 132, 0.6)',
                }
            ]
        },
        'votingMethodTrends': {
            'labels': ['2002', '2007', '2013', '2017', '2022'],
            'datasets': [
                {
                    'label': 'In-Person Voting (%)',
                    'data': [100, 100, 100, 95, 65],
                    'fill': False,
                    'backgroundColor': 'rgba(255, 99, 132, 0.6)',
                    'borderColor': 'rgba(255, 99, 132, 1)',
                    'tension': 0.1
                },
                {
                    'label': 'Mobile App Voting (%)',
                    'data': [0, 0, 0, 3, 20],
                    'fill': False,
                    'backgroundColor': 'rgba(54, 162, 235, 0.6)',
                    'borderColor': 'rgba(54, 162, 235, 1)',
                    'tension': 0.1
                },
                {
                    'label': 'Web Browser Voting (%)',
                    'data': [0, 0, 0, 2, 15],
                    'fill': False,
                    'backgroundColor': 'rgba(255, 206, 86, 0.6)',
                    'borderColor': 'rgba(255, 206, 86, 1)',
                    'tension': 0.1
                }
            ]
        },
        'insights': [
            "Voter turnout has increased by 41.4% since 2002, showing growing democratic participation.",
            "Urban areas show 12.3% higher voter turnout compared to rural regions.",
            "Mobile and web voting methods have grown from 0% to 35% of all votes in just 5 years.",
            "Invalid votes have decreased by 76.2% since 2002, likely due to improved digital voting interfaces.",
            "Peak voting times are between 12pm-2pm and 6pm-8pm, suggesting lunch breaks and after-work voting patterns.",
            "Higher education levels correlate strongly (0.75) with voting participation.",
            "Age demographics show that 25-34 and 45-54 age groups have the highest participation rates."
        ]
    }, safe=False)
