{"name": "keyvaluestorage-interface", "description": "Isomorphic Key-Value Storage Interface", "version": "1.0.0", "author": "<PERSON> <github.com/pedrouid>", "license": "MIT", "keywords": ["iso", "isomorphic", "store", "storage", "localstorage", "asyncstorage", "sequelize", "sqlite", "interface"], "files": ["dist"], "main": "dist/cjs/index.js", "types": "dist/cjs/index.d.ts", "unpkg": "dist/umd/index.min.js", "homepage": "https://github.com/pedrouid/keyvaluestorage-interface", "repository": {"type": "git", "url": "git+https://github.com/pedrouid/keyvaluestorage-interface.git"}, "bugs": {"url": "https://github.com/pedrouid/keyvaluestorage-interface/issues"}, "scripts": {"start": "tsdx watch", "clean": "rm -rf dist", "build:cjs": "./node_modules/.bin/tsc -p tsconfig.cjs.json", "build:umd": "webpack", "build": "yarn clean && yarn build:cjs && yarn build:umd", "test:pre": "rm -rf ./test/test.db", "test:run": "tsdx test ./test", "test": "yarn test:pre && yarn test:run", "lint": "tsdx lint src test", "prepare": "yarn lint && yarn build && yarn test"}, "devDependencies": {"@types/jest": "25.1.1", "@types/node": "13.7.0", "husky": "4.2.1", "tsdx": "0.12.3", "tslib": "1.10.0", "typescript": "3.7.5", "webpack": "4.41.6", "webpack-cli": "3.3.11"}, "husky": {"hooks": {"pre-commit": "yarn lint"}}, "prettier": {"printWidth": 80, "semi": true, "singleQuote": true, "trailingComma": "es5"}}