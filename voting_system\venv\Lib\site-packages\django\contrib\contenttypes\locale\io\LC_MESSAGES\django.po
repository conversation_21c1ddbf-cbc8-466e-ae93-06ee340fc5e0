# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2014
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2017-09-20 01:58+0000\n"
"Last-Translator: <PERSON><PERSON> <jann<PERSON>@leidel.info>\n"
"Language-Team: Ido (http://www.transifex.com/django/django/language/io/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: io\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Content Types"
msgstr ""

msgid "python model class name"
msgstr "klaso nomo dil python modelo"

msgid "content type"
msgstr "kontenajo tipo"

msgid "content types"
msgstr "kontenajo tipi"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "La objekto kun kontenajo tipo %(ct_id)s ne havas relatita modelo"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn't exist"
msgstr "La objekto %(obj_id)s kun kontenajo tipo %(ct_id)s ne existas"

#, python-format
msgid "%(ct_name)s objects don't have a get_absolute_url() method"
msgstr "La objekti %(ct_name)s ne havas get_absolute_url() metodo"
