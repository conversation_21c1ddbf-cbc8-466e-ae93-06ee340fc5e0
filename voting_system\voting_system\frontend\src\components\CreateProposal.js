import React, { useState } from 'react';

const CreateProposal = ({ onSubmit, onCancel }) => {
    const [title, setTitle] = useState('');
    const [description, setDescription] = useState('');
    const [durationMinutes, setDurationMinutes] = useState(60);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [error, setError] = useState('');

    // Handle form submission
    const handleSubmit = async (e) => {
        e.preventDefault();
        
        // Validate form
        if (!title.trim()) {
            setError('Title is required');
            return;
        }
        
        if (!description.trim()) {
            setError('Description is required');
            return;
        }
        
        if (durationMinutes < 1) {
            setError('Duration must be at least 1 minute');
            return;
        }
        
        try {
            setIsSubmitting(true);
            setError('');
            
            await onSubmit(title, description, durationMinutes);
        } catch (error) {
            console.error('Error creating proposal:', error);
            setError('Error creating proposal. Please try again.');
            setIsSubmitting(false);
        }
    };

    return (
        <div>
            <h2>Create a New Proposal</h2>
            <p className="lead">Create a new proposal for the community to vote on.</p>
            
            {error && (
                <div className="alert alert-danger" role="alert">
                    {error}
                </div>
            )}
            
            <div className="row">
                <div className="col-md-8">
                    <form onSubmit={handleSubmit}>
                        <div className="mb-3">
                            <label htmlFor="title" className="form-label">Title</label>
                            <input
                                type="text"
                                className="form-control"
                                id="title"
                                value={title}
                                onChange={(e) => setTitle(e.target.value)}
                                required
                            />
                        </div>
                        
                        <div className="mb-3">
                            <label htmlFor="description" className="form-label">Description</label>
                            <textarea
                                className="form-control"
                                id="description"
                                rows="5"
                                value={description}
                                onChange={(e) => setDescription(e.target.value)}
                                required
                            ></textarea>
                        </div>
                        
                        <div className="mb-3">
                            <label htmlFor="durationMinutes" className="form-label">Duration (minutes)</label>
                            <input
                                type="number"
                                className="form-control"
                                id="durationMinutes"
                                value={durationMinutes}
                                onChange={(e) => setDurationMinutes(parseInt(e.target.value))}
                                min="1"
                                required
                            />
                            <div className="form-text">How long should the voting period last?</div>
                        </div>
                        
                        <div className="d-flex">
                            <button
                                type="submit"
                                className="btn btn-primary me-2"
                                disabled={isSubmitting}
                            >
                                {isSubmitting ? (
                                    <>
                                        <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                        Creating...
                                    </>
                                ) : (
                                    'Create Proposal'
                                )}
                            </button>
                            
                            <button
                                type="button"
                                className="btn btn-outline-secondary"
                                onClick={onCancel}
                                disabled={isSubmitting}
                            >
                                Cancel
                            </button>
                        </div>
                    </form>
                </div>
                
                <div className="col-md-4">
                    <div className="card">
                        <div className="card-header">
                            About Creating Proposals
                        </div>
                        <div className="card-body">
                            <p>When you create a proposal, it will be stored both in our database and on the Ethereum blockchain.</p>
                            <p>The blockchain ensures that the proposal cannot be tampered with once it's created.</p>
                            <p>Users will be able to vote on your proposal until the voting period ends.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default CreateProposal;
