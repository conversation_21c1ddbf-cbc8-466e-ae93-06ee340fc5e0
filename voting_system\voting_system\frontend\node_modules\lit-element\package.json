{"name": "lit-element", "version": "3.3.3", "publishConfig": {"access": "public"}, "description": "A simple base class for creating fast, lightweight web components", "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "https://github.com/lit/lit.git", "directory": "packages/lit-element"}, "author": "Google LLC", "homepage": "https://lit.dev/", "main": "index.js", "module": "index.js", "types": "index.d.ts", "type": "module", "exports": {".": {"types": "./development/index.d.ts", "development": "./development/index.js", "default": "./index.js"}, "./decorators.js": {"types": "./development/decorators.d.ts", "development": "./development/decorators.js", "default": "./decorators.js"}, "./decorators/custom-element.js": {"types": "./development/decorators/custom-element.d.ts", "development": "./development/decorators/custom-element.js", "default": "./decorators/custom-element.js"}, "./decorators/event-options.js": {"types": "./development/decorators/event-options.d.ts", "development": "./development/decorators/event-options.js", "default": "./decorators/event-options.js"}, "./decorators/property.js": {"types": "./development/decorators/property.d.ts", "development": "./development/decorators/property.js", "default": "./decorators/property.js"}, "./decorators/query-all.js": {"types": "./development/decorators/query-all.d.ts", "development": "./development/decorators/query-all.js", "default": "./decorators/query-all.js"}, "./decorators/query-assigned-elements.js": {"types": "./development/decorators/query-assigned-elements.d.ts", "development": "./development/decorators/query-assigned-elements.js", "default": "./decorators/query-assigned-elements.js"}, "./decorators/query-assigned-nodes.js": {"types": "./development/decorators/query-assigned-nodes.d.ts", "development": "./development/decorators/query-assigned-nodes.js", "default": "./decorators/query-assigned-nodes.js"}, "./decorators/query-async.js": {"types": "./development/decorators/query-async.d.ts", "development": "./development/decorators/query-async.js", "default": "./decorators/query-async.js"}, "./decorators/query.js": {"types": "./development/decorators/query.d.ts", "development": "./development/decorators/query.js", "default": "./decorators/query.js"}, "./decorators/state.js": {"types": "./development/decorators/state.d.ts", "development": "./development/decorators/state.js", "default": "./decorators/state.js"}, "./experimental-hydrate-support.js": {"types": "./development/experimental-hydrate-support.d.ts", "development": "./development/experimental-hydrate-support.js", "default": "./experimental-hydrate-support.js"}, "./lit-element.js": {"types": "./development/lit-element.d.ts", "development": "./development/lit-element.js", "default": "./lit-element.js"}, "./polyfill-support.js": {"types": "./development/polyfill-support.d.ts", "development": "./development/polyfill-support.js", "default": "./polyfill-support.js"}, "./private-ssr-support.js": {"types": "./development/private-ssr-support.d.ts", "development": "./development/private-ssr-support.js", "default": "./private-ssr-support.js"}}, "scripts": {"build": "wireit", "build:rollup": "wireit", "build:ts": "wireit", "build:ts:types": "wireit", "check-version": "wireit", "checksize": "wireit", "prepublishOnly": "npm run check-version", "test": "wireit", "test:dev": "wireit", "test:prod": "wireit", "test:node": "wireit"}, "wireit": {"build": {"dependencies": ["build:rollup", "build:ts", "build:ts:types", "../lit-html:build", "../reactive-element:build"]}, "build:ts": {"#comment": "Note this also builds polyfill-support via a TypeScript project reference.", "command": "tsc --build --pretty", "clean": "if-file-deleted", "dependencies": ["../lit-html:build:ts:types", "../reactive-element:build:ts:types", "../labs/testing:build:ts:utils"], "files": ["src/**/*.ts", "tsconfig.json", "tsconfig.polyfill-support.json"], "output": ["development/**/*.{js,js.map,d.ts,d.ts.map}", "tsconfig.tsbuildinfo", "tsconfig.polyfill-support.tsbuildinfo"]}, "build:ts:types": {"command": "treemirror development . \"**/*.d.ts{,.map}\"", "dependencies": ["../internal-scripts:build", "build:ts"], "files": [], "output": ["*.d.ts{,.map}", "decorators/*.d.ts{,.map}"]}, "build:rollup": {"command": "rollup -c", "dependencies": ["build:ts", "../lit-html:build:rollup", "../reactive-element:build:rollup"], "files": ["rollup.config.js", "../../rollup-common.js", "src/test/*_test.html", "src/test/polyfill-support/*_test.html"], "output": ["decorators.js{,.map}", "experimental-hydrate-support.js{,.map}", "index.js{,.map}", "lit-element.js{,.map}", "polyfill-support.js{,.map}", "private-ssr-support.js{,.map}", "decorators/*.js{,.map}", "test/*_test.html", "development/test/*_test.html", "test/polyfill-support/*_test.html", "development/test/polyfill-support/*_test.html"]}, "checksize": {"command": "rollup -c --environment=CHECKSIZE", "dependencies": ["build:ts"], "files": ["rollup.config.js", "../../rollup-common.js"], "output": []}, "check-version": {"command": "node scripts/check-version-tracker.js", "files": ["scripts/check-version-tracker.js", "package.json", "src/lit-element.ts"], "output": []}, "test": {"dependencies": ["test:dev", "test:prod", "test:node", "check-version"]}, "test:dev": {"command": "MODE=dev node ../tests/run-web-tests.js  \"development/**/*_test.(js|html)\" --config ../tests/web-test-runner.config.js", "dependencies": ["build:ts", "../tests:build"], "env": {"BROWSERS": {"external": true}}, "files": [], "output": []}, "test:prod": {"command": "MODE=prod node ../tests/run-web-tests.js  \"development/**/*_test.(js|html)\" --config ../tests/web-test-runner.config.js", "dependencies": ["build:ts", "build:rollup", "../tests:build"], "env": {"BROWSERS": {"external": true}}, "files": [], "output": []}, "test:node": {"command": "node development/test/node-imports.js", "dependencies": ["build:ts", "build:rollup"], "files": [], "output": []}}, "files": ["/decorators.{d.ts,d.ts.map,js,js.map}", "/experimental-hydrate-support.{d.ts,d.ts.map,js,js.map}", "/index.{d.ts,d.ts.map,js,js.map}", "/lit-element.{d.ts,d.ts.map,js,js.map}", "/polyfill-support.{d.ts,d.ts.map,js,js.map}", "/private-ssr-support.{d.ts,d.ts.map,js,js.map}", "/decorators/", "/development/", "!/development/test/"], "dependencies": {"@lit-labs/ssr-dom-shim": "^1.1.0", "@lit/reactive-element": "^1.3.0", "lit-html": "^2.8.0"}, "devDependencies": {"@lit-internal/scripts": "^1.0.0", "@lit-labs/testing": "^0.2.0", "@webcomponents/shadycss": "^1.8.0", "@webcomponents/template": "^1.4.4", "@webcomponents/webcomponentsjs": "^2.6.0", "tslib": "^2.0.3"}, "directories": {"test": "test"}}