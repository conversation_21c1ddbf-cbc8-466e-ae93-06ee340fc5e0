{"name": "level-errors", "description": "Error types for LevelUP", "version": "1.0.5", "contributors": ["<PERSON>g <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/chesles/)", "<PERSON> <<EMAIL>> (https://github.com/raynos)", "<PERSON> <<EMAIL>> (https://github.com/dominictarr)", "<PERSON> <<EMAIL>> (https://github.com/maxogden)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/ralphtheninja)", "<PERSON> <<EMAIL>> (https://github.com/kesla)", "<PERSON> <<EMAIL>> (https://github.com/juliangruber)", "<PERSON> <<EMAIL>> (https://github.com/hij1nx)", "<PERSON> <<EMAIL>> (https://github.com/No9)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON> <<EMAIL>> (https://github.com/pgte)", "<PERSON> <<EMAIL>> (https://github.com/substack)", "<PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "repository": {"type": "git", "url": "https://github.com/level/errors.git"}, "scripts": {"test": "tape test.js"}, "homepage": "https://github.com/level/errors.git", "keywords": ["leveldb", "levelup", "leveldown", "errors"], "main": "errors.js", "dependencies": {"errno": "~0.1.1"}, "license": "MIT", "devDependencies": {"tape": "^4.3.0"}}