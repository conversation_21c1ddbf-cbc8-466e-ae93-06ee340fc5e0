{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../src/gestures/scroll/offsets/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,aAAa,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAA;AAE7D,OAAO,EAAE,SAAS,EAAE,MAAM,SAAS,CAAA;AACnC,OAAO,EAAE,YAAY,EAAE,MAAM,WAAW,CAAA;AAExC,OAAO,EAAE,aAAa,EAAE,MAAM,UAAU,CAAA;AAExC,MAAM,KAAK,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAA;AAE5B,MAAM,UAAU,cAAc,CAC5B,SAAsB,EACtB,IAAgB,EAChB,OAAsB;IAEtB,IAAI,EAAE,MAAM,EAAE,gBAAgB,GAAG,YAAY,CAAC,GAAG,EAAE,GAAG,OAAO,CAAA;IAC7D,MAAM,EAAE,MAAM,GAAG,SAAS,EAAE,IAAI,GAAG,GAAG,EAAE,GAAG,OAAO,CAAA;IAClD,MAAM,WAAW,GAAG,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAA;IAErD,MAAM,KAAK,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA;IAEzE;;;;OAIG;IACH,MAAM,UAAU,GACd,MAAM,KAAK,SAAS;QAClB,CAAC,CAAC,EAAE,KAAK,EAAE,SAAS,CAAC,WAAW,EAAE,MAAM,EAAE,SAAS,CAAC,YAAY,EAAE;QAClE,CAAC,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,WAAW,EAAE,MAAM,EAAE,MAAM,CAAC,YAAY,EAAE,CAAA;IAEhE,MAAM,aAAa,GAAG;QACpB,KAAK,EAAE,SAAS,CAAC,WAAW;QAC5B,MAAM,EAAE,SAAS,CAAC,YAAY;KAC/B,CAAA;IAED;;;OAGG;IACH,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAA;IAE5B;;;OAGG;IACH,IAAI,UAAU,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,WAAW,CAAA;IAExC,MAAM,UAAU,GAAG,gBAAgB,CAAC,MAAM,CAAA;IAC1C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;QACpC,MAAM,MAAM,GAAG,aAAa,CAC1B,gBAAgB,CAAC,CAAC,CAAC,EACnB,aAAa,CAAC,WAAW,CAAC,EAC1B,UAAU,CAAC,WAAW,CAAC,EACvB,KAAK,CAAC,IAAI,CAAC,CACZ,CAAA;QAED,IAAI,CAAC,UAAU,IAAI,MAAM,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,mBAAoB,CAAC,CAAC,CAAC,EAAE,CAAC;YACjE,UAAU,GAAG,IAAI,CAAA;QACnB,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAA;IAC/B,CAAC;IAED;;;OAGG;IACH,IAAI,UAAU,EAAE,CAAC;QACf,IAAI,CAAC,IAAI,CAAC,CAAC,WAAW,GAAG,WAAW,CAClC,aAAa,CAAC,UAAU,CAAC,EACzB,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAClB,CAAA;QAED,IAAI,CAAC,IAAI,CAAC,CAAC,mBAAmB,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAA;IACzD,CAAC;IACD,IAAI,CAAC,IAAI,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,WAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAA;AACnE,CAAC"}