{"version": 3, "file": "choose.js", "sources": ["../../../src/directives/choose.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/**\n * Chooses and evaluates a template function from a list based on matching\n * the given `value` to a case.\n *\n * Cases are structured as `[caseValue, func]`. `value` is matched to\n * `caseValue` by strict equality. The first match is selected. Case values\n * can be of any type including primitives, objects, and symbols.\n *\n * This is similar to a switch statement, but as an expression and without\n * fallthrough.\n *\n * @example\n *\n * ```ts\n * render() {\n *   return html`\n *     ${choose(this.section, [\n *       ['home', () => html`<h1>Home</h1>`],\n *       ['about', () => html`<h1>About</h1>`]\n *     ],\n *     () => html`<h1>Error</h1>`)}\n *   `;\n * }\n * ```\n */\nexport const choose = <T, V>(\n  value: T,\n  cases: Array<[T, () => V]>,\n  defaultCase?: () => V\n) => {\n  for (const c of cases) {\n    const caseValue = c[0];\n    if (caseValue === value) {\n      const fn = c[1];\n      return fn();\n    }\n  }\n  return defaultCase?.();\n};\n"], "names": [], "mappings": "AAAA;;;;AAIG;AAEH;;;;;;;;;;;;;;;;;;;;;;;;AAwBG;AACU,MAAA,MAAM,GAAG,CACpB,KAAQ,EACR,KAA0B,EAC1B,WAAqB,KACnB;AACF,IAAA,KAAK,MAAM,CAAC,IAAI,KAAK,EAAE;AACrB,QAAA,MAAM,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,IAAI,SAAS,KAAK,KAAK,EAAE;AACvB,YAAA,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,OAAO,EAAE,EAAE,CAAC;AACb,SAAA;AACF,KAAA;AACD,IAAA,OAAO,WAAW,KAAX,IAAA,IAAA,WAAW,KAAX,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,WAAW,EAAI,CAAC;AACzB;;;;"}