{"version": 3, "file": "query-assigned-elements.js", "sources": ["../../src/decorators/query-assigned-elements.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/*\n * IMPORTANT: For compatibility with <PERSON><PERSON><PERSON> and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\n\nimport {decorateProperty} from './base.js';\n\nimport type {ReactiveElement} from '../reactive-element.js';\nimport type {QueryAssignedNodesOptions} from './query-assigned-nodes.js';\n\nconst NODE_MODE = false;\nconst global = NODE_MODE ? globalThis : window;\n\n/**\n * A tiny module scoped polyfill for HTMLSlotElement.assignedElements.\n */\nconst slotAssignedElements =\n  global.HTMLSlotElement?.prototype.assignedElements != null\n    ? (slot: HTMLSlotElement, opts?: AssignedNodesOptions) =>\n        slot.assignedElements(opts)\n    : (slot: HTMLSlotElement, opts?: AssignedNodesOptions) =>\n        slot\n          .assignedNodes(opts)\n          .filter(\n            (node): node is Element => node.nodeType === Node.ELEMENT_NODE\n          );\n\n/**\n * Options for the {@linkcode queryAssignedElements} decorator. Extends the\n * options that can be passed into\n * [HTMLSlotElement.assignedElements](https://developer.mozilla.org/en-US/docs/Web/API/HTMLSlotElement/assignedElements).\n */\nexport interface QueryAssignedElementsOptions\n  extends QueryAssignedNodesOptions {\n  /**\n   * CSS selector used to filter the elements returned. For example, a selector\n   * of `\".item\"` will only include elements with the `item` class.\n   */\n  selector?: string;\n}\n\n/**\n * A property decorator that converts a class property into a getter that\n * returns the `assignedElements` of the given `slot`. Provides a declarative\n * way to use\n * [`HTMLSlotElement.assignedElements`](https://developer.mozilla.org/en-US/docs/Web/API/HTMLSlotElement/assignedElements).\n *\n * Can be passed an optional {@linkcode QueryAssignedElementsOptions} object.\n *\n * Example usage:\n * ```ts\n * class MyElement {\n *   @queryAssignedElements({ slot: 'list' })\n *   listItems!: Array<HTMLElement>;\n *   @queryAssignedElements()\n *   unnamedSlotEls!: Array<HTMLElement>;\n *\n *   render() {\n *     return html`\n *       <slot name=\"list\"></slot>\n *       <slot></slot>\n *     `;\n *   }\n * }\n * ```\n *\n * Note, the type of this property should be annotated as `Array<HTMLElement>`.\n *\n * @category Decorator\n */\nexport function queryAssignedElements(options?: QueryAssignedElementsOptions) {\n  const {slot, selector} = options ?? {};\n  return decorateProperty({\n    descriptor: (_name: PropertyKey) => ({\n      get(this: ReactiveElement) {\n        const slotSelector = `slot${slot ? `[name=${slot}]` : ':not([name])'}`;\n        const slotEl =\n          this.renderRoot?.querySelector<HTMLSlotElement>(slotSelector);\n        const elements =\n          slotEl != null ? slotAssignedElements(slotEl, options) : [];\n        if (selector) {\n          return elements.filter((node) => node.matches(selector));\n        }\n        return elements;\n      },\n      enumerable: true,\n      configurable: true,\n    }),\n  });\n}\n"], "names": ["slotAssignedElements", "_a", "globalThis", "HTMLSlotElement", "prototype", "assignedElements", "slot", "opts", "assignedNodes", "filter", "node", "nodeType", "Node", "ELEMENT_NODE", "queryAssignedElements", "options", "selector", "decorateProperty", "descriptor", "_name", "get", "slotSelector", "slotEl", "this", "renderRoot", "querySelector", "elements", "matches", "enumerable", "configurable"], "mappings": ";;;;;SAmBA,MAKMA,EACkD,OAAhC,QAAtBC,EANyBC,WAMlBC,uBAAe,IAAAF,OAAA,EAAAA,EAAEG,UAAUC,kBAC9B,CAACC,EAAuBC,IACtBD,EAAKD,iBAAiBE,GACxB,CAACD,EAAuBC,IACtBD,EACGE,cAAcD,GACdE,QACEC,GAA0BA,EAAKC,WAAaC,KAAKC,eA8CxD,SAAUC,EAAsBC,GACpC,MAAMT,KAACA,EAAIU,SAAEA,GAAYD,QAAAA,EAAW,GACpC,OAAOE,EAAiB,CACtBC,WAAaC,IAAwB,CACnCC,YACE,MAAMC,EAAe,QAAOf,EAAO,SAASA,KAAU,gBAChDgB,EACW,QAAfrB,EAAAsB,KAAKC,kBAAU,IAAAvB,OAAA,EAAAA,EAAEwB,cAA+BJ,GAC5CK,EACM,MAAVJ,EAAiBtB,EAAqBsB,EAAQP,GAAW,GAC3D,OAAIC,EACKU,EAASjB,QAAQC,GAASA,EAAKiB,QAAQX,KAEzCU,CACR,EACDE,YAAY,EACZC,cAAc,KAGpB"}