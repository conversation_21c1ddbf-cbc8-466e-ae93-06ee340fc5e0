{"version": 3, "file": "query.js", "sources": ["../../../src/decorators/query.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/*\n * IMPORTANT: For compatibility with <PERSON><PERSON><PERSON> and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\n\nimport {ReactiveElement} from '../reactive-element.js';\nimport {decorateProperty} from './base.js';\n\n/**\n * A property decorator that converts a class property into a getter that\n * executes a querySelector on the element's renderRoot.\n *\n * @param selector A DOMString containing one or more selectors to match.\n * @param cache An optional boolean which when true performs the DOM query only\n *     once and caches the result.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/Document/querySelector\n *\n * ```ts\n * class MyElement {\n *   @query('#first')\n *   first: HTMLDivElement;\n *\n *   render() {\n *     return html`\n *       <div id=\"first\"></div>\n *       <div id=\"second\"></div>\n *     `;\n *   }\n * }\n * ```\n * @category Decorator\n */\nexport function query(selector: string, cache?: boolean) {\n  return decorateProperty({\n    descriptor: (name: PropertyKey) => {\n      const descriptor = {\n        get(this: ReactiveElement) {\n          return this.renderRoot?.querySelector(selector) ?? null;\n        },\n        enumerable: true,\n        configurable: true,\n      };\n      if (cache) {\n        const key = typeof name === 'symbol' ? Symbol() : `__${name}`;\n        descriptor.get = function (this: ReactiveElement) {\n          if (\n            (this as unknown as {[key: string]: Element | null})[\n              key as string\n            ] === undefined\n          ) {\n            (this as unknown as {[key: string]: Element | null})[\n              key as string\n            ] = this.renderRoot?.querySelector(selector) ?? null;\n          }\n          return (this as unknown as {[key: string]: Element | null})[\n            key as string\n          ];\n        };\n      }\n      return descriptor;\n    },\n  });\n}\n"], "names": [], "mappings": ";;AAAA;;;;AAIG;AAYH;;;;;;;;;;;;;;;;;;;;;;;;AAwBG;AACa,SAAA,KAAK,CAAC,QAAgB,EAAE,KAAe,EAAA;AACrD,IAAA,OAAO,gBAAgB,CAAC;AACtB,QAAA,UAAU,EAAE,CAAC,IAAiB,KAAI;AAChC,YAAA,MAAM,UAAU,GAAG;gBACjB,GAAG,GAAA;;AACD,oBAAA,OAAO,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,aAAa,CAAC,QAAQ,CAAC,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,IAAI,CAAC;iBACzD;AACD,gBAAA,UAAU,EAAE,IAAI;AAChB,gBAAA,YAAY,EAAE,IAAI;aACnB,CAAC;AACF,YAAA,IAAI,KAAK,EAAE;AACT,gBAAA,MAAM,GAAG,GAAG,OAAO,IAAI,KAAK,QAAQ,GAAG,MAAM,EAAE,GAAG,CAAK,EAAA,EAAA,IAAI,EAAE,CAAC;gBAC9D,UAAU,CAAC,GAAG,GAAG,YAAA;;AACf,oBAAA,IACG,IAAmD,CAClD,GAAa,CACd,KAAK,SAAS,EACf;AACC,wBAAA,IAAmD,CAClD,GAAa,CACd,GAAG,CAAA,EAAA,GAAA,MAAA,IAAI,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,aAAa,CAAC,QAAQ,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,IAAI,CAAC;AACtD,qBAAA;AACD,oBAAA,OAAQ,IAAmD,CACzD,GAAa,CACd,CAAC;AACJ,iBAAC,CAAC;AACH,aAAA;AACD,YAAA,OAAO,UAAU,CAAC;SACnB;AACF,KAAA,CAAC,CAAC;AACL;;;;"}