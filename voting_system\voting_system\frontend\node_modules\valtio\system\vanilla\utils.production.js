System.register(["valtio/vanilla"],function(M){"use strict";var O,y,b,x,_;return{setters:[function(v){O=v.subscribe,y=v.snapshot,b=v.proxy,x=v.getVersion,_=v.ref}],execute:function(){M({addComputed:W,derive:N,devtools:H,proxyMap:X,proxySet:V,proxyWithComputed:U,proxyWithHistory:K,subscribeKey:v,underive:R,watch:J});function v(s,t,e,n){let r=s[t];return O(s,()=>{const o=s[t];Object.is(r,o)||e(r=o)},n)}let g;function J(s,t){let e=!0;const n=new Set,r=new Map,o=()=>{e&&(e=!1,n.forEach(i=>i()),n.clear(),r.forEach(i=>i()),r.clear())},l=()=>{if(!e)return;n.forEach(a=>a()),n.clear();const i=new Set,u=g;g=n;try{const a=s(d=>(i.add(d),d));a&&n.add(a)}finally{g=u}r.forEach((a,d)=>{i.has(d)?i.delete(d):(r.delete(d),a())}),i.forEach(a=>{const d=O(a,l,t==null?void 0:t.sync);r.set(a,d)})};return g&&g.add(o),l(),o}const j=Symbol();function H(s,t){typeof t=="string"&&(console.warn("string name option is deprecated, use { name }. https://github.com/pmndrs/valtio/pull/400"),t={name:t});const{enabled:e,name:n=""}=t||{};let r;try{r=(e!=null?e:!1)&&window.__REDUX_DEVTOOLS_EXTENSION__}catch{}if(!r)return;let o=!1;const l=r.connect({name:n}),i=O(s,a=>{const d=a.filter(([h,c])=>c[0]!==j).map(([h,c])=>`${h}:${c.map(String).join(".")}`).join(", ");if(d)if(o)o=!1;else{const h=Object.assign({},y(s));delete h[j],l.send({type:d,updatedAt:new Date().toLocaleString()},h)}}),u=l.subscribe(a=>{var d,h,c,p,m,w;if(a.type==="ACTION"&&a.payload)try{Object.assign(s,JSON.parse(a.payload))}catch(f){console.error(`please dispatch a serializable value that JSON.parse() and proxy() support
`,f)}if(a.type==="DISPATCH"&&a.state){if(((d=a.payload)==null?void 0:d.type)==="JUMP_TO_ACTION"||((h=a.payload)==null?void 0:h.type)==="JUMP_TO_STATE"){o=!0;const f=JSON.parse(a.state);Object.assign(s,f)}s[j]=a}else if(a.type==="DISPATCH"&&((c=a.payload)==null?void 0:c.type)==="COMMIT")l.init(y(s));else if(a.type==="DISPATCH"&&((p=a.payload)==null?void 0:p.type)==="IMPORT_STATE"){const f=(m=a.payload.nextLiftedState)==null?void 0:m.actionsById,$=((w=a.payload.nextLiftedState)==null?void 0:w.computedStates)||[];o=!0,$.forEach(({state:B},I)=>{const q=f[I]||"No action found";Object.assign(s,B),I===0?l.init(y(s)):l.send(q,y(s))})}});return l.init(y(s)),()=>{i(),u==null||u()}}const S=new WeakMap,E=new WeakMap,D=(s,t)=>{const e=S.get(s);e&&(e[0].forEach(n=>{const{d:r}=n;s!==r&&D(r)}),++e[2],t&&e[3].add(t))},z=(s,t)=>{const e=S.get(s);return e!=null&&e[2]?(e[3].add(t),!0):!1},T=s=>{const t=S.get(s);t&&(--t[2],t[2]||(t[3].forEach(e=>e()),t[3].clear()),t[0].forEach(e=>{const{d:n}=e;s!==n&&T(n)}))},k=s=>{const{s:t,d:e}=s;let n=E.get(e);n||(n=[new Set],E.set(s.d,n)),n[0].add(s);let r=S.get(t);if(!r){const o=new Set,l=O(t,i=>{o.forEach(u=>{const{d:a,c:d,n:h,i:c}=u;t===a&&i.every(p=>p[1].length===1&&c.includes(p[1][0]))||u.p||(D(t,d),h?T(t):u.p=Promise.resolve().then(()=>{delete u.p,T(t)}))})},!0);r=[o,l,0,new Set],S.set(t,r)}r[0].add(s)},A=s=>{const{s:t,d:e}=s,n=E.get(e);n==null||n[0].delete(s),(n==null?void 0:n[0].size)===0&&E.delete(e);const r=S.get(t);if(r){const[o,l]=r;o.delete(s),o.size||(l(),S.delete(t))}},C=s=>{const t=E.get(s);return t?Array.from(t[0]):[]},F=M("unstable_deriveSubscriptions",{add:k,remove:A,list:C});function N(s,t){const e=(t==null?void 0:t.proxy)||b({}),n=!!(t!=null&&t.sync),r=Object.keys(s);return r.forEach(o=>{if(Object.getOwnPropertyDescriptor(e,o))throw new Error("object property already defined");const l=s[o];let i=null;const u=()=>{if(i&&(Array.from(i).map(([c])=>z(c,u)).some(c=>c)||Array.from(i).every(([c,p])=>x(c)===p.v)))return;const a=new Map,d=l(c=>(a.set(c,{v:x(c)}),c)),h=()=>{a.forEach((c,p)=>{var m;const w=(m=i==null?void 0:i.get(p))==null?void 0:m.s;if(w)c.s=w;else{const f={s:p,d:e,k:o,c:u,n,i:r};k(f),c.s=f}}),i==null||i.forEach((c,p)=>{!a.has(p)&&c.s&&A(c.s)}),i=a};d instanceof Promise?d.finally(h):h(),e[o]=d};u()}),e}function R(s,t){const e=t!=null&&t.delete?new Set:null;C(s).forEach(n=>{const{k:r}=n;(!(t!=null&&t.keys)||t.keys.includes(r))&&(A(n),e&&e.add(r))}),e&&e.forEach(n=>{delete s[n]})}function W(s,t,e=s){const n={};return Object.keys(t).forEach(r=>{n[r]=o=>t[r](o(s))}),N(n,{proxy:e})}function U(s,t){Object.keys(t).forEach(n=>{if(Object.getOwnPropertyDescriptor(s,n))throw new Error("object property already defined");const r=t[n],{get:o,set:l}=typeof r=="function"?{get:r}:r,i={};i.get=()=>o(y(e)),l&&(i.set=u=>l(e,u)),Object.defineProperty(s,n,i)});const e=b(s);return e}const L=s=>typeof s=="object"&&s!==null,P=s=>{if(!L(s))return s;const t=Array.isArray(s)?[]:Object.create(Object.getPrototypeOf(s));return Reflect.ownKeys(s).forEach(e=>{t[e]=P(s[e])}),t};function K(s,t=!1){const e=b({value:s,history:_({wip:void 0,snapshots:[],index:-1}),canUndo:()=>e.history.index>0,undo:()=>{e.canUndo()&&(e.value=e.history.wip=P(e.history.snapshots[--e.history.index]))},canRedo:()=>e.history.index<e.history.snapshots.length-1,redo:()=>{e.canRedo()&&(e.value=e.history.wip=P(e.history.snapshots[++e.history.index]))},saveHistory:()=>{e.history.snapshots.splice(e.history.index+1),e.history.snapshots.push(y(e).value),++e.history.index},subscribe:()=>O(e,n=>{n.every(r=>r[1][0]==="value"&&(r[0]!=="set"||r[2]!==e.history.wip))&&e.saveHistory()})});return e.saveHistory(),t||e.subscribe(),e}function V(s){const t=b({data:Array.from(new Set(s)),has(e){return this.data.indexOf(e)!==-1},add(e){let n=!1;return typeof e=="object"&&e!==null&&(n=this.data.indexOf(b(e))!==-1),this.data.indexOf(e)===-1&&!n&&this.data.push(e),this},delete(e){const n=this.data.indexOf(e);return n===-1?!1:(this.data.splice(n,1),!0)},clear(){this.data.splice(0)},get size(){return this.data.length},forEach(e){this.data.forEach(n=>{e(n,n,this)})},get[Symbol.toStringTag](){return"Set"},toJSON(){return new Set(this.data)},[Symbol.iterator](){return this.data[Symbol.iterator]()},values(){return this.data.values()},keys(){return this.data.values()},entries(){return new Set(this.data).entries()}});return Object.defineProperties(t,{data:{enumerable:!1},size:{enumerable:!1},toJSON:{enumerable:!1}}),Object.seal(t),t}function X(s){const t=b({data:Array.from(s||[]),has(e){return this.data.some(n=>n[0]===e)},set(e,n){const r=this.data.find(o=>o[0]===e);return r?r[1]=n:this.data.push([e,n]),this},get(e){var n;return(n=this.data.find(r=>r[0]===e))==null?void 0:n[1]},delete(e){const n=this.data.findIndex(r=>r[0]===e);return n===-1?!1:(this.data.splice(n,1),!0)},clear(){this.data.splice(0)},get size(){return this.data.length},toJSON(){return new Map(this.data)},forEach(e){this.data.forEach(n=>{e(n[1],n[0],this)})},keys(){return this.data.map(e=>e[0]).values()},values(){return this.data.map(e=>e[1]).values()},entries(){return new Map(this.data).entries()},get[Symbol.toStringTag](){return"Map"},[Symbol.iterator](){return this.entries()}});return Object.defineProperties(t,{data:{enumerable:!1},size:{enumerable:!1},toJSON:{enumerable:!1}}),Object.seal(t),t}}}});
