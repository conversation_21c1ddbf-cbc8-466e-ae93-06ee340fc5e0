{"version": 3, "file": "join.js", "sources": ["../../src/directives/join.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/**\n * Returns an iterable containing the values in `items` interleaved with the\n * `joiner` value.\n *\n * @example\n *\n * ```ts\n * render() {\n *   return html`\n *     ${join(items, html`<span class=\"separator\">|</span>`)}\n *   `;\n * }\n */\nexport function join<I, J>(\n  items: Iterable<I> | undefined,\n  joiner: (index: number) => J\n): Iterable<I | J>;\nexport function join<I, J>(\n  items: Iterable<I> | undefined,\n  joiner: J\n): Iterable<I | J>;\nexport function* join<I, J>(items: Iterable<I> | undefined, joiner: J) {\n  const isFunction = typeof joiner === 'function';\n  if (items !== undefined) {\n    let i = -1;\n    for (const value of items) {\n      if (i > -1) {\n        yield isFunction ? joiner(i) : joiner;\n      }\n      i++;\n      yield value;\n    }\n  }\n}\n"], "names": ["join", "items", "joiner", "isFunction", "undefined", "i", "value"], "mappings": ";;;;;SA2BiBA,EAAWC,EAAgCC,GAC1D,MAAMC,EAA+B,mBAAXD,EAC1B,QAAcE,IAAVH,EAAqB,CACvB,IAAII,GAAK,EACT,IAAK,MAAMC,KAASL,EACdI,GAAK,UACDF,EAAaD,EAAOG,GAAKH,GAEjCG,UACMC,CAET,CACH"}