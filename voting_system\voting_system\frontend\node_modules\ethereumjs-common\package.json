{"name": "ethereumjs-common", "version": "1.5.2", "description": "Resources common to all Ethereum implementations", "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"build": "ethereumjs-config-build", "prepublishOnly": "npm run test && npm run build", "coverage": "ethereumjs-config-coverage", "coveralls": "ethereumjs-config-coveralls", "format": "ethereumjs-config-format", "format:fix": "ethereumjs-config-format-fix", "tslint": "ethereumjs-config-tslint", "tslint:fix": "ethereumjs-config-tslint-fix", "tsc": "ethereumjs-config-tsc", "lint": "ethereumjs-config-lint", "lint:fix": "ethereumjs-config-lint-fix", "unitTests": "ts-node ./node_modules/tape/bin/tape ./tests/*.ts", "test": "npm run lint && npm run unitTests", "test:fix": "npm run lint:fix && npm run unitTests", "docs:build": "typedoc --options typedoc.js"}, "repository": {"type": "git", "url": "git+https://github.com/ethereumjs/ethereumjs-common.git"}, "keywords": ["ethereum", "ethereumjs", "constants", "parameters", "genesis", "networks", "bootstrap"], "author": "", "license": "MIT", "bugs": {"url": "https://github.com/ethereumjs/ethereumjs-common/issues"}, "homepage": "https://github.com/ethereumjs-ethereumjs/common", "devDependencies": {"@ethereumjs/config-nyc": "^1.1.0", "@ethereumjs/config-prettier": "^1.1.0", "@ethereumjs/config-tsc": "^1.1.0", "@ethereumjs/config-tslint": "^1.1.0", "@types/node": "^10.12.2", "@types/tape": "^4.2.33", "coveralls": "^3.0.1", "nyc": "^11.7.1", "prettier": "^1.15.3", "tape": "^4.9.2", "ts-node": "^7.0.1", "tslint": "^5.12.0", "typedoc": "next", "typedoc-plugin-markdown": "^2.2.17", "typescript": "^3.2.2", "typestrict": "^1.0.2"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON>@gmail.com"}], "dependencies": {}}