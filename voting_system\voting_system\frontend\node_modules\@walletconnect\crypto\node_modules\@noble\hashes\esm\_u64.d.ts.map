{"version": 3, "file": "_u64.d.ts", "sourceRoot": "", "sources": ["../src/_u64.ts"], "names": [], "mappings": "AAGA;;;;GAIG;AAEH,iBAAS,OAAO,CACd,CAAC,EAAE,MAAM,EACT,EAAE,UAAQ,GACT;IACD,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;CACX,CAGA;AAED,iBAAS,KAAK,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE,UAAQ,GAAG,WAAW,EAAE,CAQvD;AAED,QAAA,MAAM,KAAK,MAAO,MAAM,KAAK,MAAM,KAAG,MAAqD,CAAC;AAE5F,QAAA,MAAM,KAAK,MAAO,MAAM,MAAM,MAAM,KAAK,MAAM,KAAG,MAAiB,CAAC;AACpE,QAAA,MAAM,KAAK,MAAO,MAAM,KAAK,MAAM,KAAK,MAAM,KAAG,MAAqC,CAAC;AAEvF,QAAA,MAAM,MAAM,MAAO,MAAM,KAAK,MAAM,KAAK,MAAM,KAAG,MAAqC,CAAC;AACxF,QAAA,MAAM,MAAM,MAAO,MAAM,KAAK,MAAM,KAAK,MAAM,KAAG,MAAqC,CAAC;AAExF,QAAA,MAAM,MAAM,MAAO,MAAM,KAAK,MAAM,KAAK,MAAM,KAAG,MAA4C,CAAC;AAC/F,QAAA,MAAM,MAAM,MAAO,MAAM,KAAK,MAAM,KAAK,MAAM,KAAG,MAA4C,CAAC;AAE/F,QAAA,MAAM,OAAO,OAAQ,MAAM,KAAK,MAAM,KAAG,MAAW,CAAC;AACrD,QAAA,MAAM,OAAO,MAAO,MAAM,MAAM,MAAM,KAAG,MAAW,CAAC;AAErD,QAAA,MAAM,MAAM,MAAO,MAAM,KAAK,MAAM,KAAK,MAAM,KAAG,MAAqC,CAAC;AACxF,QAAA,MAAM,MAAM,MAAO,MAAM,KAAK,MAAM,KAAK,MAAM,KAAG,MAAqC,CAAC;AAExF,QAAA,MAAM,MAAM,MAAO,MAAM,KAAK,MAAM,KAAK,MAAM,KAAG,MAA4C,CAAC;AAC/F,QAAA,MAAM,MAAM,MAAO,MAAM,KAAK,MAAM,KAAK,MAAM,KAAG,MAA4C,CAAC;AAI/F,iBAAS,GAAG,CACV,EAAE,EAAE,MAAM,EACV,EAAE,EAAE,MAAM,EACV,EAAE,EAAE,MAAM,EACV,EAAE,EAAE,MAAM,GACT;IACD,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;CACX,CAGA;AAED,QAAA,MAAM,KAAK,OAAQ,MAAM,MAAM,MAAM,MAAM,MAAM,KAAG,MAA8C,CAAC;AACnG,QAAA,MAAM,KAAK,QAAS,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,KAAG,MACrB,CAAC;AAC7C,QAAA,MAAM,KAAK,OAAQ,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,KAAG,MACb,CAAC;AACpD,QAAA,MAAM,KAAK,QAAS,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,KAAG,MAC5B,CAAC;AAClD,QAAA,MAAM,KAAK,OAAQ,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,KAAG,MACZ,CAAC;AACjE,QAAA,MAAM,KAAK,QAAS,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,KAAG,MACnC,CAAC;AAGvD,OAAO,EACL,OAAO,EAAE,KAAK,EAAE,KAAK,EACrB,KAAK,EAAE,KAAK,EACZ,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAC9B,OAAO,EAAE,OAAO,EAChB,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAC9B,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,GAC9C,CAAC;AAEF,QAAA,MAAM,GAAG,EAAE;IAAE,OAAO,EAAE,OAAO,OAAO,CAAC;IAAC,KAAK,EAAE,OAAO,KAAK,CAAC;IAAC,KAAK,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,KAAK,MAAM,CAAC;IAAC,KAAK,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,KAAK,MAAM,CAAC;IAAC,KAAK,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,KAAK,MAAM,CAAC;IAAC,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,KAAK,MAAM,CAAC;IAAC,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,KAAK,MAAM,CAAC;IAAC,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,KAAK,MAAM,CAAC;IAAC,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,KAAK,MAAM,CAAC;IAAC,OAAO,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,KAAK,MAAM,CAAC;IAAC,OAAO,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,KAAK,MAAM,CAAC;IAAC,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,KAAK,MAAM,CAAC;IAAC,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,KAAK,MAAM,CAAC;IAAC,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,KAAK,MAAM,CAAC;IAAC,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,KAAK,MAAM,CAAC;IAAC,GAAG,EAAE,OAAO,GAAG,CAAC;IAAC,KAAK,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,KAAK,MAAM,CAAC;IAAC,KAAK,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,KAAK,MAAM,CAAC;IAAC,KAAK,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,KAAK,MAAM,CAAC;IAAC,KAAK,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,KAAK,MAAM,CAAC;IAAC,KAAK,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,KAAK,MAAM,CAAC;IAAC,KAAK,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,KAAK,MAAM,CAAC;CAOrpC,CAAC;AACF,eAAe,GAAG,CAAC"}