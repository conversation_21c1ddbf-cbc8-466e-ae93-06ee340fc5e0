export interface Encrypted {
    iv: Uint8Array;
    mac: Uint8Array;
    publicKey: Uint8Array;
    ciphertext: Uint8Array;
}
export interface EncryptOpts {
    iv?: Uint8Array;
    sender?: KeyPair;
}
export interface KeyPair {
    privateKey: Uint8Array;
    publicKey: Uint8Array;
}
export interface PNRG {
    isAvailable: boolean;
    randomBytes(length: number): Uint8Array;
}
//# sourceMappingURL=types.d.ts.map