# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# V<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2012-2014
# V<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2015-2016,2019,2021
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-03-18 23:13+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Czech (http://www.transifex.com/django/django/language/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n "
"<= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

msgid "Administrative Documentation"
msgstr "Dokumentace správy"

msgid "Home"
msgstr "Domů"

msgid "Documentation"
msgstr "Dokumentace"

msgid "Bookmarklets"
msgstr "Bookmarklety"

msgid "Documentation bookmarklets"
msgstr "Dokumentační bookmarklety"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"Pro nainstalování bookmarkletů, přetáhněte odkaz na vaše záložky "
"(„Oblíbené“), nebo klepněte pravým tlačítkem na odkaz a přidejte ho k vašim "
"záložkám. Nyní můžete zvolit bookmarklet z libovolné stránky jakéhokoli webu."

msgid "Documentation for this page"
msgstr "Dokumentace k této stránce"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Z libovolné stránky otevře dokumentaci pohledu (view), který generoval tuto "
"stránku."

msgid "Tags"
msgstr "Tagy"

msgid "List of all the template tags and their functions."
msgstr "Seznam všech šablonových tagů a jejich funkce."

msgid "Filters"
msgstr "Filtry"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Filtry jsou akce, které mohou být aplikovány na proměnné v šabloně za účelem "
"úpravy hodnoty."

msgid "Models"
msgstr "Modely"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Modely jsou popisy všech objektů v systému a jejich přidružených polí. Každý "
"model má seznam polí, které mohou být čteny jako proměnné v šablonách."

msgid "Views"
msgstr "Views"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Každá stránka na veřejném webu je generována pomocí pohledu. Pohled "
"definuje, která šablona bude použita ke generování stránky a které objekty "
"budou v šabloně k dispozici."

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""
"Nástroje pro prohlížeč k rychlému přístupu k funkcím administračního "
"rozhraní."

msgid "Please install docutils"
msgstr "Nainstalujte balík docutils"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"Dokumentační systém administračního rozhraní vyžaduje pythonskou knihovnu <a "
"href=\"%(link)s\">docutils</a>."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Požádejte správce o instalaci balíku <a href=\"%(link)s\">docutils</a>."

#, python-format
msgid "Model: %(name)s"
msgstr "Model: %(name)s"

msgid "Fields"
msgstr "Pole"

msgid "Field"
msgstr "Pole"

msgid "Type"
msgstr "Typ"

msgid "Description"
msgstr "Popis"

msgid "Methods with arguments"
msgstr "Metody s argumenty"

msgid "Method"
msgstr "Metoda"

msgid "Arguments"
msgstr "Argumenty"

msgid "Back to Model documentation"
msgstr "Zpět na popis modelů"

msgid "Model documentation"
msgstr "Dokumentace modelů"

msgid "Model groups"
msgstr "Skupiny modelů"

msgid "Templates"
msgstr "Šablony"

#, python-format
msgid "Template: %(name)s"
msgstr "Šablona: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Šablona: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "Vyhledávací cesta pro šablonu <q>%(name)s</q>:"

msgid "(does not exist)"
msgstr "(neexistuje)"

msgid "Back to Documentation"
msgstr "Zpět na dokumentaci"

msgid "Template filters"
msgstr "Šablonové filtry"

msgid "Template filter documentation"
msgstr "Dokumentace šablonových filtrů"

msgid "Built-in filters"
msgstr "Vestavěné filtry"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"Chcete-li tyto filtry používat, vložte do šablony před místo použití kód "
"<code>%(code)s</code>."

msgid "Template tags"
msgstr "Šablonové tagy"

msgid "Template tag documentation"
msgstr "Dokumentace šablonových tagů"

msgid "Built-in tags"
msgstr "Vestavěné tagy"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"Chcete-li tyto tagy používat, vložte do šablony před místo použití kód <code>"
"%(code)s</code>."

#, python-format
msgid "View: %(name)s"
msgstr "Pohled: %(name)s"

msgid "Context:"
msgstr "Kontext:"

msgid "Templates:"
msgstr "Šablony:"

msgid "Back to View documentation"
msgstr "Zpět na popis pohledů"

msgid "View documentation"
msgstr "Dokumentace pohledů"

msgid "Jump to namespace"
msgstr "Přejít na jmenný prostor"

msgid "Empty namespace"
msgstr "Prázdný jmenný prostor"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "Pohledy dle jmenného prostoru %(name)s"

msgid "Views by empty namespace"
msgstr "Pohledy dle prázdného jmenného prostoru"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"Funkce pohledu: <code>%(full_name)s</code>. Název: <code>%(url_name)s</"
"code>.\n"

msgid "tag:"
msgstr "tag:"

msgid "filter:"
msgstr "filtr:"

msgid "view:"
msgstr "pohled (view):"

#, python-format
msgid "App %(app_label)r not found"
msgstr "Aplikace %(app_label)r nebyla nalezena"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "Model %(model_name)r v aplikaci %(app_label)r nenalezen"

msgid "model:"
msgstr "model:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "související položka `%(app_label)s.%(data_type)s`"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "související položky `%(app_label)s.%(object_name)s`"

#, python-format
msgid "all %s"
msgstr "Vše: %s"

#, python-format
msgid "number of %s"
msgstr "Počet: %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "Objekt %s patrně není typu urlpattern."
