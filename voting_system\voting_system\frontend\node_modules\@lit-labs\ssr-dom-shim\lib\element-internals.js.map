{"version": 3, "file": "element-internals.js", "sourceRoot": "", "sources": ["../src/lib/element-internals.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAMH;;GAEG;AACH,MAAM,CAAC,MAAM,mBAAmB,GAAqB;IACnD,UAAU,EAAE,aAAa;IACzB,gBAAgB,EAAE,mBAAmB;IACrC,gBAAgB,EAAE,mBAAmB;IACrC,0BAA0B,EAAE,6BAA6B;IACzD,QAAQ,EAAE,WAAW;IACrB,WAAW,EAAE,cAAc;IAC3B,YAAY,EAAE,eAAe;IAC7B,YAAY,EAAE,eAAe;IAC7B,WAAW,EAAE,cAAc;IAC3B,WAAW,EAAE,cAAc;IAC3B,eAAe,EAAE,kBAAkB;IACnC,YAAY,EAAE,eAAe;IAC7B,YAAY,EAAE,eAAe;IAC7B,YAAY,EAAE,eAAe;IAC7B,UAAU,EAAE,aAAa;IACzB,WAAW,EAAE,cAAc;IAC3B,gBAAgB,EAAE,mBAAmB;IACrC,SAAS,EAAE,YAAY;IACvB,SAAS,EAAE,YAAY;IACvB,QAAQ,EAAE,WAAW;IACrB,SAAS,EAAE,YAAY;IACvB,aAAa,EAAE,gBAAgB;IAC/B,mBAAmB,EAAE,sBAAsB;IAC3C,eAAe,EAAE,kBAAkB;IACnC,eAAe,EAAE,kBAAkB;IACnC,YAAY,EAAE,eAAe;IAC7B,WAAW,EAAE,cAAc;IAC3B,YAAY,EAAE,eAAe;IAC7B,YAAY,EAAE,eAAe;IAC7B,mBAAmB,EAAE,sBAAsB;IAC3C,YAAY,EAAE,eAAe;IAC7B,YAAY,EAAE,eAAe;IAC7B,WAAW,EAAE,cAAc;IAC3B,YAAY,EAAE,eAAe;IAC7B,WAAW,EAAE,cAAc;IAC3B,QAAQ,EAAE,WAAW;IACrB,YAAY,EAAE,eAAe;IAC7B,YAAY,EAAE,eAAe;IAC7B,YAAY,EAAE,eAAe;IAC7B,aAAa,EAAE,gBAAgB;IAC/B,IAAI,EAAE,MAAM;CACb,CAAC;AAIF,2CAA2C;AAC3C,+DAA+D;AAC/D,0BAA0B;AAC1B,MAAM,CAAC,MAAM,oBAAoB,GAAG,MAAM,gBAAgB;IA6CxD,IAAI,UAAU;QACZ,sDAAsD;QACtD,qDAAqD;QACrD,yDAAyD;QACzD,OAAQ,IAAI,CAAC,MAAmD;aAC7D,YAAY,CAAC;IAClB,CAAC;IACD,YAAY,KAAkB;QAjD9B,eAAU,GAAG,EAAE,CAAC;QAChB,qBAAgB,GAAG,EAAE,CAAC;QACtB,qBAAgB,GAAG,EAAE,CAAC;QACtB,+BAA0B,GAAG,EAAE,CAAC;QAChC,aAAQ,GAAG,EAAE,CAAC;QACd,gBAAW,GAAG,EAAE,CAAC;QACjB,iBAAY,GAAG,EAAE,CAAC;QAClB,iBAAY,GAAG,EAAE,CAAC;QAClB,gBAAW,GAAG,EAAE,CAAC;QACjB,gBAAW,GAAG,EAAE,CAAC;QACjB,oBAAe,GAAG,EAAE,CAAC;QACrB,iBAAY,GAAG,EAAE,CAAC;QAClB,iBAAY,GAAG,EAAE,CAAC;QAClB,iBAAY,GAAG,EAAE,CAAC;QAClB,eAAU,GAAG,EAAE,CAAC;QAChB,gBAAW,GAAG,EAAE,CAAC;QACjB,qBAAgB,GAAG,EAAE,CAAC;QACtB,cAAS,GAAG,EAAE,CAAC;QACf,cAAS,GAAG,EAAE,CAAC;QACf,aAAQ,GAAG,EAAE,CAAC;QACd,cAAS,GAAG,EAAE,CAAC;QACf,kBAAa,GAAG,EAAE,CAAC;QACnB,wBAAmB,GAAG,EAAE,CAAC;QACzB,oBAAe,GAAG,EAAE,CAAC;QACrB,oBAAe,GAAG,EAAE,CAAC;QACrB,iBAAY,GAAG,EAAE,CAAC;QAClB,gBAAW,GAAG,EAAE,CAAC;QACjB,iBAAY,GAAG,EAAE,CAAC;QAClB,iBAAY,GAAG,EAAE,CAAC;QAClB,wBAAmB,GAAG,EAAE,CAAC;QACzB,iBAAY,GAAG,EAAE,CAAC;QAClB,iBAAY,GAAG,EAAE,CAAC;QAClB,gBAAW,GAAG,EAAE,CAAC;QACjB,iBAAY,GAAG,EAAE,CAAC;QAClB,gBAAW,GAAG,EAAE,CAAC;QACjB,aAAQ,GAAG,EAAE,CAAC;QACd,iBAAY,GAAG,EAAE,CAAC;QAClB,iBAAY,GAAG,EAAE,CAAC;QAClB,iBAAY,GAAG,EAAE,CAAC;QAClB,kBAAa,GAAG,EAAE,CAAC;QACnB,SAAI,GAAG,EAAE,CAAC;QAqBV,SAAI,GAAG,IAAI,CAAC;QACZ,WAAM,GAAG,EAA6C,CAAC;QAMvD,WAAM,GAAG,IAAI,GAAG,EAAU,CAAC;QAC3B,sBAAiB,GAAG,EAAE,CAAC;QACvB,aAAQ,GAAG,EAAmB,CAAC;QAC/B,iBAAY,GAAG,IAAI,CAAC;QArBlB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACtB,CAAC;IACD,aAAa;QACX,uDAAuD;QACvD,6CAA6C;QAC7C,OAAO,CAAC,IAAI,CACV,8DAA8D;YAC5D,kCAAkC,CACrC,CAAC;QACF,OAAO,IAAI,CAAC;IACd,CAAC;IAGD,cAAc;QACZ,OAAO,IAAI,CAAC;IACd,CAAC;IACD,YAAY,KAAU,CAAC;IACvB,WAAW,KAAU,CAAC;CAKvB,CAAC;AAEF,MAAM,gCAAgC,GACpC,oBAAyD,CAAC;AAC5D,OAAO,EAAC,gCAAgC,IAAI,gBAAgB,EAAC,CAAC;AAE9D,MAAM,CAAC,MAAM,6BAA6B,GAAG,oBAAoB,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\ntype ARIAAttributeMap = {\n  [K in keyof ARIAMixin]: string;\n};\n\n/**\n * Map of ARIAMixin properties to attributes\n */\nexport const ariaMixinAttributes: ARIAAttributeMap = {\n  ariaAtomic: 'aria-atomic',\n  ariaAutoComplete: 'aria-autocomplete',\n  ariaBrailleLabel: 'aria-braillelabel',\n  ariaBrailleRoleDescription: 'aria-brailleroledescription',\n  ariaBusy: 'aria-busy',\n  ariaChecked: 'aria-checked',\n  ariaColCount: 'aria-colcount',\n  ariaColIndex: 'aria-colindex',\n  ariaColSpan: 'aria-colspan',\n  ariaCurrent: 'aria-current',\n  ariaDescription: 'aria-description',\n  ariaDisabled: 'aria-disabled',\n  ariaExpanded: 'aria-expanded',\n  ariaHasPopup: 'aria-haspopup',\n  ariaHidden: 'aria-hidden',\n  ariaInvalid: 'aria-invalid',\n  ariaKeyShortcuts: 'aria-keyshortcuts',\n  ariaLabel: 'aria-label',\n  ariaLevel: 'aria-level',\n  ariaLive: 'aria-live',\n  ariaModal: 'aria-modal',\n  ariaMultiLine: 'aria-multiline',\n  ariaMultiSelectable: 'aria-multiselectable',\n  ariaOrientation: 'aria-orientation',\n  ariaPlaceholder: 'aria-placeholder',\n  ariaPosInSet: 'aria-posinset',\n  ariaPressed: 'aria-pressed',\n  ariaReadOnly: 'aria-readonly',\n  ariaRequired: 'aria-required',\n  ariaRoleDescription: 'aria-roledescription',\n  ariaRowCount: 'aria-rowcount',\n  ariaRowIndex: 'aria-rowindex',\n  ariaRowSpan: 'aria-rowspan',\n  ariaSelected: 'aria-selected',\n  ariaSetSize: 'aria-setsize',\n  ariaSort: 'aria-sort',\n  ariaValueMax: 'aria-valuemax',\n  ariaValueMin: 'aria-valuemin',\n  ariaValueNow: 'aria-valuenow',\n  ariaValueText: 'aria-valuetext',\n  role: 'role',\n};\n\ntype ElementInternalsInterface = ElementInternals;\n\n// Shim the global element internals object\n// Methods should be fine as noops and properties can generally\n// be while on the server.\nexport const ElementInternalsShim = class ElementInternals\n  implements ElementInternalsInterface\n{\n  ariaAtomic = '';\n  ariaAutoComplete = '';\n  ariaBrailleLabel = '';\n  ariaBrailleRoleDescription = '';\n  ariaBusy = '';\n  ariaChecked = '';\n  ariaColCount = '';\n  ariaColIndex = '';\n  ariaColSpan = '';\n  ariaCurrent = '';\n  ariaDescription = '';\n  ariaDisabled = '';\n  ariaExpanded = '';\n  ariaHasPopup = '';\n  ariaHidden = '';\n  ariaInvalid = '';\n  ariaKeyShortcuts = '';\n  ariaLabel = '';\n  ariaLevel = '';\n  ariaLive = '';\n  ariaModal = '';\n  ariaMultiLine = '';\n  ariaMultiSelectable = '';\n  ariaOrientation = '';\n  ariaPlaceholder = '';\n  ariaPosInSet = '';\n  ariaPressed = '';\n  ariaReadOnly = '';\n  ariaRequired = '';\n  ariaRoleDescription = '';\n  ariaRowCount = '';\n  ariaRowIndex = '';\n  ariaRowSpan = '';\n  ariaSelected = '';\n  ariaSetSize = '';\n  ariaSort = '';\n  ariaValueMax = '';\n  ariaValueMin = '';\n  ariaValueNow = '';\n  ariaValueText = '';\n  role = '';\n  __host: HTMLElement;\n  get shadowRoot() {\n    // Grab the shadow root instance from the Element shim\n    // to ensure that the shadow root is always available\n    // to the internals instance even if the mode is 'closed'\n    return (this.__host as HTMLElement & {__shadowRoot: ShadowRoot})\n      .__shadowRoot;\n  }\n  constructor(_host: HTMLElement) {\n    this.__host = _host;\n  }\n  checkValidity() {\n    // TODO(augustjk) Consider actually implementing logic.\n    // See https://github.com/lit/lit/issues/3740\n    console.warn(\n      '`ElementInternals.checkValidity()` was called on the server.' +\n        'This method always returns true.'\n    );\n    return true;\n  }\n  form = null;\n  labels = [] as unknown as NodeListOf<HTMLLabelElement>;\n  reportValidity() {\n    return true;\n  }\n  setFormValue(): void {}\n  setValidity(): void {}\n  states = new Set<string>();\n  validationMessage = '';\n  validity = {} as ValidityState;\n  willValidate = true;\n};\n\nconst ElementInternalsShimWithRealType =\n  ElementInternalsShim as object as typeof ElementInternals;\nexport {ElementInternalsShimWithRealType as ElementInternals};\n\nexport const HYDRATE_INTERNALS_ATTR_PREFIX = 'hydrate-internals-';\n"]}