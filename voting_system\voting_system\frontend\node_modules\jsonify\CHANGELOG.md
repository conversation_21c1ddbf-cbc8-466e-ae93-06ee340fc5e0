# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v0.0.1](https://github.com/ljharb/jsonify/compare/v0.0.0...v0.0.1) - 2022-10-11

### Commits

- [eslint] fix indentation [`1c92797`](https://github.com/ljharb/jsonify/commit/1c92797745b21701242c14f53308d5edfef54025)
- [eslint] add eslint [`6a05332`](https://github.com/ljharb/jsonify/commit/6a05332907827cdf79a515911aec6e12625f8c5c)
- [readme] rename, add badges, soft-wrap [`cb1bd1a`](https://github.com/ljharb/jsonify/commit/cb1bd1a464733b27acd4e42062bb47f04e4e8064)
- [actions] add reusable workflows [`49710ae`](https://github.com/ljharb/jsonify/commit/49710aef8d4a92178cab79015c487e83e2211a37)
- [eslint] use function decls, but avoid relying on hoisting [`dcba2f4`](https://github.com/ljharb/jsonify/commit/dcba2f47667ca9c77fd3ce97f0c8cfa50d66a371)
- [Tests] switch to `tape` [`d31aaa0`](https://github.com/ljharb/jsonify/commit/d31aaa075e57b13587d81d5c08dede3ec3cd8438)
- [meta] add `auto-changelog` [`474188e`](https://github.com/ljharb/jsonify/commit/474188e42a5074cd43038e9f57c90e88708c3633)
- using travis [`7064ab5`](https://github.com/ljharb/jsonify/commit/7064ab53e5f73fa31e2ce7aa47d210c539662f16)
- [Refactor] `parse`: move fn comments outside of the fn body [`78f111a`](https://github.com/ljharb/jsonify/commit/78f111a8d8810d14a732a6395787b1ff5a2f899f)
- [meta] create FUNDING.yml; add `funding` in package.json [`98db8e1`](https://github.com/ljharb/jsonify/commit/98db8e1d0793f57308fcda823c568f47e90702a1)
- [meta] use `npmignore` to autogenerate an npmignore file [`1732b45`](https://github.com/ljharb/jsonify/commit/1732b4537dd952a04bee290310caed6b5fd3ade7)
- [meta] update URLs [`26ded36`](https://github.com/ljharb/jsonify/commit/26ded3613780aeb41f802f4cf98e3337ac3e783d)
- install notes [`57daaee`](https://github.com/ljharb/jsonify/commit/57daaeeb1c2d7a183cfc32727d5b447a3268c533)
- Only apps should have lockfiles [`1f25819`](https://github.com/ljharb/jsonify/commit/1f2581918edc11f55fac1be574a695444eff0d58)
- [meta] add `safe-publish-latest` [`8f3ef5e`](https://github.com/ljharb/jsonify/commit/8f3ef5e90262167420d71f08fe50a568499563d6)
- [Tests] add `aud` in `posttest` [`4c64711`](https://github.com/ljharb/jsonify/commit/4c64711da1a2c3c20c0c27e4f6d18f61c94df223)
- Update tap. [`8cf38a6`](https://github.com/ljharb/jsonify/commit/8cf38a62f34dd05c812829c6d0a9ee1a9486dc8a)
- fix broken browserify link [`9dc7744`](https://github.com/ljharb/jsonify/commit/9dc77442cc375823640f13086d5737fdd918bccf)

## v0.0.0 - 2011-08-21

### Commits

- first commit [`11c7bdf`](https://github.com/ljharb/jsonify/commit/11c7bdf47c3f21b014b1e984be52211142d8d8b7)
- the recursive descent parser is the fastest, going with it [`9210504`](https://github.com/ljharb/jsonify/commit/92105047b01811d216777d1b260373431b406f53)
- took out unnecessary functions from json.js, only stringify-related stuff remain [`99a8b65`](https://github.com/ljharb/jsonify/commit/99a8b6580af5dedebb39db18127c671b405c26d3)
- fix parse by removing another callback of indirection [`10e1b96`](https://github.com/ljharb/jsonify/commit/10e1b96b535cd66c0aee768aa6dbfc9778e87dbf)
- Hygiene [`633fe5a`](https://github.com/ljharb/jsonify/commit/633fe5ae4bd69985254a68ae111505ec3d3e74ab)
- a package.json [`5258ce2`](https://github.com/ljharb/jsonify/commit/5258ce240702c78f4affffdc3ea2e095ad04f49f)
- pared down parse.js [`40d8617`](https://github.com/ljharb/jsonify/commit/40d86179553cc972b465c2446f58a7ecda1f24ce)
- Style conformance. [`ad6079c`](https://github.com/ljharb/jsonify/commit/ad6079cbd8dc362a3cc42e1f97c01aa5ccd48bfe)
- forgot the readme and index.js [`69d3062`](https://github.com/ljharb/jsonify/commit/69d306269813548103c027df972d960d1b439eef)
- stringify test using garbage passes [`29db45f`](https://github.com/ljharb/jsonify/commit/29db45f4cd72dca91052baf744656d9561716f55)
- parse test passes too hooray [`d58c20e`](https://github.com/ljharb/jsonify/commit/d58c20ee3e2550490bfb5380ad1b0c085ee12e02)
- Create a JSON object only if one does not already exist. [`8d11dc6`](https://github.com/ljharb/jsonify/commit/8d11dc6950eafc7d01f141ce91d4f585caa29f3b)
- !isFinite [`8e0b15c`](https://github.com/ljharb/jsonify/commit/8e0b15cb492f63067a88ad786e4d5fc0fa89a241)
- move into lib [`006d2aa`](https://github.com/ljharb/jsonify/commit/006d2aaf373382b95801964d5b6505d9b79b3a16)
