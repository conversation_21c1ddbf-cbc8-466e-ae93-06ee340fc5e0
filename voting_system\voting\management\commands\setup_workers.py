from django.core.management.base import BaseCommand
from django.contrib.auth.models import User, Group, Permission
from django.contrib.contenttypes.models import ContentType
from voting.models import Worker, PollingStation, County, Constituency


class Command(BaseCommand):
    help = 'Create worker accounts and set up permissions'

    def add_arguments(self, parser):
        parser.add_argument(
            '--create-superuser',
            action='store_true',
            help='Create a superuser account',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Setting up worker system...'))

        # Create worker group and permissions
        worker_group, created = Group.objects.get_or_create(name='Registration Workers')
        if created:
            self.stdout.write(self.style.SUCCESS('Created Registration Workers group'))

            # Add permissions to the worker group
            content_type = ContentType.objects.get_for_model(Worker)
            permissions = [
                'add_voter',
                'change_voter',
                'view_voter',
                'view_validnationalid',
            ]
            
            for perm_codename in permissions:
                try:
                    permission = Permission.objects.get(codename=perm_codename)
                    worker_group.permissions.add(permission)
                    self.stdout.write(f'Added permission: {perm_codename}')
                except Permission.DoesNotExist:
                    self.stdout.write(self.style.WARNING(f'Permission not found: {perm_codename}'))

        # Create superuser if requested
        if options['create_superuser']:
            if not User.objects.filter(username='admin').exists():
                admin_user = User.objects.create_superuser(
                    username='admin',
                    email='<EMAIL>',
                    password='admin123',
                    first_name='System',
                    last_name='Administrator'
                )
                self.stdout.write(self.style.SUCCESS('Created superuser: admin/admin123'))
            else:
                self.stdout.write(self.style.WARNING('Superuser already exists'))

        # Create sample workers
        sample_workers = [
            {
                'username': 'worker1',
                'password': 'worker123',
                'first_name': 'John',
                'last_name': 'Kamau',
                'employee_id': 'EMP001',
                'phone_number': '+254712345678'
            },
            {
                'username': 'worker2',
                'password': 'worker123',
                'first_name': 'Mary',
                'last_name': 'Wanjiku',
                'employee_id': 'EMP002',
                'phone_number': '+254723456789'
            },
            {
                'username': 'worker3',
                'password': 'worker123',
                'first_name': 'Peter',
                'last_name': 'Ochieng',
                'employee_id': 'EMP003',
                'phone_number': '+254734567890'
            }
        ]

        for worker_data in sample_workers:
            if not User.objects.filter(username=worker_data['username']).exists():
                # Create user
                user = User.objects.create_user(
                    username=worker_data['username'],
                    password=worker_data['password'],
                    first_name=worker_data['first_name'],
                    last_name=worker_data['last_name']
                )
                
                # Add user to worker group
                user.groups.add(worker_group)
                
                # Get a polling station (use first available)
                polling_station = PollingStation.objects.first()
                
                # Create worker profile
                worker = Worker.objects.create(
                    user=user,
                    employee_id=worker_data['employee_id'],
                    phone_number=worker_data['phone_number'],
                    station_assigned=polling_station,
                    created_by=User.objects.filter(is_superuser=True).first()
                )
                
                self.stdout.write(
                    self.style.SUCCESS(
                        f'Created worker: {worker_data["username"]}/{worker_data["password"]} '
                        f'({worker_data["first_name"]} {worker_data["last_name"]})'
                    )
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f'Worker {worker_data["username"]} already exists')
                )

        self.stdout.write(self.style.SUCCESS('\nWorker setup completed!'))
        self.stdout.write(self.style.SUCCESS('\nLogin credentials:'))
        self.stdout.write('Superuser: admin/admin123')
        self.stdout.write('Workers: worker1/worker123, worker2/worker123, worker3/worker123')
