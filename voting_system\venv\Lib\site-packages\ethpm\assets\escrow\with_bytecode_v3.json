{"contractTypes": {"Escrow": {"abi": [{"constant": true, "inputs": [], "name": "recipient", "outputs": [{"name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "sender", "outputs": [{"name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [], "name": "releaseFunds", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"name": "_recipient", "type": "address"}], "payable": true, "stateMutability": "payable", "type": "constructor"}], "deploymentBytecode": {"bytecode": "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", "linkReferences": [{"length": 20, "name": "SafeSendLib", "offsets": [691, 1081]}]}, "runtimeBytecode": {"bytecode": "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", "linkReferences": [{"length": 20, "name": "SafeSendLib", "offsets": [508, 898]}]}}, "SafeSendLib": {"abi": [{"constant": false, "inputs": [{"name": "recipient", "type": "address"}, {"name": "value", "type": "uint256"}], "name": "sendOrThrow", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}], "deploymentBytecode": {"bytecode": "0x61015e610030600b82828239805160001a6073146000811461002057610022565bfe5b5030600052607381538281f3007300000000000000000000000000000000000000003014608060405260043610610058576000357c0100000000000000000000000000000000000000000000000000000000900463ffffffff1680639341231c1461005d575b600080fd5b81801561006957600080fd5b506100a8600480360381019080803573ffffffffffffffffffffffffffffffffffffffff169060200190929190803590602001909291905050506100c2565b604051808215151515815260200191505060405180910390f35b60003073ffffffffffffffffffffffffffffffffffffffff16318211156100e857600080fd5b8273ffffffffffffffffffffffffffffffffffffffff166108fc839081150290604051600060405180830381858888f19350505050151561012857600080fd5b60019050929150505600a165627a7a7230582041b548ecef0d0db47b915605a920606883582fc2f47f7a5d3d55692c21d26a3f0029"}, "runtimeBytecode": {"bytecode": "0x7300000000000000000000000000000000000000003014608060405260043610610058576000357c0100000000000000000000000000000000000000000000000000000000900463ffffffff1680639341231c1461005d575b600080fd5b81801561006957600080fd5b506100a8600480360381019080803573ffffffffffffffffffffffffffffffffffffffff169060200190929190803590602001909291905050506100c2565b604051808215151515815260200191505060405180910390f35b60003073ffffffffffffffffffffffffffffffffffffffff16318211156100e857600080fd5b8273ffffffffffffffffffffffffffffffffffffffff166108fc839081150290604051600060405180830381858888f19350505050151561012857600080fd5b60019050929150505600a165627a7a7230582041b548ecef0d0db47b915605a920606883582fc2f47f7a5d3d55692c21d26a3f0029"}}}, "manifest": "ethpm/3", "name": "escrow", "sources": {"Escrow.sol": {"urls": ["ipfs://Qmbm91zWRqwjuRTSbuyVNUAV7umu5o594MzBMxWbEMRQPj"]}, "SafeSendLib.sol": {"urls": ["ipfs://QmXsTBDZvtGBsJHg1HKinz1p6QvhphLV8UPX6Jqo3LcKW3"]}}, "version": "1.0.3"}