"""
Generated by `compile_contracts.py` script.
Compiled with Solidity v0.8.19.
"""

# source: web3/_utils/contract_sources/TupleContracts.sol:TupleContract
TUPLE_CONTRACT_BYTECODE = "0x608060405234801561001057600080fd5b50610abb806100206000396000f3fe608060405234801561001057600080fd5b506004361061002b5760003560e01c80638e1ae3c714610030575b600080fd5b61004a6004803603810190610045919061067b565b610060565b6040516100579190610a63565b60405180910390f35b610068610070565b819050919050565b60405180606001604052806000815260200160608152602001606081525090565b6000604051905090565b600080fd5b600080fd5b600080fd5b6000601f19601f8301169050919050565b7f4e487b7100000000000000000000000000000000000000000000000000000000600052604160045260246000fd5b6100f3826100aa565b810181811067ffffffffffffffff82111715610112576101116100bb565b5b80604052505050565b6000610125610091565b905061013182826100ea565b919050565b600080fd5b6000819050919050565b61014e8161013b565b811461015957600080fd5b50565b60008135905061016b81610145565b92915050565b600080fd5b600067ffffffffffffffff821115610191576101906100bb565b5b602082029050602081019050919050565b600080fd5b60006101ba6101b584610176565b61011b565b905080838252602082019050602084028301858111156101dd576101dc6101a2565b5b835b8181101561020657806101f2888261015c565b8452602084019350506020810190506101df565b5050509392505050565b600082601f83011261022557610224610171565b5b81356102358482602086016101a7565b91505092915050565b600067ffffffffffffffff821115610259576102586100bb565b5b602082029050602081019050919050565b6000819050919050565b61027d8161026a565b811461028857600080fd5b50565b60008135905061029a81610274565b92915050565b600067ffffffffffffffff8211156102bb576102ba6100bb565b5b602082029050919050565b60008115159050919050565b6102db816102c6565b81146102e657600080fd5b50565b6000813590506102f8816102d2565b92915050565b600061031161030c846102a0565b61011b565b9050806020840283018581111561032b5761032a6101a2565b5b835b81811015610354578061034088826102e9565b84526020840193505060208101905061032d565b5050509392505050565b600082601f83011261037357610372610171565b5b60026103808482856102fe565b91505092915050565b600067ffffffffffffffff8211156103a4576103a36100bb565b5b602082029050602081019050919050565b600073ffffffffffffffffffffffffffffffffffffffff82169050919050565b60006103e0826103b5565b9050919050565b6103f0816103d5565b81146103fb57600080fd5b50565b60008135905061040d816103e7565b92915050565b600061042661042184610389565b61011b565b90508083825260208201905060208402830185811115610449576104486101a2565b5b835b81811015610472578061045e88826103fe565b84526020840193505060208101905061044b565b5050509392505050565b600082601f83011261049157610490610171565b5b81356104a1848260208601610413565b91505092915050565b6000608082840312156104c0576104bf6100a5565b5b6104ca606061011b565b905060006104da8482850161028b565b60008301525060206104ee8482850161035e565b602083015250606082013567ffffffffffffffff81111561051257610511610136565b5b61051e8482850161047c565b60408301525092915050565b600061053d6105388461023e565b61011b565b905080838252602082019050602084028301858111156105605761055f6101a2565b5b835b818110156105a757803567ffffffffffffffff81111561058557610584610171565b5b80860161059289826104aa565b85526020850194505050602081019050610562565b5050509392505050565b600082601f8301126105c6576105c5610171565b5b81356105d684826020860161052a565b91505092915050565b6000606082840312156105f5576105f46100a5565b5b6105ff606061011b565b9050600061060f8482850161015c565b600083015250602082013567ffffffffffffffff81111561063357610632610136565b5b61063f84828501610210565b602083015250604082013567ffffffffffffffff81111561066357610662610136565b5b61066f848285016105b1565b60408301525092915050565b6000602082840312156106915761069061009b565b5b600082013567ffffffffffffffff8111156106af576106ae6100a0565b5b6106bb848285016105df565b91505092915050565b6106cd8161013b565b82525050565b600081519050919050565b600082825260208201905092915050565b6000819050602082019050919050565b600061070b83836106c4565b60208301905092915050565b6000602082019050919050565b600061072f826106d3565b61073981856106de565b9350610744836106ef565b8060005b8381101561077557815161075c88826106ff565b975061076783610717565b925050600181019050610748565b5085935050505092915050565b600081519050919050565b600082825260208201905092915050565b6000819050602082019050919050565b6107b78161026a565b82525050565b600060029050919050565b600081905092915050565b6000819050919050565b6107e6816102c6565b82525050565b60006107f883836107dd565b60208301905092915050565b6000602082019050919050565b61081a816107bd565b61082481846107c8565b925061082f826107d3565b8060005b8381101561086057815161084787826107ec565b965061085283610804565b925050600181019050610833565b505050505050565b600081519050919050565b600082825260208201905092915050565b6000819050602082019050919050565b61089d816103d5565b82525050565b60006108af8383610894565b60208301905092915050565b6000602082019050919050565b60006108d382610868565b6108dd8185610873565b93506108e883610884565b8060005b8381101561091957815161090088826108a3565b975061090b836108bb565b9250506001810190506108ec565b5085935050505092915050565b600060808301600083015161093e60008601826107ae565b5060208301516109516020860182610811565b506040830151848203606086015261096982826108c8565b9150508091505092915050565b60006109828383610926565b905092915050565b6000602082019050919050565b60006109a282610782565b6109ac818561078d565b9350836020820285016109be8561079e565b8060005b858110156109fa57848403895281516109db8582610976565b94506109e68361098a565b925060208a019950506001810190506109c2565b50829750879550505050505092915050565b6000606083016000830151610a2460008601826106c4565b5060208301518482036020860152610a3c8282610724565b91505060408301518482036040860152610a568282610997565b9150508091505092915050565b60006020820190508181036000830152610a7d8184610a0c565b90509291505056fea2646970667358221220dea5e33843b81f2de0d4fc732506054d77f009d84e7ee6273aef6ac06f72ce7464736f6c63430008130033"  # noqa: E501
TUPLE_CONTRACT_RUNTIME = "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"  # noqa: E501
TUPLE_CONTRACT_ABI = [
    {
        "inputs": [
            {
                "components": [
                    {"internalType": "uint256", "name": "a", "type": "uint256"},
                    {"internalType": "uint256[]", "name": "b", "type": "uint256[]"},
                    {
                        "components": [
                            {"internalType": "int256", "name": "x", "type": "int256"},
                            {"internalType": "bool[2]", "name": "y", "type": "bool[2]"},
                            {
                                "internalType": "address[]",
                                "name": "z",
                                "type": "address[]",
                            },
                        ],
                        "internalType": "struct TupleContract.T[]",
                        "name": "c",
                        "type": "tuple[]",
                    },
                ],
                "internalType": "struct TupleContract.S",
                "name": "s",
                "type": "tuple",
            }
        ],
        "name": "method",
        "outputs": [
            {
                "components": [
                    {"internalType": "uint256", "name": "a", "type": "uint256"},
                    {"internalType": "uint256[]", "name": "b", "type": "uint256[]"},
                    {
                        "components": [
                            {"internalType": "int256", "name": "x", "type": "int256"},
                            {"internalType": "bool[2]", "name": "y", "type": "bool[2]"},
                            {
                                "internalType": "address[]",
                                "name": "z",
                                "type": "address[]",
                            },
                        ],
                        "internalType": "struct TupleContract.T[]",
                        "name": "c",
                        "type": "tuple[]",
                    },
                ],
                "internalType": "struct TupleContract.S",
                "name": "",
                "type": "tuple",
            }
        ],
        "stateMutability": "pure",
        "type": "function",
    }
]
TUPLE_CONTRACT_DATA = {
    "bytecode": TUPLE_CONTRACT_BYTECODE,
    "bytecode_runtime": TUPLE_CONTRACT_RUNTIME,
    "abi": TUPLE_CONTRACT_ABI,
}


# source: web3/_utils/contract_sources/TupleContracts.sol:NestedTupleContract
NESTED_TUPLE_CONTRACT_BYTECODE = "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"  # noqa: E501
NESTED_TUPLE_CONTRACT_RUNTIME = "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"  # noqa: E501
NESTED_TUPLE_CONTRACT_ABI = [
    {
        "inputs": [
            {
                "components": [
                    {
                        "components": [
                            {
                                "components": [
                                    {
                                        "internalType": "int256",
                                        "name": "x",
                                        "type": "int256",
                                    },
                                    {
                                        "internalType": "int256",
                                        "name": "y",
                                        "type": "int256",
                                    },
                                ],
                                "internalType": "struct NestedTupleContract.U[]",
                                "name": "u",
                                "type": "tuple[]",
                            }
                        ],
                        "internalType": "struct NestedTupleContract.T[]",
                        "name": "t",
                        "type": "tuple[]",
                    }
                ],
                "internalType": "struct NestedTupleContract.S",
                "name": "s",
                "type": "tuple",
            }
        ],
        "name": "method",
        "outputs": [
            {
                "components": [
                    {
                        "components": [
                            {
                                "components": [
                                    {
                                        "internalType": "int256",
                                        "name": "x",
                                        "type": "int256",
                                    },
                                    {
                                        "internalType": "int256",
                                        "name": "y",
                                        "type": "int256",
                                    },
                                ],
                                "internalType": "struct NestedTupleContract.U[]",
                                "name": "u",
                                "type": "tuple[]",
                            }
                        ],
                        "internalType": "struct NestedTupleContract.T[]",
                        "name": "t",
                        "type": "tuple[]",
                    }
                ],
                "internalType": "struct NestedTupleContract.S",
                "name": "",
                "type": "tuple",
            }
        ],
        "stateMutability": "pure",
        "type": "function",
    }
]
NESTED_TUPLE_CONTRACT_DATA = {
    "bytecode": NESTED_TUPLE_CONTRACT_BYTECODE,
    "bytecode_runtime": NESTED_TUPLE_CONTRACT_RUNTIME,
    "abi": NESTED_TUPLE_CONTRACT_ABI,
}
