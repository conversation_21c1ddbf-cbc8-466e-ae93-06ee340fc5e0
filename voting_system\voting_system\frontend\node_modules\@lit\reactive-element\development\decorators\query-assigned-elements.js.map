{"version": 3, "file": "query-assigned-elements.js", "sourceRoot": "", "sources": ["../../src/decorators/query-assigned-elements.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAEH;;;;;GAKG;AAEH,OAAO,EAAC,gBAAgB,EAAC,MAAM,WAAW,CAAC;AAK3C,MAAM,SAAS,GAAG,KAAK,CAAC;AACxB,MAAM,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC;AAE/C;;GAEG;AACH,MAAM,oBAAoB,GACxB,CAAA,MAAA,MAAM,CAAC,eAAe,0CAAE,SAAS,CAAC,gBAAgB,KAAI,IAAI;IACxD,CAAC,CAAC,CAAC,IAAqB,EAAE,IAA2B,EAAE,EAAE,CACrD,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;IAC/B,CAAC,CAAC,CAAC,IAAqB,EAAE,IAA2B,EAAE,EAAE,CACrD,IAAI;SACD,aAAa,CAAC,IAAI,CAAC;SACnB,MAAM,CACL,CAAC,IAAI,EAAmB,EAAE,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,YAAY,CAC/D,CAAC;AAgBZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4BG;AACH,MAAM,UAAU,qBAAqB,CAAC,OAAsC;IAC1E,MAAM,EAAC,IAAI,EAAE,QAAQ,EAAC,GAAG,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,EAAE,CAAC;IACvC,OAAO,gBAAgB,CAAC;QACtB,UAAU,EAAE,CAAC,KAAkB,EAAE,EAAE,CAAC,CAAC;YACnC,GAAG;;gBACD,MAAM,YAAY,GAAG,OAAO,IAAI,CAAC,CAAC,CAAC,SAAS,IAAI,GAAG,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC;gBACvE,MAAM,MAAM,GACV,MAAA,IAAI,CAAC,UAAU,0CAAE,aAAa,CAAkB,YAAY,CAAC,CAAC;gBAChE,MAAM,QAAQ,GACZ,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,oBAAoB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC9D,IAAI,QAAQ,EAAE;oBACZ,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;iBAC1D;gBACD,OAAO,QAAQ,CAAC;YAClB,CAAC;YACD,UAAU,EAAE,IAAI;YAChB,YAAY,EAAE,IAAI;SACnB,CAAC;KACH,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/*\n * IMPORTANT: For compatibility with <PERSON><PERSON><PERSON> and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\n\nimport {decorateProperty} from './base.js';\n\nimport type {ReactiveElement} from '../reactive-element.js';\nimport type {QueryAssignedNodesOptions} from './query-assigned-nodes.js';\n\nconst NODE_MODE = false;\nconst global = NODE_MODE ? globalThis : window;\n\n/**\n * A tiny module scoped polyfill for HTMLSlotElement.assignedElements.\n */\nconst slotAssignedElements =\n  global.HTMLSlotElement?.prototype.assignedElements != null\n    ? (slot: HTMLSlotElement, opts?: AssignedNodesOptions) =>\n        slot.assignedElements(opts)\n    : (slot: HTMLSlotElement, opts?: AssignedNodesOptions) =>\n        slot\n          .assignedNodes(opts)\n          .filter(\n            (node): node is Element => node.nodeType === Node.ELEMENT_NODE\n          );\n\n/**\n * Options for the {@linkcode queryAssignedElements} decorator. Extends the\n * options that can be passed into\n * [HTMLSlotElement.assignedElements](https://developer.mozilla.org/en-US/docs/Web/API/HTMLSlotElement/assignedElements).\n */\nexport interface QueryAssignedElementsOptions\n  extends QueryAssignedNodesOptions {\n  /**\n   * CSS selector used to filter the elements returned. For example, a selector\n   * of `\".item\"` will only include elements with the `item` class.\n   */\n  selector?: string;\n}\n\n/**\n * A property decorator that converts a class property into a getter that\n * returns the `assignedElements` of the given `slot`. Provides a declarative\n * way to use\n * [`HTMLSlotElement.assignedElements`](https://developer.mozilla.org/en-US/docs/Web/API/HTMLSlotElement/assignedElements).\n *\n * Can be passed an optional {@linkcode QueryAssignedElementsOptions} object.\n *\n * Example usage:\n * ```ts\n * class MyElement {\n *   @queryAssignedElements({ slot: 'list' })\n *   listItems!: Array<HTMLElement>;\n *   @queryAssignedElements()\n *   unnamedSlotEls!: Array<HTMLElement>;\n *\n *   render() {\n *     return html`\n *       <slot name=\"list\"></slot>\n *       <slot></slot>\n *     `;\n *   }\n * }\n * ```\n *\n * Note, the type of this property should be annotated as `Array<HTMLElement>`.\n *\n * @category Decorator\n */\nexport function queryAssignedElements(options?: QueryAssignedElementsOptions) {\n  const {slot, selector} = options ?? {};\n  return decorateProperty({\n    descriptor: (_name: PropertyKey) => ({\n      get(this: ReactiveElement) {\n        const slotSelector = `slot${slot ? `[name=${slot}]` : ':not([name])'}`;\n        const slotEl =\n          this.renderRoot?.querySelector<HTMLSlotElement>(slotSelector);\n        const elements =\n          slotEl != null ? slotAssignedElements(slotEl, options) : [];\n        if (selector) {\n          return elements.filter((node) => node.matches(selector));\n        }\n        return elements;\n      },\n      enumerable: true,\n      configurable: true,\n    }),\n  });\n}\n"]}