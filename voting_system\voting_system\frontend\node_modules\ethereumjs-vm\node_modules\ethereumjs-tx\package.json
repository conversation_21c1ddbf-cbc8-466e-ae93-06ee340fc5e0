{"name": "ethereumjs-tx", "version": "2.1.2", "description": "A simple module for creating, manipulating and signing Ethereum transactions", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "ethereumjs-config-build", "prepublishOnly": "npm run test && npm run build", "coverage": "ethereumjs-config-coverage", "coveralls": "ethereumjs-config-coveralls", "docs:build": "typedoc --out docs --mode file --readme none --theme markdown --mdEngine github --excludeNotExported src", "format": "ethereumjs-config-format", "format:fix": "ethereumjs-config-format-fix", "tslint": "ethereumjs-config-tslint", "tslint:fix": "ethereumjs-config-tslint-fix", "tsc": "ethereumjs-config-tsc", "lint": "ethereumjs-config-lint", "lint:fix": "ethereumjs-config-lint-fix", "test": "npm run test:node", "test:node": "ts-node node_modules/tape/bin/tape ./test/index.ts", "test:browser:build": "tsc && cp ./test/*.json test-build/test/", "test:browser": "npm run test:browser:build && karma start karma.conf.js"}, "husky": {"hooks": {"pre-push": "npm run lint"}}, "keywords": ["ethereum", "transactions"], "author": "mjbecze <<EMAIL>>", "license": "MPL-2.0", "dependencies": {"ethereumjs-common": "^1.5.0", "ethereumjs-util": "^6.0.0"}, "devDependencies": {"@ethereumjs/config-nyc": "^1.1.1", "@ethereumjs/config-prettier": "^1.1.1", "@ethereumjs/config-tsc": "^1.1.1", "@ethereumjs/config-tslint": "^1.1.1", "@types/bn.js": "^4.11.5", "@types/minimist": "^1.2.0", "@types/node": "^11.13.4", "@types/tape": "^4.2.33", "browserify": "^16.2.3", "contributor": "^0.1.25", "coveralls": "^2.11.4", "ethereumjs-testing": "git+https://github.com/ethereumjs/ethereumjs-testing.git#v1.2.8", "husky": "^2.1.0", "istanbul": "^0.4.1", "karma": "^4.1.0", "karma-browserify": "^6.0.0", "karma-detect-browsers": "^2.3.3", "karma-firefox-launcher": "^1.1.0", "karma-tap": "^4.1.4", "minimist": "^1.2.0", "nyc": "^14.0.0", "prettier": "^1.17.0", "tape": "^4.0.3", "ts-node": "^8.0.3", "tslint": "^5.15.0", "typedoc": "^0.14.2", "typedoc-plugin-markdown": "^1.2.0", "typescript": "^3.4.3", "typestrict": "^1.0.2"}, "repository": {"type": "git", "url": "https://github.com/ethereumjs/ethereumjs-tx.git"}, "bugs": {"url": "https://github.com/ethereumjs/ethereumjs-tx/issues"}, "homepage": "https://github.com/ethereumjs/ethereumjs-tx", "contributors": [{"name": "<-c-g-> ", "email": "christopher<PERSON><PERSON><PERSON>@gmail.com", "url": "https://github.com/cgewecke", "contributions": 1, "additions": 3, "deletions": 1, "hireable": true}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/fanatid", "contributions": 1, "additions": 3, "deletions": 2, "hireable": true}, {"name": "<PERSON>", "email": null, "url": "https://github.com/Nexus7", "contributions": 1, "additions": 1, "deletions": 1, "hireable": null}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tcoulter", "contributions": 1, "additions": 1, "deletions": 1, "hireable": null}, {"name": "kuma<PERSON>", "email": "<EMAIL>", "url": "https://github.com/kumavis", "contributions": 1, "additions": 6, "deletions": 6, "hireable": true}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tinybike", "contributions": 2, "additions": 1, "deletions": 2, "hireable": null}, {"name": "<PERSON>", "email": null, "url": "https://github.com/SilentCicero", "contributions": 2, "additions": 29, "deletions": 0, "hireable": true}, {"name": null, "email": null, "url": "https://github.com/ckeenan", "contributions": 2, "additions": 4, "deletions": 3, "hireable": null}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/axic", "contributions": 22, "additions": 27562, "deletions": 42613, "hireable": true}, {"name": null, "email": null, "url": "https://github.com/wanderer", "contributions": 171, "additions": 112706, "deletions": 96355, "hireable": null}, {"name": null, "email": null, "url": "https://github.com/kvhnuke", "contributions": 7, "additions": 268, "deletions": 53, "hireable": null}]}