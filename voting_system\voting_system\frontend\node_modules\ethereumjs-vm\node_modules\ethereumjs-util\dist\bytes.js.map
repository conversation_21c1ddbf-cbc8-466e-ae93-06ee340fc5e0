{"version": 3, "file": "bytes.js", "sourceRoot": "", "sources": ["../src/bytes.ts"], "names": [], "mappings": ";;;AAAA,IAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,CAAA;AACvC,0BAA4B;AAE5B;;;GAGG;AACU,QAAA,KAAK,GAAG,UAAS,KAAa;IACzC,OAAO,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;AAC1C,CAAC,CAAA;AAED;;;;;;;GAOG;AACU,QAAA,aAAa,GAAG,UAAS,GAAQ,EAAE,MAAc,EAAE,KAAsB;IAAtB,sBAAA,EAAA,aAAsB;IACpF,IAAM,GAAG,GAAG,aAAK,CAAC,MAAM,CAAC,CAAA;IACzB,GAAG,GAAG,gBAAQ,CAAC,GAAG,CAAC,CAAA;IACnB,IAAI,KAAK,EAAE;QACT,IAAI,GAAG,CAAC,MAAM,GAAG,MAAM,EAAE;YACvB,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YACb,OAAO,GAAG,CAAA;SACX;QACD,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,CAAA;KAC5B;SAAM;QACL,IAAI,GAAG,CAAC,MAAM,GAAG,MAAM,EAAE;YACvB,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,CAAA;YAClC,OAAO,GAAG,CAAA;SACX;QACD,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAA;KAC1B;AACH,CAAC,CAAA;AACY,QAAA,SAAS,GAAG,qBAAa,CAAA;AAEtC;;;;;;GAMG;AACU,QAAA,cAAc,GAAG,UAAS,GAAQ,EAAE,MAAc;IAC7D,OAAO,iBAAS,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;AACrC,CAAC,CAAA;AAED;;;;GAIG;AACU,QAAA,KAAK,GAAG,UAAS,CAAM;IAClC,CAAC,GAAG,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC,CAAA;IAC/B,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;IAChB,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,QAAQ,EAAE,KAAK,GAAG,EAAE;QAC/C,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QACd,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;KACb;IACD,OAAO,CAAC,CAAA;AACV,CAAC,CAAA;AACY,QAAA,UAAU,GAAG,aAAK,CAAA;AAE/B;;;GAGG;AACU,QAAA,QAAQ,GAAG,UAAS,CAAM;IACrC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;QACvB,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;YACpB,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;SACnB;aAAM,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;YAChC,IAAI,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE;gBAC5B,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;aACzE;iBAAM;gBACL,MAAM,IAAI,KAAK,CACb,gHAA8G,CAAG,CAClH,CAAA;aACF;SACF;aAAM,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;YAChC,CAAC,GAAG,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;SAC7B;aAAM,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,SAAS,EAAE;YACxC,CAAC,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;SAC1B;aAAM,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;YACrB,CAAC,GAAG,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;SAC1B;aAAM,IAAI,CAAC,CAAC,OAAO,EAAE;YACpB,4BAA4B;YAC5B,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAA;SAC7B;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAA;SAChC;KACF;IACD,OAAO,CAAC,CAAA;AACV,CAAC,CAAA;AAED;;;;GAIG;AACU,QAAA,WAAW,GAAG,UAAS,GAAW;IAC7C,OAAO,IAAI,EAAE,CAAC,gBAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAA;AACzC,CAAC,CAAA;AAED;;;GAGG;AACU,QAAA,WAAW,GAAG,UAAS,GAAW;IAC7C,GAAG,GAAG,gBAAQ,CAAC,GAAG,CAAC,CAAA;IACnB,OAAO,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;AACnC,CAAC,CAAA;AAED;;;GAGG;AACU,QAAA,UAAU,GAAG,UAAS,GAAW;IAC5C,OAAO,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA;AAClC,CAAC,CAAA;AAED;;;GAGG;AACU,QAAA,UAAU,GAAG,UAAS,GAAO;IACxC,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC,CAAA;AAC/C,CAAC,CAAA;AAED;;GAEG;AACU,QAAA,YAAY,GAAG,UAAS,GAAW;IAC9C,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;QAC3B,OAAO,GAAG,CAAA;KACX;IAED,OAAO,SAAS,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG,GAAG,CAAA;AACxD,CAAC,CAAA;AAED;;;;GAIG;AACU,QAAA,QAAQ,GAAG,UAAS,EAAO;IACtC,IAAI,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE;QACvB,OAAO,OAAK,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAG,CAAA;KACjC;SAAM,IAAI,EAAE,YAAY,KAAK,EAAE;QAC9B,IAAM,KAAK,GAAG,EAAE,CAAA;QAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAClC,KAAK,CAAC,IAAI,CAAC,gBAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;SAC5B;QACD,OAAO,KAAK,CAAA;KACb;AACH,CAAC,CAAA"}