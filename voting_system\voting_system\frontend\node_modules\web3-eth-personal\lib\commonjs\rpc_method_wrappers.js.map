{"version": 3, "file": "rpc_method_wrappers.js", "sourceRoot": "", "sources": ["../../src/rpc_method_wrappers.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAiBA,2CAA0D;AAC1D,uCAA6C;AAC7C,2CAA8F;AAC9F,mDAAwD;AACxD,uDAAsD;AAE/C,MAAM,WAAW,GAAG,CAAO,cAAkD,EAAE,EAAE;IACvF,MAAM,MAAM,GAAG,MAAM,qCAAkB,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;IAEpE,OAAO,MAAM,CAAC,GAAG,CAAC,8BAAiB,CAAC,CAAC;AACtC,CAAC,CAAA,CAAC;AAJW,QAAA,WAAW,eAItB;AAEK,MAAM,UAAU,GAAG,CACzB,cAAkD,EAClD,QAAgB,EACf,EAAE;IACH,0BAAS,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAE3C,MAAM,MAAM,GAAG,MAAM,qCAAkB,CAAC,UAAU,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;IAE7E,OAAO,IAAA,8BAAiB,EAAC,MAAM,CAAC,CAAC;AAClC,CAAC,CAAA,CAAC;AATW,QAAA,UAAU,cASrB;AAEK,MAAM,aAAa,GAAG,CAC5B,cAAkD,EAClD,OAAgB,EAChB,QAAgB,EAChB,cAAsB,EACrB,EAAE;IACH,0BAAS,CAAC,QAAQ,CAAC,CAAC,SAAS,EAAE,QAAQ,EAAE,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAC;IAEvF,OAAO,qCAAkB,CAAC,aAAa,CAAC,cAAc,EAAE,OAAO,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC;AAC5F,CAAC,CAAA,CAAC;AATW,QAAA,aAAa,iBASxB;AAEK,MAAM,WAAW,GAAG,CAC1B,cAAkD,EAClD,OAAgB,EACf,EAAE;IACH,0BAAS,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;IAE3C,OAAO,qCAAkB,CAAC,WAAW,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;AAChE,CAAC,CAAA,CAAC;AAPW,QAAA,WAAW,eAOtB;AAEK,MAAM,YAAY,GAAG,CAC3B,cAAkD,EAClD,OAAkB,EAClB,UAAkB,EACjB,EAAE;IACH,0BAAS,CAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,CAAC;IAEhE,OAAO,qCAAkB,CAAC,YAAY,CAAC,cAAc,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;AAC7E,CAAC,CAAA,CAAC;AARW,QAAA,YAAY,gBAQvB;AAEK,MAAM,eAAe,GAAG,CAC9B,cAAkD,EAClD,EAAe,EACf,UAAkB,EAClB,MAA0B,EACzB,EAAE;IACH,MAAM,WAAW,GAAG,IAAA,4BAAiB,EAAC,EAAE,EAAE,4BAAe,EAAE;QAC1D,iBAAiB,EAAE,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,uBAAuB;KAClD,CAAC,CAAC;IAEH,OAAO,qCAAkB,CAAC,eAAe,CAAC,cAAc,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;AACpF,CAAC,CAAA,CAAC;AAXW,QAAA,eAAe,mBAW1B;AAEK,MAAM,eAAe,GAAG,CAC9B,cAAkD,EAClD,EAAe,EACf,UAAkB,EAClB,MAA0B,EACzB,EAAE;IACH,MAAM,WAAW,GAAG,IAAA,4BAAiB,EAAC,EAAE,EAAE,4BAAe,EAAE;QAC1D,iBAAiB,EAAE,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,uBAAuB;KAClD,CAAC,CAAC;IAEH,OAAO,qCAAkB,CAAC,eAAe,CAAC,cAAc,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;AACpF,CAAC,CAAA,CAAC;AAXW,QAAA,eAAe,mBAW1B;AAEK,MAAM,IAAI,GAAG,CACnB,cAAkD,EAClD,IAAe,EACf,OAAgB,EAChB,UAAkB,EACjB,EAAE;IACH,0BAAS,CAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,EAAE,CAAC,IAAI,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC,CAAC;IAEjF,MAAM,UAAU,GAAG,IAAA,4BAAW,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAA,sBAAS,EAAC,IAAI,CAAC,CAAC;IAE9D,OAAO,qCAAkB,CAAC,IAAI,CAAC,cAAc,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;AACjF,CAAC,CAAA,CAAC;AAXW,QAAA,IAAI,QAWf;AAEK,MAAM,SAAS,GAAG,CACxB,cAAkD,EAClD,UAAqB,EACrB,SAAiB,EAChB,EAAE;IACH,0BAAS,CAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC;IAElE,MAAM,gBAAgB,GAAG,IAAA,4BAAW,EAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAA,sBAAS,EAAC,UAAU,CAAC,CAAC;IAEtF,OAAO,qCAAkB,CAAC,SAAS,CAAC,cAAc,EAAE,gBAAgB,EAAE,SAAS,CAAC,CAAC;AAClF,CAAC,CAAA,CAAC;AAVW,QAAA,SAAS,aAUpB"}