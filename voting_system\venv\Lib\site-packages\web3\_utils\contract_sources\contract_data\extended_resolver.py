"""
Generated by `compile_contracts.py` script.
Compiled with Solidity v0.8.19.
"""

# source: web3/_utils/contract_sources/ExtendedResolver.sol:ExtendedResolver
EXTENDED_RESOLVER_BYTECODE = "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"  # noqa: E501
EXTENDED_RESOLVER_RUNTIME = "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"  # noqa: E501
EXTENDED_RESOLVER_ABI = [
    {
        "inputs": [{"internalType": "contract ENS", "name": "_ens", "type": "address"}],
        "stateMutability": "nonpayable",
        "type": "constructor",
    },
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": False,
                "internalType": "bytes32",
                "name": "node",
                "type": "bytes32",
            },
            {
                "indexed": False,
                "internalType": "address",
                "name": "owner",
                "type": "address",
            },
            {
                "indexed": False,
                "internalType": "address",
                "name": "target",
                "type": "address",
            },
            {
                "indexed": False,
                "internalType": "bool",
                "name": "isAuthorised",
                "type": "bool",
            },
        ],
        "name": "AuthorisationChanged",
        "type": "event",
    },
    {
        "inputs": [
            {"internalType": "bytes32", "name": "", "type": "bytes32"},
            {"internalType": "address", "name": "", "type": "address"},
            {"internalType": "address", "name": "", "type": "address"},
        ],
        "name": "authorisations",
        "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [
            {"internalType": "bytes", "name": "dnsName", "type": "bytes"},
            {"internalType": "bytes", "name": "data", "type": "bytes"},
        ],
        "name": "resolve",
        "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [
            {"internalType": "bytes32", "name": "node", "type": "bytes32"},
            {"internalType": "address", "name": "target", "type": "address"},
            {"internalType": "bool", "name": "isAuthorised", "type": "bool"},
        ],
        "name": "setAuthorisation",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "bytes4", "name": "interfaceID", "type": "bytes4"}],
        "name": "supportsInterface",
        "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
        "stateMutability": "pure",
        "type": "function",
    },
]
EXTENDED_RESOLVER_DATA = {
    "bytecode": EXTENDED_RESOLVER_BYTECODE,
    "bytecode_runtime": EXTENDED_RESOLVER_RUNTIME,
    "abi": EXTENDED_RESOLVER_ABI,
}
