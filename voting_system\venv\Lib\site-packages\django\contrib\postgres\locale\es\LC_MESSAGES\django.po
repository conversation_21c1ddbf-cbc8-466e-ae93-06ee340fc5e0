# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2015,2017
# e4db27214f7e7544f2022c647b585925_bb0e321, 2015
# <PERSON> <il<PERSON><EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2015
# Pablo, 2015
# <PERSON><PERSON> <<EMAIL>>, 2020,2023
# <AUTHOR> <EMAIL>, 2015
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-17 02:13-0600\n"
"PO-Revision-Date: 2023-04-19 09:22+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2020,2023\n"
"Language-Team: Spanish (http://www.transifex.com/django/django/language/"
"es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: es\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "PostgreSQL extensions"
msgstr "Extensiones de PostgreSQL"

#, python-format
msgid "Item %(nth)s in the array did not validate:"
msgstr "El elemento %(nth)s del arreglo no se pudo validar:"

msgid "Nested arrays must have the same length."
msgstr "Los arreglos anidados deben tener la misma longitud."

msgid "Map of strings to strings/nulls"
msgstr "Mapa de cadenas a cadenas/nulos"

#, python-format
msgid "The value of “%(key)s” is not a string or null."
msgstr "El valor de “%(key)s” no es una cadena ni es nulo."

msgid "Could not load JSON data."
msgstr "No se pududieron cargar los datos JSON."

msgid "Input must be a JSON dictionary."
msgstr "La entrada debe ser un diccionario JSON"

msgid "Enter two valid values."
msgstr "Introduzca dos valores válidos."

msgid "The start of the range must not exceed the end of the range."
msgstr "El comienzo del rango no puede exceder su final."

msgid "Enter two whole numbers."
msgstr "Ingrese dos números enteros."

msgid "Enter two numbers."
msgstr "Ingrese dos números."

msgid "Enter two valid date/times."
msgstr "Ingrese dos fechas/horas válidas."

msgid "Enter two valid dates."
msgstr "Ingrese dos fechas válidas."

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no more than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no more than "
"%(limit_value)d."
msgstr[0] ""
"La lista contiene %(show_value)d elemento, no debería contener más de "
"%(limit_value)d."
msgstr[1] ""
"La lista contiene %(show_value)d elementos, no debería contener más de "
"%(limit_value)d."
msgstr[2] ""
"La lista contiene %(show_value)d elementos, no debería contener más de "
"%(limit_value)d."

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no fewer than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no fewer than "
"%(limit_value)d."
msgstr[0] ""
"La lista contiene %(show_value)d elemento, no debería contener menos de "
"%(limit_value)d."
msgstr[1] ""
"La lista contiene %(show_value)d elementos, no debería contener menos de "
"%(limit_value)d."
msgstr[2] ""
"La lista contiene %(show_value)d elementos, no debería contener menos de "
"%(limit_value)d."

#, python-format
msgid "Some keys were missing: %(keys)s"
msgstr "Faltan algunas claves: %(keys)s"

#, python-format
msgid "Some unknown keys were provided: %(keys)s"
msgstr "Se facilitaron algunas claves desconocidas: %(keys)s"

#, python-format
msgid ""
"Ensure that the upper bound of the range is not greater than %(limit_value)s."
msgstr ""
"Asegúrese de que el límite superior del rango no sea mayor que "
"%(limit_value)s."

#, python-format
msgid ""
"Ensure that the lower bound of the range is not less than %(limit_value)s."
msgstr ""
"Asegúrese de que el límite inferior del rango no sea inferior a "
"%(limit_value)s."
