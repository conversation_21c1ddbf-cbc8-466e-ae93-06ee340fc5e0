{"name": "merkle-patricia-tree", "version": "2.3.2", "description": "This is an implementation of the modified merkle patricia tree as speficed in the Ethereum's yellow paper.", "main": "index.js", "scripts": {"test": "npm run test:node && npm run test:browser", "coverage": "istanbul cover tape ./test/*.js", "coveralls": "npm run coverage && coveralls <coverage/lcov.info", "lint": "standard", "prepublish": "npm run build", "test:browser": "karma start karma.conf.js", "test:node": "tape ./test/*.js", "build": "browserify --s Trie index.js > ./dist/trie.js", "build:docs": "documentation --github  -f md ./index.js ./secure.js ./proof.js > ./docs/index-template.md"}, "author": {"name": "mjbecze", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/ethereumjs/merkle-patricia-tree.git"}, "bugs": {"url": "https://github.com/ethereumjs/merkle-patricia-tree/issues"}, "keywords": ["merkle", "radix", "trie", "ethereum"], "license": "MPL-2.0", "dependencies": {"async": "^1.4.2", "ethereumjs-util": "^5.0.0", "level-ws": "0.0.0", "levelup": "^1.2.1", "memdown": "^1.0.0", "readable-stream": "^2.0.0", "rlp": "^2.0.0", "semaphore": ">=1.0.1"}, "devDependencies": {"browserify": "^13.0.0", "coveralls": "^2.11.6", "documentation": "^3.0.4", "ethereumjs-testing": "0.0.1", "istanbul": "^0.4.1", "karma": "^1.7.1", "karma-browserify": "^5.0.0", "karma-chrome-launcher": "^2.2.0", "karma-detect-browsers": "^2.0.2", "karma-env-preprocessor": "^0.1.1", "karma-firefox-launcher": "^1.0.1", "karma-tap": "^1.0.3", "standard": "^5.3.1", "tape": "^4.4.0"}, "standard": {"ignore": ["dist/**", "package-init.js", "package.js"]}, "contributors": ["<PERSON> <http://aaron.kumavis.me/> (https://github.com/kumavis)"]}