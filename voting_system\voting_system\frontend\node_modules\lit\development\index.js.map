{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,6DAA6D;AAC7D,8DAA8D;AAC9D,mDAAmD;AACnD,OAAO,uBAAuB,CAAC;AAC/B,OAAO,UAAU,CAAC;AAElB,cAAc,4BAA4B,CAAC;AAC3C,cAAc,uBAAuB,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n// Although these are re-exported from lit-element.js, we add\n// them here to effectively pre-fetch them and avoid the extra\n// waterfall when loading the lit package unbundled\nimport '@lit/reactive-element';\nimport 'lit-html';\n\nexport * from 'lit-element/lit-element.js';\nexport * from 'lit-html/is-server.js';\n"]}