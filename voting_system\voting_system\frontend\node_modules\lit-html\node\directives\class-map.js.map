{"version": 3, "file": "class-map.js", "sources": ["../../src/directives/class-map.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {AttributePart, noChange} from '../lit-html.js';\nimport {\n  directive,\n  Directive,\n  DirectiveParameters,\n  PartInfo,\n  PartType,\n} from '../directive.js';\n\n/**\n * A key-value set of class names to truthy values.\n */\nexport interface ClassInfo {\n  readonly [name: string]: string | boolean | number;\n}\n\nclass ClassMapDirective extends Directive {\n  /**\n   * Stores the ClassInfo object applied to a given AttributePart.\n   * Used to unset existing values when a new ClassInfo object is applied.\n   */\n  private _previousClasses?: Set<string>;\n  private _staticClasses?: Set<string>;\n\n  constructor(partInfo: PartInfo) {\n    super(partInfo);\n    if (\n      partInfo.type !== PartType.ATTRIBUTE ||\n      partInfo.name !== 'class' ||\n      (partInfo.strings?.length as number) > 2\n    ) {\n      throw new Error(\n        '`classMap()` can only be used in the `class` attribute ' +\n          'and must be the only part in the attribute.'\n      );\n    }\n  }\n\n  render(classInfo: ClassInfo) {\n    // Add spaces to ensure separation from static classes\n    return (\n      ' ' +\n      Object.keys(classInfo)\n        .filter((key) => classInfo[key])\n        .join(' ') +\n      ' '\n    );\n  }\n\n  override update(part: AttributePart, [classInfo]: DirectiveParameters<this>) {\n    // Remember dynamic classes on the first render\n    if (this._previousClasses === undefined) {\n      this._previousClasses = new Set();\n      if (part.strings !== undefined) {\n        this._staticClasses = new Set(\n          part.strings\n            .join(' ')\n            .split(/\\s/)\n            .filter((s) => s !== '')\n        );\n      }\n      for (const name in classInfo) {\n        if (classInfo[name] && !this._staticClasses?.has(name)) {\n          this._previousClasses.add(name);\n        }\n      }\n      return this.render(classInfo);\n    }\n\n    const classList = part.element.classList;\n\n    // Remove old classes that no longer apply\n    // We use forEach() instead of for-of so that we don't require down-level\n    // iteration.\n    this._previousClasses.forEach((name) => {\n      if (!(name in classInfo)) {\n        classList.remove(name);\n        this._previousClasses!.delete(name);\n      }\n    });\n\n    // Add or remove classes based on their classMap value\n    for (const name in classInfo) {\n      // We explicitly want a loose truthy check of `value` because it seems\n      // more convenient that '' and 0 are skipped.\n      const value = !!classInfo[name];\n      if (\n        value !== this._previousClasses.has(name) &&\n        !this._staticClasses?.has(name)\n      ) {\n        if (value) {\n          classList.add(name);\n          this._previousClasses.add(name);\n        } else {\n          classList.remove(name);\n          this._previousClasses.delete(name);\n        }\n      }\n    }\n    return noChange;\n  }\n}\n\n/**\n * A directive that applies dynamic CSS classes.\n *\n * This must be used in the `class` attribute and must be the only part used in\n * the attribute. It takes each property in the `classInfo` argument and adds\n * the property name to the element's `classList` if the property value is\n * truthy; if the property value is falsey, the property name is removed from\n * the element's `class`.\n *\n * For example `{foo: bar}` applies the class `foo` if the value of `bar` is\n * truthy.\n *\n * @param classInfo\n */\nexport const classMap = directive(ClassMapDirective);\n\n/**\n * The type of the class that powers this directive. Necessary for naming the\n * directive's return type.\n */\nexport type {ClassMapDirective};\n"], "names": ["classMap", "directive", "Directive", "constructor", "partInfo", "super", "type", "PartType", "ATTRIBUTE", "name", "_a", "strings", "length", "Error", "render", "classInfo", "Object", "keys", "filter", "key", "join", "update", "part", "undefined", "this", "_previousClasses", "Set", "_staticClasses", "split", "s", "has", "add", "classList", "element", "for<PERSON>ach", "remove", "delete", "value", "_b", "noChange"], "mappings": ";;;;;SA2HaA,EAAWC,EArGxB,cAAgCC,EAQ9BC,YAAYC,SAEV,GADAC,MAAMD,GAEJA,EAASE,OAASC,EAASC,WACT,UAAlBJ,EAASK,eACRC,EAAAN,EAASO,8BAASC,QAAoB,EAEvC,MAAUC,MACR,qGAIL,CAEDC,OAAOC,GAEL,MACE,IACAC,OAAOC,KAAKF,GACTG,QAAQC,GAAQJ,EAAUI,KAC1BC,KAAK,KACR,GAEH,CAEQC,OAAOC,GAAsBP,YAEpC,QAA8BQ,IAA1BC,KAAKC,GAAgC,CACvCD,KAAKC,GAAmB,IAAIC,SACPH,IAAjBD,EAAKX,UACPa,KAAKG,GAAiB,IAAID,IACxBJ,EAAKX,QACFS,KAAK,KACLQ,MAAM,MACNV,QAAQW,GAAY,KAANA,MAGrB,IAAK,MAAMpB,KAAQM,EACbA,EAAUN,MAA+B,QAArBC,EAAAc,KAAKG,UAAgB,IAAAjB,OAAA,EAAAA,EAAAoB,IAAIrB,KAC/Ce,KAAKC,GAAiBM,IAAItB,GAG9B,OAAOe,KAAKV,OAAOC,EACpB,CAED,MAAMiB,EAAYV,EAAKW,QAAQD,UAK/BR,KAAKC,GAAiBS,SAASzB,IACvBA,KAAQM,IACZiB,EAAUG,OAAO1B,GACjBe,KAAKC,GAAkBW,OAAO3B,GAC/B,IAIH,IAAK,MAAMA,KAAQM,EAAW,CAG5B,MAAMsB,IAAUtB,EAAUN,GAExB4B,IAAUb,KAAKC,GAAiBK,IAAIrB,KACd,QAArB6B,EAAAd,KAAKG,UAAgB,IAAAW,OAAA,EAAAA,EAAAR,IAAIrB,MAEtB4B,GACFL,EAAUD,IAAItB,GACde,KAAKC,GAAiBM,IAAItB,KAE1BuB,EAAUG,OAAO1B,GACjBe,KAAKC,GAAiBW,OAAO3B,IAGlC,CACD,OAAO8B,CACR"}