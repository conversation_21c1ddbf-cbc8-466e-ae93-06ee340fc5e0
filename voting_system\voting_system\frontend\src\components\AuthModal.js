import React from 'react';
import WalletConnector from './WalletConnector';

const AuthModal = ({ isOpen, onClose, onConnect, onError }) => {
    if (!isOpen) return null;

    return (
        <div className="modal-overlay" onClick={onClose}>
            <div className="modal-dialog modal-dialog-centered modal-lg" onClick={e => e.stopPropagation()}>
                <div className="modal-content">
                    <div className="modal-header">
                        <h5 className="modal-title">Connect to Vote</h5>
                        <button
                            type="button"
                            className="btn-close"
                            onClick={onClose}
                            aria-label="Close"
                        ></button>
                    </div>
                    <div className="modal-body">
                        <div className="row">
                            <div className="col-md-5 border-end d-none d-md-block">
                                <div className="p-3 text-center">
                                    <img
                                        src="https://upload.wikimedia.org/wikipedia/commons/thumb/4/49/Flag_of_Kenya.svg/320px-Flag_of_Kenya.svg.png"
                                        alt="Kenyan Flag"
                                        className="img-fluid mb-4"
                                        style={{ maxHeight: '120px' }}
                                    />
                                    <h4>Kenyan Election Voting System</h4>
                                    <p className="text-muted">Secure, Transparent, and Accessible</p>

                                    <div className="mt-4">
                                        <h6 className="text-start">Why connect?</h6>
                                        <ul className="text-start">
                                            <li>Verify your identity securely</li>
                                            <li>Register as a voter</li>
                                            <li>Cast your vote in elections</li>
                                            <li>View your voting history</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div className="col-md-7">
                                <div className="p-3">
                                    <WalletConnector
                                        onConnect={onConnect}
                                        onError={onError}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="modal-footer">
                        <div className="d-flex justify-content-between w-100">
                            <button
                                className="btn btn-outline-secondary"
                                onClick={onClose}
                            >
                                Cancel
                            </button>
                            <div className="text-muted small">
                                <p className="mb-0">Your data is secure and your identity is protected.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AuthModal;
