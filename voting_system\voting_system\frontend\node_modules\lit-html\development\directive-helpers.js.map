{"version": 3, "file": "directive-helpers.js", "sourceRoot": "", "sources": ["../src/directive-helpers.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAEH,OAAO,EACL,IAAI,GAKL,MAAM,eAAe,CAAC;AASvB,MAAM,EAAC,UAAU,EAAE,SAAS,EAAC,GAAG,IAAI,CAAC;AAIrC,MAAM,uBAAuB,GAAG,IAAI,CAAC;AAErC,MAAM,IAAI,GACR,uBAAuB;KACvB,MAAA,MAAM,CAAC,QAAQ,0CAAE,KAAK,CAAA;IACtB,CAAA,MAAA,MAAM,CAAC,QAAQ,0CAAE,OAAO,MAAK,IAAI;IAC/B,CAAC,CAAC,MAAM,CAAC,QAAS,CAAC,IAAI;IACvB,CAAC,CAAC,CAAC,IAAU,EAAE,EAAE,CAAC,IAAI,CAAC;AAE3B;;;;GAIG;AACH,MAAM,CAAC,MAAM,WAAW,GAAG,CAAC,KAAc,EAAsB,EAAE,CAChE,KAAK,KAAK,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,QAAQ,IAAI,OAAO,KAAK,IAAI,UAAU,CAAC,CAAC;AAE7E,MAAM,CAAC,MAAM,kBAAkB,GAAG;IAChC,IAAI,EAAE,CAAC;IACP,GAAG,EAAE,CAAC;CACE,CAAC;AAaX;;GAEG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAAqB,CAChD,KAAc,EACd,IAAyB,EACA,EAAE,CAC3B,IAAI,KAAK,SAAS;IAChB,CAAC,CAAC,4CAA4C;QAC5C,CAAC,KAAwB,aAAxB,KAAK,uBAAL,KAAK,CAAsB,YAAY,CAAC,MAAK,SAAS;IACzD,CAAC,CAAC,CAAC,KAAwB,aAAxB,KAAK,uBAAL,KAAK,CAAsB,YAAY,CAAC,MAAK,IAAI,CAAC;AAEzD;;GAEG;AACH,MAAM,CAAC,MAAM,wBAAwB,GAAG,CACtC,KAAc,EACmB,EAAE;;IACnC,OAAO,CAAA,MAAC,KAAgC,aAAhC,KAAK,uBAAL,KAAK,CAA8B,YAAY,CAAC,0CAAE,CAAC,KAAI,IAAI,CAAC;AACtE,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,iBAAiB,GAAG,CAAC,KAAc,EAA4B,EAAE;AAC5E,4CAA4C;AAC5C,CAAC,KAAyB,aAAzB,KAAK,uBAAL,KAAK,CAAuB,iBAAiB,CAAC,MAAK,SAAS,CAAC;AAEhE;;GAEG;AACH,MAAM,CAAC,MAAM,iBAAiB,GAAG,CAAC,KAAc,EAA8B,EAAE;AAC9E,4CAA4C;AAC3C,KAAyB,aAAzB,KAAK,uBAAL,KAAK,CAAuB,iBAAiB,CAAC,CAAC;AAElD;;;;;;;GAOG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAAG,CAAC,IAAc,EAAE,EAAE,CAClD,IAA0B,CAAC,OAAO,KAAK,SAAS,CAAC;AAEpD,MAAM,YAAY,GAAG,GAAG,EAAE,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;AAEtD;;;;;;;;;;;GAWG;AACH,MAAM,CAAC,MAAM,UAAU,GAAG,CACxB,aAAwB,EACxB,OAAmB,EACnB,IAAgB,EACL,EAAE;;IACb,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,UAAW,CAAC;IAE9D,MAAM,OAAO,GACX,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC;IAExE,IAAI,IAAI,KAAK,SAAS,EAAE;QACtB,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,YAAY,EAAE,EAAE,OAAO,CAAC,CAAC;QACxE,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,YAAY,EAAE,EAAE,OAAO,CAAC,CAAC;QACtE,IAAI,GAAG,IAAI,SAAS,CAClB,SAAS,EACT,OAAO,EACP,aAAa,EACb,aAAa,CAAC,OAAO,CACtB,CAAC;KACH;SAAM;QACL,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,SAAU,CAAC,CAAC,WAAW,CAAC;QAClD,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC;QAChC,MAAM,aAAa,GAAG,SAAS,KAAK,aAAa,CAAC;QAClD,IAAI,aAAa,EAAE;YACjB,MAAA,IAAI,CAAC,yBAAyB,qDAAG,aAAa,CAAC,CAAC;YAChD,oEAAoE;YACpE,qEAAqE;YACrE,mEAAmE;YACnE,8BAA8B;YAC9B,IAAI,CAAC,QAAQ,GAAG,aAAa,CAAC;YAC9B,0DAA0D;YAC1D,4DAA4D;YAC5D,iBAAiB;YACjB,IAAI,kBAAkB,CAAC;YACvB,IACE,IAAI,CAAC,yBAAyB,KAAK,SAAS;gBAC5C,CAAC,kBAAkB,GAAG,aAAa,CAAC,aAAa,CAAC;oBAChD,SAAU,CAAC,aAAa,EAC1B;gBACA,IAAI,CAAC,yBAAyB,CAAC,kBAAkB,CAAC,CAAC;aACpD;SACF;QACD,IAAI,OAAO,KAAK,OAAO,IAAI,aAAa,EAAE;YACxC,IAAI,KAAK,GAAgB,IAAI,CAAC,WAAW,CAAC;YAC1C,OAAO,KAAK,KAAK,OAAO,EAAE;gBACxB,MAAM,CAAC,GAAgB,IAAI,CAAC,KAAM,CAAC,CAAC,WAAW,CAAC;gBAChD,IAAI,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,KAAM,EAAE,OAAO,CAAC,CAAC;gBAC9C,KAAK,GAAG,CAAC,CAAC;aACX;SACF;KACF;IAED,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF;;;;;;;;;;;;;;;GAeG;AACH,MAAM,CAAC,MAAM,iBAAiB,GAAG,CAC/B,IAAO,EACP,KAAc,EACd,kBAAmC,IAAI,EACpC,EAAE;IACL,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;IACxC,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF,4EAA4E;AAC5E,qEAAqE;AACrE,MAAM,WAAW,GAAG,EAAE,CAAC;AAEvB;;;;;;;;;;GAUG;AACH,MAAM,CAAC,MAAM,iBAAiB,GAAG,CAAC,IAAU,EAAE,QAAiB,WAAW,EAAE,EAAE,CAC5E,CAAC,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,CAAC;AAElC;;;;;;;;;;;;;GAaG;AACH,MAAM,CAAC,MAAM,iBAAiB,GAAG,CAAC,IAAe,EAAE,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC;AAE5E;;;;GAIG;AACH,MAAM,CAAC,MAAM,UAAU,GAAG,CAAC,IAAe,EAAE,EAAE;;IAC5C,MAAA,IAAI,CAAC,yBAAyB,qDAAG,KAAK,EAAE,IAAI,CAAC,CAAC;IAC9C,IAAI,KAAK,GAAqB,IAAI,CAAC,WAAW,CAAC;IAC/C,MAAM,GAAG,GAAqB,IAAI,CAAC,IAAI,CAAC,SAAU,CAAC,CAAC,WAAW,CAAC;IAChE,OAAO,KAAK,KAAK,GAAG,EAAE;QACpB,MAAM,CAAC,GAAqB,IAAI,CAAC,KAAM,CAAC,CAAC,WAAW,CAAC;QACpD,IAAI,CAAC,KAAM,CAAe,CAAC,MAAM,EAAE,CAAC;QACrC,KAAK,GAAG,CAAC,CAAC;KACX;AACH,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,SAAS,GAAG,CAAC,IAAe,EAAE,EAAE;IAC3C,IAAI,CAAC,OAAO,EAAE,CAAC;AACjB,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {\n  _$LH,\n  Part,\n  DirectiveParent,\n  TemplateResult,\n  CompiledTemplateResult,\n} from './lit-html.js';\nimport {\n  DirectiveResult,\n  DirectiveClass,\n  PartInfo,\n  AttributePartInfo,\n} from './directive.js';\ntype Primitive = null | undefined | boolean | number | string | symbol | bigint;\n\nconst {_ChildPart: ChildPart} = _$LH;\n\ntype ChildPart = InstanceType<typeof ChildPart>;\n\nconst ENABLE_SHADYDOM_NOPATCH = true;\n\nconst wrap =\n  ENABLE_SHADYDOM_NOPATCH &&\n  window.ShadyDOM?.inUse &&\n  window.ShadyDOM?.noPatch === true\n    ? window.ShadyDOM!.wrap\n    : (node: Node) => node;\n\n/**\n * Tests if a value is a primitive value.\n *\n * See https://tc39.github.io/ecma262/#sec-typeof-operator\n */\nexport const isPrimitive = (value: unknown): value is Primitive =>\n  value === null || (typeof value != 'object' && typeof value != 'function');\n\nexport const TemplateResultType = {\n  HTML: 1,\n  SVG: 2,\n} as const;\n\nexport type TemplateResultType =\n  (typeof TemplateResultType)[keyof typeof TemplateResultType];\n\ntype IsTemplateResult = {\n  (val: unknown): val is TemplateResult | CompiledTemplateResult;\n  <T extends TemplateResultType>(\n    val: unknown,\n    type: T\n  ): val is TemplateResult<T>;\n};\n\n/**\n * Tests if a value is a TemplateResult or a CompiledTemplateResult.\n */\nexport const isTemplateResult: IsTemplateResult = (\n  value: unknown,\n  type?: TemplateResultType\n): value is TemplateResult =>\n  type === undefined\n    ? // This property needs to remain unminified.\n      (value as TemplateResult)?.['_$litType$'] !== undefined\n    : (value as TemplateResult)?.['_$litType$'] === type;\n\n/**\n * Tests if a value is a CompiledTemplateResult.\n */\nexport const isCompiledTemplateResult = (\n  value: unknown\n): value is CompiledTemplateResult => {\n  return (value as CompiledTemplateResult)?.['_$litType$']?.h != null;\n};\n\n/**\n * Tests if a value is a DirectiveResult.\n */\nexport const isDirectiveResult = (value: unknown): value is DirectiveResult =>\n  // This property needs to remain unminified.\n  (value as DirectiveResult)?.['_$litDirective$'] !== undefined;\n\n/**\n * Retrieves the Directive class for a DirectiveResult\n */\nexport const getDirectiveClass = (value: unknown): DirectiveClass | undefined =>\n  // This property needs to remain unminified.\n  (value as DirectiveResult)?.['_$litDirective$'];\n\n/**\n * Tests whether a part has only a single-expression with no strings to\n * interpolate between.\n *\n * Only AttributePart and PropertyPart can have multiple expressions.\n * Multi-expression parts have a `strings` property and single-expression\n * parts do not.\n */\nexport const isSingleExpression = (part: PartInfo) =>\n  (part as AttributePartInfo).strings === undefined;\n\nconst createMarker = () => document.createComment('');\n\n/**\n * Inserts a ChildPart into the given container ChildPart's DOM, either at the\n * end of the container ChildPart, or before the optional `refPart`.\n *\n * This does not add the part to the containerPart's committed value. That must\n * be done by callers.\n *\n * @param containerPart Part within which to add the new ChildPart\n * @param refPart Part before which to add the new ChildPart; when omitted the\n *     part added to the end of the `containerPart`\n * @param part Part to insert, or undefined to create a new part\n */\nexport const insertPart = (\n  containerPart: ChildPart,\n  refPart?: ChildPart,\n  part?: ChildPart\n): ChildPart => {\n  const container = wrap(containerPart._$startNode).parentNode!;\n\n  const refNode =\n    refPart === undefined ? containerPart._$endNode : refPart._$startNode;\n\n  if (part === undefined) {\n    const startNode = wrap(container).insertBefore(createMarker(), refNode);\n    const endNode = wrap(container).insertBefore(createMarker(), refNode);\n    part = new ChildPart(\n      startNode,\n      endNode,\n      containerPart,\n      containerPart.options\n    );\n  } else {\n    const endNode = wrap(part._$endNode!).nextSibling;\n    const oldParent = part._$parent;\n    const parentChanged = oldParent !== containerPart;\n    if (parentChanged) {\n      part._$reparentDisconnectables?.(containerPart);\n      // Note that although `_$reparentDisconnectables` updates the part's\n      // `_$parent` reference after unlinking from its current parent, that\n      // method only exists if Disconnectables are present, so we need to\n      // unconditionally set it here\n      part._$parent = containerPart;\n      // Since the _$isConnected getter is somewhat costly, only\n      // read it once we know the subtree has directives that need\n      // to be notified\n      let newConnectionState;\n      if (\n        part._$notifyConnectionChanged !== undefined &&\n        (newConnectionState = containerPart._$isConnected) !==\n          oldParent!._$isConnected\n      ) {\n        part._$notifyConnectionChanged(newConnectionState);\n      }\n    }\n    if (endNode !== refNode || parentChanged) {\n      let start: Node | null = part._$startNode;\n      while (start !== endNode) {\n        const n: Node | null = wrap(start!).nextSibling;\n        wrap(container).insertBefore(start!, refNode);\n        start = n;\n      }\n    }\n  }\n\n  return part;\n};\n\n/**\n * Sets the value of a Part.\n *\n * Note that this should only be used to set/update the value of user-created\n * parts (i.e. those created using `insertPart`); it should not be used\n * by directives to set the value of the directive's container part. Directives\n * should return a value from `update`/`render` to update their part state.\n *\n * For directives that require setting their part value asynchronously, they\n * should extend `AsyncDirective` and call `this.setValue()`.\n *\n * @param part Part to set\n * @param value Value to set\n * @param index For `AttributePart`s, the index to set\n * @param directiveParent Used internally; should not be set by user\n */\nexport const setChildPartValue = <T extends ChildPart>(\n  part: T,\n  value: unknown,\n  directiveParent: DirectiveParent = part\n): T => {\n  part._$setValue(value, directiveParent);\n  return part;\n};\n\n// A sentinel value that can never appear as a part value except when set by\n// live(). Used to force a dirty-check to fail and cause a re-render.\nconst RESET_VALUE = {};\n\n/**\n * Sets the committed value of a ChildPart directly without triggering the\n * commit stage of the part.\n *\n * This is useful in cases where a directive needs to update the part such\n * that the next update detects a value change or not. When value is omitted,\n * the next update will be guaranteed to be detected as a change.\n *\n * @param part\n * @param value\n */\nexport const setCommittedValue = (part: Part, value: unknown = RESET_VALUE) =>\n  (part._$committedValue = value);\n\n/**\n * Returns the committed value of a ChildPart.\n *\n * The committed value is used for change detection and efficient updates of\n * the part. It can differ from the value set by the template or directive in\n * cases where the template value is transformed before being committed.\n *\n * - `TemplateResult`s are committed as a `TemplateInstance`\n * - Iterables are committed as `Array<ChildPart>`\n * - All other types are committed as the template value or value returned or\n *   set by a directive.\n *\n * @param part\n */\nexport const getCommittedValue = (part: ChildPart) => part._$committedValue;\n\n/**\n * Removes a ChildPart from the DOM, including any of its content.\n *\n * @param part The Part to remove\n */\nexport const removePart = (part: ChildPart) => {\n  part._$notifyConnectionChanged?.(false, true);\n  let start: ChildNode | null = part._$startNode;\n  const end: ChildNode | null = wrap(part._$endNode!).nextSibling;\n  while (start !== end) {\n    const n: ChildNode | null = wrap(start!).nextSibling;\n    (wrap(start!) as ChildNode).remove();\n    start = n;\n  }\n};\n\nexport const clearPart = (part: ChildPart) => {\n  part._$clear();\n};\n"]}