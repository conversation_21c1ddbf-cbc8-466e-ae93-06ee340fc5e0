{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";;;AAAA,0EAAyC;AACzC,+CAA8C;AAE9C,gDAAqD;AAIrD,MAAM,GAAG,GAAG,IAAA,qBAAa,EAAwB,gBAAgB,CAAC,IAAI,6BAAc,CAAC;AAIrF,MAAM,cAAe,SAAQ,uBAAY;IAGvC,YAAY,GAAW;QACrB,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;IACjB,CAAC;IAED,WAAW,CAAC,OAAY,EAAE,OAAe,EAAE,IAAI,GAAG,CAAC,CAAC;QAClD,OAAO;YACL,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE;YACxB,EAAE,EAAE,OAAO,CAAC,EAAE;YACd,OAAO,EAAE,OAAO,CAAC,OAAO;SACzB,CAAC;IACJ,CAAC;IAEM,IAAI,CAAC,OAAY,EAAE,QAAc;QACtC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;YAC3B,IAAI,OAAO,CAAC,MAAM,KAAK,eAAe,EAAE;gBACtC,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAC5B,OAAO,EACP,uDAAuD,CACxD,CAAC;gBACF,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;gBAC1B,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC;aACvB;YACD,MAAM,GAAG,GAAG,IAAI,GAAG,EAAE,CAAC;YAEtB,IAAI,SAAS,GAAG,KAAK,CAAC;YAEtB,MAAM,GAAG,GAAG,CAAC,GAAW,EAAE,MAAY,EAAE,EAAE;gBACxC,IAAI,CAAC,SAAS,EAAE;oBACd,GAAG,CAAC,KAAK,EAAE,CAAC;oBACZ,SAAS,GAAG,IAAI,CAAC;oBACjB,IAAI,QAAQ,EAAE;wBACZ,QAAQ,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;qBACvB;yBAAM;wBACL,MAAM,EAAE,EAAE,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;wBAChC,MAAM,QAAQ,GAAG,GAAG;4BAClB,CAAC,CAAC,EAAE,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,OAAO,EAAE,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE;4BAClE,CAAC,CAAC,EAAE,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;wBAC5B,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;wBAC/B,OAAO,CAAC,QAAQ,CAAC,CAAC;qBACnB;iBACF;YACH,CAAC,CAAC;YAEF,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;YACjC,GAAG,CAAC,gBAAgB,CAAC,cAAc,EAAE,kBAAkB,CAAC,CAAC;YACzD,GAAG,CAAC,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC;YACxB,GAAG,CAAC,OAAO,GAAG,GAAU,CAAC;YACzB,GAAG,CAAC,SAAS,GAAG,GAAU,CAAC;YAC3B,GAAG,CAAC,kBAAkB,GAAG,GAAG,EAAE;gBAC5B,IAAI,GAAG,CAAC,UAAU,KAAK,CAAC,EAAE;oBACxB,IAAI;wBACF,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;wBAC9C,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;qBACtC;oBAAC,OAAO,CAAC,EAAE;wBACV,GAAG,CAAC,CAAQ,CAAC,CAAC;qBACf;iBACF;YACH,CAAC,CAAC;YACF,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAED,kBAAe,cAAc,CAAC"}