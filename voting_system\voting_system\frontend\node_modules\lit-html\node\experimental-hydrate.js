import{<PERSON>uffer as e}from"buffer";import{noChange as t,_$LH as r}from"./lit-html.js";import{PartType as n}from"./directive.js";import{isPrimitive as o,isTemplateResult as a,isCompiledTemplateResult as i,isSingleExpression as l}from"./directive-helpers.js";const{L:s,R:c,D:d,I:p,F:f}=r,m=(e,t,r={})=>{if(console.warn("Importing `hydrate()` from `lit-html/experimental-hydrate.js` is deprecated.Import from `@lit-labs/ssr-client` instead."),void 0!==t._$litPart$)throw Error("container already contains a live render");let n,o,a;const i=[],l=document.createTreeWalker(t,NodeFilter.SHOW_COMMENT,null,!1);let s;for(;null!==(s=l.nextNode());){const t=s.data;if(t.startsWith("lit-part")){if(0===i.length&&void 0!==n)throw Error(`There must be only one root part per container. Found a part marker (${s}) when we already have a root part marker (${o})`);a=h(e,s,i,r),null!=n||(n=a),null!=o||(o=s)}else if(t.startsWith("lit-node"))w(s,i,r);else if(t.startsWith("/lit-part")){if(1===i.length&&a!==n)throw Error("internal error");a=u(s,a,i)}}if(void 0===n){const e=t instanceof ShadowRoot?"{container.host.localName}'s shadow root":t instanceof DocumentFragment?"DocumentFragment":t.localName;console.error(`There should be exactly one root part in a render container, but we didn't find any in ${e}.`)}t._$litPart$=n},h=(e,r,n,l)=>{let f,m;if(0===n.length)m=new p(r,null,void 0,l),f=e;else{const e=n[n.length-1];if("template-instance"===e.type)m=new p(r,null,e.instance,l),e.instance._$AV.push(m),f=e.result.values[e.instancePartIndex++],e.templatePartIndex++;else if("iterable"===e.type){m=new p(r,null,e.part,l);const t=e.iterator.next();if(t.done)throw f=void 0,e.done=!0,Error("Unhandled shorter than expected iterable");f=t.value,e.part._$AH.push(m)}else m=new p(r,null,e.part,l)}if(f=d(m,f),f===t)n.push({part:m,type:"leaf"});else if(o(f))n.push({part:m,type:"leaf"}),m._$AH=f;else if(a(f)){if(i(f))throw Error("compiled templates are not supported");const e="lit-part "+y(f);if(r.data!==e)throw Error("Hydration value mismatch: Unexpected TemplateResult rendered to part");{const e=p.prototype._$AC(f),t=new s(e,m);n.push({type:"template-instance",instance:t,part:m,templatePartIndex:0,instancePartIndex:0,result:f}),m._$AH=t}}else c(f)?(n.push({part:m,type:"iterable",value:f,iterator:f[Symbol.iterator](),done:!1}),m._$AH=[]):(n.push({part:m,type:"leaf"}),m._$AH=null==f?"":f);return m},u=(e,t,r)=>{if(void 0===t)throw Error("unbalanced part marker");t._$AB=e;const n=r.pop();if("iterable"===n.type&&!n.iterator.next().done)throw Error("unexpected longer than expected iterable");if(r.length>0)return r[r.length-1].part},w=(e,t,r)=>{const o=/lit-node (\d+)/.exec(e.data),a=parseInt(o[1]),i=e.nextElementSibling;if(null===i)throw Error("could not find node for attribute parts");i.removeAttribute("defer-hydration");const s=t[t.length-1];if("template-instance"!==s.type)throw Error("internal error");{const e=s.instance;for(;;){const t=e._$AD.parts[s.templatePartIndex];if(void 0===t||t.type!==n.ATTRIBUTE&&t.type!==n.ELEMENT||t.index!==a)break;if(t.type===n.ATTRIBUTE){const o=new t.ctor(i,t.name,t.strings,s.instance,r),a=l(o)?s.result.values[s.instancePartIndex]:s.result.values,c=!(o.type===n.EVENT||o.type===n.PROPERTY);o._$AI(a,o,s.instancePartIndex,c),s.instancePartIndex+=t.strings.length-1,e._$AV.push(o)}else{const t=new f(i,s.instance,r);d(t,s.result.values[s.instancePartIndex++]),e._$AV.push(t)}s.templatePartIndex++}}},y=t=>{const r=new Uint32Array(2).fill(5381);for(const e of t.strings)for(let t=0;t<e.length;t++)r[t%2]=33*r[t%2]^e.charCodeAt(t);const n=String.fromCharCode(...new Uint8Array(r.buffer));return e.from(n,"binary").toString("base64")};export{y as digestForTemplateResult,m as hydrate};
//# sourceMappingURL=experimental-hydrate.js.map
