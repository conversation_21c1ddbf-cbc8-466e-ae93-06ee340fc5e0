from rest_framework import serializers
from django.contrib.auth.models import User
from .models import (
    County, Constituency, PollingStation, Party,
    ElectionType, Election, Voter, Candidate, Vote
)

class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name']

class CountySerializer(serializers.ModelSerializer):
    class Meta:
        model = County
        fields = ['id', 'name', 'code']

class ConstituencySerializer(serializers.ModelSerializer):
    county = CountySerializer(read_only=True)

    class Meta:
        model = Constituency
        fields = ['id', 'name', 'code', 'county']

class PollingStationSerializer(serializers.ModelSerializer):
    constituency = ConstituencySerializer(read_only=True)

    class Meta:
        model = PollingStation
        fields = ['id', 'name', 'code', 'constituency', 'location']

class PartySerializer(serializers.ModelSerializer):
    class Meta:
        model = Party
        fields = ['id', 'name', 'abbreviation', 'symbol', 'description', 'registration_number']

class ElectionTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = ElectionType
        fields = ['id', 'name', 'description']

class ElectionSerializer(serializers.ModelSerializer):
    election_type = ElectionTypeSerializer(read_only=True)

    class Meta:
        model = Election
        fields = ['id', 'title', 'description', 'election_type', 'start_date', 'end_date', 'active', 'contract_election_id']
        read_only_fields = ['contract_election_id']

class VoterSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    constituency = ConstituencySerializer(read_only=True)
    polling_station = PollingStationSerializer(read_only=True)

    class Meta:
        model = Voter
        fields = ['id', 'user', 'id_number', 'date_of_birth', 'phone_number',
                 'constituency', 'polling_station', 'is_verified', 'registration_date']
        read_only_fields = ['registration_date', 'is_verified']

class CandidateSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    party = PartySerializer(read_only=True)
    election = ElectionSerializer(read_only=True)
    constituency = ConstituencySerializer(read_only=True)
    county = CountySerializer(read_only=True)

    class Meta:
        model = Candidate
        fields = ['id', 'user', 'party', 'election', 'constituency', 'county',
                 'bio', 'photo', 'is_independent', 'is_approved', 'votes_count', 'contract_candidate_id']
        read_only_fields = ['votes_count', 'is_approved', 'contract_candidate_id']

class VoteSerializer(serializers.ModelSerializer):
    voter = VoterSerializer(read_only=True)
    candidate = CandidateSerializer(read_only=True)
    election = ElectionSerializer(read_only=True)
    polling_station = PollingStationSerializer(read_only=True)

    class Meta:
        model = Vote
        fields = ['id', 'voter', 'candidate', 'election', 'polling_station', 'timestamp', 'transaction_hash']
        read_only_fields = ['timestamp', 'transaction_hash']
