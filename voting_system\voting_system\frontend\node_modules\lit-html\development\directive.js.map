{"version": 3, "file": "directive.js", "sourceRoot": "", "sources": ["../src/directive.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAqCH,MAAM,CAAC,MAAM,QAAQ,GAAG;IACtB,SAAS,EAAE,CAAC;IACZ,KAAK,EAAE,CAAC;IACR,QAAQ,EAAE,CAAC;IACX,iBAAiB,EAAE,CAAC;IACpB,KAAK,EAAE,CAAC;IACR,OAAO,EAAE,CAAC;CACF,CAAC;AA+BX;;;GAGG;AACH,MAAM,CAAC,MAAM,SAAS,GACpB,CAA2B,CAAI,EAAE,EAAE,CACnC,CAAC,GAAG,MAA4C,EAAsB,EAAE,CAAC,CAAC;IACxE,4CAA4C;IAC5C,CAAC,iBAAiB,CAAC,EAAE,CAAC;IACtB,MAAM;CACP,CAAC,CAAC;AAEL;;;;GAIG;AACH,MAAM,OAAgB,SAAS;IAkB7B,YAAY,SAAmB,IAAG,CAAC;IAEnC,mEAAmE;IACnE,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC;IACrC,CAAC;IAED,gBAAgB;IAChB,YAAY,CACV,IAAU,EACV,MAAsB,EACtB,cAAkC;QAElC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACnB,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC;QACvB,IAAI,CAAC,gBAAgB,GAAG,cAAc,CAAC;IACzC,CAAC;IACD,gBAAgB;IAChB,SAAS,CAAC,IAAU,EAAE,KAAqB;QACzC,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAClC,CAAC;IAID,MAAM,CAAC,KAAW,EAAE,KAAqB;QACvC,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC;IAC/B,CAAC;CACF", "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {Disconnectable, Part} from './lit-html.js';\n\nexport {\n  AttributePart,\n  BooleanAttributePart,\n  ChildPart,\n  ElementPart,\n  EventPart,\n  Part,\n  PropertyPart,\n} from './lit-html.js';\n\nexport interface DirectiveClass {\n  new (part: PartInfo): Directive;\n}\n\n/**\n * This utility type extracts the signature of a directive class's render()\n * method so we can use it for the type of the generated directive function.\n */\nexport type DirectiveParameters<C extends Directive> = Parameters<C['render']>;\n\n/**\n * A generated directive function doesn't evaluate the directive, but just\n * returns a DirectiveResult object that captures the arguments.\n */\nexport interface DirectiveResult<C extends DirectiveClass = DirectiveClass> {\n  /**\n   * This property needs to remain unminified.\n   * @internal */\n  ['_$litDirective$']: C;\n  /** @internal */\n  values: DirectiveParameters<InstanceType<C>>;\n}\n\nexport const PartType = {\n  ATTRIBUTE: 1,\n  CHILD: 2,\n  PROPERTY: 3,\n  BOOLEAN_ATTRIBUTE: 4,\n  EVENT: 5,\n  ELEMENT: 6,\n} as const;\n\nexport type PartType = (typeof PartType)[keyof typeof PartType];\n\nexport interface ChildPartInfo {\n  readonly type: typeof PartType.CHILD;\n}\n\nexport interface AttributePartInfo {\n  readonly type:\n    | typeof PartType.ATTRIBUTE\n    | typeof PartType.PROPERTY\n    | typeof PartType.BOOLEAN_ATTRIBUTE\n    | typeof PartType.EVENT;\n  readonly strings?: ReadonlyArray<string>;\n  readonly name: string;\n  readonly tagName: string;\n}\n\nexport interface ElementPartInfo {\n  readonly type: typeof PartType.ELEMENT;\n}\n\n/**\n * Information about the part a directive is bound to.\n *\n * This is useful for checking that a directive is attached to a valid part,\n * such as with directive that can only be used on attribute bindings.\n */\nexport type PartInfo = ChildPartInfo | AttributePartInfo | ElementPartInfo;\n\n/**\n * Creates a user-facing directive function from a Directive class. This\n * function has the same parameters as the directive's render() method.\n */\nexport const directive =\n  <C extends DirectiveClass>(c: C) =>\n  (...values: DirectiveParameters<InstanceType<C>>): DirectiveResult<C> => ({\n    // This property needs to remain unminified.\n    ['_$litDirective$']: c,\n    values,\n  });\n\n/**\n * Base class for creating custom directives. Users should extend this class,\n * implement `render` and/or `update`, and then pass their subclass to\n * `directive`.\n */\nexport abstract class Directive implements Disconnectable {\n  //@internal\n  __part!: Part;\n  //@internal\n  __attributeIndex: number | undefined;\n  //@internal\n  __directive?: Directive;\n\n  //@internal\n  _$parent!: Disconnectable;\n\n  // These will only exist on the AsyncDirective subclass\n  //@internal\n  _$disconnectableChildren?: Set<Disconnectable>;\n  // This property needs to remain unminified.\n  //@internal\n  ['_$notifyDirectiveConnectionChanged']?(isConnected: boolean): void;\n\n  constructor(_partInfo: PartInfo) {}\n\n  // See comment in Disconnectable interface for why this is a getter\n  get _$isConnected() {\n    return this._$parent._$isConnected;\n  }\n\n  /** @internal */\n  _$initialize(\n    part: Part,\n    parent: Disconnectable,\n    attributeIndex: number | undefined\n  ) {\n    this.__part = part;\n    this._$parent = parent;\n    this.__attributeIndex = attributeIndex;\n  }\n  /** @internal */\n  _$resolve(part: Part, props: Array<unknown>): unknown {\n    return this.update(part, props);\n  }\n\n  abstract render(...props: Array<unknown>): unknown;\n\n  update(_part: Part, props: Array<unknown>): unknown {\n    return this.render(...props);\n  }\n}\n"]}