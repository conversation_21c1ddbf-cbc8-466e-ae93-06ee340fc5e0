{"name": "async-eventemitter", "version": "0.2.4", "description": "Just like EventEmitter, but with support for callbacks and interuption of the listener-chain", "main": "index.js", "scripts": {"test": "make test"}, "keywords": ["event", "async", "eventemitter", "callback"], "author": "<PERSON>", "license": "MIT", "dependencies": {"async": "^2.4.0"}, "devDependencies": {"grunt": "~0.4.1", "grunt-contrib-jshint": "~0.7.1", "grunt-contrib-watch": "~0.5.3", "grunt-mocha-test": "^0.11.0", "mocha": "~1.17.1", "should": "~2.0.2"}}