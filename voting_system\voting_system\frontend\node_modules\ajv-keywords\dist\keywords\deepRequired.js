"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const deepRequired_1 = __importDefault(require("../definitions/deepRequired"));
const deepRequired = (ajv) => ajv.addKeyword((0, deepRequired_1.default)());
exports.default = deepRequired;
module.exports = deepRequired;
//# sourceMappingURL=deepRequired.js.map