'use strict';

var cssVar = require('./utils/css-var.cjs.js');
var getStyleName = require('./utils/get-style-name.cjs.js');
var transforms = require('./utils/transforms.cjs.js');

const style = {
    get: (element, name) => {
        name = getStyleName.getStyleName(name);
        let value = cssVar.isCssVar(name)
            ? element.style.getPropertyValue(name)
            : getComputedStyle(element)[name];
        // TODO Decide if value can be 0
        if (!value && value !== 0) {
            const definition = transforms.transformDefinitions.get(name);
            if (definition)
                value = definition.initialValue;
        }
        return value;
    },
    set: (element, name, value) => {
        name = getStyleName.getStyleName(name);
        if (cssVar.isCssVar(name)) {
            element.style.setProperty(name, value);
        }
        else {
            element.style[name] = value;
        }
    },
};

exports.style = style;
