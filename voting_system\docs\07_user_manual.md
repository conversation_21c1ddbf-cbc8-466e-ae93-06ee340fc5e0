# User Manual

## Blockchain-Based Decentralized Voting System

### Table of Contents
1. [Introduction](#1-introduction)
2. [Getting Started](#2-getting-started)
3. [Voter Guide](#3-voter-guide)
4. [Worker Guide](#4-worker-guide)
5. [Administrator Guide](#5-administrator-guide)
6. [Troubleshooting](#6-troubleshooting)

## 1. Introduction

### 1.1 System Overview
The Blockchain-Based Decentralized Voting System is a secure, transparent, and efficient platform for conducting Kenyan general elections. The system uses blockchain technology to ensure vote integrity and provides real-time result monitoring.

### 1.2 User Types
- **Voters**: Registered citizens who can participate in elections
- **Registration Workers**: Authorized personnel who register voters
- **Election Officials**: IEBC staff who manage elections
- **System Administrators**: Technical personnel who maintain the system

### 1.3 System Requirements
- **Web Browser**: Chrome, Firefox, Safari, or Edge (latest versions)
- **Internet Connection**: Stable internet connection required
- **MetaMask**: Optional for full blockchain features
- **Mobile Device**: Responsive design supports mobile access

## 2. Getting Started

### 2.1 Accessing the System
1. Open your web browser
2. Navigate to the voting system URL: `http://localhost:8000`
3. The home page will display with navigation options

### 2.2 System Navigation
The main navigation menu includes:
- **Home**: System overview and quick access
- **Elections**: View available elections
- **Statistics**: Voting analytics and reports
- **Analytics**: Advanced data visualization
- **My Profile**: User profile and voting history

## 3. Voter Guide

### 3.1 Voter Registration Process

#### 3.1.1 Registration Requirements
- Valid Kenyan National ID
- Must be 18 years or older
- Registration must be done by authorized worker

#### 3.1.2 What to Expect
1. Visit a registration center
2. Present your National ID to a registration worker
3. Provide personal information and documents
4. Receive login credentials from the worker

### 3.2 Voter Login

#### 3.2.1 Login Steps
1. Click **"Voter Login"** on the home page
2. Enter your credentials:
   - **Username**: `voter_[your_ID_number]` (e.g., `voter_12345678`)
   - **Password**: Your 8-digit ID number (e.g., `12345678`)
3. Click **"Login"**

#### 3.2.2 First-Time Login
- Your initial password is your ID number
- Consider changing your password after first login
- Contact registration center if you forget credentials

### 3.3 Voting Process

#### 3.3.1 Viewing Elections
1. After login, click **"Elections"** in the navigation menu
2. Browse available elections
3. Click on an election to view details

#### 3.3.2 Viewing Candidates
1. In the election detail page, click **"View Candidates"**
2. Review candidate information:
   - Name and party affiliation
   - Candidate profile and manifesto
   - Previous experience

#### 3.3.3 Casting Your Vote
1. Select your preferred candidate
2. Click **"Vote"** button next to the candidate
3. Review your selection in the voting booth
4. Click **"Cast Vote"** to confirm
5. Wait for blockchain confirmation
6. Receive vote confirmation message

#### 3.3.4 Vote Verification
- Your vote is recorded on the blockchain
- You can verify your vote was counted
- Results are updated in real-time

### 3.4 Viewing Results
1. Navigate to **"Elections"**
2. Select the election
3. Click **"View Results"** (available after voting ends)
4. View real-time vote counts and percentages

### 3.5 Profile Management
1. Click **"My Profile"** in the navigation
2. View your voter information:
   - Registration details
   - Voting history
   - Constituency and polling station
3. Update contact information if needed

## 4. Worker Guide

### 4.1 Worker Login

#### 4.1.1 Accessing Worker Dashboard
1. Click **"Worker Login"** on the home page
2. Enter your worker credentials:
   - **Username**: Provided by administrator
   - **Password**: Provided by administrator
3. Click **"Login"**

#### 4.1.2 Worker Dashboard
After login, you'll see:
- Registration statistics
- Quick access to voter registration
- Recent registration activity
- System notifications

### 4.2 Voter Registration Process

#### 4.2.1 Starting Registration
1. From the worker dashboard, click **"Register New Voter"**
2. The registration form will open

#### 4.2.2 Completing Registration Form

**Personal Information**:
- **ID Number**: Enter 8-digit national ID number
- **Phone Number**: Format: +254XXXXXXXXX
- **Date of Birth**: Select from calendar

**Location Information**:
- **County**: Select from dropdown
- **Constituency**: Select after county (auto-populated)
- **Polling Station**: Select after constituency (auto-populated)

**Document Upload**:
- **ID Picture**: Upload clear photo of national ID
- **Voter Photo**: Upload recent photo of the voter

#### 4.2.3 Form Validation
The system validates:
- ID number format (8 digits)
- Phone number format (+254XXXXXXXXX)
- Age eligibility (18+ years)
- Document file formats (JPEG, PNG)
- Duplicate registration prevention

#### 4.2.4 Completing Registration
1. Review all information for accuracy
2. Click **"Register Voter"**
3. System generates login credentials
4. Provide credentials to the voter:
   - **Username**: `voter_[ID_number]`
   - **Password**: `[ID_number]`

### 4.3 Registration Management
- View registration history
- Search for registered voters
- Update voter information if needed
- Generate registration reports

### 4.4 Worker Best Practices
1. **Verify Identity**: Always check original ID documents
2. **Accurate Data**: Ensure all information is correct
3. **Secure Credentials**: Provide login details securely
4. **Privacy**: Protect voter information
5. **Documentation**: Keep records of registrations

## 5. Administrator Guide

### 5.1 Admin Access
1. Navigate to `/admin/` URL
2. Login with administrator credentials
3. Access Django admin interface

### 5.2 Election Management

#### 5.2.1 Creating Elections
1. Go to **"Elections"** section
2. Click **"Add Election"**
3. Fill in election details:
   - Name and description
   - Election type
   - Start and end dates
   - Candidate list
4. Save the election

#### 5.2.2 Managing Candidates
1. Access **"Candidates"** section
2. Add candidate information:
   - Personal details
   - Party affiliation
   - Constituency
3. Upload candidate photos and documents

### 5.3 User Management
- Create and manage worker accounts
- Reset user passwords
- Manage user permissions
- Monitor user activity

### 5.4 System Monitoring
- View system statistics
- Monitor performance metrics
- Check error logs
- Generate reports

## 6. Troubleshooting

### 6.1 Common Issues

#### 6.1.1 Login Problems
**Issue**: Cannot login with credentials
**Solutions**:
- Verify username format: `voter_[ID_number]`
- Check password is your 8-digit ID number
- Contact registration center for credential reset

**Issue**: Worker login not working
**Solutions**:
- Verify credentials with administrator
- Check if worker account is active
- Clear browser cache and try again

#### 6.1.2 Registration Issues
**Issue**: Dropdown fields not loading
**Solutions**:
- Check internet connection
- Refresh the page
- Try selecting county again
- Contact technical support

**Issue**: File upload failing
**Solutions**:
- Check file format (JPEG, PNG only)
- Ensure file size is under 5MB
- Try a different image
- Check internet connection

#### 6.1.3 Voting Problems
**Issue**: Cannot cast vote
**Solutions**:
- Verify you're logged in as voter
- Check if election is active
- Ensure you haven't already voted
- Try refreshing the page

**Issue**: Vote confirmation not received
**Solutions**:
- Wait for blockchain confirmation
- Check internet connection
- Verify transaction in blockchain explorer
- Contact support if issue persists

### 6.2 Browser Compatibility
- **Chrome**: Fully supported
- **Firefox**: Fully supported
- **Safari**: Supported with minor limitations
- **Edge**: Fully supported
- **Mobile Browsers**: Responsive design supported

### 6.3 Performance Tips
- Use latest browser version
- Clear cache regularly
- Ensure stable internet connection
- Close unnecessary browser tabs
- Disable browser extensions if issues occur

### 6.4 Security Best Practices
- Never share login credentials
- Log out after each session
- Use secure internet connections
- Report suspicious activity
- Keep browser updated

### 6.5 Contact Support
- **Technical Issues**: Contact system administrator
- **Registration Problems**: Visit registration center
- **Voting Questions**: Contact IEBC helpline
- **Emergency**: Use backup voting methods

---

**Document Version**: 1.0  
**Date**: December 2024  
**Author**: [Student Name]  
**Institution**: [University Name]  
**Course**: [Course Code and Name]
