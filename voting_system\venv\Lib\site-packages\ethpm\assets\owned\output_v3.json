{"contracts": {"Owned.sol": {"Owned": {"abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}], "devdoc": {"methods": {}}, "evm": {"bytecode": {"linkReferences": {}, "object": "6080604052348015600f57600080fd5b50600080546001600160a01b03191633179055603f80602f6000396000f3fe6080604052600080fdfea26469706673582212208cbf6c3ccde7837026b3ec9660a0e95f1dbee0ce985f6879d7bc7e422519cc7564736f6c63430006080033", "opcodes": "PUSH1 0x80 PUSH1 0x40 MSTORE CALLVALUE DUP1 ISZERO PUSH1 0xF JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH1 0x0 DUP1 SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB NOT AND CALLER OR SWAP1 SSTORE PUSH1 0x3F DUP1 PUSH1 0x2F PUSH1 0x0 CODECOPY PUSH1 0x0 RETURN INVALID PUSH1 0x80 PUSH1 0x40 MSTORE PUSH1 0x0 DUP1 REVERT INVALID LOG2 PUSH5 0x6970667358 0x22 SLT KECCAK256 DUP13 0xBF PUSH13 0x3CCDE7837026B3EC9660A0E95F SAR 0xBE 0xE0 0xCE SWAP9 0x5F PUSH9 0x79D7BC7E422519CC75 PUSH5 0x736F6C6343 STOP MOD ADDMOD STOP CALLER ", "sourceMap": "25:164:0:-:0;;;131:56;5:9:-1;2:2;;;27:1;24;17:12;2:2;-1:-1;162:5:0;:18;;-1:-1:-1;;;;;;162:18:0;170:10;162:18;;;25:164;;;;;;"}}, "metadata": "{\"compiler\":{\"version\":\"0.6.8+commit.0bbfe453\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"}],\"devdoc\":{\"methods\":{}},\"userdoc\":{\"methods\":{}}},\"settings\":{\"compilationTarget\":{\"Owned.sol\":\"Owned\"},\"evmVersion\":\"istanbul\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[]},\"sources\":{\"Owned.sol\":{\"keccak256\":\"0x38c04b8a01ef178bb0d8edc266f0ad107b52af2e913d26ef6248da83b40b88ca\",\"urls\":[\"bzz-raw://d7bfb3d5598469963d54a00878f253fb4df870dd10c35e822e1092a1e4a7f116\",\"dweb:/ipfs/QmQfmUAxLfpu5WMqwbjjjg8oqhiPVmwC4dGxf2mufmD3sw\"]}},\"version\":1}", "userdoc": {"methods": {}}}}}, "errors": [{"component": "general", "formattedMessage": "Owned.sol: Warning: SPDX license identifier not provided in source file. Before publishing, consider adding a comment containing \"SPDX-License-Identifier: <SPDX-License>\" to each source file. Use \"SPDX-License-Identifier: UNLICENSED\" for non-open-source code. Please see https://spdx.org for more information.\n", "message": "SPDX license identifier not provided in source file. Before publishing, consider adding a comment containing \"SPDX-License-Identifier: <SPDX-License>\" to each source file. Use \"SPDX-License-Identifier: UNLICENSED\" for non-open-source code. Please see https://spdx.org for more information.", "severity": "warning", "sourceLocation": {"end": -1, "file": "Owned.sol", "start": -1}, "type": "Warning"}], "sources": {"Owned.sol": {"id": 0}}}