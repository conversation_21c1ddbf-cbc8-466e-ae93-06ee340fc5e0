# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.0.6](https://github.com/browserify/cipher-base/compare/v1.0.5...v1.0.6) - 2024-11-26

### Commits

- [Fix] io.js 3.0 - Node.js 5.3 typed array support [`b7ddd2a`](https://github.com/browserify/cipher-base/commit/b7ddd2ac24e65cc47befc1e0eb5026422f8ab037)

## [v1.0.5](https://github.com/browserify/cipher-base/compare/v1.0.4...v1.0.5) - 2024-11-17

### Commits

- [Tests] standard -&gt; eslint, make test dir, etc [`ae02fd6`](https://github.com/browserify/cipher-base/commit/ae02fd6624c41ac4ac18077be797111d1955bc76)
- [Tests] migrate from travis to GHA [`66387d7`](https://github.com/browserify/cipher-base/commit/66387d71461287ad9067bb1bcbfdc47403a33ee7)
- [meta] fix package.json indentation [`5c02918`](https://github.com/browserify/cipher-base/commit/5c02918ac58c875ed36913c2dc3e1043f4d1c99c)
- [Fix] return valid values on multi-byte-wide TypedArray input [`8fd1364`](https://github.com/browserify/cipher-base/commit/8fd136432ca298a664f5637629cf2b42a6c7f294)
- [meta] add `auto-changelog` [`88dc806`](https://github.com/browserify/cipher-base/commit/88dc806806d3dc41444dbf639c87c00f82c949b3)
- [meta] add `npmignore` and `safe-publish-latest` [`7a137d7`](https://github.com/browserify/cipher-base/commit/7a137d749ce7ea7ea56b9c096844b1b8ab723f61)
- Only apps should have lockfiles [`42528f2`](https://github.com/browserify/cipher-base/commit/42528f291db16bf2e7d5f831ebe2ad87fd0b1f42)
- [Deps] update `inherits`, `safe-buffer` [`0e7a2d9`](https://github.com/browserify/cipher-base/commit/0e7a2d9a33a391e82fa9cf512d6e25cc91ab8613)
- [meta] add missing `engines.node` [`f2dc13e`](https://github.com/browserify/cipher-base/commit/f2dc13e47bbcf3c873db9a9e0f83e5f29d0783fe)

## [v1.0.4](https://github.com/browserify/cipher-base/compare/v1.0.3...v1.0.4) - 2017-07-06

### Merged

- Safe Buffer & License [`#8`](https://github.com/browserify/cipher-base/pull/8)
- fix break in node 8 [`#9`](https://github.com/browserify/cipher-base/pull/9)

### Commits

- use safe-buffer [`193bbeb`](https://github.com/browserify/cipher-base/commit/193bbeb0d60ff206b072d4e18b32fcf150eb0fad)
- add MIT LICENSE [`a9fb6e3`](https://github.com/browserify/cipher-base/commit/a9fb6e316c25b25d2bd04fa5b6eb748318bdcb04)
- index: add missing Buffer.alloc [`2397a9e`](https://github.com/browserify/cipher-base/commit/2397a9e0db33b2f3d6abbd89f01db1c066804a91)
- rename README to README, add .gitignore [`9345e1d`](https://github.com/browserify/cipher-base/commit/9345e1d21fd10555bc4f39ccae5512ca0202c5f6)

## [v1.0.3](https://github.com/browserify/cipher-base/compare/v1.0.2...v1.0.3) - 2016-09-13

### Merged

- closure-compiler fix - final is a reserved word [`#4`](https://github.com/browserify/cipher-base/pull/4)

## [v1.0.2](https://github.com/browserify/cipher-base/compare/v1.0.1...v1.0.2) - 2015-10-23

### Merged

- fix up typo in README [`#1`](https://github.com/browserify/cipher-base/pull/1)

### Commits

- Update api-methods. [`c4a7156`](https://github.com/browserify/cipher-base/commit/c4a7156c7beedb524ce5005374d2fd1327fa3ca4)
- travis [`a5873f7`](https://github.com/browserify/cipher-base/commit/a5873f7dbfe542d7ec28bcb3e306096d1d2b951c)
- Add api-methods [`208b338`](https://github.com/browserify/cipher-base/commit/208b33841d3e8a6f5322e54bdf442e1ee0d77590)
- Update error messages [`dc19a24`](https://github.com/browserify/cipher-base/commit/dc19a24119786518c1c835d948e36ddae3bf3adb)

## v1.0.1 - 2015-09-26

### Commits

- first [`8fbd9e7`](https://github.com/browserify/cipher-base/commit/8fbd9e7654d384b578e63d12e0311ae16dc07741)
- fix error in 0.10 [`61491fe`](https://github.com/browserify/cipher-base/commit/61491fe31fcc9bee0901f93b26cee6eaaca07575)
