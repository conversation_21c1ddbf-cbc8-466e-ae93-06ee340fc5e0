!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("proxy-compare")):"function"==typeof define&&define.amd?define(["exports","proxy-compare"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).valtioVanilla={},e.proxyCompare)}(this,(function(e,t){"use strict";var n=function(e){return"object"==typeof e&&null!==e},r=new WeakMap,o=new WeakSet,i=function(e,i,a,c,f,u,s,l,v){return void 0===e&&(e=Object.is),void 0===i&&(i=function(e,t){return new Proxy(e,t)}),void 0===a&&(a=function(e){return n(e)&&!o.has(e)&&(Array.isArray(e)||!(Symbol.iterator in e))&&!(e instanceof WeakMap)&&!(e instanceof WeakSet)&&!(e instanceof Error)&&!(e instanceof Number)&&!(e instanceof Date)&&!(e instanceof String)&&!(e instanceof RegExp)&&!(e instanceof ArrayBuffer)}),void 0===c&&(c=function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:throw e}}),void 0===f&&(f=new WeakMap),void 0===u&&(u=function(e){function t(t,n,r){return e.apply(this,arguments)}return t.toString=function(){return e.toString()},t}((function(e,n,i){void 0===i&&(i=c);var a=f.get(e);if((null==a?void 0:a[0])===n)return a[1];var s=Array.isArray(e)?[]:Object.create(Object.getPrototypeOf(e));return t.markToTrack(s,!0),f.set(e,[n,s]),Reflect.ownKeys(e).forEach((function(n){if(!Object.getOwnPropertyDescriptor(s,n)){var a=Reflect.get(e,n),c={value:a,enumerable:!0,configurable:!0};if(o.has(a))t.markToTrack(a,!1);else if(a instanceof Promise)delete c.value,c.get=function(){return i(a)};else if(r.has(a)){var f=r.get(a),l=f[0],v=f[1];c.value=u(l,v(),i)}Object.defineProperty(s,n,c)}})),s}))),void 0===s&&(s=new WeakMap),void 0===l&&(l=[1,1]),void 0===v&&(v=function(c){if(!n(c))throw new Error("object required");var f=s.get(c);if(f)return f;var d=l[0],p=new Set,y=function(e,t){void 0===t&&(t=++l[0]),d!==t&&(d=t,p.forEach((function(n){return n(e,t)})))},g=l[1],h=function(e){return function(t,n){var r=[].concat(t);r[1]=[e].concat(r[1]),y(r,n)}},b=new Map,w=function(e){var t,n=b.get(e);n&&(b.delete(e),null==(t=n[1])||t.call(n))},j=Array.isArray(c)?[]:Object.create(Object.getPrototypeOf(c)),m=i(j,{deleteProperty:function(e,t){var n=Reflect.get(e,t);w(t);var r=Reflect.deleteProperty(e,t);return r&&y(["delete",[t],n]),r},set:function(i,c,f,u){var l=Reflect.has(i,c),d=Reflect.get(i,c,u);if(l&&(e(d,f)||s.has(f)&&e(d,s.get(f))))return!0;w(c),n(f)&&(f=t.getUntracked(f)||f);var g=f;if(f instanceof Promise)f.then((function(e){f.status="fulfilled",f.value=e,y(["resolve",[c],e])})).catch((function(e){f.status="rejected",f.reason=e,y(["reject",[c],e])}));else{!r.has(f)&&a(f)&&(g=v(f));var j=!o.has(g)&&r.get(g);j&&function(e,t){if(p.size){var n=t[3](h(e));b.set(e,[t,n])}else b.set(e,[t])}(c,j)}return Reflect.set(i,c,g,u),y(["set",[c],f,d]),!0}});s.set(c,m);var O=[j,function(e){return void 0===e&&(e=++l[1]),g===e||p.size||(g=e,b.forEach((function(t){var n=t[0][1](e);n>d&&(d=n)}))),d},u,function(e){p.add(e),1===p.size&&b.forEach((function(e,t){var n=e[0];e[1];var r=n[3](h(t));b.set(t,[n,r])}));return function(){p.delete(e),0===p.size&&b.forEach((function(e,t){var n=e[0],r=e[1];r&&(r(),b.set(t,[n]))}))}}];return r.set(m,O),Reflect.ownKeys(c).forEach((function(e){var t=Object.getOwnPropertyDescriptor(c,e);"value"in t&&(m[e]=c[e],delete t.value,delete t.writable),Object.defineProperty(j,e,t)})),m}),[v,r,o,e,i,a,c,f,u,s,l]},a=i()[0];var c=i;e.getVersion=function(e){var t=r.get(e);return null==t?void 0:t[1]()},e.proxy=function(e){return void 0===e&&(e={}),a(e)},e.ref=function(e){return o.add(e),e},e.snapshot=function(e,t){var n=r.get(e),o=n[0],i=n[1];return(0,n[2])(o,i(),t)},e.subscribe=function(e,t,n){var o,i=r.get(e),a=[],c=i[3],f=!1,u=c((function(e){a.push(e),n?t(a.splice(0)):o||(o=Promise.resolve().then((function(){o=void 0,f&&t(a.splice(0))})))}));return f=!0,function(){f=!1,u()}},e.unstable_buildProxyFunction=c}));
