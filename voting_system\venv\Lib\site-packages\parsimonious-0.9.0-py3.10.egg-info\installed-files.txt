..\parsimonious\__init__.py
..\parsimonious\__pycache__\__init__.cpython-310.pyc
..\parsimonious\__pycache__\exceptions.cpython-310.pyc
..\parsimonious\__pycache__\expressions.cpython-310.pyc
..\parsimonious\__pycache__\grammar.cpython-310.pyc
..\parsimonious\__pycache__\nodes.cpython-310.pyc
..\parsimonious\__pycache__\utils.cpython-310.pyc
..\parsimonious\exceptions.py
..\parsimonious\expressions.py
..\parsimonious\grammar.py
..\parsimonious\nodes.py
..\parsimonious\tests\__init__.py
..\parsimonious\tests\__pycache__\__init__.cpython-310.pyc
..\parsimonious\tests\__pycache__\benchmarks.cpython-310.pyc
..\parsimonious\tests\__pycache__\test_benchmarks.cpython-310.pyc
..\parsimonious\tests\__pycache__\test_expressions.cpython-310.pyc
..\parsimonious\tests\__pycache__\test_grammar.cpython-310.pyc
..\parsimonious\tests\__pycache__\test_nodes.cpython-310.pyc
..\parsimonious\tests\benchmarks.py
..\parsimonious\tests\test_benchmarks.py
..\parsimonious\tests\test_expressions.py
..\parsimonious\tests\test_grammar.py
..\parsimonious\tests\test_nodes.py
..\parsimonious\utils.py
PKG-INFO
SOURCES.txt
dependency_links.txt
requires.txt
top_level.txt
