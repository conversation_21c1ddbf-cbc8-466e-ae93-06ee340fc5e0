{"version": 3, "file": "index.umd.js", "sources": ["../src/misc.ts", "../src/provider.ts"], "sourcesContent": ["import { EventEmitter } from \"events\";\n\nexport abstract class IEvents {\n  public abstract events: EventEmitter;\n\n  // events\n  public abstract on(event: string, listener: any): void;\n  public abstract once(event: string, listener: any): void;\n  public abstract off(event: string, listener: any): void;\n  public abstract removeListener(event: string, listener: any): void;\n}\n", "import { JsonRpcPayload, JsonRpcRequest, RequestArguments } from \"./jsonrpc\";\nimport { IEvents } from \"./misc\";\n\nexport abstract class IJsonRpcConnection extends IEvents {\n  public abstract connected: boolean;\n  public abstract connecting: boolean;\n  // @ts-ignore - opts is not used in abstract class constructor\n  constructor(opts?: any) {\n    super();\n  }\n\n  public abstract open(opts?: any): Promise<void>;\n  public abstract close(): Promise<void>;\n  public abstract send(payload: JsonRpcPayload, context?: any): Promise<void>;\n}\n\nexport abstract class IBaseJsonRpcProvider extends IEvents {\n  // eslint-disable-next-line no-useless-constructor\n  constructor() {\n    super();\n  }\n\n  public abstract connect(params?: any): Promise<void>;\n\n  public abstract disconnect(): Promise<void>;\n\n  public abstract request<Result = any, Params = any>(\n    request: RequestArguments<Params>,\n    context?: any,\n  ): Promise<Result>;\n\n  // ---------- Protected ----------------------------------------------- //\n\n  protected abstract requestStrict<Result = any, Params = any>(\n    request: JsonRpcRequest<Params>,\n    context?: any,\n  ): Promise<Result>;\n}\n\nexport abstract class IJsonRpcProvider extends IBaseJsonRpcProvider {\n  public abstract connection: IJsonRpcConnection;\n\n  // @ts-ignore - connection is not used in abstract class constructor\n  constructor(connection: string | IJsonRpcConnection) {\n    super();\n  }\n\n  public abstract connect(connection?: string | IJsonRpcConnection): Promise<void>;\n\n  // ---------- Protected ----------------------------------------------- //\n\n  protected abstract setConnection(connection?: string | IJsonRpcConnection): IJsonRpcConnection;\n\n  protected abstract onPayload(payload: JsonRpcPayload): void;\n\n  protected abstract open(connection?: string | IJsonRpcConnection): Promise<void>;\n\n  protected abstract close(): Promise<void>;\n}\n"], "names": ["o"], "mappings": ";;;;;;EAAO,MAAM,OAAO;;ECAoB,MAAM,kBAAkB,SAASA,OAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,GAAE,CAAC,CAAQ,MAAM,oBAAoB,SAASA,OAAC,CAAC,WAAW,EAAE,CAAC,KAAK,GAAE,CAAC,CAAQ,MAAM,gBAAgB,SAAS,oBAAoB,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,GAAE,CAAC;;;;;;;;;;;;;"}