{"version": 3, "file": "format.js", "sourceRoot": "", "sources": ["../../src/format.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,cAAc,EAAE,mBAAmB,EAAqB,MAAM,SAAS,CAAC;AAC3F,OAAO,EAAE,cAAc,EAAE,YAAY,EAAE,MAAM,aAAa,CAAC;AAG3D,MAAM,UAAU,SAAS,CAAC,OAAO,GAAG,CAAC;IACnC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;IAChD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC;IAChE,OAAO,IAAI,GAAG,KAAK,CAAC;AACtB,CAAC;AAED,MAAM,UAAU,cAAc,CAAC,OAAO,GAAG,CAAC;IACxC,OAAO,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;AACpC,CAAC;AAED,MAAM,UAAU,oBAAoB,CAClC,MAAc,EACd,MAAS,EACT,EAAW;IAEX,OAAO;QACL,EAAE,EAAE,EAAE,IAAI,SAAS,EAAE;QACrB,OAAO,EAAE,KAAK;QACd,MAAM;QACN,MAAM;KACP,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,mBAAmB,CAAU,EAAU,EAAE,MAAS;IAChE,OAAO;QACL,EAAE;QACF,OAAO,EAAE,KAAK;QACd,MAAM;KACP,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,kBAAkB,CAChC,EAAU,EACV,KAA8B,EAC9B,IAAa;IAEb,OAAO;QACL,EAAE;QACF,OAAO,EAAE,KAAK;QACd,KAAK,EAAE,kBAAkB,CAAC,KAAK,EAAE,IAAI,CAAC;KACvC,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,kBAAkB,CAAC,KAA8B,EAAE,IAAa;IAC9E,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;QAChC,OAAO,QAAQ,CAAC,cAAc,CAAC,CAAC;KACjC;IACD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC7B,KAAK,mCACA,QAAQ,CAAC,YAAY,CAAC,KACzB,OAAO,EAAE,KAAK,GACf,CAAC;KACH;IACD,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;QAC/B,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;KACnB;IACD,IAAI,mBAAmB,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;QACnC,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;KACpC;IACD,OAAO,KAAK,CAAC;AACf,CAAC"}