import React, { useState, useEffect, lazy, Suspense, memo } from 'react';
import { Chart, registerables } from 'chart.js';

// Register Chart.js components
Chart.register(...registerables);

// Lazy load chart components to improve performance
const Line = lazy(() => import('react-chartjs-2').then(module => ({ default: module.Line })));
const Radar = lazy(() => import('react-chartjs-2').then(module => ({ default: module.Radar })));
const Bubble = lazy(() => import('react-chartjs-2').then(module => ({ default: module.Bubble })));

// Create memoized chart components to prevent unnecessary re-renders
const MemoizedLine = memo(Line);
const MemoizedRadar = memo(Radar);
const MemoizedBubble = memo(Bubble);

// Loading placeholder for charts
const ChartPlaceholder = () => (
  <div className="chart-placeholder d-flex justify-content-center align-items-center" style={{ height: '100%', width: '100%' }}>
    <div className="spinner-border text-primary" role="status">
      <span className="visually-hidden">Loading chart...</span>
    </div>
  </div>
);

const AdvancedAnalytics = () => {
    const [analytics, setAnalytics] = useState(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState('');
    const [selectedMetric, setSelectedMetric] = useState('turnout');
    const [selectedRegion, setSelectedRegion] = useState('all');
    const [comparisonMode, setComparisonMode] = useState(false);
    const [regions, setRegions] = useState([]);

    useEffect(() => {
        fetchRegions();
        fetchAnalytics();
    }, [selectedMetric, selectedRegion, comparisonMode]);

    const fetchRegions = async () => {
        try {
            const response = await fetch('/api/counties/');
            if (response.ok) {
                const data = await response.json();
                // Ensure data is an array
                if (Array.isArray(data)) {
                    setRegions(data);
                } else {
                    console.warn('API did not return an array for regions');
                    // Use demo data
                    setRegions([
                        { id: 1, name: 'Nairobi' },
                        { id: 2, name: 'Mombasa' },
                        { id: 3, name: 'Kisumu' },
                        { id: 4, name: 'Nakuru' },
                        { id: 5, name: 'Eldoret' },
                    ]);
                }
            } else {
                console.warn('Failed to fetch regions');
                // Use demo data
                setRegions([
                    { id: 1, name: 'Nairobi' },
                    { id: 2, name: 'Mombasa' },
                    { id: 3, name: 'Kisumu' },
                    { id: 4, name: 'Nakuru' },
                    { id: 5, name: 'Eldoret' },
                ]);
            }
        } catch (error) {
            console.error('Error fetching regions:', error);
            // Use demo data
            setRegions([
                { id: 1, name: 'Nairobi' },
                { id: 2, name: 'Mombasa' },
                { id: 3, name: 'Kisumu' },
                { id: 4, name: 'Nakuru' },
                { id: 5, name: 'Eldoret' },
            ]);
        }
    };

    const fetchAnalytics = async () => {
        setIsLoading(true);
        setError('');

        try {
            // Fetch data from the backend API
            const response = await fetch(`/api/advanced-analytics/?metric=${selectedMetric}&region=${selectedRegion}&comparison=${comparisonMode}`);

            if (!response.ok) {
                throw new Error(`Server responded with status: ${response.status}`);
            }

            const data = await response.json();

            // Check if we got valid data
            if (!data || typeof data !== 'object') {
                throw new Error('Invalid data format received from server');
            }

            // If the API returns a message property instead of actual data, use demo data
            if (data.message && !data.historicalTrends) {
                console.log('API returned a message without data, using demo data instead');
                try {
                    const demoData = await generateDemoData();
                    setAnalytics(demoData);
                } catch (demoError) {
                    console.error('Error generating demo data:', demoError);
                    setError('Failed to load analytics and demo data. Please try again later.');
                }
            } else {
                setAnalytics(data);
            }

            setIsLoading(false);
        } catch (error) {
            console.error('Error fetching analytics:', error);
            setError(`Failed to load analytics: ${error.message}. Using demo data instead.`);

            try {
                // Fallback to demo data
                const demoData = await generateDemoData();
                setAnalytics(demoData);
            } catch (demoError) {
                console.error('Error generating demo data:', demoError);
                // If even demo data generation fails, set a more serious error
                setError('Failed to load analytics and demo data. Please try again later.');
            } finally {
                setIsLoading(false);
            }
        }
    };

    const generateDemoData = async () => {
        try {
            // Try to fetch dynamic demo data from the backend
            const response = await fetch('/api/advanced-analytics/demo-data/');

            if (response.ok) {
                const data = await response.json();
                return data;
            }
        } catch (error) {
            console.error('Error fetching dynamic demo data for analytics:', error);
        }

        // If the backend request fails, generate client-side demo data
        // This is a fallback mechanism only

        // Generate random years for historical data
        const currentYear = new Date().getFullYear();
        const years = [
            currentYear - 20,
            currentYear - 15,
            currentYear - 10,
            currentYear - 5,
            currentYear
        ].map(year => year.toString());

        // Generate random turnout data with an upward trend
        const turnoutBase = 55 + Math.random() * 10;
        const turnoutData = years.map((_, index) => {
            return Math.min(95, Math.max(50, turnoutBase + (index * 5) + (Math.random() * 6 - 3)));
        }).map(val => parseFloat(val.toFixed(1)));

        // Generate random invalid votes data with a downward trend
        const invalidVotesData = years.map((_, index) => {
            const baseValue = 2.5 - (index * 0.4);
            return Math.max(0.1, baseValue + (Math.random() * 0.4 - 0.2));
        }).map(val => parseFloat(val.toFixed(1)));

        // Historical Trends
        const historicalTrends = {
            labels: years,
            datasets: [
                {
                    label: 'Voter Turnout (%)',
                    data: turnoutData,
                    fill: false,
                    backgroundColor: 'rgba(75, 192, 192, 0.6)',
                    borderColor: 'rgba(75, 192, 192, 1)',
                    tension: 0.1
                },
                {
                    label: 'Invalid Votes (%)',
                    data: invalidVotesData,
                    fill: false,
                    backgroundColor: 'rgba(255, 99, 132, 0.6)',
                    borderColor: 'rgba(255, 99, 132, 1)',
                    tension: 0.1
                }
            ]
        };

        // Generate random correlation data for demographic analysis
        const demographicFactors = ['Education', 'Income Level', 'Urban/Rural', 'Age', 'Gender', 'Employment'];
        const correlationData = [
            parseFloat((Math.random() * 0.1 + 0.7).toFixed(2)),  // Education (strong)
            parseFloat((Math.random() * 0.15 + 0.55).toFixed(2)), // Income (moderate-strong)
            parseFloat((Math.random() * 0.1 + 0.75).toFixed(2)),  // Urban/Rural (strong)
            parseFloat((Math.random() * 0.15 + 0.5).toFixed(2)),  // Age (moderate)
            parseFloat((Math.random() * 0.15 + 0.25).toFixed(2)), // Gender (weak-moderate)
            parseFloat((Math.random() * 0.15 + 0.4).toFixed(2))   // Employment (moderate)
        ];

        // Demographic Analysis
        const demographicAnalysis = {
            labels: demographicFactors,
            datasets: [
                {
                    label: 'Correlation with Voting Behavior',
                    data: correlationData,
                    fill: true,
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    pointBackgroundColor: 'rgba(54, 162, 235, 1)',
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: 'rgba(54, 162, 235, 1)'
                }
            ]
        };

        // Generate voting time data
        const hours = [6, 8, 10, 12, 14, 16, 18, 20];
        const votingTimeData = hours.map(hour => {
            let avgAge, relativeSize;

            if (hour < 10) {
                // Morning: older voters, lower turnout
                avgAge = Math.floor(Math.random() * 10) + 50; // 50-60
                relativeSize = Math.floor(Math.random() * 3) + 5; // 5-8
            } else if (hour < 14) {
                // Midday: mixed ages, higher turnout
                avgAge = Math.floor(Math.random() * 10) + 35; // 35-45
                relativeSize = Math.floor(Math.random() * 5) + 15; // 15-20
            } else if (hour < 18) {
                // Afternoon: mixed ages, moderate turnout
                avgAge = Math.floor(Math.random() * 10) + 40; // 40-50
                relativeSize = Math.floor(Math.random() * 5) + 10; // 10-15
            } else {
                // Evening: younger voters, higher turnout
                avgAge = Math.floor(Math.random() * 10) + 25; // 25-35
                relativeSize = Math.floor(Math.random() * 5) + 15; // 15-20
            }

            return { x: hour, y: avgAge, r: relativeSize };
        });

        // Voting Time Analysis
        const votingTimeAnalysis = {
            datasets: [
                {
                    label: 'Voting Patterns',
                    data: votingTimeData,
                    backgroundColor: 'rgba(255, 99, 132, 0.6)',
                }
            ]
        };

        // Generate regional data
        const regionNames = regions.length >= 5
            ? regions.slice(0, 5).map(r => r.name)
            : ['Nairobi', 'Mombasa', 'Kisumu', 'Nakuru', 'Eldoret'];

        const turnoutByRegion = regionNames.map(() => parseFloat((Math.random() * 10 + 75).toFixed(1)));
        const registrationByRegion = regionNames.map(() => parseFloat((Math.random() * 8 + 85).toFixed(1)));

        // Regional Comparison
        const regionalComparison = {
            labels: regionNames,
            datasets: [
                {
                    label: 'Voter Turnout (%)',
                    data: turnoutByRegion,
                    backgroundColor: 'rgba(54, 162, 235, 0.6)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1,
                },
                {
                    label: 'Registered Voters (% of eligible population)',
                    data: registrationByRegion,
                    backgroundColor: 'rgba(255, 206, 86, 0.6)',
                    borderColor: 'rgba(255, 206, 86, 1)',
                    borderWidth: 1,
                }
            ]
        };

        // Generate voting method trends
        const inPersonTrend = years.map((year, index) => {
            const yearNum = parseInt(year);
            if (yearNum < 2015) return 100;
            if (yearNum < 2020) return Math.floor(Math.random() * 5 + 90);
            return Math.floor(Math.random() * 15 + 60);
        });

        const mobileAppTrend = years.map((year, index) => {
            const yearNum = parseInt(year);
            if (yearNum < 2015) return 0;
            if (yearNum < 2020) return Math.floor(Math.random() * 5);
            return Math.floor(Math.random() * 10 + 15);
        });

        const webBrowserTrend = years.map((year, index) => {
            const yearNum = parseInt(year);
            if (yearNum < 2015) return 0;
            if (yearNum < 2020) return Math.floor(Math.random() * 5);
            return 100 - inPersonTrend[index] - mobileAppTrend[index];
        });

        // Voting Method Trends
        const votingMethodTrends = {
            labels: years,
            datasets: [
                {
                    label: 'In-Person Voting (%)',
                    data: inPersonTrend,
                    fill: false,
                    backgroundColor: 'rgba(255, 99, 132, 0.6)',
                    borderColor: 'rgba(255, 99, 132, 1)',
                    tension: 0.1
                },
                {
                    label: 'Mobile App Voting (%)',
                    data: mobileAppTrend,
                    fill: false,
                    backgroundColor: 'rgba(54, 162, 235, 0.6)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    tension: 0.1
                },
                {
                    label: 'Web Browser Voting (%)',
                    data: webBrowserTrend,
                    fill: false,
                    backgroundColor: 'rgba(255, 206, 86, 0.6)',
                    borderColor: 'rgba(255, 206, 86, 1)',
                    tension: 0.1
                }
            ]
        };

        // Generate dynamic insights based on the data
        const turnoutIncrease = ((turnoutData[turnoutData.length - 1] - turnoutData[0]) / turnoutData[0] * 100).toFixed(1);
        const invalidVotesDecrease = ((invalidVotesData[0] - invalidVotesData[invalidVotesData.length - 1]) / invalidVotesData[0] * 100).toFixed(1);
        const digitalVotingPercentage = (mobileAppTrend[mobileAppTrend.length - 1] + webBrowserTrend[webBrowserTrend.length - 1]).toFixed(1);
        const urbanRuralDiff = (Math.random() * 5 + 10).toFixed(1);
        const educationCorrelation = correlationData[0];

        return {
            historicalTrends,
            demographicAnalysis,
            votingTimeAnalysis,
            regionalComparison,
            votingMethodTrends,
            insights: [
                `Voter turnout has increased by ${turnoutIncrease}% since ${years[0]}, showing growing democratic participation.`,
                `Urban areas show ${urbanRuralDiff}% higher voter turnout compared to rural regions.`,
                `Mobile and web voting methods have grown to ${digitalVotingPercentage}% of all votes in recent elections.`,
                `Invalid votes have decreased by ${invalidVotesDecrease}% since ${years[0]}, likely due to improved digital voting interfaces.`,
                `Peak voting times are between 12pm-2pm and 6pm-8pm, suggesting lunch breaks and after-work voting patterns.`,
                `Higher education levels correlate strongly (${educationCorrelation}) with voting participation.`,
                `Age demographics show that 25-34 and 45-54 age groups have the highest participation rates.`
            ]
        };
    };

    if (isLoading) {
        return (
            <div className="container mt-4">
                <div className="text-center p-5">
                    <div className="spinner-border text-primary" role="status">
                        <span className="visually-hidden">Loading...</span>
                    </div>
                    <p className="mt-3">Loading advanced analytics...</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="container mt-4">
                <div className="alert alert-danger" role="alert">
                    {error}
                </div>
            </div>
        );
    }

    return (
        <div className="container mt-4">
            <div className="d-flex justify-content-between align-items-center mb-4">
                <h2>Advanced Election Analytics</h2>
                <div className="d-flex gap-2">
                    <select
                        className="form-select"
                        value={selectedMetric}
                        onChange={(e) => setSelectedMetric(e.target.value)}
                    >
                        <option value="turnout">Voter Turnout</option>
                        <option value="demographics">Demographics</option>
                        <option value="methods">Voting Methods</option>
                        <option value="timing">Voting Timing</option>
                        <option value="regional">Regional Analysis</option>
                    </select>
                    <select
                        className="form-select"
                        value={selectedRegion}
                        onChange={(e) => setSelectedRegion(e.target.value)}
                    >
                        <option value="all">All Regions</option>
                        {Array.isArray(regions) && regions.map(region => (
                            <option key={region.id} value={region.id}>
                                {region.name}
                            </option>
                        ))}
                    </select>
                    <div className="form-check form-switch d-flex align-items-center">
                        <input
                            className="form-check-input me-2"
                            type="checkbox"
                            id="comparisonMode"
                            checked={comparisonMode}
                            onChange={(e) => setComparisonMode(e.target.checked)}
                        />
                        <label className="form-check-label" htmlFor="comparisonMode">
                            Comparison Mode
                        </label>
                    </div>
                </div>
            </div>

            {analytics && (
                <>
                    <div className="row mb-4">
                        <div className="col-md-12 mb-4">
                            <div className="card">
                                <div className="card-header">Historical Voting Trends (2002-2022)</div>
                                <div className="card-body" style={{ height: '300px' }}>
                                    <Suspense fallback={<ChartPlaceholder />}>
                                        <MemoizedLine
                                            data={analytics.historicalTrends}
                                            options={{
                                                maintainAspectRatio: false,
                                                responsive: true,
                                                animation: {
                                                    duration: 500
                                                },
                                                elements: {
                                                    point: {
                                                        radius: 3
                                                    },
                                                    line: {
                                                        tension: 0.2
                                                    }
                                                }
                                            }}
                                        />
                                    </Suspense>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="row mb-4">
                        <div className="col-md-6 mb-4">
                            <div className="card h-100">
                                <div className="card-header">Demographic Factors Influencing Voting</div>
                                <div className="card-body" style={{ height: '300px' }}>
                                    <Suspense fallback={<ChartPlaceholder />}>
                                        <MemoizedRadar
                                            data={analytics.demographicAnalysis}
                                            options={{
                                                maintainAspectRatio: false,
                                                responsive: true,
                                                animation: {
                                                    duration: 500
                                                }
                                            }}
                                        />
                                    </Suspense>
                                </div>
                            </div>
                        </div>
                        <div className="col-md-6 mb-4">
                            <div className="card h-100">
                                <div className="card-header">Voting Time Analysis by Age Group</div>
                                <div className="card-body" style={{ height: '300px' }}>
                                    <Suspense fallback={<ChartPlaceholder />}>
                                        <MemoizedBubble
                                            data={analytics.votingTimeAnalysis}
                                            options={{
                                                maintainAspectRatio: false,
                                                responsive: true,
                                                animation: {
                                                    duration: 500
                                                },
                                                scales: {
                                                    x: {
                                                        title: {
                                                            display: true,
                                                            text: 'Hour of Day (24h format)'
                                                        }
                                                    },
                                                    y: {
                                                        title: {
                                                            display: true,
                                                            text: 'Average Age'
                                                        }
                                                    }
                                                }
                                            }}
                                        />
                                    </Suspense>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="row mb-4">
                        <div className="col-md-12 mb-4">
                            <div className="card">
                                <div className="card-header">Evolution of Voting Methods (2002-2022)</div>
                                <div className="card-body" style={{ height: '300px' }}>
                                    <Suspense fallback={<ChartPlaceholder />}>
                                        <MemoizedLine
                                            data={analytics.votingMethodTrends}
                                            options={{
                                                maintainAspectRatio: false,
                                                responsive: true,
                                                animation: {
                                                    duration: 500
                                                },
                                                elements: {
                                                    point: {
                                                        radius: 3
                                                    },
                                                    line: {
                                                        tension: 0.2
                                                    }
                                                }
                                            }}
                                        />
                                    </Suspense>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="row mb-4">
                        <div className="col-md-12">
                            <div className="card">
                                <div className="card-header">Key Insights</div>
                                <div className="card-body">
                                    <ul className="list-group list-group-flush">
                                        {Array.isArray(analytics.insights) && analytics.insights.map((insight, index) => (
                                            <li key={index} className="list-group-item">
                                                <i className="bi bi-graph-up text-primary me-2"></i>
                                                {insight}
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </>
            )}
        </div>
    );
};

export default AdvancedAnalytics;
