'use strict';

var tslib = require('tslib');
var events = require('../utils/events.cjs.js');
var inView$1 = require('../../gestures/in-view.cjs.js');

const inView = {
    isActive: (options) => <PERSON><PERSON>an(options.inView),
    subscribe: (element, { enable, disable }, { inViewOptions = {} }) => {
        const { once } = inViewOptions, viewOptions = tslib.__rest(inViewOptions, ["once"]);
        return inView$1.inView(element, (enterEntry) => {
            enable();
            events.dispatchViewEvent(element, "viewenter", enterEntry);
            if (!once) {
                return (leaveEntry) => {
                    disable();
                    events.dispatchViewEvent(element, "viewleave", leaveEntry);
                };
            }
        }, viewOptions);
    },
};

exports.inView = inView;
