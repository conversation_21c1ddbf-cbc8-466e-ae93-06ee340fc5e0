# webpack-cli configtest

[![NPM Downloads][downloads]][downloads-url]

> **Note**
>
> This package is used by webpack-cli under-the-hood and is not intended for installation

## Description

This package validates a webpack configuration.

## Installation

```bash
#npm
npm i -D @webpack-cli/configtest

#yarn
yarn add -D @webpack-cli/configtest

```

## Usage

```bash
npx webpack configtest [config-path]
```

[downloads]: https://img.shields.io/npm/dm/@webpack-cli/configtest.svg
[downloads-url]: https://www.npmjs.com/package/@webpack-cli/configtest
