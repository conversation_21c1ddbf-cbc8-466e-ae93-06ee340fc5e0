export declare function nodeAesEncrypt(iv: <PERSON>int<PERSON><PERSON><PERSON><PERSON>, key: Uint<PERSON><PERSON><PERSON><PERSON>, data: Uint8Array): Uint8Array;
export declare function nodeAesDecrypt(iv: Uint8Array, key: Uint8Array, data: Uint8Array): Uint8Array;
export declare function nodeHmacSha256Sign(key: Uint8Array, data: Uint8Array): Uint8Array;
export declare function nodeHmacSha512Sign(key: Uint8Array, data: Uint8Array): Uint8Array;
export declare function nodeSha256(data: Uint8Array): Uint8Array;
export declare function nodeSha512(data: Uint8Array): Uint8Array;
export declare function nodeRipemd160(data: Uint8Array): Uint8Array;
//# sourceMappingURL=node.d.ts.map