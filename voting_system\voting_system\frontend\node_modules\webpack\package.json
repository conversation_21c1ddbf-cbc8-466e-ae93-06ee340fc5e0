{"name": "webpack", "version": "5.99.7", "author": "<PERSON> @sokra", "description": "Packs ECMAScript/CommonJs/AMD modules for the browser. Allows you to split your codebase into multiple bundles, which can be loaded on demand. Supports loaders to preprocess files, i.e. json, jsx, es7, css, less, ... and your custom stuff.", "license": "MIT", "dependencies": {"@types/eslint-scope": "^3.7.7", "@types/estree": "^1.0.6", "@types/json-schema": "^7.0.15", "@webassemblyjs/ast": "^1.14.1", "@webassemblyjs/wasm-edit": "^1.14.1", "@webassemblyjs/wasm-parser": "^1.14.1", "acorn": "^8.14.0", "browserslist": "^4.24.0", "chrome-trace-event": "^1.0.2", "enhanced-resolve": "^5.17.1", "es-module-lexer": "^1.2.1", "eslint-scope": "5.1.1", "events": "^3.2.0", "glob-to-regexp": "^0.4.1", "graceful-fs": "^4.2.11", "json-parse-even-better-errors": "^2.3.1", "loader-runner": "^4.2.0", "mime-types": "^2.1.27", "neo-async": "^2.6.2", "schema-utils": "^4.3.2", "tapable": "^2.1.1", "terser-webpack-plugin": "^5.3.11", "watchpack": "^2.4.1", "webpack-sources": "^3.2.3"}, "peerDependenciesMeta": {"webpack-cli": {"optional": true}}, "devDependencies": {"@babel/core": "^7.26.8", "@babel/preset-react": "^7.25.7", "@eslint/js": "^9.21.0", "@stylistic/eslint-plugin": "^4.2.0", "@types/glob-to-regexp": "^0.4.4", "@types/jest": "^29.5.11", "@types/mime-types": "^2.1.4", "@types/node": "^22.13.10", "assemblyscript": "^0.27.34", "babel-loader": "^10.0.0", "benchmark": "^2.1.4", "bundle-loader": "^0.5.6", "coffee-loader": "^5.0.0", "coffeescript": "^2.5.1", "core-js": "^3.6.5", "cspell": "^8.8.4", "css-loader": "^7.1.2", "date-fns": "^4.0.0", "es5-ext": "^0.10.53", "es6-promise-polyfill": "^1.2.0", "eslint": "^9.21.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-jest": "^28.6.0", "eslint-plugin-jsdoc": "^50.6.3", "eslint-plugin-n": "^17.16.2", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-unicorn": "^58.0.0", "file-loader": "^6.0.0", "fork-ts-checker-webpack-plugin": "^9.0.2", "globals": "^16.0.0", "hash-wasm": "^4.9.0", "husky": "^9.0.11", "istanbul": "^0.4.5", "jest": "^29.7.0", "jest-circus": "^29.7.0", "jest-cli": "^29.7.0", "jest-diff": "^29.7.0", "jest-environment-node": "^29.7.0", "jest-junit": "^16.0.0", "json-loader": "^0.5.7", "json5": "^2.1.3", "less": "^4.0.0", "less-loader": "^12.2.0", "lint-staged": "^15.2.5", "lodash": "^4.17.19", "lodash-es": "^4.17.15", "memfs": "^4.14.0", "mini-css-extract-plugin": "^2.9.0", "mini-svg-data-uri": "^1.2.3", "nyc": "^17.1.0", "open-cli": "^8.0.0", "prettier": "^3.5.1", "prettier-2": "npm:prettier@^2", "pretty-format": "^29.5.0", "pug": "^3.0.3", "pug-loader": "^2.4.0", "raw-loader": "^4.0.1", "react": "^19.0.0", "react-dom": "^19.0.0", "rimraf": "^3.0.2", "script-loader": "^0.7.2", "simple-git": "^3.27.0", "strip-ansi": "^6.0.0", "style-loader": "^4.0.0", "terser": "^5.38.1", "toml": "^3.0.0", "tooling": "webpack/tooling#v1.23.7", "ts-loader": "^9.5.1", "typescript": "^5.8.2", "url-loader": "^4.1.0", "wast-loader": "^1.12.1", "webassembly-feature": "1.3.0", "webpack-cli": "^6.0.1", "xxhashjs": "^0.2.2", "yamljs": "^0.3.0", "yarn-deduplicate": "^6.0.1"}, "engines": {"node": ">=10.13.0"}, "repository": {"type": "git", "url": "https://github.com/webpack/webpack.git"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "homepage": "https://github.com/webpack/webpack", "bugs": "https://github.com/webpack/webpack/issues", "main": "lib/index.js", "bin": {"webpack": "bin/webpack.js"}, "types": "types.d.ts", "files": ["lib/", "bin/", "hot/", "schemas/", "SECURITY.md", "module.d.ts", "types.d.ts"], "scripts": {"setup": "node ./setup/setup.js", "jest": "node --expose-gc --max-old-space-size=4096 --experimental-vm-modules --trace-deprecation node_modules/jest-cli/bin/jest --logHeapUsage", "test": "node --expose-gc --max-old-space-size=4096 --experimental-vm-modules --trace-deprecation node_modules/jest-cli/bin/jest --logHeapUsage", "test:update-snapshots": "yarn jest -u", "test:integration": "node --expose-gc --max-old-space-size=4096 --experimental-vm-modules --trace-deprecation node_modules/jest-cli/bin/jest --logHeapUsage --testMatch \"<rootDir>/test/*.{basictest,longtest,test}.js\"", "test:basic": "node --expose-gc --max-old-space-size=4096 --experimental-vm-modules --trace-deprecation node_modules/jest-cli/bin/jest --logHeapUsage --testMatch \"<rootDir>/test/*.basictest.js\"", "test:unit": "node --max-old-space-size=4096 --experimental-vm-modules --trace-deprecation node_modules/jest-cli/bin/jest --testMatch \"<rootDir>/test/*.unittest.js\"", "build:examples": "cd examples && node buildAll.js", "type-report": "rimraf coverage && yarn cover:types && yarn cover:report && open-cli coverage/lcov-report/index.html", "pretest": "yarn lint", "prelint": "yarn setup", "lint": "yarn code-lint && yarn special-lint && yarn type-lint && yarn typings-test && yarn module-typings-test && yarn yarn-lint && yarn pretty-lint && yarn spellcheck", "code-lint": "node node_modules/eslint/bin/eslint.js --cache .", "type-lint": "tsc", "type-validate": "tsc -p tsconfig.validation.json", "typings-test": "tsc -p tsconfig.types.test.json", "module-typings-test": "tsc -p tsconfig.module.test.json", "spellcheck": "cspell --cache --no-must-find-files --quiet \"**/*.*\"", "special-lint": "node node_modules/tooling/lockfile-lint && node node_modules/tooling/schemas-lint && node node_modules/tooling/inherit-types && node node_modules/tooling/format-schemas && node tooling/generate-runtime-code.js && node tooling/generate-wasm-code.js && node node_modules/tooling/format-file-header && node node_modules/tooling/compile-to-definitions && node node_modules/tooling/precompile-schemas && node node_modules/tooling/generate-types --no-template-literals", "special-lint-fix": "node node_modules/tooling/inherit-types --write && node node_modules/tooling/format-schemas --write && node tooling/generate-runtime-code.js --write && node tooling/generate-wasm-code.js --write && node node_modules/tooling/format-file-header --write && node node_modules/tooling/compile-to-definitions --write && node node_modules/tooling/precompile-schemas --write && node node_modules/tooling/generate-types --no-template-literals --write", "fix": "yarn code-lint --fix && yarn special-lint-fix && yarn pretty-lint-fix", "prepare": "husky", "pretty-lint-base": "node node_modules/prettier/bin/prettier.cjs --cache --ignore-unknown .", "pretty-lint-fix": "yarn pretty-lint-base --log-level warn --write", "pretty-lint": "yarn pretty-lint-base --check", "yarn-lint": "yarn-deduplicate --fail --list -s highest yarn.lock", "yarn-lint-fix": "yarn-deduplicate -s highest yarn.lock", "benchmark": "node --max-old-space-size=4096 --experimental-vm-modules --trace-deprecation node_modules/jest-cli/bin/jest --testMatch \"<rootDir>/test/*.benchmark.mjs\" --runInBand", "cover": "yarn cover:all && yarn cover:report", "cover:clean": "rimraf .nyc_output coverage", "cover:all": "node --expose-gc --max-old-space-size=4096 --experimental-vm-modules node_modules/jest-cli/bin/jest --logHeapUsage --coverage", "cover:basic": "node --expose-gc --max-old-space-size=4096 --experimental-vm-modules node_modules/jest-cli/bin/jest --logHeapUsage --testMatch \"<rootDir>/test/*.basictest.js\" --coverage", "cover:integration": "node --expose-gc --max-old-space-size=4096 --experimental-vm-modules node_modules/jest-cli/bin/jest --logHeapUsage --testMatch \"<rootDir>/test/*.{basictest,longtest,test}.js\" --coverage", "cover:integration:a": "node --expose-gc --max-old-space-size=4096 --experimental-vm-modules node_modules/jest-cli/bin/jest --logHeapUsage --testMatch \"<rootDir>/test/*.{basictest,test}.js\" --coverage", "cover:integration:b": "node --expose-gc --max-old-space-size=4096 --experimental-vm-modules node_modules/jest-cli/bin/jest --logHeapUsage --testMatch \"<rootDir>/test/*.longtest.js\" --coverage", "cover:unit": "node --max-old-space-size=4096 --experimental-vm-modules node_modules/jest-cli/bin/jest --testMatch \"<rootDir>/test/*.unittest.js\" --coverage", "cover:types": "node node_modules/tooling/type-coverage", "cover:merge": "yarn mkdirp .nyc_output && nyc merge .nyc_output coverage/coverage-nyc.json && rimraf .nyc_output", "cover:report": "nyc report --reporter=lcov  --reporter=text -t coverage"}, "lint-staged": {"*.{js,cjs,mjs}": ["node node_modules/eslint/bin/eslint.js --cache --fix"], "*": ["node node_modules/prettier/bin/prettier.cjs --cache --write --ignore-unknown", "cspell --cache --no-must-find-files"]}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}