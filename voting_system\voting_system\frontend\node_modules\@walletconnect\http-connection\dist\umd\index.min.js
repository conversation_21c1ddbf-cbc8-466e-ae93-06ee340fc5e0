!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define("WalletConnectHttpConnection",[],e):"object"==typeof exports?exports.WalletConnectHttpConnection=e():t.WalletConnectHttpConnection=e()}(this,(function(){return function(t){var e={};function r(n){if(e[n])return e[n].exports;var i=e[n]={i:n,l:!1,exports:{}};return t[n].call(i.exports,i,i.exports,r),i.l=!0,i.exports}return r.m=t,r.c=e,r.d=function(t,e,n){r.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)r.d(n,i,function(e){return t[e]}.bind(null,i));return n},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="",r(r.s=47)}([function(t,e,r){"use strict";(function(t){r.d(e,"f",(function(){return a})),r.d(e,"g",(function(){return u})),r.d(e,"i",(function(){return h})),r.d(e,"h",(function(){return f})),r.d(e,"b",(function(){return c})),r.d(e,"c",(function(){return l})),r.d(e,"e",(function(){return d})),r.d(e,"d",(function(){return p})),r.d(e,"n",(function(){return m})),r.d(e,"m",(function(){return g})),r.d(e,"o",(function(){return y})),r.d(e,"z",(function(){return v})),r.d(e,"y",(function(){return w})),r.d(e,"A",(function(){return b})),r.d(e,"u",(function(){return _})),r.d(e,"t",(function(){return M})),r.d(e,"r",(function(){return A})),r.d(e,"q",(function(){return O})),r.d(e,"s",(function(){return x})),r.d(e,"p",(function(){return T})),r.d(e,"l",(function(){return R})),r.d(e,"k",(function(){return k})),r.d(e,"j",(function(){return C})),r.d(e,"w",(function(){return P})),r.d(e,"a",(function(){return B})),r.d(e,"x",(function(){return N})),r.d(e,"v",(function(){return I}));var n=r(17),i=r.n(n),o=r(44),s=r.n(o);function a(t){return new Uint8Array(t)}function u(t,e=!1){const r=t.toString("hex");return e?B(r):r}function h(t){return t.toString("utf8")}function f(t){return t.readUIntBE(0,t.length)}function c(t){return s()(t)}function l(t,e=!1){return u(c(t),e)}function d(t){return h(c(t))}function p(t){return f(c(t))}function m(e){return t.from(P(e),"hex")}function g(t){return a(m(t))}function y(t){return h(m(t))}function v(e){return t.from(e,"utf8")}function w(t){return a(v(t))}function b(t,e=!1){return u(v(t),e)}function _(t){return c(E(S(t)))}function M(t){return E(S(t))}function S(t){return L((t>>>0).toString(2))}function E(t){return new Uint8Array(j(t).map(t=>parseInt(t,2)))}function A(t,e){return!("string"!=typeof t||!t.match(/^0x[0-9A-Fa-f]*$/))&&(!e||t.length===2+2*e)}function O(e){return t.isBuffer(e)}function x(t){return i.a.strict(t)&&!O(t)}function T(t){return!x(t)&&!O(t)&&void 0!==t.byteLength}function R(t){return O(t)?"buffer":x(t)?"typed-array":T(t)?"array-buffer":Array.isArray(t)?"array":typeof t}function k(t){return function(t){return!("string"!=typeof t||!new RegExp(/^[01]+$/).test(t))&&t.length%8==0}(t)?"binary":A(t)?"hex":"utf8"}function C(...e){return t.concat(e)}function j(t,e=8){const r=L(t).match(new RegExp(`.{${e}}`,"gi"));return Array.from(r||[])}function L(t,e=8,r="0"){return function(t,e,r="0"){return U(t,e,!0,r)}(t,function(t,e=8){const r=t%e;return r?(t-r)/e*e+e:t}(t.length,e),r)}function P(t){return t.replace(/^0x/,"")}function B(t){return t.startsWith("0x")?t:"0x"+t}function N(t){return(t=L(t=P(t),2))&&(t=B(t)),t}function I(t){const e=t.startsWith("0x");return t=(t=P(t)).startsWith("0")?t.substring(1):t,e?B(t):t}function U(t,e,r,n="0"){const i=e-t.length;let o=t;if(i>0){const e=n.repeat(i);o=r?e+t:t+e}return o}}).call(this,r(5).Buffer)},function(t,e,r){"use strict";r.d(e,"b",(function(){return n})),r.d(e,"d",(function(){return i})),r.d(e,"c",(function(){return o})),r.d(e,"e",(function(){return s})),r.d(e,"f",(function(){return a})),r.d(e,"a",(function(){return u}));const n="INTERNAL_ERROR",i="SERVER_ERROR",o=[-32700,-32600,-32601,-32602,-32603],s=[-32e3,-32099],a={PARSE_ERROR:{code:-32700,message:"Parse error"},INVALID_REQUEST:{code:-32600,message:"Invalid Request"},METHOD_NOT_FOUND:{code:-32601,message:"Method not found"},INVALID_PARAMS:{code:-32602,message:"Invalid params"},[n]:{code:-32603,message:"Internal error"},[i]:{code:-32e3,message:"Server error"}},u=i},function(t,e,r){"use strict";function n(t){let e=void 0;return"undefined"!=typeof window&&void 0!==window[t]&&(e=window[t]),e}function i(t){const e=n(t);if(!e)throw new Error(t+" is not defined in Window");return e}Object.defineProperty(e,"__esModule",{value:!0}),e.getLocalStorage=e.getLocalStorageOrThrow=e.getCrypto=e.getCryptoOrThrow=e.getLocation=e.getLocationOrThrow=e.getNavigator=e.getNavigatorOrThrow=e.getDocument=e.getDocumentOrThrow=e.getFromWindowOrThrow=e.getFromWindow=void 0,e.getFromWindow=n,e.getFromWindowOrThrow=i,e.getDocumentOrThrow=function(){return i("document")},e.getDocument=function(){return n("document")},e.getNavigatorOrThrow=function(){return i("navigator")},e.getNavigator=function(){return n("navigator")},e.getLocationOrThrow=function(){return i("location")},e.getLocation=function(){return n("location")},e.getCryptoOrThrow=function(){return i("crypto")},e.getCrypto=function(){return n("crypto")},e.getLocalStorageOrThrow=function(){return i("localStorage")},e.getLocalStorage=function(){return n("localStorage")}},function(t,e){var r;r=function(){return this}();try{r=r||new Function("return this")()}catch(t){"object"==typeof window&&(r=window)}t.exports=r},function(t,e){var r,n,i=t.exports={};function o(){throw new Error("setTimeout has not been defined")}function s(){throw new Error("clearTimeout has not been defined")}function a(t){if(r===setTimeout)return setTimeout(t,0);if((r===o||!r)&&setTimeout)return r=setTimeout,setTimeout(t,0);try{return r(t,0)}catch(e){try{return r.call(null,t,0)}catch(e){return r.call(this,t,0)}}}!function(){try{r="function"==typeof setTimeout?setTimeout:o}catch(t){r=o}try{n="function"==typeof clearTimeout?clearTimeout:s}catch(t){n=s}}();var u,h=[],f=!1,c=-1;function l(){f&&u&&(f=!1,u.length?h=u.concat(h):c=-1,h.length&&d())}function d(){if(!f){var t=a(l);f=!0;for(var e=h.length;e;){for(u=h,h=[];++c<e;)u&&u[c].run();c=-1,e=h.length}u=null,f=!1,function(t){if(n===clearTimeout)return clearTimeout(t);if((n===s||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(t);try{n(t)}catch(e){try{return n.call(null,t)}catch(e){return n.call(this,t)}}}(t)}}function p(t,e){this.fun=t,this.array=e}function m(){}i.nextTick=function(t){var e=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)e[r-1]=arguments[r];h.push(new p(t,e)),1!==h.length||f||a(d)},p.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=m,i.addListener=m,i.once=m,i.off=m,i.removeListener=m,i.removeAllListeners=m,i.emit=m,i.prependListener=m,i.prependOnceListener=m,i.listeners=function(t){return[]},i.binding=function(t){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(t){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},function(t,e,r){"use strict";(function(t){
/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <http://feross.org>
 * @license  MIT
 */
var n=r(52),i=r(53),o=r(19);function s(){return u.TYPED_ARRAY_SUPPORT?**********:**********}function a(t,e){if(s()<e)throw new RangeError("Invalid typed array length");return u.TYPED_ARRAY_SUPPORT?(t=new Uint8Array(e)).__proto__=u.prototype:(null===t&&(t=new u(e)),t.length=e),t}function u(t,e,r){if(!(u.TYPED_ARRAY_SUPPORT||this instanceof u))return new u(t,e,r);if("number"==typeof t){if("string"==typeof e)throw new Error("If encoding is specified then the first argument must be a string");return c(this,t)}return h(this,t,e,r)}function h(t,e,r,n){if("number"==typeof e)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&e instanceof ArrayBuffer?function(t,e,r,n){if(e.byteLength,r<0||e.byteLength<r)throw new RangeError("'offset' is out of bounds");if(e.byteLength<r+(n||0))throw new RangeError("'length' is out of bounds");e=void 0===r&&void 0===n?new Uint8Array(e):void 0===n?new Uint8Array(e,r):new Uint8Array(e,r,n);u.TYPED_ARRAY_SUPPORT?(t=e).__proto__=u.prototype:t=l(t,e);return t}(t,e,r,n):"string"==typeof e?function(t,e,r){"string"==typeof r&&""!==r||(r="utf8");if(!u.isEncoding(r))throw new TypeError('"encoding" must be a valid string encoding');var n=0|p(e,r),i=(t=a(t,n)).write(e,r);i!==n&&(t=t.slice(0,i));return t}(t,e,r):function(t,e){if(u.isBuffer(e)){var r=0|d(e.length);return 0===(t=a(t,r)).length||e.copy(t,0,0,r),t}if(e){if("undefined"!=typeof ArrayBuffer&&e.buffer instanceof ArrayBuffer||"length"in e)return"number"!=typeof e.length||(n=e.length)!=n?a(t,0):l(t,e);if("Buffer"===e.type&&o(e.data))return l(t,e.data)}var n;throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(t,e)}function f(t){if("number"!=typeof t)throw new TypeError('"size" argument must be a number');if(t<0)throw new RangeError('"size" argument must not be negative')}function c(t,e){if(f(e),t=a(t,e<0?0:0|d(e)),!u.TYPED_ARRAY_SUPPORT)for(var r=0;r<e;++r)t[r]=0;return t}function l(t,e){var r=e.length<0?0:0|d(e.length);t=a(t,r);for(var n=0;n<r;n+=1)t[n]=255&e[n];return t}function d(t){if(t>=s())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+s().toString(16)+" bytes");return 0|t}function p(t,e){if(u.isBuffer(t))return t.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(t)||t instanceof ArrayBuffer))return t.byteLength;"string"!=typeof t&&(t=""+t);var r=t.length;if(0===r)return 0;for(var n=!1;;)switch(e){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":case void 0:return D(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return q(t).length;default:if(n)return D(t).length;e=(""+e).toLowerCase(),n=!0}}function m(t,e,r){var n=!1;if((void 0===e||e<0)&&(e=0),e>this.length)return"";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return"";if((r>>>=0)<=(e>>>=0))return"";for(t||(t="utf8");;)switch(t){case"hex":return R(this,e,r);case"utf8":case"utf-8":return O(this,e,r);case"ascii":return x(this,e,r);case"latin1":case"binary":return T(this,e,r);case"base64":return A(this,e,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return k(this,e,r);default:if(n)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),n=!0}}function g(t,e,r){var n=t[e];t[e]=t[r],t[r]=n}function y(t,e,r,n,i){if(0===t.length)return-1;if("string"==typeof r?(n=r,r=0):r>**********?r=**********:r<-**********&&(r=-**********),r=+r,isNaN(r)&&(r=i?0:t.length-1),r<0&&(r=t.length+r),r>=t.length){if(i)return-1;r=t.length-1}else if(r<0){if(!i)return-1;r=0}if("string"==typeof e&&(e=u.from(e,n)),u.isBuffer(e))return 0===e.length?-1:v(t,e,r,n,i);if("number"==typeof e)return e&=255,u.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(t,e,r):Uint8Array.prototype.lastIndexOf.call(t,e,r):v(t,[e],r,n,i);throw new TypeError("val must be string, number or Buffer")}function v(t,e,r,n,i){var o,s=1,a=t.length,u=e.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(t.length<2||e.length<2)return-1;s=2,a/=2,u/=2,r/=2}function h(t,e){return 1===s?t[e]:t.readUInt16BE(e*s)}if(i){var f=-1;for(o=r;o<a;o++)if(h(t,o)===h(e,-1===f?0:o-f)){if(-1===f&&(f=o),o-f+1===u)return f*s}else-1!==f&&(o-=o-f),f=-1}else for(r+u>a&&(r=a-u),o=r;o>=0;o--){for(var c=!0,l=0;l<u;l++)if(h(t,o+l)!==h(e,l)){c=!1;break}if(c)return o}return-1}function w(t,e,r,n){r=Number(r)||0;var i=t.length-r;n?(n=Number(n))>i&&(n=i):n=i;var o=e.length;if(o%2!=0)throw new TypeError("Invalid hex string");n>o/2&&(n=o/2);for(var s=0;s<n;++s){var a=parseInt(e.substr(2*s,2),16);if(isNaN(a))return s;t[r+s]=a}return s}function b(t,e,r,n){return W(D(e,t.length-r),t,r,n)}function _(t,e,r,n){return W(function(t){for(var e=[],r=0;r<t.length;++r)e.push(255&t.charCodeAt(r));return e}(e),t,r,n)}function M(t,e,r,n){return _(t,e,r,n)}function S(t,e,r,n){return W(q(e),t,r,n)}function E(t,e,r,n){return W(function(t,e){for(var r,n,i,o=[],s=0;s<t.length&&!((e-=2)<0);++s)r=t.charCodeAt(s),n=r>>8,i=r%256,o.push(i),o.push(n);return o}(e,t.length-r),t,r,n)}function A(t,e,r){return 0===e&&r===t.length?n.fromByteArray(t):n.fromByteArray(t.slice(e,r))}function O(t,e,r){r=Math.min(t.length,r);for(var n=[],i=e;i<r;){var o,s,a,u,h=t[i],f=null,c=h>239?4:h>223?3:h>191?2:1;if(i+c<=r)switch(c){case 1:h<128&&(f=h);break;case 2:128==(192&(o=t[i+1]))&&(u=(31&h)<<6|63&o)>127&&(f=u);break;case 3:o=t[i+1],s=t[i+2],128==(192&o)&&128==(192&s)&&(u=(15&h)<<12|(63&o)<<6|63&s)>2047&&(u<55296||u>57343)&&(f=u);break;case 4:o=t[i+1],s=t[i+2],a=t[i+3],128==(192&o)&&128==(192&s)&&128==(192&a)&&(u=(15&h)<<18|(63&o)<<12|(63&s)<<6|63&a)>65535&&u<1114112&&(f=u)}null===f?(f=65533,c=1):f>65535&&(f-=65536,n.push(f>>>10&1023|55296),f=56320|1023&f),n.push(f),i+=c}return function(t){var e=t.length;if(e<=4096)return String.fromCharCode.apply(String,t);var r="",n=0;for(;n<e;)r+=String.fromCharCode.apply(String,t.slice(n,n+=4096));return r}(n)}e.Buffer=u,e.SlowBuffer=function(t){+t!=t&&(t=0);return u.alloc(+t)},e.INSPECT_MAX_BYTES=50,u.TYPED_ARRAY_SUPPORT=void 0!==t.TYPED_ARRAY_SUPPORT?t.TYPED_ARRAY_SUPPORT:function(){try{var t=new Uint8Array(1);return t.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===t.foo()&&"function"==typeof t.subarray&&0===t.subarray(1,1).byteLength}catch(t){return!1}}(),e.kMaxLength=s(),u.poolSize=8192,u._augment=function(t){return t.__proto__=u.prototype,t},u.from=function(t,e,r){return h(null,t,e,r)},u.TYPED_ARRAY_SUPPORT&&(u.prototype.__proto__=Uint8Array.prototype,u.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&u[Symbol.species]===u&&Object.defineProperty(u,Symbol.species,{value:null,configurable:!0})),u.alloc=function(t,e,r){return function(t,e,r,n){return f(e),e<=0?a(t,e):void 0!==r?"string"==typeof n?a(t,e).fill(r,n):a(t,e).fill(r):a(t,e)}(null,t,e,r)},u.allocUnsafe=function(t){return c(null,t)},u.allocUnsafeSlow=function(t){return c(null,t)},u.isBuffer=function(t){return!(null==t||!t._isBuffer)},u.compare=function(t,e){if(!u.isBuffer(t)||!u.isBuffer(e))throw new TypeError("Arguments must be Buffers");if(t===e)return 0;for(var r=t.length,n=e.length,i=0,o=Math.min(r,n);i<o;++i)if(t[i]!==e[i]){r=t[i],n=e[i];break}return r<n?-1:n<r?1:0},u.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},u.concat=function(t,e){if(!o(t))throw new TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return u.alloc(0);var r;if(void 0===e)for(e=0,r=0;r<t.length;++r)e+=t[r].length;var n=u.allocUnsafe(e),i=0;for(r=0;r<t.length;++r){var s=t[r];if(!u.isBuffer(s))throw new TypeError('"list" argument must be an Array of Buffers');s.copy(n,i),i+=s.length}return n},u.byteLength=p,u.prototype._isBuffer=!0,u.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)g(this,e,e+1);return this},u.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)g(this,e,e+3),g(this,e+1,e+2);return this},u.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)g(this,e,e+7),g(this,e+1,e+6),g(this,e+2,e+5),g(this,e+3,e+4);return this},u.prototype.toString=function(){var t=0|this.length;return 0===t?"":0===arguments.length?O(this,0,t):m.apply(this,arguments)},u.prototype.equals=function(t){if(!u.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t||0===u.compare(this,t)},u.prototype.inspect=function(){var t="",r=e.INSPECT_MAX_BYTES;return this.length>0&&(t=this.toString("hex",0,r).match(/.{2}/g).join(" "),this.length>r&&(t+=" ... ")),"<Buffer "+t+">"},u.prototype.compare=function(t,e,r,n,i){if(!u.isBuffer(t))throw new TypeError("Argument must be a Buffer");if(void 0===e&&(e=0),void 0===r&&(r=t?t.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),e<0||r>t.length||n<0||i>this.length)throw new RangeError("out of range index");if(n>=i&&e>=r)return 0;if(n>=i)return-1;if(e>=r)return 1;if(this===t)return 0;for(var o=(i>>>=0)-(n>>>=0),s=(r>>>=0)-(e>>>=0),a=Math.min(o,s),h=this.slice(n,i),f=t.slice(e,r),c=0;c<a;++c)if(h[c]!==f[c]){o=h[c],s=f[c];break}return o<s?-1:s<o?1:0},u.prototype.includes=function(t,e,r){return-1!==this.indexOf(t,e,r)},u.prototype.indexOf=function(t,e,r){return y(this,t,e,r,!0)},u.prototype.lastIndexOf=function(t,e,r){return y(this,t,e,r,!1)},u.prototype.write=function(t,e,r,n){if(void 0===e)n="utf8",r=this.length,e=0;else if(void 0===r&&"string"==typeof e)n=e,r=this.length,e=0;else{if(!isFinite(e))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");e|=0,isFinite(r)?(r|=0,void 0===n&&(n="utf8")):(n=r,r=void 0)}var i=this.length-e;if((void 0===r||r>i)&&(r=i),t.length>0&&(r<0||e<0)||e>this.length)throw new RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var o=!1;;)switch(n){case"hex":return w(this,t,e,r);case"utf8":case"utf-8":return b(this,t,e,r);case"ascii":return _(this,t,e,r);case"latin1":case"binary":return M(this,t,e,r);case"base64":return S(this,t,e,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return E(this,t,e,r);default:if(o)throw new TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),o=!0}},u.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};function x(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(127&t[i]);return n}function T(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(t[i]);return n}function R(t,e,r){var n=t.length;(!e||e<0)&&(e=0),(!r||r<0||r>n)&&(r=n);for(var i="",o=e;o<r;++o)i+=H(t[o]);return i}function k(t,e,r){for(var n=t.slice(e,r),i="",o=0;o<n.length;o+=2)i+=String.fromCharCode(n[o]+256*n[o+1]);return i}function C(t,e,r){if(t%1!=0||t<0)throw new RangeError("offset is not uint");if(t+e>r)throw new RangeError("Trying to access beyond buffer length")}function j(t,e,r,n,i,o){if(!u.isBuffer(t))throw new TypeError('"buffer" argument must be a Buffer instance');if(e>i||e<o)throw new RangeError('"value" argument is out of bounds');if(r+n>t.length)throw new RangeError("Index out of range")}function L(t,e,r,n){e<0&&(e=65535+e+1);for(var i=0,o=Math.min(t.length-r,2);i<o;++i)t[r+i]=(e&255<<8*(n?i:1-i))>>>8*(n?i:1-i)}function P(t,e,r,n){e<0&&(e=4294967295+e+1);for(var i=0,o=Math.min(t.length-r,4);i<o;++i)t[r+i]=e>>>8*(n?i:3-i)&255}function B(t,e,r,n,i,o){if(r+n>t.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function N(t,e,r,n,o){return o||B(t,0,r,4),i.write(t,e,r,n,23,4),r+4}function I(t,e,r,n,o){return o||B(t,0,r,8),i.write(t,e,r,n,52,8),r+8}u.prototype.slice=function(t,e){var r,n=this.length;if((t=~~t)<0?(t+=n)<0&&(t=0):t>n&&(t=n),(e=void 0===e?n:~~e)<0?(e+=n)<0&&(e=0):e>n&&(e=n),e<t&&(e=t),u.TYPED_ARRAY_SUPPORT)(r=this.subarray(t,e)).__proto__=u.prototype;else{var i=e-t;r=new u(i,void 0);for(var o=0;o<i;++o)r[o]=this[o+t]}return r},u.prototype.readUIntLE=function(t,e,r){t|=0,e|=0,r||C(t,e,this.length);for(var n=this[t],i=1,o=0;++o<e&&(i*=256);)n+=this[t+o]*i;return n},u.prototype.readUIntBE=function(t,e,r){t|=0,e|=0,r||C(t,e,this.length);for(var n=this[t+--e],i=1;e>0&&(i*=256);)n+=this[t+--e]*i;return n},u.prototype.readUInt8=function(t,e){return e||C(t,1,this.length),this[t]},u.prototype.readUInt16LE=function(t,e){return e||C(t,2,this.length),this[t]|this[t+1]<<8},u.prototype.readUInt16BE=function(t,e){return e||C(t,2,this.length),this[t]<<8|this[t+1]},u.prototype.readUInt32LE=function(t,e){return e||C(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},u.prototype.readUInt32BE=function(t,e){return e||C(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},u.prototype.readIntLE=function(t,e,r){t|=0,e|=0,r||C(t,e,this.length);for(var n=this[t],i=1,o=0;++o<e&&(i*=256);)n+=this[t+o]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*e)),n},u.prototype.readIntBE=function(t,e,r){t|=0,e|=0,r||C(t,e,this.length);for(var n=e,i=1,o=this[t+--n];n>0&&(i*=256);)o+=this[t+--n]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*e)),o},u.prototype.readInt8=function(t,e){return e||C(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},u.prototype.readInt16LE=function(t,e){e||C(t,2,this.length);var r=this[t]|this[t+1]<<8;return 32768&r?4294901760|r:r},u.prototype.readInt16BE=function(t,e){e||C(t,2,this.length);var r=this[t+1]|this[t]<<8;return 32768&r?4294901760|r:r},u.prototype.readInt32LE=function(t,e){return e||C(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},u.prototype.readInt32BE=function(t,e){return e||C(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},u.prototype.readFloatLE=function(t,e){return e||C(t,4,this.length),i.read(this,t,!0,23,4)},u.prototype.readFloatBE=function(t,e){return e||C(t,4,this.length),i.read(this,t,!1,23,4)},u.prototype.readDoubleLE=function(t,e){return e||C(t,8,this.length),i.read(this,t,!0,52,8)},u.prototype.readDoubleBE=function(t,e){return e||C(t,8,this.length),i.read(this,t,!1,52,8)},u.prototype.writeUIntLE=function(t,e,r,n){(t=+t,e|=0,r|=0,n)||j(this,t,e,r,Math.pow(2,8*r)-1,0);var i=1,o=0;for(this[e]=255&t;++o<r&&(i*=256);)this[e+o]=t/i&255;return e+r},u.prototype.writeUIntBE=function(t,e,r,n){(t=+t,e|=0,r|=0,n)||j(this,t,e,r,Math.pow(2,8*r)-1,0);var i=r-1,o=1;for(this[e+i]=255&t;--i>=0&&(o*=256);)this[e+i]=t/o&255;return e+r},u.prototype.writeUInt8=function(t,e,r){return t=+t,e|=0,r||j(this,t,e,1,255,0),u.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),this[e]=255&t,e+1},u.prototype.writeUInt16LE=function(t,e,r){return t=+t,e|=0,r||j(this,t,e,2,65535,0),u.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8):L(this,t,e,!0),e+2},u.prototype.writeUInt16BE=function(t,e,r){return t=+t,e|=0,r||j(this,t,e,2,65535,0),u.TYPED_ARRAY_SUPPORT?(this[e]=t>>>8,this[e+1]=255&t):L(this,t,e,!1),e+2},u.prototype.writeUInt32LE=function(t,e,r){return t=+t,e|=0,r||j(this,t,e,4,4294967295,0),u.TYPED_ARRAY_SUPPORT?(this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t):P(this,t,e,!0),e+4},u.prototype.writeUInt32BE=function(t,e,r){return t=+t,e|=0,r||j(this,t,e,4,4294967295,0),u.TYPED_ARRAY_SUPPORT?(this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t):P(this,t,e,!1),e+4},u.prototype.writeIntLE=function(t,e,r,n){if(t=+t,e|=0,!n){var i=Math.pow(2,8*r-1);j(this,t,e,r,i-1,-i)}var o=0,s=1,a=0;for(this[e]=255&t;++o<r&&(s*=256);)t<0&&0===a&&0!==this[e+o-1]&&(a=1),this[e+o]=(t/s>>0)-a&255;return e+r},u.prototype.writeIntBE=function(t,e,r,n){if(t=+t,e|=0,!n){var i=Math.pow(2,8*r-1);j(this,t,e,r,i-1,-i)}var o=r-1,s=1,a=0;for(this[e+o]=255&t;--o>=0&&(s*=256);)t<0&&0===a&&0!==this[e+o+1]&&(a=1),this[e+o]=(t/s>>0)-a&255;return e+r},u.prototype.writeInt8=function(t,e,r){return t=+t,e|=0,r||j(this,t,e,1,127,-128),u.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),t<0&&(t=255+t+1),this[e]=255&t,e+1},u.prototype.writeInt16LE=function(t,e,r){return t=+t,e|=0,r||j(this,t,e,2,32767,-32768),u.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8):L(this,t,e,!0),e+2},u.prototype.writeInt16BE=function(t,e,r){return t=+t,e|=0,r||j(this,t,e,2,32767,-32768),u.TYPED_ARRAY_SUPPORT?(this[e]=t>>>8,this[e+1]=255&t):L(this,t,e,!1),e+2},u.prototype.writeInt32LE=function(t,e,r){return t=+t,e|=0,r||j(this,t,e,4,**********,-**********),u.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24):P(this,t,e,!0),e+4},u.prototype.writeInt32BE=function(t,e,r){return t=+t,e|=0,r||j(this,t,e,4,**********,-**********),t<0&&(t=4294967295+t+1),u.TYPED_ARRAY_SUPPORT?(this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t):P(this,t,e,!1),e+4},u.prototype.writeFloatLE=function(t,e,r){return N(this,t,e,!0,r)},u.prototype.writeFloatBE=function(t,e,r){return N(this,t,e,!1,r)},u.prototype.writeDoubleLE=function(t,e,r){return I(this,t,e,!0,r)},u.prototype.writeDoubleBE=function(t,e,r){return I(this,t,e,!1,r)},u.prototype.copy=function(t,e,r,n){if(r||(r=0),n||0===n||(n=this.length),e>=t.length&&(e=t.length),e||(e=0),n>0&&n<r&&(n=r),n===r)return 0;if(0===t.length||0===this.length)return 0;if(e<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("sourceStart out of bounds");if(n<0)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),t.length-e<n-r&&(n=t.length-e+r);var i,o=n-r;if(this===t&&r<e&&e<n)for(i=o-1;i>=0;--i)t[i+e]=this[i+r];else if(o<1e3||!u.TYPED_ARRAY_SUPPORT)for(i=0;i<o;++i)t[i+e]=this[i+r];else Uint8Array.prototype.set.call(t,this.subarray(r,r+o),e);return o},u.prototype.fill=function(t,e,r,n){if("string"==typeof t){if("string"==typeof e?(n=e,e=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),1===t.length){var i=t.charCodeAt(0);i<256&&(t=i)}if(void 0!==n&&"string"!=typeof n)throw new TypeError("encoding must be a string");if("string"==typeof n&&!u.isEncoding(n))throw new TypeError("Unknown encoding: "+n)}else"number"==typeof t&&(t&=255);if(e<0||this.length<e||this.length<r)throw new RangeError("Out of range index");if(r<=e)return this;var o;if(e>>>=0,r=void 0===r?this.length:r>>>0,t||(t=0),"number"==typeof t)for(o=e;o<r;++o)this[o]=t;else{var s=u.isBuffer(t)?t:D(new u(t,n).toString()),a=s.length;for(o=0;o<r-e;++o)this[o+e]=s[o%a]}return this};var U=/[^+\/0-9A-Za-z-_]/g;function H(t){return t<16?"0"+t.toString(16):t.toString(16)}function D(t,e){var r;e=e||1/0;for(var n=t.length,i=null,o=[],s=0;s<n;++s){if((r=t.charCodeAt(s))>55295&&r<57344){if(!i){if(r>56319){(e-=3)>-1&&o.push(239,191,189);continue}if(s+1===n){(e-=3)>-1&&o.push(239,191,189);continue}i=r;continue}if(r<56320){(e-=3)>-1&&o.push(239,191,189),i=r;continue}r=65536+(i-55296<<10|r-56320)}else i&&(e-=3)>-1&&o.push(239,191,189);if(i=null,r<128){if((e-=1)<0)break;o.push(r)}else if(r<2048){if((e-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((e-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((e-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return o}function q(t){return n.toByteArray(function(t){if((t=function(t){return t.trim?t.trim():t.replace(/^\s+|\s+$/g,"")}(t).replace(U,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function W(t,e,r,n){for(var i=0;i<n&&!(i+r>=e.length||i>=t.length);++i)e[i+r]=t[i];return i}}).call(this,r(3))},function(t,e){"function"==typeof Object.create?t.exports=function(t,e){e&&(t.super_=e,t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}))}:t.exports=function(t,e){if(e){t.super_=e;var r=function(){};r.prototype=e.prototype,t.prototype=new r,t.prototype.constructor=t}}},function(t,e,r){"use strict";r.d(e,"c",(function(){return i})),r.d(e,"a",(function(){return o})),r.d(e,"b",(function(){return s}));var n=r(1);function i(t){return n.c.includes(t)}function o(t){return Object.keys(n.f).includes(t)?n.f[t]:n.f[n.a]}function s(t){const e=Object.values(n.f).find(e=>e.code===t);return e||n.f[n.a]}},function(t,e,r){"use strict";var n=r(12),i=Object.keys||function(t){var e=[];for(var r in t)e.push(r);return e};t.exports=c;var o=Object.create(r(9));o.inherits=r(6);var s=r(24),a=r(28);o.inherits(c,s);for(var u=i(a.prototype),h=0;h<u.length;h++){var f=u[h];c.prototype[f]||(c.prototype[f]=a.prototype[f])}function c(t){if(!(this instanceof c))return new c(t);s.call(this,t),a.call(this,t),t&&!1===t.readable&&(this.readable=!1),t&&!1===t.writable&&(this.writable=!1),this.allowHalfOpen=!0,t&&!1===t.allowHalfOpen&&(this.allowHalfOpen=!1),this.once("end",l)}function l(){this.allowHalfOpen||this._writableState.ended||n.nextTick(d,this)}function d(t){t.end()}Object.defineProperty(c.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),Object.defineProperty(c.prototype,"destroyed",{get:function(){return void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed&&this._writableState.destroyed)},set:function(t){void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed=t,this._writableState.destroyed=t)}}),c.prototype._destroy=function(t,e){this.push(null),this.end(),n.nextTick(e,t)}},function(t,e,r){(function(t){function r(t){return Object.prototype.toString.call(t)}e.isArray=function(t){return Array.isArray?Array.isArray(t):"[object Array]"===r(t)},e.isBoolean=function(t){return"boolean"==typeof t},e.isNull=function(t){return null===t},e.isNullOrUndefined=function(t){return null==t},e.isNumber=function(t){return"number"==typeof t},e.isString=function(t){return"string"==typeof t},e.isSymbol=function(t){return"symbol"==typeof t},e.isUndefined=function(t){return void 0===t},e.isRegExp=function(t){return"[object RegExp]"===r(t)},e.isObject=function(t){return"object"==typeof t&&null!==t},e.isDate=function(t){return"[object Date]"===r(t)},e.isError=function(t){return"[object Error]"===r(t)||t instanceof Error},e.isFunction=function(t){return"function"==typeof t},e.isPrimitive=function(t){return null===t||"boolean"==typeof t||"number"==typeof t||"string"==typeof t||"symbol"==typeof t||void 0===t},e.isBuffer=t.isBuffer}).call(this,r(5).Buffer)},function(t,e,r){"use strict";r.d(e,"a",(function(){return n}));class n{}},function(t,e,r){(function(t){!function(t,e){"use strict";function n(t,e){if(!t)throw new Error(e||"Assertion failed")}function i(t,e){t.super_=e;var r=function(){};r.prototype=e.prototype,t.prototype=new r,t.prototype.constructor=t}function o(t,e,r){if(o.isBN(t))return t;this.negative=0,this.words=null,this.length=0,this.red=null,null!==t&&("le"!==e&&"be"!==e||(r=e,e=10),this._init(t||0,e||10,r||"be"))}var s;"object"==typeof t?t.exports=o:e.BN=o,o.BN=o,o.wordSize=26;try{s=r(77).Buffer}catch(t){}function a(t,e,r){for(var n=0,i=Math.min(t.length,r),o=e;o<i;o++){var s=t.charCodeAt(o)-48;n<<=4,n|=s>=49&&s<=54?s-49+10:s>=17&&s<=22?s-17+10:15&s}return n}function u(t,e,r,n){for(var i=0,o=Math.min(t.length,r),s=e;s<o;s++){var a=t.charCodeAt(s)-48;i*=n,i+=a>=49?a-49+10:a>=17?a-17+10:a}return i}o.isBN=function(t){return t instanceof o||null!==t&&"object"==typeof t&&t.constructor.wordSize===o.wordSize&&Array.isArray(t.words)},o.max=function(t,e){return t.cmp(e)>0?t:e},o.min=function(t,e){return t.cmp(e)<0?t:e},o.prototype._init=function(t,e,r){if("number"==typeof t)return this._initNumber(t,e,r);if("object"==typeof t)return this._initArray(t,e,r);"hex"===e&&(e=16),n(e===(0|e)&&e>=2&&e<=36);var i=0;"-"===(t=t.toString().replace(/\s+/g,""))[0]&&i++,16===e?this._parseHex(t,i):this._parseBase(t,e,i),"-"===t[0]&&(this.negative=1),this.strip(),"le"===r&&this._initArray(this.toArray(),e,r)},o.prototype._initNumber=function(t,e,r){t<0&&(this.negative=1,t=-t),t<67108864?(this.words=[67108863&t],this.length=1):t<4503599627370496?(this.words=[67108863&t,t/67108864&67108863],this.length=2):(n(t<9007199254740992),this.words=[67108863&t,t/67108864&67108863,1],this.length=3),"le"===r&&this._initArray(this.toArray(),e,r)},o.prototype._initArray=function(t,e,r){if(n("number"==typeof t.length),t.length<=0)return this.words=[0],this.length=1,this;this.length=Math.ceil(t.length/3),this.words=new Array(this.length);for(var i=0;i<this.length;i++)this.words[i]=0;var o,s,a=0;if("be"===r)for(i=t.length-1,o=0;i>=0;i-=3)s=t[i]|t[i-1]<<8|t[i-2]<<16,this.words[o]|=s<<a&67108863,this.words[o+1]=s>>>26-a&67108863,(a+=24)>=26&&(a-=26,o++);else if("le"===r)for(i=0,o=0;i<t.length;i+=3)s=t[i]|t[i+1]<<8|t[i+2]<<16,this.words[o]|=s<<a&67108863,this.words[o+1]=s>>>26-a&67108863,(a+=24)>=26&&(a-=26,o++);return this.strip()},o.prototype._parseHex=function(t,e){this.length=Math.ceil((t.length-e)/6),this.words=new Array(this.length);for(var r=0;r<this.length;r++)this.words[r]=0;var n,i,o=0;for(r=t.length-6,n=0;r>=e;r-=6)i=a(t,r,r+6),this.words[n]|=i<<o&67108863,this.words[n+1]|=i>>>26-o&4194303,(o+=24)>=26&&(o-=26,n++);r+6!==e&&(i=a(t,e,r+6),this.words[n]|=i<<o&67108863,this.words[n+1]|=i>>>26-o&4194303),this.strip()},o.prototype._parseBase=function(t,e,r){this.words=[0],this.length=1;for(var n=0,i=1;i<=67108863;i*=e)n++;n--,i=i/e|0;for(var o=t.length-r,s=o%n,a=Math.min(o,o-s)+r,h=0,f=r;f<a;f+=n)h=u(t,f,f+n,e),this.imuln(i),this.words[0]+h<67108864?this.words[0]+=h:this._iaddn(h);if(0!==s){var c=1;for(h=u(t,f,t.length,e),f=0;f<s;f++)c*=e;this.imuln(c),this.words[0]+h<67108864?this.words[0]+=h:this._iaddn(h)}},o.prototype.copy=function(t){t.words=new Array(this.length);for(var e=0;e<this.length;e++)t.words[e]=this.words[e];t.length=this.length,t.negative=this.negative,t.red=this.red},o.prototype.clone=function(){var t=new o(null);return this.copy(t),t},o.prototype._expand=function(t){for(;this.length<t;)this.words[this.length++]=0;return this},o.prototype.strip=function(){for(;this.length>1&&0===this.words[this.length-1];)this.length--;return this._normSign()},o.prototype._normSign=function(){return 1===this.length&&0===this.words[0]&&(this.negative=0),this},o.prototype.inspect=function(){return(this.red?"<BN-R: ":"<BN: ")+this.toString(16)+">"};var h=["","0","00","000","0000","00000","000000","0000000","00000000","000000000","0000000000","00000000000","000000000000","0000000000000","00000000000000","000000000000000","0000000000000000","00000000000000000","000000000000000000","0000000000000000000","00000000000000000000","000000000000000000000","0000000000000000000000","00000000000000000000000","000000000000000000000000","0000000000000000000000000"],f=[0,0,25,16,12,11,10,9,8,8,7,7,7,7,6,6,6,6,6,6,6,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5],c=[0,0,33554432,43046721,16777216,48828125,60466176,40353607,16777216,43046721,1e7,19487171,35831808,62748517,7529536,11390625,16777216,24137569,34012224,47045881,64e6,4084101,5153632,6436343,7962624,9765625,11881376,14348907,17210368,20511149,243e5,28629151,33554432,39135393,45435424,52521875,60466176];function l(t,e,r){r.negative=e.negative^t.negative;var n=t.length+e.length|0;r.length=n,n=n-1|0;var i=0|t.words[0],o=0|e.words[0],s=i*o,a=67108863&s,u=s/67108864|0;r.words[0]=a;for(var h=1;h<n;h++){for(var f=u>>>26,c=67108863&u,l=Math.min(h,e.length-1),d=Math.max(0,h-t.length+1);d<=l;d++){var p=h-d|0;f+=(s=(i=0|t.words[p])*(o=0|e.words[d])+c)/67108864|0,c=67108863&s}r.words[h]=0|c,u=0|f}return 0!==u?r.words[h]=0|u:r.length--,r.strip()}o.prototype.toString=function(t,e){var r;if(e=0|e||1,16===(t=t||10)||"hex"===t){r="";for(var i=0,o=0,s=0;s<this.length;s++){var a=this.words[s],u=(16777215&(a<<i|o)).toString(16);r=0!==(o=a>>>24-i&16777215)||s!==this.length-1?h[6-u.length]+u+r:u+r,(i+=2)>=26&&(i-=26,s--)}for(0!==o&&(r=o.toString(16)+r);r.length%e!=0;)r="0"+r;return 0!==this.negative&&(r="-"+r),r}if(t===(0|t)&&t>=2&&t<=36){var l=f[t],d=c[t];r="";var p=this.clone();for(p.negative=0;!p.isZero();){var m=p.modn(d).toString(t);r=(p=p.idivn(d)).isZero()?m+r:h[l-m.length]+m+r}for(this.isZero()&&(r="0"+r);r.length%e!=0;)r="0"+r;return 0!==this.negative&&(r="-"+r),r}n(!1,"Base should be between 2 and 36")},o.prototype.toNumber=function(){var t=this.words[0];return 2===this.length?t+=67108864*this.words[1]:3===this.length&&1===this.words[2]?t+=4503599627370496+67108864*this.words[1]:this.length>2&&n(!1,"Number can only safely store up to 53 bits"),0!==this.negative?-t:t},o.prototype.toJSON=function(){return this.toString(16)},o.prototype.toBuffer=function(t,e){return n(void 0!==s),this.toArrayLike(s,t,e)},o.prototype.toArray=function(t,e){return this.toArrayLike(Array,t,e)},o.prototype.toArrayLike=function(t,e,r){var i=this.byteLength(),o=r||Math.max(1,i);n(i<=o,"byte array longer than desired length"),n(o>0,"Requested array length <= 0"),this.strip();var s,a,u="le"===e,h=new t(o),f=this.clone();if(u){for(a=0;!f.isZero();a++)s=f.andln(255),f.iushrn(8),h[a]=s;for(;a<o;a++)h[a]=0}else{for(a=0;a<o-i;a++)h[a]=0;for(a=0;!f.isZero();a++)s=f.andln(255),f.iushrn(8),h[o-a-1]=s}return h},Math.clz32?o.prototype._countBits=function(t){return 32-Math.clz32(t)}:o.prototype._countBits=function(t){var e=t,r=0;return e>=4096&&(r+=13,e>>>=13),e>=64&&(r+=7,e>>>=7),e>=8&&(r+=4,e>>>=4),e>=2&&(r+=2,e>>>=2),r+e},o.prototype._zeroBits=function(t){if(0===t)return 26;var e=t,r=0;return 0==(8191&e)&&(r+=13,e>>>=13),0==(127&e)&&(r+=7,e>>>=7),0==(15&e)&&(r+=4,e>>>=4),0==(3&e)&&(r+=2,e>>>=2),0==(1&e)&&r++,r},o.prototype.bitLength=function(){var t=this.words[this.length-1],e=this._countBits(t);return 26*(this.length-1)+e},o.prototype.zeroBits=function(){if(this.isZero())return 0;for(var t=0,e=0;e<this.length;e++){var r=this._zeroBits(this.words[e]);if(t+=r,26!==r)break}return t},o.prototype.byteLength=function(){return Math.ceil(this.bitLength()/8)},o.prototype.toTwos=function(t){return 0!==this.negative?this.abs().inotn(t).iaddn(1):this.clone()},o.prototype.fromTwos=function(t){return this.testn(t-1)?this.notn(t).iaddn(1).ineg():this.clone()},o.prototype.isNeg=function(){return 0!==this.negative},o.prototype.neg=function(){return this.clone().ineg()},o.prototype.ineg=function(){return this.isZero()||(this.negative^=1),this},o.prototype.iuor=function(t){for(;this.length<t.length;)this.words[this.length++]=0;for(var e=0;e<t.length;e++)this.words[e]=this.words[e]|t.words[e];return this.strip()},o.prototype.ior=function(t){return n(0==(this.negative|t.negative)),this.iuor(t)},o.prototype.or=function(t){return this.length>t.length?this.clone().ior(t):t.clone().ior(this)},o.prototype.uor=function(t){return this.length>t.length?this.clone().iuor(t):t.clone().iuor(this)},o.prototype.iuand=function(t){var e;e=this.length>t.length?t:this;for(var r=0;r<e.length;r++)this.words[r]=this.words[r]&t.words[r];return this.length=e.length,this.strip()},o.prototype.iand=function(t){return n(0==(this.negative|t.negative)),this.iuand(t)},o.prototype.and=function(t){return this.length>t.length?this.clone().iand(t):t.clone().iand(this)},o.prototype.uand=function(t){return this.length>t.length?this.clone().iuand(t):t.clone().iuand(this)},o.prototype.iuxor=function(t){var e,r;this.length>t.length?(e=this,r=t):(e=t,r=this);for(var n=0;n<r.length;n++)this.words[n]=e.words[n]^r.words[n];if(this!==e)for(;n<e.length;n++)this.words[n]=e.words[n];return this.length=e.length,this.strip()},o.prototype.ixor=function(t){return n(0==(this.negative|t.negative)),this.iuxor(t)},o.prototype.xor=function(t){return this.length>t.length?this.clone().ixor(t):t.clone().ixor(this)},o.prototype.uxor=function(t){return this.length>t.length?this.clone().iuxor(t):t.clone().iuxor(this)},o.prototype.inotn=function(t){n("number"==typeof t&&t>=0);var e=0|Math.ceil(t/26),r=t%26;this._expand(e),r>0&&e--;for(var i=0;i<e;i++)this.words[i]=67108863&~this.words[i];return r>0&&(this.words[i]=~this.words[i]&67108863>>26-r),this.strip()},o.prototype.notn=function(t){return this.clone().inotn(t)},o.prototype.setn=function(t,e){n("number"==typeof t&&t>=0);var r=t/26|0,i=t%26;return this._expand(r+1),this.words[r]=e?this.words[r]|1<<i:this.words[r]&~(1<<i),this.strip()},o.prototype.iadd=function(t){var e,r,n;if(0!==this.negative&&0===t.negative)return this.negative=0,e=this.isub(t),this.negative^=1,this._normSign();if(0===this.negative&&0!==t.negative)return t.negative=0,e=this.isub(t),t.negative=1,e._normSign();this.length>t.length?(r=this,n=t):(r=t,n=this);for(var i=0,o=0;o<n.length;o++)e=(0|r.words[o])+(0|n.words[o])+i,this.words[o]=67108863&e,i=e>>>26;for(;0!==i&&o<r.length;o++)e=(0|r.words[o])+i,this.words[o]=67108863&e,i=e>>>26;if(this.length=r.length,0!==i)this.words[this.length]=i,this.length++;else if(r!==this)for(;o<r.length;o++)this.words[o]=r.words[o];return this},o.prototype.add=function(t){var e;return 0!==t.negative&&0===this.negative?(t.negative=0,e=this.sub(t),t.negative^=1,e):0===t.negative&&0!==this.negative?(this.negative=0,e=t.sub(this),this.negative=1,e):this.length>t.length?this.clone().iadd(t):t.clone().iadd(this)},o.prototype.isub=function(t){if(0!==t.negative){t.negative=0;var e=this.iadd(t);return t.negative=1,e._normSign()}if(0!==this.negative)return this.negative=0,this.iadd(t),this.negative=1,this._normSign();var r,n,i=this.cmp(t);if(0===i)return this.negative=0,this.length=1,this.words[0]=0,this;i>0?(r=this,n=t):(r=t,n=this);for(var o=0,s=0;s<n.length;s++)o=(e=(0|r.words[s])-(0|n.words[s])+o)>>26,this.words[s]=67108863&e;for(;0!==o&&s<r.length;s++)o=(e=(0|r.words[s])+o)>>26,this.words[s]=67108863&e;if(0===o&&s<r.length&&r!==this)for(;s<r.length;s++)this.words[s]=r.words[s];return this.length=Math.max(this.length,s),r!==this&&(this.negative=1),this.strip()},o.prototype.sub=function(t){return this.clone().isub(t)};var d=function(t,e,r){var n,i,o,s=t.words,a=e.words,u=r.words,h=0,f=0|s[0],c=8191&f,l=f>>>13,d=0|s[1],p=8191&d,m=d>>>13,g=0|s[2],y=8191&g,v=g>>>13,w=0|s[3],b=8191&w,_=w>>>13,M=0|s[4],S=8191&M,E=M>>>13,A=0|s[5],O=8191&A,x=A>>>13,T=0|s[6],R=8191&T,k=T>>>13,C=0|s[7],j=8191&C,L=C>>>13,P=0|s[8],B=8191&P,N=P>>>13,I=0|s[9],U=8191&I,H=I>>>13,D=0|a[0],q=8191&D,W=D>>>13,F=0|a[1],Y=8191&F,z=F>>>13,$=0|a[2],V=8191&$,X=$>>>13,Z=0|a[3],G=8191&Z,J=Z>>>13,K=0|a[4],Q=8191&K,tt=K>>>13,et=0|a[5],rt=8191&et,nt=et>>>13,it=0|a[6],ot=8191&it,st=it>>>13,at=0|a[7],ut=8191&at,ht=at>>>13,ft=0|a[8],ct=8191&ft,lt=ft>>>13,dt=0|a[9],pt=8191&dt,mt=dt>>>13;r.negative=t.negative^e.negative,r.length=19;var gt=(h+(n=Math.imul(c,q))|0)+((8191&(i=(i=Math.imul(c,W))+Math.imul(l,q)|0))<<13)|0;h=((o=Math.imul(l,W))+(i>>>13)|0)+(gt>>>26)|0,gt&=67108863,n=Math.imul(p,q),i=(i=Math.imul(p,W))+Math.imul(m,q)|0,o=Math.imul(m,W);var yt=(h+(n=n+Math.imul(c,Y)|0)|0)+((8191&(i=(i=i+Math.imul(c,z)|0)+Math.imul(l,Y)|0))<<13)|0;h=((o=o+Math.imul(l,z)|0)+(i>>>13)|0)+(yt>>>26)|0,yt&=67108863,n=Math.imul(y,q),i=(i=Math.imul(y,W))+Math.imul(v,q)|0,o=Math.imul(v,W),n=n+Math.imul(p,Y)|0,i=(i=i+Math.imul(p,z)|0)+Math.imul(m,Y)|0,o=o+Math.imul(m,z)|0;var vt=(h+(n=n+Math.imul(c,V)|0)|0)+((8191&(i=(i=i+Math.imul(c,X)|0)+Math.imul(l,V)|0))<<13)|0;h=((o=o+Math.imul(l,X)|0)+(i>>>13)|0)+(vt>>>26)|0,vt&=67108863,n=Math.imul(b,q),i=(i=Math.imul(b,W))+Math.imul(_,q)|0,o=Math.imul(_,W),n=n+Math.imul(y,Y)|0,i=(i=i+Math.imul(y,z)|0)+Math.imul(v,Y)|0,o=o+Math.imul(v,z)|0,n=n+Math.imul(p,V)|0,i=(i=i+Math.imul(p,X)|0)+Math.imul(m,V)|0,o=o+Math.imul(m,X)|0;var wt=(h+(n=n+Math.imul(c,G)|0)|0)+((8191&(i=(i=i+Math.imul(c,J)|0)+Math.imul(l,G)|0))<<13)|0;h=((o=o+Math.imul(l,J)|0)+(i>>>13)|0)+(wt>>>26)|0,wt&=67108863,n=Math.imul(S,q),i=(i=Math.imul(S,W))+Math.imul(E,q)|0,o=Math.imul(E,W),n=n+Math.imul(b,Y)|0,i=(i=i+Math.imul(b,z)|0)+Math.imul(_,Y)|0,o=o+Math.imul(_,z)|0,n=n+Math.imul(y,V)|0,i=(i=i+Math.imul(y,X)|0)+Math.imul(v,V)|0,o=o+Math.imul(v,X)|0,n=n+Math.imul(p,G)|0,i=(i=i+Math.imul(p,J)|0)+Math.imul(m,G)|0,o=o+Math.imul(m,J)|0;var bt=(h+(n=n+Math.imul(c,Q)|0)|0)+((8191&(i=(i=i+Math.imul(c,tt)|0)+Math.imul(l,Q)|0))<<13)|0;h=((o=o+Math.imul(l,tt)|0)+(i>>>13)|0)+(bt>>>26)|0,bt&=67108863,n=Math.imul(O,q),i=(i=Math.imul(O,W))+Math.imul(x,q)|0,o=Math.imul(x,W),n=n+Math.imul(S,Y)|0,i=(i=i+Math.imul(S,z)|0)+Math.imul(E,Y)|0,o=o+Math.imul(E,z)|0,n=n+Math.imul(b,V)|0,i=(i=i+Math.imul(b,X)|0)+Math.imul(_,V)|0,o=o+Math.imul(_,X)|0,n=n+Math.imul(y,G)|0,i=(i=i+Math.imul(y,J)|0)+Math.imul(v,G)|0,o=o+Math.imul(v,J)|0,n=n+Math.imul(p,Q)|0,i=(i=i+Math.imul(p,tt)|0)+Math.imul(m,Q)|0,o=o+Math.imul(m,tt)|0;var _t=(h+(n=n+Math.imul(c,rt)|0)|0)+((8191&(i=(i=i+Math.imul(c,nt)|0)+Math.imul(l,rt)|0))<<13)|0;h=((o=o+Math.imul(l,nt)|0)+(i>>>13)|0)+(_t>>>26)|0,_t&=67108863,n=Math.imul(R,q),i=(i=Math.imul(R,W))+Math.imul(k,q)|0,o=Math.imul(k,W),n=n+Math.imul(O,Y)|0,i=(i=i+Math.imul(O,z)|0)+Math.imul(x,Y)|0,o=o+Math.imul(x,z)|0,n=n+Math.imul(S,V)|0,i=(i=i+Math.imul(S,X)|0)+Math.imul(E,V)|0,o=o+Math.imul(E,X)|0,n=n+Math.imul(b,G)|0,i=(i=i+Math.imul(b,J)|0)+Math.imul(_,G)|0,o=o+Math.imul(_,J)|0,n=n+Math.imul(y,Q)|0,i=(i=i+Math.imul(y,tt)|0)+Math.imul(v,Q)|0,o=o+Math.imul(v,tt)|0,n=n+Math.imul(p,rt)|0,i=(i=i+Math.imul(p,nt)|0)+Math.imul(m,rt)|0,o=o+Math.imul(m,nt)|0;var Mt=(h+(n=n+Math.imul(c,ot)|0)|0)+((8191&(i=(i=i+Math.imul(c,st)|0)+Math.imul(l,ot)|0))<<13)|0;h=((o=o+Math.imul(l,st)|0)+(i>>>13)|0)+(Mt>>>26)|0,Mt&=67108863,n=Math.imul(j,q),i=(i=Math.imul(j,W))+Math.imul(L,q)|0,o=Math.imul(L,W),n=n+Math.imul(R,Y)|0,i=(i=i+Math.imul(R,z)|0)+Math.imul(k,Y)|0,o=o+Math.imul(k,z)|0,n=n+Math.imul(O,V)|0,i=(i=i+Math.imul(O,X)|0)+Math.imul(x,V)|0,o=o+Math.imul(x,X)|0,n=n+Math.imul(S,G)|0,i=(i=i+Math.imul(S,J)|0)+Math.imul(E,G)|0,o=o+Math.imul(E,J)|0,n=n+Math.imul(b,Q)|0,i=(i=i+Math.imul(b,tt)|0)+Math.imul(_,Q)|0,o=o+Math.imul(_,tt)|0,n=n+Math.imul(y,rt)|0,i=(i=i+Math.imul(y,nt)|0)+Math.imul(v,rt)|0,o=o+Math.imul(v,nt)|0,n=n+Math.imul(p,ot)|0,i=(i=i+Math.imul(p,st)|0)+Math.imul(m,ot)|0,o=o+Math.imul(m,st)|0;var St=(h+(n=n+Math.imul(c,ut)|0)|0)+((8191&(i=(i=i+Math.imul(c,ht)|0)+Math.imul(l,ut)|0))<<13)|0;h=((o=o+Math.imul(l,ht)|0)+(i>>>13)|0)+(St>>>26)|0,St&=67108863,n=Math.imul(B,q),i=(i=Math.imul(B,W))+Math.imul(N,q)|0,o=Math.imul(N,W),n=n+Math.imul(j,Y)|0,i=(i=i+Math.imul(j,z)|0)+Math.imul(L,Y)|0,o=o+Math.imul(L,z)|0,n=n+Math.imul(R,V)|0,i=(i=i+Math.imul(R,X)|0)+Math.imul(k,V)|0,o=o+Math.imul(k,X)|0,n=n+Math.imul(O,G)|0,i=(i=i+Math.imul(O,J)|0)+Math.imul(x,G)|0,o=o+Math.imul(x,J)|0,n=n+Math.imul(S,Q)|0,i=(i=i+Math.imul(S,tt)|0)+Math.imul(E,Q)|0,o=o+Math.imul(E,tt)|0,n=n+Math.imul(b,rt)|0,i=(i=i+Math.imul(b,nt)|0)+Math.imul(_,rt)|0,o=o+Math.imul(_,nt)|0,n=n+Math.imul(y,ot)|0,i=(i=i+Math.imul(y,st)|0)+Math.imul(v,ot)|0,o=o+Math.imul(v,st)|0,n=n+Math.imul(p,ut)|0,i=(i=i+Math.imul(p,ht)|0)+Math.imul(m,ut)|0,o=o+Math.imul(m,ht)|0;var Et=(h+(n=n+Math.imul(c,ct)|0)|0)+((8191&(i=(i=i+Math.imul(c,lt)|0)+Math.imul(l,ct)|0))<<13)|0;h=((o=o+Math.imul(l,lt)|0)+(i>>>13)|0)+(Et>>>26)|0,Et&=67108863,n=Math.imul(U,q),i=(i=Math.imul(U,W))+Math.imul(H,q)|0,o=Math.imul(H,W),n=n+Math.imul(B,Y)|0,i=(i=i+Math.imul(B,z)|0)+Math.imul(N,Y)|0,o=o+Math.imul(N,z)|0,n=n+Math.imul(j,V)|0,i=(i=i+Math.imul(j,X)|0)+Math.imul(L,V)|0,o=o+Math.imul(L,X)|0,n=n+Math.imul(R,G)|0,i=(i=i+Math.imul(R,J)|0)+Math.imul(k,G)|0,o=o+Math.imul(k,J)|0,n=n+Math.imul(O,Q)|0,i=(i=i+Math.imul(O,tt)|0)+Math.imul(x,Q)|0,o=o+Math.imul(x,tt)|0,n=n+Math.imul(S,rt)|0,i=(i=i+Math.imul(S,nt)|0)+Math.imul(E,rt)|0,o=o+Math.imul(E,nt)|0,n=n+Math.imul(b,ot)|0,i=(i=i+Math.imul(b,st)|0)+Math.imul(_,ot)|0,o=o+Math.imul(_,st)|0,n=n+Math.imul(y,ut)|0,i=(i=i+Math.imul(y,ht)|0)+Math.imul(v,ut)|0,o=o+Math.imul(v,ht)|0,n=n+Math.imul(p,ct)|0,i=(i=i+Math.imul(p,lt)|0)+Math.imul(m,ct)|0,o=o+Math.imul(m,lt)|0;var At=(h+(n=n+Math.imul(c,pt)|0)|0)+((8191&(i=(i=i+Math.imul(c,mt)|0)+Math.imul(l,pt)|0))<<13)|0;h=((o=o+Math.imul(l,mt)|0)+(i>>>13)|0)+(At>>>26)|0,At&=67108863,n=Math.imul(U,Y),i=(i=Math.imul(U,z))+Math.imul(H,Y)|0,o=Math.imul(H,z),n=n+Math.imul(B,V)|0,i=(i=i+Math.imul(B,X)|0)+Math.imul(N,V)|0,o=o+Math.imul(N,X)|0,n=n+Math.imul(j,G)|0,i=(i=i+Math.imul(j,J)|0)+Math.imul(L,G)|0,o=o+Math.imul(L,J)|0,n=n+Math.imul(R,Q)|0,i=(i=i+Math.imul(R,tt)|0)+Math.imul(k,Q)|0,o=o+Math.imul(k,tt)|0,n=n+Math.imul(O,rt)|0,i=(i=i+Math.imul(O,nt)|0)+Math.imul(x,rt)|0,o=o+Math.imul(x,nt)|0,n=n+Math.imul(S,ot)|0,i=(i=i+Math.imul(S,st)|0)+Math.imul(E,ot)|0,o=o+Math.imul(E,st)|0,n=n+Math.imul(b,ut)|0,i=(i=i+Math.imul(b,ht)|0)+Math.imul(_,ut)|0,o=o+Math.imul(_,ht)|0,n=n+Math.imul(y,ct)|0,i=(i=i+Math.imul(y,lt)|0)+Math.imul(v,ct)|0,o=o+Math.imul(v,lt)|0;var Ot=(h+(n=n+Math.imul(p,pt)|0)|0)+((8191&(i=(i=i+Math.imul(p,mt)|0)+Math.imul(m,pt)|0))<<13)|0;h=((o=o+Math.imul(m,mt)|0)+(i>>>13)|0)+(Ot>>>26)|0,Ot&=67108863,n=Math.imul(U,V),i=(i=Math.imul(U,X))+Math.imul(H,V)|0,o=Math.imul(H,X),n=n+Math.imul(B,G)|0,i=(i=i+Math.imul(B,J)|0)+Math.imul(N,G)|0,o=o+Math.imul(N,J)|0,n=n+Math.imul(j,Q)|0,i=(i=i+Math.imul(j,tt)|0)+Math.imul(L,Q)|0,o=o+Math.imul(L,tt)|0,n=n+Math.imul(R,rt)|0,i=(i=i+Math.imul(R,nt)|0)+Math.imul(k,rt)|0,o=o+Math.imul(k,nt)|0,n=n+Math.imul(O,ot)|0,i=(i=i+Math.imul(O,st)|0)+Math.imul(x,ot)|0,o=o+Math.imul(x,st)|0,n=n+Math.imul(S,ut)|0,i=(i=i+Math.imul(S,ht)|0)+Math.imul(E,ut)|0,o=o+Math.imul(E,ht)|0,n=n+Math.imul(b,ct)|0,i=(i=i+Math.imul(b,lt)|0)+Math.imul(_,ct)|0,o=o+Math.imul(_,lt)|0;var xt=(h+(n=n+Math.imul(y,pt)|0)|0)+((8191&(i=(i=i+Math.imul(y,mt)|0)+Math.imul(v,pt)|0))<<13)|0;h=((o=o+Math.imul(v,mt)|0)+(i>>>13)|0)+(xt>>>26)|0,xt&=67108863,n=Math.imul(U,G),i=(i=Math.imul(U,J))+Math.imul(H,G)|0,o=Math.imul(H,J),n=n+Math.imul(B,Q)|0,i=(i=i+Math.imul(B,tt)|0)+Math.imul(N,Q)|0,o=o+Math.imul(N,tt)|0,n=n+Math.imul(j,rt)|0,i=(i=i+Math.imul(j,nt)|0)+Math.imul(L,rt)|0,o=o+Math.imul(L,nt)|0,n=n+Math.imul(R,ot)|0,i=(i=i+Math.imul(R,st)|0)+Math.imul(k,ot)|0,o=o+Math.imul(k,st)|0,n=n+Math.imul(O,ut)|0,i=(i=i+Math.imul(O,ht)|0)+Math.imul(x,ut)|0,o=o+Math.imul(x,ht)|0,n=n+Math.imul(S,ct)|0,i=(i=i+Math.imul(S,lt)|0)+Math.imul(E,ct)|0,o=o+Math.imul(E,lt)|0;var Tt=(h+(n=n+Math.imul(b,pt)|0)|0)+((8191&(i=(i=i+Math.imul(b,mt)|0)+Math.imul(_,pt)|0))<<13)|0;h=((o=o+Math.imul(_,mt)|0)+(i>>>13)|0)+(Tt>>>26)|0,Tt&=67108863,n=Math.imul(U,Q),i=(i=Math.imul(U,tt))+Math.imul(H,Q)|0,o=Math.imul(H,tt),n=n+Math.imul(B,rt)|0,i=(i=i+Math.imul(B,nt)|0)+Math.imul(N,rt)|0,o=o+Math.imul(N,nt)|0,n=n+Math.imul(j,ot)|0,i=(i=i+Math.imul(j,st)|0)+Math.imul(L,ot)|0,o=o+Math.imul(L,st)|0,n=n+Math.imul(R,ut)|0,i=(i=i+Math.imul(R,ht)|0)+Math.imul(k,ut)|0,o=o+Math.imul(k,ht)|0,n=n+Math.imul(O,ct)|0,i=(i=i+Math.imul(O,lt)|0)+Math.imul(x,ct)|0,o=o+Math.imul(x,lt)|0;var Rt=(h+(n=n+Math.imul(S,pt)|0)|0)+((8191&(i=(i=i+Math.imul(S,mt)|0)+Math.imul(E,pt)|0))<<13)|0;h=((o=o+Math.imul(E,mt)|0)+(i>>>13)|0)+(Rt>>>26)|0,Rt&=67108863,n=Math.imul(U,rt),i=(i=Math.imul(U,nt))+Math.imul(H,rt)|0,o=Math.imul(H,nt),n=n+Math.imul(B,ot)|0,i=(i=i+Math.imul(B,st)|0)+Math.imul(N,ot)|0,o=o+Math.imul(N,st)|0,n=n+Math.imul(j,ut)|0,i=(i=i+Math.imul(j,ht)|0)+Math.imul(L,ut)|0,o=o+Math.imul(L,ht)|0,n=n+Math.imul(R,ct)|0,i=(i=i+Math.imul(R,lt)|0)+Math.imul(k,ct)|0,o=o+Math.imul(k,lt)|0;var kt=(h+(n=n+Math.imul(O,pt)|0)|0)+((8191&(i=(i=i+Math.imul(O,mt)|0)+Math.imul(x,pt)|0))<<13)|0;h=((o=o+Math.imul(x,mt)|0)+(i>>>13)|0)+(kt>>>26)|0,kt&=67108863,n=Math.imul(U,ot),i=(i=Math.imul(U,st))+Math.imul(H,ot)|0,o=Math.imul(H,st),n=n+Math.imul(B,ut)|0,i=(i=i+Math.imul(B,ht)|0)+Math.imul(N,ut)|0,o=o+Math.imul(N,ht)|0,n=n+Math.imul(j,ct)|0,i=(i=i+Math.imul(j,lt)|0)+Math.imul(L,ct)|0,o=o+Math.imul(L,lt)|0;var Ct=(h+(n=n+Math.imul(R,pt)|0)|0)+((8191&(i=(i=i+Math.imul(R,mt)|0)+Math.imul(k,pt)|0))<<13)|0;h=((o=o+Math.imul(k,mt)|0)+(i>>>13)|0)+(Ct>>>26)|0,Ct&=67108863,n=Math.imul(U,ut),i=(i=Math.imul(U,ht))+Math.imul(H,ut)|0,o=Math.imul(H,ht),n=n+Math.imul(B,ct)|0,i=(i=i+Math.imul(B,lt)|0)+Math.imul(N,ct)|0,o=o+Math.imul(N,lt)|0;var jt=(h+(n=n+Math.imul(j,pt)|0)|0)+((8191&(i=(i=i+Math.imul(j,mt)|0)+Math.imul(L,pt)|0))<<13)|0;h=((o=o+Math.imul(L,mt)|0)+(i>>>13)|0)+(jt>>>26)|0,jt&=67108863,n=Math.imul(U,ct),i=(i=Math.imul(U,lt))+Math.imul(H,ct)|0,o=Math.imul(H,lt);var Lt=(h+(n=n+Math.imul(B,pt)|0)|0)+((8191&(i=(i=i+Math.imul(B,mt)|0)+Math.imul(N,pt)|0))<<13)|0;h=((o=o+Math.imul(N,mt)|0)+(i>>>13)|0)+(Lt>>>26)|0,Lt&=67108863;var Pt=(h+(n=Math.imul(U,pt))|0)+((8191&(i=(i=Math.imul(U,mt))+Math.imul(H,pt)|0))<<13)|0;return h=((o=Math.imul(H,mt))+(i>>>13)|0)+(Pt>>>26)|0,Pt&=67108863,u[0]=gt,u[1]=yt,u[2]=vt,u[3]=wt,u[4]=bt,u[5]=_t,u[6]=Mt,u[7]=St,u[8]=Et,u[9]=At,u[10]=Ot,u[11]=xt,u[12]=Tt,u[13]=Rt,u[14]=kt,u[15]=Ct,u[16]=jt,u[17]=Lt,u[18]=Pt,0!==h&&(u[19]=h,r.length++),r};function p(t,e,r){return(new m).mulp(t,e,r)}function m(t,e){this.x=t,this.y=e}Math.imul||(d=l),o.prototype.mulTo=function(t,e){var r=this.length+t.length;return 10===this.length&&10===t.length?d(this,t,e):r<63?l(this,t,e):r<1024?function(t,e,r){r.negative=e.negative^t.negative,r.length=t.length+e.length;for(var n=0,i=0,o=0;o<r.length-1;o++){var s=i;i=0;for(var a=67108863&n,u=Math.min(o,e.length-1),h=Math.max(0,o-t.length+1);h<=u;h++){var f=o-h,c=(0|t.words[f])*(0|e.words[h]),l=67108863&c;a=67108863&(l=l+a|0),i+=(s=(s=s+(c/67108864|0)|0)+(l>>>26)|0)>>>26,s&=67108863}r.words[o]=a,n=s,s=i}return 0!==n?r.words[o]=n:r.length--,r.strip()}(this,t,e):p(this,t,e)},m.prototype.makeRBT=function(t){for(var e=new Array(t),r=o.prototype._countBits(t)-1,n=0;n<t;n++)e[n]=this.revBin(n,r,t);return e},m.prototype.revBin=function(t,e,r){if(0===t||t===r-1)return t;for(var n=0,i=0;i<e;i++)n|=(1&t)<<e-i-1,t>>=1;return n},m.prototype.permute=function(t,e,r,n,i,o){for(var s=0;s<o;s++)n[s]=e[t[s]],i[s]=r[t[s]]},m.prototype.transform=function(t,e,r,n,i,o){this.permute(o,t,e,r,n,i);for(var s=1;s<i;s<<=1)for(var a=s<<1,u=Math.cos(2*Math.PI/a),h=Math.sin(2*Math.PI/a),f=0;f<i;f+=a)for(var c=u,l=h,d=0;d<s;d++){var p=r[f+d],m=n[f+d],g=r[f+d+s],y=n[f+d+s],v=c*g-l*y;y=c*y+l*g,g=v,r[f+d]=p+g,n[f+d]=m+y,r[f+d+s]=p-g,n[f+d+s]=m-y,d!==a&&(v=u*c-h*l,l=u*l+h*c,c=v)}},m.prototype.guessLen13b=function(t,e){var r=1|Math.max(e,t),n=1&r,i=0;for(r=r/2|0;r;r>>>=1)i++;return 1<<i+1+n},m.prototype.conjugate=function(t,e,r){if(!(r<=1))for(var n=0;n<r/2;n++){var i=t[n];t[n]=t[r-n-1],t[r-n-1]=i,i=e[n],e[n]=-e[r-n-1],e[r-n-1]=-i}},m.prototype.normalize13b=function(t,e){for(var r=0,n=0;n<e/2;n++){var i=8192*Math.round(t[2*n+1]/e)+Math.round(t[2*n]/e)+r;t[n]=67108863&i,r=i<67108864?0:i/67108864|0}return t},m.prototype.convert13b=function(t,e,r,i){for(var o=0,s=0;s<e;s++)o+=0|t[s],r[2*s]=8191&o,o>>>=13,r[2*s+1]=8191&o,o>>>=13;for(s=2*e;s<i;++s)r[s]=0;n(0===o),n(0==(-8192&o))},m.prototype.stub=function(t){for(var e=new Array(t),r=0;r<t;r++)e[r]=0;return e},m.prototype.mulp=function(t,e,r){var n=2*this.guessLen13b(t.length,e.length),i=this.makeRBT(n),o=this.stub(n),s=new Array(n),a=new Array(n),u=new Array(n),h=new Array(n),f=new Array(n),c=new Array(n),l=r.words;l.length=n,this.convert13b(t.words,t.length,s,n),this.convert13b(e.words,e.length,h,n),this.transform(s,o,a,u,n,i),this.transform(h,o,f,c,n,i);for(var d=0;d<n;d++){var p=a[d]*f[d]-u[d]*c[d];u[d]=a[d]*c[d]+u[d]*f[d],a[d]=p}return this.conjugate(a,u,n),this.transform(a,u,l,o,n,i),this.conjugate(l,o,n),this.normalize13b(l,n),r.negative=t.negative^e.negative,r.length=t.length+e.length,r.strip()},o.prototype.mul=function(t){var e=new o(null);return e.words=new Array(this.length+t.length),this.mulTo(t,e)},o.prototype.mulf=function(t){var e=new o(null);return e.words=new Array(this.length+t.length),p(this,t,e)},o.prototype.imul=function(t){return this.clone().mulTo(t,this)},o.prototype.imuln=function(t){n("number"==typeof t),n(t<67108864);for(var e=0,r=0;r<this.length;r++){var i=(0|this.words[r])*t,o=(67108863&i)+(67108863&e);e>>=26,e+=i/67108864|0,e+=o>>>26,this.words[r]=67108863&o}return 0!==e&&(this.words[r]=e,this.length++),this},o.prototype.muln=function(t){return this.clone().imuln(t)},o.prototype.sqr=function(){return this.mul(this)},o.prototype.isqr=function(){return this.imul(this.clone())},o.prototype.pow=function(t){var e=function(t){for(var e=new Array(t.bitLength()),r=0;r<e.length;r++){var n=r/26|0,i=r%26;e[r]=(t.words[n]&1<<i)>>>i}return e}(t);if(0===e.length)return new o(1);for(var r=this,n=0;n<e.length&&0===e[n];n++,r=r.sqr());if(++n<e.length)for(var i=r.sqr();n<e.length;n++,i=i.sqr())0!==e[n]&&(r=r.mul(i));return r},o.prototype.iushln=function(t){n("number"==typeof t&&t>=0);var e,r=t%26,i=(t-r)/26,o=67108863>>>26-r<<26-r;if(0!==r){var s=0;for(e=0;e<this.length;e++){var a=this.words[e]&o,u=(0|this.words[e])-a<<r;this.words[e]=u|s,s=a>>>26-r}s&&(this.words[e]=s,this.length++)}if(0!==i){for(e=this.length-1;e>=0;e--)this.words[e+i]=this.words[e];for(e=0;e<i;e++)this.words[e]=0;this.length+=i}return this.strip()},o.prototype.ishln=function(t){return n(0===this.negative),this.iushln(t)},o.prototype.iushrn=function(t,e,r){var i;n("number"==typeof t&&t>=0),i=e?(e-e%26)/26:0;var o=t%26,s=Math.min((t-o)/26,this.length),a=67108863^67108863>>>o<<o,u=r;if(i-=s,i=Math.max(0,i),u){for(var h=0;h<s;h++)u.words[h]=this.words[h];u.length=s}if(0===s);else if(this.length>s)for(this.length-=s,h=0;h<this.length;h++)this.words[h]=this.words[h+s];else this.words[0]=0,this.length=1;var f=0;for(h=this.length-1;h>=0&&(0!==f||h>=i);h--){var c=0|this.words[h];this.words[h]=f<<26-o|c>>>o,f=c&a}return u&&0!==f&&(u.words[u.length++]=f),0===this.length&&(this.words[0]=0,this.length=1),this.strip()},o.prototype.ishrn=function(t,e,r){return n(0===this.negative),this.iushrn(t,e,r)},o.prototype.shln=function(t){return this.clone().ishln(t)},o.prototype.ushln=function(t){return this.clone().iushln(t)},o.prototype.shrn=function(t){return this.clone().ishrn(t)},o.prototype.ushrn=function(t){return this.clone().iushrn(t)},o.prototype.testn=function(t){n("number"==typeof t&&t>=0);var e=t%26,r=(t-e)/26,i=1<<e;return!(this.length<=r)&&!!(this.words[r]&i)},o.prototype.imaskn=function(t){n("number"==typeof t&&t>=0);var e=t%26,r=(t-e)/26;if(n(0===this.negative,"imaskn works only with positive numbers"),this.length<=r)return this;if(0!==e&&r++,this.length=Math.min(r,this.length),0!==e){var i=67108863^67108863>>>e<<e;this.words[this.length-1]&=i}return this.strip()},o.prototype.maskn=function(t){return this.clone().imaskn(t)},o.prototype.iaddn=function(t){return n("number"==typeof t),n(t<67108864),t<0?this.isubn(-t):0!==this.negative?1===this.length&&(0|this.words[0])<t?(this.words[0]=t-(0|this.words[0]),this.negative=0,this):(this.negative=0,this.isubn(t),this.negative=1,this):this._iaddn(t)},o.prototype._iaddn=function(t){this.words[0]+=t;for(var e=0;e<this.length&&this.words[e]>=67108864;e++)this.words[e]-=67108864,e===this.length-1?this.words[e+1]=1:this.words[e+1]++;return this.length=Math.max(this.length,e+1),this},o.prototype.isubn=function(t){if(n("number"==typeof t),n(t<67108864),t<0)return this.iaddn(-t);if(0!==this.negative)return this.negative=0,this.iaddn(t),this.negative=1,this;if(this.words[0]-=t,1===this.length&&this.words[0]<0)this.words[0]=-this.words[0],this.negative=1;else for(var e=0;e<this.length&&this.words[e]<0;e++)this.words[e]+=67108864,this.words[e+1]-=1;return this.strip()},o.prototype.addn=function(t){return this.clone().iaddn(t)},o.prototype.subn=function(t){return this.clone().isubn(t)},o.prototype.iabs=function(){return this.negative=0,this},o.prototype.abs=function(){return this.clone().iabs()},o.prototype._ishlnsubmul=function(t,e,r){var i,o,s=t.length+r;this._expand(s);var a=0;for(i=0;i<t.length;i++){o=(0|this.words[i+r])+a;var u=(0|t.words[i])*e;a=((o-=67108863&u)>>26)-(u/67108864|0),this.words[i+r]=67108863&o}for(;i<this.length-r;i++)a=(o=(0|this.words[i+r])+a)>>26,this.words[i+r]=67108863&o;if(0===a)return this.strip();for(n(-1===a),a=0,i=0;i<this.length;i++)a=(o=-(0|this.words[i])+a)>>26,this.words[i]=67108863&o;return this.negative=1,this.strip()},o.prototype._wordDiv=function(t,e){var r=(this.length,t.length),n=this.clone(),i=t,s=0|i.words[i.length-1];0!==(r=26-this._countBits(s))&&(i=i.ushln(r),n.iushln(r),s=0|i.words[i.length-1]);var a,u=n.length-i.length;if("mod"!==e){(a=new o(null)).length=u+1,a.words=new Array(a.length);for(var h=0;h<a.length;h++)a.words[h]=0}var f=n.clone()._ishlnsubmul(i,1,u);0===f.negative&&(n=f,a&&(a.words[u]=1));for(var c=u-1;c>=0;c--){var l=67108864*(0|n.words[i.length+c])+(0|n.words[i.length+c-1]);for(l=Math.min(l/s|0,67108863),n._ishlnsubmul(i,l,c);0!==n.negative;)l--,n.negative=0,n._ishlnsubmul(i,1,c),n.isZero()||(n.negative^=1);a&&(a.words[c]=l)}return a&&a.strip(),n.strip(),"div"!==e&&0!==r&&n.iushrn(r),{div:a||null,mod:n}},o.prototype.divmod=function(t,e,r){return n(!t.isZero()),this.isZero()?{div:new o(0),mod:new o(0)}:0!==this.negative&&0===t.negative?(a=this.neg().divmod(t,e),"mod"!==e&&(i=a.div.neg()),"div"!==e&&(s=a.mod.neg(),r&&0!==s.negative&&s.iadd(t)),{div:i,mod:s}):0===this.negative&&0!==t.negative?(a=this.divmod(t.neg(),e),"mod"!==e&&(i=a.div.neg()),{div:i,mod:a.mod}):0!=(this.negative&t.negative)?(a=this.neg().divmod(t.neg(),e),"div"!==e&&(s=a.mod.neg(),r&&0!==s.negative&&s.isub(t)),{div:a.div,mod:s}):t.length>this.length||this.cmp(t)<0?{div:new o(0),mod:this}:1===t.length?"div"===e?{div:this.divn(t.words[0]),mod:null}:"mod"===e?{div:null,mod:new o(this.modn(t.words[0]))}:{div:this.divn(t.words[0]),mod:new o(this.modn(t.words[0]))}:this._wordDiv(t,e);var i,s,a},o.prototype.div=function(t){return this.divmod(t,"div",!1).div},o.prototype.mod=function(t){return this.divmod(t,"mod",!1).mod},o.prototype.umod=function(t){return this.divmod(t,"mod",!0).mod},o.prototype.divRound=function(t){var e=this.divmod(t);if(e.mod.isZero())return e.div;var r=0!==e.div.negative?e.mod.isub(t):e.mod,n=t.ushrn(1),i=t.andln(1),o=r.cmp(n);return o<0||1===i&&0===o?e.div:0!==e.div.negative?e.div.isubn(1):e.div.iaddn(1)},o.prototype.modn=function(t){n(t<=67108863);for(var e=(1<<26)%t,r=0,i=this.length-1;i>=0;i--)r=(e*r+(0|this.words[i]))%t;return r},o.prototype.idivn=function(t){n(t<=67108863);for(var e=0,r=this.length-1;r>=0;r--){var i=(0|this.words[r])+67108864*e;this.words[r]=i/t|0,e=i%t}return this.strip()},o.prototype.divn=function(t){return this.clone().idivn(t)},o.prototype.egcd=function(t){n(0===t.negative),n(!t.isZero());var e=this,r=t.clone();e=0!==e.negative?e.umod(t):e.clone();for(var i=new o(1),s=new o(0),a=new o(0),u=new o(1),h=0;e.isEven()&&r.isEven();)e.iushrn(1),r.iushrn(1),++h;for(var f=r.clone(),c=e.clone();!e.isZero();){for(var l=0,d=1;0==(e.words[0]&d)&&l<26;++l,d<<=1);if(l>0)for(e.iushrn(l);l-- >0;)(i.isOdd()||s.isOdd())&&(i.iadd(f),s.isub(c)),i.iushrn(1),s.iushrn(1);for(var p=0,m=1;0==(r.words[0]&m)&&p<26;++p,m<<=1);if(p>0)for(r.iushrn(p);p-- >0;)(a.isOdd()||u.isOdd())&&(a.iadd(f),u.isub(c)),a.iushrn(1),u.iushrn(1);e.cmp(r)>=0?(e.isub(r),i.isub(a),s.isub(u)):(r.isub(e),a.isub(i),u.isub(s))}return{a:a,b:u,gcd:r.iushln(h)}},o.prototype._invmp=function(t){n(0===t.negative),n(!t.isZero());var e=this,r=t.clone();e=0!==e.negative?e.umod(t):e.clone();for(var i,s=new o(1),a=new o(0),u=r.clone();e.cmpn(1)>0&&r.cmpn(1)>0;){for(var h=0,f=1;0==(e.words[0]&f)&&h<26;++h,f<<=1);if(h>0)for(e.iushrn(h);h-- >0;)s.isOdd()&&s.iadd(u),s.iushrn(1);for(var c=0,l=1;0==(r.words[0]&l)&&c<26;++c,l<<=1);if(c>0)for(r.iushrn(c);c-- >0;)a.isOdd()&&a.iadd(u),a.iushrn(1);e.cmp(r)>=0?(e.isub(r),s.isub(a)):(r.isub(e),a.isub(s))}return(i=0===e.cmpn(1)?s:a).cmpn(0)<0&&i.iadd(t),i},o.prototype.gcd=function(t){if(this.isZero())return t.abs();if(t.isZero())return this.abs();var e=this.clone(),r=t.clone();e.negative=0,r.negative=0;for(var n=0;e.isEven()&&r.isEven();n++)e.iushrn(1),r.iushrn(1);for(;;){for(;e.isEven();)e.iushrn(1);for(;r.isEven();)r.iushrn(1);var i=e.cmp(r);if(i<0){var o=e;e=r,r=o}else if(0===i||0===r.cmpn(1))break;e.isub(r)}return r.iushln(n)},o.prototype.invm=function(t){return this.egcd(t).a.umod(t)},o.prototype.isEven=function(){return 0==(1&this.words[0])},o.prototype.isOdd=function(){return 1==(1&this.words[0])},o.prototype.andln=function(t){return this.words[0]&t},o.prototype.bincn=function(t){n("number"==typeof t);var e=t%26,r=(t-e)/26,i=1<<e;if(this.length<=r)return this._expand(r+1),this.words[r]|=i,this;for(var o=i,s=r;0!==o&&s<this.length;s++){var a=0|this.words[s];o=(a+=o)>>>26,a&=67108863,this.words[s]=a}return 0!==o&&(this.words[s]=o,this.length++),this},o.prototype.isZero=function(){return 1===this.length&&0===this.words[0]},o.prototype.cmpn=function(t){var e,r=t<0;if(0!==this.negative&&!r)return-1;if(0===this.negative&&r)return 1;if(this.strip(),this.length>1)e=1;else{r&&(t=-t),n(t<=67108863,"Number is too big");var i=0|this.words[0];e=i===t?0:i<t?-1:1}return 0!==this.negative?0|-e:e},o.prototype.cmp=function(t){if(0!==this.negative&&0===t.negative)return-1;if(0===this.negative&&0!==t.negative)return 1;var e=this.ucmp(t);return 0!==this.negative?0|-e:e},o.prototype.ucmp=function(t){if(this.length>t.length)return 1;if(this.length<t.length)return-1;for(var e=0,r=this.length-1;r>=0;r--){var n=0|this.words[r],i=0|t.words[r];if(n!==i){n<i?e=-1:n>i&&(e=1);break}}return e},o.prototype.gtn=function(t){return 1===this.cmpn(t)},o.prototype.gt=function(t){return 1===this.cmp(t)},o.prototype.gten=function(t){return this.cmpn(t)>=0},o.prototype.gte=function(t){return this.cmp(t)>=0},o.prototype.ltn=function(t){return-1===this.cmpn(t)},o.prototype.lt=function(t){return-1===this.cmp(t)},o.prototype.lten=function(t){return this.cmpn(t)<=0},o.prototype.lte=function(t){return this.cmp(t)<=0},o.prototype.eqn=function(t){return 0===this.cmpn(t)},o.prototype.eq=function(t){return 0===this.cmp(t)},o.red=function(t){return new M(t)},o.prototype.toRed=function(t){return n(!this.red,"Already a number in reduction context"),n(0===this.negative,"red works only with positives"),t.convertTo(this)._forceRed(t)},o.prototype.fromRed=function(){return n(this.red,"fromRed works only with numbers in reduction context"),this.red.convertFrom(this)},o.prototype._forceRed=function(t){return this.red=t,this},o.prototype.forceRed=function(t){return n(!this.red,"Already a number in reduction context"),this._forceRed(t)},o.prototype.redAdd=function(t){return n(this.red,"redAdd works only with red numbers"),this.red.add(this,t)},o.prototype.redIAdd=function(t){return n(this.red,"redIAdd works only with red numbers"),this.red.iadd(this,t)},o.prototype.redSub=function(t){return n(this.red,"redSub works only with red numbers"),this.red.sub(this,t)},o.prototype.redISub=function(t){return n(this.red,"redISub works only with red numbers"),this.red.isub(this,t)},o.prototype.redShl=function(t){return n(this.red,"redShl works only with red numbers"),this.red.shl(this,t)},o.prototype.redMul=function(t){return n(this.red,"redMul works only with red numbers"),this.red._verify2(this,t),this.red.mul(this,t)},o.prototype.redIMul=function(t){return n(this.red,"redMul works only with red numbers"),this.red._verify2(this,t),this.red.imul(this,t)},o.prototype.redSqr=function(){return n(this.red,"redSqr works only with red numbers"),this.red._verify1(this),this.red.sqr(this)},o.prototype.redISqr=function(){return n(this.red,"redISqr works only with red numbers"),this.red._verify1(this),this.red.isqr(this)},o.prototype.redSqrt=function(){return n(this.red,"redSqrt works only with red numbers"),this.red._verify1(this),this.red.sqrt(this)},o.prototype.redInvm=function(){return n(this.red,"redInvm works only with red numbers"),this.red._verify1(this),this.red.invm(this)},o.prototype.redNeg=function(){return n(this.red,"redNeg works only with red numbers"),this.red._verify1(this),this.red.neg(this)},o.prototype.redPow=function(t){return n(this.red&&!t.red,"redPow(normalNum)"),this.red._verify1(this),this.red.pow(this,t)};var g={k256:null,p224:null,p192:null,p25519:null};function y(t,e){this.name=t,this.p=new o(e,16),this.n=this.p.bitLength(),this.k=new o(1).iushln(this.n).isub(this.p),this.tmp=this._tmp()}function v(){y.call(this,"k256","ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffe fffffc2f")}function w(){y.call(this,"p224","ffffffff ffffffff ffffffff ffffffff 00000000 00000000 00000001")}function b(){y.call(this,"p192","ffffffff ffffffff ffffffff fffffffe ffffffff ffffffff")}function _(){y.call(this,"25519","7fffffffffffffff ffffffffffffffff ffffffffffffffff ffffffffffffffed")}function M(t){if("string"==typeof t){var e=o._prime(t);this.m=e.p,this.prime=e}else n(t.gtn(1),"modulus must be greater than 1"),this.m=t,this.prime=null}function S(t){M.call(this,t),this.shift=this.m.bitLength(),this.shift%26!=0&&(this.shift+=26-this.shift%26),this.r=new o(1).iushln(this.shift),this.r2=this.imod(this.r.sqr()),this.rinv=this.r._invmp(this.m),this.minv=this.rinv.mul(this.r).isubn(1).div(this.m),this.minv=this.minv.umod(this.r),this.minv=this.r.sub(this.minv)}y.prototype._tmp=function(){var t=new o(null);return t.words=new Array(Math.ceil(this.n/13)),t},y.prototype.ireduce=function(t){var e,r=t;do{this.split(r,this.tmp),e=(r=(r=this.imulK(r)).iadd(this.tmp)).bitLength()}while(e>this.n);var n=e<this.n?-1:r.ucmp(this.p);return 0===n?(r.words[0]=0,r.length=1):n>0?r.isub(this.p):r.strip(),r},y.prototype.split=function(t,e){t.iushrn(this.n,0,e)},y.prototype.imulK=function(t){return t.imul(this.k)},i(v,y),v.prototype.split=function(t,e){for(var r=Math.min(t.length,9),n=0;n<r;n++)e.words[n]=t.words[n];if(e.length=r,t.length<=9)return t.words[0]=0,void(t.length=1);var i=t.words[9];for(e.words[e.length++]=4194303&i,n=10;n<t.length;n++){var o=0|t.words[n];t.words[n-10]=(4194303&o)<<4|i>>>22,i=o}i>>>=22,t.words[n-10]=i,0===i&&t.length>10?t.length-=10:t.length-=9},v.prototype.imulK=function(t){t.words[t.length]=0,t.words[t.length+1]=0,t.length+=2;for(var e=0,r=0;r<t.length;r++){var n=0|t.words[r];e+=977*n,t.words[r]=67108863&e,e=64*n+(e/67108864|0)}return 0===t.words[t.length-1]&&(t.length--,0===t.words[t.length-1]&&t.length--),t},i(w,y),i(b,y),i(_,y),_.prototype.imulK=function(t){for(var e=0,r=0;r<t.length;r++){var n=19*(0|t.words[r])+e,i=67108863&n;n>>>=26,t.words[r]=i,e=n}return 0!==e&&(t.words[t.length++]=e),t},o._prime=function(t){if(g[t])return g[t];var e;if("k256"===t)e=new v;else if("p224"===t)e=new w;else if("p192"===t)e=new b;else{if("p25519"!==t)throw new Error("Unknown prime "+t);e=new _}return g[t]=e,e},M.prototype._verify1=function(t){n(0===t.negative,"red works only with positives"),n(t.red,"red works only with red numbers")},M.prototype._verify2=function(t,e){n(0==(t.negative|e.negative),"red works only with positives"),n(t.red&&t.red===e.red,"red works only with red numbers")},M.prototype.imod=function(t){return this.prime?this.prime.ireduce(t)._forceRed(this):t.umod(this.m)._forceRed(this)},M.prototype.neg=function(t){return t.isZero()?t.clone():this.m.sub(t)._forceRed(this)},M.prototype.add=function(t,e){this._verify2(t,e);var r=t.add(e);return r.cmp(this.m)>=0&&r.isub(this.m),r._forceRed(this)},M.prototype.iadd=function(t,e){this._verify2(t,e);var r=t.iadd(e);return r.cmp(this.m)>=0&&r.isub(this.m),r},M.prototype.sub=function(t,e){this._verify2(t,e);var r=t.sub(e);return r.cmpn(0)<0&&r.iadd(this.m),r._forceRed(this)},M.prototype.isub=function(t,e){this._verify2(t,e);var r=t.isub(e);return r.cmpn(0)<0&&r.iadd(this.m),r},M.prototype.shl=function(t,e){return this._verify1(t),this.imod(t.ushln(e))},M.prototype.imul=function(t,e){return this._verify2(t,e),this.imod(t.imul(e))},M.prototype.mul=function(t,e){return this._verify2(t,e),this.imod(t.mul(e))},M.prototype.isqr=function(t){return this.imul(t,t.clone())},M.prototype.sqr=function(t){return this.mul(t,t)},M.prototype.sqrt=function(t){if(t.isZero())return t.clone();var e=this.m.andln(3);if(n(e%2==1),3===e){var r=this.m.add(new o(1)).iushrn(2);return this.pow(t,r)}for(var i=this.m.subn(1),s=0;!i.isZero()&&0===i.andln(1);)s++,i.iushrn(1);n(!i.isZero());var a=new o(1).toRed(this),u=a.redNeg(),h=this.m.subn(1).iushrn(1),f=this.m.bitLength();for(f=new o(2*f*f).toRed(this);0!==this.pow(f,h).cmp(u);)f.redIAdd(u);for(var c=this.pow(f,i),l=this.pow(t,i.addn(1).iushrn(1)),d=this.pow(t,i),p=s;0!==d.cmp(a);){for(var m=d,g=0;0!==m.cmp(a);g++)m=m.redSqr();n(g<p);var y=this.pow(c,new o(1).iushln(p-g-1));l=l.redMul(y),c=y.redSqr(),d=d.redMul(c),p=g}return l},M.prototype.invm=function(t){var e=t._invmp(this.m);return 0!==e.negative?(e.negative=0,this.imod(e).redNeg()):this.imod(e)},M.prototype.pow=function(t,e){if(e.isZero())return new o(1).toRed(this);if(0===e.cmpn(1))return t.clone();var r=new Array(16);r[0]=new o(1).toRed(this),r[1]=t;for(var n=2;n<r.length;n++)r[n]=this.mul(r[n-1],t);var i=r[0],s=0,a=0,u=e.bitLength()%26;for(0===u&&(u=26),n=e.length-1;n>=0;n--){for(var h=e.words[n],f=u-1;f>=0;f--){var c=h>>f&1;i!==r[0]&&(i=this.sqr(i)),0!==c||0!==s?(s<<=1,s|=c,(4===++a||0===n&&0===f)&&(i=this.mul(i,r[s]),a=0,s=0)):a=0}u=26}return i},M.prototype.convertTo=function(t){var e=t.umod(this.m);return e===t?e.clone():e},M.prototype.convertFrom=function(t){var e=t.clone();return e.red=null,e},o.mont=function(t){return new S(t)},i(S,M),S.prototype.convertTo=function(t){return this.imod(t.ushln(this.shift))},S.prototype.convertFrom=function(t){var e=this.imod(t.mul(this.rinv));return e.red=null,e},S.prototype.imul=function(t,e){if(t.isZero()||e.isZero())return t.words[0]=0,t.length=1,t;var r=t.imul(e),n=r.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m),i=r.isub(n).iushrn(this.shift),o=i;return i.cmp(this.m)>=0?o=i.isub(this.m):i.cmpn(0)<0&&(o=i.iadd(this.m)),o._forceRed(this)},S.prototype.mul=function(t,e){if(t.isZero()||e.isZero())return new o(0)._forceRed(this);var r=t.mul(e),n=r.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m),i=r.isub(n).iushrn(this.shift),s=i;return i.cmp(this.m)>=0?s=i.isub(this.m):i.cmpn(0)<0&&(s=i.iadd(this.m)),s._forceRed(this)},S.prototype.invm=function(t){return this.imod(t._invmp(this.m).mul(this.r2))._forceRed(this)}}(t,this)}).call(this,r(31)(t))},function(t,e,r){"use strict";(function(e){void 0===e||!e.version||0===e.version.indexOf("v0.")||0===e.version.indexOf("v1.")&&0!==e.version.indexOf("v1.8.")?t.exports={nextTick:function(t,r,n,i){if("function"!=typeof t)throw new TypeError('"callback" argument must be a function');var o,s,a=arguments.length;switch(a){case 0:case 1:return e.nextTick(t);case 2:return e.nextTick((function(){t.call(null,r)}));case 3:return e.nextTick((function(){t.call(null,r,n)}));case 4:return e.nextTick((function(){t.call(null,r,n,i)}));default:for(o=new Array(a-1),s=0;s<o.length;)o[s++]=arguments[s];return e.nextTick((function(){t.apply(null,o)}))}}}:t.exports=e}).call(this,r(4))},function(t,e,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(t,e,r,n){void 0===n&&(n=r),Object.defineProperty(t,n,{enumerable:!0,get:function(){return e[r]}})}:function(t,e,r,n){void 0===n&&(n=r),t[n]=e[r]}),i=this&&this.__exportStar||function(t,e){for(var r in t)"default"===r||e.hasOwnProperty(r)||n(e,t,r)};Object.defineProperty(e,"__esModule",{value:!0}),i(r(79),e),i(r(80),e)},function(t,e,r){var n=r(5),i=n.Buffer;function o(t,e){for(var r in t)e[r]=t[r]}function s(t,e,r){return i(t,e,r)}i.from&&i.alloc&&i.allocUnsafe&&i.allocUnsafeSlow?t.exports=n:(o(n,e),e.Buffer=s),o(i,s),s.from=function(t,e,r){if("number"==typeof t)throw new TypeError("Argument must not be a number");return i(t,e,r)},s.alloc=function(t,e,r){if("number"!=typeof t)throw new TypeError("Argument must be a number");var n=i(t);return void 0!==e?"string"==typeof r?n.fill(e,r):n.fill(e):n.fill(0),n},s.allocUnsafe=function(t){if("number"!=typeof t)throw new TypeError("Argument must be a number");return i(t)},s.allocUnsafeSlow=function(t){if("number"!=typeof t)throw new TypeError("Argument must be a number");return n.SlowBuffer(t)}},function(t,e,r){"use strict";var n=r(66),i=r(67);function o(){this.protocol=null,this.slashes=null,this.auth=null,this.host=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.query=null,this.pathname=null,this.path=null,this.href=null}e.parse=w,e.resolve=function(t,e){return w(t,!1,!0).resolve(e)},e.resolveObject=function(t,e){return t?w(t,!1,!0).resolveObject(e):e},e.format=function(t){i.isString(t)&&(t=w(t));return t instanceof o?t.format():o.prototype.format.call(t)},e.Url=o;var s=/^([a-z0-9.+-]+:)/i,a=/:[0-9]*$/,u=/^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,h=["{","}","|","\\","^","`"].concat(["<",">",'"',"`"," ","\r","\n","\t"]),f=["'"].concat(h),c=["%","/","?",";","#"].concat(f),l=["/","?","#"],d=/^[+a-z0-9A-Z_-]{0,63}$/,p=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,m={javascript:!0,"javascript:":!0},g={javascript:!0,"javascript:":!0},y={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0},v=r(68);function w(t,e,r){if(t&&i.isObject(t)&&t instanceof o)return t;var n=new o;return n.parse(t,e,r),n}o.prototype.parse=function(t,e,r){if(!i.isString(t))throw new TypeError("Parameter 'url' must be a string, not "+typeof t);var o=t.indexOf("?"),a=-1!==o&&o<t.indexOf("#")?"?":"#",h=t.split(a);h[0]=h[0].replace(/\\/g,"/");var w=t=h.join(a);if(w=w.trim(),!r&&1===t.split("#").length){var b=u.exec(w);if(b)return this.path=w,this.href=w,this.pathname=b[1],b[2]?(this.search=b[2],this.query=e?v.parse(this.search.substr(1)):this.search.substr(1)):e&&(this.search="",this.query={}),this}var _=s.exec(w);if(_){var M=(_=_[0]).toLowerCase();this.protocol=M,w=w.substr(_.length)}if(r||_||w.match(/^\/\/[^@\/]+@[^@\/]+/)){var S="//"===w.substr(0,2);!S||_&&g[_]||(w=w.substr(2),this.slashes=!0)}if(!g[_]&&(S||_&&!y[_])){for(var E,A,O=-1,x=0;x<l.length;x++){-1!==(T=w.indexOf(l[x]))&&(-1===O||T<O)&&(O=T)}-1!==(A=-1===O?w.lastIndexOf("@"):w.lastIndexOf("@",O))&&(E=w.slice(0,A),w=w.slice(A+1),this.auth=decodeURIComponent(E)),O=-1;for(x=0;x<c.length;x++){var T;-1!==(T=w.indexOf(c[x]))&&(-1===O||T<O)&&(O=T)}-1===O&&(O=w.length),this.host=w.slice(0,O),w=w.slice(O),this.parseHost(),this.hostname=this.hostname||"";var R="["===this.hostname[0]&&"]"===this.hostname[this.hostname.length-1];if(!R)for(var k=this.hostname.split(/\./),C=(x=0,k.length);x<C;x++){var j=k[x];if(j&&!j.match(d)){for(var L="",P=0,B=j.length;P<B;P++)j.charCodeAt(P)>127?L+="x":L+=j[P];if(!L.match(d)){var N=k.slice(0,x),I=k.slice(x+1),U=j.match(p);U&&(N.push(U[1]),I.unshift(U[2])),I.length&&(w="/"+I.join(".")+w),this.hostname=N.join(".");break}}}this.hostname.length>255?this.hostname="":this.hostname=this.hostname.toLowerCase(),R||(this.hostname=n.toASCII(this.hostname));var H=this.port?":"+this.port:"",D=this.hostname||"";this.host=D+H,this.href+=this.host,R&&(this.hostname=this.hostname.substr(1,this.hostname.length-2),"/"!==w[0]&&(w="/"+w))}if(!m[M])for(x=0,C=f.length;x<C;x++){var q=f[x];if(-1!==w.indexOf(q)){var W=encodeURIComponent(q);W===q&&(W=escape(q)),w=w.split(q).join(W)}}var F=w.indexOf("#");-1!==F&&(this.hash=w.substr(F),w=w.slice(0,F));var Y=w.indexOf("?");if(-1!==Y?(this.search=w.substr(Y),this.query=w.substr(Y+1),e&&(this.query=v.parse(this.query)),w=w.slice(0,Y)):e&&(this.search="",this.query={}),w&&(this.pathname=w),y[M]&&this.hostname&&!this.pathname&&(this.pathname="/"),this.pathname||this.search){H=this.pathname||"";var z=this.search||"";this.path=H+z}return this.href=this.format(),this},o.prototype.format=function(){var t=this.auth||"";t&&(t=(t=encodeURIComponent(t)).replace(/%3A/i,":"),t+="@");var e=this.protocol||"",r=this.pathname||"",n=this.hash||"",o=!1,s="";this.host?o=t+this.host:this.hostname&&(o=t+(-1===this.hostname.indexOf(":")?this.hostname:"["+this.hostname+"]"),this.port&&(o+=":"+this.port)),this.query&&i.isObject(this.query)&&Object.keys(this.query).length&&(s=v.stringify(this.query));var a=this.search||s&&"?"+s||"";return e&&":"!==e.substr(-1)&&(e+=":"),this.slashes||(!e||y[e])&&!1!==o?(o="//"+(o||""),r&&"/"!==r.charAt(0)&&(r="/"+r)):o||(o=""),n&&"#"!==n.charAt(0)&&(n="#"+n),a&&"?"!==a.charAt(0)&&(a="?"+a),e+o+(r=r.replace(/[?#]/g,(function(t){return encodeURIComponent(t)})))+(a=a.replace("#","%23"))+n},o.prototype.resolve=function(t){return this.resolveObject(w(t,!1,!0)).format()},o.prototype.resolveObject=function(t){if(i.isString(t)){var e=new o;e.parse(t,!1,!0),t=e}for(var r=new o,n=Object.keys(this),s=0;s<n.length;s++){var a=n[s];r[a]=this[a]}if(r.hash=t.hash,""===t.href)return r.href=r.format(),r;if(t.slashes&&!t.protocol){for(var u=Object.keys(t),h=0;h<u.length;h++){var f=u[h];"protocol"!==f&&(r[f]=t[f])}return y[r.protocol]&&r.hostname&&!r.pathname&&(r.path=r.pathname="/"),r.href=r.format(),r}if(t.protocol&&t.protocol!==r.protocol){if(!y[t.protocol]){for(var c=Object.keys(t),l=0;l<c.length;l++){var d=c[l];r[d]=t[d]}return r.href=r.format(),r}if(r.protocol=t.protocol,t.host||g[t.protocol])r.pathname=t.pathname;else{for(var p=(t.pathname||"").split("/");p.length&&!(t.host=p.shift()););t.host||(t.host=""),t.hostname||(t.hostname=""),""!==p[0]&&p.unshift(""),p.length<2&&p.unshift(""),r.pathname=p.join("/")}if(r.search=t.search,r.query=t.query,r.host=t.host||"",r.auth=t.auth,r.hostname=t.hostname||t.host,r.port=t.port,r.pathname||r.search){var m=r.pathname||"",v=r.search||"";r.path=m+v}return r.slashes=r.slashes||t.slashes,r.href=r.format(),r}var w=r.pathname&&"/"===r.pathname.charAt(0),b=t.host||t.pathname&&"/"===t.pathname.charAt(0),_=b||w||r.host&&t.pathname,M=_,S=r.pathname&&r.pathname.split("/")||[],E=(p=t.pathname&&t.pathname.split("/")||[],r.protocol&&!y[r.protocol]);if(E&&(r.hostname="",r.port=null,r.host&&(""===S[0]?S[0]=r.host:S.unshift(r.host)),r.host="",t.protocol&&(t.hostname=null,t.port=null,t.host&&(""===p[0]?p[0]=t.host:p.unshift(t.host)),t.host=null),_=_&&(""===p[0]||""===S[0])),b)r.host=t.host||""===t.host?t.host:r.host,r.hostname=t.hostname||""===t.hostname?t.hostname:r.hostname,r.search=t.search,r.query=t.query,S=p;else if(p.length)S||(S=[]),S.pop(),S=S.concat(p),r.search=t.search,r.query=t.query;else if(!i.isNullOrUndefined(t.search)){if(E)r.hostname=r.host=S.shift(),(R=!!(r.host&&r.host.indexOf("@")>0)&&r.host.split("@"))&&(r.auth=R.shift(),r.host=r.hostname=R.shift());return r.search=t.search,r.query=t.query,i.isNull(r.pathname)&&i.isNull(r.search)||(r.path=(r.pathname?r.pathname:"")+(r.search?r.search:"")),r.href=r.format(),r}if(!S.length)return r.pathname=null,r.search?r.path="/"+r.search:r.path=null,r.href=r.format(),r;for(var A=S.slice(-1)[0],O=(r.host||t.host||S.length>1)&&("."===A||".."===A)||""===A,x=0,T=S.length;T>=0;T--)"."===(A=S[T])?S.splice(T,1):".."===A?(S.splice(T,1),x++):x&&(S.splice(T,1),x--);if(!_&&!M)for(;x--;x)S.unshift("..");!_||""===S[0]||S[0]&&"/"===S[0].charAt(0)||S.unshift(""),O&&"/"!==S.join("/").substr(-1)&&S.push("");var R,k=""===S[0]||S[0]&&"/"===S[0].charAt(0);E&&(r.hostname=r.host=k?"":S.length?S.shift():"",(R=!!(r.host&&r.host.indexOf("@")>0)&&r.host.split("@"))&&(r.auth=R.shift(),r.host=r.hostname=R.shift()));return(_=_||r.host&&S.length)&&!k&&S.unshift(""),S.length?r.pathname=S.join("/"):(r.pathname=null,r.path=null),i.isNull(r.pathname)&&i.isNull(r.search)||(r.path=(r.pathname?r.pathname:"")+(r.search?r.search:"")),r.auth=t.auth||r.auth,r.slashes=r.slashes||t.slashes,r.href=r.format(),r},o.prototype.parseHost=function(){var t=this.host,e=a.exec(t);e&&(":"!==(e=e[0])&&(this.port=e.substr(1)),t=t.substr(0,t.length-e.length)),t&&(this.hostname=t)}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function t(){this.listeners={}}return t.prototype.addEventListener=function(t,e){t=t.toLowerCase(),this.listeners[t]=this.listeners[t]||[],this.listeners[t].push(e.handleEvent||e)},t.prototype.removeEventListener=function(t,e){if(t=t.toLowerCase(),this.listeners[t]){var r=this.listeners[t].indexOf(e.handleEvent||e);r<0||this.listeners[t].splice(r,1)}},t.prototype.dispatchEvent=function(t){var e=t.type.toLowerCase();if(t.target=this,this.listeners[e])for(var r=0,n=this.listeners[e];r<n.length;r++){n[r].call(this,t)}var i=this["on"+e];return i&&i.call(this,t),!0},t}();e.XMLHttpRequestEventTarget=n},function(t,e){t.exports=i,i.strict=o,i.loose=s;var r=Object.prototype.toString,n={"[object Int8Array]":!0,"[object Int16Array]":!0,"[object Int32Array]":!0,"[object Uint8Array]":!0,"[object Uint8ClampedArray]":!0,"[object Uint16Array]":!0,"[object Uint32Array]":!0,"[object Float32Array]":!0,"[object Float64Array]":!0};function i(t){return o(t)||s(t)}function o(t){return t instanceof Int8Array||t instanceof Int16Array||t instanceof Int32Array||t instanceof Uint8Array||t instanceof Uint8ClampedArray||t instanceof Uint16Array||t instanceof Uint32Array||t instanceof Float32Array||t instanceof Float64Array}function s(t){return n[r.call(t)]}},function(t,e,r){"use strict";const n=r(81),i=r(82),o=r(83);function s(t){if("string"!=typeof t||1!==t.length)throw new TypeError("arrayFormatSeparator must be single character string")}function a(t,e){return e.encode?e.strict?n(t):encodeURIComponent(t):t}function u(t,e){return e.decode?i(t):t}function h(t){const e=t.indexOf("#");return-1!==e&&(t=t.slice(0,e)),t}function f(t){const e=(t=h(t)).indexOf("?");return-1===e?"":t.slice(e+1)}function c(t,e){return e.parseNumbers&&!Number.isNaN(Number(t))&&"string"==typeof t&&""!==t.trim()?t=Number(t):!e.parseBooleans||null===t||"true"!==t.toLowerCase()&&"false"!==t.toLowerCase()||(t="true"===t.toLowerCase()),t}function l(t,e){s((e=Object.assign({decode:!0,sort:!0,arrayFormat:"none",arrayFormatSeparator:",",parseNumbers:!1,parseBooleans:!1},e)).arrayFormatSeparator);const r=function(t){let e;switch(t.arrayFormat){case"index":return(t,r,n)=>{e=/\[(\d*)\]$/.exec(t),t=t.replace(/\[\d*\]$/,""),e?(void 0===n[t]&&(n[t]={}),n[t][e[1]]=r):n[t]=r};case"bracket":return(t,r,n)=>{e=/(\[\])$/.exec(t),t=t.replace(/\[\]$/,""),e?void 0!==n[t]?n[t]=[].concat(n[t],r):n[t]=[r]:n[t]=r};case"comma":case"separator":return(e,r,n)=>{const i="string"==typeof r&&r.split("").indexOf(t.arrayFormatSeparator)>-1?r.split(t.arrayFormatSeparator).map(e=>u(e,t)):null===r?r:u(r,t);n[e]=i};default:return(t,e,r)=>{void 0!==r[t]?r[t]=[].concat(r[t],e):r[t]=e}}}(e),n=Object.create(null);if("string"!=typeof t)return n;if(!(t=t.trim().replace(/^[?#&]/,"")))return n;for(const i of t.split("&")){let[t,s]=o(e.decode?i.replace(/\+/g," "):i,"=");s=void 0===s?null:["comma","separator"].includes(e.arrayFormat)?s:u(s,e),r(u(t,e),s,n)}for(const t of Object.keys(n)){const r=n[t];if("object"==typeof r&&null!==r)for(const t of Object.keys(r))r[t]=c(r[t],e);else n[t]=c(r,e)}return!1===e.sort?n:(!0===e.sort?Object.keys(n).sort():Object.keys(n).sort(e.sort)).reduce((t,e)=>{const r=n[e];return Boolean(r)&&"object"==typeof r&&!Array.isArray(r)?t[e]=function t(e){return Array.isArray(e)?e.sort():"object"==typeof e?t(Object.keys(e)).sort((t,e)=>Number(t)-Number(e)).map(t=>e[t]):e}(r):t[e]=r,t},Object.create(null))}e.extract=f,e.parse=l,e.stringify=(t,e)=>{if(!t)return"";s((e=Object.assign({encode:!0,strict:!0,arrayFormat:"none",arrayFormatSeparator:","},e)).arrayFormatSeparator);const r=r=>e.skipNull&&null==t[r]||e.skipEmptyString&&""===t[r],n=function(t){switch(t.arrayFormat){case"index":return e=>(r,n)=>{const i=r.length;return void 0===n||t.skipNull&&null===n||t.skipEmptyString&&""===n?r:null===n?[...r,[a(e,t),"[",i,"]"].join("")]:[...r,[a(e,t),"[",a(i,t),"]=",a(n,t)].join("")]};case"bracket":return e=>(r,n)=>void 0===n||t.skipNull&&null===n||t.skipEmptyString&&""===n?r:null===n?[...r,[a(e,t),"[]"].join("")]:[...r,[a(e,t),"[]=",a(n,t)].join("")];case"comma":case"separator":return e=>(r,n)=>null==n||0===n.length?r:0===r.length?[[a(e,t),"=",a(n,t)].join("")]:[[r,a(n,t)].join(t.arrayFormatSeparator)];default:return e=>(r,n)=>void 0===n||t.skipNull&&null===n||t.skipEmptyString&&""===n?r:null===n?[...r,a(e,t)]:[...r,[a(e,t),"=",a(n,t)].join("")]}}(e),i={};for(const e of Object.keys(t))r(e)||(i[e]=t[e]);const o=Object.keys(i);return!1!==e.sort&&o.sort(e.sort),o.map(r=>{const i=t[r];return void 0===i?"":null===i?a(r,e):Array.isArray(i)?i.reduce(n(r),[]).join("&"):a(r,e)+"="+a(i,e)}).filter(t=>t.length>0).join("&")},e.parseUrl=(t,e)=>{e=Object.assign({decode:!0},e);const[r,n]=o(t,"#");return Object.assign({url:r.split("?")[0]||"",query:l(f(t),e)},e&&e.parseFragmentIdentifier&&n?{fragmentIdentifier:u(n,e)}:{})},e.stringifyUrl=(t,r)=>{r=Object.assign({encode:!0,strict:!0},r);const n=h(t.url).split("?")[0]||"",i=e.extract(t.url),o=e.parse(i,{sort:!1}),s=Object.assign(o,t.query);let u=e.stringify(s,r);u&&(u="?"+u);let f=function(t){let e="";const r=t.indexOf("#");return-1!==r&&(e=t.slice(r)),e}(t.url);return t.fragmentIdentifier&&(f="#"+a(t.fragmentIdentifier,r)),`${n}${u}${f}`}},function(t,e){var r={}.toString;t.exports=Array.isArray||function(t){return"[object Array]"==r.call(t)}},function(t,e,r){(function(t){var n=r(54),i=r(22),o=r(64),s=r(65),a=r(15),u=e;u.request=function(e,r){e="string"==typeof e?a.parse(e):o(e);var i=-1===t.location.protocol.search(/^https?:$/)?"http:":"",s=e.protocol||i,u=e.hostname||e.host,h=e.port,f=e.path||"/";u&&-1!==u.indexOf(":")&&(u="["+u+"]"),e.url=(u?s+"//"+u:"")+(h?":"+h:"")+f,e.method=(e.method||"GET").toUpperCase(),e.headers=e.headers||{};var c=new n(e);return r&&c.on("response",r),c},u.get=function(t,e){var r=u.request(t,e);return r.end(),r},u.ClientRequest=n,u.IncomingMessage=i.IncomingMessage,u.Agent=function(){},u.Agent.defaultMaxSockets=4,u.globalAgent=new u.Agent,u.STATUS_CODES=s,u.METHODS=["CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","REPORT","SEARCH","SUBSCRIBE","TRACE","UNLOCK","UNSUBSCRIBE"]}).call(this,r(3))},function(t,e,r){(function(t){e.fetch=a(t.fetch)&&a(t.ReadableStream),e.writableStream=a(t.WritableStream),e.abortController=a(t.AbortController),e.blobConstructor=!1;try{new Blob([new ArrayBuffer(1)]),e.blobConstructor=!0}catch(t){}var r;function n(){if(void 0!==r)return r;if(t.XMLHttpRequest){r=new t.XMLHttpRequest;try{r.open("GET",t.XDomainRequest?"/":"https://example.com")}catch(t){r=null}}else r=null;return r}function i(t){var e=n();if(!e)return!1;try{return e.responseType=t,e.responseType===t}catch(t){}return!1}var o=void 0!==t.ArrayBuffer,s=o&&a(t.ArrayBuffer.prototype.slice);function a(t){return"function"==typeof t}e.arraybuffer=e.fetch||o&&i("arraybuffer"),e.msstream=!e.fetch&&s&&i("ms-stream"),e.mozchunkedarraybuffer=!e.fetch&&o&&i("moz-chunked-arraybuffer"),e.overrideMimeType=e.fetch||!!n()&&a(n().overrideMimeType),e.vbArray=a(t.VBArray),r=null}).call(this,r(3))},function(t,e,r){(function(t,n,i){var o=r(21),s=r(6),a=r(23),u=e.readyStates={UNSENT:0,OPENED:1,HEADERS_RECEIVED:2,LOADING:3,DONE:4},h=e.IncomingMessage=function(e,r,s,u){var h=this;if(a.Readable.call(h),h._mode=s,h.headers={},h.rawHeaders=[],h.trailers={},h.rawTrailers=[],h.on("end",(function(){t.nextTick((function(){h.emit("close")}))})),"fetch"===s){if(h._fetchResponse=r,h.url=r.url,h.statusCode=r.status,h.statusMessage=r.statusText,r.headers.forEach((function(t,e){h.headers[e.toLowerCase()]=t,h.rawHeaders.push(e,t)})),o.writableStream){var f=new WritableStream({write:function(t){return new Promise((function(e,r){h._destroyed?r():h.push(new n(t))?e():h._resumeFetch=e}))},close:function(){i.clearTimeout(u),h._destroyed||h.push(null)},abort:function(t){h._destroyed||h.emit("error",t)}});try{return void r.body.pipeTo(f).catch((function(t){i.clearTimeout(u),h._destroyed||h.emit("error",t)}))}catch(t){}}var c=r.body.getReader();!function t(){c.read().then((function(e){if(!h._destroyed){if(e.done)return i.clearTimeout(u),void h.push(null);h.push(new n(e.value)),t()}})).catch((function(t){i.clearTimeout(u),h._destroyed||h.emit("error",t)}))}()}else{if(h._xhr=e,h._pos=0,h.url=e.responseURL,h.statusCode=e.status,h.statusMessage=e.statusText,e.getAllResponseHeaders().split(/\r?\n/).forEach((function(t){var e=t.match(/^([^:]+):\s*(.*)/);if(e){var r=e[1].toLowerCase();"set-cookie"===r?(void 0===h.headers[r]&&(h.headers[r]=[]),h.headers[r].push(e[2])):void 0!==h.headers[r]?h.headers[r]+=", "+e[2]:h.headers[r]=e[2],h.rawHeaders.push(e[1],e[2])}})),h._charset="x-user-defined",!o.overrideMimeType){var l=h.rawHeaders["mime-type"];if(l){var d=l.match(/;\s*charset=([^;])(;|$)/);d&&(h._charset=d[1].toLowerCase())}h._charset||(h._charset="utf-8")}}};s(h,a.Readable),h.prototype._read=function(){var t=this._resumeFetch;t&&(this._resumeFetch=null,t())},h.prototype._onXHRProgress=function(){var t=this,e=t._xhr,r=null;switch(t._mode){case"text:vbarray":if(e.readyState!==u.DONE)break;try{r=new i.VBArray(e.responseBody).toArray()}catch(t){}if(null!==r){t.push(new n(r));break}case"text":try{r=e.responseText}catch(e){t._mode="text:vbarray";break}if(r.length>t._pos){var o=r.substr(t._pos);if("x-user-defined"===t._charset){for(var s=new n(o.length),a=0;a<o.length;a++)s[a]=255&o.charCodeAt(a);t.push(s)}else t.push(o,t._charset);t._pos=r.length}break;case"arraybuffer":if(e.readyState!==u.DONE||!e.response)break;r=e.response,t.push(new n(new Uint8Array(r)));break;case"moz-chunked-arraybuffer":if(r=e.response,e.readyState!==u.LOADING||!r)break;t.push(new n(new Uint8Array(r)));break;case"ms-stream":if(r=e.response,e.readyState!==u.LOADING)break;var h=new i.MSStreamReader;h.onprogress=function(){h.result.byteLength>t._pos&&(t.push(new n(new Uint8Array(h.result.slice(t._pos)))),t._pos=h.result.byteLength)},h.onload=function(){t.push(null)},h.readAsArrayBuffer(r)}t._xhr.readyState===u.DONE&&"ms-stream"!==t._mode&&t.push(null)}}).call(this,r(4),r(5).Buffer,r(3))},function(t,e,r){(e=t.exports=r(24)).Stream=e,e.Readable=e,e.Writable=r(28),e.Duplex=r(8),e.Transform=r(30),e.PassThrough=r(62)},function(t,e,r){"use strict";(function(e,n){var i=r(12);t.exports=w;var o,s=r(19);w.ReadableState=v;r(25).EventEmitter;var a=function(t,e){return t.listeners(e).length},u=r(26),h=r(14).Buffer,f=e.Uint8Array||function(){};var c=Object.create(r(9));c.inherits=r(6);var l=r(55),d=void 0;d=l&&l.debuglog?l.debuglog("stream"):function(){};var p,m=r(56),g=r(27);c.inherits(w,u);var y=["error","close","destroy","pause","resume"];function v(t,e){t=t||{};var n=e instanceof(o=o||r(8));this.objectMode=!!t.objectMode,n&&(this.objectMode=this.objectMode||!!t.readableObjectMode);var i=t.highWaterMark,s=t.readableHighWaterMark,a=this.objectMode?16:16384;this.highWaterMark=i||0===i?i:n&&(s||0===s)?s:a,this.highWaterMark=Math.floor(this.highWaterMark),this.buffer=new m,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.destroyed=!1,this.defaultEncoding=t.defaultEncoding||"utf8",this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,t.encoding&&(p||(p=r(29).StringDecoder),this.decoder=new p(t.encoding),this.encoding=t.encoding)}function w(t){if(o=o||r(8),!(this instanceof w))return new w(t);this._readableState=new v(t,this),this.readable=!0,t&&("function"==typeof t.read&&(this._read=t.read),"function"==typeof t.destroy&&(this._destroy=t.destroy)),u.call(this)}function b(t,e,r,n,i){var o,s=t._readableState;null===e?(s.reading=!1,function(t,e){if(e.ended)return;if(e.decoder){var r=e.decoder.end();r&&r.length&&(e.buffer.push(r),e.length+=e.objectMode?1:r.length)}e.ended=!0,S(t)}(t,s)):(i||(o=function(t,e){var r;n=e,h.isBuffer(n)||n instanceof f||"string"==typeof e||void 0===e||t.objectMode||(r=new TypeError("Invalid non-string/buffer chunk"));var n;return r}(s,e)),o?t.emit("error",o):s.objectMode||e&&e.length>0?("string"==typeof e||s.objectMode||Object.getPrototypeOf(e)===h.prototype||(e=function(t){return h.from(t)}(e)),n?s.endEmitted?t.emit("error",new Error("stream.unshift() after end event")):_(t,s,e,!0):s.ended?t.emit("error",new Error("stream.push() after EOF")):(s.reading=!1,s.decoder&&!r?(e=s.decoder.write(e),s.objectMode||0!==e.length?_(t,s,e,!1):A(t,s)):_(t,s,e,!1))):n||(s.reading=!1));return function(t){return!t.ended&&(t.needReadable||t.length<t.highWaterMark||0===t.length)}(s)}function _(t,e,r,n){e.flowing&&0===e.length&&!e.sync?(t.emit("data",r),t.read(0)):(e.length+=e.objectMode?1:r.length,n?e.buffer.unshift(r):e.buffer.push(r),e.needReadable&&S(t)),A(t,e)}Object.defineProperty(w.prototype,"destroyed",{get:function(){return void 0!==this._readableState&&this._readableState.destroyed},set:function(t){this._readableState&&(this._readableState.destroyed=t)}}),w.prototype.destroy=g.destroy,w.prototype._undestroy=g.undestroy,w.prototype._destroy=function(t,e){this.push(null),e(t)},w.prototype.push=function(t,e){var r,n=this._readableState;return n.objectMode?r=!0:"string"==typeof t&&((e=e||n.defaultEncoding)!==n.encoding&&(t=h.from(t,e),e=""),r=!0),b(this,t,e,!1,r)},w.prototype.unshift=function(t){return b(this,t,null,!0,!1)},w.prototype.isPaused=function(){return!1===this._readableState.flowing},w.prototype.setEncoding=function(t){return p||(p=r(29).StringDecoder),this._readableState.decoder=new p(t),this._readableState.encoding=t,this};function M(t,e){return t<=0||0===e.length&&e.ended?0:e.objectMode?1:t!=t?e.flowing&&e.length?e.buffer.head.data.length:e.length:(t>e.highWaterMark&&(e.highWaterMark=function(t){return t>=8388608?t=8388608:(t--,t|=t>>>1,t|=t>>>2,t|=t>>>4,t|=t>>>8,t|=t>>>16,t++),t}(t)),t<=e.length?t:e.ended?e.length:(e.needReadable=!0,0))}function S(t){var e=t._readableState;e.needReadable=!1,e.emittedReadable||(d("emitReadable",e.flowing),e.emittedReadable=!0,e.sync?i.nextTick(E,t):E(t))}function E(t){d("emit readable"),t.emit("readable"),R(t)}function A(t,e){e.readingMore||(e.readingMore=!0,i.nextTick(O,t,e))}function O(t,e){for(var r=e.length;!e.reading&&!e.flowing&&!e.ended&&e.length<e.highWaterMark&&(d("maybeReadMore read 0"),t.read(0),r!==e.length);)r=e.length;e.readingMore=!1}function x(t){d("readable nexttick read 0"),t.read(0)}function T(t,e){e.reading||(d("resume read 0"),t.read(0)),e.resumeScheduled=!1,e.awaitDrain=0,t.emit("resume"),R(t),e.flowing&&!e.reading&&t.read(0)}function R(t){var e=t._readableState;for(d("flow",e.flowing);e.flowing&&null!==t.read(););}function k(t,e){return 0===e.length?null:(e.objectMode?r=e.buffer.shift():!t||t>=e.length?(r=e.decoder?e.buffer.join(""):1===e.buffer.length?e.buffer.head.data:e.buffer.concat(e.length),e.buffer.clear()):r=function(t,e,r){var n;t<e.head.data.length?(n=e.head.data.slice(0,t),e.head.data=e.head.data.slice(t)):n=t===e.head.data.length?e.shift():r?function(t,e){var r=e.head,n=1,i=r.data;t-=i.length;for(;r=r.next;){var o=r.data,s=t>o.length?o.length:t;if(s===o.length?i+=o:i+=o.slice(0,t),0===(t-=s)){s===o.length?(++n,r.next?e.head=r.next:e.head=e.tail=null):(e.head=r,r.data=o.slice(s));break}++n}return e.length-=n,i}(t,e):function(t,e){var r=h.allocUnsafe(t),n=e.head,i=1;n.data.copy(r),t-=n.data.length;for(;n=n.next;){var o=n.data,s=t>o.length?o.length:t;if(o.copy(r,r.length-t,0,s),0===(t-=s)){s===o.length?(++i,n.next?e.head=n.next:e.head=e.tail=null):(e.head=n,n.data=o.slice(s));break}++i}return e.length-=i,r}(t,e);return n}(t,e.buffer,e.decoder),r);var r}function C(t){var e=t._readableState;if(e.length>0)throw new Error('"endReadable()" called on non-empty stream');e.endEmitted||(e.ended=!0,i.nextTick(j,e,t))}function j(t,e){t.endEmitted||0!==t.length||(t.endEmitted=!0,e.readable=!1,e.emit("end"))}function L(t,e){for(var r=0,n=t.length;r<n;r++)if(t[r]===e)return r;return-1}w.prototype.read=function(t){d("read",t),t=parseInt(t,10);var e=this._readableState,r=t;if(0!==t&&(e.emittedReadable=!1),0===t&&e.needReadable&&(e.length>=e.highWaterMark||e.ended))return d("read: emitReadable",e.length,e.ended),0===e.length&&e.ended?C(this):S(this),null;if(0===(t=M(t,e))&&e.ended)return 0===e.length&&C(this),null;var n,i=e.needReadable;return d("need readable",i),(0===e.length||e.length-t<e.highWaterMark)&&d("length less than watermark",i=!0),e.ended||e.reading?d("reading or ended",i=!1):i&&(d("do read"),e.reading=!0,e.sync=!0,0===e.length&&(e.needReadable=!0),this._read(e.highWaterMark),e.sync=!1,e.reading||(t=M(r,e))),null===(n=t>0?k(t,e):null)?(e.needReadable=!0,t=0):e.length-=t,0===e.length&&(e.ended||(e.needReadable=!0),r!==t&&e.ended&&C(this)),null!==n&&this.emit("data",n),n},w.prototype._read=function(t){this.emit("error",new Error("_read() is not implemented"))},w.prototype.pipe=function(t,e){var r=this,o=this._readableState;switch(o.pipesCount){case 0:o.pipes=t;break;case 1:o.pipes=[o.pipes,t];break;default:o.pipes.push(t)}o.pipesCount+=1,d("pipe count=%d opts=%j",o.pipesCount,e);var u=(!e||!1!==e.end)&&t!==n.stdout&&t!==n.stderr?f:w;function h(e,n){d("onunpipe"),e===r&&n&&!1===n.hasUnpiped&&(n.hasUnpiped=!0,d("cleanup"),t.removeListener("close",y),t.removeListener("finish",v),t.removeListener("drain",c),t.removeListener("error",g),t.removeListener("unpipe",h),r.removeListener("end",f),r.removeListener("end",w),r.removeListener("data",m),l=!0,!o.awaitDrain||t._writableState&&!t._writableState.needDrain||c())}function f(){d("onend"),t.end()}o.endEmitted?i.nextTick(u):r.once("end",u),t.on("unpipe",h);var c=function(t){return function(){var e=t._readableState;d("pipeOnDrain",e.awaitDrain),e.awaitDrain&&e.awaitDrain--,0===e.awaitDrain&&a(t,"data")&&(e.flowing=!0,R(t))}}(r);t.on("drain",c);var l=!1;var p=!1;function m(e){d("ondata"),p=!1,!1!==t.write(e)||p||((1===o.pipesCount&&o.pipes===t||o.pipesCount>1&&-1!==L(o.pipes,t))&&!l&&(d("false write response, pause",r._readableState.awaitDrain),r._readableState.awaitDrain++,p=!0),r.pause())}function g(e){d("onerror",e),w(),t.removeListener("error",g),0===a(t,"error")&&t.emit("error",e)}function y(){t.removeListener("finish",v),w()}function v(){d("onfinish"),t.removeListener("close",y),w()}function w(){d("unpipe"),r.unpipe(t)}return r.on("data",m),function(t,e,r){if("function"==typeof t.prependListener)return t.prependListener(e,r);t._events&&t._events[e]?s(t._events[e])?t._events[e].unshift(r):t._events[e]=[r,t._events[e]]:t.on(e,r)}(t,"error",g),t.once("close",y),t.once("finish",v),t.emit("pipe",r),o.flowing||(d("pipe resume"),r.resume()),t},w.prototype.unpipe=function(t){var e=this._readableState,r={hasUnpiped:!1};if(0===e.pipesCount)return this;if(1===e.pipesCount)return t&&t!==e.pipes||(t||(t=e.pipes),e.pipes=null,e.pipesCount=0,e.flowing=!1,t&&t.emit("unpipe",this,r)),this;if(!t){var n=e.pipes,i=e.pipesCount;e.pipes=null,e.pipesCount=0,e.flowing=!1;for(var o=0;o<i;o++)n[o].emit("unpipe",this,r);return this}var s=L(e.pipes,t);return-1===s||(e.pipes.splice(s,1),e.pipesCount-=1,1===e.pipesCount&&(e.pipes=e.pipes[0]),t.emit("unpipe",this,r)),this},w.prototype.on=function(t,e){var r=u.prototype.on.call(this,t,e);if("data"===t)!1!==this._readableState.flowing&&this.resume();else if("readable"===t){var n=this._readableState;n.endEmitted||n.readableListening||(n.readableListening=n.needReadable=!0,n.emittedReadable=!1,n.reading?n.length&&S(this):i.nextTick(x,this))}return r},w.prototype.addListener=w.prototype.on,w.prototype.resume=function(){var t=this._readableState;return t.flowing||(d("resume"),t.flowing=!0,function(t,e){e.resumeScheduled||(e.resumeScheduled=!0,i.nextTick(T,t,e))}(this,t)),this},w.prototype.pause=function(){return d("call pause flowing=%j",this._readableState.flowing),!1!==this._readableState.flowing&&(d("pause"),this._readableState.flowing=!1,this.emit("pause")),this},w.prototype.wrap=function(t){var e=this,r=this._readableState,n=!1;for(var i in t.on("end",(function(){if(d("wrapped end"),r.decoder&&!r.ended){var t=r.decoder.end();t&&t.length&&e.push(t)}e.push(null)})),t.on("data",(function(i){(d("wrapped data"),r.decoder&&(i=r.decoder.write(i)),r.objectMode&&null==i)||(r.objectMode||i&&i.length)&&(e.push(i)||(n=!0,t.pause()))})),t)void 0===this[i]&&"function"==typeof t[i]&&(this[i]=function(e){return function(){return t[e].apply(t,arguments)}}(i));for(var o=0;o<y.length;o++)t.on(y[o],this.emit.bind(this,y[o]));return this._read=function(e){d("wrapped _read",e),n&&(n=!1,t.resume())},this},Object.defineProperty(w.prototype,"readableHighWaterMark",{enumerable:!1,get:function(){return this._readableState.highWaterMark}}),w._fromList=k}).call(this,r(3),r(4))},function(t,e,r){"use strict";var n,i="object"==typeof Reflect?Reflect:null,o=i&&"function"==typeof i.apply?i.apply:function(t,e,r){return Function.prototype.apply.call(t,e,r)};n=i&&"function"==typeof i.ownKeys?i.ownKeys:Object.getOwnPropertySymbols?function(t){return Object.getOwnPropertyNames(t).concat(Object.getOwnPropertySymbols(t))}:function(t){return Object.getOwnPropertyNames(t)};var s=Number.isNaN||function(t){return t!=t};function a(){a.init.call(this)}t.exports=a,t.exports.once=function(t,e){return new Promise((function(r,n){function i(r){t.removeListener(e,o),n(r)}function o(){"function"==typeof t.removeListener&&t.removeListener("error",i),r([].slice.call(arguments))}y(t,e,o,{once:!0}),"error"!==e&&function(t,e,r){"function"==typeof t.on&&y(t,"error",e,r)}(t,i,{once:!0})}))},a.EventEmitter=a,a.prototype._events=void 0,a.prototype._eventsCount=0,a.prototype._maxListeners=void 0;var u=10;function h(t){if("function"!=typeof t)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof t)}function f(t){return void 0===t._maxListeners?a.defaultMaxListeners:t._maxListeners}function c(t,e,r,n){var i,o,s,a;if(h(r),void 0===(o=t._events)?(o=t._events=Object.create(null),t._eventsCount=0):(void 0!==o.newListener&&(t.emit("newListener",e,r.listener?r.listener:r),o=t._events),s=o[e]),void 0===s)s=o[e]=r,++t._eventsCount;else if("function"==typeof s?s=o[e]=n?[r,s]:[s,r]:n?s.unshift(r):s.push(r),(i=f(t))>0&&s.length>i&&!s.warned){s.warned=!0;var u=new Error("Possible EventEmitter memory leak detected. "+s.length+" "+String(e)+" listeners added. Use emitter.setMaxListeners() to increase limit");u.name="MaxListenersExceededWarning",u.emitter=t,u.type=e,u.count=s.length,a=u,console&&console.warn&&console.warn(a)}return t}function l(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function d(t,e,r){var n={fired:!1,wrapFn:void 0,target:t,type:e,listener:r},i=l.bind(n);return i.listener=r,n.wrapFn=i,i}function p(t,e,r){var n=t._events;if(void 0===n)return[];var i=n[e];return void 0===i?[]:"function"==typeof i?r?[i.listener||i]:[i]:r?function(t){for(var e=new Array(t.length),r=0;r<e.length;++r)e[r]=t[r].listener||t[r];return e}(i):g(i,i.length)}function m(t){var e=this._events;if(void 0!==e){var r=e[t];if("function"==typeof r)return 1;if(void 0!==r)return r.length}return 0}function g(t,e){for(var r=new Array(e),n=0;n<e;++n)r[n]=t[n];return r}function y(t,e,r,n){if("function"==typeof t.on)n.once?t.once(e,r):t.on(e,r);else{if("function"!=typeof t.addEventListener)throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof t);t.addEventListener(e,(function i(o){n.once&&t.removeEventListener(e,i),r(o)}))}}Object.defineProperty(a,"defaultMaxListeners",{enumerable:!0,get:function(){return u},set:function(t){if("number"!=typeof t||t<0||s(t))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+t+".");u=t}}),a.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},a.prototype.setMaxListeners=function(t){if("number"!=typeof t||t<0||s(t))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+t+".");return this._maxListeners=t,this},a.prototype.getMaxListeners=function(){return f(this)},a.prototype.emit=function(t){for(var e=[],r=1;r<arguments.length;r++)e.push(arguments[r]);var n="error"===t,i=this._events;if(void 0!==i)n=n&&void 0===i.error;else if(!n)return!1;if(n){var s;if(e.length>0&&(s=e[0]),s instanceof Error)throw s;var a=new Error("Unhandled error."+(s?" ("+s.message+")":""));throw a.context=s,a}var u=i[t];if(void 0===u)return!1;if("function"==typeof u)o(u,this,e);else{var h=u.length,f=g(u,h);for(r=0;r<h;++r)o(f[r],this,e)}return!0},a.prototype.addListener=function(t,e){return c(this,t,e,!1)},a.prototype.on=a.prototype.addListener,a.prototype.prependListener=function(t,e){return c(this,t,e,!0)},a.prototype.once=function(t,e){return h(e),this.on(t,d(this,t,e)),this},a.prototype.prependOnceListener=function(t,e){return h(e),this.prependListener(t,d(this,t,e)),this},a.prototype.removeListener=function(t,e){var r,n,i,o,s;if(h(e),void 0===(n=this._events))return this;if(void 0===(r=n[t]))return this;if(r===e||r.listener===e)0==--this._eventsCount?this._events=Object.create(null):(delete n[t],n.removeListener&&this.emit("removeListener",t,r.listener||e));else if("function"!=typeof r){for(i=-1,o=r.length-1;o>=0;o--)if(r[o]===e||r[o].listener===e){s=r[o].listener,i=o;break}if(i<0)return this;0===i?r.shift():function(t,e){for(;e+1<t.length;e++)t[e]=t[e+1];t.pop()}(r,i),1===r.length&&(n[t]=r[0]),void 0!==n.removeListener&&this.emit("removeListener",t,s||e)}return this},a.prototype.off=a.prototype.removeListener,a.prototype.removeAllListeners=function(t){var e,r,n;if(void 0===(r=this._events))return this;if(void 0===r.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==r[t]&&(0==--this._eventsCount?this._events=Object.create(null):delete r[t]),this;if(0===arguments.length){var i,o=Object.keys(r);for(n=0;n<o.length;++n)"removeListener"!==(i=o[n])&&this.removeAllListeners(i);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(e=r[t]))this.removeListener(t,e);else if(void 0!==e)for(n=e.length-1;n>=0;n--)this.removeListener(t,e[n]);return this},a.prototype.listeners=function(t){return p(this,t,!0)},a.prototype.rawListeners=function(t){return p(this,t,!1)},a.listenerCount=function(t,e){return"function"==typeof t.listenerCount?t.listenerCount(e):m.call(t,e)},a.prototype.listenerCount=m,a.prototype.eventNames=function(){return this._eventsCount>0?n(this._events):[]}},function(t,e,r){t.exports=r(25).EventEmitter},function(t,e,r){"use strict";var n=r(12);function i(t,e){t.emit("error",e)}t.exports={destroy:function(t,e){var r=this,o=this._readableState&&this._readableState.destroyed,s=this._writableState&&this._writableState.destroyed;return o||s?(e?e(t):!t||this._writableState&&this._writableState.errorEmitted||n.nextTick(i,this,t),this):(this._readableState&&(this._readableState.destroyed=!0),this._writableState&&(this._writableState.destroyed=!0),this._destroy(t||null,(function(t){!e&&t?(n.nextTick(i,r,t),r._writableState&&(r._writableState.errorEmitted=!0)):e&&e(t)})),this)},undestroy:function(){this._readableState&&(this._readableState.destroyed=!1,this._readableState.reading=!1,this._readableState.ended=!1,this._readableState.endEmitted=!1),this._writableState&&(this._writableState.destroyed=!1,this._writableState.ended=!1,this._writableState.ending=!1,this._writableState.finished=!1,this._writableState.errorEmitted=!1)}}},function(t,e,r){"use strict";(function(e,n,i){var o=r(12);function s(t){var e=this;this.next=null,this.entry=null,this.finish=function(){!function(t,e,r){var n=t.entry;t.entry=null;for(;n;){var i=n.callback;e.pendingcb--,i(r),n=n.next}e.corkedRequestsFree?e.corkedRequestsFree.next=t:e.corkedRequestsFree=t}(e,t)}}t.exports=v;var a,u=!e.browser&&["v0.10","v0.9."].indexOf(e.version.slice(0,5))>-1?n:o.nextTick;v.WritableState=y;var h=Object.create(r(9));h.inherits=r(6);var f={deprecate:r(60)},c=r(26),l=r(14).Buffer,d=i.Uint8Array||function(){};var p,m=r(27);function g(){}function y(t,e){a=a||r(8),t=t||{};var n=e instanceof a;this.objectMode=!!t.objectMode,n&&(this.objectMode=this.objectMode||!!t.writableObjectMode);var i=t.highWaterMark,h=t.writableHighWaterMark,f=this.objectMode?16:16384;this.highWaterMark=i||0===i?i:n&&(h||0===h)?h:f,this.highWaterMark=Math.floor(this.highWaterMark),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;var c=!1===t.decodeStrings;this.decodeStrings=!c,this.defaultEncoding=t.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(t){!function(t,e){var r=t._writableState,n=r.sync,i=r.writecb;if(function(t){t.writing=!1,t.writecb=null,t.length-=t.writelen,t.writelen=0}(r),e)!function(t,e,r,n,i){--e.pendingcb,r?(o.nextTick(i,n),o.nextTick(E,t,e),t._writableState.errorEmitted=!0,t.emit("error",n)):(i(n),t._writableState.errorEmitted=!0,t.emit("error",n),E(t,e))}(t,r,n,e,i);else{var s=M(r);s||r.corked||r.bufferProcessing||!r.bufferedRequest||_(t,r),n?u(b,t,r,s,i):b(t,r,s,i)}}(e,t)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.bufferedRequestCount=0,this.corkedRequestsFree=new s(this)}function v(t){if(a=a||r(8),!(p.call(v,this)||this instanceof a))return new v(t);this._writableState=new y(t,this),this.writable=!0,t&&("function"==typeof t.write&&(this._write=t.write),"function"==typeof t.writev&&(this._writev=t.writev),"function"==typeof t.destroy&&(this._destroy=t.destroy),"function"==typeof t.final&&(this._final=t.final)),c.call(this)}function w(t,e,r,n,i,o,s){e.writelen=n,e.writecb=s,e.writing=!0,e.sync=!0,r?t._writev(i,e.onwrite):t._write(i,o,e.onwrite),e.sync=!1}function b(t,e,r,n){r||function(t,e){0===e.length&&e.needDrain&&(e.needDrain=!1,t.emit("drain"))}(t,e),e.pendingcb--,n(),E(t,e)}function _(t,e){e.bufferProcessing=!0;var r=e.bufferedRequest;if(t._writev&&r&&r.next){var n=e.bufferedRequestCount,i=new Array(n),o=e.corkedRequestsFree;o.entry=r;for(var a=0,u=!0;r;)i[a]=r,r.isBuf||(u=!1),r=r.next,a+=1;i.allBuffers=u,w(t,e,!0,e.length,i,"",o.finish),e.pendingcb++,e.lastBufferedRequest=null,o.next?(e.corkedRequestsFree=o.next,o.next=null):e.corkedRequestsFree=new s(e),e.bufferedRequestCount=0}else{for(;r;){var h=r.chunk,f=r.encoding,c=r.callback;if(w(t,e,!1,e.objectMode?1:h.length,h,f,c),r=r.next,e.bufferedRequestCount--,e.writing)break}null===r&&(e.lastBufferedRequest=null)}e.bufferedRequest=r,e.bufferProcessing=!1}function M(t){return t.ending&&0===t.length&&null===t.bufferedRequest&&!t.finished&&!t.writing}function S(t,e){t._final((function(r){e.pendingcb--,r&&t.emit("error",r),e.prefinished=!0,t.emit("prefinish"),E(t,e)}))}function E(t,e){var r=M(e);return r&&(!function(t,e){e.prefinished||e.finalCalled||("function"==typeof t._final?(e.pendingcb++,e.finalCalled=!0,o.nextTick(S,t,e)):(e.prefinished=!0,t.emit("prefinish")))}(t,e),0===e.pendingcb&&(e.finished=!0,t.emit("finish"))),r}h.inherits(v,c),y.prototype.getBuffer=function(){for(var t=this.bufferedRequest,e=[];t;)e.push(t),t=t.next;return e},function(){try{Object.defineProperty(y.prototype,"buffer",{get:f.deprecate((function(){return this.getBuffer()}),"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.","DEP0003")})}catch(t){}}(),"function"==typeof Symbol&&Symbol.hasInstance&&"function"==typeof Function.prototype[Symbol.hasInstance]?(p=Function.prototype[Symbol.hasInstance],Object.defineProperty(v,Symbol.hasInstance,{value:function(t){return!!p.call(this,t)||this===v&&(t&&t._writableState instanceof y)}})):p=function(t){return t instanceof this},v.prototype.pipe=function(){this.emit("error",new Error("Cannot pipe, not readable"))},v.prototype.write=function(t,e,r){var n,i=this._writableState,s=!1,a=!i.objectMode&&(n=t,l.isBuffer(n)||n instanceof d);return a&&!l.isBuffer(t)&&(t=function(t){return l.from(t)}(t)),"function"==typeof e&&(r=e,e=null),a?e="buffer":e||(e=i.defaultEncoding),"function"!=typeof r&&(r=g),i.ended?function(t,e){var r=new Error("write after end");t.emit("error",r),o.nextTick(e,r)}(this,r):(a||function(t,e,r,n){var i=!0,s=!1;return null===r?s=new TypeError("May not write null values to stream"):"string"==typeof r||void 0===r||e.objectMode||(s=new TypeError("Invalid non-string/buffer chunk")),s&&(t.emit("error",s),o.nextTick(n,s),i=!1),i}(this,i,t,r))&&(i.pendingcb++,s=function(t,e,r,n,i,o){if(!r){var s=function(t,e,r){t.objectMode||!1===t.decodeStrings||"string"!=typeof e||(e=l.from(e,r));return e}(e,n,i);n!==s&&(r=!0,i="buffer",n=s)}var a=e.objectMode?1:n.length;e.length+=a;var u=e.length<e.highWaterMark;u||(e.needDrain=!0);if(e.writing||e.corked){var h=e.lastBufferedRequest;e.lastBufferedRequest={chunk:n,encoding:i,isBuf:r,callback:o,next:null},h?h.next=e.lastBufferedRequest:e.bufferedRequest=e.lastBufferedRequest,e.bufferedRequestCount+=1}else w(t,e,!1,a,n,i,o);return u}(this,i,a,t,e,r)),s},v.prototype.cork=function(){this._writableState.corked++},v.prototype.uncork=function(){var t=this._writableState;t.corked&&(t.corked--,t.writing||t.corked||t.finished||t.bufferProcessing||!t.bufferedRequest||_(this,t))},v.prototype.setDefaultEncoding=function(t){if("string"==typeof t&&(t=t.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((t+"").toLowerCase())>-1))throw new TypeError("Unknown encoding: "+t);return this._writableState.defaultEncoding=t,this},Object.defineProperty(v.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),v.prototype._write=function(t,e,r){r(new Error("_write() is not implemented"))},v.prototype._writev=null,v.prototype.end=function(t,e,r){var n=this._writableState;"function"==typeof t?(r=t,t=null,e=null):"function"==typeof e&&(r=e,e=null),null!=t&&this.write(t,e),n.corked&&(n.corked=1,this.uncork()),n.ending||n.finished||function(t,e,r){e.ending=!0,E(t,e),r&&(e.finished?o.nextTick(r):t.once("finish",r));e.ended=!0,t.writable=!1}(this,n,r)},Object.defineProperty(v.prototype,"destroyed",{get:function(){return void 0!==this._writableState&&this._writableState.destroyed},set:function(t){this._writableState&&(this._writableState.destroyed=t)}}),v.prototype.destroy=m.destroy,v.prototype._undestroy=m.undestroy,v.prototype._destroy=function(t,e){this.end(),e(t)}}).call(this,r(4),r(58).setImmediate,r(3))},function(t,e,r){"use strict";var n=r(61).Buffer,i=n.isEncoding||function(t){switch((t=""+t)&&t.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function o(t){var e;switch(this.encoding=function(t){var e=function(t){if(!t)return"utf8";for(var e;;)switch(t){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return t;default:if(e)return;t=(""+t).toLowerCase(),e=!0}}(t);if("string"!=typeof e&&(n.isEncoding===i||!i(t)))throw new Error("Unknown encoding: "+t);return e||t}(t),this.encoding){case"utf16le":this.text=u,this.end=h,e=4;break;case"utf8":this.fillLast=a,e=4;break;case"base64":this.text=f,this.end=c,e=3;break;default:return this.write=l,void(this.end=d)}this.lastNeed=0,this.lastTotal=0,this.lastChar=n.allocUnsafe(e)}function s(t){return t<=127?0:t>>5==6?2:t>>4==14?3:t>>3==30?4:t>>6==2?-1:-2}function a(t){var e=this.lastTotal-this.lastNeed,r=function(t,e,r){if(128!=(192&e[0]))return t.lastNeed=0,"�";if(t.lastNeed>1&&e.length>1){if(128!=(192&e[1]))return t.lastNeed=1,"�";if(t.lastNeed>2&&e.length>2&&128!=(192&e[2]))return t.lastNeed=2,"�"}}(this,t);return void 0!==r?r:this.lastNeed<=t.length?(t.copy(this.lastChar,e,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal)):(t.copy(this.lastChar,e,0,t.length),void(this.lastNeed-=t.length))}function u(t,e){if((t.length-e)%2==0){var r=t.toString("utf16le",e);if(r){var n=r.charCodeAt(r.length-1);if(n>=55296&&n<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=t[t.length-2],this.lastChar[1]=t[t.length-1],r.slice(0,-1)}return r}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=t[t.length-1],t.toString("utf16le",e,t.length-1)}function h(t){var e=t&&t.length?this.write(t):"";if(this.lastNeed){var r=this.lastTotal-this.lastNeed;return e+this.lastChar.toString("utf16le",0,r)}return e}function f(t,e){var r=(t.length-e)%3;return 0===r?t.toString("base64",e):(this.lastNeed=3-r,this.lastTotal=3,1===r?this.lastChar[0]=t[t.length-1]:(this.lastChar[0]=t[t.length-2],this.lastChar[1]=t[t.length-1]),t.toString("base64",e,t.length-r))}function c(t){var e=t&&t.length?this.write(t):"";return this.lastNeed?e+this.lastChar.toString("base64",0,3-this.lastNeed):e}function l(t){return t.toString(this.encoding)}function d(t){return t&&t.length?this.write(t):""}e.StringDecoder=o,o.prototype.write=function(t){if(0===t.length)return"";var e,r;if(this.lastNeed){if(void 0===(e=this.fillLast(t)))return"";r=this.lastNeed,this.lastNeed=0}else r=0;return r<t.length?e?e+this.text(t,r):this.text(t,r):e||""},o.prototype.end=function(t){var e=t&&t.length?this.write(t):"";return this.lastNeed?e+"�":e},o.prototype.text=function(t,e){var r=function(t,e,r){var n=e.length-1;if(n<r)return 0;var i=s(e[n]);if(i>=0)return i>0&&(t.lastNeed=i-1),i;if(--n<r||-2===i)return 0;if((i=s(e[n]))>=0)return i>0&&(t.lastNeed=i-2),i;if(--n<r||-2===i)return 0;if((i=s(e[n]))>=0)return i>0&&(2===i?i=0:t.lastNeed=i-3),i;return 0}(this,t,e);if(!this.lastNeed)return t.toString("utf8",e);this.lastTotal=r;var n=t.length-(r-this.lastNeed);return t.copy(this.lastChar,0,n),t.toString("utf8",e,n)},o.prototype.fillLast=function(t){if(this.lastNeed<=t.length)return t.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);t.copy(this.lastChar,this.lastTotal-this.lastNeed,0,t.length),this.lastNeed-=t.length}},function(t,e,r){"use strict";t.exports=s;var n=r(8),i=Object.create(r(9));function o(t,e){var r=this._transformState;r.transforming=!1;var n=r.writecb;if(!n)return this.emit("error",new Error("write callback called multiple times"));r.writechunk=null,r.writecb=null,null!=e&&this.push(e),n(t);var i=this._readableState;i.reading=!1,(i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}function s(t){if(!(this instanceof s))return new s(t);n.call(this,t),this._transformState={afterTransform:o.bind(this),needTransform:!1,transforming:!1,writecb:null,writechunk:null,writeencoding:null},this._readableState.needReadable=!0,this._readableState.sync=!1,t&&("function"==typeof t.transform&&(this._transform=t.transform),"function"==typeof t.flush&&(this._flush=t.flush)),this.on("prefinish",a)}function a(){var t=this;"function"==typeof this._flush?this._flush((function(e,r){u(t,e,r)})):u(this,null,null)}function u(t,e,r){if(e)return t.emit("error",e);if(null!=r&&t.push(r),t._writableState.length)throw new Error("Calling transform done when ws.length != 0");if(t._transformState.transforming)throw new Error("Calling transform done when still transforming");return t.push(null)}i.inherits=r(6),i.inherits(s,n),s.prototype.push=function(t,e){return this._transformState.needTransform=!1,n.prototype.push.call(this,t,e)},s.prototype._transform=function(t,e,r){throw new Error("_transform() is not implemented")},s.prototype._write=function(t,e,r){var n=this._transformState;if(n.writecb=r,n.writechunk=t,n.writeencoding=e,!n.transforming){var i=this._readableState;(n.needTransform||i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}},s.prototype._read=function(t){var e=this._transformState;null!==e.writechunk&&e.writecb&&!e.transforming?(e.transforming=!0,this._transform(e.writechunk,e.writeencoding,e.afterTransform)):e.needTransform=!0},s.prototype._destroy=function(t,e){var r=this;n.prototype._destroy.call(this,t,(function(t){e(t),r.emit("close")}))}},function(t,e){t.exports=function(t){return t.webpackPolyfill||(t.deprecate=function(){},t.paths=[],t.children||(t.children=[]),Object.defineProperty(t,"loaded",{enumerable:!0,get:function(){return t.l}}),Object.defineProperty(t,"id",{enumerable:!0,get:function(){return t.i}}),t.webpackPolyfill=1),t}},function(t,e,r){"use strict";var n=r(13);r.o(n,"payloadId")&&r.d(e,"payloadId",(function(){return n.payloadId}));n.isNode},function(t,e,r){"use strict";r.d(e,"a",(function(){return n}));r(7),r(1);function n(){return Date.now()*Math.pow(10,3)+Math.floor(Math.random()*Math.pow(10,3))}},function(t,e,r){"use strict"},function(t,e,r){"use strict";r(36)},function(t,e,r){"use strict";r(37),r(10),r(38),r(39)},function(t,e){},function(t,e,r){"use strict";var n=r(10);n.a;n.a},function(t,e){},function(t,e,r){"use strict"},function(t,e,r){"use strict"},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getWindowMetadata=void 0;const n=r(2);e.getWindowMetadata=function(){let t,e;try{t=n.getDocumentOrThrow(),e=n.getLocationOrThrow()}catch(t){return null}function r(...e){const r=t.getElementsByTagName("meta");for(let t=0;t<r.length;t++){const n=r[t],i=["itemprop","property","name"].map(t=>n.getAttribute(t)).filter(t=>!!t&&e.includes(t));if(i.length&&i){const t=n.getAttribute("content");if(t)return t}}return""}const i=function(){let e=r("name","og:site_name","og:title","twitter:title");return e||(e=t.title),e}();return{description:r("description","og:description","twitter:description","keywords"),url:e.origin,icons:function(){const r=t.getElementsByTagName("link"),n=[];for(let t=0;t<r.length;t++){const i=r[t],o=i.getAttribute("rel");if(o&&o.toLowerCase().indexOf("icon")>-1){const t=i.getAttribute("href");if(t)if(-1===t.toLowerCase().indexOf("https:")&&-1===t.toLowerCase().indexOf("http:")&&0!==t.indexOf("//")){let r=e.protocol+"//"+e.host;if(0===t.indexOf("/"))r+=t;else{const n=e.pathname.split("/");n.pop();r+=n.join("/")+"/"+t}n.push(r)}else if(0===t.indexOf("//")){const r=e.protocol+t;n.push(r)}else n.push(t)}}return n}(),name:i}}},function(t,e,r){"use strict";(function(t){r.d(e,"a",(function(){return l}));var n=function(){for(var t=0,e=0,r=arguments.length;e<r;e++)t+=arguments[e].length;var n=Array(t),i=0;for(e=0;e<r;e++)for(var o=arguments[e],s=0,a=o.length;s<a;s++,i++)n[i]=o[s];return n},i=function(t,e,r){this.name=t,this.version=e,this.os=r,this.type="browser"},o=function(e){this.version=e,this.type="node",this.name="node",this.os=t.platform},s=function(t,e,r,n){this.name=t,this.version=e,this.os=r,this.bot=n,this.type="bot-device"},a=function(){this.type="bot",this.bot=!0,this.name="bot",this.version=null,this.os=null},u=function(){this.type="react-native",this.name="react-native",this.version=null,this.os=null},h=/(nuhk|Googlebot|Yammybot|Openbot|Slurp|MSNBot|Ask\ Jeeves\/Teoma|ia_archiver)/,f=[["aol",/AOLShield\/([0-9\._]+)/],["edge",/Edge\/([0-9\._]+)/],["edge-ios",/EdgiOS\/([0-9\._]+)/],["yandexbrowser",/YaBrowser\/([0-9\._]+)/],["kakaotalk",/KAKAOTALK\s([0-9\.]+)/],["samsung",/SamsungBrowser\/([0-9\.]+)/],["silk",/\bSilk\/([0-9._-]+)\b/],["miui",/MiuiBrowser\/([0-9\.]+)$/],["beaker",/BeakerBrowser\/([0-9\.]+)/],["edge-chromium",/EdgA?\/([0-9\.]+)/],["chromium-webview",/(?!Chrom.*OPR)wv\).*Chrom(?:e|ium)\/([0-9\.]+)(:?\s|$)/],["chrome",/(?!Chrom.*OPR)Chrom(?:e|ium)\/([0-9\.]+)(:?\s|$)/],["phantomjs",/PhantomJS\/([0-9\.]+)(:?\s|$)/],["crios",/CriOS\/([0-9\.]+)(:?\s|$)/],["firefox",/Firefox\/([0-9\.]+)(?:\s|$)/],["fxios",/FxiOS\/([0-9\.]+)/],["opera-mini",/Opera Mini.*Version\/([0-9\.]+)/],["opera",/Opera\/([0-9\.]+)(?:\s|$)/],["opera",/OPR\/([0-9\.]+)(:?\s|$)/],["ie",/Trident\/7\.0.*rv\:([0-9\.]+).*\).*Gecko$/],["ie",/MSIE\s([0-9\.]+);.*Trident\/[4-7].0/],["ie",/MSIE\s(7\.0)/],["bb10",/BB10;\sTouch.*Version\/([0-9\.]+)/],["android",/Android\s([0-9\.]+)/],["ios",/Version\/([0-9\._]+).*Mobile.*Safari.*/],["safari",/Version\/([0-9\._]+).*Safari/],["facebook",/FBAV\/([0-9\.]+)/],["instagram",/Instagram\s([0-9\.]+)/],["ios-webview",/AppleWebKit\/([0-9\.]+).*Mobile/],["ios-webview",/AppleWebKit\/([0-9\.]+).*Gecko\)$/],["searchbot",/alexa|bot|crawl(er|ing)|facebookexternalhit|feedburner|google web preview|nagios|postrank|pingdom|slurp|spider|yahoo!|yandex/]],c=[["iOS",/iP(hone|od|ad)/],["Android OS",/Android/],["BlackBerry OS",/BlackBerry|BB10/],["Windows Mobile",/IEMobile/],["Amazon OS",/Kindle/],["Windows 3.11",/Win16/],["Windows 95",/(Windows 95)|(Win95)|(Windows_95)/],["Windows 98",/(Windows 98)|(Win98)/],["Windows 2000",/(Windows NT 5.0)|(Windows 2000)/],["Windows XP",/(Windows NT 5.1)|(Windows XP)/],["Windows Server 2003",/(Windows NT 5.2)/],["Windows Vista",/(Windows NT 6.0)/],["Windows 7",/(Windows NT 6.1)/],["Windows 8",/(Windows NT 6.2)/],["Windows 8.1",/(Windows NT 6.3)/],["Windows 10",/(Windows NT 10.0)/],["Windows ME",/Windows ME/],["Open BSD",/OpenBSD/],["Sun OS",/SunOS/],["Chrome OS",/CrOS/],["Linux",/(Linux)|(X11)/],["Mac OS",/(Mac_PowerPC)|(Macintosh)/],["QNX",/QNX/],["BeOS",/BeOS/],["OS/2",/OS\/2/]];function l(e){return e?p(e):"undefined"==typeof document&&"undefined"!=typeof navigator&&"ReactNative"===navigator.product?new u:"undefined"!=typeof navigator?p(navigator.userAgent):void 0!==t&&t.version?new o(t.version.slice(1)):null}function d(t){return""!==t&&f.reduce((function(e,r){var n=r[0],i=r[1];if(e)return e;var o=i.exec(t);return!!o&&[n,o]}),!1)}function p(t){var e=d(t);if(!e)return null;var r=e[0],o=e[1];if("searchbot"===r)return new a;var u=o[1]&&o[1].split(/[._]/).slice(0,3);u?u.length<3&&(u=n(u,function(t){for(var e=[],r=0;r<t;r++)e.push("0");return e}(3-u.length))):u=[];var f=u.join("."),l=function(t){for(var e=0,r=c.length;e<r;e++){var n=c[e],i=n[0];if(n[1].exec(t))return i}return null}(t),p=h.exec(t);return p&&p[1]?new s(r,f,l,p[1]):new i(r,f,l)}}).call(this,r(4))},function(t,e,r){(function(e){var n=r(17).strict;t.exports=function(t){if(n(t)){var r=e.from(t.buffer);return t.byteLength!==t.buffer.byteLength&&(r=r.slice(t.byteOffset,t.byteOffset+t.byteLength)),r}return e.from(t)}}).call(this,r(5).Buffer)},function(t,e,r){(function(n,i){var o;
/**
 * [js-sha3]{@link https://github.com/emn178/js-sha3}
 *
 * @version 0.8.0
 * <AUTHOR> Yi-Cyuan [<EMAIL>]
 * @copyright Chen, Yi-Cyuan 2015-2018
 * @license MIT
 */!function(){"use strict";var s="input is invalid type",a="object"==typeof window,u=a?window:{};u.JS_SHA3_NO_WINDOW&&(a=!1);var h=!a&&"object"==typeof self;!u.JS_SHA3_NO_NODE_JS&&"object"==typeof n&&n.versions&&n.versions.node?u=i:h&&(u=self);var f=!u.JS_SHA3_NO_COMMON_JS&&"object"==typeof t&&t.exports,c=r(78),l=!u.JS_SHA3_NO_ARRAY_BUFFER&&"undefined"!=typeof ArrayBuffer,d="0123456789abcdef".split(""),p=[4,1024,262144,67108864],m=[0,8,16,24],g=[1,0,32898,0,32906,**********,**********,**********,32907,0,**********,0,**********,**********,32777,**********,138,0,136,0,**********,0,**********,0,**********,0,139,**********,32905,**********,32771,**********,32770,**********,128,**********,32778,0,**********,**********,**********,**********,32896,**********,**********,0,**********,**********],y=[224,256,384,512],v=[128,256],w=["hex","buffer","arrayBuffer","array","digest"],b={128:168,256:136};!u.JS_SHA3_NO_NODE_JS&&Array.isArray||(Array.isArray=function(t){return"[object Array]"===Object.prototype.toString.call(t)}),!l||!u.JS_SHA3_NO_ARRAY_BUFFER_IS_VIEW&&ArrayBuffer.isView||(ArrayBuffer.isView=function(t){return"object"==typeof t&&t.buffer&&t.buffer.constructor===ArrayBuffer});for(var _=function(t,e,r){return function(n){return new N(t,e,t).update(n)[r]()}},M=function(t,e,r){return function(n,i){return new N(t,e,i).update(n)[r]()}},S=function(t,e,r){return function(e,n,i,o){return T["cshake"+t].update(e,n,i,o)[r]()}},E=function(t,e,r){return function(e,n,i,o){return T["kmac"+t].update(e,n,i,o)[r]()}},A=function(t,e,r,n){for(var i=0;i<w.length;++i){var o=w[i];t[o]=e(r,n,o)}return t},O=function(t,e){var r=_(t,e,"hex");return r.create=function(){return new N(t,e,t)},r.update=function(t){return r.create().update(t)},A(r,_,t,e)},x=[{name:"keccak",padding:[1,256,65536,16777216],bits:y,createMethod:O},{name:"sha3",padding:[6,1536,393216,100663296],bits:y,createMethod:O},{name:"shake",padding:[31,7936,2031616,520093696],bits:v,createMethod:function(t,e){var r=M(t,e,"hex");return r.create=function(r){return new N(t,e,r)},r.update=function(t,e){return r.create(e).update(t)},A(r,M,t,e)}},{name:"cshake",padding:p,bits:v,createMethod:function(t,e){var r=b[t],n=S(t,0,"hex");return n.create=function(n,i,o){return i||o?new N(t,e,n).bytepad([i,o],r):T["shake"+t].create(n)},n.update=function(t,e,r,i){return n.create(e,r,i).update(t)},A(n,S,t,e)}},{name:"kmac",padding:p,bits:v,createMethod:function(t,e){var r=b[t],n=E(t,0,"hex");return n.create=function(n,i,o){return new I(t,e,i).bytepad(["KMAC",o],r).bytepad([n],r)},n.update=function(t,e,r,i){return n.create(t,r,i).update(e)},A(n,E,t,e)}}],T={},R=[],k=0;k<x.length;++k)for(var C=x[k],j=C.bits,L=0;L<j.length;++L){var P=C.name+"_"+j[L];if(R.push(P),T[P]=C.createMethod(j[L],C.padding),"sha3"!==C.name){var B=C.name+j[L];R.push(B),T[B]=T[P]}}function N(t,e,r){this.blocks=[],this.s=[],this.padding=e,this.outputBits=r,this.reset=!0,this.finalized=!1,this.block=0,this.start=0,this.blockCount=1600-(t<<1)>>5,this.byteCount=this.blockCount<<2,this.outputBlocks=r>>5,this.extraBytes=(31&r)>>3;for(var n=0;n<50;++n)this.s[n]=0}function I(t,e,r){N.call(this,t,e,r)}N.prototype.update=function(t){if(this.finalized)throw new Error("finalize already called");var e,r=typeof t;if("string"!==r){if("object"!==r)throw new Error(s);if(null===t)throw new Error(s);if(l&&t.constructor===ArrayBuffer)t=new Uint8Array(t);else if(!(Array.isArray(t)||l&&ArrayBuffer.isView(t)))throw new Error(s);e=!0}for(var n,i,o=this.blocks,a=this.byteCount,u=t.length,h=this.blockCount,f=0,c=this.s;f<u;){if(this.reset)for(this.reset=!1,o[0]=this.block,n=1;n<h+1;++n)o[n]=0;if(e)for(n=this.start;f<u&&n<a;++f)o[n>>2]|=t[f]<<m[3&n++];else for(n=this.start;f<u&&n<a;++f)(i=t.charCodeAt(f))<128?o[n>>2]|=i<<m[3&n++]:i<2048?(o[n>>2]|=(192|i>>6)<<m[3&n++],o[n>>2]|=(128|63&i)<<m[3&n++]):i<55296||i>=57344?(o[n>>2]|=(224|i>>12)<<m[3&n++],o[n>>2]|=(128|i>>6&63)<<m[3&n++],o[n>>2]|=(128|63&i)<<m[3&n++]):(i=65536+((1023&i)<<10|1023&t.charCodeAt(++f)),o[n>>2]|=(240|i>>18)<<m[3&n++],o[n>>2]|=(128|i>>12&63)<<m[3&n++],o[n>>2]|=(128|i>>6&63)<<m[3&n++],o[n>>2]|=(128|63&i)<<m[3&n++]);if(this.lastByteIndex=n,n>=a){for(this.start=n-a,this.block=o[h],n=0;n<h;++n)c[n]^=o[n];U(c),this.reset=!0}else this.start=n}return this},N.prototype.encode=function(t,e){var r=255&t,n=1,i=[r];for(r=255&(t>>=8);r>0;)i.unshift(r),r=255&(t>>=8),++n;return e?i.push(n):i.unshift(n),this.update(i),i.length},N.prototype.encodeString=function(t){var e,r=typeof t;if("string"!==r){if("object"!==r)throw new Error(s);if(null===t)throw new Error(s);if(l&&t.constructor===ArrayBuffer)t=new Uint8Array(t);else if(!(Array.isArray(t)||l&&ArrayBuffer.isView(t)))throw new Error(s);e=!0}var n=0,i=t.length;if(e)n=i;else for(var o=0;o<t.length;++o){var a=t.charCodeAt(o);a<128?n+=1:a<2048?n+=2:a<55296||a>=57344?n+=3:(a=65536+((1023&a)<<10|1023&t.charCodeAt(++o)),n+=4)}return n+=this.encode(8*n),this.update(t),n},N.prototype.bytepad=function(t,e){for(var r=this.encode(e),n=0;n<t.length;++n)r+=this.encodeString(t[n]);var i=e-r%e,o=[];return o.length=i,this.update(o),this},N.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var t=this.blocks,e=this.lastByteIndex,r=this.blockCount,n=this.s;if(t[e>>2]|=this.padding[3&e],this.lastByteIndex===this.byteCount)for(t[0]=t[r],e=1;e<r+1;++e)t[e]=0;for(t[r-1]|=**********,e=0;e<r;++e)n[e]^=t[e];U(n)}},N.prototype.toString=N.prototype.hex=function(){this.finalize();for(var t,e=this.blockCount,r=this.s,n=this.outputBlocks,i=this.extraBytes,o=0,s=0,a="";s<n;){for(o=0;o<e&&s<n;++o,++s)t=r[o],a+=d[t>>4&15]+d[15&t]+d[t>>12&15]+d[t>>8&15]+d[t>>20&15]+d[t>>16&15]+d[t>>28&15]+d[t>>24&15];s%e==0&&(U(r),o=0)}return i&&(t=r[o],a+=d[t>>4&15]+d[15&t],i>1&&(a+=d[t>>12&15]+d[t>>8&15]),i>2&&(a+=d[t>>20&15]+d[t>>16&15])),a},N.prototype.arrayBuffer=function(){this.finalize();var t,e=this.blockCount,r=this.s,n=this.outputBlocks,i=this.extraBytes,o=0,s=0,a=this.outputBits>>3;t=i?new ArrayBuffer(n+1<<2):new ArrayBuffer(a);for(var u=new Uint32Array(t);s<n;){for(o=0;o<e&&s<n;++o,++s)u[s]=r[o];s%e==0&&U(r)}return i&&(u[o]=r[o],t=t.slice(0,a)),t},N.prototype.buffer=N.prototype.arrayBuffer,N.prototype.digest=N.prototype.array=function(){this.finalize();for(var t,e,r=this.blockCount,n=this.s,i=this.outputBlocks,o=this.extraBytes,s=0,a=0,u=[];a<i;){for(s=0;s<r&&a<i;++s,++a)t=a<<2,e=n[s],u[t]=255&e,u[t+1]=e>>8&255,u[t+2]=e>>16&255,u[t+3]=e>>24&255;a%r==0&&U(n)}return o&&(t=a<<2,e=n[s],u[t]=255&e,o>1&&(u[t+1]=e>>8&255),o>2&&(u[t+2]=e>>16&255)),u},I.prototype=new N,I.prototype.finalize=function(){return this.encode(this.outputBits,!0),N.prototype.finalize.call(this)};var U=function(t){var e,r,n,i,o,s,a,u,h,f,c,l,d,p,m,y,v,w,b,_,M,S,E,A,O,x,T,R,k,C,j,L,P,B,N,I,U,H,D,q,W,F,Y,z,$,V,X,Z,G,J,K,Q,tt,et,rt,nt,it,ot,st,at,ut,ht,ft;for(n=0;n<48;n+=2)i=t[0]^t[10]^t[20]^t[30]^t[40],o=t[1]^t[11]^t[21]^t[31]^t[41],s=t[2]^t[12]^t[22]^t[32]^t[42],a=t[3]^t[13]^t[23]^t[33]^t[43],u=t[4]^t[14]^t[24]^t[34]^t[44],h=t[5]^t[15]^t[25]^t[35]^t[45],f=t[6]^t[16]^t[26]^t[36]^t[46],c=t[7]^t[17]^t[27]^t[37]^t[47],e=(l=t[8]^t[18]^t[28]^t[38]^t[48])^(s<<1|a>>>31),r=(d=t[9]^t[19]^t[29]^t[39]^t[49])^(a<<1|s>>>31),t[0]^=e,t[1]^=r,t[10]^=e,t[11]^=r,t[20]^=e,t[21]^=r,t[30]^=e,t[31]^=r,t[40]^=e,t[41]^=r,e=i^(u<<1|h>>>31),r=o^(h<<1|u>>>31),t[2]^=e,t[3]^=r,t[12]^=e,t[13]^=r,t[22]^=e,t[23]^=r,t[32]^=e,t[33]^=r,t[42]^=e,t[43]^=r,e=s^(f<<1|c>>>31),r=a^(c<<1|f>>>31),t[4]^=e,t[5]^=r,t[14]^=e,t[15]^=r,t[24]^=e,t[25]^=r,t[34]^=e,t[35]^=r,t[44]^=e,t[45]^=r,e=u^(l<<1|d>>>31),r=h^(d<<1|l>>>31),t[6]^=e,t[7]^=r,t[16]^=e,t[17]^=r,t[26]^=e,t[27]^=r,t[36]^=e,t[37]^=r,t[46]^=e,t[47]^=r,e=f^(i<<1|o>>>31),r=c^(o<<1|i>>>31),t[8]^=e,t[9]^=r,t[18]^=e,t[19]^=r,t[28]^=e,t[29]^=r,t[38]^=e,t[39]^=r,t[48]^=e,t[49]^=r,p=t[0],m=t[1],V=t[11]<<4|t[10]>>>28,X=t[10]<<4|t[11]>>>28,R=t[20]<<3|t[21]>>>29,k=t[21]<<3|t[20]>>>29,at=t[31]<<9|t[30]>>>23,ut=t[30]<<9|t[31]>>>23,F=t[40]<<18|t[41]>>>14,Y=t[41]<<18|t[40]>>>14,B=t[2]<<1|t[3]>>>31,N=t[3]<<1|t[2]>>>31,y=t[13]<<12|t[12]>>>20,v=t[12]<<12|t[13]>>>20,Z=t[22]<<10|t[23]>>>22,G=t[23]<<10|t[22]>>>22,C=t[33]<<13|t[32]>>>19,j=t[32]<<13|t[33]>>>19,ht=t[42]<<2|t[43]>>>30,ft=t[43]<<2|t[42]>>>30,et=t[5]<<30|t[4]>>>2,rt=t[4]<<30|t[5]>>>2,I=t[14]<<6|t[15]>>>26,U=t[15]<<6|t[14]>>>26,w=t[25]<<11|t[24]>>>21,b=t[24]<<11|t[25]>>>21,J=t[34]<<15|t[35]>>>17,K=t[35]<<15|t[34]>>>17,L=t[45]<<29|t[44]>>>3,P=t[44]<<29|t[45]>>>3,A=t[6]<<28|t[7]>>>4,O=t[7]<<28|t[6]>>>4,nt=t[17]<<23|t[16]>>>9,it=t[16]<<23|t[17]>>>9,H=t[26]<<25|t[27]>>>7,D=t[27]<<25|t[26]>>>7,_=t[36]<<21|t[37]>>>11,M=t[37]<<21|t[36]>>>11,Q=t[47]<<24|t[46]>>>8,tt=t[46]<<24|t[47]>>>8,z=t[8]<<27|t[9]>>>5,$=t[9]<<27|t[8]>>>5,x=t[18]<<20|t[19]>>>12,T=t[19]<<20|t[18]>>>12,ot=t[29]<<7|t[28]>>>25,st=t[28]<<7|t[29]>>>25,q=t[38]<<8|t[39]>>>24,W=t[39]<<8|t[38]>>>24,S=t[48]<<14|t[49]>>>18,E=t[49]<<14|t[48]>>>18,t[0]=p^~y&w,t[1]=m^~v&b,t[10]=A^~x&R,t[11]=O^~T&k,t[20]=B^~I&H,t[21]=N^~U&D,t[30]=z^~V&Z,t[31]=$^~X&G,t[40]=et^~nt&ot,t[41]=rt^~it&st,t[2]=y^~w&_,t[3]=v^~b&M,t[12]=x^~R&C,t[13]=T^~k&j,t[22]=I^~H&q,t[23]=U^~D&W,t[32]=V^~Z&J,t[33]=X^~G&K,t[42]=nt^~ot&at,t[43]=it^~st&ut,t[4]=w^~_&S,t[5]=b^~M&E,t[14]=R^~C&L,t[15]=k^~j&P,t[24]=H^~q&F,t[25]=D^~W&Y,t[34]=Z^~J&Q,t[35]=G^~K&tt,t[44]=ot^~at&ht,t[45]=st^~ut&ft,t[6]=_^~S&p,t[7]=M^~E&m,t[16]=C^~L&A,t[17]=j^~P&O,t[26]=q^~F&B,t[27]=W^~Y&N,t[36]=J^~Q&z,t[37]=K^~tt&$,t[46]=at^~ht&et,t[47]=ut^~ft&rt,t[8]=S^~p&y,t[9]=E^~m&v,t[18]=L^~A&x,t[19]=P^~O&T,t[28]=F^~B&I,t[29]=Y^~N&U,t[38]=Q^~z&V,t[39]=tt^~$&X,t[48]=ht^~et&nt,t[49]=ft^~rt&it,t[0]^=g[n],t[1]^=g[n+1]};if(f)t.exports=T;else{for(k=0;k<R.length;++k)u[R[k]]=T[R[k]];c&&(void 0===(o=function(){return T}.call(e,r,e,t))||(t.exports=o))}}()}).call(this,r(4),r(3))},function(t,e,r){"use strict";r(1),r(7);var n=r(32);r.o(n,"payloadId")&&r.d(e,"payloadId",(function(){return n.payloadId}));var i=r(33);r.d(e,"payloadId",(function(){return i.a}));r(34),r(35),r(40),r(41)},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});const n=r(48).__importDefault(r(49)),i=r(50),o=(0,r(84).getFromWindow)("XMLHttpRequest")||i.XMLHttpRequest;class s extends n.default{constructor(t){super(),this.url=t}formatError(t,e,r=-1){return{error:{message:e,code:r},id:t.id,jsonrpc:t.jsonrpc}}send(t,e){return new Promise(r=>{if("eth_subscribe"===t.method){const e=this.formatError(t,"Subscriptions are not supported by this HTTP endpoint");return this.emit("error",e),r(e)}const n=new o;let i=!1;const s=(o,s)=>{if(!i)if(n.abort(),i=!0,e)e(o,s);else{const{id:e,jsonrpc:n}=t,i=o?{id:e,jsonrpc:n,error:{message:o.message,code:o.code}}:{id:e,jsonrpc:n,result:s};this.emit("payload",i),r(i)}};n.open("POST",this.url,!0),n.setRequestHeader("Content-Type","application/json"),n.timeout=6e4,n.onerror=s,n.ontimeout=s,n.onreadystatechange=()=>{if(4===n.readyState)try{const t=JSON.parse(n.responseText);s(t.error,t.result)}catch(t){s(t)}},n.send(JSON.stringify(t))})}}e.default=s},function(t,e,r){"use strict";r.r(e),r.d(e,"__extends",(function(){return i})),r.d(e,"__assign",(function(){return o})),r.d(e,"__rest",(function(){return s})),r.d(e,"__decorate",(function(){return a})),r.d(e,"__param",(function(){return u})),r.d(e,"__metadata",(function(){return h})),r.d(e,"__awaiter",(function(){return f})),r.d(e,"__generator",(function(){return c})),r.d(e,"__exportStar",(function(){return l})),r.d(e,"__values",(function(){return d})),r.d(e,"__read",(function(){return p})),r.d(e,"__spread",(function(){return m})),r.d(e,"__await",(function(){return g})),r.d(e,"__asyncGenerator",(function(){return y})),r.d(e,"__asyncDelegator",(function(){return v})),r.d(e,"__asyncValues",(function(){return w})),r.d(e,"__makeTemplateObject",(function(){return b})),r.d(e,"__importStar",(function(){return _})),r.d(e,"__importDefault",(function(){return M}));
/*! *****************************************************************************
Copyright (c) Microsoft Corporation. All rights reserved.
Licensed under the Apache License, Version 2.0 (the "License"); you may not use
this file except in compliance with the License. You may obtain a copy of the
License at http://www.apache.org/licenses/LICENSE-2.0

THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
MERCHANTABLITY OR NON-INFRINGEMENT.

See the Apache Version 2.0 License for specific language governing permissions
and limitations under the License.
***************************************************************************** */
var n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)};function i(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}var o=function(){return(o=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var i in e=arguments[r])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)};function s(t,e){var r={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(r[n]=t[n]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(n=Object.getOwnPropertySymbols(t);i<n.length;i++)e.indexOf(n[i])<0&&(r[n[i]]=t[n[i]])}return r}function a(t,e,r,n){var i,o=arguments.length,s=o<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(i=t[a])&&(s=(o<3?i(s):o>3?i(e,r,s):i(e,r))||s);return o>3&&s&&Object.defineProperty(e,r,s),s}function u(t,e){return function(r,n){e(r,n,t)}}function h(t,e){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(t,e)}function f(t,e,r,n){return new(r||(r=Promise))((function(i,o){function s(t){try{u(n.next(t))}catch(t){o(t)}}function a(t){try{u(n.throw(t))}catch(t){o(t)}}function u(t){t.done?i(t.value):new r((function(e){e(t.value)})).then(s,a)}u((n=n.apply(t,e||[])).next())}))}function c(t,e){var r,n,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(o){return function(a){return function(o){if(r)throw new TypeError("Generator is already executing.");for(;s;)try{if(r=1,n&&(i=2&o[0]?n.return:o[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,o[1])).done)return i;switch(n=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,n=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(!(i=s.trys,(i=i.length>0&&i[i.length-1])||6!==o[0]&&2!==o[0])){s=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){s.label=o[1];break}if(6===o[0]&&s.label<i[1]){s.label=i[1],i=o;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(o);break}i[2]&&s.ops.pop(),s.trys.pop();continue}o=e.call(t,s)}catch(t){o=[6,t],n=0}finally{r=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,a])}}}function l(t,e){for(var r in t)e.hasOwnProperty(r)||(e[r]=t[r])}function d(t){var e="function"==typeof Symbol&&t[Symbol.iterator],r=0;return e?e.call(t):{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}}}function p(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,i,o=r.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(n=o.next()).done;)s.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return s}function m(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(p(arguments[e]));return t}function g(t){return this instanceof g?(this.v=t,this):new g(t)}function y(t,e,r){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n,i=r.apply(t,e||[]),o=[];return n={},s("next"),s("throw"),s("return"),n[Symbol.asyncIterator]=function(){return this},n;function s(t){i[t]&&(n[t]=function(e){return new Promise((function(r,n){o.push([t,e,r,n])>1||a(t,e)}))})}function a(t,e){try{(r=i[t](e)).value instanceof g?Promise.resolve(r.value.v).then(u,h):f(o[0][2],r)}catch(t){f(o[0][3],t)}var r}function u(t){a("next",t)}function h(t){a("throw",t)}function f(t,e){t(e),o.shift(),o.length&&a(o[0][0],o[0][1])}}function v(t){var e,r;return e={},n("next"),n("throw",(function(t){throw t})),n("return"),e[Symbol.iterator]=function(){return this},e;function n(n,i){e[n]=t[n]?function(e){return(r=!r)?{value:g(t[n](e)),done:"return"===n}:i?i(e):e}:i}}function w(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e,r=t[Symbol.asyncIterator];return r?r.call(t):(t=d(t),e={},n("next"),n("throw"),n("return"),e[Symbol.asyncIterator]=function(){return this},e);function n(r){e[r]=t[r]&&function(e){return new Promise((function(n,i){(function(t,e,r,n){Promise.resolve(n).then((function(e){t({value:e,done:r})}),e)})(n,i,(e=t[r](e)).done,e.value)}))}}}function b(t,e){return Object.defineProperty?Object.defineProperty(t,"raw",{value:e}):t.raw=e,t}function _(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var r in t)Object.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e.default=t,e}function M(t){return t&&t.__esModule?t:{default:t}}},function(t,e,r){"use strict";var n=Object.prototype.hasOwnProperty,i="~";function o(){}function s(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function a(t,e,r,n,o){if("function"!=typeof r)throw new TypeError("The listener must be a function");var a=new s(r,n||t,o),u=i?i+e:e;return t._events[u]?t._events[u].fn?t._events[u]=[t._events[u],a]:t._events[u].push(a):(t._events[u]=a,t._eventsCount++),t}function u(t,e){0==--t._eventsCount?t._events=new o:delete t._events[e]}function h(){this._events=new o,this._eventsCount=0}Object.create&&(o.prototype=Object.create(null),(new o).__proto__||(i=!1)),h.prototype.eventNames=function(){var t,e,r=[];if(0===this._eventsCount)return r;for(e in t=this._events)n.call(t,e)&&r.push(i?e.slice(1):e);return Object.getOwnPropertySymbols?r.concat(Object.getOwnPropertySymbols(t)):r},h.prototype.listeners=function(t){var e=i?i+t:t,r=this._events[e];if(!r)return[];if(r.fn)return[r.fn];for(var n=0,o=r.length,s=new Array(o);n<o;n++)s[n]=r[n].fn;return s},h.prototype.listenerCount=function(t){var e=i?i+t:t,r=this._events[e];return r?r.fn?1:r.length:0},h.prototype.emit=function(t,e,r,n,o,s){var a=i?i+t:t;if(!this._events[a])return!1;var u,h,f=this._events[a],c=arguments.length;if(f.fn){switch(f.once&&this.removeListener(t,f.fn,void 0,!0),c){case 1:return f.fn.call(f.context),!0;case 2:return f.fn.call(f.context,e),!0;case 3:return f.fn.call(f.context,e,r),!0;case 4:return f.fn.call(f.context,e,r,n),!0;case 5:return f.fn.call(f.context,e,r,n,o),!0;case 6:return f.fn.call(f.context,e,r,n,o,s),!0}for(h=1,u=new Array(c-1);h<c;h++)u[h-1]=arguments[h];f.fn.apply(f.context,u)}else{var l,d=f.length;for(h=0;h<d;h++)switch(f[h].once&&this.removeListener(t,f[h].fn,void 0,!0),c){case 1:f[h].fn.call(f[h].context);break;case 2:f[h].fn.call(f[h].context,e);break;case 3:f[h].fn.call(f[h].context,e,r);break;case 4:f[h].fn.call(f[h].context,e,r,n);break;default:if(!u)for(l=1,u=new Array(c-1);l<c;l++)u[l-1]=arguments[l];f[h].fn.apply(f[h].context,u)}}return!0},h.prototype.on=function(t,e,r){return a(this,t,e,r,!1)},h.prototype.once=function(t,e,r){return a(this,t,e,r,!0)},h.prototype.removeListener=function(t,e,r,n){var o=i?i+t:t;if(!this._events[o])return this;if(!e)return u(this,o),this;var s=this._events[o];if(s.fn)s.fn!==e||n&&!s.once||r&&s.context!==r||u(this,o);else{for(var a=0,h=[],f=s.length;a<f;a++)(s[a].fn!==e||n&&!s[a].once||r&&s[a].context!==r)&&h.push(s[a]);h.length?this._events[o]=1===h.length?h[0]:h:u(this,o)}return this},h.prototype.removeAllListeners=function(t){var e;return t?(e=i?i+t:t,this._events[e]&&u(this,e)):(this._events=new o,this._eventsCount=0),this},h.prototype.off=h.prototype.removeListener,h.prototype.addListener=h.prototype.on,h.prefixed=i,h.EventEmitter=h,t.exports=h},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),function(t){for(var r in t)e.hasOwnProperty(r)||(e[r]=t[r])}(r(51));var n=r(16);e.XMLHttpRequestEventTarget=n.XMLHttpRequestEventTarget},function(t,e,r){"use strict";(function(t,n){var i,o=this&&this.__extends||(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},function(t,e){function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),s=this&&this.__assign||Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var i in e=arguments[r])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t};Object.defineProperty(e,"__esModule",{value:!0});var a=r(20),u=r(71),h=r(72),f=r(15),c=r(73),l=r(74),d=r(16),p=r(75),m=r(76),g=function(e){function r(n){void 0===n&&(n={});var i=e.call(this)||this;return i.UNSENT=r.UNSENT,i.OPENED=r.OPENED,i.HEADERS_RECEIVED=r.HEADERS_RECEIVED,i.LOADING=r.LOADING,i.DONE=r.DONE,i.onreadystatechange=null,i.readyState=r.UNSENT,i.response=null,i.responseText="",i.responseType="",i.status=0,i.statusText="",i.timeout=0,i.upload=new p.XMLHttpRequestUpload,i.responseUrl="",i.withCredentials=!1,i._method=null,i._url=null,i._sync=!1,i._headers={},i._loweredHeaders={},i._mimeOverride=null,i._request=null,i._response=null,i._responseParts=null,i._responseHeaders=null,i._aborting=null,i._error=null,i._loadedBytes=0,i._totalBytes=0,i._lengthComputable=!1,i._restrictedMethods={CONNECT:!0,TRACE:!0,TRACK:!0},i._restrictedHeaders={"accept-charset":!0,"accept-encoding":!0,"access-control-request-headers":!0,"access-control-request-method":!0,connection:!0,"content-length":!0,cookie:!0,cookie2:!0,date:!0,dnt:!0,expect:!0,host:!0,"keep-alive":!0,origin:!0,referer:!0,te:!0,trailer:!0,"transfer-encoding":!0,upgrade:!0,"user-agent":!0,via:!0},i._privateHeaders={"set-cookie":!0,"set-cookie2":!0},i._userAgent="Mozilla/5.0 ("+h.type()+" "+h.arch()+") node.js/"+t.versions.node+" v8/"+t.versions.v8,i._anonymous=n.anon||!1,i}return o(r,e),r.prototype.open=function(t,e,n,i,o){if(void 0===n&&(n=!0),t=t.toUpperCase(),this._restrictedMethods[t])throw new r.SecurityError("HTTP method "+t+" is not allowed in XHR");var s=this._parseUrl(e,i,o);this.readyState===r.HEADERS_RECEIVED||(this.readyState,r.LOADING),this._method=t,this._url=s,this._sync=!n,this._headers={},this._loweredHeaders={},this._mimeOverride=null,this._setReadyState(r.OPENED),this._request=null,this._response=null,this.status=0,this.statusText="",this._responseParts=[],this._responseHeaders=null,this._loadedBytes=0,this._totalBytes=0,this._lengthComputable=!1},r.prototype.setRequestHeader=function(t,e){if(this.readyState!==r.OPENED)throw new r.InvalidStateError("XHR readyState must be OPENED");var n=t.toLowerCase();this._restrictedHeaders[n]||/^sec-/.test(n)||/^proxy-/.test(n)?console.warn('Refused to set unsafe header "'+t+'"'):(e=e.toString(),null!=this._loweredHeaders[n]?(t=this._loweredHeaders[n],this._headers[t]=this._headers[t]+", "+e):(this._loweredHeaders[n]=t,this._headers[t]=e))},r.prototype.send=function(t){if(this.readyState!==r.OPENED)throw new r.InvalidStateError("XHR readyState must be OPENED");if(this._request)throw new r.InvalidStateError("send() already called");switch(this._url.protocol){case"file:":return this._sendFile(t);case"http:":case"https:":return this._sendHttp(t);default:throw new r.NetworkError("Unsupported protocol "+this._url.protocol)}},r.prototype.abort=function(){null!=this._request&&(this._request.abort(),this._setError(),this._dispatchProgress("abort"),this._dispatchProgress("loadend"))},r.prototype.getResponseHeader=function(t){if(null==this._responseHeaders||null==t)return null;var e=t.toLowerCase();return this._responseHeaders.hasOwnProperty(e)?this._responseHeaders[t.toLowerCase()]:null},r.prototype.getAllResponseHeaders=function(){var t=this;return null==this._responseHeaders?"":Object.keys(this._responseHeaders).map((function(e){return e+": "+t._responseHeaders[e]})).join("\r\n")},r.prototype.overrideMimeType=function(t){if(this.readyState===r.LOADING||this.readyState===r.DONE)throw new r.InvalidStateError("overrideMimeType() not allowed in LOADING or DONE");this._mimeOverride=t.toLowerCase()},r.prototype.nodejsSet=function(t){if(this.nodejsHttpAgent=t.httpAgent||this.nodejsHttpAgent,this.nodejsHttpsAgent=t.httpsAgent||this.nodejsHttpsAgent,t.hasOwnProperty("baseUrl")){if(null!=t.baseUrl)if(!f.parse(t.baseUrl,!1,!0).protocol)throw new r.SyntaxError("baseUrl must be an absolute URL");this.nodejsBaseUrl=t.baseUrl}},r.nodejsSet=function(t){r.prototype.nodejsSet(t)},r.prototype._setReadyState=function(t){this.readyState=t,this.dispatchEvent(new c.ProgressEvent("readystatechange"))},r.prototype._sendFile=function(t){throw new Error("Protocol file: not implemented")},r.prototype._sendHttp=function(t){if(this._sync)throw new Error("Synchronous XHR processing not implemented");!t||"GET"!==this._method&&"HEAD"!==this._method?t=t||"":(console.warn("Discarding entity body for "+this._method+" requests"),t=null),this.upload._setData(t),this._finalizeHeaders(),this._sendHxxpRequest()},r.prototype._sendHxxpRequest=function(){var t=this;if(this.withCredentials){var e=r.cookieJar.getCookies(m.CookieAccessInfo(this._url.hostname,this._url.pathname,"https:"===this._url.protocol)).toValueString();this._headers.cookie=this._headers.cookie2=e}var n="http:"===this._url.protocol?[a,this.nodejsHttpAgent]:[u,this.nodejsHttpsAgent],i=n[0],o=n[1],s=i.request.bind(i)({hostname:this._url.hostname,port:+this._url.port,path:this._url.path,auth:this._url.auth,method:this._method,headers:this._headers,agent:o});this._request=s,this.timeout&&s.setTimeout(this.timeout,(function(){return t._onHttpTimeout(s)})),s.on("response",(function(e){return t._onHttpResponse(s,e)})),s.on("error",(function(e){return t._onHttpRequestError(s,e)})),this.upload._startUpload(s),this._request===s&&this._dispatchProgress("loadstart")},r.prototype._finalizeHeaders=function(){this._headers=s({},this._headers,{Connection:"keep-alive",Host:this._url.host,"User-Agent":this._userAgent},this._anonymous?{Referer:"about:blank"}:{}),this.upload._finalizeHeaders(this._headers,this._loweredHeaders)},r.prototype._onHttpResponse=function(t,e){var n=this;if(this._request===t){if(this.withCredentials&&(e.headers["set-cookie"]||e.headers["set-cookie2"])&&r.cookieJar.setCookies(e.headers["set-cookie"]||e.headers["set-cookie2"]),[301,302,303,307,308].indexOf(e.statusCode)>=0)return this._url=this._parseUrl(e.headers.location),this._method="GET",this._loweredHeaders["content-type"]&&(delete this._headers[this._loweredHeaders["content-type"]],delete this._loweredHeaders["content-type"]),null!=this._headers["Content-Type"]&&delete this._headers["Content-Type"],delete this._headers["Content-Length"],this.upload._reset(),this._finalizeHeaders(),void this._sendHxxpRequest();this._response=e,this._response.on("data",(function(t){return n._onHttpResponseData(e,t)})),this._response.on("end",(function(){return n._onHttpResponseEnd(e)})),this._response.on("close",(function(){return n._onHttpResponseClose(e)})),this.responseUrl=this._url.href.split("#")[0],this.status=e.statusCode,this.statusText=a.STATUS_CODES[this.status],this._parseResponseHeaders(e);var i=this._responseHeaders["content-length"]||"";this._totalBytes=+i,this._lengthComputable=!!i,this._setReadyState(r.HEADERS_RECEIVED)}},r.prototype._onHttpResponseData=function(t,e){this._response===t&&(this._responseParts.push(new n(e)),this._loadedBytes+=e.length,this.readyState!==r.LOADING&&this._setReadyState(r.LOADING),this._dispatchProgress("progress"))},r.prototype._onHttpResponseEnd=function(t){this._response===t&&(this._parseResponse(),this._request=null,this._response=null,this._setReadyState(r.DONE),this._dispatchProgress("load"),this._dispatchProgress("loadend"))},r.prototype._onHttpResponseClose=function(t){if(this._response===t){var e=this._request;this._setError(),e.abort(),this._setReadyState(r.DONE),this._dispatchProgress("error"),this._dispatchProgress("loadend")}},r.prototype._onHttpTimeout=function(t){this._request===t&&(this._setError(),t.abort(),this._setReadyState(r.DONE),this._dispatchProgress("timeout"),this._dispatchProgress("loadend"))},r.prototype._onHttpRequestError=function(t,e){this._request===t&&(this._setError(),t.abort(),this._setReadyState(r.DONE),this._dispatchProgress("error"),this._dispatchProgress("loadend"))},r.prototype._dispatchProgress=function(t){var e=new r.ProgressEvent(t);e.lengthComputable=this._lengthComputable,e.loaded=this._loadedBytes,e.total=this._totalBytes,this.dispatchEvent(e)},r.prototype._setError=function(){this._request=null,this._response=null,this._responseHeaders=null,this._responseParts=null},r.prototype._parseUrl=function(t,e,r){var n=null==this.nodejsBaseUrl?t:f.resolve(this.nodejsBaseUrl,t),i=f.parse(n,!1,!0);i.hash=null;var o=(i.auth||"").split(":"),s=o[0],a=o[1];return(s||a||e||r)&&(i.auth=(e||s||"")+":"+(r||a||"")),i},r.prototype._parseResponseHeaders=function(t){for(var e in this._responseHeaders={},t.headers){var r=e.toLowerCase();this._privateHeaders[r]||(this._responseHeaders[r]=t.headers[e])}null!=this._mimeOverride&&(this._responseHeaders["content-type"]=this._mimeOverride)},r.prototype._parseResponse=function(){var t=n.concat(this._responseParts);switch(this._responseParts=null,this.responseType){case"json":this.responseText=null;try{this.response=JSON.parse(t.toString("utf-8"))}catch(t){this.response=null}return;case"buffer":return this.responseText=null,void(this.response=t);case"arraybuffer":this.responseText=null;for(var e=new ArrayBuffer(t.length),r=new Uint8Array(e),i=0;i<t.length;i++)r[i]=t[i];return void(this.response=e);case"text":default:try{this.responseText=t.toString(this._parseResponseEncoding())}catch(e){this.responseText=t.toString("binary")}this.response=this.responseText}},r.prototype._parseResponseEncoding=function(){return/;\s*charset=(.*)$/.exec(this._responseHeaders["content-type"]||"")[1]||"utf-8"},r.ProgressEvent=c.ProgressEvent,r.InvalidStateError=l.InvalidStateError,r.NetworkError=l.NetworkError,r.SecurityError=l.SecurityError,r.SyntaxError=l.SyntaxError,r.XMLHttpRequestUpload=p.XMLHttpRequestUpload,r.UNSENT=0,r.OPENED=1,r.HEADERS_RECEIVED=2,r.LOADING=3,r.DONE=4,r.cookieJar=m.CookieJar(),r}(d.XMLHttpRequestEventTarget);e.XMLHttpRequest=g,g.prototype.nodejsHttpAgent=a.globalAgent,g.prototype.nodejsHttpsAgent=u.globalAgent,g.prototype.nodejsBaseUrl=null}).call(this,r(4),r(5).Buffer)},function(t,e,r){"use strict";e.byteLength=function(t){var e=h(t),r=e[0],n=e[1];return 3*(r+n)/4-n},e.toByteArray=function(t){var e,r,n=h(t),s=n[0],a=n[1],u=new o(function(t,e,r){return 3*(e+r)/4-r}(0,s,a)),f=0,c=a>0?s-4:s;for(r=0;r<c;r+=4)e=i[t.charCodeAt(r)]<<18|i[t.charCodeAt(r+1)]<<12|i[t.charCodeAt(r+2)]<<6|i[t.charCodeAt(r+3)],u[f++]=e>>16&255,u[f++]=e>>8&255,u[f++]=255&e;2===a&&(e=i[t.charCodeAt(r)]<<2|i[t.charCodeAt(r+1)]>>4,u[f++]=255&e);1===a&&(e=i[t.charCodeAt(r)]<<10|i[t.charCodeAt(r+1)]<<4|i[t.charCodeAt(r+2)]>>2,u[f++]=e>>8&255,u[f++]=255&e);return u},e.fromByteArray=function(t){for(var e,r=t.length,i=r%3,o=[],s=0,a=r-i;s<a;s+=16383)o.push(f(t,s,s+16383>a?a:s+16383));1===i?(e=t[r-1],o.push(n[e>>2]+n[e<<4&63]+"==")):2===i&&(e=(t[r-2]<<8)+t[r-1],o.push(n[e>>10]+n[e>>4&63]+n[e<<2&63]+"="));return o.join("")};for(var n=[],i=[],o="undefined"!=typeof Uint8Array?Uint8Array:Array,s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",a=0,u=s.length;a<u;++a)n[a]=s[a],i[s.charCodeAt(a)]=a;function h(t){var e=t.length;if(e%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var r=t.indexOf("=");return-1===r&&(r=e),[r,r===e?0:4-r%4]}function f(t,e,r){for(var i,o,s=[],a=e;a<r;a+=3)i=(t[a]<<16&16711680)+(t[a+1]<<8&65280)+(255&t[a+2]),s.push(n[(o=i)>>18&63]+n[o>>12&63]+n[o>>6&63]+n[63&o]);return s.join("")}i["-".charCodeAt(0)]=62,i["_".charCodeAt(0)]=63},function(t,e){
/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */
e.read=function(t,e,r,n,i){var o,s,a=8*i-n-1,u=(1<<a)-1,h=u>>1,f=-7,c=r?i-1:0,l=r?-1:1,d=t[e+c];for(c+=l,o=d&(1<<-f)-1,d>>=-f,f+=a;f>0;o=256*o+t[e+c],c+=l,f-=8);for(s=o&(1<<-f)-1,o>>=-f,f+=n;f>0;s=256*s+t[e+c],c+=l,f-=8);if(0===o)o=1-h;else{if(o===u)return s?NaN:1/0*(d?-1:1);s+=Math.pow(2,n),o-=h}return(d?-1:1)*s*Math.pow(2,o-n)},e.write=function(t,e,r,n,i,o){var s,a,u,h=8*o-i-1,f=(1<<h)-1,c=f>>1,l=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,d=n?0:o-1,p=n?1:-1,m=e<0||0===e&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(a=isNaN(e)?1:0,s=f):(s=Math.floor(Math.log(e)/Math.LN2),e*(u=Math.pow(2,-s))<1&&(s--,u*=2),(e+=s+c>=1?l/u:l*Math.pow(2,1-c))*u>=2&&(s++,u/=2),s+c>=f?(a=0,s=f):s+c>=1?(a=(e*u-1)*Math.pow(2,i),s+=c):(a=e*Math.pow(2,c-1)*Math.pow(2,i),s=0));i>=8;t[r+d]=255&a,d+=p,a/=256,i-=8);for(s=s<<i|a,h+=i;h>0;t[r+d]=255&s,d+=p,s/=256,h-=8);t[r+d-p]|=128*m}},function(t,e,r){(function(e,n,i){var o=r(21),s=r(6),a=r(22),u=r(23),h=r(63),f=a.IncomingMessage,c=a.readyStates;var l=t.exports=function(t){var r,n=this;u.Writable.call(n),n._opts=t,n._body=[],n._headers={},t.auth&&n.setHeader("Authorization","Basic "+new e(t.auth).toString("base64")),Object.keys(t.headers).forEach((function(e){n.setHeader(e,t.headers[e])}));var i=!0;if("disable-fetch"===t.mode||"requestTimeout"in t&&!o.abortController)i=!1,r=!0;else if("prefer-streaming"===t.mode)r=!1;else if("allow-wrong-content-type"===t.mode)r=!o.overrideMimeType;else{if(t.mode&&"default"!==t.mode&&"prefer-fast"!==t.mode)throw new Error("Invalid value for opts.mode");r=!0}n._mode=function(t,e){return o.fetch&&e?"fetch":o.mozchunkedarraybuffer?"moz-chunked-arraybuffer":o.msstream?"ms-stream":o.arraybuffer&&t?"arraybuffer":o.vbArray&&t?"text:vbarray":"text"}(r,i),n._fetchTimer=null,n.on("finish",(function(){n._onFinish()}))};s(l,u.Writable),l.prototype.setHeader=function(t,e){var r=t.toLowerCase();-1===d.indexOf(r)&&(this._headers[r]={name:t,value:e})},l.prototype.getHeader=function(t){var e=this._headers[t.toLowerCase()];return e?e.value:null},l.prototype.removeHeader=function(t){delete this._headers[t.toLowerCase()]},l.prototype._onFinish=function(){var t=this;if(!t._destroyed){var r=t._opts,s=t._headers,a=null;"GET"!==r.method&&"HEAD"!==r.method&&(a=o.arraybuffer?h(e.concat(t._body)):o.blobConstructor?new n.Blob(t._body.map((function(t){return h(t)})),{type:(s["content-type"]||{}).value||""}):e.concat(t._body).toString());var u=[];if(Object.keys(s).forEach((function(t){var e=s[t].name,r=s[t].value;Array.isArray(r)?r.forEach((function(t){u.push([e,t])})):u.push([e,r])})),"fetch"===t._mode){var f=null;if(o.abortController){var l=new AbortController;f=l.signal,t._fetchAbortController=l,"requestTimeout"in r&&0!==r.requestTimeout&&(t._fetchTimer=n.setTimeout((function(){t.emit("requestTimeout"),t._fetchAbortController&&t._fetchAbortController.abort()}),r.requestTimeout))}n.fetch(t._opts.url,{method:t._opts.method,headers:u,body:a||void 0,mode:"cors",credentials:r.withCredentials?"include":"same-origin",signal:f}).then((function(e){t._fetchResponse=e,t._connect()}),(function(e){n.clearTimeout(t._fetchTimer),t._destroyed||t.emit("error",e)}))}else{var d=t._xhr=new n.XMLHttpRequest;try{d.open(t._opts.method,t._opts.url,!0)}catch(e){return void i.nextTick((function(){t.emit("error",e)}))}"responseType"in d&&(d.responseType=t._mode.split(":")[0]),"withCredentials"in d&&(d.withCredentials=!!r.withCredentials),"text"===t._mode&&"overrideMimeType"in d&&d.overrideMimeType("text/plain; charset=x-user-defined"),"requestTimeout"in r&&(d.timeout=r.requestTimeout,d.ontimeout=function(){t.emit("requestTimeout")}),u.forEach((function(t){d.setRequestHeader(t[0],t[1])})),t._response=null,d.onreadystatechange=function(){switch(d.readyState){case c.LOADING:case c.DONE:t._onXHRProgress()}},"moz-chunked-arraybuffer"===t._mode&&(d.onprogress=function(){t._onXHRProgress()}),d.onerror=function(){t._destroyed||t.emit("error",new Error("XHR error"))};try{d.send(a)}catch(e){return void i.nextTick((function(){t.emit("error",e)}))}}}},l.prototype._onXHRProgress=function(){(function(t){try{var e=t.status;return null!==e&&0!==e}catch(t){return!1}})(this._xhr)&&!this._destroyed&&(this._response||this._connect(),this._response._onXHRProgress())},l.prototype._connect=function(){var t=this;t._destroyed||(t._response=new f(t._xhr,t._fetchResponse,t._mode,t._fetchTimer),t._response.on("error",(function(e){t.emit("error",e)})),t.emit("response",t._response))},l.prototype._write=function(t,e,r){this._body.push(t),r()},l.prototype.abort=l.prototype.destroy=function(){this._destroyed=!0,n.clearTimeout(this._fetchTimer),this._response&&(this._response._destroyed=!0),this._xhr?this._xhr.abort():this._fetchAbortController&&this._fetchAbortController.abort()},l.prototype.end=function(t,e,r){"function"==typeof t&&(r=t,t=void 0),u.Writable.prototype.end.call(this,t,e,r)},l.prototype.flushHeaders=function(){},l.prototype.setTimeout=function(){},l.prototype.setNoDelay=function(){},l.prototype.setSocketKeepAlive=function(){};var d=["accept-charset","accept-encoding","access-control-request-headers","access-control-request-method","connection","content-length","cookie","cookie2","date","dnt","expect","host","keep-alive","origin","referer","te","trailer","transfer-encoding","upgrade","via"]}).call(this,r(5).Buffer,r(3),r(4))},function(t,e){},function(t,e,r){"use strict";var n=r(14).Buffer,i=r(57);t.exports=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.head=null,this.tail=null,this.length=0}return t.prototype.push=function(t){var e={data:t,next:null};this.length>0?this.tail.next=e:this.head=e,this.tail=e,++this.length},t.prototype.unshift=function(t){var e={data:t,next:this.head};0===this.length&&(this.tail=e),this.head=e,++this.length},t.prototype.shift=function(){if(0!==this.length){var t=this.head.data;return 1===this.length?this.head=this.tail=null:this.head=this.head.next,--this.length,t}},t.prototype.clear=function(){this.head=this.tail=null,this.length=0},t.prototype.join=function(t){if(0===this.length)return"";for(var e=this.head,r=""+e.data;e=e.next;)r+=t+e.data;return r},t.prototype.concat=function(t){if(0===this.length)return n.alloc(0);if(1===this.length)return this.head.data;for(var e,r,i,o=n.allocUnsafe(t>>>0),s=this.head,a=0;s;)e=s.data,r=o,i=a,e.copy(r,i),a+=s.data.length,s=s.next;return o},t}(),i&&i.inspect&&i.inspect.custom&&(t.exports.prototype[i.inspect.custom]=function(){var t=i.inspect({length:this.length});return this.constructor.name+" "+t})},function(t,e){},function(t,e,r){(function(t){var n=void 0!==t&&t||"undefined"!=typeof self&&self||window,i=Function.prototype.apply;function o(t,e){this._id=t,this._clearFn=e}e.setTimeout=function(){return new o(i.call(setTimeout,n,arguments),clearTimeout)},e.setInterval=function(){return new o(i.call(setInterval,n,arguments),clearInterval)},e.clearTimeout=e.clearInterval=function(t){t&&t.close()},o.prototype.unref=o.prototype.ref=function(){},o.prototype.close=function(){this._clearFn.call(n,this._id)},e.enroll=function(t,e){clearTimeout(t._idleTimeoutId),t._idleTimeout=e},e.unenroll=function(t){clearTimeout(t._idleTimeoutId),t._idleTimeout=-1},e._unrefActive=e.active=function(t){clearTimeout(t._idleTimeoutId);var e=t._idleTimeout;e>=0&&(t._idleTimeoutId=setTimeout((function(){t._onTimeout&&t._onTimeout()}),e))},r(59),e.setImmediate="undefined"!=typeof self&&self.setImmediate||void 0!==t&&t.setImmediate||this&&this.setImmediate,e.clearImmediate="undefined"!=typeof self&&self.clearImmediate||void 0!==t&&t.clearImmediate||this&&this.clearImmediate}).call(this,r(3))},function(t,e,r){(function(t,e){!function(t,r){"use strict";if(!t.setImmediate){var n,i,o,s,a,u=1,h={},f=!1,c=t.document,l=Object.getPrototypeOf&&Object.getPrototypeOf(t);l=l&&l.setTimeout?l:t,"[object process]"==={}.toString.call(t.process)?n=function(t){e.nextTick((function(){p(t)}))}:!function(){if(t.postMessage&&!t.importScripts){var e=!0,r=t.onmessage;return t.onmessage=function(){e=!1},t.postMessage("","*"),t.onmessage=r,e}}()?t.MessageChannel?((o=new MessageChannel).port1.onmessage=function(t){p(t.data)},n=function(t){o.port2.postMessage(t)}):c&&"onreadystatechange"in c.createElement("script")?(i=c.documentElement,n=function(t){var e=c.createElement("script");e.onreadystatechange=function(){p(t),e.onreadystatechange=null,i.removeChild(e),e=null},i.appendChild(e)}):n=function(t){setTimeout(p,0,t)}:(s="setImmediate$"+Math.random()+"$",a=function(e){e.source===t&&"string"==typeof e.data&&0===e.data.indexOf(s)&&p(+e.data.slice(s.length))},t.addEventListener?t.addEventListener("message",a,!1):t.attachEvent("onmessage",a),n=function(e){t.postMessage(s+e,"*")}),l.setImmediate=function(t){"function"!=typeof t&&(t=new Function(""+t));for(var e=new Array(arguments.length-1),r=0;r<e.length;r++)e[r]=arguments[r+1];var i={callback:t,args:e};return h[u]=i,n(u),u++},l.clearImmediate=d}function d(t){delete h[t]}function p(t){if(f)setTimeout(p,0,t);else{var e=h[t];if(e){f=!0;try{!function(t){var e=t.callback,r=t.args;switch(r.length){case 0:e();break;case 1:e(r[0]);break;case 2:e(r[0],r[1]);break;case 3:e(r[0],r[1],r[2]);break;default:e.apply(void 0,r)}}(e)}finally{d(t),f=!1}}}}}("undefined"==typeof self?void 0===t?this:t:self)}).call(this,r(3),r(4))},function(t,e,r){(function(e){function r(t){try{if(!e.localStorage)return!1}catch(t){return!1}var r=e.localStorage[t];return null!=r&&"true"===String(r).toLowerCase()}t.exports=function(t,e){if(r("noDeprecation"))return t;var n=!1;return function(){if(!n){if(r("throwDeprecation"))throw new Error(e);r("traceDeprecation")?console.trace(e):console.warn(e),n=!0}return t.apply(this,arguments)}}}).call(this,r(3))},function(t,e,r){var n=r(5),i=n.Buffer;function o(t,e){for(var r in t)e[r]=t[r]}function s(t,e,r){return i(t,e,r)}i.from&&i.alloc&&i.allocUnsafe&&i.allocUnsafeSlow?t.exports=n:(o(n,e),e.Buffer=s),o(i,s),s.from=function(t,e,r){if("number"==typeof t)throw new TypeError("Argument must not be a number");return i(t,e,r)},s.alloc=function(t,e,r){if("number"!=typeof t)throw new TypeError("Argument must be a number");var n=i(t);return void 0!==e?"string"==typeof r?n.fill(e,r):n.fill(e):n.fill(0),n},s.allocUnsafe=function(t){if("number"!=typeof t)throw new TypeError("Argument must be a number");return i(t)},s.allocUnsafeSlow=function(t){if("number"!=typeof t)throw new TypeError("Argument must be a number");return n.SlowBuffer(t)}},function(t,e,r){"use strict";t.exports=o;var n=r(30),i=Object.create(r(9));function o(t){if(!(this instanceof o))return new o(t);n.call(this,t)}i.inherits=r(6),i.inherits(o,n),o.prototype._transform=function(t,e,r){r(null,t)}},function(t,e,r){var n=r(5).Buffer;t.exports=function(t){if(t instanceof Uint8Array){if(0===t.byteOffset&&t.byteLength===t.buffer.byteLength)return t.buffer;if("function"==typeof t.buffer.slice)return t.buffer.slice(t.byteOffset,t.byteOffset+t.byteLength)}if(n.isBuffer(t)){for(var e=new Uint8Array(t.length),r=t.length,i=0;i<r;i++)e[i]=t[i];return e.buffer}throw new Error("Argument must be a Buffer")}},function(t,e){t.exports=function(){for(var t={},e=0;e<arguments.length;e++){var n=arguments[e];for(var i in n)r.call(n,i)&&(t[i]=n[i])}return t};var r=Object.prototype.hasOwnProperty},function(t,e){t.exports={100:"Continue",101:"Switching Protocols",102:"Processing",200:"OK",201:"Created",202:"Accepted",203:"Non-Authoritative Information",204:"No Content",205:"Reset Content",206:"Partial Content",207:"Multi-Status",208:"Already Reported",226:"IM Used",300:"Multiple Choices",301:"Moved Permanently",302:"Found",303:"See Other",304:"Not Modified",305:"Use Proxy",307:"Temporary Redirect",308:"Permanent Redirect",400:"Bad Request",401:"Unauthorized",402:"Payment Required",403:"Forbidden",404:"Not Found",405:"Method Not Allowed",406:"Not Acceptable",407:"Proxy Authentication Required",408:"Request Timeout",409:"Conflict",410:"Gone",411:"Length Required",412:"Precondition Failed",413:"Payload Too Large",414:"URI Too Long",415:"Unsupported Media Type",416:"Range Not Satisfiable",417:"Expectation Failed",418:"I'm a teapot",421:"Misdirected Request",422:"Unprocessable Entity",423:"Locked",424:"Failed Dependency",425:"Unordered Collection",426:"Upgrade Required",428:"Precondition Required",429:"Too Many Requests",431:"Request Header Fields Too Large",451:"Unavailable For Legal Reasons",500:"Internal Server Error",501:"Not Implemented",502:"Bad Gateway",503:"Service Unavailable",504:"Gateway Timeout",505:"HTTP Version Not Supported",506:"Variant Also Negotiates",507:"Insufficient Storage",508:"Loop Detected",509:"Bandwidth Limit Exceeded",510:"Not Extended",511:"Network Authentication Required"}},function(t,e,r){(function(t,n){var i;/*! https://mths.be/punycode v1.4.1 by @mathias */!function(o){e&&e.nodeType,t&&t.nodeType;var s="object"==typeof n&&n;s.global!==s&&s.window!==s&&s.self;var a,u=**********,h=/^xn--/,f=/[^\x20-\x7E]/,c=/[\x2E\u3002\uFF0E\uFF61]/g,l={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},d=Math.floor,p=String.fromCharCode;function m(t){throw new RangeError(l[t])}function g(t,e){for(var r=t.length,n=[];r--;)n[r]=e(t[r]);return n}function y(t,e){var r=t.split("@"),n="";return r.length>1&&(n=r[0]+"@",t=r[1]),n+g((t=t.replace(c,".")).split("."),e).join(".")}function v(t){for(var e,r,n=[],i=0,o=t.length;i<o;)(e=t.charCodeAt(i++))>=55296&&e<=56319&&i<o?56320==(64512&(r=t.charCodeAt(i++)))?n.push(((1023&e)<<10)+(1023&r)+65536):(n.push(e),i--):n.push(e);return n}function w(t){return g(t,(function(t){var e="";return t>65535&&(e+=p((t-=65536)>>>10&1023|55296),t=56320|1023&t),e+=p(t)})).join("")}function b(t,e){return t+22+75*(t<26)-((0!=e)<<5)}function _(t,e,r){var n=0;for(t=r?d(t/700):t>>1,t+=d(t/e);t>455;n+=36)t=d(t/35);return d(n+36*t/(t+38))}function M(t){var e,r,n,i,o,s,a,h,f,c,l,p=[],g=t.length,y=0,v=128,b=72;for((r=t.lastIndexOf("-"))<0&&(r=0),n=0;n<r;++n)t.charCodeAt(n)>=128&&m("not-basic"),p.push(t.charCodeAt(n));for(i=r>0?r+1:0;i<g;){for(o=y,s=1,a=36;i>=g&&m("invalid-input"),((h=(l=t.charCodeAt(i++))-48<10?l-22:l-65<26?l-65:l-97<26?l-97:36)>=36||h>d((u-y)/s))&&m("overflow"),y+=h*s,!(h<(f=a<=b?1:a>=b+26?26:a-b));a+=36)s>d(u/(c=36-f))&&m("overflow"),s*=c;b=_(y-o,e=p.length+1,0==o),d(y/e)>u-v&&m("overflow"),v+=d(y/e),y%=e,p.splice(y++,0,v)}return w(p)}function S(t){var e,r,n,i,o,s,a,h,f,c,l,g,y,w,M,S=[];for(g=(t=v(t)).length,e=128,r=0,o=72,s=0;s<g;++s)(l=t[s])<128&&S.push(p(l));for(n=i=S.length,i&&S.push("-");n<g;){for(a=u,s=0;s<g;++s)(l=t[s])>=e&&l<a&&(a=l);for(a-e>d((u-r)/(y=n+1))&&m("overflow"),r+=(a-e)*y,e=a,s=0;s<g;++s)if((l=t[s])<e&&++r>u&&m("overflow"),l==e){for(h=r,f=36;!(h<(c=f<=o?1:f>=o+26?26:f-o));f+=36)M=h-c,w=36-c,S.push(p(b(c+M%w,0))),h=d(M/w);S.push(p(b(h,0))),o=_(r,y,n==i),r=0,++n}++r,++e}return S.join("")}a={version:"1.4.1",ucs2:{decode:v,encode:w},decode:M,encode:S,toASCII:function(t){return y(t,(function(t){return f.test(t)?"xn--"+S(t):t}))},toUnicode:function(t){return y(t,(function(t){return h.test(t)?M(t.slice(4).toLowerCase()):t}))}},void 0===(i=function(){return a}.call(e,r,e,t))||(t.exports=i)}()}).call(this,r(31)(t),r(3))},function(t,e,r){"use strict";t.exports={isString:function(t){return"string"==typeof t},isObject:function(t){return"object"==typeof t&&null!==t},isNull:function(t){return null===t},isNullOrUndefined:function(t){return null==t}}},function(t,e,r){"use strict";e.decode=e.parse=r(69),e.encode=e.stringify=r(70)},function(t,e,r){"use strict";function n(t,e){return Object.prototype.hasOwnProperty.call(t,e)}t.exports=function(t,e,r,o){e=e||"&",r=r||"=";var s={};if("string"!=typeof t||0===t.length)return s;var a=/\+/g;t=t.split(e);var u=1e3;o&&"number"==typeof o.maxKeys&&(u=o.maxKeys);var h=t.length;u>0&&h>u&&(h=u);for(var f=0;f<h;++f){var c,l,d,p,m=t[f].replace(a,"%20"),g=m.indexOf(r);g>=0?(c=m.substr(0,g),l=m.substr(g+1)):(c=m,l=""),d=decodeURIComponent(c),p=decodeURIComponent(l),n(s,d)?i(s[d])?s[d].push(p):s[d]=[s[d],p]:s[d]=p}return s};var i=Array.isArray||function(t){return"[object Array]"===Object.prototype.toString.call(t)}},function(t,e,r){"use strict";var n=function(t){switch(typeof t){case"string":return t;case"boolean":return t?"true":"false";case"number":return isFinite(t)?t:"";default:return""}};t.exports=function(t,e,r,a){return e=e||"&",r=r||"=",null===t&&(t=void 0),"object"==typeof t?o(s(t),(function(s){var a=encodeURIComponent(n(s))+r;return i(t[s])?o(t[s],(function(t){return a+encodeURIComponent(n(t))})).join(e):a+encodeURIComponent(n(t[s]))})).join(e):a?encodeURIComponent(n(a))+r+encodeURIComponent(n(t)):""};var i=Array.isArray||function(t){return"[object Array]"===Object.prototype.toString.call(t)};function o(t,e){if(t.map)return t.map(e);for(var r=[],n=0;n<t.length;n++)r.push(e(t[n],n));return r}var s=Object.keys||function(t){var e=[];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.push(r);return e}},function(t,e,r){var n=r(20),i=r(15),o=t.exports;for(var s in n)n.hasOwnProperty(s)&&(o[s]=n[s]);function a(t){if("string"==typeof t&&(t=i.parse(t)),t.protocol||(t.protocol="https:"),"https:"!==t.protocol)throw new Error('Protocol "'+t.protocol+'" not supported. Expected "https:"');return t}o.request=function(t,e){return t=a(t),n.request.call(this,t,e)},o.get=function(t,e){return t=a(t),n.get.call(this,t,e)}},function(t,e){e.endianness=function(){return"LE"},e.hostname=function(){return"undefined"!=typeof location?location.hostname:""},e.loadavg=function(){return[]},e.uptime=function(){return 0},e.freemem=function(){return Number.MAX_VALUE},e.totalmem=function(){return Number.MAX_VALUE},e.cpus=function(){return[]},e.type=function(){return"Browser"},e.release=function(){return"undefined"!=typeof navigator?navigator.appVersion:""},e.networkInterfaces=e.getNetworkInterfaces=function(){return{}},e.arch=function(){return"javascript"},e.platform=function(){return"browser"},e.tmpdir=e.tmpDir=function(){return"/tmp"},e.EOL="\n",e.homedir=function(){return"/"}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(t){this.type=t,this.bubbles=!1,this.cancelable=!1,this.loaded=0,this.lengthComputable=!1,this.total=0};e.ProgressEvent=n},function(t,e,r){"use strict";var n,i=this&&this.__extends||(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},function(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0});var o=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e}(Error);e.SecurityError=o;var s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e}(Error);e.InvalidStateError=s;var a=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e}(Error);e.NetworkError=a;var u=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e}(Error);e.SyntaxError=u},function(t,e,r){"use strict";(function(t){var n,i=this&&this.__extends||(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},function(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0});var o=function(e){function r(){var t=e.call(this)||this;return t._contentType=null,t._body=null,t._reset(),t}return i(r,e),r.prototype._reset=function(){this._contentType=null,this._body=null},r.prototype._setData=function(e){if(null!=e)if("string"==typeof e)0!==e.length&&(this._contentType="text/plain;charset=UTF-8"),this._body=new t(e,"utf-8");else if(t.isBuffer(e))this._body=e;else if(e instanceof ArrayBuffer){for(var r=new t(e.byteLength),n=new Uint8Array(e),i=0;i<e.byteLength;i++)r[i]=n[i];this._body=r}else{if(!(e.buffer&&e.buffer instanceof ArrayBuffer))throw new Error("Unsupported send() data "+e);r=new t(e.byteLength);var o=e.byteOffset;for(n=new Uint8Array(e.buffer),i=0;i<e.byteLength;i++)r[i]=n[i+o];this._body=r}},r.prototype._finalizeHeaders=function(t,e){this._contentType&&!e["content-type"]&&(t["Content-Type"]=this._contentType),this._body&&(t["Content-Length"]=this._body.length.toString())},r.prototype._startUpload=function(t){this._body&&t.write(this._body),t.end()},r}(r(16).XMLHttpRequestEventTarget);e.XMLHttpRequestUpload=o}).call(this,r(5).Buffer)},function(t,e){!function(){"use strict";function t(e,r,n,i){return this instanceof t?(this.domain=e||void 0,this.path=r||"/",this.secure=!!n,this.script=!!i,this):new t(e,r,n,i)}function r(t,e,n){return t instanceof r?t:this instanceof r?(this.name=null,this.value=null,this.expiration_date=1/0,this.path=String(n||"/"),this.explicit_path=!1,this.domain=e||null,this.explicit_domain=!1,this.secure=!1,this.noscript=!1,t&&this.parse(t,e,n),this):new r(t,e,n)}t.All=Object.freeze(Object.create(null)),e.CookieAccessInfo=t,e.Cookie=r,r.prototype.toString=function(){var t=[this.name+"="+this.value];return this.expiration_date!==1/0&&t.push("expires="+new Date(this.expiration_date).toGMTString()),this.domain&&t.push("domain="+this.domain),this.path&&t.push("path="+this.path),this.secure&&t.push("secure"),this.noscript&&t.push("httponly"),t.join("; ")},r.prototype.toValueString=function(){return this.name+"="+this.value};var n=/[:](?=\s*[a-zA-Z0-9_\-]+\s*[=])/g;function i(){var t,e;return this instanceof i?(t=Object.create(null),this.setCookie=function(n,i,o){var s,a;if(s=(n=new r(n,i,o)).expiration_date<=Date.now(),void 0!==t[n.name]){for(e=t[n.name],a=0;a<e.length;a+=1)if(e[a].collidesWith(n))return s?(e.splice(a,1),0===e.length&&delete t[n.name],!1):(e[a]=n,n);return!s&&(e.push(n),n)}return!s&&(t[n.name]=[n],t[n.name])},this.getCookie=function(r,n){var i,o;if(e=t[r])for(o=0;o<e.length;o+=1)if((i=e[o]).expiration_date<=Date.now())0===e.length&&delete t[i.name];else if(i.matches(n))return i},this.getCookies=function(e){var r,n,i=[];for(r in t)(n=this.getCookie(r,e))&&i.push(n);return i.toString=function(){return i.join(":")},i.toValueString=function(){return i.map((function(t){return t.toValueString()})).join("; ")},i},this):new i}r.prototype.parse=function(t,e,n){if(this instanceof r){var i,o=t.split(";").filter((function(t){return!!t})),s=o[0].match(/([^=]+)=([\s\S]*)/);if(!s)return void console.warn("Invalid cookie header encountered. Header: '"+t+"'");var a=s[1],u=s[2];if("string"!=typeof a||0===a.length||"string"!=typeof u)return void console.warn("Unable to extract values from cookie header. Cookie: '"+t+"'");for(this.name=a,this.value=u,i=1;i<o.length;i+=1)switch(a=(s=o[i].match(/([^=]+)(?:=([\s\S]*))?/))[1].trim().toLowerCase(),u=s[2],a){case"httponly":this.noscript=!0;break;case"expires":this.expiration_date=u?Number(Date.parse(u)):1/0;break;case"path":this.path=u?u.trim():"",this.explicit_path=!0;break;case"domain":this.domain=u?u.trim():"",this.explicit_domain=!!this.domain;break;case"secure":this.secure=!0}return this.explicit_path||(this.path=n||"/"),this.explicit_domain||(this.domain=e),this}return(new r).parse(t,e,n)},r.prototype.matches=function(e){return e===t.All||!(this.noscript&&e.script||this.secure&&!e.secure||!this.collidesWith(e))},r.prototype.collidesWith=function(t){if(this.path&&!t.path||this.domain&&!t.domain)return!1;if(this.path&&0!==t.path.indexOf(this.path))return!1;if(this.explicit_path&&0!==t.path.indexOf(this.path))return!1;var e=t.domain&&t.domain.replace(/^[\.]/,""),r=this.domain&&this.domain.replace(/^[\.]/,"");if(r===e)return!0;if(r){if(!this.explicit_domain)return!1;var n=e.indexOf(r);return-1!==n&&n===e.length-r.length}return!0},e.CookieJar=i,i.prototype.setCookies=function(t,e,i){var o,s,a=[];for(t=(t=Array.isArray(t)?t:t.split(n)).map((function(t){return new r(t,e,i)})),o=0;o<t.length;o+=1)s=t[o],this.setCookie(s,e,i)&&a.push(s);return a}}()},function(t,e){},function(t,e){(function(e){t.exports=e}).call(this,{})},function(t,e,r){"use strict";(function(t){function r(){return(null==t?void 0:t.crypto)||(null==t?void 0:t.msCrypto)||{}}function n(){const t=r();return t.subtle||t.webkitSubtle}Object.defineProperty(e,"__esModule",{value:!0}),e.isBrowserCryptoAvailable=e.getSubtleCrypto=e.getBrowerCrypto=void 0,e.getBrowerCrypto=r,e.getSubtleCrypto=n,e.isBrowserCryptoAvailable=function(){return!!r()&&!!n()}}).call(this,r(3))},function(t,e,r){"use strict";(function(t){function r(){return"undefined"==typeof document&&"undefined"!=typeof navigator&&"ReactNative"===navigator.product}function n(){return void 0!==t&&void 0!==t.versions&&void 0!==t.versions.node}Object.defineProperty(e,"__esModule",{value:!0}),e.isBrowser=e.isNode=e.isReactNative=void 0,e.isReactNative=r,e.isNode=n,e.isBrowser=function(){return!r()&&!n()}}).call(this,r(4))},function(t,e,r){"use strict";t.exports=t=>encodeURIComponent(t).replace(/[!'()*]/g,t=>"%"+t.charCodeAt(0).toString(16).toUpperCase())},function(t,e,r){"use strict";var n=new RegExp("%[a-f0-9]{2}","gi"),i=new RegExp("(%[a-f0-9]{2})+","gi");function o(t,e){try{return decodeURIComponent(t.join(""))}catch(t){}if(1===t.length)return t;e=e||1;var r=t.slice(0,e),n=t.slice(e);return Array.prototype.concat.call([],o(r),o(n))}function s(t){try{return decodeURIComponent(t)}catch(i){for(var e=t.match(n),r=1;r<e.length;r++)e=(t=o(e,r).join("")).match(n);return t}}t.exports=function(t){if("string"!=typeof t)throw new TypeError("Expected `encodedURI` to be of type `string`, got `"+typeof t+"`");try{return t=t.replace(/\+/g," "),decodeURIComponent(t)}catch(e){return function(t){for(var e={"%FE%FF":"��","%FF%FE":"��"},r=i.exec(t);r;){try{e[r[0]]=decodeURIComponent(r[0])}catch(t){var n=s(r[0]);n!==r[0]&&(e[r[0]]=n)}r=i.exec(t)}e["%C2"]="�";for(var o=Object.keys(e),a=0;a<o.length;a++){var u=o[a];t=t.replace(new RegExp(u,"g"),e[u])}return t}(t)}}},function(t,e,r){"use strict";t.exports=(t,e)=>{if("string"!=typeof t||"string"!=typeof e)throw new TypeError("Expected the arguments to be of type `string`");if(""===e)return[t];const r=t.indexOf(e);return-1===r?[t]:[t.slice(0,r),t.slice(r+e.length)]}},function(t,e,r){"use strict";r.r(e);var n=r(42),i=r(2),o=r(43);function s(t){return Object(o.a)(t)}function a(){const t=s();return t&&t.os?t.os:void 0}function u(){const t=a();return!!t&&t.toLowerCase().includes("android")}function h(){const t=a();return!!t&&(t.toLowerCase().includes("ios")||t.toLowerCase().includes("mac")&&navigator.maxTouchPoints>1)}function f(){return!!a()&&(u()||h())}function c(){const t=s();return!(!t||!t.name)&&"node"===t.name.toLowerCase()}function l(){return!c()&&!!v()}const d=i.getFromWindow,p=i.getFromWindowOrThrow,m=i.getDocumentOrThrow,g=i.getDocument,y=i.getNavigatorOrThrow,v=i.getNavigator,w=i.getLocationOrThrow,b=i.getLocation,_=i.getCryptoOrThrow,M=i.getCrypto,S=i.getLocalStorageOrThrow,E=i.getLocalStorage;function A(){return n.getWindowMetadata()}const O=function(t){if("string"!=typeof t)throw new Error("Cannot safe json parse value of type "+typeof t);try{return JSON.parse(t)}catch(e){return t}},x=function(t){return"string"==typeof t?t:JSON.stringify(t)};function T(t,e){const r=x(e),n=E();n&&n.setItem(t,r)}function R(t){let e=null,r=null;const n=E();return n&&(r=n.getItem(t)),e=r?O(r):r,e}function k(t){const e=E();e&&e.removeItem(t)}function C(t,e){const r=encodeURIComponent(t);return e.universalLink?`${e.universalLink}/wc?uri=${r}`:e.deepLink?`${e.deepLink}${e.deepLink.endsWith(":")?"//":"/"}wc?uri=${r}`:""}function j(t){const e=t.href.split("?")[0];T("WALLETCONNECT_DEEPLINK_CHOICE",Object.assign(Object.assign({},t),{href:e}))}function L(t,e){return t.filter(t=>t.name.toLowerCase().includes(e.toLowerCase()))[0]}function P(t,e){let r=t;return e&&(r=e.map(e=>L(t,e)).filter(Boolean)),r}const B="https://registry.walletconnect.com";function N(){return B+"/api/v2/wallets"}function I(){return B+"/api/v2/dapps"}function U(t,e="mobile"){var r;return{name:t.name||"",shortName:t.metadata.shortName||"",color:t.metadata.colors.primary||"",logo:null!==(r=t.image_url.sm)&&void 0!==r?r:"",universalLink:t[e].universal||"",deepLink:t[e].native||""}}function H(t,e="mobile"){return Object.values(t).filter(t=>!!t[e].universal||!!t[e].native).map(t=>U(t,e))}const D=["session_request","session_update","exchange_key","connect","disconnect","display_uri","modal_closed","transport_open","transport_close","transport_error"],q=["eth_sendTransaction","eth_signTransaction","eth_sign","eth_signTypedData","eth_signTypedData_v1","eth_signTypedData_v2","eth_signTypedData_v3","eth_signTypedData_v4","personal_sign","wallet_addEthereumChain","wallet_switchEthereumChain","wallet_getPermissions","wallet_requestPermissions","wallet_registerOnboarding","wallet_watchAsset","wallet_scanQRCode"],W=["eth_accounts","eth_chainId","net_version"],F={1:"mainnet",3:"ropsten",4:"rinkeby",5:"goerli",42:"kovan"};var Y=r(11),z=r.n(Y),$=r(0);function V(t){return $.b(new Uint8Array(t))}function X(t){return $.e(new Uint8Array(t))}function Z(t,e){return $.c(new Uint8Array(t),!e)}function G(t){return $.d(new Uint8Array(t))}function J(...t){return $.m(t.map(t=>$.c(new Uint8Array(t))).join("")).buffer}function K(t){return $.f(t).buffer}function Q(t){return $.i(t)}function tt(t,e){return $.g(t,!e)}function et(t){return $.h(t)}function rt(...t){return $.j(...t)}function nt(t){return $.y(t).buffer}function it(t){return $.z(t)}function ot(t,e){return $.A(t,!e)}function st(t){return new z.a(t,10).toNumber()}function at(t){return $.n(t)}function ut(t){return $.m(t).buffer}function ht(t){return $.o(t)}function ft(t){return new z.a($.w(t),"hex").toNumber()}function ct(t){return $.u(t)}function lt(t){return $.t(t).buffer}function dt(t){return new z.a(t).toString()}function pt(t,e){const r=$.w($.x(new z.a(t).toString(16)));return e?r:$.a(r)}var mt=r(45);function gt(t){return $.x(t)}function yt(t){return $.a(t)}function vt(t){return $.w(t)}function wt(t){return $.v($.a(t))}const bt=r(46).payloadId;function _t(){return((t,e)=>{for(e=t="";t++<36;e+=51*t&52?(15^t?8^Math.random()*(20^t?16:4):4).toString(16):"-");return e})()}function Mt(){console.warn("DEPRECATION WARNING: This WalletConnect client library will be deprecated in favor of @walletconnect/client. Please check docs.walletconnect.org to learn more about this migration!")}function St(t,e){let r;const n=F[t];return n&&(r=`https://${n}.infura.io/v3/${e}`),r}function Et(t,e){let r;const n=St(t,e.infuraId);return e.custom&&e.custom[t]?r=e.custom[t]:n&&(r=n),r}function At(t){return""===t||"string"==typeof t&&""===t.trim()}function Ot(t){return!(t&&t.length)}function xt(t){return $.q(t)}function Tt(t){return $.s(t)}function Rt(t){return $.p(t)}function kt(t){return $.l(t)}function Ct(t){return $.k(t)}function jt(t,e){return $.r(t,e)}function Lt(t){return"object"==typeof t.params}function Pt(t){return void 0!==t.method}function Bt(t){return void 0!==t.result}function Nt(t){return void 0!==t.error}function It(t){return void 0!==t.event}function Ut(t){return D.includes(t)||t.startsWith("wc_")}function Ht(t){return!!t.method.startsWith("wc_")||!q.includes(t.method)}function Dt(t){t=Object($.w)(t.toLowerCase());const e=Object($.w)(Object(mt.keccak_256)(it(t)));let r="";for(let n=0;n<t.length;n++)parseInt(e[n],16)>7?r+=t[n].toUpperCase():r+=t[n];return Object($.a)(r)}const qt=t=>!!t&&("0x"===t.toLowerCase().substring(0,2)&&(!!/^(0x)?[0-9a-f]{40}$/i.test(t)&&(!(!/^(0x)?[0-9a-f]{40}$/.test(t)&&!/^(0x)?[0-9A-F]{40}$/.test(t))||t===Dt(t))));function Wt(t){return Ot(t)||jt(t[0])||(t[0]=ot(t[0])),t}function Ft(t){if(void 0!==t.type&&"0"!==t.type)return t;if(void 0===t.from||!qt(t.from))throw new Error("Transaction object must include a valid 'from' value.");function e(t){let e=t;return("number"==typeof t||"string"==typeof t&&!At(t))&&(jt(t)?"string"==typeof t&&(e=gt(t)):e=pt(t)),"string"==typeof e&&(e=wt(e)),e}const r={from:gt(t.from),to:void 0===t.to?void 0:gt(t.to),gasPrice:void 0===t.gasPrice?"":e(t.gasPrice),gas:void 0===t.gas?void 0===t.gasLimit?"":e(t.gasLimit):e(t.gas),value:void 0===t.value?"":e(t.value),nonce:void 0===t.nonce?"":e(t.nonce),data:void 0===t.data?"":gt(t.data)||"0x"},n=["gasPrice","gas","value","nonce"];return Object.keys(r).forEach(t=>{(void 0===r[t]||"string"==typeof r[t]&&!r[t].trim().length)&&n.includes(t)&&delete r[t]}),r}function Yt(t,e){return async(...r)=>new Promise((n,i)=>{t.apply(e,[...r,(t,e)=>{null==t&&i(t),n(e)}])})}function zt(t){const e=t.message||"Failed or Rejected Request";let r=-32e3;if(t&&!t.code)switch(e){case"Parse error":r=-32700;break;case"Invalid request":r=-32600;break;case"Method not found":r=-32601;break;case"Invalid params":r=-32602;break;case"Internal error":r=-32603;break;default:r=-32e3}const n={code:r,message:e};return t.data&&(n.data=t.data),n}var $t=r(18);function Vt(t){const e=-1!==t.indexOf("?")?t.indexOf("?"):void 0;return void 0!==e?t.substr(e):""}function Xt(t,e){let r=Zt(t);return r=Object.assign(Object.assign({},r),e),t=Gt(r)}function Zt(t){return $t.parse(t)}function Gt(t){return $t.stringify(t)}function Jt(t){return void 0!==t.bridge}function Kt(t){const e=t.indexOf(":"),r=-1!==t.indexOf("?")?t.indexOf("?"):void 0,n=t.substring(0,e);const i=function(t){const e=t.split("@");return{handshakeTopic:e[0],version:parseInt(e[1],10)}}(t.substring(e+1,r));const o=function(t){const e=Zt(t);return{key:e.key||"",bridge:e.bridge||""}}(void 0!==r?t.substr(r):"");return Object.assign(Object.assign({protocol:n},i),o)}r.d(e,"detectEnv",(function(){return s})),r.d(e,"detectOS",(function(){return a})),r.d(e,"isAndroid",(function(){return u})),r.d(e,"isIOS",(function(){return h})),r.d(e,"isMobile",(function(){return f})),r.d(e,"isNode",(function(){return c})),r.d(e,"isBrowser",(function(){return l})),r.d(e,"getFromWindow",(function(){return d})),r.d(e,"getFromWindowOrThrow",(function(){return p})),r.d(e,"getDocumentOrThrow",(function(){return m})),r.d(e,"getDocument",(function(){return g})),r.d(e,"getNavigatorOrThrow",(function(){return y})),r.d(e,"getNavigator",(function(){return v})),r.d(e,"getLocationOrThrow",(function(){return w})),r.d(e,"getLocation",(function(){return b})),r.d(e,"getCryptoOrThrow",(function(){return _})),r.d(e,"getCrypto",(function(){return M})),r.d(e,"getLocalStorageOrThrow",(function(){return S})),r.d(e,"getLocalStorage",(function(){return E})),r.d(e,"getClientMeta",(function(){return A})),r.d(e,"safeJsonParse",(function(){return O})),r.d(e,"safeJsonStringify",(function(){return x})),r.d(e,"setLocal",(function(){return T})),r.d(e,"getLocal",(function(){return R})),r.d(e,"removeLocal",(function(){return k})),r.d(e,"mobileLinkChoiceKey",(function(){return"WALLETCONNECT_DEEPLINK_CHOICE"})),r.d(e,"formatIOSMobile",(function(){return C})),r.d(e,"saveMobileLinkInfo",(function(){return j})),r.d(e,"getMobileRegistryEntry",(function(){return L})),r.d(e,"getMobileLinkRegistry",(function(){return P})),r.d(e,"getWalletRegistryUrl",(function(){return N})),r.d(e,"getDappRegistryUrl",(function(){return I})),r.d(e,"formatMobileRegistryEntry",(function(){return U})),r.d(e,"formatMobileRegistry",(function(){return H})),r.d(e,"reservedEvents",(function(){return D})),r.d(e,"signingMethods",(function(){return q})),r.d(e,"stateMethods",(function(){return W})),r.d(e,"infuraNetworks",(function(){return F})),r.d(e,"convertArrayBufferToBuffer",(function(){return V})),r.d(e,"convertArrayBufferToUtf8",(function(){return X})),r.d(e,"convertArrayBufferToHex",(function(){return Z})),r.d(e,"convertArrayBufferToNumber",(function(){return G})),r.d(e,"concatArrayBuffers",(function(){return J})),r.d(e,"convertBufferToArrayBuffer",(function(){return K})),r.d(e,"convertBufferToUtf8",(function(){return Q})),r.d(e,"convertBufferToHex",(function(){return tt})),r.d(e,"convertBufferToNumber",(function(){return et})),r.d(e,"concatBuffers",(function(){return rt})),r.d(e,"convertUtf8ToArrayBuffer",(function(){return nt})),r.d(e,"convertUtf8ToBuffer",(function(){return it})),r.d(e,"convertUtf8ToHex",(function(){return ot})),r.d(e,"convertUtf8ToNumber",(function(){return st})),r.d(e,"convertHexToBuffer",(function(){return at})),r.d(e,"convertHexToArrayBuffer",(function(){return ut})),r.d(e,"convertHexToUtf8",(function(){return ht})),r.d(e,"convertHexToNumber",(function(){return ft})),r.d(e,"convertNumberToBuffer",(function(){return ct})),r.d(e,"convertNumberToArrayBuffer",(function(){return lt})),r.d(e,"convertNumberToUtf8",(function(){return dt})),r.d(e,"convertNumberToHex",(function(){return pt})),r.d(e,"toChecksumAddress",(function(){return Dt})),r.d(e,"isValidAddress",(function(){return qt})),r.d(e,"parsePersonalSign",(function(){return Wt})),r.d(e,"parseTransactionData",(function(){return Ft})),r.d(e,"sanitizeHex",(function(){return gt})),r.d(e,"addHexPrefix",(function(){return yt})),r.d(e,"removeHexPrefix",(function(){return vt})),r.d(e,"removeHexLeadingZeros",(function(){return wt})),r.d(e,"payloadId",(function(){return bt})),r.d(e,"uuid",(function(){return _t})),r.d(e,"logDeprecationWarning",(function(){return Mt})),r.d(e,"getInfuraRpcUrl",(function(){return St})),r.d(e,"getRpcUrl",(function(){return Et})),r.d(e,"promisify",(function(){return Yt})),r.d(e,"formatRpcError",(function(){return zt})),r.d(e,"isWalletConnectSession",(function(){return Jt})),r.d(e,"parseWalletConnectUri",(function(){return Kt})),r.d(e,"getQueryString",(function(){return Vt})),r.d(e,"appendToQueryString",(function(){return Xt})),r.d(e,"parseQueryString",(function(){return Zt})),r.d(e,"formatQueryString",(function(){return Gt})),r.d(e,"isEmptyString",(function(){return At})),r.d(e,"isEmptyArray",(function(){return Ot})),r.d(e,"isBuffer",(function(){return xt})),r.d(e,"isTypedArray",(function(){return Tt})),r.d(e,"isArrayBuffer",(function(){return Rt})),r.d(e,"getType",(function(){return kt})),r.d(e,"getEncoding",(function(){return Ct})),r.d(e,"isHexString",(function(){return jt})),r.d(e,"isJsonRpcSubscription",(function(){return Lt})),r.d(e,"isJsonRpcRequest",(function(){return Pt})),r.d(e,"isJsonRpcResponseSuccess",(function(){return Bt})),r.d(e,"isJsonRpcResponseError",(function(){return Nt})),r.d(e,"isInternalEvent",(function(){return It})),r.d(e,"isReservedEvent",(function(){return Ut})),r.d(e,"isSilentPayload",(function(){return Ht}))}])}));