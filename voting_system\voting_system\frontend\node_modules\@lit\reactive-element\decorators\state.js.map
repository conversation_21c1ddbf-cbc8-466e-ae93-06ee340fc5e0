{"version": 3, "file": "state.js", "sources": ["../src/decorators/state.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/*\n * IMPORTANT: For compatibility with <PERSON><PERSON><PERSON> and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\n\nimport {property} from './property.js';\n\nexport interface InternalPropertyDeclaration<Type = unknown> {\n  /**\n   * A function that indicates if a property should be considered changed when\n   * it is set. The function should take the `newValue` and `oldValue` and\n   * return `true` if an update should be requested.\n   */\n  hasChanged?(value: Type, oldValue: Type): boolean;\n}\n\n/**\n * Declares a private or protected reactive property that still triggers\n * updates to the element when it changes. It does not reflect from the\n * corresponding attribute.\n *\n * Properties declared this way must not be used from HTML or HTML templating\n * systems, they're solely for properties internal to the element. These\n * properties may be renamed by optimization tools like closure compiler.\n * @category Decorator\n */\nexport function state(options?: InternalPropertyDeclaration) {\n  return property({\n    ...options,\n    state: true,\n  });\n}\n"], "names": ["state", "options", "property"], "mappings": ";;;;;GAkCM,SAAUA,EAAMC,GACpB,OAAOC,EAAS,IACXD,EACHD,OAAO,GAEX"}