{"version": 3, "file": "event-options.js", "sources": ["../src/decorators/event-options.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/*\n * IMPORTANT: For compatibility with <PERSON><PERSON><PERSON> and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\n\nimport {ReactiveElement} from '../reactive-element.js';\nimport {decorateProperty} from './base.js';\n\n/**\n * Adds event listener options to a method used as an event listener in a\n * lit-html template.\n *\n * @param options An object that specifies event listener options as accepted by\n * `EventTarget#addEventListener` and `EventTarget#removeEventListener`.\n *\n * Current browsers support the `capture`, `passive`, and `once` options. See:\n * https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener#Parameters\n *\n * ```ts\n * class MyElement {\n *   clicked = false;\n *\n *   render() {\n *     return html`\n *       <div @click=${this._onClick}>\n *         <button></button>\n *       </div>\n *     `;\n *   }\n *\n *   @eventOptions({capture: true})\n *   _onClick(e) {\n *     this.clicked = true;\n *   }\n * }\n * ```\n * @category Decorator\n */\nexport function eventOptions(options: AddEventListenerOptions) {\n  return decorateProperty({\n    finisher: (ctor: typeof ReactiveElement, name: PropertyKey) => {\n      Object.assign(\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        ctor.prototype[name as keyof ReactiveElement] as any,\n        options\n      );\n    },\n  });\n}\n"], "names": ["eventOptions", "options", "decorateProperty", "finisher", "ctor", "name", "Object", "assign", "prototype"], "mappings": ";;;;;GA8CM,SAAUA,EAAaC,GAC3B,OAAOC,EAAiB,CACtBC,SAAU,CAACC,EAA8BC,KACvCC,OAAOC,OAELH,EAAKI,UAAUH,GACfJ,EACD,GAGP"}