{"version": 3, "file": "encoding.d.ts", "sourceRoot": "", "sources": ["../../src/encoding.ts"], "names": [], "mappings": ";AAKA,wBAAgB,0BAA0B,CAAC,MAAM,EAAE,WAAW,GAAG,MAAM,CAEtE;AAED,wBAAgB,wBAAwB,CAAC,MAAM,EAAE,WAAW,GAAG,MAAM,CAEpE;AAED,wBAAgB,uBAAuB,CAAC,MAAM,EAAE,WAAW,EAAE,QAAQ,CAAC,EAAE,OAAO,GAAG,MAAM,CAEvF;AAED,wBAAgB,0BAA0B,CAAC,MAAM,EAAE,WAAW,GAAG,MAAM,CAEtE;AAED,wBAAgB,kBAAkB,CAAC,GAAG,IAAI,EAAE,WAAW,EAAE,GAAG,WAAW,CAEtE;AAID,wBAAgB,0BAA0B,CAAC,GAAG,EAAE,MAAM,GAAG,WAAW,CAEnE;AAED,wBAAgB,mBAAmB,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAEvD;AAED,wBAAgB,kBAAkB,CAAC,GAAG,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,OAAO,GAAG,MAAM,CAE1E;AAED,wBAAgB,qBAAqB,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAEzD;AAED,wBAAgB,aAAa,CAAC,GAAG,IAAI,EAAE,MAAM,EAAE,GAAG,MAAM,CAEvD;AAID,wBAAgB,wBAAwB,CAAC,IAAI,EAAE,MAAM,GAAG,WAAW,CAElE;AAED,wBAAgB,mBAAmB,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM,CAExD;AAED,wBAAgB,gBAAgB,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,OAAO,GAAG,MAAM,CAEzE;AAED,wBAAgB,mBAAmB,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM,CAExD;AAID,wBAAgB,kBAAkB,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAEtD;AAED,wBAAgB,uBAAuB,CAAC,GAAG,EAAE,MAAM,GAAG,WAAW,CAEhE;AAED,wBAAgB,gBAAgB,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAEpD;AAED,wBAAgB,kBAAkB,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAEtD;AAID,wBAAgB,qBAAqB,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAEzD;AAED,wBAAgB,0BAA0B,CAAC,GAAG,EAAE,MAAM,GAAG,WAAW,CAEnE;AAED,wBAAgB,mBAAmB,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAEvD;AAED,wBAAgB,kBAAkB,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,EAAE,QAAQ,CAAC,EAAE,OAAO,GAAG,MAAM,CAGnF"}