{"version": 3, "file": "experimental-hydrate-support.js", "sources": ["src/experimental-hydrate-support.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/**\n * LitElement support for hydration of content rendered using lit-ssr.\n *\n * @packageDocumentation\n *\n * @deprecated Moved to `@lit-labs/ssr-client/lit-element-hydrate-support.js`.\n */\n\nimport type {PropertyValues} from '@lit/reactive-element';\nimport {render, RenderOptions} from 'lit-html';\nimport {hydrate} from 'lit-html/experimental-hydrate.js';\nimport {HYDRATE_INTERNALS_ATTR_PREFIX} from '@lit-labs/ssr-dom-shim';\n\ninterface PatchableLitElement extends HTMLElement {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-misused-new\n  new (...args: any[]): PatchableLitElement;\n  enableUpdating(requestedUpdate?: boolean): void;\n  createRenderRoot(): Element | ShadowRoot;\n  renderRoot: HTMLElement | DocumentFragment;\n  render(): unknown;\n  renderOptions: RenderOptions;\n  _$needsHydration: boolean;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nglobalThis.litElementHydrateSupport = ({\n  LitElement,\n}: {\n  LitElement: PatchableLitElement;\n}) => {\n  const observedAttributes = Object.getOwnPropertyDescriptor(\n    Object.getPrototypeOf(LitElement),\n    'observedAttributes'\n  )!.get!;\n\n  // Add `defer-hydration` to observedAttributes\n  Object.defineProperty(LitElement, 'observedAttributes', {\n    get() {\n      return [...observedAttributes.call(this), 'defer-hydration'];\n    },\n  });\n\n  // Enable element when 'defer-hydration' attribute is removed by calling the\n  // super.connectedCallback()\n  const attributeChangedCallback =\n    LitElement.prototype.attributeChangedCallback;\n  LitElement.prototype.attributeChangedCallback = function (\n    name: string,\n    old: string | null,\n    value: string | null\n  ) {\n    if (name === 'defer-hydration' && value === null) {\n      connectedCallback.call(this);\n    }\n    attributeChangedCallback.call(this, name, old, value);\n  };\n\n  // Override `connectedCallback` to capture whether we need hydration, and\n  // defer `super.connectedCallback()` if the 'defer-hydration' attribute is set\n  const connectedCallback = LitElement.prototype.connectedCallback;\n  LitElement.prototype.connectedCallback = function (\n    this: PatchableLitElement\n  ) {\n    // If the outer scope of this element has not yet been hydrated, wait until\n    // 'defer-hydration' attribute has been removed to enable\n    if (!this.hasAttribute('defer-hydration')) {\n      connectedCallback.call(this);\n    }\n  };\n\n  // If we've been server-side rendered, just return `this.shadowRoot`, don't\n  // call the base implementation, which would also adopt styles (for now)\n  const createRenderRoot = LitElement.prototype.createRenderRoot;\n  LitElement.prototype.createRenderRoot = function (this: PatchableLitElement) {\n    if (this.shadowRoot) {\n      this._$needsHydration = true;\n      return this.shadowRoot;\n    } else {\n      return createRenderRoot.call(this);\n    }\n  };\n\n  // Hydrate on first update when needed\n  const update = Object.getPrototypeOf(LitElement.prototype).update;\n  LitElement.prototype.update = function (\n    this: PatchableLitElement,\n    changedProperties: PropertyValues\n  ) {\n    const value = this.render();\n    // Since this is a patch, we can't call super.update(), so we capture\n    // it off the proto chain and call it instead\n    update.call(this, changedProperties);\n    if (this._$needsHydration) {\n      this._$needsHydration = false;\n      // Remove aria attributes added by internals shim during SSR\n      for (let i = 0; i < this.attributes.length; i++) {\n        const attr = this.attributes[i];\n        if (attr.name.startsWith(HYDRATE_INTERNALS_ATTR_PREFIX)) {\n          const ariaAttr = attr.name.slice(\n            HYDRATE_INTERNALS_ATTR_PREFIX.length\n          );\n          this.removeAttribute(ariaAttr);\n          this.removeAttribute(attr.name);\n        }\n      }\n      hydrate(value, this.renderRoot, this.renderOptions);\n    } else {\n      render(value, this.renderRoot, this.renderOptions);\n    }\n  };\n};\n\nconsole.warn(\n  'Import from `lit-element/experimental-hydrate-support.js` is deprecated.' +\n    'Import `@lit-labs/ssr-client/lit-element-hydrate-support.js` instead.'\n);\n"], "names": ["globalThis", "litElementHydrateSupport", "LitElement", "observedAttributes", "Object", "getOwnPropertyDescriptor", "getPrototypeOf", "get", "defineProperty", "call", "this", "attributeChangedCallback", "prototype", "name", "old", "value", "connectedCallback", "hasAttribute", "createRenderRoot", "shadowRoot", "_$needsHydration", "update", "changedProperties", "render", "i", "attributes", "length", "attr", "startsWith", "HYDRATE_INTERNALS_ATTR_PREFIX", "ariaAttr", "slice", "removeAttribute", "hydrate", "renderRoot", "renderOptions", "console", "warn"], "mappings": ";;;;;;AA+BAA,WAAWC,yBAA2B,EACpCC,iBAIA,MAAMC,EAAqBC,OAAOC,yBAChCD,OAAOE,eAAeJ,GACtB,sBACCK,IAGHH,OAAOI,eAAeN,EAAY,qBAAsB,CACtDK,MACE,MAAO,IAAIJ,EAAmBM,KAAKC,MAAO,kBAC3C,IAKH,MAAMC,EACJT,EAAWU,UAAUD,yBACvBT,EAAWU,UAAUD,yBAA2B,SAC9CE,EACAC,EACAC,GAEa,oBAATF,GAAwC,OAAVE,GAChCC,EAAkBP,KAAKC,MAEzBC,EAAyBF,KAAKC,KAAMG,EAAMC,EAAKC,EACjD,EAIA,MAAMC,EAAoBd,EAAWU,UAAUI,kBAC/Cd,EAAWU,UAAUI,kBAAoB,WAKlCN,KAAKO,aAAa,oBACrBD,EAAkBP,KAAKC,KAE3B,EAIA,MAAMQ,EAAmBhB,EAAWU,UAAUM,iBAC9ChB,EAAWU,UAAUM,iBAAmB,WACtC,OAAIR,KAAKS,YACPT,KAAKU,MAAmB,EACjBV,KAAKS,YAELD,EAAiBT,KAAKC,KAEjC,EAGA,MAAMW,EAASjB,OAAOE,eAAeJ,EAAWU,WAAWS,OAC3DnB,EAAWU,UAAUS,OAAS,SAE5BC,GAEA,MAAMP,EAAQL,KAAKa,SAInB,GADAF,EAAOZ,KAAKC,KAAMY,GACdZ,KAAKU,KAAkB,CACzBV,KAAKU,MAAmB,EAExB,IAAK,IAAII,EAAI,EAAGA,EAAId,KAAKe,WAAWC,OAAQF,IAAK,CAC/C,MAAMG,EAAOjB,KAAKe,WAAWD,GAC7B,GAAIG,EAAKd,KAAKe,WAAWC,GAAgC,CACvD,MAAMC,EAAWH,EAAKd,KAAKkB,MACzBF,EAA8BH,QAEhChB,KAAKsB,gBAAgBF,GACrBpB,KAAKsB,gBAAgBL,EAAKd,KAC3B,CACF,CACDoB,EAAQlB,EAAOL,KAAKwB,WAAYxB,KAAKyB,cACtC,MACCZ,EAAOR,EAAOL,KAAKwB,WAAYxB,KAAKyB,cAExC,CAAC,EAGHC,QAAQC,KACN"}