import { defineComponent, ref, inject, provide, onMounted, onUpdated, h, onBeforeUpdate, Transition } from 'vue';

function addUniqueItem(array, item) {
  array.indexOf(item) === -1 && array.push(item);
}

function removeItem(arr, item) {
  const index = arr.indexOf(item);
  index > -1 && arr.splice(index, 1);
}

const clamp = (min, max, v) => Math.min(Math.max(v, min), max);

const defaults = {
  duration: 0.3,
  delay: 0,
  endDelay: 0,
  repeat: 0,
  easing: "ease"
};

const isNumber = value => typeof value === "number";

const isEasingList = easing => Array.isArray(easing) && !isNumber(easing[0]);

const wrap = (min, max, v) => {
  const rangeSize = max - min;
  return ((v - min) % rangeSize + rangeSize) % rangeSize + min;
};

function getEasingForSegment(easing, i) {
  return isEasingList(easing) ? easing[wrap(0, easing.length, i)] : easing;
}

const mix = (min, max, progress) => -progress * min + progress * max + min;

const noop = () => {};

const noopReturn = v => v;

const progress = (min, max, value) => max - min === 0 ? 1 : (value - min) / (max - min);

function fillOffset(offset, remaining) {
  const min = offset[offset.length - 1];

  for (let i = 1; i <= remaining; i++) {
    const offsetProgress = progress(0, remaining, i);
    offset.push(mix(min, 1, offsetProgress));
  }
}

function defaultOffset(length) {
  const offset = [0];
  fillOffset(offset, length - 1);
  return offset;
}

function interpolate(output) {
  let input = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : defaultOffset(output.length);
  let easing = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : noopReturn;
  const length = output.length;
  /**
   * If the input length is lower than the output we
   * fill the input to match. This currently assumes the input
   * is an animation progress value so is a good candidate for
   * moving outside the function.
   */

  const remainder = length - input.length;
  remainder > 0 && fillOffset(input, remainder);
  return t => {
    let i = 0;

    for (; i < length - 2; i++) {
      if (t < input[i + 1]) break;
    }

    let progressInRange = clamp(0, 1, progress(input[i], input[i + 1], t));
    const segmentEasing = getEasingForSegment(easing, i);
    progressInRange = segmentEasing(progressInRange);
    return mix(output[i], output[i + 1], progressInRange);
  };
}

const isCubicBezier = easing => Array.isArray(easing) && isNumber(easing[0]);

const isEasingGenerator = easing => typeof easing === "object" && Boolean(easing.createAnimation);

const isFunction = value => typeof value === "function";

const isString = value => typeof value === "string";

const time = {
  ms: seconds => seconds * 1000,
  s: milliseconds => milliseconds / 1000
};

/*
  Bezier function generator

  This has been modified from Gaëtan Renaudeau's BezierEasing
  https://github.com/gre/bezier-easing/blob/master/src/index.js
  https://github.com/gre/bezier-easing/blob/master/LICENSE
  
  I've removed the newtonRaphsonIterate algo because in benchmarking it
  wasn't noticiably faster than binarySubdivision, indeed removing it
  usually improved times, depending on the curve.

  I also removed the lookup table, as for the added bundle size and loop we're
  only cutting ~4 or so subdivision iterations. I bumped the max iterations up
  to 12 to compensate and this still tended to be faster for no perceivable
  loss in accuracy.

  Usage
    const easeOut = cubicBezier(.17,.67,.83,.67);
    const x = easeOut(0.5); // returns 0.627...
*/
// Returns x(t) given t, x1, and x2, or y(t) given t, y1, and y2.

const calcBezier = (t, a1, a2) => (((1.0 - 3.0 * a2 + 3.0 * a1) * t + (3.0 * a2 - 6.0 * a1)) * t + 3.0 * a1) * t;

const subdivisionPrecision = 0.0000001;
const subdivisionMaxIterations = 12;

function binarySubdivide(x, lowerBound, upperBound, mX1, mX2) {
  let currentX;
  let currentT;
  let i = 0;

  do {
    currentT = lowerBound + (upperBound - lowerBound) / 2.0;
    currentX = calcBezier(currentT, mX1, mX2) - x;

    if (currentX > 0.0) {
      upperBound = currentT;
    } else {
      lowerBound = currentT;
    }
  } while (Math.abs(currentX) > subdivisionPrecision && ++i < subdivisionMaxIterations);

  return currentT;
}

function cubicBezier(mX1, mY1, mX2, mY2) {
  // If this is a linear gradient, return linear easing
  if (mX1 === mY1 && mX2 === mY2) return noopReturn;

  const getTForX = aX => binarySubdivide(aX, 0, 1, mX1, mX2); // If animation is at start/end, return t without easing


  return t => t === 0 || t === 1 ? t : calcBezier(getTForX(t), mY1, mY2);
}

const steps = function (steps) {
  let direction = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : "end";
  return progress => {
    progress = direction === "end" ? Math.min(progress, 0.999) : Math.max(progress, 0.001);
    const expanded = progress * steps;
    const rounded = direction === "end" ? Math.floor(expanded) : Math.ceil(expanded);
    return clamp(0, 1, rounded / steps);
  };
};

const namedEasings = {
  ease: cubicBezier(0.25, 0.1, 0.25, 1.0),
  "ease-in": cubicBezier(0.42, 0.0, 1.0, 1.0),
  "ease-in-out": cubicBezier(0.42, 0.0, 0.58, 1.0),
  "ease-out": cubicBezier(0.0, 0.0, 0.58, 1.0)
};
const functionArgsRegex = /\((.*?)\)/;

function getEasingFunction(definition) {
  // If already an easing function, return
  if (isFunction(definition)) return definition; // If an easing curve definition, return bezier function

  if (isCubicBezier(definition)) return cubicBezier(...definition); // If we have a predefined easing function, return

  if (namedEasings[definition]) return namedEasings[definition]; // If this is a steps function, attempt to create easing curve

  if (definition.startsWith("steps")) {
    const args = functionArgsRegex.exec(definition);

    if (args) {
      const argsArray = args[1].split(",");
      return steps(parseFloat(argsArray[0]), argsArray[1].trim());
    }
  }

  return noopReturn;
}

class Animation {
  constructor(output) {
    let keyframes = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [0, 1];
    let {
      easing,
      duration: initialDuration = defaults.duration,
      delay = defaults.delay,
      endDelay = defaults.endDelay,
      repeat = defaults.repeat,
      offset,
      direction = "normal"
    } = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
    this.startTime = null;
    this.rate = 1;
    this.t = 0;
    this.cancelTimestamp = null;
    this.easing = noopReturn;
    this.duration = 0;
    this.totalDuration = 0;
    this.repeat = 0;
    this.playState = "idle";
    this.finished = new Promise((resolve, reject) => {
      this.resolve = resolve;
      this.reject = reject;
    });
    easing = easing || defaults.easing;

    if (isEasingGenerator(easing)) {
      const custom = easing.createAnimation(keyframes);
      easing = custom.easing;
      keyframes = custom.keyframes || keyframes;
      initialDuration = custom.duration || initialDuration;
    }

    this.repeat = repeat;
    this.easing = isEasingList(easing) ? noopReturn : getEasingFunction(easing);
    this.updateDuration(initialDuration);
    const interpolate$1 = interpolate(keyframes, offset, isEasingList(easing) ? easing.map(getEasingFunction) : noopReturn);

    this.tick = timestamp => {
      var _a; // TODO: Temporary fix for OptionsResolver typing


      delay = delay;
      let t = 0;

      if (this.pauseTime !== undefined) {
        t = this.pauseTime;
      } else {
        t = (timestamp - this.startTime) * this.rate;
      }

      this.t = t; // Convert to seconds

      t /= 1000; // Rebase on delay

      t = Math.max(t - delay, 0);
      /**
       * If this animation has finished, set the current time
       * to the total duration.
       */

      if (this.playState === "finished" && this.pauseTime === undefined) {
        t = this.totalDuration;
      }
      /**
       * Get the current progress (0-1) of the animation. If t is >
       * than duration we'll get values like 2.5 (midway through the
       * third iteration)
       */


      const progress = t / this.duration; // TODO progress += iterationStart

      /**
       * Get the current iteration (0 indexed). For instance the floor of
       * 2.5 is 2.
       */

      let currentIteration = Math.floor(progress);
      /**
       * Get the current progress of the iteration by taking the remainder
       * so 2.5 is 0.5 through iteration 2
       */

      let iterationProgress = progress % 1.0;

      if (!iterationProgress && progress >= 1) {
        iterationProgress = 1;
      }
      /**
       * If iteration progress is 1 we count that as the end
       * of the previous iteration.
       */


      iterationProgress === 1 && currentIteration--;
      /**
       * Reverse progress if we're not running in "normal" direction
       */

      const iterationIsOdd = currentIteration % 2;

      if (direction === "reverse" || direction === "alternate" && iterationIsOdd || direction === "alternate-reverse" && !iterationIsOdd) {
        iterationProgress = 1 - iterationProgress;
      }

      const p = t >= this.totalDuration ? 1 : Math.min(iterationProgress, 1);
      const latest = interpolate$1(this.easing(p));
      output(latest);
      const isAnimationFinished = this.pauseTime === undefined && (this.playState === "finished" || t >= this.totalDuration + endDelay);

      if (isAnimationFinished) {
        this.playState = "finished";
        (_a = this.resolve) === null || _a === void 0 ? void 0 : _a.call(this, latest);
      } else if (this.playState !== "idle") {
        this.frameRequestId = requestAnimationFrame(this.tick);
      }
    };

    this.play();
  }

  play() {
    const now = performance.now();
    this.playState = "running";

    if (this.pauseTime !== undefined) {
      this.startTime = now - this.pauseTime;
    } else if (!this.startTime) {
      this.startTime = now;
    }

    this.cancelTimestamp = this.startTime;
    this.pauseTime = undefined;
    this.frameRequestId = requestAnimationFrame(this.tick);
  }

  pause() {
    this.playState = "paused";
    this.pauseTime = this.t;
  }

  finish() {
    this.playState = "finished";
    this.tick(0);
  }

  stop() {
    var _a;

    this.playState = "idle";

    if (this.frameRequestId !== undefined) {
      cancelAnimationFrame(this.frameRequestId);
    }

    (_a = this.reject) === null || _a === void 0 ? void 0 : _a.call(this, false);
  }

  cancel() {
    this.stop();
    this.tick(this.cancelTimestamp);
  }

  reverse() {
    this.rate *= -1;
  }

  commitStyles() {}

  updateDuration(duration) {
    this.duration = duration;
    this.totalDuration = duration * (this.repeat + 1);
  }

  get currentTime() {
    return this.t;
  }

  set currentTime(t) {
    if (this.pauseTime !== undefined || this.rate === 0) {
      this.pauseTime = t;
    } else {
      this.startTime = performance.now() - t / this.rate;
    }
  }

  get playbackRate() {
    return this.rate;
  }

  set playbackRate(rate) {
    this.rate = rate;
  }

}

/**
 * The MotionValue tracks the state of a single animatable
 * value. Currently, updatedAt and current are unused. The
 * long term idea is to use this to minimise the number
 * of DOM reads, and to abstract the DOM interactions here.
 */
class MotionValue {
  setAnimation(animation) {
    this.animation = animation;
    animation === null || animation === void 0 ? void 0 : animation.finished.then(() => this.clearAnimation()).catch(() => {});
  }

  clearAnimation() {
    this.animation = this.generator = undefined;
  }

}

const data = new WeakMap();

function getAnimationData(element) {
  if (!data.has(element)) {
    data.set(element, {
      transforms: [],
      values: new Map()
    });
  }

  return data.get(element);
}

function getMotionValue(motionValues, name) {
  if (!motionValues.has(name)) {
    motionValues.set(name, new MotionValue());
  }

  return motionValues.get(name);
}

/**
 * A list of all transformable axes. We'll use this list to generated a version
 * of each axes for each transform.
 */

const axes = ["", "X", "Y", "Z"];
/**
 * An ordered array of each transformable value. By default, transform values
 * will be sorted to this order.
 */

const order = ["translate", "scale", "rotate", "skew"];
const transformAlias = {
  x: "translateX",
  y: "translateY",
  z: "translateZ"
};
const rotation = {
  syntax: "<angle>",
  initialValue: "0deg",
  toDefaultUnit: v => v + "deg"
};
const baseTransformProperties = {
  translate: {
    syntax: "<length-percentage>",
    initialValue: "0px",
    toDefaultUnit: v => v + "px"
  },
  rotate: rotation,
  scale: {
    syntax: "<number>",
    initialValue: 1,
    toDefaultUnit: noopReturn
  },
  skew: rotation
};
const transformDefinitions = new Map();

const asTransformCssVar = name => `--motion-${name}`;
/**
 * Generate a list of every possible transform key
 */


const transforms = ["x", "y", "z"];
order.forEach(name => {
  axes.forEach(axis => {
    transforms.push(name + axis);
    transformDefinitions.set(asTransformCssVar(name + axis), baseTransformProperties[name]);
  });
});
/**
 * A function to use with Array.sort to sort transform keys by their default order.
 */

const compareTransformOrder = (a, b) => transforms.indexOf(a) - transforms.indexOf(b);
/**
 * Provide a quick way to check if a string is the name of a transform
 */


const transformLookup = new Set(transforms);

const isTransform = name => transformLookup.has(name);

const addTransformToElement = (element, name) => {
  // Map x to translateX etc
  if (transformAlias[name]) name = transformAlias[name];
  const {
    transforms
  } = getAnimationData(element);
  addUniqueItem(transforms, name);
  /**
   * TODO: An optimisation here could be to cache the transform in element data
   * and only update if this has changed.
   */

  element.style.transform = buildTransformTemplate(transforms);
};

const buildTransformTemplate = transforms => transforms.sort(compareTransformOrder).reduce(transformListToString, "").trim();

const transformListToString = (template, name) => `${template} ${name}(var(${asTransformCssVar(name)}))`;

const isCssVar = name => name.startsWith("--");

const registeredProperties = new Set();

function registerCssVariable(name) {
  if (registeredProperties.has(name)) return;
  registeredProperties.add(name);

  try {
    const {
      syntax,
      initialValue
    } = transformDefinitions.has(name) ? transformDefinitions.get(name) : {};
    CSS.registerProperty({
      name,
      inherits: false,
      syntax,
      initialValue
    });
  } catch (e) {}
}

const testAnimation = (keyframes, options) => document.createElement("div").animate(keyframes, options);

const featureTests = {
  cssRegisterProperty: () => typeof CSS !== "undefined" && Object.hasOwnProperty.call(CSS, "registerProperty"),
  waapi: () => Object.hasOwnProperty.call(Element.prototype, "animate"),
  partialKeyframes: () => {
    try {
      testAnimation({
        opacity: [1]
      });
    } catch (e) {
      return false;
    }

    return true;
  },
  finished: () => Boolean(testAnimation({
    opacity: [0, 1]
  }, {
    duration: 0.001
  }).finished),
  linearEasing: () => {
    try {
      testAnimation({
        opacity: 0
      }, {
        easing: "linear(0, 1)"
      });
    } catch (e) {
      return false;
    }

    return true;
  }
};
const results = {};
const supports = {};

for (const key in featureTests) {
  supports[key] = () => {
    if (results[key] === undefined) results[key] = featureTests[key]();
    return results[key];
  };
}

const resolution = 0.015;

const generateLinearEasingPoints = (easing, duration) => {
  let points = "";
  const numPoints = Math.round(duration / resolution);

  for (let i = 0; i < numPoints; i++) {
    points += easing(progress(0, numPoints - 1, i)) + ", ";
  }

  return points.substring(0, points.length - 2);
};

const convertEasing = (easing, duration) => {
  if (isFunction(easing)) {
    return supports.linearEasing() ? `linear(${generateLinearEasingPoints(easing, duration)})` : defaults.easing;
  } else {
    return isCubicBezier(easing) ? cubicBezierAsString(easing) : easing;
  }
};

const cubicBezierAsString = _ref => {
  let [a, b, c, d] = _ref;
  return `cubic-bezier(${a}, ${b}, ${c}, ${d})`;
};

function hydrateKeyframes(keyframes, readInitialValue) {
  for (let i = 0; i < keyframes.length; i++) {
    if (keyframes[i] === null) {
      keyframes[i] = i ? keyframes[i - 1] : readInitialValue();
    }
  }

  return keyframes;
}

const keyframesList = keyframes => Array.isArray(keyframes) ? keyframes : [keyframes];

function getStyleName(key) {
  if (transformAlias[key]) key = transformAlias[key];
  return isTransform(key) ? asTransformCssVar(key) : key;
}

const style = {
  get: (element, name) => {
    name = getStyleName(name);
    let value = isCssVar(name) ? element.style.getPropertyValue(name) : getComputedStyle(element)[name];

    if (!value && value !== 0) {
      const definition = transformDefinitions.get(name);
      if (definition) value = definition.initialValue;
    }

    return value;
  },
  set: (element, name, value) => {
    name = getStyleName(name);

    if (isCssVar(name)) {
      element.style.setProperty(name, value);
    } else {
      element.style[name] = value;
    }
  }
};

function stopAnimation(animation) {
  let needsCommit = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;
  if (!animation || animation.playState === "finished") return; // Suppress error thrown by WAAPI

  try {
    if (animation.stop) {
      animation.stop();
    } else {
      needsCommit && animation.commitStyles();
      animation.cancel();
    }
  } catch (e) {}
}

function getUnitConverter(keyframes, definition) {
  var _a;

  let toUnit = (definition === null || definition === void 0 ? void 0 : definition.toDefaultUnit) || noopReturn;
  const finalKeyframe = keyframes[keyframes.length - 1];

  if (isString(finalKeyframe)) {
    const unit = ((_a = finalKeyframe.match(/(-?[\d.]+)([a-z%]*)/)) === null || _a === void 0 ? void 0 : _a[2]) || "";
    if (unit) toUnit = value => value + unit;
  }

  return toUnit;
}

function getDevToolsRecord() {
  return window.__MOTION_DEV_TOOLS_RECORD;
}

function animateStyle(element, key, keyframesDefinition) {
  let options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};
  let AnimationPolyfill = arguments.length > 4 ? arguments[4] : undefined;
  const record = getDevToolsRecord();
  const isRecording = options.record !== false && record;
  let animation;
  let {
    duration = defaults.duration,
    delay = defaults.delay,
    endDelay = defaults.endDelay,
    repeat = defaults.repeat,
    easing = defaults.easing,
    persist = false,
    direction,
    offset,
    allowWebkitAcceleration = false
  } = options;
  const data = getAnimationData(element);
  const valueIsTransform = isTransform(key);
  let canAnimateNatively = supports.waapi();
  /**
   * If this is an individual transform, we need to map its
   * key to a CSS variable and update the element's transform style
   */

  valueIsTransform && addTransformToElement(element, key);
  const name = getStyleName(key);
  const motionValue = getMotionValue(data.values, name);
  /**
   * Get definition of value, this will be used to convert numerical
   * keyframes into the default value type.
   */

  const definition = transformDefinitions.get(name);
  /**
   * Stop the current animation, if any. Because this will trigger
   * commitStyles (DOM writes) and we might later trigger DOM reads,
   * this is fired now and we return a factory function to create
   * the actual animation that can get called in batch,
   */

  stopAnimation(motionValue.animation, !(isEasingGenerator(easing) && motionValue.generator) && options.record !== false);
  /**
   * Batchable factory function containing all DOM reads.
   */

  return () => {
    const readInitialValue = () => {
      var _a, _b;

      return (_b = (_a = style.get(element, name)) !== null && _a !== void 0 ? _a : definition === null || definition === void 0 ? void 0 : definition.initialValue) !== null && _b !== void 0 ? _b : 0;
    };
    /**
     * Replace null values with the previous keyframe value, or read
     * it from the DOM if it's the first keyframe.
     */


    let keyframes = hydrateKeyframes(keyframesList(keyframesDefinition), readInitialValue);
    /**
     * Detect unit type of keyframes.
     */

    const toUnit = getUnitConverter(keyframes, definition);

    if (isEasingGenerator(easing)) {
      const custom = easing.createAnimation(keyframes, key !== "opacity", readInitialValue, name, motionValue);
      easing = custom.easing;
      keyframes = custom.keyframes || keyframes;
      duration = custom.duration || duration;
    }
    /**
     * If this is a CSS variable we need to register it with the browser
     * before it can be animated natively. We also set it with setProperty
     * rather than directly onto the element.style object.
     */


    if (isCssVar(name)) {
      if (supports.cssRegisterProperty()) {
        registerCssVariable(name);
      } else {
        canAnimateNatively = false;
      }
    }
    /**
     * If we've been passed a custom easing function, and this browser
     * does **not** support linear() easing, and the value is a transform
     * (and thus a pure number) we can still support the custom easing
     * by falling back to the animation polyfill.
     */


    if (valueIsTransform && !supports.linearEasing() && (isFunction(easing) || isEasingList(easing) && easing.some(isFunction))) {
      canAnimateNatively = false;
    }
    /**
     * If we can animate this value with WAAPI, do so.
     */


    if (canAnimateNatively) {
      /**
       * Convert numbers to default value types. Currently this only supports
       * transforms but it could also support other value types.
       */
      if (definition) {
        keyframes = keyframes.map(value => isNumber(value) ? definition.toDefaultUnit(value) : value);
      }
      /**
       * If this browser doesn't support partial/implicit keyframes we need to
       * explicitly provide one.
       */


      if (keyframes.length === 1 && (!supports.partialKeyframes() || isRecording)) {
        keyframes.unshift(readInitialValue());
      }

      const animationOptions = {
        delay: time.ms(delay),
        duration: time.ms(duration),
        endDelay: time.ms(endDelay),
        easing: !isEasingList(easing) ? convertEasing(easing, duration) : undefined,
        direction,
        iterations: repeat + 1,
        fill: "both"
      };
      animation = element.animate({
        [name]: keyframes,
        offset,
        easing: isEasingList(easing) ? easing.map(thisEasing => convertEasing(thisEasing, duration)) : undefined
      }, animationOptions);
      /**
       * Polyfill finished Promise in browsers that don't support it
       */

      if (!animation.finished) {
        animation.finished = new Promise((resolve, reject) => {
          animation.onfinish = resolve;
          animation.oncancel = reject;
        });
      }

      const target = keyframes[keyframes.length - 1];
      animation.finished.then(() => {
        if (persist) return; // Apply styles to target

        style.set(element, name, target); // Ensure fill modes don't persist

        animation.cancel();
      }).catch(noop);
      /**
       * This forces Webkit to run animations on the main thread by exploiting
       * this condition:
       * https://trac.webkit.org/browser/webkit/trunk/Source/WebCore/platform/graphics/ca/GraphicsLayerCA.cpp?rev=281238#L1099
       *
       * This fixes Webkit's timing bugs, like accelerated animations falling
       * out of sync with main thread animations and massive delays in starting
       * accelerated animations in WKWebView.
       */

      if (!allowWebkitAcceleration) animation.playbackRate = 1.000001;
      /**
       * If we can't animate the value natively then we can fallback to the numbers-only
       * polyfill for transforms.
       */
    } else if (AnimationPolyfill && valueIsTransform) {
      /**
       * If any keyframe is a string (because we measured it from the DOM), we need to convert
       * it into a number before passing to the Animation polyfill.
       */
      keyframes = keyframes.map(value => typeof value === "string" ? parseFloat(value) : value);
      /**
       * If we only have a single keyframe, we need to create an initial keyframe by reading
       * the current value from the DOM.
       */

      if (keyframes.length === 1) {
        keyframes.unshift(parseFloat(readInitialValue()));
      }

      animation = new AnimationPolyfill(latest => {
        style.set(element, name, toUnit ? toUnit(latest) : latest);
      }, keyframes, Object.assign(Object.assign({}, options), {
        duration,
        easing
      }));
    } else {
      const target = keyframes[keyframes.length - 1];
      style.set(element, name, definition && isNumber(target) ? definition.toDefaultUnit(target) : target);
    }

    if (isRecording) {
      record(element, key, keyframes, {
        duration,
        delay: delay,
        easing,
        repeat,
        offset
      }, "motion-one");
    }

    motionValue.setAnimation(animation);
    return animation;
  };
}

const getOptions = (options, key) =>
/**
 * TODO: Make test for this
 * Always return a new object otherwise delay is overwritten by results of stagger
 * and this results in no stagger
 */
options[key] ? Object.assign(Object.assign({}, options), options[key]) : Object.assign({}, options);

function resolveElements(elements, selectorCache) {
  var _a;

  if (typeof elements === "string") {
    if (selectorCache) {
      (_a = selectorCache[elements]) !== null && _a !== void 0 ? _a : selectorCache[elements] = document.querySelectorAll(elements);
      elements = selectorCache[elements];
    } else {
      elements = document.querySelectorAll(elements);
    }
  } else if (elements instanceof Element) {
    elements = [elements];
  }
  /**
   * Return an empty array
   */


  return Array.from(elements || []);
}

/******************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */
function __rest(s, e) {
  var t = {};

  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];

  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
}

const thresholds = {
  any: 0,
  all: 1
};

function inView$1(elementOrSelector, onStart) {
  let {
    root,
    margin: rootMargin,
    amount = "any"
  } = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};

  /**
   * If this browser doesn't support IntersectionObserver, return a dummy stop function.
   * Default triggering of onStart is tricky - it could be used for starting/stopping
   * videos, lazy loading content etc. We could provide an option to enable a fallback, or
   * provide a fallback callback option.
   */
  if (typeof IntersectionObserver === "undefined") {
    return () => {};
  }

  const elements = resolveElements(elementOrSelector);
  const activeIntersections = new WeakMap();

  const onIntersectionChange = entries => {
    entries.forEach(entry => {
      const onEnd = activeIntersections.get(entry.target);
      /**
       * If there's no change to the intersection, we don't need to
       * do anything here.
       */

      if (entry.isIntersecting === Boolean(onEnd)) return;

      if (entry.isIntersecting) {
        const newOnEnd = onStart(entry);

        if (isFunction(newOnEnd)) {
          activeIntersections.set(entry.target, newOnEnd);
        } else {
          observer.unobserve(entry.target);
        }
      } else if (onEnd) {
        onEnd(entry);
        activeIntersections.delete(entry.target);
      }
    });
  };

  const observer = new IntersectionObserver(onIntersectionChange, {
    root,
    rootMargin,
    threshold: typeof amount === "number" ? amount : thresholds[amount]
  });
  elements.forEach(element => observer.observe(element));
  return () => observer.disconnect();
}

function hasChanged(a, b) {
  if (typeof a !== typeof b) return true;
  if (Array.isArray(a) && Array.isArray(b)) return !shallowCompare(a, b);
  return a !== b;
}

function shallowCompare(next, prev) {
  const prevLength = prev.length;
  if (prevLength !== next.length) return false;

  for (let i = 0; i < prevLength; i++) {
    if (prev[i] !== next[i]) return false;
  }

  return true;
}

function isVariant(definition) {
  return typeof definition === "object";
}

function resolveVariant(definition, variants) {
  if (isVariant(definition)) {
    return definition;
  } else if (definition && variants) {
    return variants[definition];
  }
}

let scheduled = undefined;

function processScheduledAnimations() {
  if (!scheduled) return;
  const generators = scheduled.sort(compareByDepth).map(fireAnimateUpdates);
  generators.forEach(fireNext);
  generators.forEach(fireNext);
  scheduled = undefined;
}

function scheduleAnimation(state) {
  if (!scheduled) {
    scheduled = [state];
    requestAnimationFrame(processScheduledAnimations);
  } else {
    addUniqueItem(scheduled, state);
  }
}

function unscheduleAnimation(state) {
  scheduled && removeItem(scheduled, state);
}

const compareByDepth = (a, b) => a.getDepth() - b.getDepth();

const fireAnimateUpdates = state => state.animateUpdates();

const fireNext = iterator => iterator.next();

const motionEvent = (name, target) => new CustomEvent(name, {
  detail: {
    target
  }
});

function dispatchPointerEvent(element, name, event) {
  element.dispatchEvent(new CustomEvent(name, {
    detail: {
      originalEvent: event
    }
  }));
}

function dispatchViewEvent(element, name, entry) {
  element.dispatchEvent(new CustomEvent(name, {
    detail: {
      originalEntry: entry
    }
  }));
}

const inView = {
  isActive: options => Boolean(options.inView),
  subscribe: (element, _ref, _ref2) => {
    let {
      enable,
      disable
    } = _ref;
    let {
      inViewOptions = {}
    } = _ref2;

    const {
      once
    } = inViewOptions,
          viewOptions = __rest(inViewOptions, ["once"]);

    return inView$1(element, enterEntry => {
      enable();
      dispatchViewEvent(element, "viewenter", enterEntry);

      if (!once) {
        return leaveEntry => {
          disable();
          dispatchViewEvent(element, "viewleave", leaveEntry);
        };
      }
    }, viewOptions);
  }
};

const mouseEvent = (element, name, action) => event => {
  if (event.pointerType && event.pointerType !== "mouse") return;
  action();
  dispatchPointerEvent(element, name, event);
};

const hover = {
  isActive: options => Boolean(options.hover),
  subscribe: (element, _ref) => {
    let {
      enable,
      disable
    } = _ref;
    const onEnter = mouseEvent(element, "hoverstart", enable);
    const onLeave = mouseEvent(element, "hoverend", disable);
    element.addEventListener("pointerenter", onEnter);
    element.addEventListener("pointerleave", onLeave);
    return () => {
      element.removeEventListener("pointerenter", onEnter);
      element.removeEventListener("pointerleave", onLeave);
    };
  }
};

const press = {
  isActive: options => Boolean(options.press),
  subscribe: (element, _ref) => {
    let {
      enable,
      disable
    } = _ref;

    const onPointerUp = event => {
      disable();
      dispatchPointerEvent(element, "pressend", event);
      window.removeEventListener("pointerup", onPointerUp);
    };

    const onPointerDown = event => {
      enable();
      dispatchPointerEvent(element, "pressstart", event);
      window.addEventListener("pointerup", onPointerUp);
    };

    element.addEventListener("pointerdown", onPointerDown);
    return () => {
      element.removeEventListener("pointerdown", onPointerDown);
      window.removeEventListener("pointerup", onPointerUp);
    };
  }
};

const gestures = {
  inView,
  hover,
  press
};
/**
 * A list of state types, in priority order. If a value is defined in
 * a righter-most type, it will override any definition in a lefter-most.
 */

const stateTypes = ["initial", "animate", ...Object.keys(gestures), "exit"];
/**
 * A global store of all generated motion states. This can be used to lookup
 * a motion state for a given Element.
 */

const mountedStates = new WeakMap();

function createMotionState() {
  let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  let parent = arguments.length > 1 ? arguments[1] : undefined;

  /**
   * The element represented by the motion state. This is an empty reference
   * when we create the state to support SSR and allow for later mounting
   * in view libraries.
   *
   * @ts-ignore
   */
  let element;
  /**
   * Calculate a depth that we can use to order motion states by tree depth.
   */

  let depth = parent ? parent.getDepth() + 1 : 0;
  /**
   * Track which states are currently active.
   */

  const activeStates = {
    initial: true,
    animate: true
  };
  /**
   * A map of functions that, when called, will remove event listeners for
   * a given gesture.
   */

  const gestureSubscriptions = {};
  /**
   * Initialise a context to share through motion states. This
   * will be populated by variant names (if any).
   */

  const context = {};

  for (const name of stateTypes) {
    context[name] = typeof options[name] === "string" ? options[name] : parent === null || parent === void 0 ? void 0 : parent.getContext()[name];
  }
  /**
   * If initial is set to false we use the animate prop as the initial
   * animation state.
   */


  const initialVariantSource = options.initial === false ? "animate" : "initial";
  /**
   * Destructure an initial target out from the resolved initial variant.
   */

  let _a = resolveVariant(options[initialVariantSource] || context[initialVariantSource], options.variants) || {},
      target = __rest(_a, ["transition"]);
  /**
   * The base target is a cached map of values that we'll use to animate
   * back to if a value is removed from all active state types. This
   * is usually the initial value as read from the DOM, for instance if
   * it hasn't been defined in initial.
   */


  const baseTarget = Object.assign({}, target);
  /**
   * A generator that will be processed by the global animation scheduler.
   * This yields when it switches from reading the DOM to writing to it
   * to prevent layout thrashing.
   */

  function* animateUpdates() {
    var _a, _b;

    const prevTarget = target;
    target = {};
    const animationOptions = {};

    for (const name of stateTypes) {
      if (!activeStates[name]) continue;
      const variant = resolveVariant(options[name]);
      if (!variant) continue;

      for (const key in variant) {
        if (key === "transition") continue;
        target[key] = variant[key];
        animationOptions[key] = getOptions((_b = (_a = variant.transition) !== null && _a !== void 0 ? _a : options.transition) !== null && _b !== void 0 ? _b : {}, key);
      }
    }

    const allTargetKeys = new Set([...Object.keys(target), ...Object.keys(prevTarget)]);
    const animationFactories = [];
    allTargetKeys.forEach(key => {
      var _a;

      if (target[key] === undefined) {
        target[key] = baseTarget[key];
      }

      if (hasChanged(prevTarget[key], target[key])) {
        (_a = baseTarget[key]) !== null && _a !== void 0 ? _a : baseTarget[key] = style.get(element, key);
        animationFactories.push(animateStyle(element, key, target[key], animationOptions[key], Animation));
      }
    }); // Wait for all animation states to read from the DOM

    yield;
    const animations = animationFactories.map(factory => factory()).filter(Boolean);
    if (!animations.length) return;
    const animationTarget = target;
    element.dispatchEvent(motionEvent("motionstart", animationTarget));
    Promise.all(animations.map(animation => animation.finished)).then(() => {
      element.dispatchEvent(motionEvent("motioncomplete", animationTarget));
    }).catch(noop);
  }

  const setGesture = (name, isActive) => () => {
    activeStates[name] = isActive;
    scheduleAnimation(state);
  };

  const updateGestureSubscriptions = () => {
    for (const name in gestures) {
      const isGestureActive = gestures[name].isActive(options);
      const remove = gestureSubscriptions[name];

      if (isGestureActive && !remove) {
        gestureSubscriptions[name] = gestures[name].subscribe(element, {
          enable: setGesture(name, true),
          disable: setGesture(name, false)
        }, options);
      } else if (!isGestureActive && remove) {
        remove();
        delete gestureSubscriptions[name];
      }
    }
  };

  const state = {
    update: newOptions => {
      if (!element) return;
      options = newOptions;
      updateGestureSubscriptions();
      scheduleAnimation(state);
    },
    setActive: (name, isActive) => {
      if (!element) return;
      activeStates[name] = isActive;
      scheduleAnimation(state);
    },
    animateUpdates,
    getDepth: () => depth,
    getTarget: () => target,
    getOptions: () => options,
    getContext: () => context,
    mount: newElement => {
      element = newElement;
      mountedStates.set(element, state);
      updateGestureSubscriptions();
      return () => {
        mountedStates.delete(element);
        unscheduleAnimation(state);

        for (const key in gestureSubscriptions) {
          gestureSubscriptions[key]();
        }
      };
    },
    isMounted: () => Boolean(element)
  };
  return state;
}

function createStyles(keyframes) {
  const initialKeyframes = {};
  const transformKeys = [];

  for (let key in keyframes) {
    const value = keyframes[key];

    if (isTransform(key)) {
      if (transformAlias[key]) key = transformAlias[key];
      transformKeys.push(key);
      key = asTransformCssVar(key);
    }

    let initialKeyframe = Array.isArray(value) ? value[0] : value;
    /**
     * If this is a number and we have a default value type, convert the number
     * to this type.
     */

    const definition = transformDefinitions.get(key);

    if (definition) {
      initialKeyframe = isNumber(value) ? definition.toDefaultUnit(value) : value;
    }

    initialKeyframes[key] = initialKeyframe;
  }

  if (transformKeys.length) {
    initialKeyframes.transform = buildTransformTemplate(transformKeys);
  }

  return initialKeyframes;
}

const contextId = "motion-state";
const presenceId = "motion-presence";

const objectType = () => ({
  type: Object
});

const Motion = defineComponent({
  name: "Motion",
  inheritAttrs: true,
  props: {
    tag: {
      type: String,
      default: "div"
    },
    initial: {
      type: [Object, Boolean]
    },
    animate: objectType(),
    inView: objectType(),
    hover: objectType(),
    press: objectType(),
    exit: objectType(),
    inViewOptions: objectType(),
    transition: objectType(),
    style: objectType()
  },

  setup(props) {
    const root = ref(null);
    const parentState = inject(contextId, undefined);
    const presenceState = inject(presenceId, undefined);
    const state = createMotionState(Object.assign(Object.assign({}, props), {
      initial: (presenceState === null || presenceState === void 0 ? void 0 : presenceState.initial) === false ? presenceState.initial : props.initial === true ? undefined : props.initial
    }), parentState);
    provide(contextId, state);
    onMounted(() => {
      const unmount = state.mount(root.value);
      state.update(Object.assign(Object.assign({}, props), {
        initial: props.initial === true ? undefined : props.initial
      }));
      return unmount;
    });
    let manuallyAppliedMotionStyles = false;
    onUpdated(() => {
      /**
       * Vue reapplies all styles every render, rather than diffing and
       * only reapplying the ones that change. This means that initially
       * calculated motion styles also get reapplied every render, leading
       * to incorrect animation origins.
       *
       * To prevent this, once an element is mounted we hand over these
       * styles to Motion. This will currently still lead to a jump if interrupting
       * transforms in browsers where the number polyfill is used.
       */
      if (!manuallyAppliedMotionStyles && root.value) {
        manuallyAppliedMotionStyles = true;
        const styles = createStyles(state.getTarget());

        for (const key in styles) {
          style.set(root.value, key, styles[key]);
        }
      }

      state.update(Object.assign(Object.assign({}, props), {
        initial: props.initial === true ? undefined : props.initial
      }));
    });
    return {
      state,
      root,
      initialStyles: createStyles(state.getTarget())
    };
  },

  render() {
    var _a, _b;

    return h(this.tag, {
      ref: "root",
      style: this.state.isMounted() ? this.style : Object.assign(Object.assign({}, this.style), this.initialStyles)
    }, (_b = (_a = this.$slots).default) === null || _b === void 0 ? void 0 : _b.call(_a));
  }

});

const doneCallbacks = new WeakMap();

function removeDoneCallback(element) {
  const prevDoneCallback = doneCallbacks.get(element);
  prevDoneCallback && element.removeEventListener("motioncomplete", prevDoneCallback);
  doneCallbacks.delete(element);
}

const Presence = defineComponent({
  name: "Presence",
  props: {
    name: {
      type: String
    },
    initial: {
      type: Boolean,
      default: true
    },
    exitBeforeEnter: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    enter(element) {
      const state = mountedStates.get(element);
      if (!state) return;
      removeDoneCallback(element);
      state.setActive("exit", false);
    },

    exit(element, done) {
      const state = mountedStates.get(element);
      if (!state) return done();
      state.setActive("exit", true);
      removeDoneCallback(element);
      doneCallbacks.set(element, done);
      element.addEventListener("motioncomplete", done);
    }

  },

  setup(_ref) {
    let {
      initial
    } = _ref;
    const state = {
      initial
    };
    provide(presenceId, state);
    onBeforeUpdate(() => {
      state.initial = undefined;
    });
  },

  render() {
    return h(Transition, {
      name: this.name,
      onEnter: this.enter,
      onLeave: this.exit,
      css: false,
      mode: this.exitBeforeEnter ? "out-in" : undefined
    }, this.$slots.default);
  }

});

export { Motion, Presence };
