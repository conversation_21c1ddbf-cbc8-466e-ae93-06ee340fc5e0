{"version": 3, "file": "transaction.js", "sourceRoot": "", "sources": ["../src/transaction.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,mDAWwB;AACxB,uDAAsC;AACtC,iCAA+B;AAG/B,eAAe;AACf,IAAM,OAAO,GAAG,IAAI,oBAAE,CAAC,kEAAkE,EAAE,EAAE,CAAC,CAAA;AAE9F;;GAEG;AACH;IAgBE;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA4BG;IACH,qBACE,IAA6D,EAC7D,IAA6B;QAD7B,qBAAA,EAAA,SAA6D;QAC7D,qBAAA,EAAA,SAA6B;QAE7B,4DAA4D;QAC5D,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE;gBAC/B,MAAM,IAAI,KAAK,CACb,8FAA8F,CAC/F,CAAA;aACF;YAED,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAA;SAC3B;aAAM;YACL,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAA;YACjD,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAA;YAE7D,IAAI,CAAC,OAAO,GAAG,IAAI,2BAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;SAC3C;QAED,oBAAoB;QACpB,IAAM,MAAM,GAAG;YACb;gBACE,IAAI,EAAE,OAAO;gBACb,MAAM,EAAE,EAAE;gBACV,SAAS,EAAE,IAAI;gBACf,OAAO,EAAE,IAAI,eAAM,CAAC,EAAE,CAAC;aACxB;YACD;gBACE,IAAI,EAAE,UAAU;gBAChB,MAAM,EAAE,EAAE;gBACV,SAAS,EAAE,IAAI;gBACf,OAAO,EAAE,IAAI,eAAM,CAAC,EAAE,CAAC;aACxB;YACD;gBACE,IAAI,EAAE,UAAU;gBAChB,KAAK,EAAE,KAAK;gBACZ,MAAM,EAAE,EAAE;gBACV,SAAS,EAAE,IAAI;gBACf,OAAO,EAAE,IAAI,eAAM,CAAC,EAAE,CAAC;aACxB;YACD;gBACE,IAAI,EAAE,IAAI;gBACV,SAAS,EAAE,IAAI;gBACf,MAAM,EAAE,EAAE;gBACV,OAAO,EAAE,IAAI,eAAM,CAAC,EAAE,CAAC;aACxB;YACD;gBACE,IAAI,EAAE,OAAO;gBACb,MAAM,EAAE,EAAE;gBACV,SAAS,EAAE,IAAI;gBACf,OAAO,EAAE,IAAI,eAAM,CAAC,EAAE,CAAC;aACxB;YACD;gBACE,IAAI,EAAE,MAAM;gBACZ,KAAK,EAAE,OAAO;gBACd,SAAS,EAAE,IAAI;gBACf,OAAO,EAAE,IAAI,eAAM,CAAC,EAAE,CAAC;aACxB;YACD;gBACE,IAAI,EAAE,GAAG;gBACT,SAAS,EAAE,IAAI;gBACf,OAAO,EAAE,IAAI,eAAM,CAAC,EAAE,CAAC;aACxB;YACD;gBACE,IAAI,EAAE,GAAG;gBACT,MAAM,EAAE,EAAE;gBACV,SAAS,EAAE,IAAI;gBACf,SAAS,EAAE,IAAI;gBACf,OAAO,EAAE,IAAI,eAAM,CAAC,EAAE,CAAC;aACxB;YACD;gBACE,IAAI,EAAE,GAAG;gBACT,MAAM,EAAE,EAAE;gBACV,SAAS,EAAE,IAAI;gBACf,SAAS,EAAE,IAAI;gBACf,OAAO,EAAE,IAAI,eAAM,CAAC,EAAE,CAAC;aACxB;SACF,CAAA;QAED,qBAAqB;QACrB,kCAAgB,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;QAEpC;;;;WAIG;QACH,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE;YAClC,UAAU,EAAE,IAAI;YAChB,YAAY,EAAE,IAAI;YAClB,GAAG,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC;SACtC,CAAC,CAAA;QAEF,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACvB,IAAI,CAAC,8BAA8B,EAAE,CAAA;IACvC,CAAC;IAED;;OAEG;IACH,uCAAiB,GAAjB;QACE,OAAO,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,CAAA;IACvC,CAAC;IAED;;;OAGG;IACH,0BAAI,GAAJ,UAAK,gBAAgC;QAAhC,iCAAA,EAAA,uBAAgC;QACnC,IAAI,KAAK,CAAA;QACT,IAAI,gBAAgB,EAAE;YACpB,KAAK,GAAG,IAAI,CAAC,GAAG,CAAA;SACjB;aAAM;YACL,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE;gBAC5B,KAAK,GACA,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;oBACvB,0BAAQ,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;oBAC3B,8EAA8E;oBAC9E,4BAAU,CAAC,0BAAQ,CAAC,CAAC,CAAC,CAAC;oBACvB,4BAAU,CAAC,0BAAQ,CAAC,CAAC,CAAC,CAAC;kBACxB,CAAA;aACF;iBAAM;gBACL,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;aAC7B;SACF;QAED,cAAc;QACd,OAAO,yBAAO,CAAC,KAAK,CAAC,CAAA;IACvB,CAAC;IAED;;OAEG;IACH,gCAAU,GAAV;QACE,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAA;IAC/B,CAAC;IAED;;OAEG;IACH,sCAAgB,GAAhB;QACE,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,OAAO,IAAI,CAAC,KAAK,CAAA;SAClB;QACD,IAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAA;QACxC,IAAI,CAAC,KAAK,GAAG,iCAAe,CAAC,MAAM,CAAC,CAAA;QACpC,OAAO,IAAI,CAAC,KAAK,CAAA;IACnB,CAAC;IAED;;OAEG;IACH,wCAAkB,GAAlB;QACE,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE;YAC3B,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAA;SACrC;QAED,gFAAgF;QAChF,OAAO,IAAI,CAAC,aAAc,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,qCAAe,GAAf;QACE,IAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QAChC,gGAAgG;QAChG,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,IAAI,oBAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAC9E,OAAO,KAAK,CAAA;SACb;QAED,IAAI;YACF,IAAM,CAAC,GAAG,6BAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;YAC7B,IAAM,+BAA+B,GACnC,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE,GAAG,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAA;YAC/E,IAAI,CAAC,aAAa,GAAG,2BAAS,CAC5B,OAAO,EACP,CAAC,EACD,IAAI,CAAC,CAAC,EACN,IAAI,CAAC,CAAC,EACN,+BAA+B,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,SAAS,CAChE,CAAA;SACF;QAAC,OAAO,CAAC,EAAE;YACV,OAAO,KAAK,CAAA;SACb;QAED,OAAO,CAAC,CAAC,IAAI,CAAC,aAAa,CAAA;IAC7B,CAAC;IAED;;;OAGG;IACH,0BAAI,GAAJ,UAAK,UAAkB;QACrB,6FAA6F;QAC7F,mDAAmD;QACnD,IAAI,CAAC,CAAC,GAAG,IAAI,eAAM,CAAC,EAAE,CAAC,CAAA;QACvB,IAAI,CAAC,CAAC,GAAG,IAAI,eAAM,CAAC,EAAE,CAAC,CAAA;QACvB,IAAI,CAAC,CAAC,GAAG,IAAI,eAAM,CAAC,EAAE,CAAC,CAAA;QAEvB,IAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QAChC,IAAM,GAAG,GAAG,wBAAM,CAAC,OAAO,EAAE,UAAU,CAAC,CAAA;QAEvC,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE;YAC5B,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE,GAAG,CAAC,GAAG,CAAC,CAAA;SACnC;QAED,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;IAC1B,CAAC;IAED;;OAEG;IACH,gCAAU,GAAV;QACE,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;QACxB,IAAM,IAAI,GAAG,IAAI,oBAAE,CAAC,CAAC,CAAC,CAAA;QACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACpC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;gBACX,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;gBAC3D,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC,CAAA;SACjE;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;OAEG;IACH,gCAAU,GAAV;QACE,IAAM,GAAG,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,CAAA;QAC1E,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE;YACrE,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC,CAAA;SACzD;QACD,OAAO,GAAG,CAAA;IACZ,CAAC;IAED;;OAEG;IACH,oCAAc,GAAd;QACE,OAAO,IAAI,oBAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,oBAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,oBAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAA;IACnF,CAAC;IAQD,8BAAQ,GAAR,UAAS,WAA4B;QAA5B,4BAAA,EAAA,mBAA4B;QACnC,IAAM,MAAM,GAAG,EAAE,CAAA;QACjB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE;YAC3B,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAA;SACjC;QAED,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC,GAAG,CAAC,IAAI,oBAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE;YACpD,MAAM,CAAC,IAAI,CAAC,CAAC,yCAAuC,IAAI,CAAC,UAAU,EAAI,CAAC,CAAC,CAAA;SAC1E;QAED,IAAI,WAAW,KAAK,KAAK,EAAE;YACzB,OAAO,MAAM,CAAC,MAAM,KAAK,CAAC,CAAA;SAC3B;aAAM;YACL,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;SACxB;IACH,CAAC;IAED;;OAEG;IACH,+BAAS,GAAT;QACE,kEAAkE;QAClE,OAAO,qBAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IAC7B,CAAC;IAED;;;OAGG;IACH,4BAAM,GAAN,UAAO,MAAuB;QAAvB,uBAAA,EAAA,cAAuB;QAC5B,kEAAkE;QAClE,OAAO,EAAE,CAAA;IACX,CAAC;IAEO,gCAAU,GAAlB,UAAmB,CAAU;QAC3B,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;YACrC,OAAM;SACP;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,gBAAgB,CAAC,EAAE;YAC/C,OAAM;SACP;QAED,IAAM,IAAI,GAAG,6BAAW,CAAC,CAAC,CAAC,CAAA;QAE3B,IAAI,IAAI,KAAK,EAAE,IAAI,IAAI,KAAK,EAAE,EAAE;YAC9B,OAAM;SACP;QAED,IAAM,cAAc,GAClB,IAAI,KAAK,IAAI,CAAC,UAAU,EAAE,GAAG,CAAC,GAAG,EAAE,IAAI,IAAI,KAAK,IAAI,CAAC,UAAU,EAAE,GAAG,CAAC,GAAG,EAAE,CAAA;QAE5E,IAAI,CAAC,cAAc,EAAE;YACnB,MAAM,IAAI,KAAK,CACb,iCAA+B,IAAI,sBAAiB,IAAI,CAAC,UAAU,EAAE,mFAAgF,CACtJ,CAAA;SACF;IACH,CAAC;IAEO,+BAAS,GAAjB;QACE,OAAO,IAAI,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAA;IACpE,CAAC;IAEO,oDAA8B,GAAtC;QAAA,iBAaC;QAZC,IAAM,WAAW,GAAG,MAAM,CAAC,wBAAwB,CAAC,IAAI,EAAE,GAAG,CAAE,CAAA;QAE/D,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,GAAG,eAC1B,WAAW,IACd,GAAG,EAAE,UAAA,CAAC;gBACJ,IAAI,CAAC,KAAK,SAAS,EAAE;oBACnB,KAAI,CAAC,UAAU,CAAC,0BAAQ,CAAC,CAAC,CAAC,CAAC,CAAA;iBAC7B;gBAED,WAAW,CAAC,GAAI,CAAC,CAAC,CAAC,CAAA;YACrB,CAAC,IACD,CAAA;IACJ,CAAC;IAEO,uCAAiB,GAAzB;QACE,IAAM,oBAAoB,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAA;QAEvE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE;YACrB,qEAAqE;YACrE,OAAO,oBAAoB,CAAA;SAC5B;QAED,eAAe;QACf,uGAAuG;QACvG,yGAAyG;QACzG,qGAAqG;QACrG,6BAA6B;QAC7B,IAAM,CAAC,GAAG,6BAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QAE7B,IAAM,+BAA+B,GACnC,CAAC,KAAK,IAAI,CAAC,UAAU,EAAE,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,EAAE,GAAG,CAAC,GAAG,EAAE,CAAA;QACtE,OAAO,+BAA+B,IAAI,oBAAoB,CAAA;IAChE,CAAC;IACH,kBAAC;AAAD,CAAC,AAvYD,IAuYC"}