<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

# Block

Creates a new block object

**Parameters**

-   `data` **([Array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array) \| [Buffer](https://nodejs.org/api/buffer.html) \| [Object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object))** 

**Properties**

-   `header` **Header** the block's header
-   `uncleList` **[Array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array)&lt;Header>** an array of uncle headers
-   `raw` **[Array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array)&lt;[<PERSON>uffer](https://nodejs.org/api/buffer.html)>** an array of buffers containing the raw blocks.

## hash

Produces a hash the RLP of the block

## isGenisis

Determines if a given block is the genesis block

Returns **any** Boolean

## setGenesisParams

turns the block in to the canonical genesis block

## serialize

Produces a serialization of the block.

**Parameters**

-   `rlpEncode` **[Boolean](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** whether to rlp encode the block or not

## genTxTrie

Generate transaction trie. The tx trie must be generated before the transaction trie can
be validated with `validateTransactionTrie`

**Parameters**

-   `cb` **[Function](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function)** the callback

## validateTransactionTrie

Validates the transaction trie

Returns **[Boolean](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** 

## validateTransactions

Validates the transactions

**Parameters**

-   `stringError` **[Boolean](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean)?** whether to return a string with a dscription of why the validation failed or return a Bloolean (optional, default `false`)

Returns **[Boolean](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** 

## validate

Validates the entire block. Returns a string to the callback if block is invalid

**Parameters**

-   `blockChain` **BlockChain** the blockchain that this block wants to be part of
-   `cb` **[Function](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function)** the callback which is given a `String` if the block is not valid

## validateUncleHash

Validates the uncle's hash

Returns **[Boolean](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** 

## validateUncles

Validates the uncles that are in the block if any. Returns a string to the callback if uncles are invalid

**Parameters**

-   `blockChaina` **Blockchain** an instance of the Blockchain
-   `cb` **[Function](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function)** the callback
-   `blockChain`  

## toJSON

Converts the block toJSON

**Parameters**

-   `labeled` **Bool** whether to create an labeled object or an array

Returns **[Object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** 
