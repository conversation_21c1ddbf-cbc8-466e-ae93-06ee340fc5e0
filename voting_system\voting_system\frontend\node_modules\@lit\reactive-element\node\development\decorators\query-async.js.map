{"version": 3, "file": "query-async.js", "sources": ["../../../src/decorators/query-async.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/*\n * IMPORTANT: For compatibility with <PERSON><PERSON><PERSON> and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\n\nimport {ReactiveElement} from '../reactive-element.js';\nimport {decorateProperty} from './base.js';\n\n// Note, in the future, we may extend this decorator to support the use case\n// where the queried element may need to do work to become ready to interact\n// with (e.g. load some implementation code). If so, we might elect to\n// add a second argument defining a function that can be run to make the\n// queried element loaded/updated/ready.\n/**\n * A property decorator that converts a class property into a getter that\n * returns a promise that resolves to the result of a querySelector on the\n * element's renderRoot done after the element's `updateComplete` promise\n * resolves. When the queried property may change with element state, this\n * decorator can be used instead of requiring users to await the\n * `updateComplete` before accessing the property.\n *\n * @param selector A DOMString containing one or more selectors to match.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/Document/querySelector\n *\n * ```ts\n * class MyElement {\n *   @queryAsync('#first')\n *   first: Promise<HTMLDivElement>;\n *\n *   render() {\n *     return html`\n *       <div id=\"first\"></div>\n *       <div id=\"second\"></div>\n *     `;\n *   }\n * }\n *\n * // external usage\n * async doSomethingWithFirst() {\n *  (await aMyElement.first).doSomething();\n * }\n * ```\n * @category Decorator\n */\nexport function queryAsync(selector: string) {\n  return decorateProperty({\n    descriptor: (_name: PropertyKey) => ({\n      async get(this: ReactiveElement) {\n        await this.updateComplete;\n        return this.renderRoot?.querySelector(selector);\n      },\n      enumerable: true,\n      configurable: true,\n    }),\n  });\n}\n"], "names": [], "mappings": ";;AAAA;;;;AAIG;AAYH;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BG;AACG,SAAU,UAAU,CAAC,QAAgB,EAAA;AACzC,IAAA,OAAO,gBAAgB,CAAC;AACtB,QAAA,UAAU,EAAE,CAAC,KAAkB,MAAM;AACnC,YAAA,MAAM,GAAG,GAAA;;gBACP,MAAM,IAAI,CAAC,cAAc,CAAC;gBAC1B,OAAO,CAAA,EAAA,GAAA,IAAI,CAAC,UAAU,0CAAE,aAAa,CAAC,QAAQ,CAAC,CAAC;aACjD;AACD,YAAA,UAAU,EAAE,IAAI;AAChB,YAAA,YAAY,EAAE,IAAI;SACnB,CAAC;AACH,KAAA,CAAC,CAAC;AACL;;;;"}