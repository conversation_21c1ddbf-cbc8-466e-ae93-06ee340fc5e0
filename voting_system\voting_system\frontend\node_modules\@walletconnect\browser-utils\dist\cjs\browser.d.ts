/// <reference types="node" />
import { IClientMeta } from "@walletconnect/types";
import * as windowGetters from "@walletconnect/window-getters";
import { BrowserInfo, BotInfo, NodeInfo, SearchBotDeviceInfo, ReactNativeInfo } from "detect-browser";
export declare function detectEnv(userAgent?: string): BrowserInfo | BotInfo | NodeInfo | SearchBotDeviceInfo | ReactNativeInfo | null;
export declare function detectOS(): import("detect-browser").OperatingSystem | NodeJS.Platform | undefined;
export declare function isAndroid(): boolean;
export declare function isIOS(): boolean;
export declare function isMobile(): boolean;
export declare function isNode(): boolean;
export declare function isBrowser(): boolean;
export declare const getFromWindow: typeof windowGetters.getFromWindow;
export declare const getFromWindowOrThrow: typeof windowGetters.getFromWindowOrThrow;
export declare const getDocumentOrThrow: typeof windowGetters.getDocumentOrThrow;
export declare const getDocument: typeof windowGetters.getDocument;
export declare const getNavigatorOrThrow: typeof windowGetters.getNavigatorOrThrow;
export declare const getNavigator: typeof windowGetters.getNavigator;
export declare const getLocationOrThrow: typeof windowGetters.getLocationOrThrow;
export declare const getLocation: typeof windowGetters.getLocation;
export declare const getCryptoOrThrow: typeof windowGetters.getCryptoOrThrow;
export declare const getCrypto: typeof windowGetters.getCrypto;
export declare const getLocalStorageOrThrow: typeof windowGetters.getLocalStorageOrThrow;
export declare const getLocalStorage: typeof windowGetters.getLocalStorage;
export declare function getClientMeta(): IClientMeta | null;
//# sourceMappingURL=browser.d.ts.map