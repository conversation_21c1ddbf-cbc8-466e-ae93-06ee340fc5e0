export { render, hydrate } from './render';
export {
	createElement,
	createElement as h,
	Fragment,
	createRef,
	isValidElement
} from './create-element';
export { Component } from './component';
export { cloneElement } from './clone-element';
export { createContext } from './create-context';
export { toChildArray } from './diff/children';
export { unmount as _unmount } from './diff';
export { default as options } from './options';
