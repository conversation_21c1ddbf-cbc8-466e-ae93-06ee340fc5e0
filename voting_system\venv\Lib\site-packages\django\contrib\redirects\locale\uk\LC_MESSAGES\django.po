# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <o.ch<PERSON><PERSON><PERSON>@gmail.com>, 2014
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2014
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2015
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-01-20 00:12+0000\n"
"Last-Translator: <PERSON>lia Volochii <<EMAIL>>\n"
"Language-Team: Ukrainian (http://www.transifex.com/django/django/language/"
"uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != "
"11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % "
"100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || "
"(n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

msgid "Redirects"
msgstr "Перенаправлення"

msgid "site"
msgstr "сайт"

msgid "redirect from"
msgstr "перенаправлення з"

msgid ""
"This should be an absolute path, excluding the domain name. Example: “/"
"events/search/”."
msgstr ""
"Шлях має бути абсолютним без доменного імені. Наприклад, “/events/search/”."

msgid "redirect to"
msgstr "перенаправлення до"

msgid ""
"This can be either an absolute path (as above) or a full URL starting with a "
"scheme such as “https://”."
msgstr ""
"Шлях може бути або абсолютним (як вказано вище), або повним URL (який "
"починається із схеми, як-от “https://”)."

msgid "redirect"
msgstr "перенаправлення"

msgid "redirects"
msgstr "перенаправлення"
