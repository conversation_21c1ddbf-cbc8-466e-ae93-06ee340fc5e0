{"version": 3, "file": "jsonrpc.d.ts", "sourceRoot": "", "sources": ["../../src/jsonrpc.ts"], "names": [], "mappings": "AAAA,MAAM,WAAW,sBAAsB,CAAC,CAAC,GAAG,GAAG;IAC7C,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,CAAC,CAAC;CACT;AAED,MAAM,WAAW,gBAAgB,CAAC,CAAC,GAAG,GAAG;IACvC,MAAM,EAAE,MAAM,CAAC;IACf,MAAM,CAAC,EAAE,CAAC,CAAC;IACX,EAAE,CAAC,EAAE,MAAM,CAAC;CACb;AAED,MAAM,WAAW,cAAc,CAAC,CAAC,GAAG,GAAG,CAAE,SAAQ,QAAQ,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;IAC5E,EAAE,EAAE,MAAM,CAAC;IACX,OAAO,EAAE,MAAM,CAAC;CACjB;AAED,MAAM,WAAW,aAAa,CAAC,CAAC,GAAG,GAAG;IACpC,EAAE,EAAE,MAAM,CAAC;IACX,OAAO,EAAE,MAAM,CAAC;IAChB,MAAM,EAAE,CAAC,CAAC;CACX;AAED,MAAM,WAAW,YAAY;IAC3B,EAAE,EAAE,MAAM,CAAC;IACX,OAAO,EAAE,MAAM,CAAC;IAChB,KAAK,EAAE,aAAa,CAAC;CACtB;AAED,MAAM,WAAW,aAAa;IAC5B,IAAI,EAAE,MAAM,CAAC;IACb,OAAO,EAAE,MAAM,CAAC;IAChB,IAAI,CAAC,EAAE,MAAM,CAAC;CACf;AAED,MAAM,MAAM,eAAe,CAAC,CAAC,GAAG,GAAG,IAAI,aAAa,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC;AAEvE,MAAM,MAAM,cAAc,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,IAAI,cAAc,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC"}