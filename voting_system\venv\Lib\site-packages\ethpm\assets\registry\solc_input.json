{"language": "Solidity", "settings": {"outputSelection": {"*": {"*": ["abi", "evm.bytecode.object", "evm.deployedBytecode", "metadata", "<PERSON>v<PERSON><PERSON>"]}}}, "sources": {"PackageRegistryInterface.sol": {"urls": ["/Users/<USER>/ethereum/ethpm-cli/projects/registry/contracts/PackageRegistryInterface.sol"]}, "PackageRegistry.sol": {"urls": ["/Users/<USER>/ethereum/ethpm-cli/projects/registry/contracts/PackageRegistry.sol"]}, "PackageDB.sol": {"urls": ["/Users/<USER>/ethereum/ethpm-cli/projects/registry/contracts/PackageDB.sol"]}, "ReleaseDB.sol": {"urls": ["/Users/<USER>/ethereum/ethpm-cli/projects/registry/contracts/ReleaseDB.sol"]}, "Authority.sol": {"urls": ["/Users/<USER>/ethereum/ethpm-cli/projects/registry/contracts/Authority.sol"]}, "ReleaseValidator.sol": {"urls": ["/Users/<USER>/ethereum/ethpm-cli/projects/registry/contracts/ReleaseValidator.sol"]}, "IndexedOrderedSetLib.sol": {"urls": ["/Users/<USER>/ethereum/ethpm-cli/projects/registry/contracts/IndexedOrderedSetLib.sol"]}}}